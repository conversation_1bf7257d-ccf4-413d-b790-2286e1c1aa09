---
description: Standards for handling authentication in controllers and their tests.
globs: (lib|test)/.*/(controllers|plugs)/.*\\.ex(s)?$
---
# Authentication Standards

Standards for handling authentication in controllers and their tests.

<rule>
name: authentication_standards
description: Standards for implementing authentication in controllers and their tests, including Guardian setup and accessing authenticated resources
filters:
  # Match controller files and their tests
  - type: file_path
    pattern: "(lib|test)/.*/(controllers|plugs)/.*\\.ex(s)?$"
  # Match files that might use authentication
  - type: content
    pattern: "(current_resource|authenticate_conn)"

actions:
  - type: suggest
    message: |
      When implementing authentication:

      1. In Controllers:
         ```elixir
         # Good - Get authenticated user with Guardian
         def some_action(conn, params) do
           resource = Rms.Guardian.Plug.current_resource(conn)
           
           case YourContext.do_something(resource.organization_id, params) do
             {:ok, result} -> 
               conn
               |> put_status(:ok)
               |> json(result)
             
             {:error, _reason} ->
               conn
               |> put_status(:unprocessable_entity)
               |> json(%{error: "some error message"})
           end
         end

         # Bad - Using assigns directly
         def some_action(conn, params) do
           organization_id = conn.assigns.current_organization.id
           # ...
         end
         ```

      2. Setting Up Authentication in Tests:
         ```elixir
         # In test setup
         setup %{conn: conn} do
           organization = insert(:organization)
           user = insert(:user, organization: organization)
           
           conn = authenticate_conn(conn, user)
           
           {:ok, %{conn: conn, organization: organization}}
         end
         ```

      3. Testing Authentication Requirements:
         ```elixir
         test "requires authentication", %{conn: _conn} do
           conn = build_conn()
           conn = post(conn, ~p"/api/some/endpoint")
           
           assert response(conn, 401)
         end
         ```

      4. Testing Authenticated Actions:
         ```elixir
         test "some authenticated action", %{conn: conn, organization: organization} do
           # Setup test data
           item = insert(:item, organization: organization)
           
           # Make request
           conn = post(conn, ~p"/api/items/#{item.id}/action")
           
           # Assert response
           assert %{"status" => "success"} = json_response(conn, 200)
         end
         ```

      Important Rules:
      - Always use Rms.Guardian.Plug.current_resource(conn) to access the authenticated user in controllers
      - Never access conn.assigns directly for authentication data
      - All authenticated actions must get organization context from the authenticated user
      - Controller actions should pattern match on the result and return appropriate status codes
      - Test both authenticated and unauthenticated scenarios
      - In tests, always set up proper authentication in the setup block
      - Remember that organization context comes from the authenticated user
      - Use proper status codes (401 for unauthenticated, 403 for unauthorized)

examples:
  - input: |
      # Bad controller implementation
      def create(conn, params) do
        org_id = conn.assigns.current_organization.id
        case create_something(org_id, params) do
          {:ok, item} -> json(conn, item)
        end
      end
      
      # Good controller implementation
      def create(conn, params) do
        resource = Rms.Guardian.Plug.current_resource(conn)
        
        case create_something(resource.organization_id, params) do
          {:ok, item} -> 
            conn
            |> put_status(:created)
            |> json(item)
            
          {:error, _reason} ->
            conn
            |> put_status(:unprocessable_entity)
            |> json(%{error: "Failed to create item"})
        end
      end
    output: "Correctly structured controller with authentication"

metadata:
  priority: high
  version: 1.0
  tags:
    - controllers
    - authentication
    - guardian
    - testing
</rule>