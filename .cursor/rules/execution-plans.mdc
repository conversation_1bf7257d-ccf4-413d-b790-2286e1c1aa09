---
description: Standards for managing and organizing execution plans in the project
globs: *
---
# Execution Plans Standards

Standards for managing and organizing execution plans in the project.

<rule>
name: execution_plans
description: Standards for managing execution plans and tracking task progress
filters:
  # Match markdown files in .plans directory
  - type: file_path
    pattern: "^\\.plans/.*\\.md$"

actions:
  - type: reject
    conditions:
      - pattern: "^\\.plans/(?!current\\.md|[A-Z]+-\\d+\\.md$)"
        message: "Execution plan files must be named either 'current.md' or follow the pattern 'TASK-ID.md'"

  - type: suggest
    message: |
      When working with execution plans:

      1. Directory Structure:
         ```
         .plans/
         ├── current.md      # Active execution plan
         ├── TASK-123.md     # Completed plan
         └── TASK-456.md     # Completed plan
         ```

      2. File Naming:
         - Active plan must be named `current.md`
         - Completed plans must be named with their task ID (e.g., `TASK-123.md`)

      3. Content Format:
         ```markdown
         # Task Description
         Brief description of the overall task

         ## Steps
         - [x] Completed step
         - [ ] Pending step
         - [ ] Next step

         ## Notes
         Additional context or important information
         ```

      4. Workflow:
         - Keep active plan in `current.md`
         - Mark steps as completed using `[x]`
         - When all steps are done, rename `current.md` to `TASK-ID.md`
         - Never delete completed plans

      5. Maintenance:
         - Review and update progress after each completed step
         - Keep the plan up-to-date with any new requirements
         - Document any blockers or dependencies

examples:
  - input: |
      # Invalid locations/names
      plans/current.md
      .plans/plan1.md
      .plans/todo.md

      # Valid files
      .plans/current.md
      .plans/TASK-123.md
      .plans/PRJ-456.md
    output: "Correctly structured execution plans"

metadata:
  priority: high
  version: 1.0
</rule> 