---
description: Standards for logging
globs: *.ex
---
 # Logging Standards

Standards for consistent logging across the codebase.

<rule>
name: lowercase_logging
description: Enforce lowercase logging standards across the codebase
filters:
  # Match Elixir files
  - type: file_extension
    pattern: "\\.ex$|\\.exs$"
  # Match logging calls
  - type: content
    pattern: "Logger\\.(debug|info|warn|error)"

actions:
  - type: reject
    conditions:
      - pattern: "Logger\\.(debug|info|warn|error)\\([\"'].*[A-Z].*[\"']"
        message: "Log messages must be in lowercase"

  - type: suggest
    message: |
      When logging:

      1. Always use lowercase for log messages:
         ```elixir
         # Bad
         Logger.info("User Created Successfully")
         Logger.error("Database Connection Failed")

         # Good
         Logger.info("user created successfully")
         Logger.error("database connection failed")
         ```

      2. This applies to all logging levels:
         - debug
         - info
         - warn
         - error

      3. Exception for:
         - Variable names when interpolated
         - System-generated error messages
         - Stack traces

examples:
  - input: |
      # Bad logging
      Logger.info("User #{user.name} Created Successfully")
      Logger.error("Database ERROR: connection failed")

      # Good logging
      Logger.info("user #{user.name} created successfully")
      Logger.error("database error: connection failed")
    output: "Correctly formatted lowercase log messages"

metadata:
  priority: medium
  version: 1.0
</rule>