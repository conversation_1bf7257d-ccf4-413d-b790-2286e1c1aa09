---
description: Standards for implementing dependency injection and mocking external services
globs: *.ex,*.exs
---
 # Mox and Dependency Injection Standards

Standards for implementing dependency injection and mocking external services.

<rule>
name: mox_and_deps
description: Standards for implementing dependency injection and mocking external services
filters:
  # Match Elixir files
  - type: file_extension
    pattern: "\\.ex$|\\.exs$"
  # Match files that might contain external service calls
  - type: content
    pattern: "(Tesla|ExAws|HTTPoison)"

actions:
  - type: suggest
    message: |
      When implementing external service integrations:

      1. Create a Behaviour Module:
         ```elixir
         defmodule MyApp.Service.ClientBehaviour do
           @callback some_operation(args) :: {:ok, result} | {:error, reason}
         end
         ```

      2. Create the Real Implementation:
         ```elixir
         defmodule MyApp.Service.Client do
           @behaviour MyApp.Service.ClientBehaviour
           
           @impl true
           def some_operation(args) do
             # Real implementation
           end
         end
         ```

      3. Set up Dependency Injection:
         ```elixir
         defmodule MyApp.Service do
           alias MyApp.Service.Client
           
           @client Application.compile_env(:my_app, :service_client, Client)
           
           def public_operation(args) do
             @client.some_operation(args)
           end
         end
         ```

      4. Create Mock for Testing:
         ```elixir
         # test/support/mocks/service_mock.ex
         Mox.defmock(MyApp.Service.Mock, for: MyApp.Service.ClientBehaviour)
         ```

      5. Configure Test Environment:
         ```elixir
         # config/test.exs
         config :my_app, :service_client, MyApp.Service.Mock
         ```

      6. Use in Tests:
         ```elixir
         # In test file
         import Mox
         setup :verify_on_exit!
         
         test "some test" do
           expect(MyApp.Service.Mock, :some_operation, fn args ->
             {:ok, "result"}
           end)
         end
         ```

      Important Rules:
      - Only mock external service calls (APIs, S3, etc.)
      - DO NOT mock database calls or internal contexts
      - Keep real implementation in a Client module
      - Use descriptive names for behaviours and implementations
      - Always verify mocks with verify_on_exit!
      - Group related operations in a single behaviour

examples:
  - input: |
      # Bad - Mocking internal context
      Mox.defmock(MyApp.Accounts.Mock, for: MyApp.Accounts)
      
      # Bad - No behaviour defined
      defmodule MyApp.Service do
        @service Application.compile_env(:my_app, :service, MyService)
      end
      
      # Good - External service with behaviour
      defmodule MyApp.ExternalAPI.ClientBehaviour do
        @callback call_api(params) :: {:ok, response} | {:error, reason}
      end
      
      defmodule MyApp.ExternalAPI.Client do
        @behaviour MyApp.ExternalAPI.ClientBehaviour
      end
    output: "Correctly structured external service mocking"

metadata:
  priority: high
  version: 1.0
  tags:
    - testing
    - mocking
    - dependency-injection
</rule>