---
description: Standards for writing and organizing tests in the project.
globs: test/.*_(test)\\.exs$
---
 # Test Standards

Standards for writing and organizing tests in the project.

<rule>
name: test_standards
description: Standards for writing and organizing tests, including describe blocks, factories, and test structure
filters:
  # Match test files
  - type: file_path
    pattern: "test/.*_(test)\\.exs$"

actions:
  - type: suggest
    message: |
      When writing tests:

      1. Describe Block Naming:
         ```elixir
         # Good - Use function name with arity
         describe "create_user/2" do
         
         # Bad - Generic descriptions
         describe "user creation" do
         
         # Bad - Missing arity
         describe "create_user" do
         ```

      2. Factory Usage with ExMachina:
         ```elixir
         # Setup in test file
         import Rms.Factory
         
         # Creating records
         user = insert(:user)
         org = insert(:organization)
         
         # Creating with associations
         user = insert(:user, organization: org)
         
         # Building without inserting
         user = build(:user)
         ```

      3. Multi-tenancy:
         - Always include organization_id in entities
         - Pass organization context in tests
         ```elixir
         setup do
           organization = insert(:organization)
           {:ok, %{organization: organization}}
         end
         
         test "some test", %{organization: organization} do
           user = insert(:user, organization: organization)
         end
         ```

      4. Test Structure:
         ```elixir
         test "descriptive name", %{organization: organization} do
           # Setup
           user = insert(:user, organization: organization)
           
           # Action
           {:ok, result} = MyApp.create_something(user)
           
           # Assertions
           assert result.status == "created"
           assert result.user_id == user.id
         end
         ```

      Important Rules:
      - Always use function/arity in describe blocks
      - Use ExMachina factories for data creation
      - Include organization context for multi-tenancy
      - Follow setup → action → assert structure
      - Use descriptive test names that explain the behavior
      - Group related test cases under the same describe block

examples:
  - input: |
      # Bad test structure
      describe "user management" do
        test "creates" do
          {:ok, user} = create_user()
          assert user.name == "John"
        end
      end
      
      # Good test structure
      describe "create_user/2" do
        test "creates user with valid attributes", %{organization: organization} do
          # Setup
          attrs = %{name: "John", email: "<EMAIL>"}
          
          # Action
          {:ok, user} = create_user(organization, attrs)
          
          # Assert
          assert user.name == "John"
          assert user.organization_id == organization.id
        end
      end
    output: "Correctly structured test following standards"

metadata:
  priority: high
  version: 1.0
  tags:
    - testing
    - organization
    - factories
</rule>