name: Continuous Deployment Workflow

on:
  workflow_call:
    inputs:
      aws_region:
        required: true
        type: string
        default: "sa-east-1"
      aws_role:
        required: true
        type: string
      aws_ecs_cluster:
        required: true
        type: string
      aws_ecs_service:
        required: true
        type: string
      aws_ecs_task_definition:
        required: true
        type: string
      aws_ecs_container_name:
        required: true
        type: string
        default: "app"
      image_name:
        required: true
        type: string

    secrets:
      OBAN_AUTH_KEY:
        required: true

jobs:
  build-push:
    name: Build & Push
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 10

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ inputs.aws_role }}
          aws-region: ${{ inputs.aws_region }}

      - name: Set up docker buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Get a short SHA
        uses: benjlevesque/short-sha@v3.0
        id: short-sha
        with:
          length: 6

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          file: "Dockerfile"
          context: .
          push: true
          build-args: |
            VERSION="${{ steps.short-sha.outputs.sha }}"
          secrets: |
            "oban_auth_key=${{ secrets.OBAN_AUTH_KEY }}"
          tags: |
            ${{ inputs.image_name }}:latest
            ${{ inputs.image_name }}:${{ steps.short-sha.outputs.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  publish:
    name: Publish
    needs: build-push
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 10

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ inputs.aws_role }}
          aws-region: ${{ inputs.aws_region }}

      - name: Get a short SHA
        uses: benjlevesque/short-sha@v3.0
        id: short-sha
        with:
          length: 6

      - name: Get ECS task definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ inputs.aws_ecs_task_definition }} \
                                           --query taskDefinition \
                                           --region ${{ inputs.aws_region }} > ./${{ inputs.aws_ecs_task_definition }}.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ./${{ inputs.aws_ecs_task_definition }}.json
          container-name: ${{ inputs.aws_ecs_container_name }}
          image: ${{ inputs.image_name }}:${{ steps.short-sha.outputs.sha }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ inputs.aws_ecs_service }}
          cluster: ${{ inputs.aws_ecs_cluster }}
