name: Continuous Integration Workflow

on:
  workflow_call:
    inputs:
      mix_env:
        required: true
        type: string
        default: "test"

permissions:
  contents: read

jobs:
  compile:
    name: Compile
    runs-on: ubuntu-24.04

    steps:
      - name: Set up <PERSON>xi<PERSON>
        uses: erlef/setup-beam@v1
        with:
          otp-version: 26.1.2
          elixir-version: 1.15.7

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Cache deps
        id: cache-deps
        uses: actions/cache@v3
        env:
          cache-name: cache-elixir-deps
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-

      - name: Cache compiled build
        id: cache-build
        uses: actions/cache@v3
        env:
          cache-name: cache-compiled-build
        with:
          path: _build
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-
            ${{ runner.os }}-mix-

      - name: Clean to rule out incremental build as a source of flakiness
        if: github.run_attempt != '1'
        run: |
          mix deps.clean --all
          mix clean
        shell: sh

      - name: Authorize Oban
        run: |
          mix hex.repo add oban https://getoban.pro/repo \
            --fetch-public-key SHA256:4/OSKi0NRF91QVVXlGAhb/BIMLnK8NHcx/EWs+aIWPc \
            --auth-key ${{secrets.OBAN_LICENSE_KEY}}

      - name: Install dependencies
        run: mix deps.get

      - name: Check Formatting
        run: mix format --check-formatted

      - name: Compiles without warnings
        env:
          MIX_ENV: ${{ inputs.mix_env }}
        run: mix compile --warnings-as-errors

  test:
    name: Unit Tests
    needs: compile
    runs-on: ubuntu-24.04

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - name: Fix pg_dump version
        run: sudo apt-get install postgresql-client-16

      - name: Set up Elixir
        uses: erlef/setup-beam@v1
        with:
          otp-version: 26.1.2
          elixir-version: 1.15.7

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Cache deps
        id: cache-deps
        uses: actions/cache@v3
        env:
          cache-name: cache-elixir-deps
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-

      - name: Cache compiled build
        id: cache-build
        uses: actions/cache@v3
        env:
          cache-name: cache-compiled-build
        with:
          path: _build
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-
            ${{ runner.os }}-mix-

      - name: Setup database
        env:
          MIX_ENV: ${{ inputs.mix_env }}
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        run: |
          mix ecto.drop
          mix ecto.create
          mix ecto.migrate

      - name: Run tests
        run: mix test --exclude localstack_integration

  localstack-integration:
    name: Localstack Integration Tests
    needs: compile
    runs-on: ubuntu-24.04

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
      
      localstack:
        image: localstack/localstack:3.0
        env:
          SERVICES: s3
          AWS_DEFAULT_REGION: us-east-1
          EDGE_PORT: 4566
        ports:
          - 4566:4566
        options: >-
          --health-cmd "curl -f http://localhost:4566/_localstack/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Fix pg_dump version
        run: sudo apt-get install postgresql-client-16

      - name: Set up Elixir
        uses: erlef/setup-beam@v1
        with:
          otp-version: 26.1.2
          elixir-version: 1.15.7

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Cache deps
        id: cache-deps
        uses: actions/cache@v3
        env:
          cache-name: cache-elixir-deps
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-

      - name: Cache compiled build
        id: cache-build
        uses: actions/cache@v3
        env:
          cache-name: cache-compiled-build
        with:
          path: _build
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-
            ${{ runner.os }}-mix-

      - name: Setup database
        env:
          MIX_ENV: ${{ inputs.mix_env }}
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        run: |
          mix ecto.drop
          mix ecto.create
          mix ecto.migrate

      - name: Run localstack integration tests
        env:
          AWS_ACCESS_KEY_ID: test
          AWS_SECRET_ACCESS_KEY: test
          AWS_REGION: us-east-1
          AWS_ENDPOINT: http://localhost:4566
        run: mix test --only localstack_integration

  credo:
    runs-on: ubuntu-latest
    needs: compile
    steps:
      # Step: Setup Elixir + Erlang image as the base.
      - name: Set up Elixir
        uses: erlef/setup-beam@v1
        with:
          otp-version: 26.1.2
          elixir-version: 1.15.7

      # Step: Check out the code.
      - name: Checkout code
        uses: actions/checkout@v3

      # Step: Define how to cache deps. Restores existing cache if present.
      - name: Cache deps
        id: cache-deps
        uses: actions/cache@v3
        env:
          cache-name: cache-elixir-deps
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-

      # Step: Define how to cache the `_build` directory. After the first run,
      # this speeds up tests runs a lot. This includes not re-compiling our
      # project's downloaded deps every run.
      - name: Cache compiled build
        id: cache-build
        uses: actions/cache@v3
        env:
          cache-name: cache-compiled-build
        with:
          path: _build
          key: ${{ runner.os }}-mix-${{ env.cache-name }}-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-${{ env.cache-name }}-
            ${{ runner.os }}-mix-

      - name: Run credo
        env:
          MIX_ENV: ${{ inputs.mix_env }}
        run: mix credo
