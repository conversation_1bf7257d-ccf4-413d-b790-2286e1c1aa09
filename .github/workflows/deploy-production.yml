name: Production Deployment
on:
  push:
    branches:
      - "main"
  pull_request:
    branches:
      - "main"

env:
  AWS_REGION: "sa-east-1"
  AWS_ROLE: "arn:aws:iam::628877775814:role/oidc-iglu-rms-production"
  AWS_ECS_CLUSTER: "iglu-production-ecs-cluster"
  AWS_ECS_SERVICE: "iglu-rms-service-production"
  AWS_ECS_TASK_DEFINITION: "iglu-rms-service-production"
  AWS_ECS_CONTAINER_NAME: "app"
  IMAGE_NAME: "628877775814.dkr.ecr.sa-east-1.amazonaws.com/iglu-rms-production"

jobs:
  env-vars:
    runs-on: ubuntu-latest
    outputs:
      aws_region: ${{ env.AWS_REGION }}
      aws_role: ${{ env.AWS_ROLE }}
      aws_ecs_cluster: ${{ env.AWS_ECS_CLUSTER }}
      aws_ecs_service: ${{ env.AWS_ECS_SERVICE }}
      aws_ecs_task_definition: ${{ env.AWS_ECS_TASK_DEFINITION }}
      aws_ecs_container_name: ${{ env.AWS_ECS_CONTAINER_NAME }}
      image_name: ${{ env.IMAGE_NAME }}
    steps:
      - run: echo "Exposing environment variables to reusable jobs"

  analyze:
    uses: ./.github/workflows/ci.yml
    with:
      mix_env: "test"
    secrets: inherit

  deployment:
    if: github.event_name == 'push'
    needs: [env-vars, analyze]
    uses: ./.github/workflows/cd.yml
    with:
      aws_region: ${{ needs.env-vars.outputs.AWS_REGION }}
      aws_role: ${{ needs.env-vars.outputs.AWS_ROLE }}
      aws_ecs_cluster: ${{ needs.env-vars.outputs.AWS_ECS_CLUSTER }}
      aws_ecs_service: ${{ needs.env-vars.outputs.AWS_ECS_SERVICE }}
      aws_ecs_task_definition: ${{ needs.env-vars.outputs.AWS_ECS_TASK_DEFINITION }}
      aws_ecs_container_name: ${{ needs.env-vars.outputs.AWS_ECS_CONTAINER_NAME }}
      image_name: ${{ needs.env-vars.outputs.IMAGE_NAME }}
    secrets: inherit
