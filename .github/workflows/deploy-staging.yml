name: Staging Deployment
on:
  workflow_dispatch:
    inputs:
      team_name:
        type: string
        description: "Name of the team's environment where the application will be deployed."
        required: false

env:
  AWS_REGION: "sa-east-1"
  AWS_ROLE: "arn:aws:iam::628877775814:role/oidc-iglu-rms-staging"
  AWS_ECS_CLUSTER: "iglu-staging-ecs-cluster"
  AWS_ECS_SERVICE: "iglu-rms-service-staging"
  AWS_ECS_TASK_DEFINITION: "iglu-rms-service-staging"
  AWS_ECS_CONTAINER_NAME: "app"
  IMAGE_NAME: "628877775814.dkr.ecr.sa-east-1.amazonaws.com/iglu-rms-staging"

jobs:
  env-vars:
    runs-on: ubuntu-latest
    outputs:
      aws_region: ${{ env.AWS_REGION }}
      aws_role: ${{ env.AWS_ROLE }}
      aws_ecs_cluster: ${{ env.AWS_ECS_CLUSTER }}
      aws_ecs_service: ${{ env.AWS_ECS_SERVICE }}
      aws_ecs_task_definition: ${{ env.AWS_ECS_TASK_DEFINITION }}
      aws_ecs_container_name: ${{ env.AWS_ECS_CONTAINER_NAME }}
      image_name: ${{ env.IMAGE_NAME }}
    steps:
      - run: echo "Exposing environment variables to reusable jobs"

  check-environment:
    runs-on: ubuntu-latest
    needs: env-vars
    outputs:
      ecs_svc_name: ${{ steps.get-ecs-service.outputs.ecs_svc_name }}

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ needs.env-vars.outputs.AWS_ROLE }}
          aws-region: ${{ needs.env-vars.outputs.AWS_REGION }}

      - id: get-ecs-service
        run: |
          if [[ -z "${{ github.event.inputs.team_name }}" ]]; then
            SVC_NAME="${{ env.AWS_ECS_SERVICE }}"
          else
            SVC_NAME="${{ env.AWS_ECS_SERVICE }}-${{ github.event.inputs.team_name }}"
          fi

          EXISTS=$(aws ecs describe-services --cluster ${{ needs.env-vars.outputs.AWS_ECS_CLUSTER }} --services ${SVC_NAME} --region ${{ needs.env-vars.outputs.AWS_REGION }} --query "services[*].serviceName" --output text)
          if [[ -z "$EXISTS" ]]; then
            echo "Service ${SVC_NAME} does not exist"
            exit 1
          fi

          echo "Service ${SVC_NAME} exists"
          echo "ecs_svc_name=${SVC_NAME}" >> $GITHUB_OUTPUT

  deployment:
    needs: [env-vars, check-environment]
    uses: ./.github/workflows/cd.yml
    with:
      aws_region: ${{ needs.env-vars.outputs.AWS_REGION }}
      aws_role: ${{ needs.env-vars.outputs.AWS_ROLE }}
      aws_ecs_cluster: ${{ needs.env-vars.outputs.AWS_ECS_CLUSTER }}
      aws_ecs_service: ${{ needs.check-environment.outputs.ecs_svc_name }}
      aws_ecs_task_definition: ${{ needs.check-environment.outputs.ecs_svc_name }}
      aws_ecs_container_name: ${{ needs.env-vars.outputs.AWS_ECS_CONTAINER_NAME }}
      image_name: ${{ needs.env-vars.outputs.IMAGE_NAME }}
    secrets: inherit
