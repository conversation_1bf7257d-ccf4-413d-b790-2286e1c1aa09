# The directory Mix will write compiled artifacts to.
/_build/

# If you run "mix test --cover", coverage assets end up here.
/cover/

# The directory Mix downloads your dependencies sources to.
/deps/

# Where 3rd-party dependencies like ExDoc output generated docs.
/doc/

# Ignore .fetch files in case you like to edit your project deps locally.
/.fetch

# If the VM crashes, it generates a dump, let's ignore it too.
erl_crash.dump

# Also ignore archive artifacts (built via "mix archive.build").
*.ez

# Temporary files, for example, from tests.
/tmp/

# Custom scripts
/scripts/

# Ignore package tarball (built via "mix hex.build").
rms-*.tar

/config/*.secret.exs

vtex_local_integration_setup.exs

.env

# Ignore vscode files
.vscode/
.zed/
.idea/
# Ignore aider files
.aider*
.aider.conf.yml
.aider.input.history
.aider.tags.cache.v2

.iex.exs
