# ENG-2474 - Improved Execution Plan (Completed)

## Phase 1: Backend Foundation

### 1. Create Upload Entity

- **Tasks**:

  1. ✅ Create migration for `upload_entries` table
     - ✅ Fields:
       - ✅ `s3_key` (string, unique)
       - ✅ `status` (enum: pending, processing, cancelled, error, expired, completed)
       - ✅ `expires_at` (utc_datetime)
       - ✅ `uploaded_at` (utc_datetime)
       - ✅ `error_messages` (string array)
       - ✅ `organization_id` (references organizations)
       - ✅ `inserted_at` and `updated_at` timestamps
     - ✅ Indexes:
       - ✅ Unique index on `s3_key`
       - ✅ Index on `status`
  2. ✅ Create `Rms.Fiscal.UploadEntry` schema module
     - ✅ Define schema fields
     - ✅ Add organization association
  3. ✅ Implement changeset for validation
     - ✅ Validate presence of required fields
     - ✅ Validate state transitions
     - ✅ Validate status inclusion
     - ✅ Enforce s3_key uniqueness
  4. ✅ Add tests
     - ✅ Test validation of required fields
     - ✅ Test state transition validity
     - ✅ Test unique constraint on s3_key
     - ✅ Test status transitions
     - ✅ Test error handling

- **Completed**:
  - Implemented robust schema with proper validations
  - Added comprehensive state transition management
  - Included error message tracking
  - Added thorough test coverage
  - Implemented concurrent index creation
  - Added proper foreign key constraints

### 2. Presigned URL Endpoint

- **Tasks**:
  1. ✅ Add route `POST /api/fiscal/product_taxes/upload_requests`
  2. ✅ Create `FiscalUploadController` with `create_upload_request` action
  3. ✅ Implement `Fiscal.create_upload_request/1` context function
  ```elixir
  def create_upload_request(attrs) do
    attrs
    |> generate_s3_key()
    |> generate_presigned_url()
    |> create_upload_entry()
  end
  ```

  4. ✅ Generate presigned URL using ExAws.S3 with:
    - ✅ 10MB size limit
    - ✅ 1 hour expiration
    - ✅ Proper AWS credentials
  5. ✅ Add tests
    - ✅ Test successful URL generation
    - ✅ Test error handling for invalid requests

- **Completed**:
  - Implemented presigned URL generation with proper configuration
  - Added comprehensive test coverage for both success and error cases
  - Implemented proper authentication handling
  - Added proper error handling and status codes
  - Included organization context in upload entries
  - Added proper mocking for S3 client in tests

- **Files**:
  - ✅ `router.ex`: Added new route under `/fiscal` scope
  - ✅ `fiscal_upload_controller.ex`: Implemented controller with proper authentication
  - ✅ `fiscal.ex`: Added context functions for upload request creation
  - ✅ `storage.ex`: Added storage context for S3 operations
  - ✅ `storage/client_behaviour.ex`: Added behaviour for storage client
  - ✅ `storage/s3_client.ex`: Implemented S3 client
  - ✅ Added proper test files with mocks and test coverage

### 3. Upload Confirmation & Monitoring

- **Tasks**:

  1. ✅ Create `ConfirmProductTaxUpload` Oban job
     - ✅ Poll S3 for file existence
     - ✅ Handle expiration status
     - ✅ Transition states: pending → processing/expired
  2. ✅ Add endpoint `POST /api/fiscal/product_taxes/uploads/:id/confirm`
     - ✅ Add route
     - ✅ Implement controller action
     - ✅ Add authentication
     - ✅ Add tests
  3. ✅ Implement exponential backoff for S3 checks
     - ✅ Initial delay: 5s
     - ✅ Max attempts: 3
  4. ✅ Add tests
     - ✅ Test job execution
     - ✅ Test retry logic
     - ✅ Test expiration handling

- **Completed**:
  - ✅ Created `ConfirmProductTaxUploadWorker` using Oban Pro
  - ✅ Implemented proper state transitions using `status_changeset`
  - ✅ Added error handling with `error_changeset`
  - ✅ Added Mox expectations for S3 client
  - ✅ Added DateTime truncation for proper storage
  - ✅ Implemented atomic updates using Ecto.Multi
  - ✅ Added comprehensive test coverage
  - ✅ Fixed mock module naming
  - ✅ Added proper logging
  - ✅ Extracted confirmation logic to `Fiscal` context
  - ✅ Added confirmation endpoint with proper error handling
  - ✅ Added endpoint tests for all scenarios

- **Files**:
  - ✅ `workers/confirm_product_tax_upload_worker.ex`: Implemented worker with proper state handling
  - ✅ `workers/confirm_product_tax_upload_worker_test.exs`: Added comprehensive tests
  - ✅ `storage/client_behaviour.ex`: Added `exists?` callback
  - ✅ `storage/s3_client.ex`: Implemented S3 existence check
  - ✅ `support/mocks/storage_client_mock.ex`: Added proper mock setup
  - ✅ `support/factory.ex`: Added proper DateTime handling in factory
  - ✅ `fiscal.ex`: Added confirmation logic
  - ✅ `controllers/fiscal_upload_controller.ex`: Added confirmation endpoint
  - ✅ `test/rms_web/controllers/fiscal_upload_controller_test.exs`: Added endpoint tests
  - ✅ `router.ex`: Added confirmation route

## Phase 2: CSV Processing

### 1. Product Tax Bulk Operations

- **Tasks**:
  1. ✅ Add unique index on `product_taxes` table
     ```elixir
     create index(:product_taxes,
     [:ncm, :sku, :uf],
     where: "ncm IS NOT NULL OR sku IS NOT NULL OR uf IS NOT NULL",
     name: :product_taxes_unique_combination
     )
     ```

  2. ✅ Implement `Fiscal.bulk_create_product_taxes/1` (Implemented as `partial_create_product_taxes/2`)
     - ✅ Use Ecto's chunked inserts
     - ✅ Handle conflicts with replace strategy
     - ✅ Use atomic transactions
     - ✅ Added error handling with partial success support
     - ✅ Added chunking with configurable chunk size
     - ✅ Implemented detailed error reporting
  3. ✅ Add tests
     - ✅ Test bulk insert success
     - ✅ Test uniqueness constraint
     - ✅ Test error handling
     - ✅ Test partial failures
     - ✅ Test duplicate handling with updates

- **Completed**:
  - Implemented robust bulk insert with partial success handling
  - Added comprehensive test coverage
  - Included proper error formatting and reporting
  - Added support for updating existing records
  - Implemented proper validation before insertion

### 2. CSV Stream Processing

- **Tasks**:

  1. ✅ Create `ProductTaxCSV` module
     - ✅ Use NimbleCSV for parsing
     - ✅ Implement header validation
     - ✅ Add validation with error messages
     - ✅ Added line number tracking
  2. ✅ Implement error-tolerant processing
     - ✅ Continue on invalid rows
     - ✅ Collect errors with row numbers
  3. ✅ Add specs for CSV format
     - ✅ Define required headers
     - ✅ Validate required fields
  4. ✅ Add tests
     - ✅ Test successful parsing
     - ✅ Test header validation
     - ✅ Test error scenarios

- **Completed**:
  - Implemented robust CSV parsing with NimbleCSV
  - Added comprehensive header validation
  - Included line number tracking for error reporting
  - Added thorough test coverage

### 3. Processing Pipeline

- **Tasks**:
  1. ✅ Create `ProcessProductTaxUpload` Oban job
  2. ✅ Implement processing pipeline
     ```elixir
     stream = generate_s3_stream(s3_key)
     |> ProductTaxCSV.parse_stream()
     |> Stream.chunk_every(@chunk_size)
     |> process_chunks()
     ```
  3. ✅ Implement S3 streaming
     - ✅ Efficient chunk-based download
     - ✅ Proper memory handling
     - ✅ Configurable chunk size and timeout
  4. ✅ Implement error tracking
     - ✅ Store errors in upload_entry
     - ✅ Detailed error messages with line numbers
  5. ✅ Add tests
     - ✅ Test successful processing
     - ✅ Test error scenarios
     - ✅ Test state transitions

- **Completed**:
  - Implemented efficient S3 streaming with proper memory handling
  - Added comprehensive error tracking and reporting
  - Implemented proper state management
  - Added detailed logging
  - Included proper cleanup and error handling

## Completion Notes
- ✅ Successfully implemented all backend functionality for product tax management
- ✅ Implemented robust CSV processing with error handling
- ✅ Added comprehensive test coverage
- ✅ Frontend tasks moved to separate plan: ENG-2474-frontend.md

## Next Steps
- Frontend implementation tracked in ENG-2474-frontend.md
- Monitor production performance
- Gather user feedback for potential improvements
