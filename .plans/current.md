# Project Plan: Cart Simulator V2 Refactor
### **1. High-Level Objective**

The current cart simulation module (`Rms.Integrations.Shopify.Carts.Simulate`) has become overly complex, making it difficult to maintain and extend. This project will replace it with a new, robust, and modular implementation. The transition will be managed via a feature flag to ensure zero downtime and a safe, incremental rollout.

### **2. Guiding Principles**

- **Single Responsibility:** Each module will have one well-defined purpose (e.g., handling a specific fulfillment type, applying discounts).
- **Clear Boundaries & Contracts:** We will use Elixir behaviours to define explicit contracts between components, especially for external API interactions.
- **Canonical Data Structures:** A single, consistent set of internal structs will be used throughout the simulation pipeline to eliminate redundant data transformations.
- **Safety and Isolation:** All new code will live in its own directory, and a feature flag will control its activation, isolating it from the existing, stable implementation.

### **3. Proposed Directory Structure**

All new code will be placed within a dedicated `simulator` directory to keep it organized and separate from the legacy module.

```
lib/rms/commerce/carts/
├── simulator/
│   ├── core.ex           # Domain structs (Cart, Item, Simulation, etc.)
│   ├── simulator.ex      # Public façade with the V1/V2 feature-flag switch
│   ├── v2.ex             # The new implementation's primary orchestrator
│   ├── discount.ex       # Pure functions for applying discounts & calculating yotals
│   ├── strategy/
│   │   ├── behaviour.ex
│   │   ├── in_store.ex
│   │   ├── local_pickup.ex
│   │   └── delivery.ex
│   └── adapters/
│       └── shopify/
│           ├── behaviour.ex
│           └── storefront_client.ex  # Handles all GraphQL communication
└── simulate.ex           # The old V1 implementation, which remains untouched initially

```

### **4. Core Data Contracts (`core.ex`)**

We will establish a set of unambiguous structs to represent the domain. These structs will be the single source of truth passed between all internal modules of the new simulator.

```elixir
defmodule Rms.Commerce.Carts.Simulator.Core do
  defmodule Cart do
    @doc "Represents the initial cart input."
    @enforce_keys [:organization_id, :items]
    defstruct [:organization_id, :customer_id, :location_id, :items, :discounts, :metadata]
  end

  defmodule Item do
    @doc "Represents a single item within the cart."
    @enforce_keys [:variant_id, :quantity]
    defstruct [:variant_id, :quantity, :fulfillment, :price, :list_price, :metadata]
  end

  defmodule Simulation do
    @doc "The result of a single fulfillment strategy."
    @enforce_keys [:strategy, :items]
    defstruct [:strategy, :items, :discounts, :messages, :is_fulfillable]
  end

  defmodule Discount do
    @doc "Represents an applicable discount, either manual or from a platform."
    @enforce_keys [:code, :value]
    defstruct [:code, :value, :type, :target, :description]
  end

  defmodule Totals do
    @doc "Represents the final calculated totals for the cart."
    defstruct [:items_list_price, :items_selling_price, :delivery_price, :manual_discount, :ecommerce_discounts, :final_price]
  end
end

```

### **5. Orchestration and Control Flow (`v2.ex`)**

The `V2` module will coordinate the entire simulation process.

1. **Decode Input:** Transform the raw controller payload into the canonical `%Cart{}` struct.
2. **Build Options:** Assemble a `keyword` list of operational flags (e.g., `ignore_inventory_level`, `ignore_automatic_discounts`) derived from feature flags to control the behavior of downstream strategies.
3. **Run Strategies in Parallel:** Execute all fulfillment strategy modules (`InStore`, `Delivery`, `LocalPickup`) concurrently using `Task.async_stream` for maximum performance.
4. **Aggregate & Apply Discounts:** Collect the `Simulation` results. Pass them, along with any manual discounts from the original cart, to the `Discount.apply/4` function to calculate final totals.
5. **Encode Output:** Format the aggregated results and final totals into the JSON structure expected by the API consumer.

### **6. Fulfillment Strategies (`strategy/`)**

Each fulfillment method will be a distinct module implementing a shared `Strategy.Behaviour`.

- **The Behaviour:**
    
    ```elixir
    defmodule Rms.Commerce.Carts.Simulator.Strategy.Behaviour do
      alias Rms.Commerce.Carts.Simulator.Core.{Cart, Simulation}
    
      @doc "Defines the contract for a fulfillment strategy."
      @callback run(Cart.t(), keyword()) :: {:ok, Simulation.t()} | {:error, term()}
    end
    
    ```
    
- **Responsibilities:** Each strategy module (`InStore`, `Delivery`, etc.) is responsible for its own logic. For external platforms like Shopify, it will use a dedicated adapter to perform API calls and will translate the platform-specific response into our internal `%Simulation{}` struct. This struct will include adjusted item prices/quantities and any applicable discounts discovered by that platform's simulation.

### **7. Discount Calculation (`discount.ex`)**

This module will contain only pure functions responsible for financial calculations.

- **Function Signature:** `apply(cart, simulations, manual_discounts, opts)`
- **Responsibilities:**
    - Aggregate all automatic discounts returned by the various fulfillment strategies.
    - Combine them with any manual discounts from the original cart payload.
    - Honor flags like `:ignore_automatic_discounts`.
    - Perform all the math required to calculate the final cart totals (`total_price`, `total_discounts`, etc.).
    - Return a `Totals` struct and any generated messages.

### **8. Implementation & Rollout Plan**

This plan is designed to be executed incrementally, ensuring stability at each step.

```markdown
# Execution Plan: Cart Simulator V2

## Phase 1: Foundation and Scaffolding
- [ ] **Feature Flag**: Create `cart_simulator_v2` in LaunchDarkly. Add a `should_use_new_simulator?/1` function to `Rms.FeatureFlag`.
- [ ] **Directory & Files**: Create the new `lib/rms/commerce/carts/simulator/` directory and all the module files outlined above.
- [ ] **Core Structs**: Define all data contracts in `lib/rms/commerce/carts/simulator/core.ex`.
- [ ] **Public Façade**: Implement the main `Rms.Commerce.Carts.Simulator.run/2` function with the feature flag switch to delegate to either V1 or V2.
- [ ] **Behaviours**: Define the `Strategy.Behaviour` and the `ShopifyAdapter.Behaviour`.
- [ ] **Scaffold Modules**: Create the strategy modules and `V2.ex` orchestrator with empty functions that conform to their contracts.

## Phase 2: Logic and Integration
- [ ] **Shopify Adapter**: Implement the new Shopify API client, adhering to the `mox-and-deps` rule with a real implementation and a mock for testing.
- [ ] **Implement Strategies**: Populate the `run/2` function for each fulfillment strategy (`InStore`, `Delivery`, `LocalPickup`), using the new adapter where necessary.
- [ ] **Implement Discount Logic**: Write the pure functions in `Discount.ex` to perform all financial calculations.
- [ ] **Implement Orchestrator**: Fully implement the `V2.run/2` orchestrator to tie all the pieces together.

## Phase 3: Testing and Validation
- [ ] **Unit Tests**: Write comprehensive tests for each module in isolation, especially for the pure logic in the `Discount` module and each `Strategy`.
- [ ] **Integration Tests**: Re-purpose fixtures from the existing `simulate_cart_test.exs` to create a new, parallel test suite. These tests will call the top-level `Simulator.run/2` with the feature flag forced `true` to guarantee that the V2 output matches the V1 output for a range of scenarios.

## Phase 4: Rollout and Deprecation
- [ ] **Staging Deployment**: Enable the `cart_simulator_v2` flag in staging environments for QA and end-to-end validation.
- [ ] **Production Rollout**: Begin a gradual production rollout using LaunchDarkly, starting at 1% of organizations and slowly increasing.
- [ ] **Monitoring**: Closely monitor error rates, performance (latency), and business metrics throughout the rollout.
- [ ] **Full Adoption**: Once the feature flag is at 100% and the new system is confirmed stable, schedule the removal of the old `Rms.Integrations.Shopify.Carts.Simulate` module and its associated tests.

```

```
lib/rms/commerce/carts/
├── simulator/
│   ├── core.ex           # Domain structs (Cart, Item, Simulation, etc.)
│   ├── simulator.ex      # Public façade with the V1/V2 feature-flag switch
│   ├── v2.ex             # The new implementation's primary orchestrator
│   ├── discount.ex       # Pure functions for applying discounts & calculating totals
│   ├── strategy/
│   │   ├── behaviour.ex
│   │   ├── in_store.ex
│   │   ├── local_pickup.ex
│   │   └── delivery.ex
│   └── adapters/
│       └── shopify/
│           ├── behaviour.ex
│           └── storefront_client.ex  # Handles all GraphQL communication
└── simulate.ex           # The old V1 implementation, which remains untouched initially

```

```elixir
defmodule Rms.Commerce.Carts.Simulator.Core do
  defmodule Cart do
    @doc "Represents the initial cart input."
    @enforce_keys [:organization_id, :items]
    defstruct [:organization_id, :customer_id, :location_id, :items, :discounts, :metadata]
  end

  defmodule Item do
    @doc "Represents a single item within the cart."
    @enforce_keys [:variant_id, :quantity]
    defstruct [:variant_id, :quantity, :fulfillment, :price, :list_price, :metadata]
  end

  defmodule Simulation do
    @doc "The result of a single fulfillment strategy."
    @enforce_keys [:strategy, :items]
    defstruct [:strategy, :items, :discounts, :messages, :is_fulfillable]
  end

  defmodule Discount do
    @doc "Represents an applicable discount, either manual or from a platform."
    @enforce_keys [:code, :value]
    defstruct [:code, :value, :type, :target, :description]
  end

  defmodule Totals do
    @doc "Represents the final calculated totals for the cart."
    defstruct [:items_list_price, :items_selling_price, :delivery_price, :manual_discount, :ecommerce_discounts, :final_price]
  end
end

```

```elixir
    defmodule Rms.Commerce.Carts.Simulator.Strategy.Behaviour do
      alias Rms.Commerce.Carts.Simulator.Core.{Cart, Simulation}

      @doc "Defines the contract for a fulfillment strategy."
      @callback run(Cart.t(), keyword()) :: {:ok, Simulation.t()} | {:error, term()}
    end

```

```markdown
# Execution Plan: Cart Simulator V2

## Phase 1: Foundation and Scaffolding
- [ ] **Feature Flag**: Create `cart_simulator_v2` in LaunchDarkly. Add a `should_use_new_simulator?/1` function to `Rms.FeatureFlag`.
- [ ] **Directory & Files**: Create the new `lib/rms/commerce/carts/simulator/` directory and all the module files outlined above.
- [ ] **Core Structs**: Define all data contracts in `lib/rms/commerce/carts/simulator/core.ex`.
- [ ] **Public Façade**: Implement the main `Rms.Commerce.Carts.Simulator.run/2` function with the feature flag switch to delegate to either V1 or V2.
- [ ] **Behaviours**: Define the `Strategy.Behaviour` and the `ShopifyAdapter.Behaviour`.
- [ ] **Scaffold Modules**: Create the strategy modules and `V2.ex` orchestrator with empty functions that conform to their contracts.

## Phase 2: Logic and Integration
- [ ] **Shopify Adapter**: Implement the new Shopify API client, adhering to the `mox-and-deps` rule with a real implementation and a mock for testing.
- [ ] **Implement Strategies**: Populate the `run/2` function for each fulfillment strategy (`InStore`, `Delivery`, `LocalPickup`), using the new adapter where necessary.
- [ ] **Implement Discount Logic**: Write the pure functions in `Discount.ex` to perform all financial calculations.
- [ ] **Implement Orchestrator**: Fully implement the `V2.run/2` orchestrator to tie all the pieces together.

## Phase 3: Testing and Validation
- [ ] **Unit Tests**: Write comprehensive tests for each module in isolation, especially for the pure logic in the `Discount` module and each `Strategy`.
- [ ] **Integration Tests**: Re-purpose fixtures from the existing `simulate_cart_test.exs` to create a new, parallel test suite. These tests will call the top-level `Simulator.run/2` with the feature flag forced `true` to guarantee that the V2 output matches the V1 output for a range of scenarios.

## Phase 4: Rollout and Deprecation
- [ ] **Staging Deployment**: Enable the `cart_simulator_v2` flag in staging environments for QA and end-to-end validation.
- [ ] **Production Rollout**: Begin a gradual production rollout using LaunchDarkly, starting at 1% of organizations and slowly increasing.
- [ ] **Monitoring**: Closely monitor error rates, performance (latency), and business metrics throughout the rollout.
- [ ] **Full Adoption**: Once the feature flag is at 100% and the new system is confirmed stable, schedule the removal of the old `Rms.Integrations.Shopify.Carts.Simulate` module and its associated tests.

```