# Cart Simulator V2 Refactor

This plan outlines the work to refactor the cart simulation logic into a new, modular, and robust system.

## Steps

- [ ] **Feature Flag**: Create `cart_simulator_v2` in LaunchDarkly and `should_use_new_simulator?/1` in `Rms.FeatureFlag`.
- [ ] **Directory & Files**: Create the `lib/rms/commerce/carts/simulator/` directory and new module files.
- [ ] **Core Structs**: Define data contracts in `lib/rms/commerce/carts/simulator/core.ex`.
- [ ] **Public Façade**: Implement `Rms.Commerce.Carts.Simulator.run/2` with the feature flag switch.
- [ ] **Behaviours**: Define `Strategy.Behaviour` and `ShopifyAdapter.Behaviour`.
- [ ] **Scaffold Modules**: Create skeleton modules for strategies and the `V2` orchestrator.
- [ ] **Shopify Adapter**: Implement the new Shopify API client (real and mock).
- [ ] **Implement Strategies**: Implement `run/2` for `InStore`, `Delivery`, and `LocalPickup`.
- [ ] **Implement Discount Logic**: Implement pure functions in `Discount.ex`.
- [ ] **Implement Orchestrator**: Fully implement the `V2.run/2` orchestrator.
- [ ] **Unit Tests**: Write unit tests for all new modules.
- [ ] **Integration Tests**: Create a parallel integration test suite to validate V2 against V1 outputs.
- [ ] **Staging Deployment**: Enable the flag in staging for QA.
- [ ] **Production Rollout**: Begin gradual production rollout via LaunchDarkly.
- [ ] **Monitoring**: Monitor performance and errors during rollout.
- [ ] **Full Adoption & Cleanup**: At 100%, remove the old `Simulate.ex` module and tests.

## Notes

The transition will be managed via a feature flag to ensure zero downtime. All new code will live in a dedicated `simulator` directory to maintain isolation from the legacy system.
