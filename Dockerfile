# Stage 1: Build the release
FROM elixir:1.17-alpine as build
WORKDIR /app
COPY mix.exs mix.lock ./

RUN mix local.hex --force && \
    mix local.rebar --force

RUN --mount=type=secret,id=oban_auth_key \
    mix hex.repo add oban https://getoban.pro/repo \
    --fetch-public-key SHA256:4/OSKi0NRF91QVVXlGAhb/BIMLnK8NHcx/EWs+aIWPc \
    --auth-key "$(cat /run/secrets/oban_auth_key)"

RUN mix deps.get && \
    MIX_ENV=prod mix compile

COPY . .
RUN MIX_ENV=prod mix release

# Stage 2: Run the release
FROM elixir:1.17-alpine
WORKDIR /app

# Install netcat-openbsd for SOCKS5 proxy readiness check
RUN apk add --no-cache netcat-openbsd

# Copy application files
COPY --from=build /app/_build/prod/rel/rms ./

# Install Tailscale binaries
COPY --from=docker.io/tailscale/tailscale:stable /usr/local/bin/tailscaled /app/tailscaled
COPY --from=docker.io/tailscale/tailscale:stable /usr/local/bin/tailscale /app/tailscale

RUN mkdir -p /var/run && ln -s /tmp/tailscale /var/run/tailscale && \
    mkdir -p /var/cache && ln -s /tmp/tailscale /var/cache/tailscale && \
    mkdir -p /var/lib && ln -s /tmp/tailscale /var/lib/tailscale && \
    mkdir -p /var/task && ln -s /tmp/tailscale /var/task/tailscale

ARG VERSION
ENV OTEL_RESOURCE_ATTRIBUTES="service.name=rms-backend,service.version=${VERSION}"

CMD ["./bin/server"]
