# Rms

To start your Phoenix server:

- Run `mix setup` to install and setup dependencies
- Start Phoenix endpoint with `mix phx.server` or inside IEx with `iex -S mix phx.server`

Now you can visit [`localhost:4000`](http://localhost:4000) from your browser.

Ready to run in production? Please [check our deployment guides](https://hexdocs.pm/phoenix/deployment.html).

## Learn more

- Official website: https://www.phoenixframework.org/
- Guides: https://hexdocs.pm/phoenix/overview.html
- Docs: https://hexdocs.pm/phoenix
- Forum: https://elixirforum.com/c/phoenix-forum
- Source: https://github.com/phoenixframework/phoenix

## Estrutura de Pastas

Quando usamos o `mix phx.new` para gerar um novo projeto Phoenix, ele vai por padrão criar uma estrutura de pastas com as seguintes pastas:

- `config/`
  - `config.exs` - esse, dev, prod e test tem config de compile-time
  - `dev.exs`
  - `prod.exs`
  - `test.exs`
  - `runtime.exs` - contém config de runtime
- `lib/`
  - `app_name/`
    - `application.ex` - define a árvore de supervião
  - `app_name_web/`
    - `controllers/` - contém todos controllers
    - `endpoint.ex`
    - `router.ex` - contém todas as rotas
- `priv/`
  - `repo/`
    - `migrations/` - contém todas migrações
    - `seeds.exs`

## Criando Imagem e Subindo Containers

Para criar uma imagem e subir os containers utilizando o arquivo `compose.dev.yml`, siga os passos abaixo:

1. **Construir a Imagem:**
   - Execute o comando na raiz do projeto para construir a imagem:
     ```bash
     docker-compose -f compose.dev.yml build
     ```

2. **Subir os Containers:**
   - Após a construção da imagem, suba os containers em segundo plano com o seguinte comando:
     ```bash
     docker-compose -f compose.dev.yml up -d
     ```

3. **Verificar os Containers em Execução:**
   - Para verificar se os containers estão em execução, utilize:
     ```bash
     docker-compose -f compose.dev.yml ps
     ```

Esses passos irão construir a imagem Docker a partir do seu `compose.dev.yml` e iniciar os serviços especificados, facilitando o desenvolvimento.

