services:
  db:
    image: postgres:16.6
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: rms_dev
    ports:
      - "5432:5432"
    volumes:
      - ./postgres_data:/var/lib/postgresql/data/
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d rms_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    
  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: Dev123@
    user: '$UID:$GID'
    ports:
      - '5050:80'
    volumes:
      - ./pgadmin_data:/var/lib/pgadmin
    
      
volumes:
  postgres_data:
  deps:
  priv:
  pgadmin-data:
