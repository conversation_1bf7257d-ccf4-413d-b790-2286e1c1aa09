import Config

config :rms, environment: :dev

# Configure your database
config :rms, Rms.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "localhost",
  database: "rms_dev",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :rms, RmsWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "Zmog3V8pvHhmdCVrhmUwzYJ6zVNuqqFyav4jIoC7hq3+zWs2wfiIoh3bqxkBWrGo",
  watchers: []

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Enable dev routes for dashboard and mailbox
config :rms, dev_routes: true

config :rms, :vinco_key_partner, System.get_env("VINCO_KEY_PARTNER")
config :rms, :vinco_token, System.get_env("VINCO_TOKEN")
config :rms, :vinco_url, System.get_env("VINCO_URL")

config :rms, :one_singal_url, System.get_env("ONE_SIGNAL_URL")
config :rms, :one_singal_api_key, System.get_env("ONE_SIGNAL_API_KEY")
config :rms, :one_singal_app_id, System.get_env("ONE_SIGNAL_APP_ID")

# Do not include metadata nor timestamps in development logs
config :logger, :console, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# auth0 dev tentant public key
config :rms, Rms.Guardian,
  issuer: "https://dev-56q2f1zjdyev02nl.us.auth0.com/",
  verify_issuer: true,
  allowed_algos: ["RS256"],
  secret_key: %{
    "e" => "AQAB",
    "kty" => "RSA",
    "n" =>
      "nQX1oCw5OBwvEC_6Yw4huU4ok7HdSRsVh-Y2MHlyV8uv2t6H99NWhvzXLCsJYIF2j9ivrSA_mXHhTU8vGzqHNMW5vN4pFZuEEQwusQUXZZ35Gco77kaJgyY0vKcW7iwu_ujaR2FDowP5QckRJ7iYkJdUzajY5ljw3AtBI8dT1UF18bBiSek8TZieccQJJaJgY7amUHl3mNiUmTXNa6DoRMtejcVcB8YNV7IYSxhRJzs8yG12TW7j9_HA6_D45EPFmTsN1A3FkjlH4g7JgP7TsFfLjpD2hn4LikbE3Y4lHqZZ0hmvmcfBxYX06D_8vHukNZQRu1r4AnBFrpEk7WPE9Q"
  }

config :rms, svix_token: "testsk_sIXc4YR1Cp1nVrvj3VLSfvq2GofoqFaa.us"

config :rms, shopify_client_secret: "ffaaca2898cae8b876bfe29e6877db87"

config :rms, Rms.Integrations.GiftPromo,
  capture_type: "1",
  provider: "2",
  base_url: "https://secure-stage.giftpromo.com.br/giftpromows/api.svc/secure/apigp"

config :rms,
  oban_pro_user: "admin",
  oban_pro_password: "secret",
  integrator_url: "http://localhost:5000",
  integrator_service_api_key: "dev_placeholder"

config :opentelemetry,
  traces_exporter: :none
