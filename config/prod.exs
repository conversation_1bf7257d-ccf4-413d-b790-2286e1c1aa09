import Config

config :rms, environment: :prod

# Configures Swoosh API Client
config :swoosh, api_client: Swoosh.ApiClient.Finch, finch_name: R<PERSON>.Finch

# Disable Swoosh Local Memory Storage
config :swoosh, local: false

# Do not print debug messages in production
config :logger, level: :info
config :logger, :default_handler, formatter: {LoggerJSON.Formatters.Basic, metadata: :all}

config :opentelemetry,
  span_processor: :batch,
  traces_exporter: :otlp

config :rms, Rms.Guardian,
  issuer: "https://auth.iglu.io/",
  verify_issuer: true,
  allowed_algos: ["RS256"],
  secret_key: %{
    "e" => "AQAB",
    "kty" => "RSA",
    "n" =>
      "9YZnj7Xb-PTP9ez1GVkxWIXp9Jmxhqs8K0PXntMqUxYRIA7I1W5xhxnj9IVahul_Nsd4ZexjbR1X-w_mn6MbH_r3h-ehAYDrivbLrZSB2Tw9E1UtLv1ev1cuJfj9OR4Tqrd_w7wD_cAaXfeT2CNxoy8IeADa6q34vuuhP8F9OCyZI_zwKqDOhLP1YompjG6uWAEVCDOUY0rq69-PBHaJJcKeWe_VjXYNBpD9tYlXC7KD1LbqEFR4KIVwYCYRf8gn-lb9Ak47We-pv0ULukEIy44ZY9ySKjBLxbR14AuWASDIB_QewJPggg8pOzcVaJ0ez5AzOd1a3vBT_G1n7VaMBQ"
  }

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.

config :rms, Rms.Integrations.GiftPromo,
  capture_type: "1",
  provider: "16",
  base_url: "https://secure.giftpromo.com.br/giftpromows/api.svc/secure/apigp"

config :rms, launchdarkly_sdk_key: "****************************************"
