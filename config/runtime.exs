import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/rms start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.
if System.get_env("PHX_SERVER") do
  config :rms, RmsWeb.Endpoint, server: true
end

if System.get_env("MIGRATION") do
  config :rms, Rms.Repo, queue_target: 60_000
end

if System.get_env("ENVIRONMENT") == "STAGING" do
  # auth0 dev tentant public key
  config :rms, Rms.Guardian,
    issuer: "https://dev-56q2f1zjdyev02nl.us.auth0.com/",
    verify_issuer: true,
    allowed_algos: ["RS256"],
    secret_key: %{
      "e" => "AQAB",
      "kty" => "RSA",
      "n" =>
        "nQX1oCw5OBwvEC_6Yw4huU4ok7HdSRsVh-Y2MHlyV8uv2t6H99NWhvzXLCsJYIF2j9ivrSA_mXHhTU8vGzqHNMW5vN4pFZuEEQwusQUXZZ35Gco77kaJgyY0vKcW7iwu_ujaR2FDowP5QckRJ7iYkJdUzajY5ljw3AtBI8dT1UF18bBiSek8TZieccQJJaJgY7amUHl3mNiUmTXNa6DoRMtejcVcB8YNV7IYSxhRJzs8yG12TW7j9_HA6_D45EPFmTsN1A3FkjlH4g7JgP7TsFfLjpD2hn4LikbE3Y4lHqZZ0hmvmcfBxYX06D_8vHukNZQRu1r4AnBFrpEk7WPE9Q"
    }
end

if config_env() == :prod do
  config :rms,
    oban_pro_user: System.get_env("OBAN_PRO_USER"),
    oban_pro_password: System.get_env("OBAN_PRO_PASSWORD")

  config :rms, svix_token: System.get_env("SVIX_TOKEN")
  config :rms, shopify_client_secret: System.get_env("SHOPIFY_CLIENT_SECRET")
  config :rms, smart_store_api_key: System.get_env("SMART_STORE_API_KEY")

  config :rms,
    integrator_url: System.get_env("INTEGRATOR_URL", "http://localhost:5000"),
    integrator_service_api_key: System.get_env("RMS_INTEGRATOR_SERVICE_API_KEY")

  database_url =
    System.get_env("DATABASE_URL") ||
      raise """
      environment variable DATABASE_URL is missing.
      For example: ecto://USER:PASS@HOST/DATABASE
      """

  maybe_ipv6 = if System.get_env("ECTO_IPV6") in ~w(true 1), do: [:inet6], else: []

  config :rms, Rms.Repo,
    ssl: true,
    ssl_opts: [
      verify: :verify_none
    ],
    url: database_url,
    pool_size: String.to_integer(System.get_env("POOL_SIZE") || "20"),
    socket_options: maybe_ipv6

  # The secret key base is used to sign/encrypt cookies and other secrets.
  # A default value is used in config/dev.exs and config/test.exs but you
  # want to use a different value for prod and you most likely don't want
  # to check this value into version control, so we use an environment
  # variable instead.
  secret_key_base =
    System.get_env("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host = System.get_env("PHX_HOST") || "example.com"
  port = String.to_integer(System.get_env("PORT") || "4000")

  config :rms, :dns_cluster_query, System.get_env("DNS_CLUSTER_QUERY")

  config :rms, :vinco_key_partner, System.get_env("VINCO_KEY_PARTNER")
  config :rms, :vinco_token, System.get_env("VINCO_TOKEN")
  config :rms, :vinco_url, System.get_env("VINCO_URL")

  config :rms, :one_singal_url, System.get_env("ONE_SIGNAL_URL")
  config :rms, :one_singal_api_key, System.get_env("ONE_SIGNAL_API_KEY")
  config :rms, :one_singal_app_id, System.get_env("ONE_SIGNAL_APP_ID")

  config :opentelemetry_exporter,
    otlp_protocol: :http_protobuf,
    otlp_endpoint: "http://localhost:4318"

  config :rms, RmsWeb.Endpoint,
    url: [host: host, port: 443, scheme: "https"],
    http: [
      # Enable IPv6 and bind on all interfaces.
      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      ip: {0, 0, 0, 0, 0, 0, 0, 0},
      port: port
    ],
    secret_key_base: secret_key_base

  # ## SSL Support
  #
  # To get SSL working, you will need to add the `https` key
  # to your endpoint configuration:
  #
  #     config :rms, RmsWeb.Endpoint,
  #       https: [
  #         ...,
  #         port: 443,
  #         cipher_suite: :strong,
  #         keyfile: System.get_env("SOME_APP_SSL_KEY_PATH"),
  #         certfile: System.get_env("SOME_APP_SSL_CERT_PATH")
  #       ]
  #
  # The `cipher_suite` is set to `:strong` to support only the
  # latest and more secure SSL ciphers. This means old browsers
  # and clients may not be supported. You can set it to
  # `:compatible` for wider support.
  #
  # `:keyfile` and `:certfile` expect an absolute path to the key
  # and cert in disk or a relative path inside priv, for example
  # "priv/ssl/server.key". For all supported SSL configuration
  # options, see https://hexdocs.pm/plug/Plug.SSL.html#configure/1
  #
  # We also recommend setting `force_ssl` in your endpoint, ensuring
  # no data is ever sent via http, always redirecting to https:
  #
  #     config :rms, RmsWeb.Endpoint,
  #       force_ssl: [hsts: true]
  #
  # Check `Plug.SSL` for all available options in `force_ssl`.

  # ## Configuring the mailer
  #
  # In production you need to configure the mailer to use a different adapter.
  # Also, you may need to configure the Swoosh API client of your choice if you
  # are not using SMTP. Here is an example of the configuration:
  #
  config :rms, Rms.Mailer,
    adapter: Swoosh.Adapters.Sendgrid,
    api_key: System.get_env("SENDGRID_API_KEY")

  #
  # For this example you need include a HTTP client required by Swoosh API client.
  # Swoosh supports Hackney and Finch out of the box:
  #
  #     config :swoosh, :api_client, Swoosh.ApiClient.Hackney
  #
  # See https://hexdocs.pm/swoosh/Swoosh.html#module-installation for details.

  cloak_key =
    "CLOAK_KEY"
    |> System.fetch_env!()
    |> Base.decode64!()

  config :rms, Rms.Vault,
    ciphers: [
      default: {
        Cloak.Ciphers.AES.GCM,
        tag: "AES.GCM.V1", key: cloak_key, iv_length: 12
      }
    ]
end
