import Config

config :rms, environment: :test

# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
config :rms, Rms.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "localhost",
  database: "rms_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :rms, RmsWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "iLsBZ+GXfJ4sKoYrvwmjFWotx4X/pwVrzpnAecrQ1Q4BBLxa3PuYi8b3BqNL2Gm7",
  server: false

config :rms, Oban,
  notifier: <PERSON>ban.Notifiers.PG,
  queues: false,
  plugins: false,
  testing: :manual

config :rms, Rms.Events,
  queues: false,
  plugins: false,
  testing: :manual

# In test we don't send emails.
config :rms, Rms.Mailer, adapter: Swoosh.Adapters.Test

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# Print only warnings and errors during test
config :logger,
  level: :warning,
  backends: []

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

config :rms, Rms.Guardian,
  issuer: "rms",
  secret_key: "1GTShqvI8xMt7taJEvZi9LwkEXQ6X6QWgMznP61V7SvPSJySVinaC8+DhRCoeg/u"

config :rms, Rms.Vault,
  ciphers: [
    default: {Rms.Vault.PlaintextCipher, []}
  ]

config :tesla, adapter: Tesla.Mock

config :rms, :vinco_url, "vinco_url"

config :rms, :pagarme_client, PagarMeClientMock
config :rms, :shopify_client, Rms.Integrations.Shopify.Mock
config :rms, :viacep_client, Rms.Integrations.Viacep.Mock
config :rms, :ibge_client, Rms.Integrations.Ibge.Mock
config :rms, :storage_client, Rms.Storage.Mock

config :rms, :vinco_client, VincoClientMock
config :rms, :integrator_client, Rms.Integrator.Mock
config :rms, :vtex_client, Rms.Integrations.VTEX.Mock
config :rms, :gift_promo_client, Rms.Integrations.GiftPromo.Mock
config :rms, :smart_store_client, Rms.Integrations.SmartStore.Mock
config :rms, :one_signal_client, Rms.Integrations.OneSignal.Mock
config :rms, :sallve_client, Rms.Integrations.Erp.Sallve.Mock
config :rms, :amaro_client, Rms.Integrations.Erp.Amaro.Mock
config :rms, :cielo_client, Rms.Integrations.Cielo.Mock
config :rms, :feature_flag, Rms.Clients.FeatureFlag.Mock

config :rms, Rms.Integrations.GiftPromo,
  capture_type: "1",
  provider: "2",
  base_url: "https://secure-stage.giftpromo.com.br/giftpromows/api.svc/secure/apigp"

config :rms, shopify_client_secret: "some-client-secret"

config :rms, launchdarkly_sdk_key: "test"

# Configure ExAws to use localstack in test
config :ex_aws,
  json_codec: Jason,
  access_key_id: "test",
  secret_access_key: "test",
  region: "us-east-1"

config :ex_aws, :s3,
  scheme: "http://",
  host: "localhost",
  port: 4566

config :rms, :upload_bucket, "test-bucket"
