defmodule Rms.Accounts do
  alias Rms.Accounts.ApiToken
  alias Rms.Repo
  alias Rms.Accounts.User

  import Ecto.Query

  @doc """
  Return a user by ID.

  ## Examples

      iex> get_user!(1)
      %User{}

      iex> get_user!(2)
      (** Ecto.NoResultsError)

  """
  def get_user!(organization_id, id) do
    Repo.get_by!(User, id: id, organization_id: organization_id)
  end

  @doc """
  Return a user by ID.

  ## Examples

      iex> unsafe_get_user!(1)
      %User{}

      iex> unsafe_get_user!(2)
      (** Ecto.NoResultsError)

  """
  def unsafe_get_user!(id) do
    Repo.get!(User, id)
  end

  @doc """
  Gets a user by provider and external_id.

  ## Examples

      iex> get_user_by_provider_and_external_id("auth0", "123456")
      {:ok, %User{}}

      iex> get_user_by_provider_and_external_id("auth0", "invalid")
      {:error, :not_found}

  """
  def get_user_by_provider_and_external_id(provider, external_id) do
    case Repo.get_by(User, provider: provider, external_id: external_id) do
      nil -> {:error, :not_found}
      user -> {:ok, user}
    end
  end

  @doc """
  Create a user.

  ## Examples

      iex> create_user(%{name: "John Doe", email: "<EMAIL>", external_id: "external-id"})
      {:ok, %User{}}

      iex> create_user(%{invalid: "value"})
      {:error, %Ecto.Changeset{}}

  """
  def create_user(attrs) do
    org = get_or_create_org!(attrs)

    %User{organization_id: org.id}
    |> User.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:provider, :external_id]},
      conflict_target: [:provider, :external_id],
      returning: true
    )
  end

  alias Rms.Accounts.LocationUser

  def assign_location(user, location_id) do
    user
    |> Ecto.build_assoc(:assigned_locations, location_id: location_id)
    |> LocationUser.changeset(%{})
    |> Repo.insert(on_conflict: :nothing, conflict_target: [:user_id, :location_id])
  end

  def assigned_location_ids(resource) do
    case resource do
      %ApiToken{} ->
        :all

      %User{} ->
        LocationUser
        |> where(user_id: ^resource.id)
        |> select([lu], lu.location_id)
        |> Repo.all()
    end
  end

  alias Rms.Accounts.Organization

  @doc """
  Gets an existing organization by org_id or creates a new one if it doesn't exist.

  ## Parameters

    - `attrs`: The attributes containing the org_id.

  ## Examples

      iex> get_or_create_org!(%{org_id: "acme-corp"})
      %Organization{}

      iex> get_or_create_org!(%{invalid: "value"})
      (** Ecto.ChangesetError)

  """
  def get_or_create_org!(attrs) do
    org_id = Map.get(attrs, :org_id) || Map.get(attrs, "org_id")

    case Repo.get_by(Organization, org_id: org_id) do
      nil ->
        # Organization doesn't exist, create it
        %Organization{}
        |> Organization.changeset(attrs)
        |> Repo.insert!()

      org ->
        # Organization exists, return it
        org
    end
  end

  @doc """
  Create an organization.

  ## Examples

      iex> create_org!(%{name: "Acme Corp", org_id: "acme-corp"})
      %Organization{}

      iex> create_org!(%{invalid: "value"})
      (** Ecto.ChangesetError)

  """
  def create_org!(attrs) do
    %Organization{}
    |> Organization.changeset(attrs)
    |> Repo.insert!(
      on_conflict: {:replace, [:org_id]},
      conflict_target: [:org_id],
      returning: true
    )
  end

  @doc """
  Retrieves an organization by its ID.

  ## Parameters

    - `id`: The ID of the organization to retrieve.

  ## Examples

      iex> get_organization!(1)
      %Organization{}

      iex> get_organization!(2)
      (** Ecto.NoResultsError)

  """
  def get_organization!(id) do
    Repo.get!(Organization, id)
  end

  alias Rms.Accounts.Location

  @doc """
  Lists all archived locations for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization whose archived locations are to be listed.

  ## Examples

      iex> list_locations(1)
      [%Location{}, %Location{}]

      iex> list_locations(1, allow_archived?: true)
      [%Location{}, %Location{}]

  """
  def list_locations(organization_id, opts \\ []) do
    allow_archived? = Keyword.get(opts, :allow_archived?, false)

    Location
    |> where([l], l.organization_id == ^organization_id)
    |> where([l], ^allow_archived? or is_nil(l.archived_at))
    |> preload(:address)
    |> Repo.all()
  end

  @doc """
  Paginates the list of locations for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the locations.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:after` - A cursor value for fetching results after this cursor.
    - `:before` - A cursor value for fetching results before this cursor.
    - `:limit` - The maximum number of locations to return. Defaults to 20.
    - `:preload` - A list of associations to preload.
    - `:allow_archived` - A boolean value to include archived locations. Defaults to false.
    - `:name` - A string value to filter locations by name.

  ## Examples

      iex> Rms.Accounts.paginated_locations(1, limit: 10, after: "cursor_value")
      %{entries: [%Rms.Accounts.Location{}, ...], metadata: %{}}
  """
  def paginated_locations(organization_id, opts \\ []) do
    query = build_locations_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      opts
      |> Keyword.get(:limit, 20)
      |> case do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    preloads = Keyword.get(opts, :preload, [])
    query = query |> preload(^preloads) |> order_by(desc: :inserted_at, desc: :id)

    paginate_opts = [
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:inserted_at, :desc}, {:id, :desc}]
    ]

    Repo.paginate(query, paginate_opts)
  end

  defp build_locations_query(organization_id, opts) do
    allow_archived? = Keyword.get(opts, :allow_archived, false)

    query =
      Location
      |> where(organization_id: ^organization_id)
      |> where([l], ^allow_archived? or is_nil(l.archived_at))

    Enum.reduce(opts, query, fn
      {:name, value}, query ->
        where(query, [l], fragment("? %> ?", l.name, ^value))

      {:cnpj, value}, query ->
        where(query, [l], l.cnpj == ^value)

      _, query ->
        query
    end)
  end

  @doc """
  Return a location by ID and organization ID.

  ## Examples

      iex> get_location!(1, 1)
      %Location{}

      iex> get_location!(2, 1)
      (** Ecto.NoResultsError)

  """
  def get_location!(organization_id, location_id, opts \\ []) do
    allow_archived? = Keyword.get(opts, :allow_archived?, false)
    preloads = Keyword.get(opts, :preload, [:address])

    Location
    |> where([l], ^allow_archived? or is_nil(l.archived_at))
    |> where(id: ^location_id, organization_id: ^organization_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Creates a location with the given attributes.

  ## Parameters

    - `organization_id`: The associated Organization
    - `attrs`: The attributes to create the location with.

  ## Examples

      iex> create_location(1, %{name: "New Location", address: "123 Main St"})
      {:ok, %Location{}}

      iex> create_location(2, %{invalid: "data"})
      {:error, %Ecto.Changeset{}}

  """
  def create_location(organization_id, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :location,
      Location.changeset(%Location{organization_id: organization_id}, attrs)
    )
    |> Ecto.Multi.run(:map_location, fn _repo, %{location: location} ->
      Oban.insert(
        Rms.Workers.ImportLocationWorker.new(%{
          "organization_id" => location.organization_id,
          "location_id" => location.id
        })
      )
    end)
    |> Repo.transaction()
    |> create_location_normalizer()
  end

  defp create_location_normalizer(result) do
    case result do
      {:ok, %{location: location}} ->
        {:ok, location}

      {:error, :location, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  @doc """
  Updates a location with the given attributes.

  ## Parameters

    - `location`: The Location struct to update.
    - `attrs`: The attributes to update the location with.

  ## Examples

      iex> update_location(%Location{id: 1}, %{name: "Updated Location"})
      {:ok, %Location{}}

      iex> update_location(%Location{}, %{invalid: "data"})
      {:error, %Ecto.Changeset{}}

  """
  def update_location(location, attrs) do
    result =
      Ecto.Multi.new()
      |> Ecto.Multi.update(:location, Location.update_changeset(location, attrs))
      |> Ecto.Multi.run(:preload_location, fn _, %{location: location} ->
        {:ok, Repo.preload(location, :address)}
      end)
      |> Ecto.Multi.run(:map_location, fn _repo, %{location: location} ->
        Oban.insert(
          Rms.Workers.ImportLocationWorker.new(%{
            "organization_id" => location.organization_id,
            "location_id" => location.id
          })
        )
      end)
      |> Repo.transaction()

    case result do
      {:ok, %{preload_location: location}} ->
        {:ok, location}

      {:error, :location, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  @doc """
  Archives a location by setting its `archived_at` timestamp.

  ## Parameters

    - `location`: The Location struct to archive.

  ## Examples

      iex> archive_location(%Location{id: 1})
      {:ok, %Location{}}

      iex> archive_location(%Location{})
      {:error, %Ecto.Changeset{}}

  """
  def archive_location(location) do
    location
    |> Location.archive_changeset()
    |> Repo.update()
  end

  @doc """
  Deletes a location.

  ## Parameters

    - `location`: The Location struct to delete.

  ## Examples

      iex> delete_location(%Location{id: 1})
      {:ok, %Location{}}

      iex> delete_location(%Location{})
      {:error, %Ecto.Changeset{}}

  """
  def delete_location(location) do
    location
    |> Location.delete_changeset()
    |> Repo.delete()
  end

  alias Rms.Accounts.Dock

  @doc """
  Creates a dock with the given attributes.

  ## Parameters

    - `organization_id`: The associated Organization
    - `attrs`: The attributes to create the dock with.

  ## Examples

      iex> create_dock(1, %{name: "New Dock", location_id: 1, external_id: "1234"})
      {:ok, %Dock{}}

      iex> create_dock(2, %{invalid: "data"})
      {:error, %Ecto.Changeset{}}

  """
  def create_dock(organization_id, attrs) do
    %Dock{organization_id: organization_id}
    |> Dock.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Paginates the list of docks for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the docks.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:after` - A cursor value for fetching results after this cursor.
    - `:before` - A cursor value for fetching results before this cursor.
    - `:limit` - The maximum number of docks to return. Defaults to 20.
    - `:preload` - A list of associations to preload.
    - `:name` - A string value to filter docks by name.

  ## Examples

      iex> Rms.Accounts.paginated_docks(1, limit: 10, after: "cursor_value")
      %{entries: [%Rms.Accounts.Dock{}, ...], metadata: %{}}
  """
  def paginated_docks(organization_id, opts \\ []) do
    query = build_docks_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      opts
      |> Keyword.get(:limit, 20)
      |> case do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    preloads = Keyword.get(opts, :preload, [])
    query = query |> preload(^preloads) |> order_by(desc: :inserted_at, desc: :id)

    paginate_opts = [
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:inserted_at, :desc}, {:id, :desc}]
    ]

    Repo.paginate(query, paginate_opts)
  end

  defp build_docks_query(organization_id, opts) do
    query =
      Dock
      |> where(organization_id: ^organization_id)

    Enum.reduce(opts, query, fn
      {:name, value}, query ->
        where(query, [l], fragment("? %> ?", l.name, ^value))

      _, query ->
        query
    end)
  end

  @doc """
  Return a dock by ID and organization ID.

  ## Examples

      iex> get_dock!(1, 1)
      %Dock{}

      iex> get_dock!(2, 1)
      (** Ecto.NoResultsError)

  """
  def get_dock!(organization_id, dock_id) do
    Dock
    |> where(id: ^dock_id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  def get_docks_from_external_ids(organization_id, external_ids) do
    Dock
    |> where(
      [d],
      d.organization_id == ^organization_id and
        d.external_id in ^external_ids
    )
    |> select([d], {d.id, d.external_id})
    |> Repo.all()
    |> Enum.into(%{})
  end

  @doc """
  Deletes a dock.

  ## Parameters

    - `dock`: The Dock struct to delete.

  ## Examples

      iex> delete_dock(%Dock{id: 1})
      {:ok, %Dock{}}

      iex> delete_dock(%Dock{})
      {:error, %Ecto.Changeset{}}

  """
  def delete_dock(dock) do
    dock
    |> Repo.delete()
  end

  alias Rms.Accounts.ApiToken

  @doc """
  Creates an API token for the given organization.

  ## Parameters

    - `organization`: The organization for which the API token will be created.

  ## Examples

      iex> create_api_token!(%Organization{id: 1})
      %ApiToken{}

      iex> create_api_token!(%{invalid: "data"})
      (** Ecto.ChangesetError)

  """
  def create_api_token!(organization) do
    %ApiToken{organization_id: organization.id}
    |> ApiToken.changeset(%{})
    |> Repo.insert!()
  end

  @doc """
  Replicates the behavior of `create_api_token!` using `Repo.insert/1`.

  ## Parameters

    - `organization`: The organization for which the API token will be created.

  ## Examples

      iex> replicate_create_api_token!(%Organization{id: 1})
      {:ok, %ApiToken{}}

      iex> replicate_create_api_token!(%{invalid: "data"})
      {:error, %Ecto.Changeset{}}

  """
  def create_api_token(organization) do
    %ApiToken{organization_id: organization.id}
    |> ApiToken.changeset(%{})
    |> Repo.insert()
  end

  @doc """
  Deletes an API token for the given organization.

  ## Parameters

    - `organization_id`: The ID of the organization whose API token will be deleted.

  ## Examples

      iex> delete_api_token!(1)
      {:ok, %ApiToken{}}

      iex> delete_api_token!(999)
      {:error, :not_found}

  """
  def delete_api_token!(organization_id) do
    token = Repo.get_by!(ApiToken, organization_id: organization_id)

    Repo.delete(token)
  end

  @doc """
  Retrieves an API token by its value and updates the `last_used` timestamp.

  ## Parameters

    - `token`: The value of the API token to retrieve.

  ## Examples

      iex> get_api_token!("some_token_value")
      %ApiToken{}

      iex> get_api_token!("invalid_token")
      (** Ecto.NoResultsError)

  """
  def get_api_token!(token_value) do
    Repo.get_by!(ApiToken, token_hash: token_value)
  end

  @doc """
  Retrieves an API token by its organization ID.
  Returns the decrypted token value.

  ## Parameters

    - `organization_id`: The ID of the organization.

  ## Examples

      iex> get_api_token_by_organization_id!(1)
      "some_decrypted_token_value"

      iex> get_api_token_by_organization_id!(999)
      (** Ecto.NoResultsError)

  """
  def get_api_token_by_organization_id!(organization_id) do
    api_token = Repo.get_by!(ApiToken, organization_id: organization_id)
    api_token.token
  end

  alias Rms.Accounts.Staff

  def list_staffs(organization_id, opts \\ []) do
    allow_archived? = Keyword.get(opts, :allow_archived?, false)
    query_params = Keyword.get(opts, :query_params, [])

    Staff
    |> join(:inner, [s], sl in assoc(s, :staff_locations))
    |> join(:inner, [s, sl], l in assoc(sl, :location))
    |> join(:inner, [s, sl, l], sr in assoc(s, :staff_role))
    |> where([s, sl, l, sr], s.organization_id == ^organization_id)
    |> where([s, sl, l, sr], ^allow_archived? or is_nil(s.archived_at))
    |> where(^add_staff_filter_where(query_params))
    |> preload(:staff_locations)
    |> Repo.all()
  end

  def validate_staffs_organization(organization_id, staffs_ids) do
    Staff
    |> where(organization_id: ^organization_id)
    |> where([s, sl, l], s.id in ^staffs_ids)
    |> Repo.all()
    |> case do
      staffs when length(staffs) == length(staffs_ids) -> :ok
      _ -> {:error, :invalid_staffs}
    end
  end

  def paginated_staff(organization_id, opts \\ []) do
    allow_archived? = Keyword.get(opts, :allow_archived?, false)
    preloads = Keyword.get(opts, :preloads, [])
    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      opts
      |> Keyword.get(:limit, 20)
      |> case do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    query =
      Staff
      |> join(:inner, [s], sl in assoc(s, :staff_locations))
      |> join(:inner, [s, sl], l in assoc(sl, :location))
      |> join(:inner, [s, sl, l], sr in assoc(s, :staff_role))
      |> where([s, sl, l, sr], s.organization_id == ^organization_id)
      |> where([s, sl, l, sr], ^allow_archived? or is_nil(s.archived_at))
      |> where(^add_staff_filter_where(opts))
      |> distinct(true)
      |> preload(^preloads)
      |> order_by(asc: :name, desc: :id)

    paginate_opts =
      [
        after: cursor_after,
        before: cursor_before,
        limit: limit,
        cursor_fields: [{:name, :asc}, {:id, :desc}]
      ]

    Repo.paginate(query, paginate_opts)
  end

  def get_staff!(organization_id, staff_id, opts \\ []) do
    allow_archived? = Keyword.get(opts, :allow_archived?, false)
    preloads = Keyword.get(opts, :preloads, [])

    Staff
    |> where([s], ^allow_archived? or is_nil(s.archived_at))
    |> where(id: ^staff_id, organization_id: ^organization_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  def add_staff_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {:roles, {:seller, value}}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and sr.seller == ^value)

      {:roles, {:stocker, value}}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and sr.stocker == ^value)

      {:roles, {:cashier, value}}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and sr.cashier == ^value)

      {:location_id, value}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and l.id == ^value)

      {:location_cnpj, value}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and l.cnpj == ^value)

      {:staff_name, value}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and fragment("? %> ?", s.name, ^value))

      {:staff_external_id, value}, dynamic ->
        dynamic([s, sl, l, sr], ^dynamic and s.external_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def create_staff(organization_id, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :staff,
      Staff.changeset(%Staff{organization_id: organization_id}, attrs)
    )
    |> Ecto.Multi.run(:map_staff, fn _repo, %{staff: staff} ->
      Rms.Events.emit("staff.created", Staff.event_payload(staff))
    end)
    |> Repo.transaction()
    |> transaction_staff_normalizer()
  end

  def transaction_staff_normalizer(result) do
    case result do
      {:ok, %{staff: staff}} ->
        {:ok, staff}

      {:error, :staff, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  def update_staff(staff, attrs) do
    staff = Repo.preload(staff, :staff_locations)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:staff, Staff.update_changeset(staff, attrs))
    |> Ecto.Multi.run(:map_staff, fn _repo, %{staff: staff} ->
      Rms.Events.emit("staff.updated", Staff.event_payload(staff))
    end)
    |> Repo.transaction()
    |> transaction_staff_normalizer()
  end

  def archive_staff(staff) do
    staff
    |> Staff.archive_changeset()
    |> Repo.update()
  end

  def delete_staff(staff) do
    staff
    |> Staff.delete_changeset()
    |> Repo.delete()
  end
end
