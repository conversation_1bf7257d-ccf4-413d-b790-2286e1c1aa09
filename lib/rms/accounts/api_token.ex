defmodule Rms.Accounts.ApiToken do
  use Ecto.Schema

  import Ecto.Changeset

  schema "api_tokens" do
    field :token, Rms.Vault.EncryptedBinary
    field :token_hash, Cloak.Ecto.SHA256

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  def changeset(api_token, attrs) do
    api_token
    |> cast(attrs, [:organization_id, :token])
    |> generate_token()
    |> put_token_hash()
    |> validate_required([:token, :token_hash])
    |> unique_constraint(:token_hash)
    |> unique_constraint(:organization_id)
  end

  defp generate_token(changeset) do
    case get_field(changeset, :token) do
      nil ->
        secure_token = :crypto.strong_rand_bytes(64) |> Base.url_encode64()
        put_change(changeset, :token, secure_token)

      _ ->
        changeset
    end
  end

  defp put_token_hash(changeset) do
    token = get_change(changeset, :token)
    put_change(changeset, :token_hash, token)
  end
end
