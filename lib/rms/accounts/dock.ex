defmodule Rms.Accounts.Dock do
  use Ecto.Schema
  import Ecto.Changeset

  alias Rms.Accounts.Location
  alias Rms.Accounts.Organization

  schema "docks" do
    field :name, :string
    field :external_id, :string
    belongs_to :location, Location
    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(dock, attrs) do
    dock
    |> cast(attrs, [:name, :external_id, :location_id])
    |> validate_required([:name, :external_id])
    |> foreign_key_constraint(:organization)
    |> foreign_key_constraint(:location)
    |> unique_constraint([:organization_id, :location_id, :external_id], error_key: :external_id)
  end

  @doc false
  def update_changeset(dock, attrs) do
    dock
    |> cast(attrs, [:name, :external_id])
  end
end
