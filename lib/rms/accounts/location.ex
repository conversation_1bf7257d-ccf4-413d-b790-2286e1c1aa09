defmodule Rms.Accounts.Location do
  use Ecto.Schema
  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Fiscal.LocationTax

  schema "locations" do
    field :name, :string
    field :cnpj, :string
    field :external_id, :string
    belongs_to :organization, Organization

    has_one :address, Rms.Addresses.Address,
      on_replace: :delete_if_exists,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :location_mappings, Rms.Integrations.LocationMapping

    has_one :location_tax, LocationTax,
      on_replace: :mark_as_invalid,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :archived_at, :utc_datetime
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(location, attrs) do
    location
    |> cast(attrs, [:name, :cnpj])
    |> cast_assoc(:address, required: true)
    |> cast_assoc(:location_tax, with: &Rms.Fiscal.LocationTax.assoc_changeset/2)
    |> validate_required([:name, :cnpj])
    |> foreign_key_constraint(:organization)
  end

  @doc false
  def update_changeset(location, attrs) do
    location
    |> cast(attrs, [:name, :cnpj, :external_id])
    |> cast_assoc(:address)
    |> cast_assoc(:location_tax, with: &Rms.Fiscal.LocationTax.assoc_changeset/2)
  end

  @doc false
  def archive_changeset(location) do
    now = DateTime.truncate(DateTime.utc_now(), :second)
    change(location, archived_at: now)
  end

  @doc false
  def unarchive_changeset(location) do
    change(location, archived_at: nil)
  end

  @doc false
  def delete_changeset(location) do
    changeset = change(location)

    if field_missing?(changeset, :archived_at),
      do: add_error(changeset, :id, "can only delete archived locations"),
      else: changeset
  end
end
