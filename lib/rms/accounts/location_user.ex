defmodule Rms.Accounts.LocationUser do
  use Ecto.Schema

  import Ecto.Changeset

  schema "location_users" do
    belongs_to :user, Rms.Accounts.User
    belongs_to :location, Rms.Accounts.Location
    belongs_to :organization, Rms.Accounts.Organization
  end

  @doc false
  def changeset(location_user, attrs) do
    location_user
    |> cast(attrs, [])
    |> assoc_constraint(:user)
    |> assoc_constraint(:location)
    |> assoc_constraint(:organization)
  end
end
