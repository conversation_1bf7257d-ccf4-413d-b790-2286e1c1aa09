defmodule Rms.Accounts.Organization do
  use Ecto.Schema
  import Ecto.Changeset

  @type t :: %__MODULE__{
          id: pos_integer(),
          name: String.t(),
          cnpj: String.t(),
          org_id: String.t(),
          safety_net_quantity: non_neg_integer(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  schema "organizations" do
    field :name, :string
    field :cnpj, :string
    field :org_id, :string

    field :safety_net_quantity, :integer, default: 10

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(organization, attrs) do
    organization
    |> cast(attrs, [:name, :cnpj, :org_id])
    |> validate_required([:org_id])
  end

  @doc false
  def update_changeset(organization, attrs) do
    organization
    |> cast(attrs, [:name, :safety_net_quantity])
    |> validate_required([:safety_net_quantity])
    |> validate_number(:safety_net_quantity, greater_than_or_equal_to: 0)
  end
end
