defmodule Rms.Accounts.StaffRole do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization

  schema "staff_roles" do
    field :seller, :boolean
    field :stocker, :boolean
    field :cashier, :boolean

    belongs_to :organization, Organization
    belongs_to :staff, Rms.Accounts.Staff

    timestamps(type: :utc_datetime)
  end

  def changeset(staff_role, attrs) do
    staff_role
    |> cast(attrs, [
      :seller,
      :stocker,
      :cashier,
      :staff_id
    ])
    |> validate_required([:seller, :stocker, :cashier])
    |> foreign_key_constraint(:staff_id)
  end

  def update_changeset(staff_, attrs) do
    staff_
    |> cast(attrs, [
      :seller,
      :cashier,
      :stocker
    ])
  end
end
