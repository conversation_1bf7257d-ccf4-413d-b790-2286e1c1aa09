defmodule Rms.Accounts.Staff do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization

  schema "staffs" do
    field :name, :string
    field :external_id, :string
    field :archived_at, :utc_datetime
    field :erp_id, :string

    belongs_to :organization, Organization

    has_many :staff_locations, Rms.Accounts.StaffLocation,
      on_replace: :delete,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_one :staff_role, Rms.Accounts.StaffRole,
      on_replace: :update,
      defaults: {Rms.Repo, :add_organization_id, []}

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(staff, attrs) do
    staff
    |> cast(attrs, [:name, :external_id, :erp_id, :archived_at])
    |> cast_assoc(:staff_locations, required: true)
    |> cast_assoc(:staff_role, required: true)
    |> validate_required([:name, :external_id])
    |> unique_constraint([:external_id, :orgnization_id],
      name: "staffs_external_id_organization_id_index"
    )
  end

  @doc false
  def update_changeset(staff, attrs) do
    staff
    |> cast(attrs, [:name, :external_id, :erp_id])
    |> cast_assoc(:staff_role)
    |> update_locations()
    |> unique_constraint([:external_id, :orgnization_id],
      name: "staffs_external_id_organization_id_index"
    )
  end

  @doc false
  def archive_changeset(staff) do
    now = DateTime.truncate(DateTime.utc_now(), :second)
    change(staff, archived_at: now)
  end

  @doc false
  def unarchive_changeset(staff) do
    change(staff, archived_at: nil)
  end

  @doc false
  def delete_changeset(staff) do
    changeset = change(staff)

    if field_missing?(changeset, :archived_at),
      do: add_error(changeset, :id, "can only delete archived locations"),
      else: changeset
  end

  defp update_locations(changeset) do
    case fetch_field!(changeset, :staff_locations) do
      :error ->
        changeset

      _ ->
        cast_assoc(changeset, :staff_locations, required: true)
    end
  end

  def event_payload(staff) do
    %{
      id: staff.id,
      name: staff.name,
      external_id: staff.external_id,
      erp_id: staff.erp_id,
      organization_id: staff.organization_id
    }
  end
end
