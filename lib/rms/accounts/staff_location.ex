defmodule Rms.Accounts.StaffLocation do
  use Ecto.Schema

  import Ecto.Changeset

  schema "staffs_locations" do
    belongs_to :staff, Rms.Accounts.Staff
    belongs_to :location, Rms.Accounts.Location
    belongs_to :organization, Rms.Accounts.Organization
  end

  @doc false
  def changeset(staff_location, attrs) do
    staff_location
    |> cast(attrs, [:location_id])
    |> validate_required([:location_id])
    |> foreign_key_constraint(:location_id)
    |> foreign_key_constraint(:organization_id)
  end
end
