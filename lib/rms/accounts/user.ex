defmodule Rms.Accounts.User do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [:id, :name, :email, :external_id, :organization_id, :inserted_at, :updated_at]}
  schema "users" do
    field :name, :string
    field :email, :string
    field :external_id, :string
    field :provider, :string

    belongs_to :organization, Rms.Accounts.Organization

    has_many :assigned_locations, Rms.Accounts.LocationUser,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :locations,
      through: [:assigned_locations, :location]

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:name, :email, :provider, :external_id])
    |> validate_required([:provider, :external_id])
  end

  @doc false
  def assign_location_changeset(user, attrs) do
    user
    |> cast(attrs, [])
    |> cast_assoc(:assigned_locations)
  end
end
