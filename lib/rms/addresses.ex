defmodule Rms.Addresses do
  @moduledoc """
  The Addresses context.
  """

  import Ecto.Query, warn: false
  alias Rms.Repo
  alias Rms.Addresses.Address

  @doc """
  Gets a single address.

  Raises `Ecto.NoResultsError` if the Address does not exist.

  ## Examples

      iex> get_address!(1, 123)
      %Address{}

      iex> get_address!(1, 456)
      ** (Ecto.NoResultsError)

  """
  def get_address!(organization_id, id) do
    Address
    |> where(id: ^id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  @doc """
  Deletes an address.

  ## Examples

      iex> delete_address(customer)
      {:ok, %Customer{}}

      iex> delete_address(customer)
      {:error, %Ecto.Changeset{}}

  """

  def delete_address!(%Address{} = address) do
    Repo.delete(address)
  end

  @doc """
  Updates an address.

  ## Examples


      iex> update_address(address, %{field: new_value})
      {:ok, %Address{}}

      iex> update_address(address, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_address!(%Address{} = address, attrs) do
    address
    |> Address.changeset(attrs)
    |> Repo.update()
  end
end
