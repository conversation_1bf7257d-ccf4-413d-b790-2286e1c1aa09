defmodule Rms.Addresses.Address do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :id,
             :receiver_name,
             :city_name,
             :city_code,
             :state,
             :country_name,
             :neighborhood,
             :street,
             :street_type,
             :number,
             :zip,
             :complement
           ]}

  schema "addresses" do
    field :receiver_name, :string
    field :city_name, :string
    field :city_code, :string
    field :state, :string
    field :country_name, :string
    field :neighborhood, :string
    field :street, :string
    field :street_type, :string
    field :number, :string
    field :zip, :string
    field :complement, :string

    belongs_to :customer, Rms.Customers.Customer
    belongs_to :location, Rms.Accounts.Location
    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(address, attrs) do
    address
    |> cast(attrs, [
      :city_name,
      :city_code,
      :receiver_name,
      :state,
      :country_name,
      :neighborhood,
      :street,
      :street_type,
      :number,
      :zip,
      :complement
    ])
    |> validate_required([
      :receiver_name,
      :city_name,
      :state,
      :country_name,
      :street,
      :zip
    ])
    |> validate_zip()
    |> assoc_constraint(:customer)
    |> assoc_constraint(:location)
    |> assoc_constraint(:organization)
  end

  defp validate_zip(changeset) do
    changeset
    |> update_change(:zip, &String.replace(&1, ~r/\D/, ""))
    |> validate_change(:zip, fn _, zip ->
      if zip && Regex.match?(~r/^\d{8}$/, zip) do
        []
      else
        [zip: "must have 8 digits"]
      end
    end)
  end

  def event_payload(%__MODULE__{} = address) do
    %{
      id: address.id,
      organization_id: address.organization_id,
      customer_id: address.customer_id,
      receiver_name: address.receiver_name,
      city_name: address.city_name,
      city_code: address.city_code,
      state: address.state,
      country_name: address.country_name,
      neighborhood: address.neighborhood,
      street: address.street,
      street_type: address.street_type,
      number: address.number,
      zip: address.zip,
      complement: address.complement
    }
  end
end
