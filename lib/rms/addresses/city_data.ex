defmodule Rms.Addresses.CityData do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :ibge_id,
             :name,
             :uf_name,
             :uf_initials
           ]}

  @primary_key {:ibge_id, :integer, autogenerate: false}
  schema "cities_datas" do
    field :name, :string
    field :uf_name, :string
    field :uf_initials, :string

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(city_data, attrs) do
    city_data
    |> cast(attrs, [
      :ibge_id,
      :name,
      :uf_name,
      :uf_initials
    ])
    |> validate_required([
      :ibge_id,
      :name,
      :uf_name,
      :uf_initials
    ])
  end
end
