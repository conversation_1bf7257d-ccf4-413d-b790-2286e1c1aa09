defmodule Rms.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  import Telemetry.Metrics, only: [counter: 2, distribution: 2, sum: 2]

  @impl true
  def start(_type, _args) do
    setup_opentelemetry()
    setup_launchdarkly()
    # Setup logging
    :logger.update_primary_config(%{metadata: %{service: "rms-backend"}})

    # Attach Oban worker hook for error logging
    :ok = Oban.Pro.Worker.attach_hook(Rms.ObanWorkerHook)

    children = [
      RmsWeb.Telemetry,
      Rms.Vault,
      Rms.Repo,
      {DNSCluster, query: Application.get_env(:rms, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Rms.PubSub},
      {PrometheusTelemetry, metrics: [metrics_definitions()], exporter: [enabled?: true]},
      {Oban, Application.fetch_env!(:rms, Oban)},
      {<PERSON>ban, Application.fetch_env!(:rms, Rms.Events)},
      # Start the Finch HTTP client for sending emails
      {<PERSON>,
       name: Rms.<PERSON>,
       pools: %{
         :default => [pool_max_idle_time: 60_000],
         "https://dfe.vinco.com.br" => [pool_max_idle_time: 60_000]
       }},
      # Start the ProductSyncBatcher for batching VTEX product notifications
      Rms.Integrations.VTEX.ProductSyncBatcher,
      # Start to serve requests, typically the last entry
      RmsWeb.Endpoint,
      {Cachex, name: :rms_global_cache, router: Cachex.Router.Mod}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Rms.Supervisor]
    Supervisor.start_link(children, opts)
  end

  defp setup_opentelemetry do
    :opentelemetry_cowboy.setup()
    OpentelemetryPhoenix.setup()

    OpentelemetryOban.setup()

    OpentelemetryEcto.setup([:rms, :repo], time_unit: :millisecond, db_statement: :enabled)
  end

  defp setup_launchdarkly do
    launchdarkly_sdk_key = Application.get_env(:rms, :launchdarkly_sdk_key)

    opts =
      if launchdarkly_sdk_key == "test" do
        %{
          datasource: :testdata,
          send_events: false,
          feature_store: :ldclient_storage_map
        }
      else
        %{
          http_options: %{
            tls_options: :ldclient_config.tls_basic_options()
          }
        }
      end

    :ldclient.start_instance(to_charlist(launchdarkly_sdk_key), opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    RmsWeb.Endpoint.config_change(changed, removed)
    :ok
  end

  defp metrics_definitions do
    buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, 30, 60]

    [
      counter("oban.job.started.count",
        event_name: [:oban, :job, :start],
        measurement: :count,
        description: "Oban jobs fetched count",
        tags: [:prefix, :queue, :attempts_bucket, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            worker: metadata.job.worker,
            attempts_bucket: bucketize_attempts(metadata.job.attempt),
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end
      ),
      counter("oban.job.success.total",
        event_name: [:oban, :job, :stop],
        measurement: :count,
        description: "Total number of successfully completed Oban jobs.",
        tags: [:queue, :worker, :attempts_bucket, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            worker: metadata.job.worker,
            attempts_bucket: bucketize_attempts(metadata.job.attempt),
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        keep: fn metadata -> metadata.state == :success end
      ),
      counter("oban.job.failure.total",
        event_name: [:oban, :job, :exception],
        description: "Total number of Oban jobs that failed (and may be retried).",
        tags: [:queue, :worker, :attempts_bucket, :error_kind, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            worker: metadata.job.worker,
            attempts_bucket: bucketize_attempts(metadata.job.attempt),
            error_kind: Atom.to_string(metadata.kind),
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        keep: fn metadata -> metadata.state == :failure end,
        measurement: :count
      ),
      counter("oban.job.discarded.total",
        event_name: [:oban, :job, :stop],
        description: "Total number of Oban jobs that were discarded after exhausting retries.",
        tags: [:queue, :worker, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            worker: metadata.job.worker,
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        keep: fn metadata -> metadata.state == :discard end,
        measurement: :count
      ),
      distribution("oban.job.execution.duration.seconds",
        event_name: [:oban, :job, :stop],
        measurement: :duration,
        unit: {:native, :second},
        tags: [:queue, :worker, :state, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            worker: metadata.job.worker,
            # typically :failure
            state: Atom.to_string(metadata.state),
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        reporter_options: [buckets: buckets],
        description: "Histogram of Oban job execution durations for jobs"
      ),
      distribution("oban.job.queue.time.seconds",
        event_name: [:oban, :job, :stop],
        measurement: :queue_time,
        unit: {:native, :second},
        tags: [:queue, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        reporter_options: [buckets: buckets],
        description: "Histogram of Oban job queue times for jobs"
      ),
      sum("oban.snoozed.jobs",
        event_name: [:oban, :job, :stop],
        measurement: :count,
        tags: [:queue, :workflow_name],
        tag_values: fn metadata ->
          %{
            queue: metadata.job.queue,
            workflow_name: metadata.job.meta["workflow_name"] || "unnamed_workflow"
          }
        end,
        keep: fn metadata -> metadata.state == :snoozed end
      )
    ]
  end

  defp bucketize_attempts(attempt) when is_integer(attempt) do
    cond do
      attempt == 1 -> "1"
      attempt <= 3 -> "2-3"
      attempt <= 7 -> "4-7"
      attempt <= 15 -> "8-15"
      true -> "16+"
    end
  end
end
