defmodule Rms.CitiesDatas do
  @moduledoc """
  The CitiesDatas context.
  """

  import Ecto.Query, warn: false
  alias Rms.Repo
  alias Rms.Addresses.CityData

  @doc """
  Gets a single city data.

  Raises `Ecto.NoResultsError` if the CityData does not exist.

  ## Examples

      iex> get_city_data!(123)
      %CityData{}

      iex> get_city_data!(456)
      ** (Ecto.NoResultsError)

  """
  def get_city_data!(id) do
    CityData
    |> where(ibge_id: ^id)
    |> Repo.one!()
  end

  @doc """
  Gets a single city data by name and uf initials.

  Returns nil if one does not exist.

  ## Examples

      iex> get_city_data("City", "ST")
      %CityData{}

      iex> get_city_data!("City", "ST")
      %CityData{}

      iex> get_city_data!("City", "ST")
      ** (Ecto.NoResultsError)

  """
  def get_city_data!(city_name, uf_initials) do
    normalized_city = normalize_string(city_name)
    normalized_uf = normalize_string(uf_initials)

    CityData
    |> where(
      [c],
      fragment("lower(unaccent(?)) = lower(unaccent(?))", c.name, ^normalized_city) and
        fragment("lower(unaccent(?)) = lower(unaccent(?))", c.uf_initials, ^normalized_uf)
    )
    |> Repo.one!()
  end

  def get_all() do
    CityData
    |> Repo.all()
  end

  @doc """
  Upserts a city data.

  ## Examples

      iex> upsert_city_data(%{ibge_id: 123, name: "City", uf_name: "State", uf_initials: "ST"})
      {:ok, %CityData{}}

  """
  def upsert_city_data(attrs) do
    %CityData{}
    |> CityData.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:name, :uf_name, :uf_initials, :updated_at]},
      conflict_target: [:ibge_id]
    )
  end

  @doc """
  Upserts a list of city data.

  ## Examples

      iex> upsert_cities_data([%{ibge_id: 123, name: "City", uf_name: "State", uf_initials: "ST"}])
      {:ok, [%CityData{}]}

  """

  def upsert_cities_data(cities_data) when is_list(cities_data) do
    timestamp = DateTime.utc_now() |> DateTime.truncate(:second)

    Repo.insert_all(
      CityData,
      Enum.map(cities_data, &Map.merge(&1, %{updated_at: timestamp, inserted_at: timestamp})),
      on_conflict: {:replace, [:name, :uf_name, :uf_initials, :updated_at]},
      conflict_target: [:ibge_id]
    )
  end

  def upsert_cities_data(_), do: {:error, :invalid_cities_data}

  defp normalize_string(str) when is_binary(str) do
    str
    |> String.downcase()
    |> String.normalize(:nfd)
    |> String.replace(~r/[^A-z\s]/u, "")
    |> String.replace(~r/\s+/, " ")
    |> String.trim()
  end

  defp normalize_string(nil), do: nil
end
