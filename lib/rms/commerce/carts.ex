defmodule Rms.Commerce.Carts do
  alias Rms.Repo
  import Ecto.Query

  alias Rms.Commerce.Carts
  alias Rms.Commerce.Carts.Cart

  def create_cart(organization_id, attrs) do
    %Cart{organization_id: organization_id}
    |> Carts.Cart.changeset(attrs)
    |> Repo.insert()
  end

  def get_cart!(organization_id, cart_id, preloads \\ []) do
    Cart
    |> where(id: ^cart_id, organization_id: ^organization_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  def update_cart(%Cart{} = cart, attrs) do
    cart
    |> Cart.update_changeset(attrs)
    |> Repo.update()
  end

  def paginated_carts(organization_id, opts \\ []) do
    query =
      build_carts_query(organization_id, opts)
      |> order_by(desc: :id)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    Repo.paginate(
      query,
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:id, :desc}]
    )
  end

  defp build_carts_query(organization_id, opts) do
    preloads = Keyword.get(opts, :preloads, [])

    base_query =
      Cart
      |> preload(^preloads)
      |> where([c], c.organization_id == ^organization_id)

    Enum.reduce(opts, base_query, fn
      {key, value}, query when key in ~w(saved state_of_sale location_id staff_id customer_id)a ->
        where(query, [c], field(c, ^key) == ^value)

      _, query ->
        query
    end)
  end

  def simulate_and_save_cart(organization_id, cart_info, ecomm) do
    with {:ok, simulated_cart} <-
           Carts.SimulateCart.simulate_cart(organization_id, cart_info, ecomm),
         new_cart <-
           Rms.Commerce.Discounts.Engine.execute(organization_id, simulated_cart, cart_info) do
      create_cart(organization_id, new_cart)
    end
  end

  def group_cart(organization_id, cart_id, grouped_items) do
    new_cart = Rms.Commerce.Carts.GroupCart.execute(organization_id, cart_id, grouped_items)
    create_cart(organization_id, new_cart)
  end
end
