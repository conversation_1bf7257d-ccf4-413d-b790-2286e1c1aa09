defmodule Rms.Commerce.Carts.Cart do
  use Ecto.Schema
  import Ecto.Changeset

  @statuses ~w(active canceled moved_to_order)

  @primary_key {:id, Uniq.UUID, version: 7, autogenerate: true}

  defmodule Message do
    use Ecto.<PERSON>hema

    @derive {Jason.Encoder, only: [:type, :code, :message, :info]}
    embedded_schema do
      field :type, :string
      field :code, :string
      field :message, :string
      field :info, :map
    end
  end

  defmodule Metadata do
    use Ecto.Schema

    @derive {Jason.Encoder, only: [:notes]}

    embedded_schema do
      field :notes, :string
    end
  end

  schema "carts" do
    field :status, :string, default: "active"
    field :saved, :boolean, default: false
    field :state_of_sale, :string, default: "seller"
    field :note, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location
    belongs_to :customer, Rms.Customers.Customer
    belongs_to :staff, Rms.Accounts.Staff
    belongs_to :shipping_address, Rms.Addresses.Address

    has_many :discounts, Rms.Commerce.Discounts.Discount,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :delivery_groups, Rms.Commerce.Carts.DeliveryGroup,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :cart_addons, Rms.Commerce.Carts.CartAddon,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :total_price, :decimal
    field :total_items_list_price, :decimal
    field :total_items_selling_price, :decimal
    field :total_manual_discount, :decimal
    field :total_ecommerce_discounts, :decimal, default: 0
    field :total_items_manual_discount, :decimal, default: 0

    field :total_delivery_price, :decimal, default: 0
    field :max_delivery_time, :integer, default: 0

    field :ecommerce, :string

    embeds_one :metadata, Metadata
    embeds_many :messages, Message

    timestamps(type: :utc_datetime)
  end

  def metadata_changeset(changeset, attrs) do
    changeset
    |> cast(attrs, [:notes])
  end

  def changeset(cart, attrs) do
    cart
    |> cast(attrs, [
      :id,
      :saved,
      :state_of_sale,
      :staff_id,
      :customer_id,
      :location_id,
      :shipping_address_id,
      :status,
      :note,
      :total_price,
      :total_items_list_price,
      :total_items_selling_price,
      :total_manual_discount,
      :total_delivery_price,
      :total_ecommerce_discounts,
      :total_items_manual_discount,
      :max_delivery_time,
      :ecommerce
    ])
    |> validate_required([
      :status,
      :total_price,
      :total_items_list_price,
      :total_items_selling_price,
      :total_manual_discount,
      :total_delivery_price
    ])
    |> cast_embed(:messages, with: &messages_changeset/2)
    |> cast_embed(:metadata, with: &metadata_changeset/2)
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:state_of_sale, ["seller", "cashier"])
    |> cast_assoc(:discounts)
    |> cast_assoc(:delivery_groups)
    |> cast_assoc(:cart_addons)
    |> foreign_key_constraint(:customer_id)
    |> foreign_key_constraint(:staff_id)
    |> foreign_key_constraint(:location_id)
    |> foreign_key_constraint(:shipping_address_id)
  end

  def update_changeset(cart, attrs) do
    cart
    |> cast(attrs, [
      :staff_id,
      :location_id,
      :saved,
      :state_of_sale,
      :note
    ])
    |> foreign_key_constraint(:staff_id)
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:state_of_sale, ["seller", "cashier"])
    |> foreign_key_constraint(:location_id)
  end

  defp messages_changeset(messages, attrs) do
    messages
    |> cast(attrs, [
      :message,
      :type,
      :code,
      :info
    ])
    |> validate_required([
      :type,
      :code
    ])
  end
end
