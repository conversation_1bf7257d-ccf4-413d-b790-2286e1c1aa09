defmodule Rms.Commerce.Carts.CartAddon do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}

  schema "cart_addons" do
    field :quantity, :integer
    field :name, :string
    field :type, :string
    field :price, :decimal
    field :list_price, :decimal
    field :image_url, :string
    field :description, :string
    field :metadata, :map

    belongs_to :cart, Rms.Commerce.Carts.Cart, type: :binary_id
    belongs_to :addon, Rms.Commerce.Products.Addon
    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  @doc false
  def changeset(cart_addon, attrs) do
    cart_addon
    |> cast(attrs, [
      :quantity,
      :addon_id,
      :name,
      :type,
      :price,
      :description,
      :list_price,
      :image_url
    ])
    |> validate_required([
      :quantity,
      :addon_id,
      :organization_id,
      :name,
      :type,
      :price,
      :list_price,
      :image_url
    ])
    |> validate_number(:quantity, greater_than: 0)
    |> validate_number(:price, greater_than_or_equal_to: 0)
    |> validate_number(:list_price, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:cart_id)
    |> foreign_key_constraint(:addon_id)
    |> foreign_key_constraint(:organization_id)
  end
end
