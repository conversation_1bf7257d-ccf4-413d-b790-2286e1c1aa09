defmodule Rms.Commerce.Carts.CartItem do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Uniq.UUID, version: 7, autogenerate: true}

  schema "cart_items" do
    field :quantity, :integer
    field :list_price, :decimal
    field :selling_price, :decimal
    field :total_price, :decimal
    field :item_index, :integer
    field :is_gift, :boolean, default: false
    field :group_index, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant

    belongs_to :delivery_group, Rms.Commerce.Carts.DeliveryGroup, type: :binary_id

    has_many :discounts, Rms.Commerce.Discounts.CartItemDiscount,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :metadata, :map
  end

  def changeset(cart_item, attrs) do
    cart_item
    |> cast(attrs, [
      :quantity,
      :list_price,
      :selling_price,
      :total_price,
      :product_variant_id,
      :delivery_group_id,
      :item_index,
      :metadata,
      :is_gift,
      :group_index
    ])
    |> validate_required([
      :list_price,
      :selling_price,
      :total_price,
      :quantity,
      :product_variant_id
    ])
    |> cast_assoc(:discounts)
    |> foreign_key_constraint(:cart_id)
    |> foreign_key_constraint(:delivery_group_id)
  end
end
