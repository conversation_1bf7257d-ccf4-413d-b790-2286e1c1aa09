defmodule Rms.Commerce.Carts.DeliveryGroup do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Uniq.UUID, version: 7, autogenerate: true}

  schema "delivery_groups" do
    field :delivery_time, :integer, default: 0
    field :delivery_price, :decimal, default: 0
    field :fulfillment_type, :string
    field :pickup_point, :map
    field :metadata, :map

    belongs_to :organization, Rms.Accounts.Organization

    has_many :cart_items, Rms.Commerce.Carts.CartItem,
      defaults: {Rms.Repo, :add_organization_id, []}

    belongs_to :cart, Rms.Commerce.Carts.Cart, type: :binary_id
  end

  def changeset(delivery_group, attrs) do
    delivery_group
    |> cast(attrs, [
      :id,
      :delivery_time,
      :delivery_price,
      :fulfillment_type,
      :pickup_point,
      :metadata
    ])
    |> validate_required([
      :fulfillment_type
    ])
    |> cast_assoc(:cart_items,
      with: &Rms.Commerce.Carts.CartItem.changeset/2,
      required: true
    )
  end
end
