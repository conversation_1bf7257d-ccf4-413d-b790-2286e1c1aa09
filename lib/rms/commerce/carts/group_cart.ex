defmodule Rms.Commerce.Carts.GroupCart do
  @preloads [
    :shipping_address,
    :staff,
    :customer,
    :discounts,
    cart_addons: [:addon],
    delivery_groups: [cart_items: [:discounts, product_variant: [product: :product_variants]]]
  ]

  def execute(organization_id, cart_id, grouped_items) do
    cart = Rms.Commerce.Carts.get_cart!(organization_id, cart_id, @preloads)

    new_delivery_groups =
      Enum.map(cart.delivery_groups, &build_delivery_group(&1, grouped_items))

    %{
      location_id: cart.location_id,
      customer_id: cart.customer_id,
      organization_id: cart.organization_id,
      staff_id: cart.staff_id,
      shipping_address_id: cart.shipping_address_id,
      discounts: build_discounts(cart.discounts),
      delivery_groups: new_delivery_groups,
      metadata: cart.metadata && Map.from_struct(cart.metadata),
      total_delivery_price: cart.total_delivery_price,
      total_manual_discount: cart.total_manual_discount,
      total_ecommerce_discounts: cart.total_ecommerce_discounts,
      total_items_manual_discount: cart.total_items_manual_discount,
      total_items_selling_price: cart.total_items_selling_price,
      total_items_list_price: cart.total_items_list_price,
      total_price: cart.total_price
    }
  end

  defp build_delivery_group(delivery_group, grouped_items) do
    cart_items =
      delivery_group.cart_items
      |> Enum.flat_map(&build_cart_items(&1, grouped_items))

    %{
      delivery_time: delivery_group.delivery_time,
      delivery_price: delivery_group.delivery_price,
      fulfillment_type: delivery_group.fulfillment_type,
      pickup_point: delivery_group.pickup_point,
      metadata: delivery_group.metadata,
      cart_items: cart_items
    }
  end

  defp build_cart_items(cart_item, grouped_items) do
    grouped_items
    |> Enum.filter(&(&1["id"] == cart_item.id))
    |> Enum.map(fn grouped_item ->
      %{
        quantity: grouped_item["quantity"],
        group_index: "#{grouped_item["group_index"]}",
        discounts: build_discounts(cart_item.discounts),
        item_index: cart_item.item_index,
        is_gift: cart_item.is_gift,
        list_price: cart_item.list_price,
        selling_price: cart_item.selling_price,
        total_price: Decimal.mult(cart_item.selling_price, Decimal.new(grouped_item["quantity"])),
        product_variant_id: cart_item.product_variant_id,
        metadata: cart_item.metadata
      }
    end)
  end

  defp build_discounts(discounts) do
    Enum.map(discounts, fn discount ->
      %{
        type: discount.type,
        value: discount.value,
        description: discount.description,
        authorization_request_id: discount.authorization_request_id
      }
    end)
  end
end
