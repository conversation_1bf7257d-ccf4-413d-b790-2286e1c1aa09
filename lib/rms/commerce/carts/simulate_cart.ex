defmodule Rms.Commerce.Carts.SimulateCart do
  alias Rms.Integrations

  require OpenTelemetry.Tracer

  defp prepare_and_simulate_cart(organization_id, cart_info, "vtex") do
    vtex_credentials = Integrations.get_vtex_credential!(organization_id)

    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credentials)

    formatted_client =
      if customer_id = cart_info["customer_id"] do
        organization_id
        |> Rms.Customers.get_customer!(customer_id)
        |> Rms.Integrations.parse_vtex_customer_info()
      else
        nil
      end

    price_table = get_price_table(vtex_credentials, formatted_client)

    cart_info =
      cart_info
      |> Map.get("items")
      |> Enum.map(fn item ->
        item
        |> Map.put("id", item["external_id"])
        |> Map.take(["id", "quantity"])
        |> Map.put("seller", "1")
      end)
      |> then(&Map.put(cart_info, "items", &1))
      |> Map.put("postalCode", cart_info["postal_code"])
      |> Map.put("clientProfileData", formatted_client)
      |> Map.put("priceTables", price_table)
      |> Map.put(
        "marketingData",
        %{
          "coupon" =>
            cart_info
            |> Map.get("discounts", [])
            |> Enum.filter(fn discount -> discount["type"] == "coupon" end)
            |> case do
              [first_code] -> Map.get(first_code, "value", "")
              _ -> nil
            end
        }
        |> Map.reject(fn {_k, v} -> is_nil(v) end)
      )

    with {:ok, response} <-
           Rms.Integrations.VTEX.simulate_fulfillment(
             vtex_client,
             vtex_credentials.affiliate_id,
             vtex_credentials.sales_channel_id,
             cart_info
           ) do
      cart_simulation_messages =
        Map.get(response, "messages", [])
        |> Enum.reduce([], fn message, errors ->
          case message["code"] do
            "couponNotFound" ->
              [
                %{
                  "type" => "warning",
                  "code" => "coupon_not_found",
                  "coupon_codes" => [cart_info["marketingData"]["coupon"]]
                }
              ] ++ errors

            "couponExpired" ->
              [
                %{
                  "type" => "warning",
                  "code" => "coupon_expired",
                  "coupon_codes" => [cart_info["marketingData"]["coupon"]]
                }
              ] ++ errors

            _ ->
              errors
          end
        end)

      {:ok, response, cart_simulation_messages}
    end
  end

  defp get_price_table(vtex_credentials, %{"email" => email}) when is_binary(email) do
    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credentials, use_sale_channel: false)

    with {:ok, response} <- Integrations.VTEX.search_customer(vtex_client, email),
         price_table when not is_nil(price_table) <-
           get_in(response, [Access.at(0), "priceTables"]) do
      [price_table]
    else
      _anything -> nil
    end
  end

  defp get_price_table(_vtex_client, _customer), do: nil

  defp enrich_cart_info(cart_info, product_variant_external_id_map) do
    cart_info
    |> Map.get("items")
    |> Enum.map(fn item ->
      Map.put(
        item,
        "external_id",
        Map.get(product_variant_external_id_map, item["product_variant_id"])
      )
    end)
    |> then(&Map.put(cart_info, "items", &1))
  end

  defp enrich_cart_info(cart_info, product_variant_external_id_map, addon_external_id_map) do
    items =
      cart_info
      |> Map.get("items")
      |> Enum.map(fn item ->
        Map.put(
          item,
          "external_id",
          Map.get(product_variant_external_id_map, item["product_variant_id"])
        )
      end)

    addons =
      cart_info
      |> Map.get("addons", [])
      |> Enum.map(fn addon ->
        addon
        |> Map.put("external_id", Map.get(addon_external_id_map, addon["addon_id"]))
        |> Map.put("quantity", 1)
        |> Map.put("addon_id", addon["addon_id"])
        |> Map.put("seller", "1")
        |> Map.put("fulfillment_type", "in-store")
        |> Map.put("metadata", nil)
      end)

    cart_info =
      (items ++ addons)
      |> then(&Map.put(cart_info, "items", &1))

    cart_info |> Map.delete("addons")
  end

  def fetch_delivery_option_for_addons(
        organization_id,
        %{"addons" => addons, "location_id" => location_id} = _order
      ) do
    addon_ids = Enum.map(addons || [], & &1["addon_id"])

    addon_external_id_map =
      Rms.Integrations.get_addon_external_ids(
        organization_id,
        addon_ids,
        "vtex"
      )

    zip =
      Rms.Accounts.Location
      |> Rms.Repo.get_by!(id: location_id)
      |> Rms.Repo.preload(:address)
      |> Map.get(:address)
      |> Map.get(:zip)

    addons_to_simulate =
      addons
      |> Enum.map(fn addon ->
        addon
        |> Map.put("id", Map.get(addon_external_id_map, addon["addon_id"]))
        |> Map.put("external_id", Map.get(addon_external_id_map, addon["addon_id"]))
        |> Map.put("addon", addon)
        |> Map.put("quantity", 1)
        |> Map.put("seller", "1")
      end)

    cart_info = %{
      "items" => addons_to_simulate,
      "country" => "BRA",
      "postalCode" => zip
    }

    vtex_credentials = Integrations.get_vtex_credential!(organization_id)

    vtex_client = Rms.Integrations.VTEX.client(vtex_credentials)

    with {:ok, response} <-
           Rms.Integrations.VTEX.simulate_fulfillment(
             vtex_client,
             vtex_credentials.affiliate_id,
             vtex_credentials.sales_channel_id,
             cart_info
           ) do
      %{"delivery_options" => delivery_option} =
        response["logisticsInfo"]
        |> Enum.map(fn logistic_item ->
          %{
            "delivery_options" =>
              logistic_item["slas"]
              |> Enum.map(fn sla ->
                %{
                  "id" => sla["id"],
                  "name" => sla["name"],
                  "delivery_type" => sla["deliveryChannel"],
                  "price" => sla["price"],
                  "quantity" =>
                    sla["deliveryIds"]
                    |> Enum.reduce(0, fn elem, acc -> acc + elem["quantity"] end),
                  "delivery_time" => sla["shippingEstimate"],
                  "metadata" => sla
                }
              end)
              |> Enum.sort(fn option1, option2 ->
                safe_parse = fn str ->
                  case Integer.parse(str) do
                    {int, _} -> int
                    _ -> :infinity
                  end
                end

                estimate1 = safe_parse.(option1["delivery_time"])
                estimate2 = safe_parse.(option2["delivery_time"])

                cond do
                  option1["price"] != option2["price"] ->
                    option1["price"] < option2["price"]

                  estimate1 == :infinity ->
                    true

                  estimate2 == :infinity ->
                    false

                  true ->
                    estimate1 < estimate2
                end
              end)
              |> hd()
          }
        end)
        |> hd()

      addons_to_simulate
      |> Enum.map(fn addon ->
        Map.put(addon, :delivery_option, delivery_option)
      end)
    end
  end

  def fetch_delivery_options(organization_id, cart_info) do
    # Get connected ecommerce
    ecomm = Rms.Settings.get_connected_ecommerce(organization_id)

    # Extract location_id from cart_info if present
    location_id = cart_info["location_id"]

    # Check if enabled_fulfillments setting is present and both delivery and local_pickup are false
    enabled_fulfillments =
      Rms.Settings.get_setting(organization_id, location_id, "enabled_fulfillments")

    case enabled_fulfillments do
      %{"delivery" => false, "local_pickup" => false} ->
        # Return empty response since no fulfillment methods are enabled
        {:ok, %{"items" => [], "pickup_points" => []}}

      _ ->
        # Continue with normal flow based on ecommerce integration
        fetch_delivery_options_from_ecommerce(organization_id, cart_info, ecomm)
    end
  end

  defp fetch_delivery_options_from_ecommerce(organization_id, cart_info, "vtex") do
    product_variant_ids = Enum.map(cart_info["items"], & &1["product_variant_id"])

    product_variant_mappings =
      Rms.Integrations.list_product_variant_mappings(
        organization_id,
        "vtex",
        product_variant_ids
      )

    external_id_map =
      Enum.into(product_variant_mappings, %{}, &{&1.product_variant_id, &1.external_id})

    pvm_map =
      Enum.reduce(product_variant_mappings, %{}, fn pvm, acc ->
        Map.put(acc, pvm.product_variant_id, pvm)
      end)

    cart_info_with_addons = enrich_cart_info(cart_info, external_id_map)

    with {:ok, response, _messages} <-
           prepare_and_simulate_cart(organization_id, cart_info_with_addons, "vtex") do
      inverse_external_id_map = Map.new(external_id_map, fn {k, v} -> {v, k} end)

      items =
        response["items"]
        |> Enum.sort_by(& &1["itemIndex"])
        |> Enum.zip(cart_info["items"])
        |> Enum.map(fn {item, requested_item} ->
          %{
            "product_variant_id" => Map.get(inverse_external_id_map, item["id"]),
            "requested_quantity" => requested_item["quantity"],
            "sold_out" => pvm_map[requested_item["product_variant_id"]].sold_out
          }
        end)

      delivery_options =
        response["logisticsInfo"]
        |> Enum.sort_by(& &1["itemIndex"])
        |> Enum.zip(items)
        |> Enum.map(fn {logistic_item, item} ->
          %{
            "item_index" => logistic_item["itemIndex"],
            "product_variant_id" => item["product_variant_id"],
            "delivery_options" =>
              logistic_item["slas"]
              |> Enum.map(fn sla ->
                %{
                  "id" => sla["id"],
                  "name" => sla["name"],
                  "delivery_type" => sla["deliveryChannel"],
                  "price" => sla["price"],
                  "quantity" =>
                    sla["deliveryIds"]
                    |> Enum.reduce(0, fn elem, acc -> acc + elem["quantity"] end)
                    |> min(item["requested_quantity"]),
                  "delivery_time" => sla["shippingEstimate"],
                  "metadata" => sla
                }
              end)
              |> Enum.reject(fn delivery_option ->
                delivery_option["quantity"] < item["requested_quantity"] || item["sold_out"]
              end)
              |> Enum.sort(fn option1, option2 ->
                safe_parse = fn str ->
                  case Integer.parse(str) do
                    {int, _} -> int
                    _ -> :infinity
                  end
                end

                estimate1 = safe_parse.(option1["delivery_time"])
                estimate2 = safe_parse.(option2["delivery_time"])

                cond do
                  option1["price"] != option2["price"] ->
                    option1["price"] < option2["price"]

                  estimate1 == :infinity ->
                    true

                  estimate2 == :infinity ->
                    false

                  true ->
                    estimate1 < estimate2
                end
              end)
          }
        end)

      pickup_points =
        response["logisticsInfo"]
        |> Enum.flat_map(fn logistic_info ->
          logistic_info["slas"]
          |> Enum.filter(&(&1["deliveryChannel"] == "pickup-in-point"))
          |> Enum.map(fn sla ->
            pickup_info = sla["pickupStoreInfo"]

            %{
              "name" => pickup_info["friendlyName"],
              "address" => format_vtex_address(pickup_info["address"]),
              "pickup_distance" => sla["pickupDistance"]
            }
          end)
        end)
        |> Enum.uniq_by(& &1["address"]["address_id"])
        |> Enum.sort_by(& &1["pickup_distance"])

      {:ok, %{"items" => delivery_options, "pickup_points" => pickup_points}}
    end
  end

  defp fetch_delivery_options_from_ecommerce(organization_id, cart_info, "shopify") do
    OpenTelemetry.Tracer.with_span "shopify fetch_delivery_options_from_ecommerce", %{
      attributes: [
        {"organization_id", organization_id},
        {"cart_info", inspect(cart_info)}
      ]
    } do
      organization_id
      |> Rms.Integrations.Shopify.Carts.DeliveryOptions.execute(cart_info)
      |> tap(fn response ->
        OpenTelemetry.Tracer.set_attribute("DeliveryOptions.execute.response", inspect(response))
      end)
    end
  end

  defp fetch_delivery_options_from_ecommerce(_organization_id, _attrs, ecommerce),
    do: {:error, "unsupported ecommerce integration #{inspect(ecommerce)}"}

  defp price_fetch_simulate_cart(response, organization_id, cart_info_with_addons) do
    if Enum.any?(response["items"], fn item ->
         item["availability"] == "cannotBeDelivered"
       end) do
      price_fetch_cart_info =
        cart_info_with_addons
        |> Map.drop(["postal_code"])

      prepare_and_simulate_cart(
        organization_id,
        price_fetch_cart_info,
        "vtex"
      )
    else
      {:ok, response, []}
    end
  end

  def simulate_cart(organization_id, cart_info, "vtex") do
    {simulation_cart_items, local_cart_items, local_ecommerce_cart_items} =
      prepare_cart_items(cart_info, organization_id)

    ecommerce_cart_info = Map.put(cart_info, "items", simulation_cart_items)

    cart_info_with_addons =
      enrich_cart_info_with_addons(ecommerce_cart_info, organization_id)

    with {:ok, response, cart_simulation_messages} <-
           prepare_and_simulate_cart(
             organization_id,
             cart_info_with_addons,
             "vtex"
           ),
         {:ok, price_fetch_response, _} <-
           price_fetch_simulate_cart(
             response,
             organization_id,
             cart_info_with_addons
           ) do
      process_simulation_results(
        response,
        price_fetch_response,
        simulation_cart_items,
        local_cart_items,
        local_ecommerce_cart_items,
        cart_info,
        organization_id,
        cart_simulation_messages
      )
    end
  end

  def simulate_cart(organization_id, cart_info, "shopify") do
    case Rms.Integrations.get_shopify_storefront_credential(organization_id) do
      nil ->
        simulate_cart(organization_id, cart_info, "")

      _ ->
        {:ok, Rms.Integrations.Shopify.Carts.Simulate.execute(organization_id, cart_info)}
    end
  end

  def simulate_cart(organization_id, cart_info, _ecomm) do
    local_cart_items =
      cart_info["items"]
      |> Enum.with_index(fn item, index -> Map.put(item, "item_index", index) end)

    local_items = map_local_items(organization_id, local_cart_items)

    delivery_groups = add_instore_delivery_group(local_items, [])

    {prices, total_price_messages} =
      calculate_total_price(
        delivery_groups,
        Map.get(cart_info, "discounts", [])
      )

    {:ok,
     %{
       "delivery_groups" => delivery_groups,
       "max_delivery_time" => 0,
       "messages" => total_price_messages,
       "customer_id" => cart_info["customer_id"],
       "staff_id" => cart_info["staff_id"],
       "location_id" => cart_info["location_id"],
       "shipping_address_id" => cart_info["shipping_address_id"],
       "discounts" => cart_info["discounts"] || []
     }
     |> Map.merge(prices)}
  end

  defp prepare_cart_items(cart_info, organization_id) do
    product_variant_ids = Enum.map(cart_info["items"], & &1["product_variant_id"])

    product_variant_external_id_map =
      Rms.Integrations.get_product_variant_external_ids(
        organization_id,
        product_variant_ids,
        "vtex"
      )

    cart_info["items"]
    |> Enum.with_index(fn item, index -> Map.put(item, "item_index", index) end)
    |> Enum.reduce({[], [], []}, fn item, {ecommerce, local, local_ecommerce} ->
      cond do
        item["fulfillment_type"] == "in-store" and
            not Map.has_key?(product_variant_external_id_map, item["product_variant_id"]) ->
          {ecommerce, [item | local], local_ecommerce}

        item["fulfillment_type"] == "in-store" ->
          {ecommerce, local, [item | local_ecommerce]}

        true ->
          {[item | ecommerce], local, local_ecommerce}
      end
    end)
    |> then(fn {ecommerce, local, local_ecommerce} ->
      {Enum.reverse(ecommerce) ++ Enum.reverse(local_ecommerce), local, local_ecommerce}
    end)
  end

  defp enrich_cart_info_with_addons(ecommerce_cart_info, organization_id) do
    addon_ids = Enum.map(ecommerce_cart_info["addons"] || [], & &1["addon_id"])

    addon_external_id_map =
      Rms.Integrations.get_addon_external_ids(
        organization_id,
        addon_ids,
        "vtex"
      )

    product_variant_ids = Enum.map(ecommerce_cart_info["items"], & &1["product_variant_id"])

    product_variant_external_id_map =
      Rms.Integrations.get_product_variant_external_ids(
        organization_id,
        product_variant_ids,
        "vtex"
      )

    enrich_cart_info(
      ecommerce_cart_info,
      product_variant_external_id_map,
      addon_external_id_map
    )
  end

  defp process_simulation_results(
         response,
         price_fetch_response,
         simulation_cart_items,
         local_cart_items,
         local_ecommerce_cart_items,
         cart_info,
         organization_id,
         cart_simulation_messages
       ) do
    {simulation_items, ecommerce_mapping_messages} =
      map_ecommerce_items(response, price_fetch_response, simulation_cart_items, "vtex")

    {ecommerce_items, instore_ecommerce_items} =
      Enum.split_with(simulation_items, &(&1["fulfillment_type"] != "in-store"))

    removed_instore_ecommerce_items =
      local_ecommerce_cart_items
      |> Enum.reject(fn item ->
        Enum.any?(instore_ecommerce_items, &(&1["item_index"] == item["item_index"]))
      end)

    local_items =
      map_local_items(organization_id, local_cart_items ++ removed_instore_ecommerce_items)

    {delivery_groups, delivery_group_messages} =
      case cart_info["postal_code"] do
        postal_code when is_nil(postal_code) or postal_code == "" ->
          {map_deliveryless_items(ecommerce_items), []}

        _ ->
          calculate_ecommerce_delivery_groups(response, ecommerce_items)
      end

    instore_items = local_items ++ instore_ecommerce_items
    delivery_groups = add_instore_delivery_group(instore_items, delivery_groups)

    {prices, total_price_messages} =
      calculate_total_price(
        delivery_groups,
        Map.get(cart_info, "discounts", [])
      )

    max_delivery_time =
      delivery_groups
      |> get_in([Access.all(), "delivery_time"])
      |> Enum.reject(&is_nil/1)
      |> Enum.max(fn -> 0 end)

    cart_addons = format_cart_addons(cart_info)

    {:ok,
     %{
       "delivery_groups" => delivery_groups,
       "cart_addons" => cart_addons,
       "max_delivery_time" => max_delivery_time,
       "messages" =>
         cart_simulation_messages ++
           delivery_group_messages ++
           total_price_messages ++
           ecommerce_mapping_messages,
       "ecommerce" => "vtex",
       "customer_id" => cart_info["customer_id"],
       "staff_id" => cart_info["staff_id"],
       "location_id" => cart_info["location_id"],
       "shipping_address_id" => cart_info["shipping_address_id"],
       "discounts" => cart_info["discounts"] || []
     }
     |> Map.merge(prices)}
  end

  defp format_cart_addons(cart_info) do
    cart_info
    |> Map.get("addons", [])
    |> Enum.map(fn addon ->
      %{
        "quantity" => addon["quantity"],
        "addon_id" => addon["addon_id"],
        "name" => addon["name"],
        "type" => addon["type"],
        "price" => addon["price"],
        "list_price" => addon["list_price"],
        "image_url" => addon["image_url"],
        "description" => addon["description"]
      }
    end)
  end

  defp map_deliveryless_items(items) do
    items
    |> Enum.group_by(& &1["fulfillment_type"])
    |> Enum.map(fn {fulfillment_type, cart_items} ->
      %{
        "max_delivery_time" => 0,
        "fulfillment_type" => fulfillment_type,
        "cart_items" => cart_items
      }
    end)
  end

  defp map_ecommerce_items(response, price_fetch_reponse, ecommerce_cart_items, "vtex") do
    response_items =
      response["items"]
      |> Enum.zip(price_fetch_reponse["items"])
      |> Enum.reject(fn {item, _item_price} ->
        cond do
          is_nil(item["requestIndex"]) -> true
          is_nil(Enum.at(ecommerce_cart_items, item["requestIndex"])) -> true
          true -> false
        end
      end)
      |> Enum.reduce(%{}, fn {item, item_price}, acc ->
        request_index = item["requestIndex"]
        request_item = Enum.at(ecommerce_cart_items, request_index)

        price_map =
          case request_item["fulfillment_type"] do
            "in-store" ->
              %{
                "quantity" => item_price["quantity"],
                "price" => Decimal.new(item_price["price"]) |> Decimal.div(100),
                "list_price" => Decimal.new(item_price["listPrice"]) |> Decimal.div(100),
                "total_price" =>
                  Decimal.new(item_price["priceDefinition"]["total"]) |> Decimal.div(100),
                "selling_price" =>
                  Decimal.new(item_price["sellingPrice"] || item_price["price"])
                  |> Decimal.div(100)
              }

            _ ->
              %{
                "quantity" => item["quantity"],
                "price" => Decimal.new(item["price"]) |> Decimal.div(100),
                "list_price" => Decimal.new(item["listPrice"]) |> Decimal.div(100),
                "total_price" =>
                  Decimal.new(item["priceDefinition"]["total"]) |> Decimal.div(100),
                "selling_price" =>
                  Decimal.new(item["sellingPrice"] || item["price"])
                  |> Decimal.div(100)
              }
          end

        new_item =
          %{
            "item_index" => request_item["item_index"],
            "request_index" => request_index,
            "product_variant_id" => request_item["product_variant_id"],
            "metadata" => request_item["metadata"],
            "fulfillment_type" => request_item["fulfillment_type"]
          }
          |> Map.merge(price_map)

        Map.update(acc, request_index, new_item, fn old_item ->
          new_quantity = old_item["quantity"] + new_item["quantity"]
          new_total_price = Decimal.add(old_item["total_price"], new_item["total_price"])
          new_selling_price = Decimal.div(new_total_price, new_quantity)

          %{
            old_item
            | "quantity" => new_quantity,
              "total_price" => new_total_price,
              "selling_price" => new_selling_price
          }
        end)
      end)
      |> Map.values()

    removed_items_indexes =
      ecommerce_cart_items
      |> Enum.map(& &1["item_index"])
      |> MapSet.new()
      |> MapSet.difference(MapSet.new(Enum.map(response_items, & &1["item_index"])))
      |> MapSet.to_list()

    messages =
      if removed_items_indexes != [] do
        [
          %{
            "type" => "warning",
            "code" => "items_removed",
            "message" => "Some items were removed from the response.",
            "info" => %{
              "items" =>
                Enum.map(removed_items_indexes, fn index ->
                  Enum.at(ecommerce_cart_items, index)
                end)
            }
          }
        ]
      else
        []
      end

    {response_items, messages}
  end

  defp map_local_items(organization_id, local_cart_items) do
    price_table =
      Rms.Commerce.Products.get_price_table(
        organization_id,
        local_cart_items |> Enum.map(& &1["product_variant_id"])
      )

    Enum.map(local_cart_items, fn item ->
      %{
        "item_index" => item["item_index"],
        "product_variant_id" => item["product_variant_id"],
        "price" => price_table |> Map.get(item["product_variant_id"]) |> Map.get(:price),
        "list_price" =>
          price_table |> Map.get(item["product_variant_id"]) |> Map.get(:list_price),
        "quantity" => item["quantity"],
        "total_price" =>
          Decimal.mult(
            Decimal.new(price_table |> Map.get(item["product_variant_id"]) |> Map.get(:price)),
            Decimal.new(item["quantity"])
          ),
        "selling_price" =>
          Decimal.new(price_table |> Map.get(item["product_variant_id"]) |> Map.get(:price)),
        "fulfillment_type" => item["fulfillment_type"]
      }
    end)
  end

  defp calculate_ecommerce_delivery_groups(vtex_response, ecommerce_cart_items) do
    delivery_groups_map =
      ecommerce_cart_items
      |> Enum.map(fn item ->
        logistics_info = Enum.at(vtex_response["logisticsInfo"], item["request_index"])

        delivery_option =
          Enum.find(logistics_info["slas"], fn sla ->
            sla["id"] == item["metadata"]["id"] and
              sla["deliveryChannel"] == item["metadata"]["deliveryChannel"]
          end)

        item
        |> Map.put("logistics_info", logistics_info)
        |> Map.put("original_metadata", item["metadata"])
        |> Map.put("metadata", delivery_option)
      end)
      |> Enum.group_by(fn item ->
        if is_nil(item["metadata"]) do
          "unknown"
        else
          "#{item["metadata"]["deliveryChannel"]}_#{item["metadata"]["id"]}"
        end
      end)

    user_defined_delivery_groups =
      delivery_groups_map
      |> Map.drop(["unknown"])
      |> Enum.map(fn {_metadata, group} ->
        item = hd(group)

        pickup_point =
          if get_in(item["metadata"], ["pickupStoreInfo", "isPickupStore"]) do
            pickup_info = item["metadata"]["pickupStoreInfo"]

            %{
              "name" => pickup_info["friendlyName"],
              "address" => format_vtex_address(pickup_info["address"]),
              "pickup_distance" => item["metadata"]["pickupDistance"]
            }
          else
            nil
          end

        max_shipping_estimate =
          group
          |> Enum.map(&(&1["metadata"]["shippingEstimate"] |> Integer.parse() |> elem(0)))
          |> Enum.max()

        delivery_price =
          vtex_response["logisticsInfo"]
          |> Stream.flat_map(fn logistics_info -> logistics_info["slas"] end)
          |> Stream.filter(fn sla -> sla["id"] == item["metadata"]["id"] end)
          |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1["price"], &2))
          |> Decimal.div(100)

        %{
          "fulfillment_type" => item["fulfillment_type"],
          "cart_items" => group,
          "pickup_point" => pickup_point,
          "delivery_time" => max_shipping_estimate,
          "delivery_price" => delivery_price |> Decimal.round(2),
          "metadata" => %{
            "sla_id" => item["metadata"]["id"]
          }
        }
      end)

    unknown_delivery_groups = delivery_groups_map["unknown"] || []

    auto_defined_delivery_groups =
      unknown_delivery_groups
      |> Enum.map(fn
        %{"fulfillment_type" => "delivery"} = item ->
          delivery_option =
            item["logistics_info"]["slas"]
            |> Enum.filter(fn delivery_option ->
              delivery_option["deliveryChannel"] == "delivery"
            end)
            |> Enum.min_by(
              fn delivery_option ->
                elem(Integer.parse(delivery_option["shippingEstimate"]), 0)
              end,
              fn -> nil end
            )

          Map.put(item, "metadata", delivery_option)

        item ->
          item
      end)
      |> Enum.group_by(fn item -> item["metadata"]["id"] end)
      |> Enum.map(fn
        {nil, group} ->
          %{
            "fulfillment_type" => "unfulfillable",
            "pickup_point" => "",
            "cart_items" => group
          }

        {delivery_group_name, group} ->
          item = hd(group)

          max_shipping_estimate =
            group
            |> Enum.filter(&(not is_nil(&1["metadata"]["shippingEstimate"])))
            |> Enum.map(fn item ->
              item["metadata"]["shippingEstimate"]
              |> Integer.parse()
              |> elem(0)
            end)
            |> Enum.max(fn -> nil end)

          delivery_price =
            vtex_response["logisticsInfo"]
            |> Stream.flat_map(fn logistics_info -> logistics_info["slas"] end)
            |> Stream.filter(fn sla -> sla["id"] == item["metadata"]["id"] end)
            |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1["price"], &2))
            |> Decimal.div(100)

          %{
            "fulfillment_type" => "delivery",
            "pickup_point" => "",
            "delivery_time" => max_shipping_estimate,
            "cart_items" => group,
            "delivery_price" => delivery_price |> Decimal.round(2),
            "metadata" => %{
              "sla_id" => delivery_group_name
            }
          }
      end)

    delivery_groups =
      auto_defined_delivery_groups ++ user_defined_delivery_groups

    {delivery_groups, build_delivery_group_messages(delivery_groups)}
  end

  defp add_instore_delivery_group([], delivery_groups), do: delivery_groups

  defp add_instore_delivery_group(local_cart_items, delivery_groups) do
    instore_delivery_group = %{
      "fulfillment_type" => "in-store",
      "pickup_point" => "",
      "delivery_time" => 0,
      "cart_items" => local_cart_items
    }

    [instore_delivery_group | delivery_groups]
  end

  defp build_delivery_group_messages(delivery_groups) do
    unfulfillable =
      Enum.find(delivery_groups, fn delivery_group ->
        delivery_group["fulfillment_type"] == "unfulfillable"
      end)

    case unfulfillable do
      nil ->
        []

      delivery_group ->
        [
          %{
            "type" => "error",
            "code" => "unfulfillable_items",
            "message" => "Some items are not available for the selected fulfillment_type",
            "info" => %{
              "items" => delivery_group["cart_items"]
            }
          }
        ]
    end
  end

  defp calculate_total_price(delivery_groups, discounts) do
    fulfillable_items =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.flat_map(fn group -> group["cart_items"] end)

    total_items_list_price =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.mult(Decimal.new(item["list_price"]), Decimal.new(item["quantity"]))
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_price =
      fulfillable_items
      |> Stream.map(fn item -> item["total_price"] end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_delivery_price =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.map(fn group -> group["delivery_price"] || 0 end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_manual_discount =
      Decimal.min(
        Decimal.sub(
          total_items_price,
          Enum.reduce(discounts, total_items_price, fn %{"type" => type, "value" => value}, acc ->
            case type do
              "percentage" ->
                Decimal.sub(acc, Decimal.mult(acc, Decimal.div(value, 100)))

              "fixed" ->
                Decimal.sub(acc, value)

              _ ->
                acc
            end
          end)
        ),
        total_items_price
      )

    total_items_selling_price =
      Decimal.sub(
        total_items_price,
        total_manual_discount
      )

    total_price =
      Decimal.add(
        total_delivery_price,
        total_items_selling_price
      )

    {%{
       "total_price" => total_price,
       "total_items_list_price" => total_items_list_price,
       "total_manual_discount" => total_manual_discount,
       "total_delivery_price" => total_delivery_price,
       "total_items_selling_price" => total_items_selling_price
     }
     |> Map.new(fn {k, v} -> {k, v |> Decimal.round(2)} end), []}
  end

  defp format_vtex_address(%{
         "receiverName" => receiver_name,
         "addressId" => address_id,
         "postalCode" => postal_code,
         "city" => city,
         "state" => state,
         "country" => country,
         "street" => street,
         "number" => number,
         "neighborhood" => neighborhood,
         "complement" => complement,
         "reference" => reference,
         "geoCoordinates" => geo_coordinates
       }) do
    %{
      "receiver_name" => receiver_name,
      "address_id" => address_id,
      "postal_code" => postal_code,
      "city" => city,
      "state" => state,
      "country" => country,
      "street" => street,
      "number" => number,
      "neighborhood" => neighborhood,
      "complement" => complement,
      "reference" => reference,
      "geo_coordinates" => geo_coordinates
    }
  end

  @doc """
  Re-runs a simulation for an existing cart.

  ## Examples

      iex> resimulate_cart(organization_id, cart_id)
      {:ok, %{...}}
  """
  def resimulate_cart(organization_id, cart_id) do
    cart =
      organization_id
      |> Rms.Commerce.Carts.get_cart!(cart_id, [
        :shipping_address,
        :customer,
        :discounts,
        cart_addons: [:addon],
        delivery_groups: [cart_items: [:product_variant]]
      ])

    # Build simulation parameters from existing cart
    simulation_params =
      %{
        "items" =>
          cart.delivery_groups
          |> Enum.flat_map(fn group ->
            Enum.map(group.cart_items, fn item ->
              %{
                "product_variant_id" => item.product_variant_id,
                "quantity" => item.quantity,
                "fulfillment_type" => group.fulfillment_type,
                "metadata" => group.metadata
              }
            end)
          end),
        "addons" =>
          Enum.map(cart.cart_addons, fn cart_addon ->
            %{
              "addon_id" => cart_addon.addon_id,
              "quantity" => cart_addon.quantity,
              "name" => cart_addon.name,
              "type" => cart_addon.type,
              "price" => cart_addon.price,
              "list_price" => cart_addon.list_price,
              "image_url" => cart_addon.image_url,
              "description" => cart_addon.description
            }
          end),
        "discounts" =>
          Enum.map(cart.discounts, fn discount ->
            %{
              "type" => discount.type,
              "value" => discount.value,
              "description" => discount.description
            }
          end),
        "customer_id" => cart.customer_id,
        "staff_id" => cart.staff_id,
        "location_id" => cart.location_id,
        "shipping_address_id" => cart.shipping_address_id,
        "postal_code" =>
          if cart.shipping_address do
            cart.shipping_address.zip
          else
            "04088004"
          end,
        "country" => "BRA"
      }

    simulate_cart(organization_id, simulation_params, cart.ecommerce)
  end
end
