defmodule Rms.Commerce.Carts.Simulator.Adapters.Shopify.Behaviour do
  @moduledoc """
  Defines the contract for Shopify API interactions in the cart simulator.

  This behaviour abstracts the Shopify GraphQL API calls to enable testing
  with mocks and provide a clean interface for cart simulation operations.
  """

  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Simulation}

  @doc """
  Simulates a cart using Shopify's draft order API.

  ## Parameters

    - `client`: The Shopify client configured for the organization
    - `cart`: The cart to simulate with Shopify
    - `opts`: Additional options for the simulation:
      - `:fulfillment_type` - The type of fulfillment ("in-store", "delivery", "local-pickup")
      - `:external_id_map` - Map of variant IDs to Shopify variant IDs
      - `:cart_attributes` - Additional cart attributes for Shopify

  ## Returns

    - `{:ok, simulation_result}` - Raw Shopify simulation response
    - `{:error, reason}` - API call failed

  ## Examples

      iex> simulate_cart(client, cart, fulfillment_type: "delivery")
      {:ok, %{"cart" => %{"lines" => %{"edges" => [...]}}}}
  """
  @callback simulate_cart(
              client :: Tesla.Client.t(),
              cart :: Cart.t(),
              opts :: keyword()
            ) :: {:ok, map()} | {:error, term()}

  @doc """
  Transforms a raw Shopify simulation response into our internal Simulation struct.

  ## Parameters

    - `shopify_response`: The raw response from Shopify's simulation API
    - `strategy`: The fulfillment strategy that was simulated
    - `original_items`: The original cart items for comparison
    - `opts`: Additional transformation options

  ## Returns

    - `{:ok, simulation}` - Successfully transformed simulation
    - `{:error, reason}` - Transformation failed
  """
  @callback transform_response(
              shopify_response :: map(),
              strategy :: atom(),
              original_items :: [Cart.Item.t()],
              opts :: keyword()
            ) :: {:ok, Simulation.t()} | {:error, term()}
end
