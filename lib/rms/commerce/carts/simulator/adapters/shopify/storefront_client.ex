defmodule Rms.Commerce.Carts.Simulator.Adapters.Shopify.StorefrontClient do
  @moduledoc """
  Shopify Storefront API adapter for cart simulation.

  This module handles all GraphQL communication with Shopify's Storefront API
  for cart simulation operations. It implements the Shopify adapter behaviour
  and provides a clean interface for cart simulation strategies.
  """

  @behaviour Rms.Commerce.Carts.Simulator.Adapters.Shopify.Behaviour

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount}

  @doc """
  Simulates a cart using Shopify's draft order API.

  This is the main entry point for Shopify cart simulation. It builds the
  appropriate GraphQL request, executes it, and returns the raw response.
  """
  @impl true
  def simulate_cart(client, cart, opts) do
    Logger.debug("Starting Shopify cart simulation",
      organization_id: cart.organization_id,
      fulfillment_type: Keyword.get(opts, :fulfillment_type)
    )

    try do
      # Prepare cart parameters for Shopify
      shopify_params = build_shopify_params(cart, opts)

      # Execute Shopify simulation
      case Rms.Integrations.Shopify.simulate_cart(client, shopify_params, cart.organization_id) do
        {:ok, raw_response} ->
          # Transform response to our internal format
          transform_response(raw_response, Keyword.get(opts, :fulfillment_type, :in_store), cart.items, opts)

        {:error, reason} = error ->
          Logger.warning("Shopify simulation API call failed",
            organization_id: cart.organization_id,
            error: reason
          )
          error
      end
    rescue
      error ->
        Logger.error("Shopify simulation failed with exception",
          organization_id: cart.organization_id,
          error: error
        )
        {:error, {:shopify_simulation_exception, error}}
    end
  end

  @doc """
  Transforms a raw Shopify simulation response into our internal Simulation struct.
  """
  @impl true
  def transform_response(shopify_response, strategy, original_items, opts) do
    try do
      # Extract cart data from Shopify response
      cart_data = get_in(shopify_response, ["cart"])

      if cart_data do
        # Process items from Shopify response
        processed_items = process_shopify_items(cart_data, original_items, strategy)

        # Extract discounts from Shopify response
        discounts = extract_discounts(cart_data)

        # Extract additional info based on strategy
        additional_info = extract_strategy_specific_info(cart_data, strategy)

        result = %{
          items: processed_items,
          discounts: discounts
        }
        |> Map.merge(additional_info)

        {:ok, result}
      else
        {:error, :invalid_shopify_response}
      end
    rescue
      error ->
        {:error, {:response_transformation_failed, error}}
    end
  end

  # Build Shopify client for the organization
  defp build_shopify_client(organization_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    Rms.Integrations.Shopify.storefront_client(
      shopify_credential.shop,
      shopify_credential.credential,
      organization_id: organization_id
    )
  end

  # Build Shopify-compatible parameters from our cart
  defp build_shopify_params(cart, opts) do
    # Convert our cart format to Shopify's expected format
    cart_info = %{
      "customer_id" => cart.customer_id,
      "location_id" => cart.location_id,
      "items" => Enum.map(cart.items, &item_to_shopify_format/1),
      "discounts" => Enum.map(cart.discounts || [], &discount_to_shopify_format/1),
      "metadata" => cart.metadata
    }

    # Use existing BuildParams module to format for Shopify
    external_id_map = Keyword.get(opts, :external_id_map, %{})
    cart_attributes = Keyword.get(opts, :cart_attributes, %{})

    Rms.Integrations.Shopify.Carts.BuildParams.execute(
      cart_info,
      external_id_map,
      cart.organization_id,
      cart_attributes: cart_attributes
    )
  end

  # Convert our Item struct to Shopify format
  defp item_to_shopify_format(item) do
    %{
      "product_variant_id" => item.variant_id,
      "quantity" => item.quantity,
      "fulfillment_type" => item.fulfillment,
      "external_id" => get_in(item.metadata, ["external_id"])
    }
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end

  # Convert our Discount struct to Shopify format
  defp discount_to_shopify_format(discount) do
    %{
      "code" => discount.code,
      "value" => Decimal.to_string(discount.value),
      "type" => discount.type
    }
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end

  # Process items from Shopify response back to our format
  defp process_shopify_items(cart_data, original_items, strategy) do
    shopify_lines = get_in(cart_data, ["lines", "edges"]) || []

    # Match Shopify items back to our original items
    Enum.map(original_items, fn original_item ->
      external_id = get_in(original_item.metadata, ["external_id"])

      # Find corresponding Shopify item
      shopify_item = find_shopify_item(shopify_lines, external_id)

      if shopify_item do
        process_matched_item(original_item, shopify_item, strategy)
      else
        # Item not found in Shopify response, return with original data
        %Item{
          variant_id: original_item.variant_id,
          quantity: original_item.quantity,
          fulfillment: original_item.fulfillment,
          price: original_item.price,
          list_price: original_item.list_price,
          metadata: Map.put(original_item.metadata || %{}, "shopify_processed", false)
        }
      end
    end)
  end

  # Find Shopify item by external ID
  defp find_shopify_item(shopify_lines, external_id) do
    Enum.find(shopify_lines, fn %{"node" => node} ->
      get_in(node, ["merchandise", "id"]) == external_id
    end)
  end

  # Process an item that was matched with Shopify data
  defp process_matched_item(original_item, shopify_item, strategy) do
    node = shopify_item["node"]

    # Extract pricing from Shopify
    cost = get_in(node, ["cost", "totalAmount", "amount"])
    unit_price = if cost && original_item.quantity > 0 do
      Decimal.div(Decimal.new(cost), Decimal.new(original_item.quantity))
    else
      original_item.price
    end

    # Extract discount allocations
    discount_allocations = get_in(node, ["discountAllocations"]) || []

    %Item{
      variant_id: original_item.variant_id,
      quantity: get_in(node, ["quantity"]) || original_item.quantity,
      fulfillment: original_item.fulfillment,
      price: unit_price,
      list_price: original_item.list_price || unit_price,
      metadata: Map.merge(original_item.metadata || %{}, %{
        "shopify_processed" => true,
        "total_cost" => cost,
        "discount_allocations" => discount_allocations,
        "strategy" => strategy
      })
    }
  end

  # Extract discounts from Shopify cart data
  defp extract_discounts(cart_data) do
    # Extract from discount codes
    discount_codes = get_in(cart_data, ["discountCodes"]) || []
    code_discounts = Enum.map(discount_codes, &process_discount_code/1)

    # Extract from discount allocations
    allocations = get_in(cart_data, ["discountAllocations"]) || []
    allocation_discounts = Enum.map(allocations, &process_discount_allocation/1)

    (code_discounts ++ allocation_discounts)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq_by(& &1.code)
  end

  # Process a discount code from Shopify
  defp process_discount_code(discount_code) do
    case discount_code do
      %{"code" => code, "applicable" => true} ->
        %Discount{
          code: code,
          value: Decimal.new("0"),  # Value will be calculated from allocations
          type: "code",
          description: "Discount code: #{code}"
        }
      _ ->
        nil
    end
  end

  # Process a discount allocation from Shopify
  defp process_discount_allocation(allocation) do
    amount = get_in(allocation, ["discountedAmount", "amount"])
    title = get_in(allocation, ["title"])

    if amount && title do
      %Discount{
        code: title,
        value: Decimal.new(amount),
        type: "automatic",
        description: title
      }
    else
      nil
    end
  end

  # Extract strategy-specific information
  defp extract_strategy_specific_info(cart_data, :delivery) do
    %{delivery_info: extract_delivery_info(cart_data)}
  end

  defp extract_strategy_specific_info(cart_data, :local_pickup) do
    %{pickup_info: extract_pickup_info(cart_data)}
  end

  defp extract_strategy_specific_info(_cart_data, _strategy) do
    %{}
  end

  # Extract delivery-specific information
  defp extract_delivery_info(cart_data) do
    delivery_groups = get_in(cart_data, ["deliveryGroups", "edges"]) || []

    if Enum.empty?(delivery_groups) do
      nil
    else
      # Take the first delivery group for now
      first_group = get_in(List.first(delivery_groups), ["node"])

      %{
        "delivery_options" => get_in(first_group, ["deliveryOptions"]) || [],
        "selected_delivery_option" => get_in(first_group, ["selectedDeliveryOption"]),
        "delivery_address" => get_in(first_group, ["deliveryAddress"])
      }
    end
  end

  # Extract pickup-specific information
  defp extract_pickup_info(cart_data) do
    # Extract pickup points and availability
    pickup_points = get_in(cart_data, ["pickupPoints"]) || []

    %{
      "available_locations" => pickup_points,
      "ready_time" => "24 hours"  # Default pickup time
    }
  end
end
