defmodule Rms.Commerce.Carts.Simulator.Core do
  @moduledoc """
  Core data structures for the Cart Simulator V2.

  This module defines the canonical data contracts used throughout the simulation pipeline.
  These structs serve as the single source of truth for data transformation and processing.
  """

  defmodule Cart do
    @moduledoc """
    Represents the initial cart input for simulation.

    This struct contains all the necessary information to perform cart simulation
    across different fulfillment strategies.
    """

    @enforce_keys [:organization_id, :items]
    defstruct [
      :organization_id,
      :customer_id,
      :location_id,
      :items,
      :discounts,
      :metadata
    ]

    @type t :: %__MODULE__{
            organization_id: pos_integer(),
            customer_id: pos_integer() | nil,
            location_id: pos_integer() | nil,
            items: [Item.t()],
            discounts: [Discount.t()] | nil,
            metadata: map() | nil
          }
  end

  defmodule Item do
    @moduledoc """
    Represents a single item within the cart.

    Contains product variant information, quantity, and fulfillment details.
    """

    @enforce_keys [:variant_id, :quantity]
    defstruct [
      :variant_id,
      :quantity,
      :fulfillment,
      :price,
      :list_price,
      :metadata
    ]

    @type t :: %__MODULE__{
            variant_id: pos_integer(),
            quantity: pos_integer(),
            fulfillment: String.t() | nil,
            price: Decimal.t() | nil,
            list_price: Decimal.t() | nil,
            metadata: map() | nil
          }
  end

  defmodule Simulation do
    @moduledoc """
    The result of a single fulfillment strategy simulation.

    Contains the strategy name, processed items, applicable discounts,
    and any messages or errors from the simulation.
    """

    @enforce_keys [:strategy, :items]
    defstruct [
      :strategy,
      :items,
      :discounts,
      :messages,
      :is_fulfillable
    ]

    @type t :: %__MODULE__{
            strategy: atom(),
            items: [Item.t()],
            discounts: [Discount.t()] | nil,
            messages: [String.t()] | nil,
            is_fulfillable: boolean() | nil
          }
  end

  defmodule Discount do
    @moduledoc """
    Represents an applicable discount, either manual or from a platform.

    Can represent both automatic discounts discovered during simulation
    and manual discounts applied by staff.
    """

    @enforce_keys [:code, :value]
    defstruct [
      :code,
      :value,
      :type,
      :target,
      :description
    ]

    @type t :: %__MODULE__{
            code: String.t(),
            value: Decimal.t(),
            type: String.t() | nil,
            target: String.t() | nil,
            description: String.t() | nil
          }
  end

  defmodule Totals do
    @moduledoc """
    Represents the final calculated totals for the cart.

    Contains all pricing information including list prices, selling prices,
    delivery costs, discounts, and the final total.
    """

    defstruct [
      :items_list_price,
      :items_selling_price,
      :delivery_price,
      :manual_discount,
      :ecommerce_discounts,
      :final_price
    ]

    @type t :: %__MODULE__{
            items_list_price: Decimal.t() | nil,
            items_selling_price: Decimal.t() | nil,
            delivery_price: Decimal.t() | nil,
            manual_discount: Decimal.t() | nil,
            ecommerce_discounts: Decimal.t() | nil,
            final_price: Decimal.t() | nil
          }
  end
end
