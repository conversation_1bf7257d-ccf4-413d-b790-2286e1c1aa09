defmodule Rms.Commerce.Carts.Simulator.Discount do
  @moduledoc """
  Pure functions for discount calculation and cart totals.

  This module contains only pure functions responsible for financial calculations.
  It aggregates automatic discounts from fulfillment strategies, combines them
  with manual discounts, and calculates final cart totals.
  """

  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Simulation, Discount, Totals}

  @doc """
  Applies discounts and calculates final cart totals.

  ## Parameters

    - `cart`: The original cart with manual discounts
    - `simulations`: Results from all fulfillment strategies
    - `manual_discounts`: Additional manual discounts to apply
    - `opts`: Options controlling discount behavior:
      - `:ignore_automatic_discounts` - Skip automatic discounts if true

  ## Returns

    - `{:ok, totals, messages}` - Calculated totals and any messages
    - `{:error, reason}` - Calculation failed

  ## Examples

      iex> apply(cart, simulations, [], [])
      {:ok, %Totals{final_price: Decimal.new("29.99")}, []}
  """
  @spec apply(Cart.t(), [Simulation.t()], [Discount.t()], keyword()) ::
          {:ok, Totals.t(), [String.t()]} | {:error, term()}
  def apply(cart, simulations, manual_discounts, opts \\ []) do
    try do
      # Extract fulfillable simulations
      fulfillable_simulations = Enum.filter(simulations, & &1.is_fulfillable)

      # Calculate base item totals
      {items_list_price, items_selling_price} = calculate_item_totals(fulfillable_simulations)

      # Calculate delivery costs
      delivery_price = calculate_delivery_price(fulfillable_simulations)

      # Aggregate automatic discounts
      automatic_discounts =
        if Keyword.get(opts, :ignore_automatic_discounts, false) do
          []
        else
          aggregate_automatic_discounts(fulfillable_simulations)
        end

      # Calculate manual discount total
      manual_discount_total = calculate_manual_discount_total(manual_discounts)

      # Calculate ecommerce discount total
      ecommerce_discount_total = calculate_ecommerce_discount_total(automatic_discounts)

      # Calculate final price (matching V1 logic)
      final_price =
        items_selling_price
        |> Decimal.add(delivery_price)
        |> Decimal.sub(ecommerce_discount_total)
        # Ensure non-negative
        |> Decimal.max(Decimal.new("0"))

      totals = %Totals{
        items_list_price: round_decimal(items_list_price),
        items_selling_price: round_decimal(items_selling_price),
        delivery_price: round_decimal(delivery_price),
        manual_discount: round_decimal(manual_discount_total),
        ecommerce_discounts: round_decimal(ecommerce_discount_total),
        final_price: round_decimal(final_price)
      }

      # Generate any calculation messages
      messages = generate_messages(cart, simulations, totals)

      {:ok, totals, messages}
    rescue
      error ->
        {:error, {:discount_calculation_error, error}}
    end
  end

  # Calculate total list price and selling price from all fulfillable items
  defp calculate_item_totals(simulations) do
    {list_total, selling_total} =
      simulations
      |> Enum.flat_map(& &1.items)
      |> Enum.reduce({Decimal.new("0"), Decimal.new("0")}, fn item, {list_acc, selling_acc} ->
        quantity = Decimal.new(item.quantity)

        list_price =
          if item.list_price do
            Decimal.mult(item.list_price, quantity)
          else
            Decimal.new("0")
          end

        selling_price =
          if item.price do
            Decimal.mult(item.price, quantity)
          else
            # Fallback to list price if no selling price
            list_price
          end

        {Decimal.add(list_acc, list_price), Decimal.add(selling_acc, selling_price)}
      end)

    {list_total, selling_total}
  end

  # Calculate total delivery costs from fulfillable simulations
  defp calculate_delivery_price(simulations) do
    simulations
    |> Enum.filter(fn simulation ->
      simulation.strategy in [:delivery, :local_pickup] and simulation.is_fulfillable
    end)
    |> Enum.reduce(Decimal.new("0"), fn simulation, acc ->
      delivery_cost = extract_delivery_cost(simulation)
      Decimal.add(acc, delivery_cost)
    end)
  end

  # Extract delivery cost from simulation
  defp extract_delivery_cost(simulation) do
    # Look for delivery cost in various places in the simulation metadata
    cond do
      # Check for Shopify delivery cost
      delivery_info = get_in(simulation.items, [Access.at(0), :metadata, "delivery_info"]) ->
        get_in(delivery_info, ["selected_delivery_option", "amount"])
        |> case do
          nil -> Decimal.new("0")
          amount -> Decimal.new(amount)
        end

      # Check for logistics info delivery cost
      logistics_info = get_in(simulation.items, [Access.at(0), :metadata, "logistics_info"]) ->
        get_in(logistics_info, ["delivery_price"])
        |> case do
          nil -> Decimal.new("0")
          price -> Decimal.new(price)
        end

      # Default to zero
      true ->
        Decimal.new("0")
    end
  end

  # Round decimal to 2 places (matching V1 behavior)
  defp round_decimal(decimal) when is_nil(decimal), do: Decimal.new("0")
  defp round_decimal(decimal), do: Decimal.round(decimal, 2)

  # Aggregate all automatic discounts from fulfillment strategies
  defp aggregate_automatic_discounts(simulations) do
    simulations
    |> Enum.flat_map(fn simulation ->
      simulation.discounts || []
    end)
    # Remove duplicates by code
    |> Enum.uniq_by(& &1.code)
  end

  # Calculate total value of manual discounts
  defp calculate_manual_discount_total(manual_discounts) do
    manual_discounts
    |> Enum.reduce(Decimal.new("0"), fn discount, acc ->
      Decimal.add(acc, discount.value)
    end)
  end

  # Calculate total value of ecommerce/automatic discounts
  defp calculate_ecommerce_discount_total(automatic_discounts) do
    automatic_discounts
    |> Enum.reduce(Decimal.new("0"), fn discount, acc ->
      Decimal.add(acc, discount.value)
    end)
  end

  # Generate informational messages about the calculation
  defp generate_messages(cart, simulations, totals) do
    messages = []

    # Add messages for unfulfillable simulations
    unfulfillable_messages =
      simulations
      |> Enum.filter(fn simulation -> not simulation.is_fulfillable end)
      |> Enum.flat_map(fn simulation ->
        simulation.messages || ["#{simulation.strategy} fulfillment not available"]
      end)

    # Add messages for zero total (if applicable)
    zero_total_messages =
      if Decimal.equal?(totals.final_price, Decimal.new("0")) do
        ["Cart total is zero after discounts"]
      else
        []
      end

    messages ++ unfulfillable_messages ++ zero_total_messages
  end
end
