defmodule Rms.Commerce.Carts.Simulator do
  @moduledoc """
  Public facade for cart simulation with feature flag controlled V1/V2 switching.

  This module provides the main entry point for cart simulation and handles
  the transition between the legacy implementation (V1) and the new modular
  implementation (V2) based on feature flags.
  """

  require Logger

  @doc """
  Simulates a cart using either V1 (legacy) or V2 (new) implementation.

  The implementation choice is controlled by the `cart_simulator_v2` feature flag.
  When the flag is enabled for an organization, the new V2 implementation is used.
  Otherwise, it falls back to the legacy V1 implementation.

  ## Parameters

    - `organization_id`: The organization performing the simulation
    - `cart_info`: Raw cart information from the API request

  ## Returns

    - `{:ok, simulation_result}` - Successful simulation with formatted response
    - `{:error, reason}` - Simulation failed

  ## Examples

      iex> cart_info = %{"items" => [%{"product_variant_id" => 123, "quantity" => 2}]}
      iex> Simulator.run(1, cart_info)
      {:ok, %{"delivery_groups" => [...], "total_price" => "29.99"}}
  """
  @spec run(pos_integer(), map()) :: {:ok, map()} | {:error, term()}
  def run(organization_id, cart_info) do
    if Rms.FeatureFlag.should_use_new_simulator?(organization_id) do
      Logger.info("Using Cart Simulator V2", organization_id: organization_id)

      case Rms.Commerce.Carts.Simulator.V2.run(organization_id, cart_info) do
        {:ok, result} ->
          {:ok, result}

        {:error, reason} = error ->
          Logger.error("Cart Simulator V2 failed, falling back to V1",
            organization_id: organization_id,
            error: reason
          )

          # Fallback to V1 on error to ensure system stability
          run_v1(organization_id, cart_info)
      end
    else
      Logger.debug("Using Cart Simulator V1 (legacy)", organization_id: organization_id)
      run_v1(organization_id, cart_info)
    end
  end

  @doc """
  Forces the use of V2 implementation regardless of feature flag.

  This function is primarily intended for testing and gradual rollout scenarios
  where you want to explicitly test the V2 implementation.

  ## Parameters

    - `organization_id`: The organization performing the simulation
    - `cart_info`: Raw cart information from the API request

  ## Returns

    - `{:ok, simulation_result}` - Successful V2 simulation
    - `{:error, reason}` - V2 simulation failed
  """
  @spec run_v2(pos_integer(), map()) :: {:ok, map()} | {:error, term()}
  def run_v2(organization_id, cart_info) do
    Logger.info("Forcing Cart Simulator V2", organization_id: organization_id)
    Rms.Commerce.Carts.Simulator.V2.run(organization_id, cart_info)
  end

  # Private function to run the legacy V1 implementation
  defp run_v1(organization_id, cart_info) do
    {:ok, Rms.Integrations.Shopify.Carts.Simulate.execute(organization_id, cart_info)}
  end
end
