defmodule Rms.Commerce.Carts.Simulator.Strategy.Behaviour do
  @moduledoc """
  Defines the contract for fulfillment strategy implementations.

  Each fulfillment strategy (in-store, delivery, local-pickup) must implement
  this behaviour to provide a consistent interface for cart simulation.
  """

  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Simulation}

  @doc """
  Executes the fulfillment strategy simulation for the given cart.

  ## Parameters

    - `cart`: The cart to simulate, containing items and customer information
    - `opts`: Keyword list of options that control simulation behavior:
      - `:ignore_inventory_level` - Skip inventory checks if true
      - `:ignore_automatic_discounts` - Skip automatic discount discovery if true
      - `:external_id_map` - Map of variant IDs to external platform IDs
      - `:cart_attributes` - Additional cart attributes for platform simulation

  ## Returns

    - `{:ok, simulation}` - Successful simulation with results
    - `{:error, reason}` - Simulation failed with error details

  ## Examples

      iex> cart = %Cart{organization_id: 1, items: [%Item{variant_id: 123, quantity: 2}]}
      iex> InStore.run(cart, ignore_inventory_level: true)
      {:ok, %Simulation{strategy: :in_store, items: [...], is_fulfillable: true}}
  """
  @callback run(cart :: Cart.t(), opts :: keyword()) ::
              {:ok, Simulation.t()} | {:error, term()}
end
