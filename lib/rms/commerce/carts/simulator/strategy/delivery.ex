defmodule Rms.Commerce.Carts.Simulator.Strategy.Delivery do
  @moduledoc """
  Delivery fulfillment strategy implementation.

  Handles simulation for items that will be delivered, including shipping cost
  calculation, delivery time estimation, and integration with Shopify's delivery
  simulation APIs.
  """

  @behaviour Rms.Commerce.Carts.Simulator.Strategy.Behaviour

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount}
  alias Rms.Commerce.Carts.Simulator.Adapters.Shopify.StorefrontClient

  @impl true
  def run(%Cart{} = cart, opts) do
    Logger.debug("Running delivery strategy",
      organization_id: cart.organization_id,
      items_count: length(cart.items)
    )

    try do
      # Validate delivery requirements
      case validate_delivery_requirements(cart) do
        {:ok, _} ->
          # Get external ID mapping for Shopify integration
          external_id_map = get_external_id_map(cart.organization_id, cart.items)

          # Simulate with Shopify to get delivery options and pricing
          case simulate_with_shopify(cart, external_id_map, opts) do
            {:ok, %{items: processed_items, discounts: discounts, delivery_info: delivery_info}} ->
              # Determine if delivery is available
              is_fulfillable = determine_delivery_availability(delivery_info, processed_items)

              simulation = %Simulation{
                strategy: :delivery,
                items: processed_items,
                discounts: discounts,
                is_fulfillable: is_fulfillable,
                messages: generate_delivery_messages(delivery_info, is_fulfillable)
              }

              Logger.debug("Delivery strategy completed",
                organization_id: cart.organization_id,
                is_fulfillable: is_fulfillable,
                delivery_cost: get_delivery_cost(delivery_info)
              )

              {:ok, simulation}

            {:error, reason} ->
              Logger.warning("Shopify delivery simulation failed",
                organization_id: cart.organization_id,
                error: reason
              )

              # Return unfulfillable simulation
              {:ok, create_unfulfillable_simulation(cart.items, reason)}
          end

        {:error, reason} ->
          Logger.info("Delivery requirements not met",
            organization_id: cart.organization_id,
            reason: reason
          )

          {:ok, create_unfulfillable_simulation(cart.items, reason)}
      end
    rescue
      error ->
        Logger.error("Delivery strategy failed",
          organization_id: cart.organization_id,
          error: error
        )

        {:error, {:delivery_strategy_failed, error}}
    end
  end

  # Validate that delivery can be performed for this cart
  defp validate_delivery_requirements(cart) do
    cond do
      is_nil(cart.customer_id) ->
        {:error, "Customer ID required for delivery"}

      is_nil(cart.location_id) ->
        {:error, "Location ID required for delivery"}

      Enum.empty?(cart.items) ->
        {:error, "No items to deliver"}

      true ->
        {:ok, :valid}
    end
  end

  # Build Shopify client for the organization
  defp build_shopify_client(organization_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    Rms.Integrations.Shopify.storefront_client(
      shopify_credential.shop,
      shopify_credential.credential,
      organization_id: organization_id
    )
  end

  # Get mapping of variant IDs to external Shopify IDs
  defp get_external_id_map(organization_id, items) do
    variant_ids = Enum.map(items, & &1.variant_id)

    Rms.Integrations.get_product_variant_external_ids(
      organization_id,
      variant_ids,
      "shopify"
    )
  end

  # Simulate delivery using Shopify's delivery simulation
  defp simulate_with_shopify(cart, external_id_map, opts) do
    # Add external IDs to items
    items_with_external_ids =
      Enum.map(cart.items, fn item ->
        external_id = Map.get(external_id_map, item.variant_id)
        %{item | metadata: Map.put(item.metadata || %{}, "external_id", external_id)}
      end)

    shopify_cart = %{cart | items: items_with_external_ids}

    # Build Shopify client
    client = build_shopify_client(shopify_cart.organization_id)

    # Use the Shopify adapter to perform delivery simulation
    adapter_opts =
      Keyword.merge(opts,
        fulfillment_type: "delivery",
        external_id_map: external_id_map,
        include_delivery_options: true
      )

    StorefrontClient.simulate_cart(client, shopify_cart, adapter_opts)
  end

  # Determine if delivery is available based on simulation results
  defp determine_delivery_availability(delivery_info, items) do
    cond do
      is_nil(delivery_info) ->
        false

      Enum.empty?(items) ->
        false

      # Check if any delivery options are available
      get_delivery_options(delivery_info) == [] ->
        false

      # Check if all items can be delivered
      not all_items_deliverable?(items) ->
        false

      true ->
        true
    end
  end

  # Extract delivery options from delivery info
  defp get_delivery_options(delivery_info) do
    delivery_info
    |> Map.get("delivery_options", [])
    |> Enum.filter(fn option ->
      # Filter out options that are not available
      Map.get(option, "available", false)
    end)
  end

  # Check if all items can be delivered
  defp all_items_deliverable?(items) do
    Enum.all?(items, fn item ->
      # Check item-specific delivery constraints
      availability = get_in(item.metadata, ["availability"])
      availability != "cannotBeDelivered"
    end)
  end

  # Get delivery cost from delivery info
  defp get_delivery_cost(delivery_info) do
    delivery_info
    |> Map.get("selected_delivery_option", %{})
    |> Map.get("amount", "0.00")
    |> Decimal.new()
  end

  # Generate messages about delivery simulation
  defp generate_delivery_messages(delivery_info, is_fulfillable) do
    messages = []

    # Add availability messages
    availability_messages =
      if not is_fulfillable do
        ["Delivery not available for this location"]
      else
        []
      end

    # Add delivery time messages
    time_messages =
      if is_fulfillable and delivery_info do
        delivery_time = get_delivery_time(delivery_info)

        if delivery_time do
          ["Estimated delivery: #{delivery_time}"]
        else
          []
        end
      else
        []
      end

    # Add cost messages
    cost_messages =
      if is_fulfillable and delivery_info do
        cost = get_delivery_cost(delivery_info)

        if Decimal.positive?(cost) do
          ["Delivery cost: #{cost}"]
        else
          ["Free delivery"]
        end
      else
        []
      end

    messages ++ availability_messages ++ time_messages ++ cost_messages
  end

  # Extract delivery time from delivery info
  defp get_delivery_time(delivery_info) do
    delivery_info
    |> Map.get("selected_delivery_option", %{})
    |> Map.get("estimated_time")
  end

  # Create an unfulfillable simulation for error cases
  defp create_unfulfillable_simulation(items, reason) do
    %Simulation{
      strategy: :delivery,
      items: items,
      discounts: [],
      is_fulfillable: false,
      messages: ["Delivery unavailable: #{reason}"]
    }
  end
end
