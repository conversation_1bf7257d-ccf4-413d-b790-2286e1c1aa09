defmodule Rms.Commerce.Carts.Simulator.Strategy.InStore do
  @moduledoc """
  In-store fulfillment strategy implementation.

  Handles simulation for items that will be fulfilled in-store, including
  inventory checks, pricing, and discount discovery through Shopify integration.
  """

  @behaviour Rms.Commerce.Carts.Simulator.Strategy.Behaviour

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount}
  alias Rms.Commerce.Carts.Simulator.Adapters.Shopify.StorefrontClient

  @impl true
  def run(%Cart{} = cart, opts) do
    Logger.debug("Running in-store strategy",
      organization_id: cart.organization_id,
      items_count: length(cart.items)
    )

    try do
      # Get external ID mapping for Shopify integration
      external_id_map = get_external_id_map(cart.organization_id, cart.items)

      # Filter items that have Shopify mappings vs local-only items
      {shopify_items, local_items} = partition_items(cart.items, external_id_map)

      # Process Shopify items if any exist
      shopify_simulation_result =
        if Enum.empty?(shopify_items) do
          {:ok, %{items: [], discounts: []}}
        else
          simulate_with_shopify(cart, shopify_items, external_id_map, opts)
        end

      case shopify_simulation_result do
        {:ok, %{items: shopify_processed_items, discounts: shopify_discounts}} ->
          # Process local-only items
          local_processed_items = process_local_items(local_items, cart.organization_id)

          # Combine results
          all_items = shopify_processed_items ++ local_processed_items
          all_discounts = shopify_discounts

          # Determine if fulfillment is possible
          is_fulfillable = determine_fulfillability(all_items, opts)

          simulation = %Simulation{
            strategy: :in_store,
            items: all_items,
            discounts: all_discounts,
            is_fulfillable: is_fulfillable,
            messages: generate_messages(all_items, is_fulfillable)
          }

          Logger.debug("In-store strategy completed",
            organization_id: cart.organization_id,
            is_fulfillable: is_fulfillable,
            items_count: length(all_items)
          )

          {:ok, simulation}

        {:error, reason} ->
          Logger.warning("Shopify simulation failed for in-store strategy",
            organization_id: cart.organization_id,
            error: reason
          )

          # Fallback to local processing only
          local_processed_items = process_local_items(cart.items, cart.organization_id)
          is_fulfillable = determine_fulfillability(local_processed_items, opts)

          simulation = %Simulation{
            strategy: :in_store,
            items: local_processed_items,
            discounts: [],
            is_fulfillable: is_fulfillable,
            messages:
              ["Shopify integration unavailable, using local pricing"] ++
                generate_messages(local_processed_items, is_fulfillable)
          }

          {:ok, simulation}
      end
    rescue
      error ->
        Logger.error("In-store strategy failed",
          organization_id: cart.organization_id,
          error: error
        )

        {:error, {:in_store_strategy_failed, error}}
    end
  end

  # Build Shopify client for the organization
  defp build_shopify_client(organization_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    Rms.Integrations.Shopify.storefront_client(
      shopify_credential.shop,
      shopify_credential.credential,
      organization_id: organization_id
    )
  end

  # Get mapping of variant IDs to external Shopify IDs
  defp get_external_id_map(organization_id, items) do
    variant_ids = Enum.map(items, & &1.variant_id)

    Rms.Integrations.get_product_variant_external_ids(
      organization_id,
      variant_ids,
      "shopify"
    )
  end

  # Separate items into those with Shopify mappings vs local-only
  defp partition_items(items, external_id_map) do
    Enum.split_with(items, fn item ->
      Map.has_key?(external_id_map, item.variant_id)
    end)
  end

  # Simulate Shopify items using the Shopify adapter
  defp simulate_with_shopify(cart, items, external_id_map, opts) do
    # Add external IDs to items
    items_with_external_ids =
      Enum.map(items, fn item ->
        external_id = Map.get(external_id_map, item.variant_id)
        %{item | metadata: Map.put(item.metadata || %{}, "external_id", external_id)}
      end)

    shopify_cart = %{cart | items: items_with_external_ids}

    # Build Shopify client
    client = build_shopify_client(shopify_cart.organization_id)

    # Use the Shopify adapter to perform the simulation
    adapter_opts =
      Keyword.merge(opts,
        fulfillment_type: "in-store",
        external_id_map: external_id_map
      )

    StorefrontClient.simulate_cart(client, shopify_cart, adapter_opts)
  end

  # Process items that don't have Shopify mappings (local-only)
  defp process_local_items(items, organization_id) do
    Enum.map(items, fn item ->
      # Get product variant information from local database
      variant = Rms.Commerce.Products.get_product_variant!(organization_id, item.variant_id)

      %Item{
        variant_id: item.variant_id,
        quantity: item.quantity,
        fulfillment: item.fulfillment,
        price: variant.price,
        list_price: variant.list_price || variant.price,
        metadata:
          Map.merge(item.metadata || %{}, %{
            "variant_name" => variant.name,
            "sku" => variant.sku,
            "processed_locally" => true
          })
      }
    end)
  end

  # Determine if the items can be fulfilled based on inventory and other constraints
  defp determine_fulfillability(items, opts) do
    ignore_inventory = Keyword.get(opts, :ignore_inventory_level, false)

    if ignore_inventory do
      true
    else
      # Check inventory levels for each item
      Enum.all?(items, fn item ->
        # This would check actual inventory levels
        # For now, assume all items are fulfillable
        true
      end)
    end
  end

  # Generate informational messages about the simulation
  defp generate_messages(items, is_fulfillable) do
    messages = []

    # Add inventory messages if needed
    inventory_messages =
      if not is_fulfillable do
        ["Some items are out of stock for in-store fulfillment"]
      else
        []
      end

    # Add local processing messages
    local_items_count =
      items
      |> Enum.count(fn item ->
        get_in(item.metadata, ["processed_locally"]) == true
      end)

    local_messages =
      if local_items_count > 0 do
        ["#{local_items_count} items processed with local pricing"]
      else
        []
      end

    messages ++ inventory_messages ++ local_messages
  end
end
