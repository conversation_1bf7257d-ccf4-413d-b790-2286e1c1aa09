defmodule Rms.Commerce.Carts.Simulator.Strategy.LocalPickup do
  @moduledoc """
  Local pickup fulfillment strategy implementation.

  Handles simulation for items that will be picked up at designated pickup points,
  including pickup location validation, availability checks, and integration with
  Shopify's local pickup simulation.
  """

  @behaviour Rms.Commerce.Carts.Simulator.Strategy.Behaviour

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount}
  alias Rms.Commerce.Carts.Simulator.Adapters.Shopify.StorefrontClient

  @impl true
  def run(%Cart{} = cart, opts) do
    Logger.debug("Running local pickup strategy",
      organization_id: cart.organization_id,
      items_count: length(cart.items)
    )

    try do
      # Extract pickup location from cart metadata
      pickup_location = extract_pickup_location(cart)

      case validate_pickup_requirements(cart, pickup_location) do
        {:ok, validated_location} ->
          # Get external ID mapping for Shopify integration
          external_id_map = get_external_id_map(cart.organization_id, cart.items)

          # Simulate with Shopify to get pickup availability and pricing
          case simulate_with_shopify(cart, external_id_map, validated_location, opts) do
            {:ok, %{items: processed_items, discounts: discounts, pickup_info: pickup_info}} ->
              # Determine if pickup is available
              is_fulfillable = determine_pickup_availability(pickup_info, processed_items, validated_location)

              simulation = %Simulation{
                strategy: :local_pickup,
                items: processed_items,
                discounts: discounts,
                is_fulfillable: is_fulfillable,
                messages: generate_pickup_messages(pickup_info, validated_location, is_fulfillable)
              }

              Logger.debug("Local pickup strategy completed",
                organization_id: cart.organization_id,
                is_fulfillable: is_fulfillable,
                pickup_location: validated_location
              )

              {:ok, simulation}

            {:error, reason} ->
              Logger.warning("Shopify pickup simulation failed",
                organization_id: cart.organization_id,
                error: reason
              )

              # Return unfulfillable simulation
              {:ok, create_unfulfillable_simulation(cart.items, reason)}
          end

        {:error, reason} ->
          Logger.info("Local pickup requirements not met",
            organization_id: cart.organization_id,
            reason: reason
          )

          {:ok, create_unfulfillable_simulation(cart.items, reason)}
      end
    rescue
      error ->
        Logger.error("Local pickup strategy failed",
          organization_id: cart.organization_id,
          error: error
        )
        {:error, {:local_pickup_strategy_failed, error}}
    end
  end

  # Extract pickup location information from cart
  defp extract_pickup_location(cart) do
    # Look for pickup location in item metadata first
    pickup_from_items =
      cart.items
      |> Enum.find_value(fn item ->
        get_in(item.metadata, ["metadata"])
      end)

    # Fallback to cart metadata
    pickup_from_cart = cart.metadata

    pickup_from_items || pickup_from_cart
  end

  # Validate that local pickup can be performed
  defp validate_pickup_requirements(cart, pickup_location) do
    cond do
      is_nil(pickup_location) ->
        {:error, "Pickup location not specified"}

      Enum.empty?(cart.items) ->
        {:error, "No items for pickup"}

      not valid_pickup_location?(pickup_location) ->
        {:error, "Invalid pickup location"}

      true ->
        {:ok, pickup_location}
    end
  end

  # Validate pickup location format and availability
  defp valid_pickup_location?(pickup_location) when is_map(pickup_location) do
    # Check for required pickup location fields
    required_fields = ["location_id", "address"]
    Enum.all?(required_fields, &Map.has_key?(pickup_location, &1))
  end

  defp valid_pickup_location?(_), do: false

  # Build Shopify client for the organization
  defp build_shopify_client(organization_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    Rms.Integrations.Shopify.storefront_client(
      shopify_credential.shop,
      shopify_credential.credential,
      organization_id: organization_id
    )
  end

  # Get mapping of variant IDs to external Shopify IDs
  defp get_external_id_map(organization_id, items) do
    variant_ids = Enum.map(items, & &1.variant_id)

    Rms.Integrations.get_product_variant_external_ids(
      organization_id,
      variant_ids,
      "shopify"
    )
  end

  # Simulate local pickup using Shopify's pickup simulation
  defp simulate_with_shopify(cart, external_id_map, pickup_location, opts) do
    # Add external IDs to items
    items_with_external_ids =
      Enum.map(cart.items, fn item ->
        external_id = Map.get(external_id_map, item.variant_id)
        %{item | metadata: Map.put(item.metadata || %{}, "external_id", external_id)}
      end)

    shopify_cart = %{cart | items: items_with_external_ids}

    # Build Shopify client
    client = build_shopify_client(shopify_cart.organization_id)

    # Use the Shopify adapter to perform pickup simulation
    adapter_opts = Keyword.merge(opts, [
      fulfillment_type: "local-pickup",
      external_id_map: external_id_map,
      pickup_location: pickup_location
    ])

    StorefrontClient.simulate_cart(client, shopify_cart, adapter_opts)
  end

  # Determine if pickup is available based on simulation results
  defp determine_pickup_availability(pickup_info, items, pickup_location) do
    cond do
      is_nil(pickup_info) ->
        false

      Enum.empty?(items) ->
        false

      # Check if pickup location is available
      not pickup_location_available?(pickup_info, pickup_location) ->
        false

      # Check if all items are available for pickup
      not all_items_available_for_pickup?(items) ->
        false

      true ->
        true
    end
  end

  # Check if the specified pickup location is available
  defp pickup_location_available?(pickup_info, pickup_location) do
    available_locations = Map.get(pickup_info, "available_locations", [])
    requested_location_id = Map.get(pickup_location, "location_id")

    Enum.any?(available_locations, fn location ->
      Map.get(location, "id") == requested_location_id
    end)
  end

  # Check if all items are available for pickup
  defp all_items_available_for_pickup?(items) do
    Enum.all?(items, fn item ->
      # Check item-specific pickup availability
      availability = get_in(item.metadata, ["pickup_availability"])
      availability != "unavailable"
    end)
  end

  # Generate messages about pickup simulation
  defp generate_pickup_messages(pickup_info, pickup_location, is_fulfillable) do
    messages = []

    # Add availability messages
    availability_messages =
      if not is_fulfillable do
        ["Local pickup not available at requested location"]
      else
        []
      end

    # Add pickup location messages
    location_messages =
      if is_fulfillable and pickup_location do
        location_name = Map.get(pickup_location, "name", "Selected location")
        ["Pickup available at: #{location_name}"]
      else
        []
      end

    # Add pickup time messages
    time_messages =
      if is_fulfillable and pickup_info do
        pickup_time = get_pickup_time(pickup_info)
        if pickup_time do
          ["Ready for pickup: #{pickup_time}"]
        else
          ["Ready for pickup within 24 hours"]
        end
      else
        []
      end

    # Add special instructions if any
    instruction_messages =
      if is_fulfillable and pickup_location do
        instructions = Map.get(pickup_location, "instructions")
        if instructions do
          ["Pickup instructions: #{instructions}"]
        else
          []
        end
      else
        []
      end

    messages ++ availability_messages ++ location_messages ++ time_messages ++ instruction_messages
  end

  # Extract pickup ready time from pickup info
  defp get_pickup_time(pickup_info) do
    pickup_info
    |> Map.get("ready_time")
  end

  # Create an unfulfillable simulation for error cases
  defp create_unfulfillable_simulation(items, reason) do
    %Simulation{
      strategy: :local_pickup,
      items: items,
      discounts: [],
      is_fulfillable: false,
      messages: ["Local pickup unavailable: #{reason}"]
    }
  end
end
