defmodule Rms.Commerce.Carts.Simulator.V2 do
  @moduledoc """
  The new Cart Simulator V2 implementation orchestrator.

  This module coordinates the entire simulation process:
  1. Decodes input into canonical structs
  2. Builds operational options from feature flags
  3. Runs fulfillment strategies in parallel
  4. Aggregates results and applies discounts
  5. Encodes output to expected JSON format
  """

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation}
  alias Rms.Commerce.Carts.Simulator.{Discount}
  alias Rms.Commerce.Carts.Simulator.Strategy

  @doc """
  Executes the complete cart simulation process using the V2 implementation.

  ## Parameters

    - `organization_id`: The organization performing the simulation
    - `cart_info`: Raw cart information from the API request

  ## Returns

    - `{:ok, formatted_result}` - Successful simulation with formatted response
    - `{:error, reason}` - Simulation failed at any stage
  """
  @spec run(pos_integer(), map()) :: {:ok, map()} | {:error, term()}
  def run(organization_id, cart_info) do
    Logger.debug("Starting Cart Simulator V2", organization_id: organization_id)

    with {:ok, cart} <- decode_input(organization_id, cart_info),
         {:ok, opts} <- build_options(organization_id, cart_info),
         {:ok, simulations} <- run_strategies(cart, opts),
         {:ok, totals, messages} <- apply_discounts(cart, simulations, opts),
         {:ok, result} <- encode_output(cart, simulations, totals, messages) do

      Logger.info("Cart Simulator V2 completed successfully",
        organization_id: organization_id,
        strategies_count: length(simulations)
      )

      {:ok, result}
    else
      {:error, reason} = error ->
        Logger.error("Cart Simulator V2 failed",
          organization_id: organization_id,
          error: reason
        )
        error
    end
  end

  # Step 1: Transform raw controller payload into canonical Cart struct
  defp decode_input(organization_id, cart_info) do
    try do
      items =
        cart_info
        |> Map.get("items", [])
        |> Enum.map(&decode_item/1)

      discounts =
        cart_info
        |> Map.get("discounts", [])
        |> Enum.map(&decode_discount/1)

      cart = %Cart{
        organization_id: organization_id,
        customer_id: cart_info["customer_id"],
        location_id: cart_info["location_id"],
        items: items,
        discounts: discounts,
        metadata: cart_info["metadata"]
      }

      {:ok, cart}
    rescue
      error ->
        {:error, {:decode_input_failed, error}}
    end
  end

  defp decode_item(item_info) do
    %Item{
      variant_id: item_info["product_variant_id"],
      quantity: item_info["quantity"],
      fulfillment: item_info["fulfillment_type"],
      metadata: item_info
    }
  end

  defp decode_discount(discount_info) do
    %Rms.Commerce.Carts.Simulator.Core.Discount{
      code: discount_info["code"] || "manual",
      value: Decimal.new(discount_info["value"] || "0"),
      type: discount_info["type"],
      description: discount_info["description"]
    }
  end

  # Step 2: Build operational options from feature flags and settings
  defp build_options(organization_id, cart_info) do
    opts = [
      organization_id: organization_id,
      ignore_inventory_level: false,  # Could be controlled by feature flag
      ignore_automatic_discounts: false,  # Could be controlled by feature flag
      cart_attributes: build_cart_attributes(organization_id, cart_info)
    ]

    {:ok, opts}
  end

  defp build_cart_attributes(organization_id, cart_info) do
    # This would build Shopify-specific cart attributes
    # For now, return basic attributes
    %{
      "customer_id" => cart_info["customer_id"],
      "location_id" => cart_info["location_id"],
      "organization_id" => organization_id
    }
  end

  # Step 3: Run all fulfillment strategies in parallel
  defp run_strategies(cart, opts) do
    strategies = [
      {Rms.Commerce.Carts.Simulator.Strategy.InStore, :in_store},
      {Rms.Commerce.Carts.Simulator.Strategy.Delivery, :delivery},
      {Rms.Commerce.Carts.Simulator.Strategy.LocalPickup, :local_pickup}
    ]

    # Group items by fulfillment type
    items_by_fulfillment = group_items_by_fulfillment(cart.items)

    # Run strategies for relevant fulfillment types only
    tasks =
      strategies
      |> Enum.filter(fn {_module, strategy_name} ->
        Map.has_key?(items_by_fulfillment, strategy_name)
      end)
      |> Enum.map(fn {strategy_module, strategy_name} ->
        strategy_cart = %{cart | items: Map.get(items_by_fulfillment, strategy_name, [])}

        Task.async(fn ->
          case strategy_module.run(strategy_cart, opts) do
            {:ok, simulation} -> simulation
            {:error, reason} ->
              Logger.warning("Strategy #{strategy_name} failed",
                organization_id: cart.organization_id,
                error: reason
              )
              %Simulation{
                strategy: strategy_name,
                items: strategy_cart.items,
                is_fulfillable: false,
                messages: ["Strategy failed: #{inspect(reason)}"]
              }
          end
        end)
      end)

    simulations =
      tasks
      |> Enum.map(&Task.await(&1, 10_000))

    {:ok, simulations}
  end

  defp group_items_by_fulfillment(items) do
    items
    |> Enum.group_by(fn item ->
      case item.fulfillment do
        "in-store" -> :in_store
        "delivery" -> :delivery
        "local-pickup" -> :local_pickup
        _ -> :in_store  # Default fallback
      end
    end)
  end

  # Step 4: Aggregate results and apply discount calculations
  defp apply_discounts(cart, simulations, opts) do
    manual_discounts = cart.discounts || []

    case Discount.apply(cart, simulations, manual_discounts, opts) do
      {:ok, totals, messages} -> {:ok, totals, messages}
      {:error, reason} -> {:error, {:discount_calculation_failed, reason}}
    end
  end

  # Step 5: Format results into expected JSON structure
  defp encode_output(cart, simulations, totals, messages) do
    # This would format the output to match the expected API response
    # For now, return a basic structure
    result = %{
      "customer_id" => cart.customer_id,
      "location_id" => cart.location_id,
      "delivery_groups" => format_delivery_groups(simulations),
      "ecommerce" => "shopify",
      "messages" => messages || [],
      "total_delivery_price" => totals.delivery_price,
      "total_items_list_price" => totals.items_list_price,
      "total_items_selling_price" => totals.items_selling_price,
      "total_manual_discount" => totals.manual_discount,
      "total_price" => totals.final_price,
      "discounts" => format_discounts(simulations)
    }

    {:ok, result}
  end

  defp format_delivery_groups(simulations) do
    # Transform simulations into delivery groups format
    # This is a placeholder - would need full implementation
    []
  end

  defp format_discounts(simulations) do
    # Extract and format discounts from simulations
    # This is a placeholder - would need full implementation
    []
  end
end
