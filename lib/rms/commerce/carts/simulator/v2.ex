defmodule Rms.Commerce.Carts.Simulator.V2 do
  @moduledoc """
  The new Cart Simulator V2 implementation orchestrator.

  This module coordinates the entire simulation process:
  1. Decodes input into canonical structs
  2. Builds operational options from feature flags
  3. Runs fulfillment strategies in parallel
  4. Aggregates results and applies discounts
  5. Encodes output to expected JSON format
  """

  require Logger
  alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation}
  alias Rms.Commerce.Carts.Simulator.{Discount}
  alias Rms.Commerce.Carts.Simulator.Strategy

  @doc """
  Executes the complete cart simulation process using the V2 implementation.

  ## Parameters

    - `organization_id`: The organization performing the simulation
    - `cart_info`: Raw cart information from the API request

  ## Returns

    - `{:ok, formatted_result}` - Successful simulation with formatted response
    - `{:error, reason}` - Simulation failed at any stage
  """
  @spec run(pos_integer(), map()) :: {:ok, map()} | {:error, term()}
  def run(organization_id, cart_info) do
    Logger.debug("Starting Cart Simulator V2", organization_id: organization_id)

    with {:ok, cart} <- decode_input(organization_id, cart_info),
         {:ok, opts} <- build_options(organization_id, cart_info),
         {:ok, simulations} <- run_strategies(cart, opts),
         {:ok, totals, messages} <- apply_discounts(cart, simulations, opts),
         {:ok, result} <- encode_output(cart, simulations, totals, messages) do
      Logger.info("Cart Simulator V2 completed successfully",
        organization_id: organization_id,
        strategies_count: length(simulations)
      )

      {:ok, result}
    else
      {:error, reason} = error ->
        Logger.error("Cart Simulator V2 failed",
          organization_id: organization_id,
          error: reason
        )

        error
    end
  end

  # Step 1: Transform raw controller payload into canonical Cart struct
  defp decode_input(organization_id, cart_info) do
    try do
      # Add item indices like V1 does
      items_with_indices =
        cart_info
        |> Map.get("items", [])
        |> Enum.with_index(fn item, index ->
          Map.put(item, "item_index", index)
        end)

      items = Enum.map(items_with_indices, &decode_item/1)

      discounts =
        cart_info
        |> Map.get("discounts", [])
        |> Enum.map(&decode_discount/1)

      # Include additional metadata that V1 uses
      metadata =
        Map.merge(cart_info["metadata"] || %{}, %{
          "shipping_address_id" => cart_info["shipping_address_id"],
          "staff_id" => cart_info["staff_id"],
          "notes" => cart_info["notes"]
        })

      cart = %Cart{
        organization_id: organization_id,
        customer_id: cart_info["customer_id"],
        location_id: cart_info["location_id"],
        items: items,
        discounts: discounts,
        metadata: metadata
      }

      {:ok, cart}
    rescue
      error ->
        {:error, {:decode_input_failed, error}}
    end
  end

  defp decode_item(item_info) do
    %Item{
      variant_id: item_info["product_variant_id"],
      quantity: item_info["quantity"],
      fulfillment: item_info["fulfillment_type"],
      metadata: %{
        "item_index" => item_info["item_index"],
        "original_metadata" => item_info["metadata"],
        "fulfillment_type" => item_info["fulfillment_type"],
        "discounts" => item_info["discounts"]
      }
    }
  end

  defp decode_discount(discount_info) do
    %Rms.Commerce.Carts.Simulator.Core.Discount{
      code: discount_info["code"] || "manual",
      value: Decimal.new(discount_info["value"] || "0"),
      type: discount_info["type"],
      description: discount_info["description"]
    }
  end

  # Step 2: Build operational options from feature flags and settings
  defp build_options(organization_id, cart_info) do
    opts = [
      organization_id: organization_id,
      # Could be controlled by feature flag
      ignore_inventory_level: false,
      # Could be controlled by feature flag
      ignore_automatic_discounts: false,
      cart_attributes: build_cart_attributes(organization_id, cart_info)
    ]

    {:ok, opts}
  end

  defp build_cart_attributes(organization_id, cart_info) do
    # This would build Shopify-specific cart attributes
    # For now, return basic attributes
    %{
      "customer_id" => cart_info["customer_id"],
      "location_id" => cart_info["location_id"],
      "organization_id" => organization_id
    }
  end

  # Step 3: Run all fulfillment strategies in parallel
  defp run_strategies(cart, opts) do
    # Get external ID mapping like V1 does
    product_variant_ids = Enum.map(cart.items, & &1.variant_id)

    external_id_map =
      Rms.Integrations.get_product_variant_external_ids(
        cart.organization_id,
        product_variant_ids,
        "shopify"
      )

    # Filter items like V1 does: local vs ecommerce items
    {local_items, ecommerce_items, removed_items} = filter_items(cart.items, external_id_map)

    # Group ecommerce items by fulfillment type for Shopify simulation
    ecommerce_items_by_fulfillment = group_items_by_fulfillment(ecommerce_items)

    # Run strategies for ecommerce items that have external mappings
    ecommerce_simulations =
      run_ecommerce_strategies(cart, ecommerce_items_by_fulfillment, external_id_map, opts)

    # Process local items (items without Shopify mappings)
    local_simulations = process_local_items_as_simulations(cart, local_items)

    # Process removed items (items that couldn't be processed)
    removed_simulations = process_removed_items_as_simulations(cart, removed_items)

    all_simulations = ecommerce_simulations ++ local_simulations ++ removed_simulations

    {:ok, all_simulations}
  end

  # Filter items like V1 does
  defp filter_items(items, external_id_map) do
    {local_items, ecommerce_items, removed_items} =
      Enum.reduce(items, {[], [], []}, fn item, {local_acc, ecommerce_acc, removed_acc} ->
        cond do
          # In-store items without external mapping go to local
          item.fulfillment == "in-store" and not Map.has_key?(external_id_map, item.variant_id) ->
            {[item | local_acc], ecommerce_acc, removed_acc}

          # Items with external mapping go to ecommerce
          Map.has_key?(external_id_map, item.variant_id) ->
            external_id = external_id_map[item.variant_id]

            item_with_external_id = %{
              item
              | metadata: Map.put(item.metadata || %{}, "external_id", external_id)
            }

            {local_acc, [item_with_external_id | ecommerce_acc], removed_acc}

          # Other items go to removed
          true ->
            {local_acc, ecommerce_acc, [item | removed_acc]}
        end
      end)

    {Enum.reverse(local_items), Enum.reverse(ecommerce_items), Enum.reverse(removed_items)}
  end

  # Run strategies for ecommerce items (items with Shopify mappings)
  defp run_ecommerce_strategies(cart, items_by_fulfillment, external_id_map, opts) do
    strategies = [
      {Rms.Commerce.Carts.Simulator.Strategy.InStore, :in_store},
      {Rms.Commerce.Carts.Simulator.Strategy.Delivery, :delivery},
      {Rms.Commerce.Carts.Simulator.Strategy.LocalPickup, :local_pickup}
    ]

    # Run strategies for relevant fulfillment types only
    tasks =
      strategies
      |> Enum.filter(fn {_module, strategy_name} ->
        Map.has_key?(items_by_fulfillment, strategy_name)
      end)
      |> Enum.map(fn {strategy_module, strategy_name} ->
        strategy_items = Map.get(items_by_fulfillment, strategy_name, [])
        strategy_cart = %{cart | items: strategy_items}

        Task.async(fn ->
          strategy_opts = Keyword.put(opts, :external_id_map, external_id_map)

          case strategy_module.run(strategy_cart, strategy_opts) do
            {:ok, simulation} ->
              simulation

            {:error, reason} ->
              Logger.warning("Strategy #{strategy_name} failed",
                organization_id: cart.organization_id,
                error: reason
              )

              %Simulation{
                strategy: strategy_name,
                items: strategy_items,
                is_fulfillable: false,
                messages: ["Strategy failed: #{inspect(reason)}"]
              }
          end
        end)
      end)

    tasks
    |> Enum.map(&Task.await(&1, 10_000))
  end

  # Process local items (items without Shopify mappings) as simulations
  defp process_local_items_as_simulations(cart, local_items) do
    if Enum.empty?(local_items) do
      []
    else
      # Group local items by fulfillment type
      local_items_by_fulfillment = group_items_by_fulfillment(local_items)

      Enum.map(local_items_by_fulfillment, fn {fulfillment_type, items} ->
        processed_items =
          Enum.map(items, fn item ->
            # Get product variant information from local database
            variant =
              Rms.Commerce.Products.get_product_variant!(cart.organization_id, item.variant_id)

            %Item{
              variant_id: item.variant_id,
              quantity: item.quantity,
              fulfillment: item.fulfillment,
              price: variant.price,
              list_price: variant.list_price || variant.price,
              metadata:
                Map.merge(item.metadata || %{}, %{
                  "variant_name" => variant.name,
                  "sku" => variant.sku,
                  "processed_locally" => true,
                  "avaliable_fulfillment_type" => "LOCAL-IN-STORE"
                })
            }
          end)

        %Simulation{
          strategy: fulfillment_type,
          items: processed_items,
          discounts: [],
          is_fulfillable: true,
          messages: ["#{length(items)} items processed with local pricing"]
        }
      end)
    end
  end

  # Process removed items (items that couldn't be processed) as unfulfillable simulations
  defp process_removed_items_as_simulations(cart, removed_items) do
    if Enum.empty?(removed_items) do
      []
    else
      processed_items =
        Enum.map(removed_items, fn item ->
          # Get product variant information from local database
          variant =
            Rms.Commerce.Products.get_product_variant!(cart.organization_id, item.variant_id)

          %Item{
            variant_id: item.variant_id,
            quantity: item.quantity,
            fulfillment: item.fulfillment,
            price: variant.price,
            list_price: variant.list_price || variant.price,
            metadata:
              Map.merge(item.metadata || %{}, %{
                "variant_name" => variant.name,
                "sku" => variant.sku,
                "removed_item" => true
              })
          }
        end)

      [
        %Simulation{
          strategy: :unfulfillable,
          items: processed_items,
          discounts: [],
          is_fulfillable: false,
          messages: ["#{length(removed_items)} items could not be processed"]
        }
      ]
    end
  end

  defp group_items_by_fulfillment(items) do
    items
    |> Enum.group_by(fn item ->
      case item.fulfillment do
        "in-store" -> :in_store
        "delivery" -> :delivery
        "local-pickup" -> :local_pickup
        # Default fallback
        _ -> :in_store
      end
    end)
  end

  # Step 4: Aggregate results and apply discount calculations
  defp apply_discounts(cart, simulations, opts) do
    manual_discounts = cart.discounts || []

    case Discount.apply(cart, simulations, manual_discounts, opts) do
      {:ok, totals, messages} -> {:ok, totals, messages}
      {:error, reason} -> {:error, {:discount_calculation_failed, reason}}
    end
  end

  # Step 5: Format results into expected JSON structure
  defp encode_output(cart, simulations, totals, messages) do
    # Format delivery groups from simulations
    {fulfillable_items, unfulfillable_items} = categorize_simulation_items(simulations)
    delivery_groups = format_delivery_groups(fulfillable_items, unfulfillable_items)

    # Extract ecommerce discounts from simulations
    ecommerce_discounts = extract_ecommerce_discounts(simulations)
    cart_discounts = cart.discounts || []

    # Format the response to match V1 exactly
    result = %{
      "customer_id" => cart.customer_id,
      "location_id" => cart.location_id,
      "shipping_address_id" => get_in(cart.metadata, ["shipping_address_id"]),
      "delivery_groups" => delivery_groups,
      "ecommerce" => "shopify",
      "messages" => messages || [],
      "staff_id" => get_in(cart.metadata, ["staff_id"]),
      "total_delivery_price" => totals.delivery_price,
      "total_items_list_price" => totals.items_list_price,
      "total_items_selling_price" => totals.items_selling_price,
      "total_manual_discount" => totals.manual_discount,
      "total_price" => totals.final_price,
      "discounts" =>
        format_discount_list(ecommerce_discounts) ++ format_discount_list(cart_discounts),
      "total_ecommerce_discounts" => totals.ecommerce_discounts || Decimal.new("0"),
      "metadata" => %{"notes" => get_in(cart.metadata, ["notes"])}
    }

    {:ok, result}
  end

  # Categorize simulation items into fulfillable and unfulfillable
  defp categorize_simulation_items(simulations) do
    {fulfillable, unfulfillable} =
      simulations
      |> Enum.reduce({[], []}, fn simulation, {fulfillable_acc, unfulfillable_acc} ->
        if simulation.is_fulfillable do
          formatted_items = format_simulation_items_for_delivery_group(simulation)
          {formatted_items ++ fulfillable_acc, unfulfillable_acc}
        else
          formatted_items = format_simulation_items_for_delivery_group(simulation)
          {fulfillable_acc, formatted_items ++ unfulfillable_acc}
        end
      end)

    {fulfillable, unfulfillable}
  end

  # Format simulation items to match V1's cart item structure
  defp format_simulation_items_for_delivery_group(simulation) do
    simulation.items
    |> Enum.with_index()
    |> Enum.map(fn {item, index} ->
      # Extract logistics info from item metadata
      logistics_info = get_in(item.metadata, ["logistics_info"])

      %{
        "avaliable_fulfillment_type" =>
          determine_available_fulfillment_type(simulation.strategy, item),
        "fulfillment_type" => atom_to_fulfillment_string(simulation.strategy),
        "item_index" => get_in(item.metadata, ["item_index"]) || index,
        "list_price" => item.list_price || item.price || Decimal.new("0"),
        "logistics_info" => logistics_info,
        "metadata" => extract_item_metadata(item, simulation),
        "original_metadata" => get_in(item.metadata, ["original_metadata"]),
        "price" => calculate_total_item_price(item),
        "product_variant_id" => item.variant_id,
        "quantity" => item.quantity,
        "request_index" => 0,
        "selling_price" => item.price || Decimal.new("0"),
        "total_price" => calculate_total_item_price(item)
      }
    end)
  end

  # Format delivery groups to match V1's structure exactly
  defp format_delivery_groups(fulfillable_items, unfulfillable_items) do
    # Group fulfillable items by fulfillment type and handle
    fulfillable_groups =
      fulfillable_items
      |> Enum.group_by(fn item ->
        {item["fulfillment_type"], get_in(item["metadata"], ["handle"]) || ""}
      end)
      |> Enum.map(fn {{fulfillment_type, _handle}, items} ->
        %{
          "fulfillment_type" => fulfillment_type,
          "pickup_point" => "",
          "cart_items" => add_selling_price_to_items(items)
        }
      end)

    # Add unfulfillable group if there are unfulfillable items
    case unfulfillable_items do
      [] ->
        fulfillable_groups

      _ ->
        unfulfillable_group = %{
          "fulfillment_type" => "unfulfillable",
          "pickup_point" => "",
          "cart_items" => add_selling_price_to_items(unfulfillable_items)
        }

        [unfulfillable_group | fulfillable_groups]
    end
  end

  # Add selling_price and price fields to items (V1 compatibility)
  defp add_selling_price_to_items(items) do
    Enum.map(items, fn item ->
      selling_price =
        if item["quantity"] > 0 do
          Decimal.div(item["total_price"], Decimal.new(item["quantity"]))
        else
          Decimal.new("0")
        end

      item
      |> Map.put("selling_price", selling_price)
      |> Map.put("price", selling_price)
    end)
  end

  # Helper functions for item formatting
  defp determine_available_fulfillment_type(:in_store, _item), do: "in-store"
  defp determine_available_fulfillment_type(:delivery, _item), do: "delivery"
  defp determine_available_fulfillment_type(:local_pickup, _item), do: "local-pickup"
  defp determine_available_fulfillment_type(_, _item), do: nil

  defp atom_to_fulfillment_string(:in_store), do: "in-store"
  defp atom_to_fulfillment_string(:delivery), do: "delivery"
  defp atom_to_fulfillment_string(:local_pickup), do: "local-pickup"
  defp atom_to_fulfillment_string(_), do: "unfulfillable"

  defp calculate_total_item_price(item) do
    if item.price && item.quantity do
      Decimal.mult(item.price, Decimal.new(item.quantity))
    else
      Decimal.new("0")
    end
  end

  defp extract_item_metadata(item, simulation) do
    base_metadata = item.metadata || %{}

    # Add simulation-specific metadata
    Map.merge(base_metadata, %{
      "handle" => get_handle_from_strategy(simulation.strategy),
      "strategy" => simulation.strategy
    })
  end

  defp get_handle_from_strategy(:in_store), do: "in-store"
  defp get_handle_from_strategy(:delivery), do: "delivery"
  defp get_handle_from_strategy(:local_pickup), do: "local-pickup"
  defp get_handle_from_strategy(_), do: ""

  # Extract ecommerce discounts from simulations
  defp extract_ecommerce_discounts(simulations) do
    simulations
    |> Enum.flat_map(fn simulation ->
      simulation.discounts || []
    end)
    |> Enum.uniq_by(& &1.code)
  end

  # Format discount list to match V1 structure
  defp format_discount_list(discounts) when is_list(discounts) do
    Enum.map(discounts, &format_single_discount/1)
  end

  defp format_discount_list(_), do: []

  defp format_single_discount(%Rms.Commerce.Carts.Simulator.Core.Discount{} = discount) do
    %{
      "code" => discount.code,
      "value" => discount.value,
      "type" => discount.type,
      "description" => discount.description
    }
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end

  defp format_single_discount(discount) when is_map(discount), do: discount
  defp format_single_discount(_), do: %{}
end
