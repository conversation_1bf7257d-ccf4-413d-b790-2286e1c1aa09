defmodule Rms.Commerce.Discounts do
  alias Rms.Commerce.Discounts

  def validate_token(authorization_request, code) do
    cond do
      authorization_request.status == "accepted" -> {:error, "request already approved"}
      authorization_request.status == "canceled" -> {:error, "request already canceled"}
      authorization_request.status == "rejected" -> {:error, "request already denied"}
      !validate_code(code) -> {:unauthorized, "invalid code"}
      Plug.Crypto.secure_compare(authorization_request.code, code) -> {:ok, "valid code"}
      true -> {:unauthorized, "wrong code"}
    end
  end

  defp validate_code(code) do
    code =~ ~r/^[0-9]{6}$/
  end

  def get_authorization_request!(organization_id, id) do
    Rms.Commerce.Discounts.AuthorizationRequest
    |> Rms.Repo.get_by!(id: id, organization_id: organization_id)
  end

  def update_authorization_request_status!(authorization_request, status) do
    authorization_request
    |> Discounts.AuthorizationRequest.update_changeset(%{"status" => status})
    |> Rms.Repo.update!()
  end

  def create_authorization_request(organization_id, attrs) do
    code =
      (:rand.uniform(1_000_000) - 1)
      |> Integer.to_string()
      |> String.pad_leading(6, "0")

    attrs = Map.put(attrs, "code", code)

    %Discounts.AuthorizationRequest{organization_id: organization_id}
    |> Discounts.AuthorizationRequest.changeset(attrs)
    |> Rms.Repo.insert!()
  end

  def send_authorization_request(authorization_request, attrs) do
    %{
      receivers:
        get_manager_emails(
          authorization_request.organization_id,
          authorization_request.location_id
        ),
      params: %{
        code: authorization_request.code,
        cart: %{
          staff:
            Rms.Accounts.get_staff!(authorization_request.organization_id, attrs["staff_id"]),
          items: Map.get(attrs, "items", []),
          discounts:
            authorization_request.discounts
            |> Enum.reject(fn discount -> discount.type == "coupon" end)
            |> Enum.map(&format_discounts/1),
          from: attrs["total_items_list_price"],
          to: attrs["total_items_selling_price"]
        }
      }
    }
    |> Rms.Emails.Authorization.send()
  end

  defp format_discounts(discount) do
    %{
      value:
        case discount.type do
          "percentage" -> "#{discount.value}%"
          "fixed" -> "R$ #{discount.value |> Decimal.round(2)}"
          _ -> "unknown"
        end,
      description: discount.description
    }
  end

  defp get_manager_emails(organization_id, location_id) do
    case Rms.Settings.get_location_setting(organization_id, location_id, "manager_emails") do
      %{value: %{"data" => [name, email]}} -> {name, email}
      _ -> nil
    end
  end
end
