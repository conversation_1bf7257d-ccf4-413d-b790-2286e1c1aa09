defmodule Rms.Commerce.Discounts.AuthorizationRequest do
  use Ecto.Schema
  import Ecto.Changeset

  @statuses ~w(accepted pending rejected canceled)

  schema "authorization_requests" do
    field :code, :string
    field :status, :string, default: "pending"

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location
    belongs_to :staff, Rms.Accounts.Staff

    has_many :discounts, Rms.Commerce.Discounts.Discount,
      defaults: {Rms.Repo, :add_organization_id, []}

    timestamps(type: :utc_datetime)
  end

  def changeset(discount, attrs, _opts \\ []) do
    discount
    |> cast(attrs, [
      :code,
      :status,
      :staff_id,
      :location_id
    ])
    |> validate_required([
      :code,
      :status,
      :staff_id,
      :location_id
    ])
    |> validate_inclusion(:status, @statuses)
    |> foreign_key_constraint(:location_id)
    |> foreign_key_constraint(:staff_id)
    |> cast_assoc(:discounts)
  end

  def update_changeset(discount, attrs, _opts \\ []) do
    discount
    |> cast(attrs, [
      :status
    ])
    |> validate_inclusion(:status, @statuses)
  end
end
