defmodule Rms.Commerce.Discounts.Discount do
  use Ecto.Schema
  import Ecto.Changeset

  @types ~w(coupon automatic_ecommerce percentage fixed)

  schema "discounts" do
    field :type, :string
    field :value, :string
    field :description, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :authorization_request, Rms.Commerce.Discounts.AuthorizationRequest
    belongs_to :order, Rms.Commerce.Orders.Order
    belongs_to :cart, Rms.Commerce.Carts.Cart, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  def changeset(discount, attrs) do
    discount
    |> cast(attrs, [
      :type,
      :value,
      :description,
      :order_id,
      :cart_id
    ])
    |> validate_required([
      :type,
      :value
    ])
    |> validate_inclusion(:type, @types)
    |> foreign_key_constraint(:authorization_request_id)
    |> foreign_key_constraint(:order_id)
    |> foreign_key_constraint(:cart_id)
    |> validate_required([:value])
  end
end
