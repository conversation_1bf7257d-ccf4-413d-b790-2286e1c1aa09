defmodule Rms.Commerce.Discounts.Engine do
  def execute(organization_id, %{"ecommerce" => ecommerce} = cart, cart_info)
      when ecommerce != "vtex" do
    organization_id
    |> get_used_engines()
    |> run_engines(organization_id, cart, cart_info)
  end

  def execute(_organization_id, cart, _cart_info) do
    cart
  end

  defp run_engines(engines, organization_id, cart, cart_info) do
    Enum.reduce(engines, cart, fn engine, acc ->
      run_engine(engine, organization_id, acc, cart_info)
    end)
  end

  defp run_engine("manual_discount", organization_id, cart, cart_info) do
    Rms.Commerce.Discounts.Engine.ManualDiscount.execute(organization_id, cart_info, cart)
  end

  defp run_engine("gifts", organization_id, cart, _cart_info) do
    Rms.Commerce.Discounts.Engine.GiftHandler.execute(organization_id, cart)
  end

  defp run_engine(_, _organization_id, cart, _) do
    cart
  end

  defp get_used_engines(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "discounts_engines") do
      %{value: %{"data" => discounts_engines}} -> discounts_engines ++ ["manual_discount"]
      _ -> ["manual_discount"]
    end
  end
end
