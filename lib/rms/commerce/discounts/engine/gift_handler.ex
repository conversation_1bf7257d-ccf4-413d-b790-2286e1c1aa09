defmodule Rms.Commerce.Discounts.Engine.GiftHandler do
  def execute(organization_id, cart) do
    updated_delivery_groups = build_new_delivery_groups(organization_id, cart["delivery_groups"])

    {updated_cart_values, _} =
      calculate_cart_values(
        updated_delivery_groups,
        cart["total_manual_discount"],
        cart["total_ecommerce_discounts"]
      )

    cart
    |> Map.put("delivery_groups", updated_delivery_groups)
    |> Map.put("total_items_list_price", updated_cart_values["total_items_list_price"])
    |> Map.put("total_items_selling_price", updated_cart_values["total_items_selling_price"])
    |> Map.put("total_price", updated_cart_values["total_price"])
  end

  defp build_new_delivery_groups(organization_id, delivery_groups) do
    Enum.reduce(delivery_groups, [], fn delivery_group, acc ->
      if should_process_delivery_group?(delivery_group) do
        process_delivery_group(delivery_group, organization_id, acc)
      else
        [delivery_group | acc]
      end
    end)
  end

  defp should_process_delivery_group?(%{"fulfillment_type" => fulfillment_type}) do
    fulfillment_type in ["in-store", "delivery", "local-pickup"]
  end

  defp should_process_delivery_group?(_), do: false

  defp process_delivery_group(delivery_group, organization_id, acc) do
    case build_new_items(organization_id, delivery_group) do
      {:ok, new_delivery_group} -> [new_delivery_group | acc]
      _ -> [delivery_group | acc]
    end
  end

  defp build_new_items(organization_id, delivery_group) do
    delivery_group
    |> get_gifts(organization_id)
    |> update_gift_items()
    |> update_items_cost()
    |> build_new_delivery_group(delivery_group)
  end

  defp get_gifts(%{"cart_items" => items}, organization_id) do
    Enum.reduce(items, {:ok, []}, fn item, {:ok, acc} ->
      {
        :ok,
        [
          %{
            item: item,
            gift_configuration:
              get_gifts_configuration(organization_id, item["product_variant_id"])
          }
          | acc
        ]
      }
    end)
  end

  defp update_gift_items({:ok, item_maps}) do
    Enum.reduce(item_maps, {:ok, []}, fn item_map, {:ok, acc} ->
      gifts =
        Enum.map(item_map.gift_configuration.gifts, fn %{
                                                         quantity: q,
                                                         cost: c,
                                                         product_variant: product_variant
                                                       } ->
          q = q * item_map.item["quantity"]

          %{
            "avaliable_fulfillment_type" => item_map.item["avaliable_fulfillment_type"],
            "fulfillment_type" => item_map.item["fulfillment_type"],
            "item_index" => item_map.item["item_index"],
            "list_price" => product_variant.price,
            "logistics_info" => item_map.item["logistics_info"],
            "metadata" => item_map.item["metadata"],
            "original_metadata" => item_map.item["original_metadata"],
            "price" => c,
            "product_variant_id" => product_variant.id,
            "quantity" => q,
            "request_index" => item_map.item["request_index"],
            "selling_price" => c,
            "total_price" => Decimal.mult(c, Decimal.new(q)),
            "is_gift" => true
          }
        end)

      {:ok, [Map.put(item_map, :gift_items, gifts) | acc]}
    end)
  end

  defp update_gift_items(error) do
    error
  end

  defp update_items_cost({:ok, item_maps}) do
    Enum.reduce(item_maps, {:ok, []}, fn item_map, {:ok, acc} ->
      gifts_cost = calculate_gifts_cost(item_map.gift_configuration.gifts)

      updated_items =
        item_map.item
        |> Map.put("price", Decimal.sub(item_map.item["price"], gifts_cost))
        |> Map.put("selling_price", Decimal.sub(item_map.item["selling_price"], gifts_cost))
        |> Map.put(
          "total_price",
          Decimal.sub(
            item_map.item["total_price"],
            Decimal.mult(item_map.item["quantity"], gifts_cost)
          )
        )

      {:ok, [Map.put(item_map, :item, updated_items) | acc]}
    end)
  end

  defp update_items_cost(error) do
    error
  end

  defp build_new_delivery_group({:ok, item_maps}, delivery_group) do
    new_items =
      Enum.reduce(item_maps, [], fn item_map, acc ->
        items = [item_map.item | item_map.gift_items]

        acc ++ items
      end)
      |> Enum.reverse()

    {:ok, Map.put(delivery_group, "cart_items", new_items)}
  end

  defp build_new_delivery_group(error, _delivery_group) do
    error
  end

  defp get_gifts_configuration(organization_id, product_variant_id) do
    variant =
      Rms.Commerce.Products.get_product_variant!(organization_id, product_variant_id,
        gifts_bundle: [gifts: [:product_variant]]
      )

    case variant.gifts_bundle do
      nil ->
        %{gifts: []}

      gifts ->
        gifts
    end
  end

  defp calculate_gifts_cost(gifts) do
    Enum.reduce(gifts, Decimal.new("0"), fn gift, acc ->
      Decimal.add(acc, Decimal.mult(Decimal.new(gift.quantity), gift.cost))
    end)
  end

  defp calculate_cart_values(delivery_groups, discounts, ecommerce_discounts) do
    fulfillable_items =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.flat_map(fn group -> group["cart_items"] end)

    total_items_list_price =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.mult(Decimal.new(item["list_price"]), Decimal.new(item["quantity"]))
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_price =
      fulfillable_items
      |> Stream.map(fn item -> item["total_price"] end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_delivery_price =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.reject(&(&1["fulfillment_type"] == "in-store"))
      |> Stream.map(fn group ->
        hd(group["cart_items"])["logistics_info"]["estimatedCost"]["amount"] || 0
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_manual_discount = discounts

    total_ecommerce_discounts = ecommerce_discounts

    total_items_selling_price =
      Decimal.sub(
        total_items_price,
        total_manual_discount
      )

    total_price =
      Decimal.add(
        total_delivery_price,
        total_items_selling_price
      )
      |> Decimal.sub(total_ecommerce_discounts)

    {%{
       "total_price" => total_price,
       "total_items_list_price" => total_items_list_price,
       "total_manual_discount" => total_manual_discount,
       "total_delivery_price" => total_delivery_price,
       "total_items_selling_price" => total_items_selling_price,
       "total_ecommerce_discounts" => total_ecommerce_discounts
     }
     |> Map.new(fn {k, v} -> {k, v |> Decimal.round(2)} end), []}
  end
end
