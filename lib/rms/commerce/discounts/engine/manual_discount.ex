defmodule Rms.Commerce.Discounts.Engine.ManualDiscount do
  def execute(_organization_id, cart_info, cart) do
    updated_delivery_groups =
      calculate_new_items(cart_info, cart["delivery_groups"])

    {updated_cart_values, _} =
      calculate_cart_values(
        updated_delivery_groups,
        cart,
        cart_info
      )

    cart
    |> Map.put("delivery_groups", updated_delivery_groups)
    |> Map.put("total_items_list_price", updated_cart_values["total_items_list_price"])
    |> Map.put("total_items_selling_price", updated_cart_values["total_items_selling_price"])
    |> Map.put("total_price", updated_cart_values["total_price"])
    |> Map.put("total_manual_discount", updated_cart_values["total_manual_discount"])
    |> Map.put("total_items_manual_discount", updated_cart_values["total_items_manual_discount"])
  end

  defp calculate_new_items(cart_info, delivery_groups) do
    Enum.map(delivery_groups, fn delivery_group ->
      process_delivery_group(delivery_group, cart_info)
    end)
  end

  defp process_delivery_group(delivery_group, cart_info) do
    new_items =
      Enum.map(delivery_group["cart_items"], fn cart_item ->
        item_info =
          Enum.find(cart_info["items"], fn item ->
            item["product_variant_id"] == cart_item["product_variant_id"] and
              item["fulfillment_type"] == cart_item["fulfillment_type"]
          end)

        if is_nil(item_info) or item_info["discounts"] == [] do
          cart_item
        else
          apply_item_discounts(cart_item, item_info)
        end
      end)

    Map.put(delivery_group, "cart_items", new_items)
  end

  defp apply_item_discounts(cart_item, items_info) do
    original_total_price = cart_item["total_price"]

    total_manual_discount =
      Decimal.min(
        Decimal.sub(
          original_total_price,
          Enum.reduce(items_info["discounts"] || [], original_total_price, fn
            %{"type" => type, "value" => value}, acc ->
              case type do
                "percentage" ->
                  Decimal.sub(acc, Decimal.mult(acc, Decimal.div(value, 100)))

                "fixed" ->
                  Decimal.sub(acc, value)

                _ ->
                  acc
              end
          end)
        ),
        original_total_price
      )

    total_price = Decimal.sub(original_total_price, total_manual_discount)
    selling_price = Decimal.div(total_price, cart_item["quantity"]) |> Decimal.round(2, :ceiling)
    total_price = Decimal.mult(selling_price, cart_item["quantity"])

    cart_item
    |> Map.put("discounts", items_info["discounts"] || [])
    |> Map.put("total_manual_discounts", total_manual_discount)
    |> Map.put("selling_price", selling_price)
    |> Map.put("price", total_price)
    |> Map.put("total_price", total_price)
  end

  defp calculate_cart_values(delivery_groups, cart, cart_info) do
    fulfillable_items =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.flat_map(fn group -> group["cart_items"] end)

    total_items_list_price =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.mult(Decimal.new(item["list_price"]), Decimal.new(item["quantity"]))
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_manual_discount =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.new(item["total_manual_discounts"] || "0")
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_price =
      fulfillable_items
      |> Stream.map(fn item -> item["total_price"] end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_delivery_price =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.reject(&(&1["fulfillment_type"] == "in-store"))
      |> Stream.map(fn group ->
        hd(group["cart_items"])["logistics_info"]["estimatedCost"]["amount"] || 0
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_manual_discount =
      Decimal.min(
        Decimal.sub(
          total_items_price,
          Enum.reduce(cart_info["discounts"] || [], total_items_price, fn
            %{"type" => type, "value" => value}, acc ->
              case type do
                "percentage" ->
                  Decimal.sub(acc, Decimal.mult(acc, Decimal.div(value, 100)))

                "fixed" ->
                  Decimal.sub(acc, value)

                _ ->
                  acc
              end
          end)
        ),
        total_items_price
      )

    total_ecommerce_discounts = cart["total_ecommerce_discounts"] || Decimal.new(0)

    total_items_selling_price =
      Decimal.sub(
        total_items_price,
        total_manual_discount
      )

    total_price =
      Decimal.add(
        total_delivery_price,
        total_items_selling_price
      )
      |> Decimal.sub(total_ecommerce_discounts)

    {%{
       "total_price" => total_price,
       "total_items_list_price" => total_items_list_price,
       "total_manual_discount" => total_manual_discount,
       "total_delivery_price" => total_delivery_price,
       "total_items_selling_price" => total_items_selling_price,
       "total_items_manual_discount" => total_items_manual_discount,
       "total_ecommerce_discounts" => total_ecommerce_discounts
     }
     |> Map.new(fn {k, v} -> {k, v |> Decimal.round(2)} end), []}
  end
end
