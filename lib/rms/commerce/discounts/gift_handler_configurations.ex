defmodule Rms.Commerce.Discounts.GiftHandlerConfigurations do
  use Ecto.Schema
  import Ecto.Changeset

  defmodule Gift do
    use Ecto.Schema
    import Ecto.Changeset

    embedded_schema do
      field :quantity, :integer
      field :cost, :decimal

      belongs_to :product_variant, Rms.Commerce.Products.ProductVariant
    end
  end

  schema "gift_handler_configurations" do
    embeds_many :gifts, Gift, on_replace: :delete

    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant
    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  def changeset(gift_handler_configuration, attrs) do
    gift_handler_configuration
    |> cast(attrs, [:product_variant_id, :organization_id])
    |> validate_required([:product_variant_id, :organization_id])
    |> foreign_key_constraint(:organization_id)
    |> foreign_key_constraint(:product_variant_id)
    |> cast_embed(:gifts, with: &gifts_changeset/2, required: true)
  end

  def gifts_changeset(gift, attrs) do
    gift
    |> cast(attrs, [:product_variant_id, :quantity, :cost])
    |> validate_required([:product_variant_id, :quantity, :cost])
    |> validate_number(:quantity, greater_than: 0)
    |> validate_number(:cost, greater_than: 0)
  end
end
