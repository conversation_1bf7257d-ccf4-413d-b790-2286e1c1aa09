defmodule Rms.Commerce.Discounts.LineItemDiscount do
  use Ecto.Schema
  import Ecto.Changeset

  @types ~w(percentage fixed)

  schema "line_item_discounts" do
    field :type, :string
    field :value, :string
    field :description, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :authorization_request, Rms.Commerce.Discounts.AuthorizationRequest
    belongs_to :line_item, Rms.Commerce.Orders.LineItem

    timestamps(type: :utc_datetime)
  end

  def changeset(discount, attrs) do
    discount
    |> cast(attrs, [
      :type,
      :value,
      :description
    ])
    |> validate_required([
      :type,
      :value
    ])
    |> validate_inclusion(:type, @types)
    |> foreign_key_constraint(:authorization_request_id)
    |> foreign_key_constraint(:line_item_id)
  end
end
