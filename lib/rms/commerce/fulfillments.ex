defmodule Rms.Commerce.Fulfillments do
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Fulfillments.ReverseFulfillment

  alias Rms.Repo

  import Ecto.Query

  @doc """
  Retrieves a list of fulfillments within an organization matching the search parameters.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the carts
    - `opts`: A keyword list of options. (optional)

  ## Options

  - `:status` - A string value to filter carts by status. Defaults to "active".
  - `:location_id` - The location id associated with the cart. No defaault value.
  - `:staff_id` - The staff id associated with the cart. No default value.
  - `:customer_id` - The customer id associated with the cart. No default value.

  ## Examples

  iex> Rms.Commerce.Fulfillments.list_fulfillments(1)
  [ ... ]

  iex> Rms.Commerce.Fulfillments.list_fulfillments(1, query_params: [customer_id: 3, status: "canceled"])
  [ ... ]

  """
  def list_fulfillments(organization_id, opts \\ []) do
    query_params =
      Keyword.get(opts, :query_params, %{})

    Fulfillment
    |> where([f], f.organization_id == ^organization_id)
    |> where(^add_fulfillment_filter_where(query_params))
    |> Repo.all()
  end

  defp add_fulfillment_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["shipping_method", :shipping_method] ->
        dynamic([f], ^dynamic and f.shipping_method == ^value)

      {key, value}, dynamic when key in ["status", :status] ->
        dynamic([f], ^dynamic and f.status == ^value)

      {key, value}, dynamic when key in ["ecommerce", :ecommerce] ->
        dynamic([f], ^dynamic and f.ecommerce == ^value)

      {key, value}, dynamic when key in ["external_reference", :external_reference] ->
        dynamic([f], ^dynamic and f.external_reference == ^value)

      {key, value}, dynamic when key in ["order_id", :order_id] ->
        dynamic([f], ^dynamic and f.order_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  @doc """
  Retrieves a fulfillment by its External Reference (Order) ID.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the fulfillment.
    - `reference_id`: The ID of MarketPlaceOrder of the fulfillment that owns.

  ## Examples

      iex> Rms.Commerce.Fulfillments.get_fulfillment!(2, "1233443-34443")
      %Rms.Commerce.Fulfillments.Fulfillment{organization_id: 2, external_id: "1233443-34443" ...}

  ## Raises

  `Ecto.NoResultsError` if the fulfillment with the given ID does not exist.

  """

  def unsafe_get_fulfillment_by_external_reference!(
        external_reference,
        preloads \\ []
      ) do
    Fulfillment
    |> where(external_reference: ^external_reference)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Retrieves a fulfillment by its ID.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the fulfillment.
    - `fulfillment_id`: The ID of the fulfillment to retrieve.

  ## Examples

      iex> Rms.Commerce.Fulfillments.get_fulfillment!(2, 1)
      %Rms.Commerce.Fulfillments.Fulfillment{id: 1, organization_id: 2, ...}

  ## Raises

  `Ecto.NoResultsError` if the fulfillment with the given ID does not exist.

  """
  def get_fulfillment!(organization_id, fulfillment_id, preloads \\ []) do
    Fulfillment
    |> where(organization_id: ^organization_id, id: ^fulfillment_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Retrieves a fulfillment by its ID without checking the organization ID.

  ## Parameters

    - `fulfillment_id`: The ID of the fulfillment to retrieve.
    - `preloads`: A list of associations to preload. (optional)

  ## Examples

      iex> Rms.Commerce.Fulfillments.unsafe_get_fulfillment!(1)
      %Rms.Commerce.Fulfillments.Fulfillment{id: 1, ...}

  ## Raises

  `Ecto.NoResultsError` if the fulfillment with the given ID does not exist.

  """
  def unsafe_get_fulfillment!(fulfillment_id, preloads \\ []) do
    Fulfillment
    |> where(id: ^fulfillment_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Deletes a fulfillment and associated cart items.

  ## Parameters

    - `fulfillment`: The fulfillment to delete.

  ## Examples

      iex> Rms.Commerce.Fulfillments.delete_fulfillment(%Rms.Commerce.Fulfillments.Fulfillment{})
      :ok

  ## Returns

  `:ok` if the fulfillment was successfully deleted, or
  `{:error, changeset}` if there was an error.

  """
  def delete_fulfillment(fulfillment) do
    Repo.delete(fulfillment)
  end

  @doc """
  Updates an existing fulfillment with the provided attributes.

  ## Parameters

    - `fulfillment`: The fulfillment to update.
    - `attrs`: A map containing the attributes to update.

  ## Examples

      iex> Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{price: 15.00})
      {:ok, %Rms.Commerce.Fulfillments.Fullfillment{}}

  ## Returns

  `{:ok, fulfillment}` if the fulfillment was successfully updated, or
  `{:error, changeset}` if there was an error.

  """
  def update_fulfillment(fulfillment, attrs) do
    fulfillment
    |> Fulfillment.update_changeset(attrs)
    |> Repo.update()
  end

  @spec set_completed(Fulfillment.t(), map) ::
          {:ok, Fulfillment.t()} | {:error, Ecto.Changeset.t()}
  def set_completed(fulfillment, attrs) do
    attrs = Map.put(attrs, :status, "completed")
    update_fulfillment(fulfillment, attrs)
  end

  @doc """
  Creates a reverse fulfillment for returned items.
  """
  def create_reverse_fulfillment(
        organization_id,
        returned_line_items,
        location_id,
        staff_id,
        customer_id
      ) do
    attrs =
      %{
        organization_id: organization_id,
        line_items: returned_line_items,
        location_id: location_id,
        staff_id: staff_id,
        customer_id: customer_id
      }

    Repo.transaction_with(fn ->
      with {:ok, reverse_fulfillment} <-
             %ReverseFulfillment{organization_id: organization_id}
             |> ReverseFulfillment.changeset(attrs)
             |> Repo.insert(),
           preloaded_fulfillment <- Repo.preload(reverse_fulfillment, :line_items),
           reverse_fulfillment_payload <-
             ReverseFulfillment.reverse_fulfillment_payload(preloaded_fulfillment),
           {:ok, _} <- Rms.Events.emit("reverse_fulfillment.created", reverse_fulfillment_payload) do
        {:ok, preloaded_fulfillment}
      end
    end)
  end

  @doc """
  Updates the status of a reverse fulfillment.
  """
  def update_reverse_fulfillment_status(%ReverseFulfillment{} = fulfillment, status) do
    fulfillment
    |> ReverseFulfillment.update_changeset(%{status: status})
    |> Repo.update()
  end

  def get_reverse_fulfillment_by_id!(organization_id, reverse_fulfillment_id, opts \\ []) do
    preload = Keyword.get(opts, :preload, [])

    ReverseFulfillment
    |> where(organization_id: ^organization_id)
    |> preload(^preload)
    |> Repo.get!(reverse_fulfillment_id)
  end

  @doc """
  Retrieves line items with their SKUs from a reverse fulfillment.
  This is useful for matching with Shopify return line items for dispose operations.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the reverse fulfillment
    - `reverse_fulfillment_id`: The ID of the reverse fulfillment to retrieve line items from

  ## Examples

      iex> Rms.Commerce.Fulfillments.get_reverse_fulfillment_line_items_with_skus(1, "abc-123")
      [
        %{id: 1, sku: "SKU123", returned_quantity: 2, line_item_id: 123},
        %{id: 2, sku: "SKU456", returned_quantity: 1, line_item_id: 456}
      ]

  ## Returns

  A list of maps containing line item information with SKUs.
  """
  def get_reverse_fulfillment_line_items_with_skus(organization_id, reverse_fulfillment_id) do
    query =
      from rfl in Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem,
        join: li in Rms.Commerce.Orders.LineItem,
        on: rfl.line_item_id == li.id,
        where:
          rfl.organization_id == ^organization_id and
            rfl.reverse_fulfillment_id == ^reverse_fulfillment_id and
            rfl.returned_quantity > 0,
        select: %{
          id: rfl.id,
          sku: li.sku,
          returned_quantity: rfl.returned_quantity,
          line_item_id: rfl.line_item_id,
          return_to_inventory: rfl.return_to_inventory
        }

    Repo.all(query)
  end

  @doc """
  Completes a reverse fulfillment and emits a completion event.

  ## Parameters

    - `reverse_fulfillment`: The reverse fulfillment to complete

  ## Examples

      iex> Rms.Commerce.Fulfillments.complete_reverse_fulfillment(reverse_fulfillment)
      {:ok, %ReverseFulfillment{}}

  ## Returns

  `{:ok, reverse_fulfillment}` if successfully completed
  `{:error, changeset}` if there was an error updating the status
  """
  def complete_reverse_fulfillment(%ReverseFulfillment{} = reverse_fulfillment) do
    Repo.transaction_with(fn ->
      with {:ok, completed_fulfillment} <-
             update_reverse_fulfillment_status(reverse_fulfillment, "completed"),
           reverse_fulfillment_payload =
             ReverseFulfillment.reverse_fulfillment_payload(completed_fulfillment),
           {:ok, _} <-
             Rms.Events.emit("reverse_fulfillment.completed", reverse_fulfillment_payload) do
        {:ok, completed_fulfillment}
      end
    end)
  end

  @doc """
  Cancels a reverse fulfillment and all its associated records.

  ## Parameters

    - `reverse_fulfillment`: The reverse fulfillment to cancel

  ## Examples

      iex> Rms.Commerce.Fulfillments.cancel_reverse_fulfillment(reverse_fulfillment)
      {:ok, %ReverseFulfillment{}}

  ## Returns

  `{:ok, reverse_fulfillment}` if successfully cancelled
  `{:error, changeset}` if there was an error updating the records
  """
  def cancel_reverse_fulfillment(%ReverseFulfillment{} = reverse_fulfillment) do
    reverse_fulfillment =
      Repo.preload(reverse_fulfillment, [:line_items, iglu_credits: [:payments]])

    Repo.transaction_with(fn ->
      with {:ok, canceled_fulfillment} <-
             reverse_fulfillment
             |> ReverseFulfillment.cancel_changeset()
             |> Repo.update(),
           reverse_fulfillment_payload =
             ReverseFulfillment.reverse_fulfillment_payload(canceled_fulfillment),
           {:ok, _} <-
             Rms.Events.emit("reverse_fulfillment.cancelled", reverse_fulfillment_payload) do
        {:ok, canceled_fulfillment}
      end
    end)
  end
end
