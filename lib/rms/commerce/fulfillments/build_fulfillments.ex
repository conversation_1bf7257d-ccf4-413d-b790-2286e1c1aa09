defmodule Rms.Commerce.Fulfillments.BuildFulfillments do
  def execute(order, organization_id) do
    case Rms.Settings.list_settings(organization_id, organization_id, [
           "organization_ecommerce"
         ]) do
      [%{type: "organization_setting", value: ecommerce, key: "ecommerce"}] ->
        build_fulfillments(order, ecommerce)

      _ ->
        build_default_fulfillments(order)
    end
  end

  defp build_fulfillments(order, "vtex") do
    Rms.Integrations.VTEX.Fulfillments.BuildFulfillments.execute(order)
  end

  defp build_fulfillments(order, "shopify") do
    Rms.Integrations.Shopify.Fulfillments.BuildFulfillments.execute(order)
  end

  defp build_fulfillments(_order, ecommerce) do
    {:error,
     %{
       reason: "Ecommerce #{ecommerce} not suported in fulfillments creation",
       stacktrace: "#{__MODULE__}"
     }}
  end

  defp build_default_fulfillments(order) do
    fulfillments =
      order.line_items
      |> Enum.group_by(& &1.shipping_method)
      |> Enum.reduce_while([], fn {shipping_method, line_items}, acc ->
        case build_fulfillment(shipping_method, line_items, order) do
          {:ok, fulfillment} -> {:cont, [fulfillment | acc]}
          {:error, reason} -> {:halt, {:error, reason}}
        end
      end)

    case fulfillments do
      {:error, reason} ->
        {:error, reason}

      fulfillments ->
        {:ok, order |> Map.put(:fulfillments, fulfillments) |> Map.drop([:line_items])}
    end
  end

  defp build_fulfillment(shipping_method, line_items, _order)
       when shipping_method in ["in-store", "local-pickup", "delivery"] do
    {:ok,
     %{
       ecommerce: "vtex",
       shipping_method: shipping_method,
       shipping_settings: nil,
       line_items: line_items
     }}
  end

  defp build_fulfillment(_, _, _) do
    {:error, "shipping method not suported in fullfilments creation"}
  end
end
