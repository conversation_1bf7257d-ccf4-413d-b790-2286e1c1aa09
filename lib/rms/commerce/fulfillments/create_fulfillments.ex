defmodule Rms.Commerce.Fulfillments.CreateFulfillments do
  def execute(order_id, organization_id) do
    order =
      Rms.Commerce.Orders.get_order!(organization_id, order_id)
      |> Rms.Repo.preload(fulfillments: [:line_items])

    case Rms.Settings.list_settings(organization_id, organization_id, [
           "organization_ecommerce"
         ]) do
      [%{type: "organization_setting", value: ecommerce, key: "ecommerce"}] ->
        create_fulfillments(order, ecommerce)

      _ ->
        {:error, %{reason: "No organization ecommerce found", stacktrace: "#{__MODULE__}"}}
    end
  end

  defp create_fulfillments(_order, "vtex") do
    :ok
  end

  defp create_fulfillments(order, "shopify") do
    Rms.Integrations.Shopify.Fulfillments.CreateFulfillments.execute(order)
  end

  defp create_fulfillments(_order, ecommerce) do
    {:error,
     %{
       reason: "Ecommerce #{ecommerce} not suported in fulfillments creation",
       stacktrace: "#{__MODULE__}"
     }}
  end
end
