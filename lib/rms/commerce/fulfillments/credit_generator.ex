defmodule Rms.Commerce.Fulfillments.CreditGenerator do
  @moduledoc """
  Handles credit generation for reverse fulfillments.
  """

  alias Rms.Finance
  alias Rms.Commerce.Fulfillments
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Repo

  @doc """
  Generates credit for a reverse fulfillment.
  """
  def generate_credit(%ReverseFulfillment{} = fulfillment, customer_id) do
    fulfillment = Repo.preload(fulfillment, line_items: [:line_item])
    total_credit = calculate_credit_amount(fulfillment)

    # Start a transaction to ensure both credit creation and status update succeed or fail together
    Repo.transaction_with(fn ->
      credit_attrs = %{
        amount: total_credit,
        used_amount: Decimal.new(0),
        reason: "return",
        customer_id: customer_id,
        reverse_fulfillment_id: fulfillment.id,
        metadata: %{
          reference: %{
            type: "reverse_fulfillment",
            id: fulfillment.id
          },
          items: format_items(fulfillment.line_items)
        }
      }

      with {:ok, fulfillment} <-
             Fulfillments.update_reverse_fulfillment_status(fulfillment, "processing"),
           {:ok, credit} <- Finance.create_iglu_credit(fulfillment.organization_id, credit_attrs) do
        {:ok, [credit, fulfillment]}
      end
    end)
  end

  defp calculate_credit_amount(%ReverseFulfillment{line_items: line_items}) do
    line_items
    |> Enum.reduce(Decimal.new(0), fn item, acc ->
      item.line_item.price
      |> Decimal.mult(item.returned_quantity)
      |> Decimal.add(acc)
    end)
  end

  def format_items(line_items) do
    Enum.map(line_items, fn item ->
      %{
        line_item_id: item.line_item.id,
        returned_quantity: item.returned_quantity,
        reason: item.reason,
        price: item.line_item.price
      }
    end)
  end
end
