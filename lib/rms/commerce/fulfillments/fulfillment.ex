defmodule Rms.Commerce.Fulfillments.Fulfillment do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Commerce.Orders.Order

  alias Rms.Commerce.Fulfillments.ShippingSetting
  alias Rms.Commerce.Orders.LineItem

  schema "fulfillments" do
    field :shipping_method, :string
    field :external_reference, :string
    field :ecommerce, :string
    field :status, :string, default: "pending"
    field :metadata, :map

    belongs_to :organization, Organization
    belongs_to :order, Order

    has_one :shipping_settings, ShippingSetting, defaults: {Rms.Repo, :add_organization_id, []}

    has_many :fiscal_invoices, Rms.Fiscal.FiscalInvoice

    has_many :line_items, LineItem, defaults: {Rms.Repo, :add_organization_id, []}

    field :reference_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(fulfillment, attrs, opts) do
    fulfillment
    |> cast(attrs, [
      :ecommerce,
      :status,
      :external_reference,
      :shipping_method,
      :metadata,
      :reference_at
    ])
    |> validate_required([
      :shipping_method
    ])
    |> cast_assoc(:line_items, with: &Rms.Commerce.Orders.LineItem.changeset(&1, &2, opts))
    |> validate_inclusion(:shipping_method, ~w(in-store local-pickup delivery))
    |> cast_assoc(:shipping_settings)
    |> foreign_key_constraint(:order_id)
    |> foreign_key_constraint(:organization_id)
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
  end

  @doc false
  def update_changeset(fulfillment, attrs) do
    cast(fulfillment, attrs, [:status, :external_reference, :metadata])
  end
end
