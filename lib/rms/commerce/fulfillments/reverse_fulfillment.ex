defmodule Rms.Commerce.Fulfillments.ReverseFulfillment do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Finance.IgluCredit
  alias Rms.Accounts.Location
  alias Rms.Accounts.Organization
  alias Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem
  alias Rms.Customers.Customer

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          status: String.t(),
          metadata: map() | nil,
          organization_id: pos_integer(),
          organization: Organization.t() | Ecto.Association.NotLoaded.t(),
          line_items: [LineItem.t()] | Ecto.Association.NotLoaded.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @allowed_transitions %{
    "pending" => ["pending", "processing", "completed", "cancelled"],
    "processing" => ["processing", "completed", "cancelled"],
    "completed" => ["completed"],
    "cancelled" => []
  }

  @primary_key {:id, :binary_id, autogenerate: true}
  schema "reverse_fulfillments" do
    field :status, :string, default: "pending"
    field :metadata, :map
    field :version, :integer, default: 1

    belongs_to :organization, Organization

    belongs_to :customer, Customer
    belongs_to :location, Location
    belongs_to :staff, Rms.Accounts.Staff

    has_one :fiscal_invoice, Rms.Fiscal.FiscalInvoice
    has_many :line_items, LineItem, defaults: {Rms.Repo, :add_organization_id, []}
    has_many :iglu_credits, IgluCredit, defaults: {Rms.Repo, :add_organization_id, []}

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for a reverse fulfillment.
  """
  def changeset(reverse_fulfillment, attrs) do
    reverse_fulfillment
    |> cast(attrs, [:status, :metadata, :location_id, :staff_id, :customer_id])
    |> cast_assoc(:line_items, required: true)
    |> validate_required([:organization_id, :location_id, :customer_id])
    |> validate_inclusion(:status, ~w(pending processing completed cancelled))
    |> assoc_constraint(:customer)
    |> assoc_constraint(:staff)
    |> assoc_constraint(:organization)
    |> assoc_constraint(:location)
    |> optimistic_lock(:version)
  end

  @doc """
  Updates a changeset for a reverse fulfillment.
  Only allows updating status and metadata.
  """
  def update_changeset(reverse_fulfillment, attrs) do
    reverse_fulfillment
    |> cast(attrs, [:status, :metadata])
    |> validate_inclusion(:status, ~w(pending processing completed cancelled))
    |> validate_state_transition()
    |> optimistic_lock(:version)
  end

  @doc """
  Creates a changeset for cancelling a reverse fulfillment.
  Validates that the current status allows cancellation and propagates cancellation to associated records.
  """
  def cancel_changeset(reverse_fulfillment, attrs \\ %{}) do
    reverse_fulfillment
    |> cast(attrs, [:status])
    |> put_change(:status, "cancelled")
    |> validate_state_transition()
    |> optimistic_lock(:version)
    |> cancel_line_items()
    |> cancel_iglu_credits()
  end

  defp validate_state_transition(changeset) do
    current_status = changeset.data.status
    validate_inclusion(changeset, :status, @allowed_transitions[current_status])
  end

  defp cancel_line_items(changeset) do
    line_items = Enum.map(changeset.data.line_items, &LineItem.cancel_changeset(&1))
    put_assoc(changeset, :line_items, line_items)
  end

  defp cancel_iglu_credits(changeset) do
    iglu_credits = Enum.map(changeset.data.iglu_credits, &IgluCredit.cancel_changeset(&1))
    put_assoc(changeset, :iglu_credits, iglu_credits)
  end

  @doc """
  Converts a ReverseFulfillment struct into a map suitable for JSON encoding.
  """
  def reverse_fulfillment_payload(%__MODULE__{} = reverse_fulfillment) do
    reverse_fulfillment = Rms.Repo.preload(reverse_fulfillment, [:line_items, :organization])

    %{
      id: reverse_fulfillment.id,
      status: reverse_fulfillment.status,
      metadata: reverse_fulfillment.metadata,
      location_id: reverse_fulfillment.location_id,
      customer_id: reverse_fulfillment.customer_id,
      staff_id: reverse_fulfillment.staff_id,
      organization_id: reverse_fulfillment.organization_id,
      organization: organization_payload(reverse_fulfillment.organization),
      line_items: Enum.map(reverse_fulfillment.line_items, &line_item_payload/1),
      inserted_at: reverse_fulfillment.inserted_at,
      updated_at: reverse_fulfillment.updated_at
    }
  end

  defp organization_payload(%Rms.Accounts.Organization{} = organization) do
    %{
      id: organization.id,
      name: organization.name,
      cnpj: organization.cnpj,
      org_id: organization.org_id
    }
  end

  defp organization_payload(_), do: nil

  defp line_item_payload(%LineItem{} = line_item) do
    %{
      id: line_item.id,
      returned_quantity: line_item.returned_quantity,
      reason: line_item.reason,
      metadata: line_item.metadata,
      return_to_inventory: line_item.return_to_inventory,
      line_item_id: line_item.line_item_id,
      organization_id: line_item.organization_id
    }
  end

  defp line_item_payload(_), do: nil
end
