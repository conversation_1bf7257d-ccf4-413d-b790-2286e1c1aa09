defmodule Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Commerce.Orders.LineItem
  alias Rms.Commerce.Fulfillments.ReverseFulfillment

  @type t :: %__MODULE__{}

  schema "reverse_fulfillment_line_items" do
    field :returned_quantity, :integer
    field :reason, :string
    field :metadata, :map
    field :return_to_inventory, :boolean, default: false

    belongs_to :organization, Organization
    belongs_to :line_item, LineItem
    belongs_to :reverse_fulfillment, ReverseFulfillment, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for a reverse fulfillment line item.
  """
  def changeset(line_item, attrs) do
    line_item
    |> cast(attrs, [
      :returned_quantity,
      :reason,
      :metadata,
      :line_item_id,
      :return_to_inventory
    ])
    |> validate_required([
      :returned_quantity,
      :organization_id,
      :line_item_id
    ])
    |> validate_number(:returned_quantity, greater_than: 0)
    |> assoc_constraint(:organization)
    |> assoc_constraint(:line_item)
    |> check_constraint(:returned_quantity,
      name: :check_available_quantity,
      message: "must be less than or equal to available quantity"
    )
  end

  @doc """
  Creates a changeset for cancelling a reverse fulfillment line item.
  Sets returned_quantity to 0 and stores the original quantity in metadata.
  """
  def cancel_changeset(line_item) do
    original_quantity = line_item.returned_quantity

    metadata =
      Map.merge(line_item.metadata || %{}, %{
        original_returned_quantity: original_quantity,
        cancelled_at: DateTime.utc_now()
      })

    attrs = %{returned_quantity: 0, metadata: metadata}

    cast(line_item, attrs, [:returned_quantity, :metadata])
  end
end
