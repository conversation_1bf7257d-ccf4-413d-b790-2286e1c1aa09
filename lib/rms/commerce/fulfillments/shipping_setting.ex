defmodule Rms.Commerce.Fulfillments.ShippingSetting do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization

  alias Rms.Commerce.Fulfillments.Fulfillment

  schema "shipping_settings" do
    field :price, :decimal
    field :time_period, :string
    field :ecommerce, :string

    field :settings, :map

    belongs_to :fulfillment, Fulfillment
    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(serie, attrs) do
    serie
    |> cast(attrs, [
      :price,
      :time_period,
      :ecommerce,
      :settings
    ])
    |> foreign_key_constraint(:fulfillment_id)
    |> foreign_key_constraint(:organization_id)
  end
end
