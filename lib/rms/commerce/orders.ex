defmodule Rms.Commerce.Orders do
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Orders.Order
  alias Rms.Commerce.Orders.LineItem
  alias Rms.Repo
  alias Rms.Integrations
  alias Rms.Settings
  require Logger

  alias Rms.Commerce.Fulfillments.BuildFulfillments

  import Ecto.Query

  @ecommerce_setting_key "ecommerce"

  @doc """
  Fetches external orders from the configured e-commerce platform,
  filters out orders sourced from 'iglu' that already exist internally,
  and annotates the remaining orders with their internal ID if available.

  Handles pagination via :after, :before, and :limit opts.
  """
  def get_external_orders(organization_id, opts \\ []) do
    with {:ok, ecommerce_data} <- fetch_ecommerce_setting_data(organization_id),
         {:ok, platform_data} <- fetch_orders_from_platform(organization_id, ecommerce_data, opts) do
      external_orders_raw = platform_data.orders
      platform_metadata = Map.drop(platform_data, [:orders])

      external_ids = Enum.map(external_orders_raw, & &1["id"])

      internal_orders_map =
        find_internal_orders_by_external_ids(organization_id, external_ids)

      filtered_external_orders =
        Enum.filter(external_orders_raw, fn ext_ord ->
          external_id = ext_ord["id"]

          internal_info =
            Map.get(internal_orders_map, external_id)

          is_nil(internal_info) or internal_info.source != "iglu"
        end)

      final_annotated_orders =
        Enum.map(filtered_external_orders, fn ext_ord ->
          external_id = ext_ord["id"]
          internal_info = Map.get(internal_orders_map, external_id)
          internal_id = if internal_info, do: internal_info.id, else: nil

          ext_ord
          |> Map.put("internal_order_id", internal_id)
          |> Map.put("external_source", ecommerce_data)
        end)

      final_metadata =
        %{
          after: Keyword.get(opts, :after, nil),
          before: Keyword.get(opts, :before, nil),
          first: Keyword.get(opts, :limit, 10),
          count: length(final_annotated_orders)
        }
        |> Map.merge(platform_metadata)

      {:ok, %{orders: final_annotated_orders, metadata: final_metadata}}
    end
  end

  defp fetch_ecommerce_setting_data(organization_id) do
    case Settings.get_organization_setting(organization_id, @ecommerce_setting_key) do
      %Settings.OrganizationSetting{value: %{"data" => data}} when not is_nil(data) ->
        {:ok, data}

      %Settings.OrganizationSetting{value: value} ->
        Logger.warning(
          "e-commerce integration setting found but malformed #{inspect(value)}",
          organization_id: organization_id,
          ecommerce_setting_key: @ecommerce_setting_key
        )

        {:error, :setting_malformed}

      nil ->
        Logger.warning(
          "e-commerce integration setting not found",
          organization_id: organization_id,
          ecommerce_setting_key: @ecommerce_setting_key
        )

        {:error, :setting_not_found}
    end
  end

  defp fetch_orders_from_platform(organization_id, "shopify", opts) do
    creds = Integrations.get_shopify_credential!(organization_id)
    shopify_client = Integrations.Shopify.client(creds.shop, creds.credential)
    fetch_shopify_orders(shopify_client, opts)
  end

  defp fetch_orders_from_platform(_organization_id, platform, _opts) when is_binary(platform) do
    Logger.warning("Unsupported e-commerce platform: #{platform}")
    {:error, :unsupported_platform}
  end

  defp fetch_orders_from_platform(_organization_id, invalid_data, _opts) do
    Logger.warning("Invalid e-commerce platform data structure: #{inspect(invalid_data)}")
    {:error, :invalid_platform_data}
  end

  defp fetch_shopify_orders(shopify_client, opts) do
    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)
    limit = Keyword.get(opts, :limit, 10)

    filters =
      opts
      |> Keyword.take([:search, :from, :until])
      |> Enum.map(fn
        {:from, value} when not is_nil(value) -> "created_at:>#{value}"
        {:until, value} -> "created_at:<#{value || "now"}"
        {:search, value} when not is_nil(value) and value != "" -> value
        {:search, _} -> nil
      end)
      |> Enum.reject(&is_nil/1)

    all_filters = ["fulfillment_status:fulfilled" | filters]

    query_string = Enum.join(all_filters, " AND ")

    query_params = [
      after: cursor_after,
      before: cursor_before,
      first: limit,
      query: query_string,
      reverse: true
    ]

    Integrations.Shopify.find_orders(shopify_client, query_params)
  end

  defp find_internal_orders_by_external_ids(organization_id, external_ids) do
    direct_matches_query =
      from o in Order,
        where: o.organization_id == ^organization_id and o.external_id in ^external_ids,
        select: {o.external_id, %{id: o.id, source: o.source}}

    fulfillment_matches_query =
      from f in Rms.Commerce.Fulfillments.Fulfillment,
        join: o in assoc(f, :order),
        where: f.organization_id == ^organization_id and f.external_reference in ^external_ids,
        select: {f.external_reference, %{id: o.id, source: o.source}}

    direct_matches = Repo.all(direct_matches_query) |> Map.new()
    fulfillment_matches = Repo.all(fulfillment_matches_query) |> Map.new()

    Map.merge(fulfillment_matches, direct_matches)
  end

  @doc """
  Paginates the list of orders for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the orders.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:after` - A cursor value for fetching results after this cursor.
    - `:before` - A cursor value for fetching results before this cursor.
    - `:limit` - The maximum number of orders to return. Defaults to 20.
    - `:preload` - A list of associations to preload.

  ## Examples

      iex> Rms.Commerce.paginated_orders(1, limit: 10, after: "cursor_value")
      %{entries: [%Rms.Commerce.Orders.Order{}, ...], metadata: %{}}

  """
  def paginated_orders(organization_id, opts \\ []) do
    query = build_orders_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)
    total_count = Keyword.has_key?(opts, :count)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    preloads = Keyword.get(opts, :preloads, [])

    query =
      query
      |> preload(^preloads)
      |> order_by(desc: :reference_at, desc: :id)

    paginate_opts =
      [
        after: cursor_after,
        before: cursor_before,
        limit: limit,
        include_total_count: total_count,
        cursor_fields: [{:reference_at, :desc}, {:id, :desc}]
      ]

    Repo.paginate(
      query,
      paginate_opts
    )
  end

  defp build_orders_query(organization_id, opts) do
    {:ok, inserted_at} = parse_datetime(Keyword.get(opts, :inserted_at, nil))
    {:ok, reference_at} = parse_datetime(Keyword.get(opts, :reference_at, nil))
    {search, opts} = Keyword.pop(opts, :search)

    organization_id
    |> build_base_query()
    |> join_transaction_and_customer()
    |> maybe_filter_by_reference_at(reference_at)
    |> maybe_filter_by_inserted_at(inserted_at)
    |> maybe_join_payments(opts)
    |> apply_search_filter(search)
    |> apply_filters(opts)
  end

  defp build_base_query(organization_id) do
    from(o in Order, where: o.organization_id == ^organization_id)
  end

  defp join_transaction_and_customer(query) do
    query
    |> join(:left, [o], t in assoc(o, :transaction))
    |> join(:left, [o], c in assoc(o, :order_customer))
  end

  defp maybe_join_payments(query, opts) do
    all? = Keyword.get(opts, :all?, false)
    status = Keyword.get(opts, :status)

    cond do
      all? ->
        query
        |> join(:left, [_o, t], p in assoc(t, :payments))
        |> distinct(true)

      status == "open" ->
        query
        |> join(:left, [_o, t], p in assoc(t, :payments))
        |> distinct(true)

      # fallback case
      true ->
        query
        |> join(:inner, [_o, t], p in assoc(t, :payments))
        |> distinct(true)
    end
  end

  defp apply_search_filter(query, nil), do: query

  defp apply_search_filter(query, search) do
    query
    |> where([o], like(type(o.id, :string), ^"%#{search}%"))
    |> or_where([o], not is_nil(o.name) and like(o.name, ^"%#{search}%"))
    |> or_where(
      [_o, _t, c],
      not is_nil(c.plain_name) and
        fragment("unaccent(?) ILIKE unaccent(?)", c.plain_name, ^"%#{search}%")
    )
    |> or_where(
      [_o, _t, c],
      (not is_nil(c.document_hash) and c.document_hash == ^search) or
        (not is_nil(c.email_hash) and c.email_hash == ^search) or
        (not is_nil(c.primary_phone_number_hash) and c.primary_phone_number_hash == ^search)
    )
  end

  defp apply_filters(query, opts) do
    Enum.reduce(opts, query, fn
      {key, value}, query when key in ~w(status location_id staff_id cashier_id source)a ->
        where(query, [o], field(o, ^key) == ^value)

      {:payment_method, payment_method}, query ->
        where(query, [_o, _t, _c, p], p.method == ^payment_method)

      _, query ->
        query
    end)
  end

  @doc """
  Retrieves an order by its ID, raising an exception if not found.

  ## Parameters

    - `order_id`: The ID of the order to retrieve.
    - `preload`: A list of associations to preload. Defaults to an empty list.

  ## Examples

      iex> Rms.Commerce.Orders.get_order!(1)
      %Rms.Commerce.Orders.Order{id: 1, ...}

      iex> Rms.Commerce.Orders.get_order!(1, [:transaction])
      %Rms.Finance.Transaction{}, ...}

  ## Raises

  `Ecto.NoResultsError` if the order with the given ID does not exist.

  """
  def get_order!(organization_id, order_id, preload \\ []) do
    Order
    |> preload(^preload)
    |> Repo.get_by!(id: order_id, organization_id: organization_id)
  end

  def unsafe_get_order!(order_id, preload \\ []) do
    Order
    |> preload(^preload)
    |> Repo.get!(order_id)
  end

  def get_line_items!(organization_id, line_item_ids) do
    LineItem
    |> where([li], li.organization_id == ^organization_id and li.id in ^line_item_ids)
    |> Repo.all()
  end

  def update_line_item(line_item, attrs) do
    line_item
    |> LineItem.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Creates a new order for the given organization with the specified attributes.

  ## Parameters

    - `organization_id`: The ID of the organization under which the order should be created.
    - `attrs`: A map containing the attributes for the new order.

  ## Examples

      iex> Rms.Commerce.Orders.create_order(1, %{total_price: 100.00, status: "open"})
      {:ok, %Rms.Commerce.Orders.Order{}}

  ## Returns

  `{:ok, order}` if the order was successfully created, or
  `{:error, changeset}` if there was an error.

  """
  def create_order(organization_id, attrs, opts \\ []) do
    {transaction_customer, opts} = Keyword.pop(opts, :transaction_customer)
    {create_transaction, opts} = Keyword.pop(opts, :create_transaction, false)

    order_attrs = calculate_discounts(attrs)

    with {:ok, order_attrs} <- BuildFulfillments.execute(order_attrs, organization_id) do
      Ecto.Multi.new()
      |> Ecto.Multi.insert(
        :order,
        Order.changeset(%Order{organization_id: organization_id}, order_attrs, opts)
      )
      |> maybe_create_transaction(transaction_customer, create_transaction)
      |> Ecto.Multi.run(:emit_event, fn _repo, %{order: order} ->
        Rms.Events.emit("order.created", Order.event_payload(order))
      end)
      |> Repo.transaction()
      |> create_order_normalizer()
    end
  end

  defp maybe_create_transaction(multi, _, false), do: multi

  defp maybe_create_transaction(multi, transaction_customer, true) do
    Ecto.Multi.run(multi, :transaction, fn _repo, %{order: order} ->
      order = Repo.preload(order, [:transaction])
      ensure_transaction(order, transaction_customer)
    end)
  end

  defp create_order_normalizer(result) do
    case result do
      {:ok, %{order: order}} ->
        {:ok, order}

      {:error, _, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  @doc """
  Creates an order and applies credit as payment.

  ## Parameters

    - `organization_id`: The ID of the organization
    - `attrs`: A map of attributes for the order
    - `credit`: The credit to apply to the order
    - `opts`: A keyword list of options
      - `:allowed_location_ids` - A list of location IDs that the user is allowed to access
      - `:transaction_customer` - Optional map with transaction customer data

  ## Examples

      iex> credit = %IgluCredit{amount: 100.00, status: "available"}
      iex> Rms.Commerce.Orders.create_order_with_credit(1, %{total_price: 150.00}, credit)
      {:ok, %Order{}}

      iex> attrs = %{organization_id: 1, prefix: "ORD-", suffix: "-2024", current_order_number: 0}
      iex> Rms.Commerce.Orders.create_order_with_credit(1, %{total_price: 150.00}, credit)
      {:error, %Ecto.Changeset{}}

  ## Returns

    Returns `{:ok, order}` if successful, `{:error, reason}` otherwise
  """
  def create_order_with_credit(organization_id, attrs, credit, opts \\ []) do
    {transaction_customer, opts} = Keyword.pop(opts, :transaction_customer)

    Repo.transaction_with(fn ->
      with {:ok, order} <-
             create_order(organization_id, attrs, opts),
           order = Repo.preload(order, [:transaction]),
           {:ok, transaction} <- ensure_transaction(order, transaction_customer),
           payment_value <- get_payment_amount(order, credit),
           payment_attrs =
             %{
               amount: payment_value,
               method: "return_credit",
               status: "settled",
               transaction_id: transaction.id,
               metadata: %{
                 credit_id: credit.id,
                 reason: credit.reason,
                 reference:
                   get_in(credit.metadata, ["reference"]) || get_in(credit.metadata, [:reference])
               }
             },
           {:ok, _payment} <-
             Rms.Finance.create_iglu_credit_payment(organization_id, credit, payment_attrs),
           {:ok, new_credit} <-
             Rms.Finance.update_iglu_credit_used_amount(credit, payment_value),
           {:ok, _credit} <- Rms.Finance.update_iglu_credit_status(new_credit, "used") do
        {:ok, Repo.reload!(order)}
      end
    end)
  end

  defp ensure_transaction(%{transaction: nil} = order, nil) do
    transaction_attrs = %{order_id: order.id}

    Rms.Finance.create_transaction(order.organization_id, transaction_attrs)
  end

  defp ensure_transaction(%{transaction: nil} = order, transaction_customer) do
    transaction_attrs = %{order_id: order.id, customer: transaction_customer}

    Rms.Finance.create_transaction(order.organization_id, transaction_attrs)
  end

  defp ensure_transaction(%{transaction: transaction}, _transaction_customer),
    do: {:ok, transaction}

  defp get_payment_amount(order, credit) do
    Decimal.min(order.total_price, credit.amount)
  end

  @doc """
  Updates an existing order with the provided name.

  ## Parameters

    - `order`: The order to update.
    - `name`: A string representing the name

  ## Examples

      iex> Rms.Commerce.Orders.update_order_name(order, "EXT_123")
      {:ok, %Rms.Commerce.Orders.Order{id: 1, name: "EXT_123", ...}}

      iex> Rms.Commerce.Orders.update_order_name(order, "invalid name")
      {:error, %Ecto.Changeset{}}

  ## Returns

  `{:ok, order}` if the order was successfully updated, or
  `{:error, changeset}` if there was an error.
  """
  def update_order_name(order, name) do
    order
    |> Order.update_name_changeset(name)
    |> Repo.update()
  end

  def format_cart(cart, order_params) do
    cart
    |> Map.take([
      :customer_id,
      :staff_id
    ])
    |> Map.merge(%{
      total_items_list_price: Decimal.new(cart.total_items_list_price),
      total_items_selling_price: Decimal.new(cart.total_items_selling_price),
      total_price: Decimal.new(cart.total_price),
      total_manual_discount: Decimal.new(cart.total_manual_discount),
      total_ecommerce_discounts: Decimal.new(cart.total_ecommerce_discounts),
      total_items_manual_discount: Decimal.new(cart.total_items_manual_discount),
      total_delivery_price: Decimal.new(cart.total_delivery_price),
      location_id: order_params["location_id"] || cart.location_id,
      cashier_id: order_params["cashier_id"],
      shipping_address: RmsWeb.AddressJSON.render("show.json", %{address: cart.shipping_address}),
      discounts: Enum.map(cart.discounts, &RmsWeb.DiscountsJSON.data/1)
    })
    |> Map.put(
      :line_items,
      cart.delivery_groups
      |> Enum.flat_map(fn dg -> format_cart_items(cart, dg, dg.cart_items, order_params) end)
    )
    |> Map.put(:addons, format_addons(cart.cart_addons))
    |> Map.put(:metadata, %{notes: (cart.metadata || %{}) |> Map.get(:notes)})
  end

  defp format_addons(cart_addons) do
    cart_addons
    |> Enum.map(fn addon ->
      %{
        addon_id: addon.addon_id,
        name: addon.name,
        type: addon.type,
        list_price: addon.list_price,
        image_url: addon.image_url,
        quantity: addon.quantity,
        price: addon.price
      }
    end)
  end

  defp format_cart_items(cart, delivery_group, cart_items, order_params) do
    cart_items
    |> Enum.map(fn item ->
      item
      |> Map.take([:product_variant_id, :quantity])
      |> Map.merge(%{
        list_price: Decimal.new(item.list_price),
        price: Decimal.new(item.selling_price),
        shipping_method: delivery_group.fulfillment_type,
        shipping_settings: item.metadata,
        customer_id: cart.customer_id,
        staff_id: cart.staff_id,
        location_id: order_params["location_id"] || cart.location_id,
        product_name: item.product_variant.product.name,
        variant_name: item.product_variant.name,
        sku: item.product_variant.sku,
        image_url: get_image_url(item.product_variant.image_urls),
        is_gift: item.is_gift,
        discounts: Enum.map(item.discounts, &RmsWeb.DiscountsJSON.data/1),
        group_index: item.group_index
      })
    end)
  end

  defp get_image_url([]) do
    nil
  end

  defp get_image_url(urls) do
    hd(urls)
  end

  def get_order_by_df_key!(organization_id, df_key) do
    Order
    |> join(:inner, [o], f in assoc(o, :fulfillments))
    |> join(:inner, [o, f], fi in Rms.Fiscal.FiscalInvoice, on: fi.fulfillment_id == f.id)
    |> join(:inner, [o, f, fi], is in Rms.Fiscal.InvoiceSerie, on: is.id == fi.serie_id)
    |> where(
      [o, f, fi, is],
      o.organization_id == ^organization_id and fi.df_key == ^df_key and is.invoice_env != "dev"
    )
    |> preload([o, f, fi, is], [:fulfillments])
    |> Repo.one!()
  end

  defp calculate_discounts(order_attrs) do
    ecommerce_discount_total =
      Map.get(order_attrs, :total_ecommerce_discounts, 0) |> Decimal.new()

    manual_discount_from_attrs = Map.get(order_attrs, :total_manual_discount, 0) |> Decimal.new()

    discount_to_distribute = Decimal.add(manual_discount_from_attrs, ecommerce_discount_total)

    line_items = order_attrs.line_items

    original_line_items_total_price =
      Enum.reduce(line_items, Decimal.new(0), fn item, acc ->
        item_price = item.price
        item_quantity = item.quantity
        item_total = Decimal.mult(item_price, item_quantity)
        Decimal.add(acc, item_total)
      end)

    {updated_line_items, _remaining_discount} =
      assign_discounts({line_items, discount_to_distribute},
        strategy: :weighted,
        total_price: original_line_items_total_price
      )
      |> assign_discounts(strategy: :greedy)

    Map.put(order_attrs, :line_items, updated_line_items)
  end

  defp assign_discounts({line_items, discount_total}, opts) do
    line_items
    |> Enum.map_reduce(discount_total, fn line_item, discount ->
      ratio =
        case opts[:strategy] do
          :weighted ->
            Decimal.div(discount_total, Decimal.max(opts[:total_price], Decimal.new("0.01")))

          :greedy ->
            Decimal.new("1.00")
        end

      to_reduce_amount =
        Enum.reduce(
          [
            discount
            |> Decimal.div(line_item.quantity),
            line_item.price
            |> Decimal.mult(ratio),
            line_item.price
            |> Decimal.sub(Decimal.new("0.01"))
          ],
          fn num, acc ->
            num
            |> Decimal.min(acc)
            |> Decimal.max(Decimal.new("0"))
          end
        )
        |> Decimal.round(2, :floor)

      reduced_amount = Decimal.mult(to_reduce_amount, line_item.quantity)
      updated_value = Decimal.sub(line_item.price, to_reduce_amount)

      updated_discount =
        line_item
        |> Map.get(:manual_discount, Decimal.new("0"))
        |> Decimal.add(reduced_amount)

      updated_line_item =
        line_item
        |> Map.put(:price, updated_value)
        |> Map.put(:manual_discount, updated_discount)

      {updated_line_item, Decimal.sub(discount, reduced_amount)}
    end)
  end

  @doc """
  Updates an existing order with the provided attributes.

  ## Parameters

    - `order`: The order to update.
    - `attrs`: A map containing the attributes to update.

  ## Examples

      iex> Rms.Commerce.Orders.update_order!(order, %{status: "paid"})
      %Rms.Commerce.Orders.Order{id: 1, status: "paid", ...}

      iex> Rms.Commerce.Orders.update_order!(order, %{status: "invalid status})
      (** Ecto.InvalidChangesetError)

  ## Raises

  `Ecto.InvalidChangesetError` if the changeset is invalid.
  """
  def update_order!(order, attrs) do
    order
    |> Order.update_changeset(attrs)
    |> Repo.update!()
  end

  @doc """
  Cancels an existing order with the provided attributes.

  ## Parameters

    - `order`: The order to cancel.
    - `attrs`: A map containing the attributes for cancellation.

  ## Examples

      iex> Rms.Commerce.Orders.cancel_order(order, %{external_id: "123"})
      {:ok, %Rms.Commerce.Orders.Order{id: 1, status: "canceled", ...}}

      iex> Rms.Commerce.Orders.cancel_order(order, %{})
      {:error, %Ecto.Changeset{}}

  ## Returns

  `{:ok, order}` if the order was successfully canceled, or
  `{:error, changeset}` if there was an error.
  """
  def cancel_order(order, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:cancel_order, Order.cancel_changeset(order, attrs))
    |> Ecto.Multi.run(:emit_event, fn _repo, %{cancel_order: updated_order} ->
      Rms.Events.emit("order.canceled", Order.event_payload(updated_order))
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{cancel_order: updated_order}} -> {:ok, updated_order}
      {:error, :cancel_order, changeset, _} -> {:error, changeset}
      _ -> {:error, :unknown_error}
    end
  end

  @doc """
  Marks an existing order as paid.

  ## Parameters

    - `order`: The order to mark as paid.

  ## Examples

      iex> Rms.Commerce.Orders.mark_order_as_paid(order)
      %Rms.Commerce.Orders.Order{id: 1, status: "paid", ...}

  """
  def mark_order_as_paid(order, opts \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:update_order, Order.update_changeset(order, %{status: "paid"}))
    |> Ecto.Multi.run(:emit_event, fn _repo, %{update_order: order} ->
      if opts[:skip_events] do
        {:ok, nil}
      else
        Rms.Events.emit("order.paid", Order.event_payload(order))
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Creates a new Order record in the database based on an external order ID, only if it doesn't already exists.

  This function takes two arguments, `organization_id` and `external_id`. If the `external_id`
  has the prefix "gid://shopify/Order/" indicating it is a Shopify order ID, the function will
  retrieve the corresponding order details from Shopify using the organization's Shopify credentials.

  ## Parameters

  - `organization_id`: The ID of the organization under which the order should be created.
  - `external_id`: The external identifier of the order. Should be in the format "gid://shopify/Order/{order_id}"
    for Shopify orders.

  ## Example

      iex> Rms.Commerce.Orders.create_new(organization_id, "gid://shopify/Order/12345")
      {:ok, %Rms.Commerce.Order{...}}

      iex> Rms.Commerce.Orders.create_new(organization_id, "invalid-external-id")
      {:error, :invalid_external_id}

  """
  def create_new(organization_id, "gid://shopify/Order" <> _ = shopify_order_id) do
    existing_order = Repo.get_by(Order, external_id: shopify_order_id)

    with nil <- existing_order,
         shopify_credential = Integrations.get_shopify_credential!(organization_id),
         shopify_client =
           Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential),
         {:ok, response} <- Integrations.Shopify.get_order!(shopify_client, shopify_order_id),
         order_data = get_in(response, ["data", "order"]),
         {:ok, order_attrs} <- prepare_order_attrs(order_data, :shopify) do
      %Order{organization_id: organization_id, external_id: shopify_order_id}
      |> Order.changeset(order_attrs)
      |> Repo.insert(on_conflict: :replace_all, conflict_target: [:external_id])
    else
      %Order{} = existing_order -> {:ok, existing_order}
      error -> error
    end
  end

  def create_new(_organization_id, _external_id) do
    {:error, :invalid_external_id}
  end

  defp prepare_order_attrs(nil, :shopify), do: {:error, :not_found}

  defp prepare_order_attrs(order_data, :shopify) do
    {:ok,
     %{
       total_price: order_data["currentTotalPriceSet"]["shopMoney"]["amount"],
       status: status_from_shopify(order_data["displayFinancialStatus"]),
       sales_channel: "ecommerce-shopify"
     }}
  end

  def update_order_statuses(affected_orders, organization_id) do
    Enum.each(affected_orders, fn order_id ->
      new_status = determine_order_status(order_id)
      update_order_status(order_id, new_status, organization_id)
    end)
  end

  defp determine_order_status(order_id) do
    line_items = get_line_items_with_current_quantity(order_id)

    if Enum.all?(line_items, &(&1.current_quantity == 0)) do
      "returned"
    else
      "partially_returned"
    end
  end

  defp update_order_status(order_id, new_status, organization_id) do
    order = get_order!(organization_id, order_id)
    update_order!(order, %{status: new_status})
  end

  defp status_from_shopify("PENDING"), do: "open"
  defp status_from_shopify("AUTHORIZED"), do: "open"
  defp status_from_shopify("EXPIRED"), do: "canceled"
  defp status_from_shopify("PARTIALLY_PAID"), do: "partially_paid"
  defp status_from_shopify("PAID"), do: "paid"
  defp status_from_shopify("PARTIALLY_REFUNDED"), do: "partially_refunded"
  defp status_from_shopify("REFUNDED"), do: "refunded"
  defp status_from_shopify("VOIDED"), do: "canceled"
  defp status_from_shopify(_), do: "open"

  alias Rms.Commerce.Orders.OrderSettings

  @doc """
  Creates or updates order settings for an organization.

  ## Examples

      iex> attrs = %{organization_id: 1, prefix: "ORD-", suffix: "-2024", current_order_number: 0}
      iex> Rms.Commerce.Orders.upsert_order_settings(attrs)
      {:ok, %Rms.Commerce.Orders.OrderSettings{organization_id: 1, prefix: "ORD-", suffix: "-2024", current_order_number: 0}}

      iex> attrs = %{organization_id: 1, prefix: "NEW-", suffix: "-UPDATED"}
      iex> Rms.Commerce.Orders.upsert_order_settings(attrs)
      {:ok, %Rms.Commerce.Orders.OrderSettings{organization_id: 1, prefix: "NEW-", suffix: "-UPDATED", current_order_number: 0}}
  """
  def upsert_order_settings(organization_id, attrs \\ %{}) do
    %OrderSettings{organization_id: organization_id}
    |> OrderSettings.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:prefix, :suffix]},
      conflict_target: :organization_id
    )
  end

  @doc """
  Generates a new order name based on the organization's settings.

  ## Examples

      iex> Rms.Commerce.Orders.generate_order_name(1)
      {:ok, "ORD-1-2024"}

      iex> Rms.Commerce.Orders.generate_order_name(1)
      {:ok, "ORD-2-2024"}

  """
  def generate_order_name(organization_id) do
    Repo.transaction(fn ->
      order_settings =
        OrderSettings
        |> where([os], os.organization_id == ^organization_id)
        |> lock("FOR UPDATE")
        |> Repo.one()

      with %OrderSettings{} = order_settings <- order_settings do
        new_number = order_settings.current_order_number + 1

        order_settings
        |> Ecto.Changeset.change(current_order_number: new_number)
        |> Repo.update!()

        "#{order_settings.prefix}#{new_number}#{order_settings.suffix}"
      end
    end)
  end

  @doc """
  Gets order settings for an organization.
  Raises if the OrderSettings does not exist.

  ## Examples

    iex> Rms.Commerce.Orders.get_order_settings!(1)
    %Rms.Commerce.Orders.OrderSettings{organization_id: 1, prefix: "ORD-", suffix: "-2024", current_order_number: 0}

    iex> Rms.Commerce.Orders.get_order_settings!(2)
    ** (Ecto.NoResultsError)

  """
  def get_order_settings!(organization_id) do
    Repo.get_by!(OrderSettings, organization_id: organization_id)
  end

  @doc """
  Returns a list of line items for a given order with their current quantities,
  which is the original quantity minus the sum of returned quantities from reverse fulfillments.

  ## Parameters

    - `order_id`: The ID of the order to retrieve the line items for.

  ## Examples

      iex> Rms.Commerce.Orders.get_line_items_with_current_quantity(1)
      [%{line_item: %LineItem{}, current_quantity: 5}, ...]

  """
  def get_line_items_with_current_quantity(order_id) do
    query =
      from li in Rms.Commerce.Orders.LineItem,
        join: f in assoc(li, :fulfillment),
        left_join: rfl in Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem,
        on: rfl.line_item_id == li.id,
        left_join: rf in Rms.Commerce.Fulfillments.ReverseFulfillment,
        on: rf.id == rfl.reverse_fulfillment_id,
        where:
          f.order_id == ^order_id and (rf.status in ["completed", "processing"] or is_nil(rf.id)),
        group_by: [li.id],
        select: %{
          line_item: li,
          current_quantity: coalesce(li.quantity - sum(rfl.returned_quantity), li.quantity)
        }

    Repo.all(query)
  end

  defp parse_datetime(nil), do: {:ok, nil}

  defp parse_datetime(date) when is_binary(date) do
    case DateTime.from_iso8601(date) do
      {:ok, datetime, _offset} -> {:ok, datetime}
      _error -> {:error, :invalid_date}
    end
  end

  defp parse_datetime(date), do: {:ok, date}

  defp maybe_filter_by_inserted_at(query, nil), do: query

  defp maybe_filter_by_inserted_at(query, inserted_at) do
    end_time = DateTime.add(inserted_at, 1, :day)

    query |> where([o, t, p], o.inserted_at >= ^inserted_at and o.inserted_at < ^end_time)
  end

  defp maybe_filter_by_reference_at(query, nil), do: query

  defp maybe_filter_by_reference_at(query, reference_at) do
    date = NaiveDateTime.beginning_of_day(reference_at)
    where(query, [o], fragment("DATE_TRUNC('day', ?)", o.reference_at) == ^date)
  end

  @doc """
  Gets an order by its external identifier (either `external_id` on the order
  or `external_reference` on one of its fulfillments).

  Returns the order record or `nil` if not found.
  """
  def get_order_by_external_identifier(organization_id, external_identifier) do
    # Query for direct match on order.external_id
    direct_match_query =
      from o in Order,
        where: o.organization_id == ^organization_id and o.external_id == ^external_identifier,
        limit: 1

    # Query for match on fulfillment.external_reference
    fulfillment_match_query =
      from f in Fulfillment,
        join: o in assoc(f, :order),
        where:
          f.organization_id == ^organization_id and f.external_reference == ^external_identifier,
        limit: 1,
        select: o

    # Fetch the first result found, prioritizing direct match
    Repo.one(direct_match_query) || Repo.one(fulfillment_match_query)
  end
end
