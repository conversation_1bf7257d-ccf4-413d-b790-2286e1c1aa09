defmodule Rms.Commerce.Orders.LineItem do
  use Ecto.Schema

  import Ecto.Changeset

  schema "line_items" do
    field :price, :decimal
    field :list_price, :decimal
    field :manual_discount, :decimal, default: 0
    field :quantity, :integer
    field :shipping_method, :string
    field :shipping_settings, :map
    field :is_gift, :boolean, default: false
    field :group_index, :string

    field :image_url, :string
    field :variant_name, :string
    field :product_name, :string
    field :sku, :string

    belongs_to :organization, Rms.Accounts.Organization

    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant

    belongs_to :staff, Rms.Accounts.Staff
    belongs_to :location, Rms.Accounts.Location

    belongs_to :fulfillment, Rms.Commerce.Fulfillments.Fulfillment

    has_many :discounts, Rms.Commerce.Discounts.LineItemDiscount,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :reverse_fulfillment_line_items,
             Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem

    field :reference_at, :utc_datetime

    timestamps()
  end

  @doc false
  def changeset(line_item, attrs, opts \\ []) do
    line_item
    |> cast(attrs, [
      :price,
      :list_price,
      :manual_discount,
      :quantity,
      :shipping_method,
      :product_variant_id,
      :staff_id,
      :location_id,
      :shipping_settings,
      :image_url,
      :variant_name,
      :product_name,
      :sku,
      :group_index,
      :is_gift,
      :reference_at
    ])
    |> validate_required([
      :price,
      :quantity,
      :shipping_method,
      :location_id
    ])
    |> cast_assoc(:discounts)
    |> validate_price(opts)
    |> validate_shipping_settings()
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
  end

  def update_changeset(line_item, attrs) do
    line_item
    |> cast(attrs, [:fulfillment_id])
  end

  defp validate_price(changeset, opts) do
    product_variant_id = get_field(changeset, :product_variant_id)
    price_table = Keyword.get(opts, :price_table, %{})
    validate_price? = Keyword.get(opts, :validate_price, false)

    cond do
      not validate_price? or is_nil(product_variant_id) ->
        changeset

      not Map.has_key?(price_table, product_variant_id) ->
        add_error(changeset, :product_variant_id, "is not in price table")

      not Decimal.eq?(
        price_table |> Map.get(product_variant_id) |> Map.get(:price),
        fetch_field!(changeset, :price)
      ) ->
        add_error(changeset, :price, "does not match product variant price")

      true ->
        changeset
    end
  end

  defp validate_shipping_settings(changeset) do
    shipping_settings = fetch_field!(changeset, :shipping_settings)
    shipping_method = fetch_field!(changeset, :shipping_method)

    if is_nil(shipping_settings) and shipping_method in ["delivery", "local-pickup"] do
      add_error(
        changeset,
        :shipping_settings,
        "can't be blank for #{shipping_method} shipping_method"
      )
    else
      changeset
    end
  end
end
