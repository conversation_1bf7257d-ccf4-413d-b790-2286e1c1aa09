defmodule Rms.Commerce.Orders.Order do
  use Ecto.Schema
  import Ecto.Changeset

  alias Rms.Commerce.Orders.OrderCustomer

  @statuses ~w(open partially_paid paid canceling canceled partially_refunded refunded partially_returned returned)
  @valid_sources ~w(iglu vtex shopify)

  defmodule ShippingAddress do
    use Ecto.<PERSON>hema

    @derive {Jason.Encoder,
             only: [
               :receiver_name,
               :city_name,
               :state,
               :country_name,
               :neighborhood,
               :street,
               :street_type,
               :number,
               :zip,
               :complement
             ]}

    embedded_schema do
      field :receiver_name, :string
      field :city_name, :string
      field :state, :string
      field :country_name, :string
      field :neighborhood, :string
      field :street, :string
      field :street_type, :string
      field :number, :string
      field :zip, :string
      field :complement, :string
    end
  end

  defmodule Addon do
    use Ecto.Schema

    @derive {Jason.Encoder,
             only: [
               :addon_id,
               :name,
               :price,
               :list_price,
               :image_url,
               :type,
               :metadata
             ]}

    embedded_schema do
      field :addon_id, :integer
      field :name, :string
      field :price, :decimal
      field :list_price, :decimal
      field :image_url, :string
      field :type, :string
      field :metadata, :map
    end
  end

  defmodule Metadata do
    use Ecto.<PERSON>hema

    @derive {Jason.Encoder, only: [:notes]}

    embedded_schema do
      field :notes, :string
    end
  end

  schema "orders" do
    field :name, :string
    field :status, :string, default: "open"
    field :external_id, :string
    field :sales_channel, :string, default: "iglu-pos"
    field :source, :string, default: "iglu"

    field :total_price, :decimal
    field :total_price_with_addons, :decimal
    field :total_discount_with_addons, :decimal
    field :total_ecommerce_discounts, :decimal, default: 0
    field :total_items_manual_discount, :decimal, default: 0

    field :total_items_selling_price, :decimal
    field :total_items_list_price, :decimal
    field :total_discount, :decimal
    field :total_delivery_price, :decimal

    embeds_one :shipping_address, ShippingAddress

    embeds_many :addons, Addon, on_replace: :delete

    has_one :order_customer, OrderCustomer

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location

    belongs_to :staff, Rms.Accounts.Staff
    belongs_to :cashier, Rms.Accounts.Staff

    belongs_to :customer, Rms.Customers.Customer
    has_one :transaction, Rms.Finance.Transaction, where: [status: {:in, ["open", "done"]}]

    has_many :fulfillments, Rms.Commerce.Fulfillments.Fulfillment,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :discounts, Rms.Commerce.Discounts.Discount,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :reference_at, :utc_datetime

    embeds_one :metadata, Metadata

    timestamps(type: :utc_datetime)
  end

  defp addons_changeset(changeset, attrs) do
    changeset
    |> cast(attrs, [:addon_id, :name, :price, :type, :metadata, :list_price, :image_url])
    |> validate_required([:addon_id, :name, :price, :type, :list_price])
  end

  defp shipping_address_changeset(changeset, attrs) do
    changeset
    |> cast(attrs, [
      :receiver_name,
      :city_name,
      :state,
      :country_name,
      :neighborhood,
      :street,
      :street_type,
      :number,
      :zip,
      :complement
    ])
    |> validate_required([
      :receiver_name,
      :city_name,
      :state,
      :country_name,
      :street,
      :zip
    ])
  end

  def metadata_changeset(changeset, attrs) do
    changeset
    |> cast(attrs, [:notes])
  end

  @doc false
  def changeset(order, attrs, opts \\ []) do
    order
    |> cast(attrs, [
      :name,
      :total_price,
      :total_items_selling_price,
      :total_items_list_price,
      :total_discount,
      :total_delivery_price,
      :total_ecommerce_discounts,
      :total_items_manual_discount,
      :status,
      :external_id,
      :customer_id,
      :sales_channel,
      :source,
      :location_id,
      :staff_id,
      :cashier_id,
      :reference_at
    ])
    |> cast_fulfillments(opts)
    |> cast_assoc(:discounts)
    |> cast_embed(:shipping_address, with: &shipping_address_changeset/2)
    |> cast_embed(:addons, with: &addons_changeset/2)
    |> cast_embed(:metadata, with: &metadata_changeset/2)
    |> validate_subscription_addon()
    |> validate_required([:status])
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:source, @valid_sources)
    |> validate_location(opts)
    |> unique_constraint(:external_id)
    |> calculate_total_price()
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
    |> prepare_changes(&cast_order_customer(&1))
    |> prepare_changes(fn changeset ->
      organization_id = fetch_field!(changeset, :organization_id)

      with name when is_nil(name) <- fetch_field!(changeset, :name),
           {:ok, generated_name} <- Rms.Commerce.Orders.generate_order_name(organization_id) do
        put_change(changeset, :name, generated_name)
      else
        _ -> changeset
      end
    end)
  end

  defp validate_subscription_addon(changeset) do
    case get_field(changeset, :addons) do
      nil ->
        changeset

      addons ->
        has_subscription = Enum.any?(addons, &(&1.type == "subscription"))
        customer_id = get_field(changeset, :customer_id)

        case {has_subscription, customer_id} do
          {true, nil} ->
            add_error(changeset, :customer_id, "is required when adding a subscription addon")

          _ ->
            changeset
        end
    end
  end

  defp validate_location(changeset, opts) do
    allowed_location_ids = Keyword.get(opts, :allowed_location_ids, [])
    sales_channel = get_field(changeset, :sales_channel)
    location_id = get_field(changeset, :location_id)

    cond do
      sales_channel != "iglu-pos" ->
        changeset

      is_nil(location_id) ->
        add_error(changeset, :location_id, "is required")

      allowed_location_ids == :all ->
        changeset

      location_id in allowed_location_ids ->
        changeset

      true ->
        add_error(changeset, :location_id, "is not within the allowed locations")
    end
  end

  defp calculate_total_price(changeset) do
    zero = Decimal.new("0")
    total_delivery_price = get_field(changeset, :total_delivery_price) || zero

    line_items =
      get_field(changeset, :fulfillments, [])
      |> Enum.flat_map(& &1.line_items)

    case line_items do
      [] ->
        put_change(changeset, :total_price, total_delivery_price)

      line_items ->
        {total_price, total_discount} =
          Enum.reduce(line_items, {total_delivery_price, zero}, fn line_item,
                                                                   {total_price, total_discount} ->
            total_price =
              line_item.price
              |> Decimal.mult(line_item.quantity)
              |> Decimal.add(total_price)

            total_discount =
              (line_item.list_price || line_item.price)
              |> Decimal.sub(line_item.price)
              |> Decimal.mult(line_item.quantity)
              |> Decimal.add(total_discount)

            {total_price, total_discount}
          end)

        changeset
        |> put_change(:total_price, total_price)
        |> put_change(:total_discount, total_discount)
    end
    |> calculate_total_price_with_addons()
  end

  defp calculate_total_price_with_addons(changeset) do
    zero = Decimal.new("0")
    total_discount = get_field(changeset, :total_discount) || zero
    total_price = get_field(changeset, :total_price)

    case get_field(changeset, :addons) do
      nil ->
        changeset
        |> put_change(:total_discount_with_addons, total_discount)
        |> put_change(:total_price_with_addons, total_price)

      addons ->
        total_price_with_addons =
          total_price
          |> Decimal.add(
            Enum.reduce(addons, zero, fn addon, acc -> acc |> Decimal.add(addon.price) end)
          )

        total_addons_discount =
          total_discount
          |> Decimal.add(
            Enum.reduce(addons, zero, fn
              addon, acc when not is_nil(addon.list_price) ->
                acc
                |> Decimal.add(
                  Decimal.new(addon.list_price)
                  |> Decimal.sub(addon.price)
                )

              addon, acc when is_nil(addon.list_price) ->
                acc
                |> Decimal.add(
                  Decimal.new(addon.price)
                  |> Decimal.sub(addon.price)
                )
            end)
          )

        changeset
        |> put_change(:total_discount_with_addons, total_addons_discount)
        |> put_change(:total_price_with_addons, total_price_with_addons)
    end
  end

  defp cast_order_customer(changeset) do
    organization_id = fetch_field!(changeset, :organization_id)

    cond do
      changeset.data.customer_id ->
        changeset

      get_change(changeset, :customer_id) == nil ->
        changeset

      true ->
        customer_id = get_change(changeset, :customer_id)
        customer = Rms.Customers.get_customer!(organization_id, customer_id)

        order_customer_changeset =
          OrderCustomer.changeset(
            %OrderCustomer{organization_id: organization_id, customer_id: customer_id},
            %{
              name: customer.name,
              email: customer.email,
              document_type: customer.document_type,
              document: customer.document,
              primary_phone_number: customer.primary_phone_number
            }
          )

        put_assoc(changeset, :order_customer, order_customer_changeset)
    end
  end

  @doc false
  def update_changeset(order, attrs) do
    order
    |> cast(attrs, [:total_price, :status, :external_id, :reference_at])
    |> validate_inclusion(:status, @statuses)
    |> cast_embed(:addons, with: &addons_changeset/2)
    |> unique_constraint(:external_id)
  end

  @doc false
  def cancel_changeset(order, attrs) do
    order
    |> cast(attrs, [:status, :external_id])
    |> put_change(:status, "canceled")
    |> unique_constraint(:external_id)
  end

  @doc false
  def update_name_changeset(order, name) do
    order
    |> cast(%{name: name}, [:name])
    |> validate_required([:name])
  end

  @doc """
  Gets the UF (state) from the order's location address.
  Returns nil if the location or its address is not loaded/set.

  Note: The location and its address must be preloaded for this function to work.
  """
  def find_uf(%__MODULE__{location: %{address: %{state: state}}}) when not is_nil(state),
    do: state

  def find_uf(_), do: nil

  @doc false
  def event_payload(order) do
    order =
      Rms.Repo.preload(order, [
        :staff,
        :cashier,
        :location,
        :discounts,
        customer: [:addresses],
        fulfillments: [line_items: [product_variant: [:product]]],
        transaction: [:payments, :customer]
      ])

    zero = Decimal.new("0")

    %{
      id: order.id,
      name: order.name,
      amount: order.total_items_selling_price || zero,
      delivery_price: order.total_delivery_price,
      discounts:
        order.discounts
        |> Enum.map(&discount_payload/1),
      discounts_total: order.total_discount || zero,
      total_amount: order.total_price || zero,
      total_with_addons: order.total_price_with_addons || zero,
      total_discount_with_addons: order.total_discount_with_addons || zero,
      status: order.status,
      inserted_at: order.inserted_at,
      external_id: order.external_id,
      organization_id: order.organization_id,
      staff: staff_payload(order.staff),
      cashier: staff_payload(order.cashier),
      customer: customer_payload(order.customer),
      addons: (order.addons || []) |> Enum.map(&addon_payload/1),
      line_items:
        order.fulfillments
        |> Enum.flat_map(& &1.line_items)
        |> Enum.map(&line_item_payload/1),
      location_id: order.location_id,
      location: location_payload(order.location),
      transaction: transaction_payload(order.transaction),
      fulfillments: Enum.map(order.fulfillments, &fulfillment_payload/1),
      shipping_address: address_payload(order.shipping_address)
    }
  end

  defp addon_payload(nil), do: nil

  defp addon_payload(%Addon{} = addon) do
    %{
      addon_id: addon.addon_id,
      type: addon.type,
      name: addon.name,
      price: addon.price,
      list_price: addon.list_price,
      image_url: addon.image_url,
      metadata: addon.metadata
    }
  end

  defp address_payload(nil), do: nil

  defp address_payload(%Rms.Finance.Customer.CustomerAddress{} = address) do
    %{
      city_name: address.city_name,
      state: address.state,
      country_name: address.country_name,
      neighborhood: address.neighborhood,
      street: address.street,
      number: address.number,
      zip: address.zip,
      complement: address.complement
    }
  end

  defp address_payload(address) do
    %{
      receiver_name: address.receiver_name,
      city_name: address.city_name,
      state: address.state,
      country_name: address.country_name,
      neighborhood: address.neighborhood,
      street: address.street,
      street_type: address.street_type,
      number: address.number,
      zip: address.zip,
      complement: address.complement
    }
  end

  defp fulfillment_payload(%Rms.Commerce.Fulfillments.Fulfillment{} = fulfillment) do
    %{
      id: fulfillment.id,
      status: fulfillment.status,
      ecommerce: fulfillment.ecommerce,
      shipping_method: fulfillment.shipping_method,
      external_reference: fulfillment.external_reference,
      line_item_ids: Enum.map(fulfillment.line_items, & &1.id)
    }
  end

  defp fulfillment_payload(_), do: nil

  defp transaction_payload(%Rms.Finance.Transaction{} = transaction) do
    %{
      id: transaction.id,
      status: transaction.status,
      inserted_at: transaction.inserted_at,
      updated_at: transaction.updated_at,
      payments: Enum.map(transaction.payments, &payment_payload/1),
      customer: transaction_customer_payload(transaction.customer)
    }
  end

  defp transaction_payload(_), do: nil

  defp payment_payload(%Rms.Finance.Payment{} = payment) do
    %{
      id: payment.id,
      amount: payment.amount,
      status: payment.status,
      method: payment.method,
      metadata: payment.metadata,
      inserted_at: payment.inserted_at,
      updated_at: payment.updated_at
    }
  end

  defp payment_payload(_), do: nil

  defp transaction_customer_payload(%Rms.Finance.Customer{} = customer) do
    %{
      id: customer.id,
      name: customer.name,
      email: customer.email,
      document: customer.document,
      document_type: customer.document_type,
      address: address_payload(customer.address)
    }
  end

  defp transaction_customer_payload(_), do: nil

  defp location_payload(%Rms.Accounts.Location{} = location) do
    %{
      id: location.id,
      name: location.name,
      cnpj: location.cnpj,
      external_id: location.external_id,
      inserted_at: location.inserted_at,
      updated_at: location.updated_at,
      archived_at: location.archived_at
    }
  end

  defp location_payload(_), do: nil

  defp line_item_payload(%Rms.Commerce.Orders.LineItem{} = line_item) do
    %{
      id: line_item.id,
      list_price: line_item.list_price,
      price: line_item.price,
      manual_discount: line_item.manual_discount,
      quantity: line_item.quantity,
      sku: line_item.sku,
      shipping_method: line_item.shipping_method
    }
  end

  defp staff_payload(%Rms.Accounts.Staff{} = staff) do
    %{
      id: staff.id,
      name: staff.name,
      external_id: staff.external_id,
      inserted_at: staff.inserted_at,
      updated_at: staff.updated_at,
      archived_at: staff.archived_at
    }
  end

  defp staff_payload(_), do: nil

  defp customer_payload(%Rms.Customers.Customer{} = customer) do
    %{
      id: customer.id,
      name: customer.name,
      email: customer.email,
      document: customer.document,
      birthdate: customer.birthdate,
      document_type: customer.document_type,
      primary_phone_number: customer.primary_phone_number,
      addresses: Enum.map(customer.addresses, &address_payload/1)
    }
  end

  defp customer_payload(_), do: nil

  defp discount_payload(%Rms.Commerce.Discounts.Discount{} = discount) do
    %{
      type: discount.type,
      value: discount.value,
      description: discount.description
    }
  end

  defp discount_payload(_) do
    nil
  end

  defp cast_fulfillments(changeset, opts) do
    opts =
      Keyword.put_new_lazy(opts, :price_table, fn -> build_price_table(changeset) end)

    cast_assoc(changeset, :fulfillments,
      with: &Rms.Commerce.Fulfillments.Fulfillment.changeset(&1, &2, opts)
    )
  end

  defp build_price_table(changeset) do
    line_items = Map.get(changeset.params, "line_items", [])

    product_variant_ids =
      line_items
      |> Enum.map(fn line_item ->
        case line_item do
          %{"product_variant_id" => product_variant_id} -> product_variant_id
          %{product_variant_id: product_variant_id} -> product_variant_id
          _ -> nil
        end
      end)
      |> Enum.filter(&(not is_nil(&1)))

    organization_id = fetch_field!(changeset, :organization_id)
    Rms.Commerce.Products.get_price_table(organization_id, product_variant_ids)
  end
end
