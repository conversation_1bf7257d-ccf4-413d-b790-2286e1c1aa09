defmodule Rms.Commerce.Orders.OrderCustomer do
  use Ecto.Schema
  import Ecto.Changeset

  schema "order_customers" do
    field :name, :string
    field :plain_name, :string
    field :document_type, :string
    field :document, Rms.Vault.EncryptedBinary
    field :document_hash, Cloak.Ecto.SHA256
    field :email, Rms.Vault.EncryptedBinary
    field :email_hash, Cloak.Ecto.SHA256
    field :primary_phone_number, Rms.Vault.EncryptedBinary
    field :primary_phone_number_hash, Cloak.Ecto.SHA256

    timestamps(type: :utc_datetime)

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :order, Rms.Commerce.Orders.Order
    belongs_to :customer, Rms.Customers.Customer
  end

  @doc false
  def changeset(customer, attrs) do
    customer
    |> cast(attrs, [:name, :document_type, :document, :email, :primary_phone_number])
    |> put_plain_name()
    |> put_hashed_fields()
  end

  defp put_hashed_fields(changeset) do
    downcase_email =
      with email when not is_nil(email) <- get_field(changeset, :email) do
        String.downcase(email)
      end

    changeset
    |> put_change(:document_hash, get_field(changeset, :document))
    |> put_change(:email_hash, downcase_email)
    |> put_change(:primary_phone_number_hash, get_field(changeset, :primary_phone_number))
  end

  defp put_plain_name(changeset) do
    plain_name = get_field(changeset, :name)
    put_change(changeset, :plain_name, plain_name)
  end
end
