defmodule Rms.Commerce.Orders.OrderData do
  import Ecto.Query

  alias Rms.Commerce.Discounts.Discount
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Finance.Transaction
  alias Rms.Repo

  def get_order_discounts(order_id) do
    Discount
    |> where([d], d.order_id == ^order_id)
    |> select([d], %{
      id: d.id,
      amount: d.amount,
      type: d.type
    })
    |> Repo.all()
  end

  def get_order_fulfillments(order_id) do
    Fulfillment
    |> where([f], f.order_id == ^order_id)
    |> select([f], %{
      id: f.id,
      shipping_method: f.shipping_method,
      external_reference: f.external_reference,
      status: f.status,
      line_items: f.line_items
    })
    |> Repo.all()
  end

  def get_return_credit_info(order_id) do
    Transaction
    |> where([t], t.order_id == ^order_id)
    |> join(:inner, [t], p in assoc(t, :payments))
    |> join(:inner, [t, p], cp in assoc(p, :iglu_credit_payment))
    |> join(:inner, [t, p, cp], c in assoc(cp, :iglu_credit))
    |> join(:inner, [t, p, cp, c], rf in assoc(c, :reverse_fulfillment))
    |> join(:inner, [t, p, cp, c, rf], rfl in assoc(rf, :line_items))
    |> join(:inner, [t, p, cp, c, rf, rfl], l in assoc(rfl, :line_item))
    |> join(:inner, [t, p, cp, c, rf, rfl, l], f in assoc(l, :fulfillment))
    |> select([t, p, cp, c, rf, rfl, l, f], %{
      payment_id: p.id,
      credit_id: c.id,
      reverse_fulfillment_id: rf.id,
      original_order_id: f.order_id
    })
    |> Repo.all()
  end

  def get_items_from_reverse_fulfillment(reverse_fulfillment_id) do
    Rms.Commerce.Orders.LineItem
    |> join(:inner, [li], rfl in assoc(li, :reverse_fulfillment_line_items))
    |> where([li, rfl], rfl.reverse_fulfillment_id == ^reverse_fulfillment_id)
    |> Repo.all()
  end

  def get_order_from_reverse_fulfillment(reverse_fulfillment_id) do
    Rms.Commerce.Orders.Order
    |> join(:inner, [o], t in assoc(o, :transaction))
    |> join(:inner, [o, t], p in assoc(t, :payments))
    |> join(:inner, [o, t, p], icp in assoc(p, :iglu_credit_payment))
    |> join(:inner, [o, t, p, icp], c in assoc(icp, :iglu_credit))
    |> where([o, t, p, icp, c], c.reverse_fulfillment_id == ^reverse_fulfillment_id)
    |> distinct([o], o.id)
    |> Repo.all()
  end

  def get_original_order_from_reverse_fulfillment(reverse_fulfillment_id) do
    Rms.Commerce.Orders.Order
    |> join(:inner, [o], f in assoc(o, :fulfillments))
    |> join(:inner, [o, f], li in assoc(f, :line_items))
    |> join(:inner, [o, f, li], rfl in assoc(li, :reverse_fulfillment_line_items))
    |> where([o, f, li, rfl], rfl.reverse_fulfillment_id == ^reverse_fulfillment_id)
    |> distinct([o], o.id)
    |> Repo.all()
  end

  def get_reverse_fulfillment_details(reverse_fulfillment_id, organization_id) do
    ReverseFulfillment
    |> join(:inner, [rf], rfl in assoc(rf, :line_items))
    |> join(:inner, [rf, rfl], li in assoc(rfl, :line_item))
    |> join(:left, [rf, rfl, li], pv in assoc(li, :product_variant))
    |> join(:left, [rf, rfl, li, pv], p in assoc(pv, :product))
    |> where(
      [rf, rfl, li, pv, p],
      rf.id == ^reverse_fulfillment_id and rf.organization_id == ^organization_id
    )
    |> select(
      [rf, rfl, li, pv, p],
      %{
        reverse_fulfillment_id: rf.id,
        line_item_id: li.id,
        returned_quantity: rfl.returned_quantity,
        product_variant_id: pv.id,
        product_id: p.id,
        sku: li.sku
      }
    )
    |> Repo.all()
  end
end
