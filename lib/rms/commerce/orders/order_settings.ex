defmodule Rms.Commerce.Orders.OrderSettings do
  use Ecto.Schema
  import Ecto.Changeset

  schema "order_settings" do
    field :prefix, :string
    field :suffix, :string
    field :current_order_number, :integer, default: 0

    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  @doc false
  def changeset(order_settings, attrs) do
    order_settings
    |> cast(attrs, [:prefix, :suffix])
    |> validate_at_least_one_present([:prefix, :suffix])
    |> validate_length(:prefix, max: 5)
    |> validate_length(:suffix, max: 5)
    |> unique_constraint(:organization_id)
    |> assoc_constraint(:organization)
  end

  defp validate_at_least_one_present(changeset, fields) do
    if Enum.any?(fields, &present?(changeset, &1)) do
      changeset
    else
      add_error(changeset, hd(fields), "at least one of prefix or suffix must be set")
    end
  end

  defp present?(changeset, field) do
    value = get_field(changeset, field)
    value && value != ""
  end
end
