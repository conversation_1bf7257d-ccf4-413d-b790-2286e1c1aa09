defmodule Rms.Commerce.Packings do
  alias Rms.Commerce.Packings.Packing
  alias Rms.Repo

  import Ecto.Query

  def get_packing!(organization_id, packing_id, preload \\ []) do
    Packing
    |> preload(^preload)
    |> Repo.get_by!(id: packing_id, organization_id: organization_id)
  end

  def create_packing(organization_id, fulfillment_id, params, _opts \\ []) do
    Packing.changeset(
      %Packing{organization_id: organization_id, fulfillment_id: fulfillment_id},
      params
    )
    |> Repo.insert()
  end

  def create_packings(organization_id, fulfillment_id, params_list, _opts \\ []) do
    params_list
    |> Enum.with_index()
    |> Enum.reduce(Ecto.Multi.new(), fn {packing_attrs, idx}, multi ->
      Packing.changeset(
        %Packing{organization_id: organization_id, fulfillment_id: fulfillment_id},
        packing_attrs
      )
      |> then(&Ecto.Multi.insert(multi, {:packing, idx}, &1))
    end)
    |> Repo.transaction()
  end

  def paginated_packings(organization_id, opts \\ []) do
    query = build_packings_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    preloads = Keyword.get(opts, :preloads, [])

    query =
      query
      |> preload(^preloads)
      |> order_by(desc: :inserted_at, desc: :id)

    paginate_opts =
      [
        after: cursor_after,
        before: cursor_before,
        limit: limit,
        cursor_fields: [{:inserted_at, :desc}, {:id, :desc}]
      ]

    Repo.paginate(
      query,
      paginate_opts
    )
  end

  defp build_packings_query(organization_id, opts) do
    base_query = from(p in Packing, where: p.organization_id == ^organization_id)

    Enum.reduce(opts, base_query, fn
      {key, value}, query when key in ~w(status shipping_method external_reference)a ->
        where(query, [p], field(p, ^key) == ^value)

      {:location_id, location_id}, query ->
        query
        |> join(:left, [p], d in assoc(p, :dock))
        |> where([_p, d], d.location_id == ^location_id)
        |> distinct(true)

      {:inserted_at, %NaiveDateTime{} = inserted_at}, query ->
        date = NaiveDateTime.beginning_of_day(inserted_at)
        where(query, [o], fragment("DATE_TRUNC('day', ?)", o.inserted_at) == ^date)

      {:inserted_at, inserted_at}, query when is_binary(inserted_at) ->
        date =
          inserted_at
          |> NaiveDateTime.from_iso8601!()
          |> NaiveDateTime.beginning_of_day()

        where(query, [o], fragment("DATE_TRUNC('day', ?)", o.inserted_at) == ^date)

      _, query ->
        query
    end)
  end
end
