defmodule Rms.Commerce.Packings.Packing do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Packings.PackingItem
  alias Rms.Accounts.Dock
  alias Rms.Accounts.Staff
  alias Rms.Customers.Customer

  @valid_statuses ~w(pending accepted separated canceled delivered_to_courier delivered_to_customer)

  schema "packings" do
    field :shipping_method, :string
    field :courier_name, :string
    field :external_reference, :string
    field :status, :string, default: "pending"

    belongs_to :organization, Organization
    belongs_to :dock, Dock
    belongs_to :fulfillment, Fulfillment
    belongs_to :customer, Customer
    belongs_to :staff, Staff

    has_many :packing_items, PackingItem,
      defaults: {Rms.Repo, :inherit_fields, [[:organization_id, :fulfillment_id]]}

    field :total_price, :decimal

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(packing, attrs) do
    packing
    |> cast(attrs, [
      :shipping_method,
      :courier_name,
      :external_reference,
      :status,
      :dock_id,
      :customer_id,
      :staff_id,
      :total_price
    ])
    |> validate_required([
      :shipping_method,
      :dock_id,
      :fulfillment_id,
      :total_price
    ])
    |> validate_inclusion(:status, @valid_statuses)
    |> validate_inclusion(:shipping_method, ~w(in-store local-pickup delivery))
    |> assoc_constraint(:fulfillment)
    |> assoc_constraint(:dock)
    |> assoc_constraint(:customer)
    |> assoc_constraint(:staff)
    |> assoc_constraint(:organization)
    |> cast_assoc(:packing_items, with: &Rms.Commerce.Packings.PackingItem.changeset(&1, &2))
  end
end
