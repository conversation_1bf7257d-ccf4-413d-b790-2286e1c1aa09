defmodule Rms.Commerce.Packings.PackingItem do
  use Ecto.Schema

  import Ecto.Changeset

  schema "packing_items" do
    field :quantity, :integer

    belongs_to :organization, Rms.Accounts.Organization

    belongs_to :packing, Rms.Commerce.Packings.Packing
    belongs_to :fulfillment, Rms.Commerce.Fulfillments.Fulfillment
    belongs_to :line_item, Rms.Commerce.Orders.LineItem

    timestamps()
  end

  @doc false
  def changeset(packing_item, attrs) do
    packing_item
    |> cast(attrs, [
      :quantity,
      :line_item_id
    ])
    |> validate_required([
      :quantity,
      :line_item_id
    ])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:line_item)
    |> assoc_constraint(:fulfillment)
    |> assoc_constraint(:packing)
  end
end
