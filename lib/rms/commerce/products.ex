defmodule Rms.Commerce.Products do
  require Logger

  alias Rms.Commerce.Products.Addon
  alias Rms.Commerce.Products.InventoryItem
  alias Rms.Commerce.Products.Product
  alias Rms.Commerce.Products.ProductVariant
  alias Rms.Repo

  import Ecto.Query

  @spec list_products(any(), keyword()) :: any()
  @doc """
  Retrieves a list of all products.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the product.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:limit` - The maximum number of products to return. Defaults to 20.
    - `:preloads` - A list of associations to preload.

  ## Examples

      iex> Rms.Commerce.Products.list_products(1)
      [%Rms.Commerce.Products.Product{}, ...]

  """
  def list_products(organization_id, opts \\ []) do
    query = build_product_query(organization_id, opts)
    limit = Keyword.get(opts, :limit, 20)

    query
    |> limit(^limit)
    |> Repo.all()
  end

  @doc """
  Paginates the list of products for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the products.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:after` - A cursor value for fetching results after this cursor.
    - `:before` - A cursor value for fetching results before this cursor.
    - `:limit` - The maximum number of products to return. Defaults to 20.
    - `:preloads` - A list of associations to preload.

  ## Examples

      iex> Rms.Commerce.Products.paginated_products(1, limit: 10, after: "cursor_value")
      %{entries: [%Rms.Commerce.Products.Product{}, ...], metadata: %{}}

  """
  def paginated_products(organization_id, opts \\ []) do
    query = build_product_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    Repo.paginate(
      query,
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:inserted_at, :desc}, {:id, :desc}]
    )
  end

  defp build_product_query(organization_id, opts) do
    preloads = Keyword.get(opts, :preloads, [])
    product_types = Keyword.get(opts, :product_types, Product.get_product_types())

    Product
    |> join(:inner, [p], pv in assoc(p, :product_variants))
    |> preload(^preloads)
    |> where(organization_id: ^organization_id)
    |> where([p], p.product_type in ^product_types)
    |> search(opts)
    |> distinct([p], desc: p.inserted_at, desc: p.id)
    |> order_by([p], desc: p.inserted_at, desc: p.id)
  end

  defp search_exactly(query, search) do
    query
    |> where(
      [p, pv],
      fragment("unaccent(?) ilike unaccent(?)", p.name, ^search) or
        fragment("? ilike ?", pv.bar_code, ^search) or
        fragment("unaccent(?) ilike unaccent(?)", pv.name, ^search) or
        fragment("? ilike ?", pv.sku, ^search)
    )
  end

  defp search_by_similarity(query, search) do
    query
    |> where(
      [p, pv],
      fragment("unaccent(?) %> ?", p.name, ^search) or fragment("? %> ?", pv.bar_code, ^search) or
        fragment("unaccent(?) %> ?", pv.name, ^search) or fragment("? %> ?", pv.sku, ^search)
    )
    |> order_by([p, pv],
      desc: fragment("? <% unaccent(?)", ^search, pv.name),
      desc: fragment("? <% ?", ^search, pv.bar_code),
      desc: fragment("? <% unaccent(?)", ^search, p.name),
      desc: fragment("? <% ?", ^search, pv.sku)
    )
  end

  defp search(query, opts) do
    case Keyword.get(opts, :search) do
      nil ->
        query

      search ->
        exact_query = search_exactly(query, search)

        if Repo.exists?(exact_query) do
          exact_query
        else
          search_by_similarity(query, search)
        end
    end
  end

  @doc """
  Retrieves a product by its ID.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the product.
    - `product_id`: The ID of the product to retrieve.

  ## Examples

      iex> Rms.Commerce.Products.get_product!(2, 1)
      %Rms.Commerce.Products.Product{id: 1, organization_id: 2, ...}

  ## Raises

  `Ecto.NoResultsError` if the product with the given ID does not exist.

  """
  def get_product!(organization_id, product_id, preloads \\ []) do
    Product
    |> where(organization_id: ^organization_id, id: ^product_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Retrieves a product by its barcode, raising an exception if not found.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the product.
    - `barcode`: The barcode of the product variant to retrieve.

  ## Examples

      iex> Rms.Commerce.Products.get_product_by_barcode!(1, "123456789")
      %Rms.Commerce.Products.Product{id: 1, organization_id: 1, ...}

  ## Raises

  `Ecto.NoResultsError` if the product with the given barcode does not exist.

  """
  def get_product_by_barcode!(organization_id, barcode) do
    Product
    |> join(:inner, [p], pv in assoc(p, :product_variants), on: pv.bar_code == ^barcode)
    |> where([p, pv], p.organization_id == ^organization_id and is_nil(pv.archived_at))
    |> preload(:product_variants)
    |> order_by([p, pv], desc: pv.inserted_at)
    |> limit(1)
    |> Repo.one!()
  end

  def get_product_variant!(organization_id, product_variant_id, preloads \\ []) do
    ProductVariant
    |> where(organization_id: ^organization_id, id: ^product_variant_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  def get_product_variants!(organization_id, product_variant_ids) do
    ProductVariant
    |> where([pv], pv.organization_id == ^organization_id and pv.id in ^product_variant_ids)
    |> Repo.all()
    |> Enum.into(%{}, fn pv -> {pv.id, pv} end)
  end

  @doc """
  Creates a new product with the given attributes.

  ## Parameters

    - `organization_id`: The organization that owns the product.
    - `attrs`: A map containing the attributes to create the product with.
    - `opts`: A keyword list of options to pass to the changeset.

  ## Examples

      iex> Rms.Commerce.Products.create_product(1, %{name: "New Product", price: 10.00})
      {:ok, %Rms.Commerce.Products.Product{}}

  ## Returns

  `{:ok, product}` if the product was successfully created, or
  `{:error, changeset}` if there was an error.

  """
  def create_product(organization_id, attrs, opts \\ []) do
    %Product{organization_id: organization_id}
    |> Product.changeset(attrs, opts)
    |> Repo.insert()
  end

  @doc """
  Updates an existing product with the provided attributes.

  ## Parameters

    - `product`: The product to update.
    - `attrs`: A map containing the attributes to update.
    - `opts`: A keyword list of options to pass to the changeset.

  ## Examples

      iex> Rms.Commerce.Products.update_product(product, %{price: 15.00})
      {:ok, %Rms.Commerce.Products.Product{}}

  ## Returns

  `{:ok, product}` if the product was successfully updated, or
  `{:error, changeset}` if there was an error.

  """
  def update_product(product, attrs, opts \\ []) do
    product
    |> Product.update_changeset(attrs, opts)
    |> Repo.update()
  end

  def archive(product, opts) do
    variant_ids = Keyword.get(opts, :variant_ids, [])

    Ecto.Multi.new()
    |> archive_variant(product, variant_ids)
    |> Repo.transaction()
  end

  defp archive_variant(multi, product, ids) do
    product.product_variants
    |> filter_variants(ids)
    |> Enum.reduce(multi, fn variant, multi ->
      multi
      |> Ecto.Multi.run("achive:variant#{variant.id}", fn _, _ ->
        archive_product_variant(variant)
      end)
    end)
  end

  defp filter_variants(variants, []) do
    variants
  end

  defp filter_variants(variants, ids) when is_list(ids) do
    Enum.filter(variants, fn variant -> to_string(variant.id) in ids end)
  end

  defp filter_variants(variants, _ids) do
    variants
  end

  @doc """
  Deletes a product.

  ## Parameters

    - `product`: The product to delete.

  ## Examples

      iex> Rms.Commerce.Products.delete_product(%Rms.Commerce.Products.Product{})
      :ok

  ## Returns

  `:ok` if the product was successfully deleted, or
  `{:error, changeset}` if there was an error.

  """
  def delete_product(product) do
    Repo.delete(product)
  end

  alias Rms.Commerce.Products.ProductVariant

  def archive_product_variant(product_variant) do
    product_variant
    |> ProductVariant.archive_changeset()
    |> Repo.update()
  end

  @doc """
  Retrieves a price table for all products within an organization.

  ## Parameters

    - `organization_id`: The ID of the organization for which to retrieve the price table.
    - `product_variant_ids`: The Product Variant IDs to filter the price table by.

  ## Examples

      iex> Rms.Commerce.Products.get_price_table(1, [1, 2, 3])
      %{1: 10.00, 2: 15.00, 3: 20.00}

  """
  def get_price_table(organization_id, product_variant_ids \\ []) do
    base_query =
      Product
      |> where([p], p.organization_id == ^organization_id)
      |> join(:inner, [p], pv in assoc(p, :product_variants))

    query =
      if product_variant_ids != [] do
        where(base_query, [p, pv], pv.id in ^product_variant_ids)
      else
        base_query
      end

    query
    |> select([p, pv], {pv.id, pv.list_price, pv.price})
    |> Repo.all()
    |> Map.new(fn {id, list_price, price} ->
      {id, %{price: price, list_price: list_price}}
    end)
  end

  @doc """

  Retrieves a list of inventory_items within an organization matching the search parameters.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the carts
    - `opts`: A keyword list of options. (optional)

  ## Options

  - `:status` - A string value to filter carts by status. Defaults to "active".
  - `:location_id` - The location id associated with the cart. No defaault value.
  - `:staff_id` - The staff id associated with the cart. No default value.
  - `:customer_id` - The customer id associated with the cart. No default value.

  ## Examples

  iex> Rms.Commerce.Products.list_inventory_items(1)
  [ ... ]

  iex> Rms.Commerce.Products.list_inventory_items(1, query_params: [customer_id: 3, status: "canceled"])
  [ ... ]

  """
  def list_inventory_items(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, %{})

    inventory_items = fetch_inventory_items(organization_id, query_params)
    params = opts[:query_params]

    single_variant = single_variant?(params)
    single_location = single_location?(params)
    inventory_item = Enum.at(inventory_items, 0)

    inventory_integration =
      get_inventory_integration(organization_id) ||
        Rms.Settings.get_connected_ecommerce(organization_id)

    if call_inventory_integration?(
         inventory_integration,
         single_variant,
         single_location,
         inventory_item
       ) do
      get_inventory_from_integration(
        organization_id,
        params,
        inventory_item,
        inventory_integration
      )
    else
      inventory_items
    end
  end

  defp fetch_inventory_items(organization_id, query_params) do
    InventoryItem
    |> where([f], f.organization_id == ^organization_id)
    |> where(^add_inventory_item_filter_where(query_params))
    |> Repo.all()
  end

  defp single_variant?(params) do
    not is_nil(params[:product_variant_id]) || length(params[:product_variant_ids] || []) == 1
  end

  defp single_location?(params) do
    not is_nil(params[:location_id])
  end

  defp get_inventory_integration(organization_id) do
    ld_context_map = :ldclient_context.new(to_string(organization_id), "organization_id")
    ld_context = :ldclient_context.new_from_map(ld_context_map)

    case :ldclient.variation(
           "custom-inventory-integration",
           ld_context,
           "none"
         ) do
      "none" ->
        Rms.Settings.get_connected_ecommerce(organization_id)

      integration ->
        integration
    end
  end

  defp call_inventory_integration?(
         inventory_integration,
         single_variant,
         single_location,
         inventory_item
       ) do
    inventory_integration != :null and
      single_variant and
      single_location and
      (is_nil(inventory_item) or outdated_inventory_level?(inventory_item))
  end

  defp get_inventory_from_integration(
         organization_id,
         params,
         _inventory_item,
         inventory_integration
       ) do
    product_variant_id = params[:product_variant_id] || Enum.at(params[:product_variant_ids], 0)

    case Rms.Integrations.import_inventory_level(
           organization_id,
           params[:location_id],
           product_variant_id,
           inventory_integration
         ) do
      {:ok, inventory_item} ->
        [inventory_item]

      rest ->
        Logger.warning("failed to fetch inventory from integration: #{inspect(rest)}")

        []
    end
  end

  defp outdated_inventory_level?(%{updated_at: updated_at}) do
    now =
      DateTime.utc_now()
      |> DateTime.to_naive()

    updated_at
    |> NaiveDateTime.add(5 * 60, :second)
    |> NaiveDateTime.before?(now)
  end

  defp add_inventory_item_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["location_id", :location_id] ->
        dynamic([f], ^dynamic and f.location_id == ^value)

      {key, value}, dynamic when key in ["quantity", :quantity] ->
        dynamic([f], ^dynamic and f.quantity == ^value)

      {key, value}, dynamic when key in ["product_variant_id", :product_variant_id] ->
        dynamic([f], ^dynamic and f.product_variant_id == ^value)

      {key, value}, dynamic when key in ["product_variant_ids", :product_variant_ids] ->
        dynamic([f], ^dynamic and f.product_variant_id in ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  @doc """
  Creates a new inventory_item with the given attributes.

  ## Parameters

    - `organization_id`: The organization that owns the inventory_item.
    - `attrs`: A map containing the attributes to create the inventory_item with.

  ## Examples

      iex> Rms.Commerce.Products.create_inventory_item(1, %{})
      {:ok, %Rms.Commerce.Products.InventoryItem{}}

  ## Returns

  `{:ok, inventory_item}` if the inventory_item was successfully created, or
  `{:error, changeset}` if there was an error.

  """
  def create_inventory_item(organization_id, attrs) do
    %InventoryItem{organization_id: organization_id}
    |> InventoryItem.changeset(attrs)
    |> Repo.insert(
      conflict_target: [:location_id, :product_variant_id],
      on_conflict: {:replace, [:updated_at, :quantity]}
    )
  end

  @doc """
  Retrieves a inventory_item by its ID.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the inventory_item.
    - `inventory_item_id`: The ID of the inventory_item to retrieve.

  ## Examples

      iex> Rms.Commerce.Products.get_inventory_item!(2, 1)
      %Rms.Commerce.Products.InventoryItem{id: 1, organization_id: 2, ...}

  ## Raises

  `Ecto.NoResultsError` if the inventory_item with the given ID does not exist.

  """
  def get_inventory_item!(organization_id, inventory_item_id, preloads \\ []) do
    InventoryItem
    |> where(organization_id: ^organization_id, id: ^inventory_item_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Retrieves an inventory_item by its SKU.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the inventory_item.
    - `sku`: The SKU of the inventory_item to retrieve.

  ## Examples

      iex> Rms.Commerce.Products.get_inventory_item_by_sku(2, "SKU123")
      %Rms.Commerce.Products.InventoryItem{organization_id: 2, ...}

  ## Raises

  `Ecto.NoResultsError` if the inventory_item with the given SKU does not exist.

  """
  def get_inventory_item_by_sku!(organization_id, sku, preloads \\ []) do
    InventoryItem
    |> join(:inner, [ii], pv in assoc(ii, :product_variant))
    |> where([ii, pv], ii.organization_id == ^organization_id and pv.sku == ^sku)
    |> preload(^preloads)
    |> Repo.one!()
  end

  @doc """
  Deletes a inventory_item and associated cart items.

  ## Parameters

    - `inventory_item`: The inventory_item to delete.

  ## Examples

      iex> Rms.Commerce.Products.delete_inventory_item(%Rms.Commerce.Prodcuts.InventoryItem{})
      :ok

  ## Returns

  `:ok` if the inventory_item was successfully deleted, or
  `{:error, changeset}` if there was an error.

  """
  def delete_inventory_item(inventory_item) do
    Repo.delete(inventory_item)
  end

  @doc """
  Updates an existing inventory_item with the provided attributes.

  ## Parameters

    - `inventory_item`: The inventory_item to update.
    - `attrs`: A map containing the attributes to update.

  ## Examples

      iex> Rms.Commerce.Products.update_inventory_item(inventory_item, %{price: 15.00})
      {:ok, %Rms.Commerce.Products.Fullfillment{}}

  ## Returns

  `{:ok, inventory_item}` if the inventory_item was successfully updated, or
  `{:error, changeset}` if there was an error.

  """
  def update_inventory_item(inventory_item, attrs) do
    inventory_item
    |> InventoryItem.update_changeset(attrs)
    |> Repo.update()
  end

  def create_inventory_item_by_sku(organization_id, sku, attrs) do
    product_variant = get_product_variant_by_sku!(organization_id, sku)

    create_inventory_item(
      organization_id,
      Map.put(attrs, "product_variant_id", product_variant.id)
    )
  end

  def create_inventory_items_by_sku(organization_id, items) do
    # Get all SKUs from the items map
    skus = Map.keys(items)

    # Get all product variants for the SKUs in a single query
    variants =
      ProductVariant
      |> where(
        [pv],
        pv.organization_id == ^organization_id and pv.sku in ^skus and is_nil(pv.archived_at)
      )
      |> Repo.all()
      |> Map.new(fn variant -> {variant.sku, variant.id} end)

    # Prepare inventory items for bulk insert
    inventory_items =
      items
      |> Enum.reject(fn {sku, _attrs} -> is_nil(Map.get(variants, sku)) end)
      |> Enum.map(fn {sku, attrs} ->
        variant_id = Map.fetch!(variants, sku)

        %{
          organization_id: organization_id,
          product_variant_id: variant_id,
          location_id: attrs["location_id"],
          quantity: attrs["quantity"],
          inserted_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second),
          updated_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
        }
      end)

    Repo.insert_all(
      InventoryItem,
      inventory_items,
      conflict_target: [:location_id, :product_variant_id],
      on_conflict: {:replace, [:updated_at, :quantity]},
      returning: true
    )
  end

  def update_inventory_items_by_sku(organization_id, items) do
    # Get all SKUs from the items map
    skus = Map.keys(items)

    # Update all inventory items in a single query
    query =
      from i in InventoryItem,
        join: pv in ProductVariant,
        on: i.product_variant_id == pv.id,
        where:
          i.organization_id == ^organization_id and pv.sku in ^skus and is_nil(pv.archived_at),
        select: {pv.sku, i}

    # Get all existing inventory items to update
    existing_items = Repo.all(query) |> Map.new()

    # Update each inventory item individually with transaction
    items
    |> Enum.reduce(Ecto.Multi.new(), fn {sku, attrs}, multi ->
      case Map.get(existing_items, sku) do
        nil ->
          multi

        inventory_item ->
          Ecto.Multi.update(
            multi,
            {:inventory, sku},
            InventoryItem.update_changeset(inventory_item, attrs)
          )
      end
    end)
    |> Repo.transaction()
  end

  defp get_product_variant_by_sku!(organization_id, sku) do
    ProductVariant
    |> where([pv], pv.organization_id == ^organization_id and pv.sku == ^sku)
    |> Repo.one!()
  end

  def list_addons(organization_id, opts \\ []) do
    query = build_addons_query(organization_id, opts)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    query
    |> limit(^limit)
    |> Repo.all()
  end

  def paginated_addons(organization_id, opts \\ []) do
    query = build_addons_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    Repo.paginate(
      query,
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:inserted_at, :desc}, {:id, :desc}]
    )
  end

  defp build_addons_query(organization_id, opts) do
    preloads = Keyword.get(opts, :preloads, [])

    Addon
    |> preload(^preloads)
    |> where(organization_id: ^organization_id)
    |> where(^add_addons_filter_where(opts))
    |> distinct([a], desc: a.inserted_at, desc: a.id)
    |> order_by([a], desc: a.inserted_at, desc: a.id)
  end

  defp add_addons_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["name", :name] ->
        dynamic([a], ^dynamic and a.name == ^value)

      {key, value}, dynamic when key in ["type", :type] ->
        dynamic([a], ^dynamic and a.type == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def get_addon!(organization_id, addon_id, preloads \\ []) do
    Addon
    |> where(organization_id: ^organization_id, id: ^addon_id)
    |> preload(^preloads)
    |> Repo.one!()
  end

  def create_addon(organization_id, attrs) do
    %Addon{organization_id: organization_id}
    |> Addon.changeset(attrs)
    |> Repo.insert()
  end

  def delete_addon(addon) do
    Repo.delete(addon)
  end

  def create_gift_handler_configuration(organization_id, attrs) do
    %Rms.Commerce.Discounts.GiftHandlerConfigurations{organization_id: organization_id}
    |> Rms.Commerce.Discounts.GiftHandlerConfigurations.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:gifts]},
      conflict_target: [:product_variant_id, :organization_id],
      returning: true
    )
  end

  def delete_gift_handler_configuration(inventory_item) do
    Repo.delete(inventory_item)
  end

  def list_gift_handler_configuration(organization_id, _opts \\ []) do
    Rms.Commerce.Discounts.GiftHandlerConfigurations
    |> where([gh], gh.organization_id == ^organization_id)
    |> preload(product_variant: [gifts: [:product_variant]])
    |> Repo.all()
  end

  def import_gift_handler_configurations(organization_id, bundles) do
    all_skus =
      bundles
      |> Enum.flat_map(fn %{"Sku" => sku, "Bundle" => bundle_items} ->
        [sku | Enum.map(bundle_items, & &1["Sku"])]
      end)
      |> Enum.uniq()

    variants_map =
      ProductVariant
      |> where(
        [pv],
        pv.organization_id == ^organization_id and pv.sku in ^all_skus and is_nil(pv.archived_at)
      )
      |> Repo.all()
      |> Map.new(fn variant -> {variant.sku, variant} end)

    bundles
    |> Enum.reduce(Ecto.Multi.new(), fn bundle, multi ->
      prepare_gift_handler_configuration(multi, bundle, variants_map)
    end)
    |> Repo.transaction()
  end

  defp prepare_gift_handler_configuration(
         multi,
         %{"Bundle" => bundles, "Sku" => sku},
         variants_map
       ) do
    with {:ok, items} <- prepare_bundle_items(bundles, variants_map),
         {:ok, variant} <- get_variant_from_map(variants_map, sku) do
      add_gift_handler_to_multi(multi, variant, items)
    else
      {:error, error} -> Ecto.Multi.error(multi, sku, error)
    end
  end

  defp get_variant_from_map(variants_map, sku) do
    case Map.get(variants_map, sku) do
      nil -> {:error, "Sku #{sku} Not Found"}
      variant -> {:ok, variant}
    end
  end

  defp add_gift_handler_to_multi(multi, variant, items) do
    multi
    |> Ecto.Multi.run(
      "create_bundle_#{variant.sku}",
      fn _repo, _ ->
        create_gift_handler_configuration(variant.organization_id, %{
          product_variant_id: variant.id,
          gifts: items
        })
      end
    )
  end

  defp prepare_bundle_items(bundles, variants_map) do
    bundles
    |> Enum.reduce_while({:ok, []}, fn bundle, {:ok, acc} ->
      case Map.get(variants_map, bundle["Sku"]) do
        nil ->
          {:halt, {:error, "Sku #{bundle["Sku"]} Not Found"}}

        bundle_product_variant ->
          bundle_data = %{
            product_variant_id: bundle_product_variant.id,
            quantity: bundle["Quantity"],
            cost: bundle["Cost"]
          }

          {:cont, {:ok, [bundle_data | acc]}}
      end
    end)
    |> case do
      {:ok, items} -> {:ok, Enum.reverse(items)}
      error -> error
    end
  end
end
