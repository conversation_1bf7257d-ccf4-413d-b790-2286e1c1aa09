defmodule Rms.Commerce.Products.Addon do
  use Ecto.Schema
  import Ecto.Changeset

  schema "addons" do
    field :name, :string
    field :price, :decimal
    field :list_price, :decimal
    field :type, :string
    field :description, :string
    field :image_url, :string
    field :metadata, :map

    belongs_to :organization, Rms.Accounts.Organization

    has_many :addon_mappings, Rms.Integrations.AddonMapping

    field :archived_at, :utc_datetime

    timestamps()
  end

  @doc false
  def changeset(addon, attrs) do
    addon
    |> cast(attrs, [:name, :type, :price, :list_price, :image_url, :description, :metadata])
    |> put_price()
    |> validate_required_fields()
    |> assoc_constraint(:organization)
  end

  @doc false
  def archive_changeset(addon, now \\ DateTime.utc_now()) do
    now = DateTime.truncate(now, :second)
    change(addon, %{archived_at: now})
  end

  defp validate_required_fields(changeset) do
    id = fetch_field!(changeset, :id)

    if is_nil(id) do
      validate_required(changeset, [:name, :list_price])
    else
      changeset
    end
  end

  defp put_price(changeset) do
    price = fetch_field!(changeset, :price)

    if is_nil(price) do
      list_price = fetch_field!(changeset, :list_price)
      put_change(changeset, :price, list_price)
    else
      changeset
    end
  end
end
