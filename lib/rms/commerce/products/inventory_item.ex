defmodule Rms.Commerce.Products.InventoryItem do
  use Ecto.Schema

  import Ecto.Changeset

  schema "inventory_items" do
    field :quantity, :integer

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant
    belongs_to :location, Rms.Accounts.Location

    timestamps()
  end

  def changeset(inventory_item, attrs, _opts \\ []) do
    inventory_item
    |> cast(attrs, [:quantity, :product_variant_id, :location_id])
    |> validate_required([:quantity, :product_variant_id, :location_id])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:product_variant)
    |> assoc_constraint(:location)
    |> unique_constraint([:location_id, :product_variant_id],
      error_key: :product_variant_id,
      message: "has already been taken for this location_id"
    )
  end

  @doc false
  def update_changeset(inventory_item, attrs) do
    inventory_item
    |> cast(attrs, [:quantity])
    |> validate_required([:quantity])
  end
end
