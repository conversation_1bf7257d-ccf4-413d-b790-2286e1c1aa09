defmodule Rms.Commerce.Products.Product do
  use Ecto.Schema
  import Ecto.Changeset

  @allowed_product_types ~w(item subscription)

  schema "products" do
    field :name, :string
    field :ncm, :string
    field :product_type, :string
    field :tags, {:array, :string}

    belongs_to :organization, Rms.Accounts.Organization

    has_many :product_variants, Rms.Commerce.Products.ProductVariant,
      defaults: {Rms.Repo, :add_organization_id, []},
      on_replace: :mark_as_invalid,
      where: [archived_at: nil]

    has_one :product_sync_mapping, Rms.Integrations.ProductSyncMapping,
      on_replace: :mark_as_invalid,
      defaults: {Rms.Repo, :add_organization_id, []}

    timestamps()
  end

  def get_product_types() do
    @allowed_product_types
  end

  @doc false
  def changeset(product, attrs, opts \\ []) do
    product
    |> cast(attrs, [:name, :ncm, :tags])
    |> validate_required([:name, :organization_id])
    |> validate_inclusion(:product_type, @allowed_product_types)
    |> assoc_constraint(:organization)
    |> cast_product_variants(opts)
  end

  @doc false
  def update_changeset(product, attrs, opts \\ []) do
    product
    |> cast(attrs, [:name, :ncm, :product_type, :tags])
    |> cast_product_variants(opts)
  end

  defp cast_product_variants(changeset, opts) do
    name = fetch_field!(changeset, :name)
    source = Keyword.get(opts, :source)

    cast_assoc(changeset, :product_variants,
      with: fn product_variant, attrs ->
        Rms.Commerce.Products.ProductVariant.changeset(
          %{product_variant | name: name},
          attrs,
          source
        )
      end
    )
  end
end
