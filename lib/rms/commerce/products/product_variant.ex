defmodule Rms.Commerce.Products.ProductVariant do
  use Ecto.Schema
  import Ecto.Changeset

  defmodule VariationType do
    use Ecto.Schema

    embedded_schema do
      field :key, :string
      field :value, :string
      field :metadata, :map, default: %{}
    end
  end

  schema "product_variants" do
    field :name, :string
    field :list_price, :decimal
    field :price, :decimal
    field :sku, :string
    field :bar_code, :string
    field :image_urls, {:array, :string}
    field :tags, {:array, :string}
    embeds_many :variation_types, VariationType, on_replace: :delete

    belongs_to :product, Rms.Commerce.Products.Product
    belongs_to :organization, Rms.Accounts.Organization

    has_many :product_variant_mappings, Rms.Integrations.ProductVariantMapping
    has_many :inventory_item_mappings, Rms.Integrations.InventoryItemMapping

    has_one :gifts_bundle, Rms.Commerce.Discounts.GiftHandlerConfigurations

    field :sync_metadata, :map, default: %{}
    field :archived_at, :utc_datetime

    timestamps()
  end

  @doc false
  def changeset(product_variant, attrs, source \\ "internal") do
    product_variant
    |> cast(attrs, [
      :sku,
      :name,
      :bar_code,
      :list_price,
      :price,
      :image_urls,
      :tags,
      :archived_at,
      :sync_metadata
    ])
    |> cast_embed(:variation_types, with: &variation_type_changeset/2)
    |> put_price()
    |> put_name_suffix()
    |> validate_required_fields()
    |> assoc_constraint(:product)
    |> assoc_constraint(:organization)
    |> prepare_changes(fn changeset ->
      organization_id = get_field(changeset, :organization_id)
      sync_config = Rms.Integrations.get_product_sync_configuration(organization_id)

      changeset
      |> apply_product_sync_configuration(source, sync_config)
      |> put_sync_metadata(source)
    end)
  end

  defp apply_product_sync_configuration(changeset, _source, nil), do: changeset

  defp apply_product_sync_configuration(changeset, source, sync_config) do
    current_metadata = get_field(changeset, :sync_metadata) || %{}
    current_field_sources = get_in(current_metadata, ["field_sources"]) || %{}

    changes =
      Enum.reduce(changeset.changes, %{}, fn {field, value}, acc ->
        current_source = get_in(current_field_sources, [to_string(field), "source"])

        priorities =
          Map.get(sync_config.field_priorities, to_string(field), sync_config.default_priority)

        if should_sync_field?(source, current_source, priorities) do
          Map.put(acc, field, value)
        else
          acc
        end
      end)

    %{changeset | changes: changes}
  end

  @doc false
  def archive_changeset(product_variant, now \\ DateTime.utc_now()) do
    now = DateTime.truncate(now, :second)
    change(product_variant, %{archived_at: now})
  end

  defp put_price(changeset) do
    price = fetch_field!(changeset, :price)

    if is_nil(price) do
      list_price = fetch_field!(changeset, :list_price)
      put_change(changeset, :price, list_price)
    else
      changeset
    end
  end

  defp variation_type_changeset(variation_type, attrs) do
    variation_type
    |> cast(attrs, [:key, :value, :metadata])
    |> validate_required([:key, :value])
  end

  defp should_sync_field?(source, current_source, priorities) do
    current_source_idx = Enum.find_index(priorities, &(&1 == current_source))
    new_source_idx = Enum.find_index(priorities, &(&1 == source))

    cond do
      # If it's the same source, always allow the update
      source == current_source -> true
      # If we don't have the current source in priorities, allow update
      is_nil(current_source_idx) -> true
      # If we don't have the new source in priorities, deny update
      is_nil(new_source_idx) -> false
      # Allow update if new source has higher priority (lower index)
      true -> new_source_idx <= current_source_idx
    end
  end

  defp put_sync_metadata(changeset, source) do
    case changed_fields(changeset) do
      [] -> changeset
      changed_fields -> update_sync_metadata(changeset, changed_fields, source)
    end
  end

  defp changed_fields(changeset) do
    changeset.changes
    |> Map.keys()
    |> Enum.reject(fn field ->
      field in [:sync_metadata, :archived_at, :updated_at, :inserted_at]
    end)
  end

  defp update_sync_metadata(changeset, changed_fields, source) do
    current_metadata = get_field(changeset, :sync_metadata) || %{}
    current_field_sources = get_in(current_metadata, ["field_sources"]) || %{}

    updated_field_sources =
      Enum.reduce(changed_fields, current_field_sources, fn field, acc ->
        Map.put(acc, to_string(field), %{
          "source" => source,
          "updated_at" => DateTime.utc_now() |> DateTime.to_iso8601()
        })
      end)

    new_sync_metadata = Map.merge(current_metadata, %{"field_sources" => updated_field_sources})
    put_change(changeset, :sync_metadata, new_sync_metadata)
  end

  defp validate_required_fields(changeset) do
    id = fetch_field!(changeset, :id)

    if is_nil(id) do
      validate_required(changeset, [:name, :sku, :list_price])
    else
      changeset
    end
  end

  defp put_name_suffix(changeset) do
    name = calculate_name(changeset)

    put_change(changeset, :name, name)
  end

  defp calculate_name(changeset) do
    variation_types = fetch_field!(changeset, :variation_types)

    suffix = Enum.map_join(variation_types, " ", fn %{value: value} -> value end)

    name = fetch_field!(changeset, :name)

    String.trim("#{name} #{suffix}")
  end

  def event_payload(variant) do
    zero = Decimal.new("0")

    %{
      id: variant.id,
      product_id: variant.product_id,
      organization_id: variant.organization_id,
      name: variant.name,
      price: variant.price || zero,
      list_price: variant.list_price || zero,
      sku: variant.sku,
      bar_code: variant.bar_code
    }
  end
end
