defmodule Rms.Commerce.Workflows.ConfirmOrderCancelWorker do
  use Oban.Pro.Workers.Workflow, queue: :order_cancel_workflow

  require Logger

  alias Rms.Commerce.Orders
  alias Rms.Integrations.Orders, as: OrderIntegrations

  @impl Oban.Pro.Worker
  def process(%{args: %{"order_id" => order_id, "organization_id" => organization_id} = args}) do
    order = Orders.get_order!(organization_id, order_id, [:transaction])
    cancelation_endpoint = OrderIntegrations.get_active_cancelation_endpoint(organization_id)

    with {:ok, _} <-
           cancel_order(order, cancelation_endpoint, args["reason"] || "canceled by iglu") do
      Orders.cancel_order(order, %{status: "canceled"})
    end
  end

  defp cancel_order(%{transaction: %{status: status}}, _cancelation_endpoint, _reason)
       when status != "done" do
    {:ok, "confirm cancelation of order with no payment"}
  end

  defp cancel_order(_order, nil, _reason) do
    {:ok, "no active cancelation endpoint"}
  end

  defp cancel_order(order, %{endpoint: "https://vinco.com"}, _reason) do
    order = Rms.Repo.preload(order, fulfillments: [:fiscal_invoices])

    result =
      order.fulfillments
      |> Enum.reduce([], fn fulfillment, acc ->
        fulfillment.fiscal_invoices ++ acc
      end)
      |> Enum.filter(fn invoice ->
        invoice.status == "authorized" and is_nil(invoice.canceled_at)
      end)
      |> Enum.reduce_while({:ok, []}, fn invoice, {:ok, acc} ->
        case Rms.Integrations.Vinco.CancelFiscalInvoice.execute(invoice) do
          {:error, _} = error -> {:halt, error}
          {:ok, invoice} -> {:cont, [invoice | acc]}
        end
      end)

    case result do
      {:error, error} -> {:error, error}
      _ -> {:ok, "order canceled"}
    end
  end

  defp cancel_order(order, cancelation_endpoint, reason) do
    case OrderIntegrations.cancel_order(cancelation_endpoint, order.id, reason) do
      {:ok, _} ->
        {:ok, "order canceled"}

      {:error, {status, body}} ->
        {:error, "cancelation failed with status #{inspect(status)} and body #{inspect(body)}"}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
