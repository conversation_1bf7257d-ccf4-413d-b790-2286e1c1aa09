defmodule Rms.Commerce.Workflows.OrderCancellationWorkflow do
  use Oban.Pro.Workers.Workflow, queue: :order_cancel_workflow

  alias Oban.Pro.Workers.Workflow

  alias Rms.Commerce.Orders
  alias Rms.Commerce.Workflows.CancelFulfillmentWorker
  alias Rms.Commerce.Workflows.ConfirmOrderCancelWorker

  @impl true
  def process(%{args: %{"order_id" => order_id, "organization_id" => organization_id}}) do
    organization_id
    |> Orders.get_order!(order_id)
    |> Orders.update_order!(%{status: "canceling"})

    :ok
  end

  def build_workflow(order, opts \\ []) do
    order =
      Rms.Repo.preload(order, [:fulfillments])

    {cancel_reason, opts} = Keyword.pop(opts, :reason, "canceled by iglu")

    start_workflow_worker =
      new(%{
        order_id: order.id,
        organization_id: order.organization_id,
        reason: cancel_reason
      })

    opts
    |> Keyword.put_new(:workflow_name, "order_cancellation")
    |> Workflow.new()
    |> Workflow.add(:start_workflow, start_workflow_worker)
    |> cancel_fulfillment_steps(order, cancel_reason)
    |> cancel_order_step(order, cancel_reason)
  end

  defp cancel_fulfillment_steps(workflow, order, cancel_reason) do
    order.fulfillments
    |> Enum.reduce({workflow, []}, fn fulfillment, {acc_workflow, acc_names} ->
      cancel_fulfillment_worker =
        CancelFulfillmentWorker.new(%{
          fulfillment_id: fulfillment.id,
          organization_id: fulfillment.organization_id,
          reason: cancel_reason
        })

      workflow_name = :"cancel_fulfillment_#{fulfillment.id}"

      updated_workflow =
        Workflow.add(
          acc_workflow,
          workflow_name,
          cancel_fulfillment_worker,
          deps: [:start_workflow]
        )

      {updated_workflow, [workflow_name | acc_names]}
    end)
  end

  defp cancel_order_step({workflow, deps}, order, cancel_reason) do
    confirm_order_cancel_worker =
      ConfirmOrderCancelWorker.new(%{
        order_id: order.id,
        organization_id: order.organization_id,
        reason: cancel_reason
      })

    Workflow.add(
      workflow,
      :cancel_order,
      confirm_order_cancel_worker,
      deps: deps
    )
  end
end
