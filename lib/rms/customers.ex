defmodule Rms.Customers do
  @moduledoc """
  The Customers context.
  """

  import Ecto.Query, warn: false
  alias Rms.Repo

  alias Rms.Customers.Customer
  alias Rms.Addresses.Address
  alias Rms.Integrations.Customers, as: IntegrationCustomers

  @doc """
  Paginates the list of customers for a given organization.

  ## Parameters

    - `organization_id`: The ID of the organization that owns the customers.
    - `opts`: A keyword list of options. (optional)

  ## Options

    - `:after` - A cursor value for fetching results after this cursor.
    - `:before` - A cursor value for fetching results before this cursor.
    - `:limit` - The maximum number of customers to return. Defaults to 20.
    - `:preload` - A list of associations to preload.
    - `:search` - A search term to filter customers by name, email, or phone number.
    - `:status` - Filter customers by status.
    - `:document` - Filter customers by document (CPF/CNPJ).
    - `:document_type` - Filter customers by document type (e.g., "CPF" or "CNPJ").
    - `:email` - Filter customers by email.
    - `:primary_phone_number` - Filter customers by primary phone number.

  ## Examples

      iex> Rms.Customers.paginated_customers(1, limit: 10, after: "cursor_value")
      %{entries: [%Rms.Customers.Customer{}, ...], metadata: %{}}

  """
  def paginated_customers(organization_id, opts \\ []) do
    query = build_customers_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    total_count =
      case opts[:count] do
        "true" -> true
        "false" -> false
        count when is_boolean(count) -> count
        _ -> false
      end

    preloads = Keyword.get(opts, :preloads, [])

    query = query |> preload(^preloads)

    has_search? = Keyword.has_key?(opts, :search)

    {cursor_fields, order_by_fields} =
      if has_search? do
        {[{:name, :asc}, {:id, :desc}], [asc: :name, desc: :id]}
      else
        {[{:updated_at, :desc}, {:id, :desc}], [desc: :updated_at, desc: :id]}
      end

    query = query |> order_by(^order_by_fields)

    paginate_opts = [
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      include_total_count: total_count,
      cursor_fields: cursor_fields
    ]

    Repo.paginate(query, paginate_opts)
  end

  defp build_customers_query(organization_id, opts) do
    {search, opts} = Keyword.pop(opts, :search)

    organization_id
    |> build_base_query()
    |> apply_search_filter(search)
    |> apply_filters(opts)
  end

  defp build_base_query(organization_id) do
    from(c in Customer, where: c.organization_id == ^organization_id)
  end

  defp apply_search_filter(query, nil), do: query

  defp apply_search_filter(query, search) do
    query
    |> where(
      [c],
      (not is_nil(c.name) and
         fragment("unaccent(lower(?)) LIKE unaccent(lower(?))", c.name, ^"%#{search}%")) or
        ilike(c.document_plain, ^"%#{search}%") or
        ilike(c.email_plain, ^"%#{search}%") or
        ilike(c.primary_phone_number_plain, ^"%#{search}%")
    )
  end

  defp apply_filters(query, opts) do
    Enum.reduce(opts, query, fn
      {:status, status}, query ->
        where(query, [c], c.status == ^status)

      {:document, document}, query ->
        document_type = Keyword.get(opts, :document_type)
        where(query, [c], c.document_hash == ^document and c.document_type == ^document_type)

      {:email, email}, query ->
        where(query, [c], c.email_hash == ^email)

      {:primary_phone_number, phone}, query ->
        where(query, [c], c.primary_phone_number_hash == ^phone)

      _, query ->
        query
    end)
  end

  @doc """
  Returns the list of customers.

  ## Examples

      iex> list_customers(1)
      [%Customer{}, ...]

  """
  def list_customers(organization_id, opts \\ []) do
    active_endpoint = IntegrationCustomers.get_active_customer_endpoint(organization_id)
    fetch_customers(organization_id, active_endpoint, opts)
  end

  defp fetch_customers(organization_id, nil, opts) do
    query_customers_from_db(organization_id, opts)
  end

  defp fetch_customers(organization_id, active_endpoint, opts) do
    db_task = Task.async(fn -> query_customers_from_db(organization_id, opts) end)

    endpoint_task =
      Task.async(fn ->
        customers =
          IntegrationCustomers.list_customer(organization_id, opts)

        unique_fields = Map.get(active_endpoint, :unique_on)

        customers
        |> Enum.map(&IntegrationCustomers.import_customers(organization_id, &1, unique_fields))
        |> Enum.filter(fn
          {:ok, customer} when is_map(customer) ->
            true

          _ ->
            false
        end)
        |> Enum.map(&elem(&1, 1))
      end)

    [endpoint_task, db_task]
    |> Task.yield_many()
    |> Stream.filter(fn
      {_, {:ok, _}} -> true
      _ -> false
    end)
    |> Stream.flat_map(fn
      {_, {:ok, result}} when is_list(result) -> result
      _ -> []
    end)
    |> Enum.uniq_by(& &1.id)
  end

  defp query_customers_from_db(organization_id, opts) do
    base_query =
      where(Customer, organization_id: ^organization_id)
      |> preload(:addresses)

    query =
      Enum.reduce(opts, base_query, fn
        {:email, value}, acc ->
          where(acc, [c], c.email_hash == ^String.downcase(value))

        {:name, value}, acc ->
          where(acc, [c], fragment("? %> ?", c.name, ^String.downcase(value)))

        {:primary_phone_number, value}, acc ->
          where(acc, [c], c.primary_phone_number_hash == ^value)

        {:document, value}, acc ->
          document_type = opts[:document_type]

          where(acc, [c], c.document_hash == ^value and c.document_type == ^document_type)

        _, acc ->
          acc
      end)

    Repo.all(query)
  end

  @doc """
  Gets a single customer.

  Raises `Ecto.NoResultsError` if the Customer does not exist.

  ## Examples

      iex> get_customer!(1, 123)
      %Customer{}

      iex> get_customer!(1, 456)
      ** (Ecto.NoResultsError)

  """
  def get_customer!(organization_id, id) do
    Customer
    |> where(id: ^id, organization_id: ^organization_id)
    |> preload(:addresses)
    |> Repo.one!()
  end

  @doc """
  Creates a customer.

  ## Examples

      iex> create_customer(1, %{field: value})
      {:ok, %Customer{}}

      iex> create_customer(1, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_customer(organization_id, attrs \\ %{}, opts \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :create_customer,
      Customer.changeset(%Customer{organization_id: organization_id}, attrs)
    )
    |> Ecto.Multi.run(:emit_event, fn _repo, %{create_customer: customer} ->
      maybe_emit_customer_created_event(customer, opts)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{create_customer: customer}} -> {:ok, Repo.preload(customer, [:addresses])}
      {:error, _step, changeset, _changes} -> {:error, changeset}
    end
  end

  defp maybe_emit_customer_created_event(customer, opts) do
    if is_list(opts) && opts[:skip_events] do
      {:ok, nil}
    else
      Rms.Events.emit("customer.created", Customer.event_payload(customer))
    end
  end

  defp parse_timestamp(timestamp) when is_binary(timestamp) do
    case DateTime.from_iso8601(timestamp) do
      {:ok, datetime, _offset} ->
        datetime |> DateTime.shift_zone!("Etc/UTC") |> DateTime.to_naive()

      _ ->
        nil
    end
  end

  defp parse_timestamp(_), do: nil

  @doc """
  Imports a customer.

  The main difference with `create_customer/2` is that this function also creates a mapping.

  ## Parameters

    * `organization_id` - The ID of the organization to which the customer belongs.
    * `attrs` - A map of attributes for creating the customer. Defaults to an empty map.

  ## Examples

      iex> import_customer(1, %{name: "John Doe", email: "<EMAIL>"})
      {:ok, %Customer{}}

      iex> import_customer(1, %{name: "Invalid", email: ""})
      {:error, %Ecto.Changeset{}}

  ## Returns

    * `{:ok, customer}` - If the customer was successfully created and loaded with addresses.
    * `{:error, changeset}` - If there was an error creating the customer.

  """
  def import_customer(organization_id, attrs \\ %{}) do
    result =
      %Customer{organization_id: organization_id}
      |> Customer.changeset(attrs)
      |> Repo.insert()

    with {:ok, customer} <- result do
      {:ok, Repo.preload(customer, [:addresses])}
    end
  end

  def maybe_update_customer_and_addresses(
        %Customer{} = customer,
        attrs,
        [addresses_to_delete, addresses_to_create],
        opts
      ) do
    should_update =
      case Map.get(attrs, :updated_at) do
        nil ->
          true

        incoming_updated_at ->
          parsed_incoming_timestamp = parse_timestamp(incoming_updated_at)
          customer_updated_at = customer.updated_at

          is_nil(customer_updated_at) ||
            (not is_nil(parsed_incoming_timestamp) &&
               NaiveDateTime.compare(parsed_incoming_timestamp, customer_updated_at) == :gt)
      end

    if should_update do
      Ecto.Multi.new()
      |> delete_addresses(addresses_to_delete)
      |> insert_addresses(addresses_to_create, customer)
      |> Ecto.Multi.update(
        :update_customer,
        Customer.changeset(customer, attrs)
      )
      |> Ecto.Multi.run(:emit_event, fn _repo, %{update_customer: customer} ->
        maybe_emit_customer_updated_event(customer, opts)
      end)
      |> Repo.transaction()
      |> case do
        {:ok, %{update_customer: customer}} -> {:ok, Repo.preload(customer, [:addresses])}
        {:error, _step, changeset, _changes} -> {:error, changeset}
      end
    else
      {:ok, customer}
    end
  end

  defp maybe_emit_customer_updated_event(customer, opts) do
    if opts[:skip_events] do
      {:ok, nil}
    else
      Rms.Events.emit("customer.updated", Customer.event_payload(customer))
    end
  end

  @doc """
  Updates a customer.

  ## Examples

      iex> update_customer(customer, %{field: new_value})
      {:ok, %Customer{}}

      iex> update_customer(customer, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_customer(%Customer{} = customer, attrs, opts \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update_customer,
      Customer.changeset(customer, attrs)
    )
    |> Ecto.Multi.run(:emit_event, fn _repo, %{update_customer: customer} ->
      maybe_emit_customer_updated_event(customer, opts)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{update_customer: customer}} -> {:ok, Repo.preload(customer, [:addresses])}
      {:error, _step, changeset, _changes} -> {:error, changeset}
    end
  end

  defp delete_addresses(multi, addresses_to_delete) do
    Enum.reduce(Enum.with_index(addresses_to_delete), multi, fn {address, index}, acc_multi ->
      Ecto.Multi.delete(acc_multi, {:delete_address, index}, address)
    end)
  end

  defp insert_addresses(multi, addresses_to_create, customer) do
    Enum.reduce(Enum.with_index(addresses_to_create), multi, fn {address, index}, acc_multi ->
      changeset =
        Rms.Addresses.Address.changeset(
          %Rms.Addresses.Address{
            organization_id: customer.organization_id,
            customer_id: customer.id
          },
          address
        )

      Ecto.Multi.insert(acc_multi, {:insert_address, index}, changeset)
    end)
  end

  @doc """
  Deletes a customer.

  ## Examples

      iex> delete_customer(customer)
      {:ok, %Customer{}}

      iex> delete_customer(customer)
      {:error, %Ecto.Changeset{}}

  """
  def delete_customer(%Customer{} = customer) do
    Repo.delete(customer)
  end

  @doc """
  Get a customer address.

  ## Examples

      iex> get_customer_address(customer)
      {:ok, %Address{}}

      iex> get_customer_address(customer)
      {:error, %Ecto.Changeset{}}

  """
  def get_customer_address!(%Customer{} = customer, address_id) do
    Address
    |> where(
      id: ^address_id,
      organization_id: ^customer.organization_id,
      customer_id: ^customer.id
    )
    |> Repo.one!()
  end

  @doc """
  Get a customer address from given zip code.

  ## Examples

      iex> get_customer_address(customer, "12345678")
      {:ok, %Address{}}

      iex> get_customer_address_from_zip(customer, "78654321")
      {:error, :not_found}

  """
  def get_customer_address_from_zip(%Customer{} = customer, zip) do
    Address
    |> where(
      zip: ^zip,
      organization_id: ^customer.organization_id,
      customer_id: ^customer.id
    )
    |> Repo.one()
    |> case do
      nil -> {:error, :not_found}
      address -> {:ok, address}
    end
  end

  @doc """
  Deletes a customer address.

  ## Examples

      iex> delete_customer_address(customer, address_id)
      {:ok, %Customer{}}

      iex> delete_customer_address(customer, address_id)
      {:error, %Ecto.Changeset{}}

  """
  def delete_customer_address!(%Customer{} = customer, address_id) do
    address = get_customer_address!(customer, address_id)
    Repo.delete!(address)
  end

  @doc """
  Updates a customer address.

  ## Examples

      iex> update_customer_address(customer, addresses)
      {:ok, %Customer{}}

      iex> update_customer_address(customer, addresses)
      {:error, %Ecto.Changeset{}}

  """
  def update_customer_address!(%Customer{} = customer, addresses) do
    changeset = Customer.update_changeset(customer, %{addresses: addresses})

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update, changeset)
    |> Ecto.Multi.run(:emit_events, fn _repo, %{update: updated_customer} ->
      changeset
      |> Ecto.Changeset.fetch_change(:addresses)
      |> emit_address_events(updated_customer)
    end)
    |> Ecto.Multi.run(:emit_event, fn _repo, %{update: customer} ->
      Rms.Events.emit("customer.updated", Customer.event_payload(customer))
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_customer}} -> {:ok, updated_customer}
      {:error, _step, changeset, _changes} -> {:error, changeset}
    end
  end

  defp emit_address_events(:error, _updated_customer), do: {:ok, nil}

  defp emit_address_events({:ok, addresses}, updated_customer) do
    Enum.reduce_while(addresses, {:ok, []}, fn address, {:ok, acc} ->
      case emit_address_event(address, updated_customer) do
        {:ok, nil} -> {:cont, {:ok, acc}}
        {:ok, term} -> {:cont, {:ok, [term | acc]}}
        {:error, term} -> {:halt, {:error, term}}
      end
    end)
  end

  defp emit_address_event(address_changeset, updated_customer) do
    cond do
      address_changeset.action == :insert ->
        emit_address_created_event(address_changeset, updated_customer)

      address_changeset.action == :update and not Enum.empty?(address_changeset.changes) ->
        emit_address_updated_event(address_changeset, updated_customer)

      true ->
        {:ok, nil}
    end
  end

  defp emit_address_created_event(address_changeset, updated_customer) do
    address_payload =
      address_changeset
      |> Ecto.Changeset.apply_changes()
      |> Map.put(:customer_id, updated_customer.id)
      |> Rms.Addresses.Address.event_payload()

    Rms.Events.emit("address.created", address_payload)
  end

  defp emit_address_updated_event(address_changeset, updated_customer) do
    address_payload =
      address_changeset
      |> Ecto.Changeset.apply_changes()
      |> Map.put(:customer_id, updated_customer.id)
      |> Rms.Addresses.Address.event_payload()

    Rms.Events.emit("address.updated", address_payload)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking customer changes.

  ## Examples

      iex> change_customer(customer)
      %Ecto.Changeset{data: %Customer{}}

  """
  def change_customer(%Customer{} = customer, attrs \\ %{}) do
    Customer.changeset(customer, attrs)
  end
end
