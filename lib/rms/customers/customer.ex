defmodule Rms.Customers.Customer do
  use Ecto.Schema
  import Ecto.Changeset

  schema "customers" do
    field :name, :string, source: :plain_name
    field :document_type, :string
    field :document, Rms.Vault.EncryptedBinary
    field :document_hash, Cloak.Ecto.SHA256
    field :document_plain, :string

    field :email, Rms.Vault.EncryptedBinary
    field :email_hash, Cloak.Ecto.SHA256
    field :email_plain, :string

    field :primary_phone_number, Rms.Vault.EncryptedBinary
    field :primary_phone_number_hash, Cloak.Ecto.SHA256
    field :primary_phone_number_plain, :string

    field :birthdate, :date

    belongs_to :organization, Rms.Accounts.Organization

    has_many :customer_mappings, Rms.Integrations.CustomerSyncMapping

    has_many :addresses, Rms.Addresses.Address,
      on_replace: :delete_if_exists,
      defaults: {Rms.Repo, :add_organization_id, []}

    timestamps(type: :utc_datetime)
  end

  def update_changeset(customer, attrs) do
    customer
    |> cast(attrs, [:name])
    |> cast_assoc(:addresses)
  end

  @doc false
  def changeset(customer, attrs) do
    customer
    |> cast(
      attrs,
      [
        :name,
        :document_type,
        :document,
        :email,
        :primary_phone_number,
        :birthdate,
        :document_plain,
        :email_plain,
        :primary_phone_number_plain
      ]
    )
    |> cast_assoc(:addresses)
    |> put_plain_fields()
    |> put_hashed_fields()
    |> validate_identification_required()
    |> validate_required_if_document_present()
    |> update_change(:document_type, fn
      nil -> nil
      value -> String.upcase(value)
    end)
  end

  @doc false
  def shopify_import_changeset(customer, attrs) do
    customer
    |> cast(
      attrs,
      [
        :name,
        :document_type,
        :document,
        :email,
        :primary_phone_number,
        :birthdate,
        :document_plain,
        :email_plain,
        :primary_phone_number_plain
      ]
    )
    |> cast_assoc(:addresses)
    |> put_plain_fields()
    |> put_hashed_fields()
    # No validate_identification_required()
    |> validate_required_if_document_present()
    |> update_change(:document_type, fn
      nil -> nil
      value -> String.upcase(value)
    end)
  end

  defp validate_required_if_document_present(changeset) do
    if get_field(changeset, :document) && is_nil(get_field(changeset, :document_type)) do
      add_error(changeset, :document_type, "must be present if document is provided")
    else
      changeset
    end
  end

  defp put_hashed_fields(changeset) do
    downcase_email =
      with email when not is_nil(email) <- get_field(changeset, :email) do
        String.downcase(email)
      end

    changeset
    |> put_change(:document_hash, get_field(changeset, :document))
    |> put_change(:email_hash, downcase_email)
    |> put_change(:primary_phone_number_hash, get_field(changeset, :primary_phone_number))
  end

  defp put_plain_fields(changeset) do
    downcase_email = downcase_when_exists(changeset, :email)

    changeset
    |> put_change(:document_plain, get_field(changeset, :document))
    |> put_change(:email_plain, downcase_email)
    |> put_change(:primary_phone_number_plain, get_field(changeset, :primary_phone_number))
  end

  defp downcase_when_exists(changeset, key) when is_atom(key) do
    with value when not is_nil(value) <- get_field(changeset, key) do
      String.downcase(value)
    end
  end

  defp validate_identification_required(changeset) do
    identifying_fields = [:document, :email, :primary_phone_number]

    at_least_one_present? =
      identifying_fields
      |> Enum.map(fn identifying_field ->
        nil? =
          changeset
          |> get_field(identifying_field)
          |> is_nil()

        not nil?
      end)
      |> Enum.any?()

    if at_least_one_present?,
      do: changeset,
      else:
        add_error(
          changeset,
          :document,
          "at least one of the following fields must be present: document, email, primary_phone_number"
        )
  end

  def event_payload(%__MODULE__{} = customer) do
    customer = Rms.Repo.preload(customer, [:addresses])

    %{
      id: customer.id,
      organization_id: customer.organization_id,
      name: customer.name,
      document_type: customer.document_type,
      document: customer.document,
      email: customer.email,
      birthdate: customer.birthdate,
      primary_phone_number: customer.primary_phone_number,
      addresses:
        Enum.map(customer.addresses, fn address ->
          %{
            id: address.id,
            receiver_name: address.receiver_name,
            city_name: address.city_name,
            state: address.state,
            country_name: address.country_name,
            neighborhood: address.neighborhood,
            street: address.street,
            street_type: address.street_type,
            number: address.number,
            zip: address.zip,
            complement: address.complement
          }
        end)
    }
  end
end
