defmodule Rms.Demos do
  alias Rms.Integrations.VTEXCredential
  alias Rms.Repo

  def connect_vtex(organization_id, vtex_demo_credential) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:vtex_credential, fn _repo, _changes ->
      Rms.Integrations.create_vtex_credential(organization_id, vtex_demo_credential)
    end)
    |> Ecto.Multi.run(:vtex_import_catalog, fn _repo, _changes ->
      %{
        organization_id: organization_id,
        from: 1,
        to: 50
      }
      |> Rms.Workers.Demos.VTEXImportCatalog.new()
      |> Oban.insert()
    end)
    |> Repo.transaction()
    |> connect_vtex_normalizer()
  end

  defp connect_vtex_normalizer(result) do
    case result do
      {:ok, %{vtex_credential: credential}} ->
        {:ok, credential}

      {:error, :order, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  def get_vtex_credential!(organization_id) do
    Repo.get_by!(VTEXCredential, organization_id: organization_id)
  end
end
