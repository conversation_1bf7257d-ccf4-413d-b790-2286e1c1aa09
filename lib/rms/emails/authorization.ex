defmodule Rms.Emails.Authorization do
  alias Rms.Mailer
  import Swoosh.Email, only: [html_body: 2, text_body: 2]

  use Phoenix.Swoosh,
    template_root: "lib/rms_web/templates/emails",
    template_path: "authorization"

  def premail(email) do
    html = Premailex.to_inline_css(email.html_body)
    text = Premailex.to_text(email.html_body)

    email
    |> html_body(html)
    |> text_body(text)
  end

  def email(attrs) do
    staff = get_in(attrs, [:params, :cart, :staff]) || %{}
    code = get_in(attrs, [:params, :code]) || ""

    name = Map.get(staff, :name)

    new()
    |> to(attrs[:receivers])
    |> from({"iGlu", "<EMAIL>"})
    |> subject(
      "Envie o código #{code} para aprovar a solicitação de desconto de #{name |> String.split(" ") |> Enum.map_join(" ", &String.capitalize(&1))}"
    )
    |> render_body("authorization.html", attrs[:params])
    |> premail()
  end

  def send(attrs) do
    attrs
    |> email()
    |> Mailer.deliver()
  end
end
