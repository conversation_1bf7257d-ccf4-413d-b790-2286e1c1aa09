defmodule Rms.Emails.FiscalInvoice do
  use Phoenix.Swoosh,
    template_root: "lib/rms_web/templates/emails",
    template_path: "fiscal_invoice"

  alias Rms.Mailer
  import Swoosh.Email

  def send_invoice_email(invoice, email_address) do
    invoice
    |> prepare_email(email_address)
    |> Mailer.deliver()
  end

  def prepare_email(invoice, email_address) do
    {subject, {serie, number}, authorized_at} = get_attributes_from_xml(invoice)

    new()
    |> to(email_address)
    |> from({"iGlu", "<EMAIL>"})
    |> subject(subject)
    |> render_body("fiscal_invoice.html", %{
      invoice: invoice,
      serie: serie,
      number: number,
      authorized_at: authorized_at
    })
    |> attach_xml(invoice)
    |> premail()
  end

  defp get_attributes_from_xml(invoice) do
    default_subject = "Nota Fiscal #{invoice.invoice_number}"

    case Floki.parse_document(invoice.xml) do
      {:error, _} ->
        {default_subject, {nil, invoice.invoice_number},
         invoice.authorized_at || invoice.inserted_at}

      {:ok, xml_tree} ->
        number = xml_tree |> Floki.find("ide > nnf") |> Floki.text()
        serie = xml_tree |> Floki.find("ide > serie") |> Floki.text()

        authorized_at = xml_tree |> Floki.find("ide > dhemi") |> Floki.text()

        invoice_type = xml_tree |> Floki.find("ide > mod") |> Floki.text()
        seller_name = xml_tree |> Floki.find("emit > xnome") |> Floki.text()

        {email_subject(invoice_type, serie, number, seller_name) || default_subject,
         {serie, number}, authorized_at}
    end
  end

  defp email_subject("65", serie, number, seller_name),
    do: "NFC-e Numero: #{number} Serie: #{serie} da #{seller_name}"

  defp email_subject("55", serie, number, seller_name),
    do: "NF-e Numero: #{number} Serie: #{serie} da #{seller_name}"

  defp email_subject(_, serie, number, seller_name)
       when not is_nil(serie) and serie != "" and
              not is_nil(number) and number != "" and
              not is_nil(seller_name) and seller_name != "",
       do: "Nota Fiscal Numero: #{number} Serie: #{serie} da #{seller_name}"

  defp email_subject(_, _serie, _number, _seller_name),
    do: nil

  defp attach_xml(email, invoice) do
    attachment =
      Swoosh.Attachment.new(
        {:data, invoice.xml},
        filename: "invoice_#{invoice.invoice_number}.xml",
        content_type: "application/xml",
        type: :inline
      )

    attachment(email, attachment)
  end

  defp premail(email) do
    html = Premailex.to_inline_css(email.html_body)
    text = Premailex.to_text(email.html_body)

    email
    |> html_body(html)
    |> text_body(text)
  end
end
