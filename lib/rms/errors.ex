defmodule Rms.Errors do
  import Ecto.Query
  alias Rms.Repo
  alias Rms.Errors.FiscalInvoiceError
  alias Rms.Errors.OrderError

  def create_fiscal_invoice_error(fiscal_invoice, attrs) do
    %FiscalInvoiceError{
      organization_id: fiscal_invoice.organization_id,
      fiscal_invoice_id: fiscal_invoice.id
    }
    |> FiscalInvoiceError.changeset(attrs)
    |> Repo.insert()
  end

  def get_fiscal_invoice_error!(organization_id, fiscal_invoice_error_id) do
    Repo.get_by!(FiscalInvoiceError,
      organization_id: organization_id,
      id: fiscal_invoice_error_id
    )
  end

  def list_fiscal_invoice_error(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, [])

    FiscalInvoiceError
    |> where([is], is.organization_id == ^organization_id)
    |> where(^add_fiscal_invoice_error_filter_where(query_params))
    |> Repo.all()
  end

  def add_fiscal_invoice_error_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["ficsal_invoice_id", :ficsal_invoice_id] ->
        dynamic([is], ^dynamic and is.fiscal_invoice_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def create_order_error(organization_id, attrs) do
    %OrderError{organization_id: organization_id}
    |> OrderError.changeset(attrs)
    |> Repo.insert()
  end

  def get_order_error!(organization_id, order_error_id) do
    Repo.get_by!(OrderError, organization_id: organization_id, id: order_error_id)
  end

  def list_order_error(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, [])

    OrderError
    |> where([is], is.organization_id == ^organization_id)
    |> where(^add_order_error_filter_where(query_params))
    |> Repo.all()
  end

  def add_order_error_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["order_id", :order_id] ->
        dynamic([is], ^dynamic and is.order_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end
end
