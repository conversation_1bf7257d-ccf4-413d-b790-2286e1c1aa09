defmodule Rms.Errors.FiscalInvoiceError do
  use Ecto.Schema
  import Ecto.Changeset

  schema "fiscal_invoice_errors" do
    field :reason, :string
    field :stacktrace, :string
    field :attempt, :integer
    field :status, :string

    belongs_to :fiscal_invoice, Rms.Fiscal.FiscalInvoice
    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  def changeset(fiscal_invoice_error, attrs, _opts \\ []) do
    attrs =
      if is_atom(attrs[:status]), do: Map.update(attrs, :status, nil, &to_string/1), else: attrs

    fiscal_invoice_error
    |> cast(attrs, [
      :reason,
      :stacktrace,
      :fiscal_invoice_id,
      :status
    ])
    |> validate_required([
      :reason,
      :fiscal_invoice_id
    ])
    |> foreign_key_constraint(:fiscal_invoice_id)
    |> foreign_key_constraint(:organization_id)
  end
end
