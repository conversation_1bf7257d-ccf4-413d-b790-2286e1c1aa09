defmodule Rms.Errors.LoggingError do
  require OpenTelemetry.Tracer

  def execute({multi_name, error, _changes}) do
    message = <PERSON>.encode!(%{step: multi_name, error: error})
    OpenTelemetry.Tracer.set_status(:error, message)
  end

  def execute(error) when is_map(error) do
    message = Jason.encode!(error)
    OpenTelemetry.Tracer.set_status(:error, message)
  end

  def execute(error) when is_binary(error) do
    message = Jason.encode!(%{stacktrace: nil, reason: error})
    OpenTelemetry.Tracer.set_status(:error, message)
  end

  def execute(error) do
    message = inspect(error)
    OpenTelemetry.Tracer.set_status(:error, message)
  end
end
