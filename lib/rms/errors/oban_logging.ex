defmodule Rms.Errors.ObanLogging do
  @config Application.compile_env(:rms, __MODULE__)
  @enabled_workers @config[:slack][:enabled_workers]

  require Logger

  def log_error(job) do
    Task.start(fn ->
      message =
        """
        [#{__MODULE__}] [#{parse_env()}] Job attempt #{job.attempt} failed
        worker: #{job.worker}
        job_id: #{job.id}
        args: #{inspect(job.args)}
        last error: #{inspect(List.last(job.errors))}
        """

      if job.worker in @enabled_workers do
        Rms.Messaging.Slack.send_message(message)
      end

      Logger.error(message)
    end)
  end

  defp parse_env() do
    case Application.get_env(:rms, :environment) do
      :dev -> "STAGING"
      :prod -> "PRODUCTION"
    end
  end
end
