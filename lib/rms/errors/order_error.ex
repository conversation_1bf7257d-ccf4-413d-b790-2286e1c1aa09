defmodule Rms.Errors.OrderError do
  use Ecto.Schema
  import Ecto.Changeset

  schema "order_errors" do
    field :error, :string
    field :stacktrace, :string

    belongs_to :order, Rms.Commerce.Orders.Order
    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  def changeset(order_error, attrs, _opts \\ []) do
    order_error
    |> cast(attrs, [
      :error,
      :stacktrace,
      :order_id
    ])
    |> validate_required([
      :error,
      :order_id
    ])
    |> foreign_key_constraint(:order_id)
    |> foreign_key_constraint(:organization_id)
  end
end
