defmodule Rms.Events.Handler do
  @moduledoc """
  Oban Worker that's responsible for multiplexing a event into many handlers
  """
  use Oban.Pro.Worker

  require Logger

  alias Rms.Events.Handlers

  Module.register_attribute(__MODULE__, :event_handlers, accumulate: true, persist: false)
  Module.register_attribute(__MODULE__, :event_handler_map, accumulate: false, persist: true)

  @event_handlers {"order.paid", Handlers.SyncOrder}
  @event_handlers {"order.paid", Handlers.SyncAddonOrder}
  @event_handlers {"order.paid", Handlers.EmitInvoiceOrder}
  @event_handlers {"order.paid", Handlers.CompleteReverseFulfillment}

  @event_handlers {"order.created", Handlers.CreateFulfillmentOrder}

  @event_handlers {"order.imported", Handlers.ImportSalesInvoice}

  @event_handlers {"reverse_fulfillment.completed", Handlers.CreateReturnFiscal}

  @event_handlers {"reverse_fulfillment.completed", Handlers.SyncShopifyReturn}

  @event_handlers {"customer.created", Handlers.SyncCustomer}
  @event_handlers {"customer.updated", Handlers.UpdateCustomer}
  @event_handlers {"address.created", Handlers.SyncAddress}
  @event_handlers {"address.updated", Handlers.SyncAddress}

  @event_handlers {"payment.canceled", Handlers.RefundPayment}

  @event_handlers {"invoice.issued", Handlers.UpdateEcommcerOrder}

  @event_handlers {"invoice.issued", Handlers.ErpInvoiceInsert}

  @event_handlers {"return_invoice.issued", Handlers.ErpReturnInvoiceInsert}

  @event_handlers {"invoice.canceled", Handlers.ErpInvoiceInsert}

  @event_handlers {"product.updated", Handlers.NotifySvix}
  @event_handlers {"product.created", Handlers.NotifySvix}
  @event_handlers {"product.created", Handlers.AmaroNcmUpdate}
  @event_handlers {"invoice.issued", Handlers.NotifySvix}
  @event_handlers {"return_invoice.issued", Handlers.NotifySvix}
  @event_handlers {"transfer_invoice.issued", Handlers.NotifySvix}
  @event_handlers {"order.paid", Handlers.NotifySvix}
  @event_handlers {"subscription.paid", Handlers.NotifySvix}
  @event_handlers {"order.canceled", Handlers.NotifySvix}
  @event_handlers {"customer.created", Handlers.NotifySvix}
  @event_handlers {"invoice.canceled", Handlers.NotifySvix}
  @event_handlers {"reverse_fulfillment.completed", Handlers.NotifySvix}
  @event_handlers {"reverse_fulfillment.cancelled", Handlers.NotifySvix}
  @event_handlers {"reverse_fulfillment.created", Handlers.NotifySvix}
  @event_handlers {"staff.created", Handlers.NotifySvix}
  @event_handlers {"staff.updated", Handlers.NotifySvix}

  @event_handler_map (for {event, handler} <- @event_handlers,
                          reduce: %{} do
                        acc ->
                          current_handlers = Map.get(acc, event, [])
                          Map.put(acc, event, [handler | current_handlers])
                      end)

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"event_name" => event_name} = args}) do
    @event_handler_map
    |> Map.get(event_name, [])
    |> Enum.map(& &1.new(args))
    |> then(&Oban.insert_all(Rms.Events, &1))

    :ok
  end
end
