defmodule Rms.Events.Handlers.AmaroNcmUpdate do
  use Oban.Pro.Worker, queue: :event_handler, max_attempts: 5

  alias Rms.Commerce.Products
  alias Rms.Integrations.Erp.Amaro

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"resource" => resource}}) do
    org_id = resource["organization_id"]
    product_id = resource["product_id"]
    sku = resource["sku"]

    with "amaro" <- get_erp_integration(org_id),
         %{ncm: ncm} = product <- get_product(product_id, org_id),
         true <- is_nil(ncm) || ncm == "",
         {:ok, amaro_data} <- fetch_amaro_ncm(org_id, sku),
         {:ok, _updated} <- update_product_ncm(product, amaro_data["ncm"]) do
      :ok
    else
      erp when erp != "amaro" and is_binary(erp) -> {:ok, "not amaro integration"}
      nil -> {:ok, "erp integration not defined"}
      false -> {:ok, "product already has an ncm code"}
      {:error, reason} -> {:error, reason}
      _ -> {:error, "unable to update ncm code"}
    end
  end

  defp get_erp_integration(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "erp_integration") do
      %{value: %{"data" => erp_integration}} -> erp_integration
      _ -> nil
    end
  end

  defp get_product(product_id, org_id) do
    Products.get_product!(org_id, product_id)
  end

  defp fetch_amaro_ncm(organization_id, sku) do
    client = Amaro.client(organization_id)
    Amaro.get_product_taxes(client, sku)
  end

  defp update_product_ncm(product, ncm) do
    Products.update_product(product, %{ncm: ncm})
  end
end
