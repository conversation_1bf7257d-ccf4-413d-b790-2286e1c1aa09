defmodule Rms.Events.Handlers.CompleteReverseFulfillment do
  @moduledoc """
  Event handler that completes reverse fulfillments when an order is paid and updates order status based on returns.
  """

  use Oban.Pro.Worker, queue: :event_handler

  import Ecto.Query

  alias Rms.Commerce.Fulfillments
  alias Rms.Commerce.Orders
  alias Rms.Repo

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"event_name" => "order.paid", "resource" => order}}) do
    transaction_id = get_in(order, ["transaction", "id"])
    organization_id = order["organization_id"]

    processing_reverse_fulfillments =
      get_processing_reverse_fulfillments(transaction_id, organization_id)

    affected_orders = get_affected_orders(processing_reverse_fulfillments)

    Repo.transaction_with(fn ->
      with :ok <- complete_reverse_fulfillments(processing_reverse_fulfillments) do
        Orders.update_order_statuses(affected_orders, organization_id)
      end
    end)
  end

  defp get_processing_reverse_fulfillments(transaction_id, organization_id) do
    from(t in Rms.Finance.Transaction,
      join: p in assoc(t, :payments),
      join: icp in assoc(p, :iglu_credit_payment),
      join: ic in assoc(icp, :iglu_credit),
      join: rf in assoc(ic, :reverse_fulfillment),
      where:
        t.id == ^transaction_id and
          p.method == "return_credit" and
          rf.status == "processing" and
          rf.organization_id == ^organization_id,
      distinct: [rf.id],
      select: rf
    )
    |> Repo.all()
  end

  defp get_affected_orders(processing_reverse_fulfillments) do
    from(rf in Fulfillments.ReverseFulfillment,
      join: rfl in assoc(rf, :line_items),
      join: li in assoc(rfl, :line_item),
      join: f in assoc(li, :fulfillment),
      where: rf.id in ^Enum.map(processing_reverse_fulfillments, & &1.id),
      distinct: true,
      select: f.order_id
    )
    |> Repo.all()
  end

  defp complete_reverse_fulfillments(processing_reverse_fulfillments) do
    Enum.reduce_while(processing_reverse_fulfillments, :ok, fn rf, _acc ->
      case Fulfillments.complete_reverse_fulfillment(rf) do
        {:ok, _} -> {:cont, :ok}
        {:error, error} -> {:halt, {:error, error}}
      end
    end)
  end
end
