defmodule Rms.Events.Handlers.CreateFulfillmentOrder do
  use Oban.Pro.Worker,
    queue: :event_handler,
    unique: [states: [:available, :scheduled, :executing, :retryable], fields: [:args, :worker]],
    max_attempts: 1

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => order_id,
            "organization_id" => organization_id
          }
        }
      }) do
    Rms.Commerce.Fulfillments.CreateFulfillments.execute(order_id, organization_id)
    |> validate_response()
  end

  defp validate_response(response) do
    case response do
      {:error, multi_name, error, changes} -> {:error, {multi_name, error, changes}}
      rest -> rest
    end
  end
end
