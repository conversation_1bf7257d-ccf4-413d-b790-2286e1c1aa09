defmodule Rms.Events.Handlers.CreateReturnFiscal do
  use Oban.Pro.Worker,
    queue: :event_handler,
    max_attempts: 1

  alias Rms.Fiscal.FiscalSettings
  alias Rms.Repo

  import Ecto.Query

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => reverse_fulfillment_id,
            "organization_id" => organization_id
          }
        }
      }) do
    case get_orders(reverse_fulfillment_id) do
      {:ok, orders} -> process_orders(orders, organization_id, reverse_fulfillment_id)
    end
  end

  defp process_orders(orders, organization_id, reverse_fulfillment_id) do
    orders
    |> Enum.reduce_while(:ok, fn original_order, _acc ->
      original_order
      |> process_single_order(organization_id, reverse_fulfillment_id)
    end)
  end

  defp process_single_order(original_order, organization_id, reverse_fulfillment_id) do
    case get_fiscal_settings(organization_id, original_order.location_id) do
      %FiscalSettings{handle_return: true, environment: env} ->
        process_return_workflow(reverse_fulfillment_id, organization_id, env)

      %FiscalSettings{handle_return: false} ->
        {:cont, :fiscal_return_disabled}

      nil ->
        {:halt, {:error, :fiscal_settings_not_found}}
    end
  end

  defp process_return_workflow(reverse_fulfillment_id, organization_id, env) do
    case Rms.Fiscal.Workflows.ReturnWorkflow.process(%{
           args: %{
             "reverse_fulfillment_id" => reverse_fulfillment_id,
             "organization_id" => organization_id,
             "env" => env
           }
         }) do
      :ok -> {:cont, :ok}
      error -> {:halt, error}
    end
  end

  defp get_orders(reverse_fulfillment_id) do
    orders =
      Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
        reverse_fulfillment_id
      )

    {:ok, orders}
  end

  defp get_fiscal_settings(organization_id, location_id) do
    FiscalSettings
    |> where([fs], fs.organization_id == ^organization_id and fs.location_id == ^location_id)
    |> Repo.one()
  end
end
