defmodule Rms.Events.Handlers.EmitInvoiceOrder do
  use Oban.Pro.Worker,
    queue: :event_handler,
    unique: [states: [:available, :scheduled, :executing, :retryable], fields: [:args, :worker]],
    max_attempts: 3

  import Ecto.Query

  require OpenTelemetry.Tracer

  alias Rms.Fiscal.InvoiceCreator
  alias Rms.Finance.Transaction

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => order_id
          }
        }
      }) do
    attributes = [
      {"var.order_id", order_id}
    ]

    OpenTelemetry.Tracer.with_span "#{__MODULE__}.perform/1", %{
      attributes: attributes
    } do
      with {:ok, transaction} <- find_transaction(order_id),
           {:ok, invoices} <- InvoiceCreator.execute(transaction) do
        {:ok, invoices}
      else
        {:error, error} ->
          Rms.Errors.LoggingError.execute(error)
          {:error, error}

        error ->
          Rms.Errors.LoggingError.execute("Undefined error")
          {:error, error}
      end
    end
  end

  defp find_transaction(order_id) do
    transaction =
      Transaction
      |> where([t], t.order_id == ^order_id and t.status == ^"done")
      |> Rms.Repo.one()

    case transaction do
      %Transaction{} = transaction ->
        {:ok, transaction}

      _ ->
        {:error, %{reason: "Can not find a transaction", stacktrace: "#{__MODULE__}"}}
    end
  end
end
