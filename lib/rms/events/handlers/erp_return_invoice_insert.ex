defmodule Rms.Events.Handlers.ErpReturnInvoiceInsert do
  use Oban.Pro.Worker,
    queue: :event_handler,
    unique: [states: [:available, :scheduled, :executing, :retryable], fields: [:args, :worker]],
    max_attempts: 3

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => invoice_id,
            "organization_id" => organization_id
          }
        }
      }) do
    erp_integration = get_erp_integration(organization_id)
    insert_invoice(organization_id, invoice_id, erp_integration)
  end

  defp insert_invoice(organization_id, invoice_id, "amaro") do
    Rms.Integrations.Erp.Amaro.FiscalInvoice.Insert.execute(
      organization_id,
      invoice_id
    )
  end

  defp insert_invoice(organization_id, invoice_id, "sallve") do
    Rms.Integrations.Erp.Sallve.FiscalInvoice.Insert.execute_return(organization_id, invoice_id)
  end

  defp insert_invoice(_organization_id, _invoice_id, erp_integration) do
    {:discard, "insert invoice in erp not defined for #{erp_integration}"}
  end

  defp get_erp_integration(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "erp_integration") do
      %{value: %{"data" => erp_integration}} -> erp_integration
      _ -> nil
    end
  end
end
