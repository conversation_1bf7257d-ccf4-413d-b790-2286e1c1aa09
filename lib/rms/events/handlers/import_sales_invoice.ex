defmodule Rms.Events.Handlers.ImportSalesInvoice do
  use Oban.Pro.Worker, queue: :invoice_import, max_attempts: 10

  alias Rms.Commerce.Orders
  alias Rms.Fiscal

  def process(%{args: %{"resource" => resource}}) do
    resource["organization_id"]
    |> Orders.get_order!(resource["id"])
    |> Fiscal.import_sales_invoice_for_order()
    |> case do
      {:error, :no_fulfillments_without_fiscal_invoice} ->
        {:ok, :no_fulfillments_without_fiscal_invoice}

      result ->
        result
    end
  end
end
