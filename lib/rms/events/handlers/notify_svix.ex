defmodule Rms.Events.Handlers.NotifySvix do
  use Oban.Pro.Worker, queue: :webhooks, max_attempts: 10

  alias Rms.Integrations.Svix

  def process(%Oban.Job{id: id, args: %{"event_name" => event_name, "resource" => resource}}) do
    org = Rms.Accounts.get_organization!(resource["organization_id"])

    client = Svix.client()

    with {:error, :not_found} <-
           Svix.send_message(client, org.org_id, %{
             eventType: event_name,
             eventId: inspect(id),
             payload: Map.put(resource, "event_name", event_name)
           }) do
      {:discard, "source not found"}
    end
  end
end
