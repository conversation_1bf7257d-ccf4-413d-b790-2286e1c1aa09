defmodule Rms.Events.Handlers.RefundPayment do
  require Logger
  use Oban.Pro.Worker, queue: :event_handler

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => payment_id,
            "organization_id" => organization_id
          }
        }
      }) do
    payment = Rms.Finance.get_payment!(organization_id, payment_id, transaction: [:order])
    Rms.Finance.refund_to_provider(payment)
  end

  def process(_job) do
    :ok
  end
end
