defmodule Rms.Events.Handlers.SyncAddonOrder do
  use Oban.Pro.Worker, queue: :event_handler, max_attempts: 1
  alias Rms.Integrations.VTEX
  alias Oban.Pro.Workers.Workflow

  @impl Oban.Pro.Worker
  def process(%Job{args: %{"resource" => resource}}) do
    with {:ok, ecommerce} <- fetch_ecommmerce_settings(resource) do
      resource
      |> sync_addon_order(ecommerce)
    end
  end

  defp fetch_ecommmerce_settings(%{"organization_id" => organization_id}) do
    case Rms.Settings.get_organization_setting(organization_id, "ecommerce") do
      %{value: %{"data" => ecommerce}} ->
        {:ok, ecommerce}

      _ ->
        {:discard, "no ecommerce found for #{organization_id}"}
    end
  end

  defp sync_addon_order(%{"addons" => _, "organization_id" => organization_id} = order, "vtex") do
    addons_with_delivery =
      Rms.Commerce.Carts.SimulateCart.fetch_delivery_option_for_addons(organization_id, order)

    Workflow.new(workflow_name: "sync_addon_order_vtex")
    |> Workflow.add(
      :create_order,
      VTEX.CreateAddonOrderWorker.new(%{
        addons: addons_with_delivery,
        order_id: order["id"],
        organization_id: organization_id
      })
    )
    |> Workflow.add(
      :send_payment_information,
      VTEX.SendPaymentInformationWorker.new(%{
        order_id: order["id"],
        organization_id: organization_id
      }),
      deps: [:create_order]
    )
    |> Workflow.add(
      :authorize_transaction,
      VTEX.AuthorizeTransactionWorker.new(%{
        order_id: order["id"],
        organization_id: organization_id
      }),
      deps: [:send_payment_information]
    )
    |> Workflow.add(
      :notify_subscription_paid,
      Rms.Workers.NotifySubscriptionPaidWorker.new(%{
        order_id: order["id"],
        organization_id: organization_id
      }),
      deps: [:authorize_transaction]
    )
    |> Oban.insert_all()

    :ok
  end

  defp sync_addon_order(_, ecommerce), do: {:discard, "invalid ecommerce #{inspect(ecommerce)}"}
end
