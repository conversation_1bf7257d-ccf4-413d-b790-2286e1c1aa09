defmodule Rms.Events.Handlers.SyncAddress do
  use Oban.Pro.Worker

  alias Rms.Integrations
  alias Rms.Integrations.VTEX

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"event_name" => event_name} = args}) do
    case event_name do
      "address.created" -> handle_create(args["resource"])
      "address.updated" -> handle_update(args["resource"])
      _ -> {:error, :invalid_event}
    end
  end

  defp handle_create(%{"organization_id" => organization_id} = address) do
    with {:ok, vtex_customer_id} <- get_vtex_customer_id(organization_id, address["customer_id"]) do
      vtex_credential = Integrations.get_vtex_credential!(organization_id)
      client = VTEX.client(vtex_credential)

      VTEX.create_document(client, "AD", build_address_params(address, vtex_customer_id))
    end
  end

  defp handle_update(%{"organization_id" => organization_id} = address) do
    with {:ok, vtex_customer_id} <-
           get_vtex_customer_id(organization_id, address["customer_id"]),
         vtex_credential = Integrations.get_vtex_credential!(organization_id),
         client = VTEX.client(vtex_credential),
         {:ok, vtex_addresses} <-
           VTEX.search_customer_address(
             client,
             vtex_customer_id,
             ~w(postalCode street number id)
           ),
         {:ok, vtex_address} <- find_matching_address(vtex_addresses, address) do
      VTEX.update_document(
        client,
        "AD",
        vtex_address["id"],
        build_address_params(address, vtex_customer_id)
      )
    end
  end

  defp get_vtex_customer_id(organization_id, customer_id) do
    case Integrations.get_customer_sync_mapping_by_customer(organization_id, customer_id, "vtex") do
      nil -> {:discard, :vtex_customer_not_found}
      mapping -> {:ok, mapping.external_id}
    end
  end

  defp find_matching_address(vtex_addresses, address) do
    case Enum.find(vtex_addresses, &match_address?(&1, address)) do
      nil -> {:error, :vtex_address_not_found}
      vtex_address -> {:ok, vtex_address}
    end
  end

  defp match_address?(vtex_address, address) do
    vtex_address["postalCode"] == address["zip"] &&
      vtex_address["street"] == address["street"] &&
      vtex_address["number"] == address["number"]
  end

  defp build_address_params(address, vtex_customer_id) do
    %{
      userId: vtex_customer_id,
      addressName: "IGLU-#{address["id"]}",
      receiverName: address["receiver_name"],
      city: address["city_name"],
      state: address["state"],
      country: address["country_name"],
      neighborhood: address["neighborhood"],
      street: address["street"],
      streetType: address["street_type"],
      number: address["number"],
      postalCode: address["zip"],
      complement: address["complement"]
    }
  end
end
