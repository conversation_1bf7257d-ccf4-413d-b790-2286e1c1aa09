defmodule Rms.Events.Handlers.SyncCustomer do
  use Oban.Pro.Worker, queue: :event_handler
  alias Rms.Integrations.VTEX
  alias Oban.Pro.Workers.Workflow

  @impl Oban.Pro.Worker
  def process(%Job{args: %{"resource" => resource}}) do
    with {:ok, service} <- fetch_customer_service_settings(resource) do
      case service do
        "vtex" ->
          resource
          |> vtex_workflow()
          |> Oban.insert_all()

          :ok

        "shopify" ->
          resource
          |> shopify_workflow()
          |> Oban.insert_all()

          :ok

        _ ->
          {:discard, "invalid service #{inspect(service)}"}
      end
    end
  end

  defp fetch_customer_service_settings(%{"organization_id" => organization_id}) do
    case Rms.Settings.get_organization_setting(organization_id, "customer_sync_service") do
      %{value: %{"data" => customer_service}} ->
        {:ok, customer_service}

      _ ->
        case Rms.Settings.get_connected_ecommerce(organization_id) do
          "shopify" -> {:ok, "shopify"}
          _ -> {:discard, "no customer service found for #{organization_id}"}
        end
    end
  end

  def shopify_workflow(%{"organization_id" => organization_id, "id" => customer_id} = _resource) do
    Workflow.new(workflow_name: "customer_sync_shopify")
    |> Workflow.add(
      :create_customer,
      Rms.Integrations.Shopify.CreateCustomerWorker.new(%{
        organization_id: organization_id,
        customer_id: customer_id
      })
    )
  end

  @doc false
  def vtex_workflow(%{"organization_id" => organization_id} = resource) do
    Workflow.new(workflow_name: "customer_sync_vtex")
    |> Workflow.add(
      :create_customer,
      VTEX.Customers.CreateCustomerWorker.new(%{
        customer: resource,
        organization_id: organization_id
      })
    )
    |> Workflow.add(
      :create_addresses,
      VTEX.Customers.CreateAddressWorker.new(%{
        customer: resource,
        organization_id: organization_id
      }),
      deps: [:create_customer]
    )
    |> Workflow.add(
      :notify_customer,
      VTEX.Customers.CreateMappingWorker.new(%{
        customer: resource,
        organization_id: organization_id
      }),
      deps: [:create_customer]
    )
  end
end
