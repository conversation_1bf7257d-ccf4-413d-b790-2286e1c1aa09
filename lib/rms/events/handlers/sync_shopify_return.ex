defmodule Rms.Events.Handlers.SyncShopifyReturn do
  @moduledoc """
  Event handler that syncs completed reverse fulfillments to Shopify as returns.
  """
  use Oban.Pro.Worker, queue: :event_handler

  require Logger

  alias Rms.Commerce.Fulfillments
  alias Rms.Integrations.Shopify.Returns.Workflow

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{"event_name" => "reverse_fulfillment.completed", "resource" => resource}
      }) do
    reverse_fulfillment_id = resource["id"]
    organization_id = resource["organization_id"]

    # Create LD context for feature flag check
    ld_context_map = :ldclient_context.new(to_string(organization_id), "organization_id")
    ld_context = :ldclient_context.new_from_map(ld_context_map)

    case :ldclient.variation("shopify-return-sync", ld_context, false) do
      true ->
        # Get the reverse fulfillment with line items and their associated fulfillments
        reverse_fulfillment =
          Fulfillments.get_reverse_fulfillment_by_id!(organization_id, reverse_fulfillment_id,
            preload: [line_items: [line_item: :fulfillment]]
          )

        # Extract Shopify fulfillments and create workflows
        jobs = start_shopify_return_workflows(reverse_fulfillment)

        {:ok, jobs}

      false ->
        Logger.info(
          "skipping shopify return sync for reverse fulfillment #{reverse_fulfillment_id} as feature flag is disabled"
        )

        :ok
    end
  end

  defp start_shopify_return_workflows(reverse_fulfillment) do
    # Group line items by fulfillment and only keep Shopify fulfillments
    shopify_fulfillments = extract_shopify_fulfillments(reverse_fulfillment)

    # Create a workflow for each Shopify fulfillment
    shopify_fulfillments
    |> Enum.map(&create_return_workflow(reverse_fulfillment, &1))
    |> List.flatten()
  end

  defp extract_shopify_fulfillments(reverse_fulfillment) do
    reverse_fulfillment.line_items
    |> Enum.map(fn rf_line_item ->
      # Get the fulfillment from the line item if it exists
      case rf_line_item.line_item.fulfillment do
        %{ecommerce: "shopify"} = fulfillment -> fulfillment
        _ -> nil
      end
    end)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq_by(& &1.id)
  end

  defp create_return_workflow(reverse_fulfillment, fulfillment) do
    reverse_fulfillment
    |> Workflow.build_workflow(fulfillment.id)
    |> Oban.insert_all()
  end
end
