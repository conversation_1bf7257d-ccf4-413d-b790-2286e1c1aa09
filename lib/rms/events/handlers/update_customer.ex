alias Rms.Repo

defmodule Rms.Events.Handlers.UpdateCustomer do
  use Oban.Pro.Worker, queue: :event_handler

  @impl Oban.Pro.Worker
  def process(%Job{args: %{"resource" => %{"organization_id" => organization_id, "id" => id}}}) do
    organization_ecommerce = Rms.Settings.get_connected_ecommerce(organization_id)

    customer =
      Rms.Customers.get_customer!(organization_id, id)
      |> Repo.preload([:customer_mappings, :addresses])

    maybe_update_external_customer(customer, organization_ecommerce)
  end

  defp maybe_update_external_customer(customer, "shopify") do
    case find_external_customer_id(customer, "shopify") do
      nil ->
        {:ok, nil}

      shopify_id ->
        shopify_credential = Rms.Integrations.get_shopify_credential!(customer.organization_id)

        client =
          Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

        {first_name, last_name} = split_name(customer.name)

        Rms.Integrations.Shopify.update_customer(client, %{
          id: shopify_id,
          email: customer.email,
          firstName: first_name,
          lastName: last_name,
          phone: customer.primary_phone_number,
          addresses:
            Enum.map(customer.addresses, &transform_address(&1, "shopify"))
            |> Enum.reject(&is_nil/1)
        })
    end
  end

  defp maybe_update_external_customer(_customer, _source) do
    {:ok, nil}
  end

  defp transform_address(address, "shopify") do
    {first_name, last_name} = split_name(address.receiver_name)

    %{
      firstName: first_name,
      lastName: last_name,
      address1: (address.street || "") <> " " <> (address.number || ""),
      address2: address.complement,
      city: address.city_name,
      zip: address.zip,
      provinceCode: address.state,
      countryCode: get_country_code(address.country_name)
    }
  end

  defp transform_address(_address, _source), do: nil

  defp get_country_code("BRA"), do: "BR"
  defp get_country_code("BR"), do: "BR"
  defp get_country_code("Brazil"), do: "BR"
  defp get_country_code(_), do: nil

  defp find_external_customer_id(customer, source) do
    case Enum.find(customer.customer_mappings, fn mapping ->
           mapping.source == source
         end) do
      nil -> nil
      mapping -> mapping.external_id
    end
  end

  defp split_name(name) when is_binary(name) do
    case String.split(name, " ", parts: 2) do
      [first_name] -> {first_name, ""}
      [first_name, last_name] -> {first_name, last_name}
      _ -> {"", ""}
    end
  end

  defp split_name(_), do: {"", ""}
end
