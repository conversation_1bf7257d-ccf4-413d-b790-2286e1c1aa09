defmodule Rms.Events.Handlers.UpdateEcommcerOrder do
  use Oban.Pro.Worker,
    queue: :event_handler,
    unique: [states: [:available, :scheduled, :executing, :retryable], fields: [:args, :worker]],
    max_attempts: 5

  alias Rms.Integrations
  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.Build

  @backoff_time 1 * 60

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "resource" => %{
            "id" => invoice_id,
            "organization_id" => organization_id
          }
        }
      }) do
    if send_to_shopify_enabled?(organization_id) do
      update_ecommerce_order(
        organization_id,
        invoice_id,
        Rms.Settings.get_connected_ecommerce(organization_id)
      )
    else
      {:discard, :send_to_shopify_disabled}
    end
  end

  @impl Worker
  def backoff(%Job{attempt: _attempt, unsaved_error: _unsaved_error}) do
    @backoff_time
  end

  defp send_to_shopify_enabled?(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "send_order_to_shopify") do
      %{value: %{"data" => value}} -> value
      _ -> true
    end
  end

  defp update_ecommerce_order(organization_id, invoice_id, "shopify") do
    invoice =
      Rms.Fiscal.get_fiscal_invoice!(organization_id, invoice_id)
      |> Rms.Repo.preload([
        :serie,
        invoice_payments: [
          payment: [
            transaction: [
              order: [
                :staff,
                fulfillments: [
                  order: [
                    :staff,
                    :discounts,
                    location: [:location_mappings],
                    transaction: [:payments]
                  ]
                ],
                location: [:location_mappings]
              ]
            ]
          ]
        ]
      ])

    invoice_payment =
      Enum.find(invoice.invoice_payments, fn invoice_payment ->
        invoice_payment.payment.status == "settled"
      end)

    transaction = invoice_payment.payment.transaction

    shopify_credential = Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    transaction.order.fulfillments
    |> Enum.filter(fn fulfillment -> fulfillment.shipping_method in ["in-store"] end)
    |> Enum.reduce_while({:ok, []}, fn fulfillment, {:ok, acc} ->
      with {:ok, external_id} <- get_ecommerce_order_id(fulfillment),
           {:ok, params} <-
             build_update_params(
               external_id,
               invoice,
               fulfillment
             ),
           {:ok, _updated_shopify_order} <-
             Integrations.Shopify.update_order(shopify_client, params) do
        {:cont, {:ok, ["ecomerce order updated #{external_id}" | acc]}}
      else
        {:snooze, time} ->
          {:halt, {:snooze, time}}

        error ->
          {:halt, error}
      end
    end)
  end

  defp update_ecommerce_order(_organization_id, _order_id, ecommerce) do
    {:discard, "does noe update #{ecommerce} orders after invoice issued"}
  end

  defp get_ecommerce_order_id(fulfillment) do
    if String.starts_with?(fulfillment.external_reference || "", "gid://shopify/Order/") do
      {:ok, fulfillment.external_reference}
    else
      {:snooze, 20}
    end
  end

  defp build_update_params(external_id, invoice, fulfillment) do
    {
      :ok,
      %{
        id: external_id,
        customAttributes: Build.execute(fulfillment, invoice)
      }
    }
  end
end
