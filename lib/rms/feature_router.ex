defmodule Rms.FeatureRouter do
  @type features :: %{
          required(:atom) => %{flows: [atom]}
        }

  @type flows :: %{
          :atom => fun
        }

  @callback features :: features

  @doc """
  Routes the given feature to the appropriate flow.

  ## Parameters

    - `feature_name`: The name of the feature to route.
    - `attrs`: A map of attributes associated with the feature.
    - `flows`: A map containing the flow functions for the feature.

  ## Returns

  This function does not return a value. It triggers the execution of the flow function.

  ## Examples

    iex> route(:create_order, %{organization_id: 1, fulfillments: []}, %{create_order: fn -> :ok end})
    :ok

  Raises an error if the feature or flow is not implemented.
  """
  @callback route(
              feature_name :: atom,
              attrs :: map,
              flows :: flows
            ) :: any

  @spec call_flow!(flows_map :: flows, flow_key :: atom) :: any
  def call_flow!(flows_map, flow_key) do
    Map.get(flows_map, flow_key).()
  end

  @doc """
  Validates that the given feature exists and that all the given flows are
  known flows for that feature.

  ## Parameters

    - `module`: The module which implements the `features/0` callback.
    - `feature_name`: The name of the feature to validate.
    - `flows`: A map of flows to validate.

  ## Returns

    - `:ok` if the feature exists and all the given flows are known.
    - Raises an error if the feature does not exist or if some flow is unknown.
    - Raises an error if some feature flow is not implemented.

  ## Examples

    iex> validate!(MyFeatureRouter, :create_order, %{create_order: fn -> :ok end})
    :ok

  """
  @spec validate!(atom, atom, flows) :: :ok | no_return
  def validate!(module, feature_name, implemented_flows) do
    features = module.features()

    check_feature_exists!(features, feature_name)

    unless all_implemented_flows?(features[feature_name], implemented_flows) do
      feature_flows = features[feature_name].flows

      raise """
      Some unimplemented flow for #{feature_name}.
      Expected: #{inspect(feature_flows)}, got: #{inspect(implemented_flows)}
      """
    end
  end

  defp check_feature_exists!(features, feature_name) do
    unless Map.has_key?(features, feature_name) do
      raise "Feature not defined on. Got #{feature_name} on #{inspect(Map.keys(features))}"
    end
  end

  def all_implemented_flows?(feature, implemented_flows) do
    implemented_flows_keys = Map.keys(implemented_flows)

    all_implemented? = Enum.all?(feature.flows, fn fname -> fname in implemented_flows_keys end)
    all_functions? = Enum.all?(implemented_flows, fn {_k, v} -> is_function(v) end)
    all_implemented? && all_functions?
  end
end
