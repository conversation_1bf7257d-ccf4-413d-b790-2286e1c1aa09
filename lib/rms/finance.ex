defmodule Rms.Finance do
  import Ecto.Query

  alias Rms.Finance.CardToken
  alias Rms.Finance.GiftCards
  alias Rms.Finance.IgluCredit
  alias Rms.Finance.IgluCreditPayment
  alias Rms.Finance.Payment
  alias Rms.Finance.Subscription
  alias Rms.Finance.Transaction
  alias Rms.Integrations.ExternalPaymentReference
  alias Rms.Repo

  require Logger

  @gift_promo_capture_type Application.compile_env(
                             :rms,
                             Rms.Integrations.GiftPromo,
                             []
                           )[:capture_type]
  @gift_promo_provider Application.compile_env(:rms, Rms.Integrations.GiftPromo, [])[:provider]
  @doc """
  Creates a Transaction.

  ## Examples

      iex> create_transaction(attrs)
      {:ok, %Transaction{}}

      iex> create_transaction(invalid_attrs)
      {:error, %Ecto.Changeset{}}

  """
  def create_transaction(organization_id, attrs) do
    %Transaction{organization_id: organization_id}
    |> Transaction.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Closes a Transaction – changing its status to `done`

  ## Examples

      iex> close_transaction(transaction)
      {:ok, %Transaction{status: "done"}}

      iex> close_transaction(invalid_transaction)
      {:error, %Ecto.Changeset{}}

  """
  def close_transaction(%Transaction{} = transaction, opts \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:transaction, fn _repo, _changes ->
      transaction
      |> Repo.preload([:payments, :order])
      |> Transaction.done_changeset(opts)
      |> Repo.update()
    end)
    |> create_subscriptions()
    |> Repo.transaction()
    |> create_subscription_normalizer()
  end

  defp create_subscriptions(multi) do
    Ecto.Multi.merge(multi, fn %{transaction: transaction} ->
      transaction = Repo.preload(transaction, :order)

      Enum.reduce(transaction.order.addons, Ecto.Multi.new(), &process_addon(&1, &2, transaction))
    end)
  end

  defp process_addon(
         %{id: id, metadata: %{"content" => %{"vtex.subscription.key.frequency" => frequency}}} =
           addon,
         acc_multi,
         transaction
       ) do
    Ecto.Multi.run(acc_multi, {:create_subscription, id}, fn _repo, _changes ->
      attrs = %{
        value: addon.price,
        frequency: frequency,
        customer_id: transaction.order.customer_id
      }

      create_subscription(transaction.organization_id, attrs)
    end)
  end

  defp process_addon(_, acc_multi, _transaction), do: acc_multi

  defp create_subscription_normalizer(result) do
    case result do
      {:ok, %{transaction: transaction}} ->
        {:ok, transaction}

      {:error, _, changeset, _} ->
        {:error, changeset}
    end
  end

  @doc """
  Cancels a Transaction – changing its status to `cancelled`

  ## Examples

      iex> cancel_transaction(transaction)
      {:ok, %Transaction{status: "cancelled"}}

      iex> cancel_transaction(invalid_transaction)
      {:error, %Ecto.Changeset{}}

  """
  def cancel_transaction(%Transaction{} = transaction) do
    transaction
    |> Repo.preload([:payments, :order])
    |> Transaction.cancel_changeset()
    |> Repo.update()
  end

  @doc """
  Fetches a Transaction by its ID and Organization ID, raising an error if not found.

  ## Examples

      iex> get_transaction!(1, 1)
      %Transaction{}

      iex> get_transaction!(1, nonexistent_id)
      ** (Ecto.NoResultsError)

  """
  def get_transaction!(organization_id, id, opts \\ []) do
    preloads = Keyword.get(opts, :preloads, [])

    Transaction
    |> preload(^preloads)
    |> Repo.get_by!(id: id, organization_id: organization_id)
  end

  @doc """
  Creates a Payment.

  ## Examples

      iex> create_payment(location_id, attrs)
      {:ok, %Payment{}}

      iex> create_payment(location_id, invalid_attrs)
      {:error, %Ecto.Changeset{}}

  """

  def create_gift_card(location_id, payment) do
    with %{method: "gift_card", status: "pending"} <- payment,
         %{store_id: store_id, access_key: access_key, username: username, password: password} <-
           Rms.Integrations.get_gift_promo_credentials!(payment.organization_id, location_id),
         client =
           Rms.Integrations.GiftPromo.client(%{username: username, password: password}),
         {:ok, %{"sucesso" => "1", "cartaoStatus" => "Ativo", "cartaoSaldo" => card_value}} <-
           Rms.Integrations.GiftPromo.check_gift_card(
             client,
             %{
               store_id: store_id,
               access_key: access_key,
               card_number: payment.metadata["card_number"],
               card_pin: payment.metadata["card_pin"]
             }
           ) do
      decimal_card_value = Decimal.new(String.replace(card_value, ",", "."))

      case Decimal.compare(decimal_card_value, payment.amount) do
        :lt ->
          %{status: "canceled"}

        _ ->
          case Rms.Integrations.GiftPromo.redeem_gift_card(client, %{
                 store_id: store_id,
                 access_key: access_key,
                 card_number: payment.metadata["card_number"],
                 card_pin: payment.metadata["card_pin"],
                 value: payment.amount,
                 nsu: payment.id,
                 capture_type:
                   Application.get_env(:rms, Rms.Integrations.GiftPromo, [])[:capture_type],
                 provider: Application.get_env(:rms, Rms.Integrations.GiftPromo, [])[:provider]
               }) do
            {:ok,
             %{
               "codigoErro" => "0",
               "mensagem" => "Transação OK",
               "sucesso" => "1",
               "nsuHost" => nsu_host,
               "autorizacao" => authorization
             }} ->
              %{
                status: "settled",
                metadata:
                  Map.merge(payment.metadata, %{
                    "nsu_host" => nsu_host,
                    "authorization" => authorization
                  })
              }

            _ ->
              %{status: "canceled"}
          end
      end
    else
      {:error, error} ->
        Logger.error("Error creating gift card payment: #{inspect(error)}")
        {:error, error}

      _ ->
        Logger.error("Canceling gift card payment: #{inspect(payment)}")

        %{status: "canceled"}
    end
  end

  defp redeem_from_provider(%{metadata: %{"provider" => "shopify"}} = pending_payment) do
    case GiftCards.durable_redeem_from_shopify(pending_payment, timeout: 10_000) do
      {:ok, settled_payment} -> {:ok, settled_payment}
      {:cancel, {canceled_payment, _}} -> {:ok, canceled_payment}
      {:error, _} -> {:ok, pending_payment}
    end
  end

  defp redeem_from_provider(%{metadata: %{"provider" => "gift_promo"}} = payment) do
    payment = Rms.Repo.preload(payment, transaction: [:order])

    with %{} = attrs <-
           create_gift_card(payment.transaction.order.location_id, payment) do
      payment
      |> Payment.changeset(attrs)
      |> Repo.update()
    end
  end

  defp redeem_from_provider(payment) do
    {:ok, payment}
  end

  def create_payment(organization_id, %{"method" => "gift_card"} = attrs) do
    with {:ok, payment} <-
           %Payment{organization_id: organization_id}
           |> Payment.changeset(attrs)
           |> Repo.insert() do
      redeem_from_provider(payment)
    end
  end

  def create_payment(
        organization_id,
        %{"method" => "credit_card", "card_token" => card_token} = attrs
      ) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :payment,
      Payment.changeset(%Payment{organization_id: organization_id}, attrs)
    )
    |> Ecto.Multi.run(:card_token, fn _repo, %{payment: payment} ->
      payment = Repo.preload(payment, transaction: [:order])

      %CardToken{
        organization_id: organization_id,
        payment_id: payment.id,
        customer_id: payment.transaction.order.customer_id
      }
      |> CardToken.changeset(%{token: card_token})
      |> Repo.insert()
    end)
    |> Repo.transaction()
    |> create_payment_normalizer()
  end

  def create_payment(
        organization_id,
        %{"method" => "payment_link", "status" => "settled"} = attrs
      ) do
    %Payment{organization_id: organization_id}
    |> Payment.changeset(attrs)
    |> Repo.insert()
  end

  def create_payment(
        organization_id,
        %{"method" => "payment_link", "transaction_id" => transaction_id} = attrs
      ) do
    transaction =
      get_transaction!(organization_id, transaction_id, preloads: [:customer, order: [:staff]])

    with {:ok, payment_link, {partner, external_id}} <-
           Rms.Integrations.create_payment_link(
             organization_id,
             transaction.order,
             transaction.customer
           ) do
      params = update_payment_link_params(attrs, payment_link, partner, external_id)

      %Payment{organization_id: organization_id}
      |> Payment.changeset(params)
      |> Repo.insert()
    end
  end

  def create_payment(organization_id, attrs) do
    %Payment{organization_id: organization_id}
    |> Payment.changeset(attrs)
    |> Repo.insert()
  end

  defp update_payment_link_params(params, payment_link, partner, external_id) do
    default_metadata = %{"link" => payment_link}

    params
    |> Map.update("metadata", default_metadata, fn metadata ->
      Map.merge(metadata || %{}, default_metadata)
    end)
    |> Map.put("external_payment_reference", %{
      "partner" => partner,
      "external_id" => external_id
    })
  end

  defp create_payment_normalizer(result) do
    case result do
      {:ok, %{payment: payment}} ->
        {:ok, payment}

      {:error, :payment, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end

  def update_payment(payment, attrs) do
    payment
    |> Payment.changeset(attrs)
    |> Repo.update()
  end

  def update_payment_metadata(payment, attrs) do
    payment
    |> Payment.update_metadata_changeset(attrs)
    |> Repo.update()
  end

  def refund_to_provider(%{metadata: %{"provider" => "gift_promo"}} = settled_payment) do
    %{
      id: payment_id,
      organization_id: organization_id,
      metadata: %{
        "card_number" => card_number,
        "card_pin" => card_pin,
        "nsu_host" => nsu_host,
        "authorization" => authorization
      }
    } = settled_payment

    with %{store_id: store_id, access_key: access_key, username: username, password: password} <-
           Rms.Integrations.get_gift_promo_credentials!(
             organization_id,
             settled_payment.transaction.order.location_id
           ),
         client <- Rms.Integrations.GiftPromo.client(%{username: username, password: password}) do
      Rms.Integrations.GiftPromo.refund_transaction(client, %{
        store_id: store_id,
        access_key: access_key,
        card_number: card_number,
        card_pin: card_pin,
        authorization: authorization,
        nsu: payment_id,
        nsu_host: nsu_host,
        capture_type: @gift_promo_capture_type,
        provider: @gift_promo_provider
      })
    end
  end

  def refund_to_provider(%{metadata: %{"provider" => "shopify"}} = settled_payment) do
    case GiftCards.durable_refund_to_shopify(settled_payment) do
      {:ok, canceled_payment} -> {:ok, canceled_payment}
      {:error, _} -> {:ok, settled_payment}
    end
  end

  def refund_to_provider(payment) do
    {:ok, payment}
  end

  @doc """
  Cancels a payment and updates its status to "canceled".

  For "return_credit" payments, this will also cancel the associated reverse fulfillment
  and update linked iglu credits to "cancelled" status.

  ## Examples

      iex> cancel_payment(payment)
      {:ok, %Payment{status: "canceled"}}

      iex> cancel_payment(invalid_payment)
      {:error, %Ecto.Changeset{}}

  """

  def cancel_payment(payment), do: cancel_payment(payment, %{})

  def cancel_payment(%{method: "return_credit"} = payment, _receipt_metadata) do
    payment = Repo.preload(payment, iglu_credit: :reverse_fulfillment)
    fulfillment = payment.iglu_credit.reverse_fulfillment

    with {:ok, _} <- Rms.Commerce.Fulfillments.cancel_reverse_fulfillment(fulfillment) do
      {:ok, Repo.reload!(payment)}
    end
  end

  def cancel_payment(payment, receipt_metadata) do
    payment = Repo.preload(payment, transaction: :order)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:updated_payment, Payment.cancel_changeset(payment, receipt_metadata))
    |> Ecto.Multi.run(:emit_event, fn _repo, %{updated_payment: payment} ->
      Rms.Events.emit("payment.canceled", Payment.event_payload(payment))
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{updated_payment: payment}} ->
        {:ok, payment}

      {:error, _, changeset, _} ->
        Logger.error("Error canceling payment: #{inspect(changeset)}")
        {:error, changeset}
    end
  end

  def get_payment!(organization_id, id, preload \\ []) do
    Payment
    |> preload(^preload)
    |> Repo.get_by!(id: id, organization_id: organization_id)
  end

  def get_payment_by_external_reference(partner, external_id) do
    query =
      ExternalPaymentReference
      |> join(:inner, [epr], p in assoc(epr, :payment))
      |> where([epr, p], epr.partner == ^partner)
      |> where([epr, p], epr.external_id == ^external_id)
      |> select([epr, p], p)

    payment =
      query
      |> Repo.one()
      |> Repo.preload(transaction: [:order])

    case payment do
      %Payment{} ->
        {:ok, payment}

      _ ->
        {:error, :not_found}
    end
  end

  def get_payment_by_external_reference_and_organization(organization_id, partner, external_id) do
    query =
      ExternalPaymentReference
      |> join(:inner, [epr], p in assoc(epr, :payment))
      |> where([epr, p], epr.partner == ^partner)
      |> where([epr, p], epr.external_id == ^external_id)
      |> where([_epr, p], p.organization_id == ^organization_id)
      |> select([_epr, p], p)

    payment =
      query
      |> Repo.one()
      |> Repo.preload(transaction: [:order])

    case payment do
      %Rms.Finance.Payment{} ->
        {:ok, payment}

      _ ->
        {:error, :not_found}
    end
  end

  def sync_payment(payment, params) do
    with {:ok, updated_payment} <- update_payment(payment, params),
         %Transaction{} = transaction <-
           Repo.preload(updated_payment.transaction, [:payments, :order]),
         true <- Transaction.done?(transaction),
         {:ok, _} <- close_transaction(transaction) do
      {:ok, :updated_payment}
    else
      false ->
        {:ok, :updated_payment}

      error ->
        error
    end
  end

  def create_subscription(organization_id, attrs) do
    %Subscription{organization_id: organization_id}
    |> Subscription.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates an iglu credit.
  """
  def create_iglu_credit(organization_id, attrs \\ %{}) do
    %IgluCredit{organization_id: organization_id}
    |> IgluCredit.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets an iglu credit by ID and organization ID.
  """
  def get_iglu_credit!(organization_id, id),
    do: Repo.get_by!(IgluCredit, id: id, organization_id: organization_id)

  @doc """
  Lists iglu credits for a customer.
  """
  def list_customer_iglu_credits(organization_id, customer_id, opts \\ []) do
    status = Keyword.get(opts, :status)

    IgluCredit
    |> where([c], c.organization_id == ^organization_id)
    |> where([c], c.customer_id == ^customer_id)
    |> maybe_filter_by_status(status)
    |> order_by([c], desc: c.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets the total available credit for a customer.
  """
  def get_customer_available_credit(organization_id, customer_id) do
    now = DateTime.utc_now()

    IgluCredit
    |> where([c], c.organization_id == ^organization_id)
    |> where([c], c.customer_id == ^customer_id)
    |> where([c], c.status == "available")
    |> where([c], c.expires_at > ^now)
    |> select([c], sum(c.amount))
    |> Repo.one()
    |> Decimal.new()
  end

  def update_iglu_credit_used_amount(%IgluCredit{} = credit, amount) do
    new_amount = Decimal.add(credit.used_amount, amount)

    credit
    |> IgluCredit.update_used_amount_changeset(%{used_amount: new_amount})
    |> Repo.update()
  end

  @doc """
  Updates an iglu credit status.
  """
  def update_iglu_credit_status(%IgluCredit{} = credit, status) do
    credit
    |> IgluCredit.update_changeset(%{status: status})
    |> Repo.update()
  end

  @doc """
  Expires all credits that have passed their expiration date.
  """
  def expire_iglu_credits(organization_id) do
    now = DateTime.utc_now()

    IgluCredit
    |> where([c], c.organization_id == ^organization_id)
    |> where([c], c.status == "available")
    |> where([c], c.expires_at <= ^now)
    |> Repo.update_all(set: [status: "expired"])
  end

  @doc """
  Gets the remaining balance of an iglu credit by summing all associated payments.
  """
  def get_iglu_credit_remaining_balance(%IgluCredit{} = credit) do
    payments_total =
      Payment
      |> join(:inner, [p], icp in IgluCreditPayment, on: p.id == icp.payment_id)
      |> where([p, icp], icp.iglu_credit_id == ^credit.id)
      |> select([p, _], sum(p.amount))
      |> Repo.one()
      |> Kernel.||("0")
      |> Decimal.new()

    Decimal.sub(credit.amount, payments_total)
  end

  defp maybe_filter_by_status(query, nil), do: query

  defp maybe_filter_by_status(query, status) do
    where(query, [c], c.status == ^status)
  end

  @doc """
  Creates a payment using an iGlu credit and tracks the credit usage.
  """
  def create_iglu_credit_payment(organization_id, credit, payment_attrs) do
    credit_payment = %IgluCreditPayment{
      iglu_credit_id: credit.id,
      organization_id: organization_id
    }

    credit_payment
    |> IgluCreditPayment.changeset(%{payment: payment_attrs})
    |> Repo.insert()
  end
end
