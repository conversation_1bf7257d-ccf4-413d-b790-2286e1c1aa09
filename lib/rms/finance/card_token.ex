defmodule Rms.Finance.CardToken do
  use Ecto.Schema
  import Ecto.Changeset

  schema "card_tokens" do
    field :token, Rms.Vault.EncryptedBinary

    belongs_to :customer, Rms.Customers.Customer
    belongs_to :payment, Rms.Finance.Payment

    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  def changeset(card_token, attrs) do
    card_token
    |> cast(attrs, [:token, :customer_id, :payment_id])
    |> validate_required([:token, :customer_id, :payment_id])
  end
end
