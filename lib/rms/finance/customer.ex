defmodule Rms.Finance.Customer do
  use Ecto.Schema
  import Ecto.Changeset

  defmodule CustomerAddress do
    use Ecto.Schema

    @derive {Jason.Encoder,
             only: [
               :city_name,
               :city_code,
               :state,
               :country_name,
               :neighborhood,
               :street,
               :number,
               :zip,
               :complement
             ]}
    embedded_schema do
      field :city_name, :string
      field :city_code, :string
      field :state, :string
      field :country_name, :string
      field :neighborhood, :string
      field :street, :string
      field :number, :string
      field :zip, :string
      field :complement, :string
    end
  end

  @derive {Jason.Encoder,
           only: [
             :name,
             :document_type,
             :document,
             :email,
             :phone_number,
             :address,
             :additional_information
           ]}

  schema "transaction_customers" do
    field :name, :string
    field :document_type, :string
    field :additional_information, :string
    field :document, Rms.Vault.EncryptedBinary
    field :email, Rms.Vault.EncryptedBinary
    field :phone_number, Rms.Vault.EncryptedBinary

    embeds_one :address, CustomerAddress

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :transaction, Rms.Finance.Transaction

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer, attrs) do
    customer
    |> cast(attrs, [
      :name,
      :document_type,
      :document,
      :transaction_id,
      :email,
      :phone_number,
      :additional_information
    ])
    |> cast_embed(:address, with: &customer_address_changeset/2)
    |> validate_required([:document_type, :document])
    |> validate_document()
    |> foreign_key_constraint(:transaction_id)
  end

  defp customer_address_changeset(variation_type, attrs) do
    variation_type
    |> cast(attrs, [
      :city_name,
      :city_code,
      :state,
      :country_name,
      :neighborhood,
      :street,
      :number,
      :zip,
      :complement
    ])
    |> validate_required([
      :city_name,
      :state,
      :country_name,
      :street,
      :zip
    ])
  end

  defp validate_document(changeset) do
    document_type = fetch_field!(changeset, :document_type)
    document = fetch_field!(changeset, :document)

    case document_type do
      "cpf" ->
        if Brcpfcnpj.cpf_valid?(document),
          do: changeset,
          else: add_error(changeset, :document, "document (cpf) is invalid")

      "cnpj" ->
        if Brcpfcnpj.cnpj_valid?(document),
          do: changeset,
          else: add_error(changeset, :document, "document (cnpj) is invalid")

      _ ->
        add_error(changeset, :document_type, "document_type must be either cpf or cnpj")
    end
  end
end
