defmodule Rms.Finance.GiftCards do
  require Logger

  alias Rms.Integrations
  alias Rms.Integrations.GiftPromo
  alias Rms.Integrations.Shopify

  @doc """
  Redeems a Shopify gift card associated with the given payment.

  This function attempts the redemption directly in the current process.
  It expects a `Rms.Finance.Payment` struct with the necessary metadata
  (like `:card_number`) to identify the gift card on Shopify.

  ## Arguments

    * `payment`: A `%Rms.Finance.Payment{}` struct representing the payment
      to be settled using the gift card.

  ## Returns

    * `{:ok, updated_payment}`: If the redemption is successful.
    * `{:error, reason}`: If an error occurs during the process (e.g., API error, insufficient funds).
    * `{:cancel, payment, reason}`: If the redemption cannot proceed due to pre-validation checks
      (e.g., invalid payment status, missing metadata).
  """
  defdelegate redeem_from_shopify(payment),
    to: Rms.Finance.GiftCards.RedeemFromShopify,
    as: :redeem

  @doc """
  Durably redeems a Shopify gift card associated with the given payment using Oban.

  This function enqueues an Oban job to perform the redemption, providing
  durability and automatic retries in case of transient failures. It delegates
  to `Rms.Finance.GiftCards.RedeemFromShopify.relay_redeem/1`.

  ## Arguments

    * `payment`: A `%Rms.Finance.Payment{}` struct representing the payment
      to be settled using the gift card.

  ## Returns

    * `{:ok, updated_payment}`: If the redemption is successful.
    * `{:error, reason}`: If an error occurs during the process (e.g., API error, insufficient funds).
    * `{:cancel, payment, reason}`: If the redemption cannot proceed due to pre-validation checks
      (e.g., invalid payment status, missing metadata).
  """

  defdelegate durable_redeem_from_shopify(payment, opts \\ []),
    to: Rms.Finance.GiftCards.RedeemFromShopify,
    as: :relay_redeem

  defdelegate durable_refund_to_shopify(payment, opts \\ []),
    to: Rms.Finance.GiftCards.RefundToShopify,
    as: :relay_refund

  def check_balance(organization_id, provider, card_number, opts \\ [])

  def check_balance(
        organization_id,
        :shopify,
        <<_first_digits::binary-size(4), rest::binary>> = card_number,
        _opts
      )
      when rest != "" do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)

    shopify_response =
      shopify_credential.shop
      |> Shopify.client(shopify_credential.credential)
      |> Shopify.fetch_gift_card(card_number)

    with {:ok, gift_card_data} <- shopify_response do
      handle_shopify_response(gift_card_data)
    end
  end

  def check_balance(organization_id, :shopify, first_digits, _opts) do
    # We do not allow checking the balance of codes less than 4 digits because we
    # may end up getting multiple results from the API
    Logger.warning(
      "attempt at checking balance of less than 4 digits gift card code #{inspect(first_digits)}",
      organization_id: organization_id
    )

    {:error, :card_not_found}
  end

  def check_balance(organization_id, :gift_promo, card_number, opts) do
    location_id = opts[:location_id]

    gift_promo_credentials =
      Integrations.get_gift_promo_credentials!(organization_id, location_id)

    client =
      GiftPromo.client(%{
        username: gift_promo_credentials.username,
        password: gift_promo_credentials.password
      })

    card_pin = opts[:card_pin]

    with {:ok, body} <-
           GiftPromo.check_gift_card(client, %{
             store_id: gift_promo_credentials.store_id,
             access_key: gift_promo_credentials.access_key,
             card_number: card_number,
             card_pin: card_pin
           }),
         {:ok, balance} <- handle_gift_promo_response(body) do
      pass_validation =
        GiftPromo.validate_gift_card_password(client, %{
          store_id: gift_promo_credentials.store_id,
          access_key: gift_promo_credentials.access_key,
          card_number: card_number,
          card_pin: card_pin
        })

      case pass_validation do
        {:ok, _} ->
          {:ok, balance}

        {:error, _} ->
          {:error, :wrong_card_pin}
      end
    end
  end

  def check_balance(organization_id, provider, _card_number, _opts) do
    Logger.warning("unsupported provider sent to gift card check balance #{inspect(provider)}",
      organization_id: organization_id
    )

    {:error, {:unsupported_provider, provider}}
  end

  defp card_expired?(expires_on) do
    today =
      "Etc/UTC"
      |> DateTime.now!()
      |> DateTime.to_date()

    case Date.from_iso8601(expires_on) do
      {:ok, date} -> Date.before?(date, today)
      _ -> true
    end
  end

  defp handle_shopify_response(%{
         "data" => %{"giftCards" => %{"edges" => [%{"node" => gift_card}]}}
       }) do
    case gift_card do
      %{"enabled" => false} ->
        {:error, :card_not_active}

      %{"expiresOn" => expires_on} when not is_nil(expires_on) ->
        if card_expired?(expires_on) do
          {:error, :card_expired}
        else
          {:ok, get_in(gift_card, ["balance", "amount"])}
        end

      _ ->
        {:ok, get_in(gift_card, ["balance", "amount"])}
    end
  end

  defp handle_shopify_response(%{"data" => %{"giftCards" => %{"edges" => []}}}) do
    {:error, :card_not_found}
  end

  defp handle_shopify_response(shopify_response) do
    {:error, {:unexpected_response, shopify_response}}
  end

  defp handle_gift_promo_response(%{
         "sucesso" => "1",
         "cartaoStatus" => "Ativo",
         "cartaoSaldo" => balance
       }) do
    formatted_balance =
      balance
      |> String.replace(".", "")
      |> String.replace(",", ".")

    {:ok, formatted_balance}
  end

  defp handle_gift_promo_response(%{"sucesso" => "1", "cartaoStatus" => "Inativo"}) do
    {:error, :card_not_active}
  end

  defp handle_gift_promo_response(%{"sucesso" => "1", "cartaoStatus" => "Expirado"}) do
    {:error, :card_expired}
  end

  defp handle_gift_promo_response(%{"sucesso" => "1", "cartaoStatus" => "Cancelado"}) do
    {:error, :card_canceled}
  end

  defp handle_gift_promo_response(gift_promo_response) do
    {:error, {:unexpected_response, gift_promo_response}}
  end
end
