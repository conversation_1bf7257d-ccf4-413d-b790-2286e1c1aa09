defmodule Rms.Finance.GiftCards.RedeemFromShopify do
  alias Rms.Integrations
  alias Rms.Integrations.Shopify

  alias Rms.Finance
  alias Rms.Finance.GiftCards.RedeemFromShopifyWorker
  alias Rms.Finance.Payment

  alias Oban.Pro.Relay

  require Logger

  def redeem(%Payment{} = payment) do
    payment = Rms.Repo.reload!(payment)

    with {:ok, payment} <- validate_payment(payment) do
      case safe_redeem(payment) do
        {:ok, payment} ->
          {:ok, payment}

        error ->
          handle_error(error, payment)
      end
    end
  end

  def relay_redeem(%Payment{} = payment, opts \\ []) do
    timeout = Keyword.get(opts, :timeout, 5_000)

    %{id: payment.id, organization_id: payment.organization_id}
    |> RedeemFromShopifyWorker.new()
    |> Relay.async()
    |> Relay.await(timeout)
  end

  defp validate_payment(payment) do
    with {:status, "pending"} <- {:status, payment.status},
         {:method, "gift_card"} <- {:method, payment.method},
         metadata = payment.metadata,
         {:card_number, card_number} when not is_nil(card_number) <-
           {:card_number, Map.get(metadata, "card_number")} do
      {:ok, payment}
    else
      # Handle errors from the initial validation `with` chain
      {:status, status} when status != "pending" ->
        {:cancel, {payment, :invalid_payment_status}}

      {:method, method} when method != "gift_card" ->
        {:cancel, {payment, :invalid_payment_method}}

      # Matched when card_number is nil
      {:card_number, nil} ->
        {:cancel, {payment, :missing_card_number}}

      # Catch any other unexpected non-match from the initial `with`
      _other ->
        {:cancel, {payment, :unexpected_validation_error}}
    end
  end

  defp safe_redeem(payment) do
    card_number = payment.metadata["card_number"]
    shopify_credential = Integrations.get_shopify_credential!(payment.organization_id)
    client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, gift_card_data} <- Shopify.fetch_gift_card(client, card_number),
         {:ok, gift_card} <- extract_gift_card_node(gift_card_data) do
      case idempotency_check(payment, gift_card) do
        :create_transaction ->
          do_redeem(payment, client, gift_card["id"])

        {:reuse_transaction, transaction} ->
          updated_metadata =
            Map.put(payment.metadata, "provider_transaction_id", transaction["id"])

          Finance.update_payment(payment, %{
            status: "settled",
            metadata: updated_metadata
          })
      end
    end
  end

  defp do_redeem(payment, client, gift_card_id) do
    debit_input = %{
      debitAmount: %{
        amount: Decimal.to_string(payment.amount),
        currencyCode: "BRL"
      },
      note: "iglu_payment_id:#{payment.id}"
    }

    with {:ok, debit_data} <- Shopify.debit_gift_card(client, gift_card_id, debit_input) do
      debit_transaction_id = get_in(debit_data, ["giftCardDebitTransaction", "id"])

      updated_metadata =
        Map.put(payment.metadata, "provider_transaction_id", debit_transaction_id)

      Finance.update_payment(payment, %{
        status: "settled",
        metadata: updated_metadata
      })
    end
  end

  defp extract_gift_card_node(shopify_response) do
    case get_in(shopify_response, ["data", "giftCards", "edges"]) do
      [] ->
        {:error, :gift_card_not_found}

      [%{"node" => node}] ->
        {:ok, node}

      [_ | _] ->
        {:error, :multiple_gift_cards_found}

      shopify_response ->
        {:error, {:invalid_shopify_response_structure, shopify_response}}
    end
  end

  defp idempotency_check(payment, gift_card) do
    transactions = get_in(gift_card, ["transactions", "nodes", Access.all()]) || []

    transaction =
      Enum.find(transactions, fn
        %{
          "note" => "iglu_payment_id:" <> id,
          "id" => "gid://shopify/GiftCardDebitTransaction/" <> _
        } ->
          id == to_string(payment.id)

        _ ->
          false
      end)

    if transaction do
      {:reuse_transaction, transaction}
    else
      :create_transaction
    end
  end

  # --- Error Handling ---

  # Errors from Shopify interaction `with` chain in safe_redeem
  defp handle_error({:error, :gift_card_not_found}, payment),
    do: handle_cancel(payment, :gift_card_not_found)

  defp handle_error({:error, :multiple_gift_cards_found}, payment),
    do: handle_cancel(payment, :multiple_gift_cards_found)

  defp handle_error({:error, :invalid_shopify_response_structure}, payment),
    do: handle_cancel(payment, :invalid_shopify_response)

  # Specific Shopify debit errors
  defp handle_error(
         {:error, {:unknown_error_code, %{"code" => "INSUFFICIENT_FUNDS"} = _details}},
         payment
       ),
       do: handle_cancel(payment, :insufficient_funds)

  defp handle_error(
         {:error, {:unknown_error_code, %{"code" => "MISMATCHING_CURRENCY"} = _details}},
         payment
       ),
       do: handle_cancel(payment, :currency_mismatch)

  defp handle_error(
         {:error, {:unknown_error_code, %{"code" => "NEGATIVE_OR_ZERO_AMOUNT"} = _details}},
         payment
       ),
       do: handle_cancel(payment, :invalid_amount)

  defp handle_error(
         {:error, {:unknown_error_code, %{"code" => "GIFT_CARD_LIMIT_EXCEEDED"} = _details}},
         payment
       ),
       do: handle_cancel(payment, :limit_exceeded)

  defp handle_error(
         {:error, {:unknown_error_code, %{"code" => "INTERNAL_ERROR"} = details}},
         _payment
       ),
       do: {:error, {:shopify_error, details}}

  defp handle_error({:error, {:unknown_error_code, details}}, payment),
    do: handle_cancel(payment, {:shopify_error, details})

  # Validation errors passed through safe_redeem
  defp handle_error({:cancel, {_payment, reason}}, payment), do: handle_cancel(payment, reason)

  # Payment update errors passed through handle_cancel/handle_error
  defp handle_error({:error, {:payment_update_failed, _changeset} = reason}, _payment),
    do: {:error, reason}

  # Catch-all for other errors from safe_redeem (e.g., network failures)
  defp handle_error({:error, reason}, _payment) do
    Logger.error("unexpected error format in handle_error: #{inspect(reason)}")
    {:error, {:unhandled_error, reason}}
  end

  # --- Error Handling Helper ---

  defp handle_cancel(payment, reason) do
    case Finance.update_payment(payment, %{status: "canceled"}) do
      {:ok, canceled_payment} -> {:cancel, {canceled_payment, reason}}
      # If updating the payment fails, return an error tuple
      {:error, changeset} -> {:error, {:payment_update_failed, changeset}}
    end
  end
end
