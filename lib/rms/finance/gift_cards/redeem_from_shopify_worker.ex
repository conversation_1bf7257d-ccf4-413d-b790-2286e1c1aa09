defmodule Rms.Finance.GiftCards.RedeemFromShopifyWorker do
  use Oban.Pro.Worker,
    max_attempts: 3,
    queue: :shopify,
    unique: [
      period: :infinity,
      states: [:executing, :available, :scheduled, :retryable, :discarded, :completed]
    ]

  require Logger

  alias Rms.Finance
  alias Rms.Finance.GiftCards.RedeemFromShopify

  @impl Oban.Pro.Worker
  def process(%{args: %{"id" => payment_id, "organization_id" => organization_id}}) do
    payment = Rms.Finance.get_payment!(organization_id, payment_id)
    RedeemFromShopify.redeem(payment)
  end

  @impl Oban.Pro.Worker
  def after_process(
        :discard,
        %{args: %{"id" => payment_id, "organization_id" => organization_id}} = job,
        result
      ) do
    Logger.warning(
      "canceling payment after gift card redeem retry exhaust; last result: #{inspect(result)}",
      payment_id: payment_id,
      organization_id: organization_id,
      job_id: job.id
    )

    payment = Rms.Finance.get_payment!(organization_id, payment_id)
    Finance.update_payment(payment, %{status: "canceled"})
  end

  def after_process(_state, _job, _result), do: :ok
end
