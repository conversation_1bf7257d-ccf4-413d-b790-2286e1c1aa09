defmodule Rms.Finance.GiftCards.RefundToShopify do
  import Ecto.Query

  alias Rms.Integrations
  alias Rms.Integrations.Shopify

  alias Rms.Finance
  alias Rms.Finance.GiftCards.RefundToShopifyWorker
  alias Rms.Finance.Payment
  alias Rms.Repo

  alias Ecto.Multi

  alias Oban.Pro.Relay
  require Logger

  defp cancel_payment(payment, attrs) do
    payment
    |> Payment.cancel_changeset(attrs)
    |> Repo.update()
  end

  def get_payment_with_lock(payment) do
    payment =
      Payment
      |> where([p], p.id == ^payment.id and p.organization_id == ^payment.organization_id)
      |> lock("FOR UPDATE")
      |> Repo.one()

    case payment do
      nil -> {:error, :payment_not_found}
      payment -> {:ok, payment}
    end
  end

  def refund(%Payment{} = payment) do
    Multi.new()
    |> Multi.run(:get_payment, fn _repo, _changes ->
      get_payment_with_lock(payment)
    end)
    |> Multi.run(:refund, fn _repo, %{get_payment: payment} -> validate_payment(payment) end)
    |> Multi.run(:safe_refund, fn _repo, %{refund: payment} -> safe_refund(payment) end)
    |> Repo.transaction()
    |> case do
      {:ok, %{safe_refund: payment}} ->
        {:ok, payment}

      {:error, _step, error, _} ->
        Logger.error("Error refunding payment: #{inspect(error)}")
        {:error, error}
    end
  end

  def relay_refund(%Payment{} = payment, opts \\ []) do
    timeout = Keyword.get(opts, :timeout, 5_000)

    %{id: payment.id, organization_id: payment.organization_id}
    |> RefundToShopifyWorker.new()
    |> Relay.async()
    |> Relay.await(timeout)
  end

  defp validate_payment(payment) do
    with {:method, "gift_card"} <- {:method, payment.method},
         metadata = payment.metadata,
         {:card_number, card_number} when not is_nil(card_number) <-
           {:card_number, Map.get(metadata, "card_number")} do
      {:ok, payment}
    else
      {:method, method} when method != "gift_card" ->
        {:error, {payment, :invalid_payment_method}}

      # Matched when card_number is nil
      {:card_number, nil} ->
        {:error, {payment, :missing_card_number}}

      # Catch any other unexpected non-match from the initial `with`
      _other ->
        {:error, {payment, :unexpected_validation_error}}
    end
  end

  defp safe_refund(payment) do
    card_number = payment.metadata["card_number"]
    shopify_credential = Integrations.get_shopify_credential!(payment.organization_id)
    client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, gift_card_data} <- Shopify.fetch_gift_card(client, card_number),
         {:ok, gift_card} <- extract_gift_card_node(gift_card_data) do
      case idempotency_check(payment, gift_card) do
        :create_transaction ->
          do_refund(payment, client, gift_card["id"])

        {:reuse_transaction, transaction} ->
          updated_metadata =
            Map.put(payment.metadata, "provider_refund_transaction_id", transaction["id"])

          cancel_payment(payment, %{
            metadata: updated_metadata
          })

        {:connect_transaction, transaction} ->
          Finance.update_payment_metadata(payment, %{
            metadata:
              Map.put(payment.metadata, "provider_refund_transaction_id", transaction["id"])
          })

        error ->
          error
      end
    end
  end

  defp do_refund(payment, client, gift_card_id) do
    credit_input = %{
      creditAmount: %{
        amount: Decimal.to_string(payment.amount),
        currencyCode: "BRL"
      },
      note: "refund_iglu_payment_id:#{payment.id}"
    }

    with {:ok, credit_data} <- Shopify.credit_gift_card(client, gift_card_id, credit_input) do
      credit_transaction_id = get_in(credit_data, ["giftCardCreditTransaction", "id"])

      updated_metadata =
        Map.put(payment.metadata, "provider_refund_transaction_id", credit_transaction_id)

      cancel_payment(payment, %{
        metadata: updated_metadata
      })
    end
  end

  defp extract_gift_card_node(shopify_response) do
    case get_in(shopify_response, ["data", "giftCards", "edges"]) do
      [] ->
        {:error, :gift_card_not_found}

      [%{"node" => node}] ->
        {:ok, node}

      [_ | _] ->
        {:error, :multiple_gift_cards_found}

      shopify_response ->
        {:error, {:invalid_shopify_response_structure, shopify_response}}
    end
  end

  defp idempotency_check(%Payment{status: "canceled"} = payment, gift_card) do
    transaction = find_transaction(gift_card, payment.id)

    if transaction do
      {:connect_transaction, transaction}
    else
      {:error, :invalid_status_for_refund}
    end
  end

  defp idempotency_check(%Payment{status: "settled"} = payment, gift_card) do
    transaction = find_transaction(gift_card, payment.id)

    if transaction do
      {:reuse_transaction, transaction}
    else
      :create_transaction
    end
  end

  defp idempotency_check(%Payment{} = _payment, _gift_card) do
    {:error, :invalid_status_for_refund}
  end

  defp find_transaction(gift_card, payment_id) do
    transactions = get_in(gift_card, ["transactions", "nodes", Access.all()]) || []

    Enum.find(transactions, fn
      %{
        "note" => "refund_iglu_payment_id:" <> id,
        "id" => "gid://shopify/GiftCardCreditTransaction/" <> _
      } ->
        id == to_string(payment_id)

      _ ->
        false
    end)
  end
end
