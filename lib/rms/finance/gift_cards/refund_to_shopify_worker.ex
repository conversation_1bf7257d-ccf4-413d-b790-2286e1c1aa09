defmodule Rms.Finance.GiftCards.RefundToShopifyWorker do
  use Oban.Pro.Worker,
    max_attempts: 3,
    queue: :shopify,
    unique: [
      period: :infinity,
      states: [:executing, :available, :scheduled, :retryable, :discarded, :completed]
    ]

  require Logger

  alias Rms.Finance.GiftCards.RefundToShopify

  @impl Oban.Pro.Worker
  def process(%{args: %{"id" => payment_id, "organization_id" => organization_id}}) do
    payment = Rms.Finance.get_payment!(organization_id, payment_id)
    RefundToShopify.refund(payment)
  end
end
