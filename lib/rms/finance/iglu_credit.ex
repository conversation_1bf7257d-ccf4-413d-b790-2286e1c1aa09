defmodule Rms.Finance.IgluCredit do
  use Ecto.Schema
  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Customers.Customer
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Finance.Payment

  @allowed_transitions %{
    "available" => ["available", "used", "expired", "cancelled"],
    "used" => ["used", "cancelled"],
    "expired" => ["expired"],
    "cancelled" => ["cancelled"]
  }

  @type t :: %__MODULE__{}

  schema "iglu_credits" do
    field :amount, :decimal
    field :used_amount, :decimal, default: Decimal.new(0)
    field :status, :string, default: "available"
    field :reason, :string
    field :expires_at, :utc_datetime
    field :metadata, :map, default: %{}
    field :version, :integer, default: 1

    belongs_to :organization, Organization
    belongs_to :customer, Customer
    belongs_to :reverse_fulfillment, ReverseFulfillment, type: :binary_id

    has_many :iglu_credit_payments, Rms.Finance.IgluCreditPayment
    has_many :payments, through: [:iglu_credit_payments, :payment]

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for an iglu credit.
  """
  def changeset(credit, attrs) do
    credit
    |> cast(attrs, [
      :amount,
      :used_amount,
      :status,
      :reason,
      :expires_at,
      :metadata,
      :customer_id,
      :organization_id,
      :reverse_fulfillment_id
    ])
    |> validate_required([
      :amount,
      :status,
      :reason,
      :organization_id,
      :reverse_fulfillment_id
    ])
    |> validate_number(:amount, greater_than_or_equal_to: 0)
    |> validate_number(:used_amount, greater_than_or_equal_to: 0)
    |> validate_used_amount_not_exceeds_amount()
    |> check_constraint(:used_amount,
      name: :used_amount_must_not_exceed_amount,
      message: "cannot be greater than total amount"
    )
    |> validate_inclusion(:status, ~w(available used expired cancelled))
    |> validate_inclusion(:reason, ~w(return exchange))
    |> validate_expires_at()
    |> unique_constraint(:reverse_fulfillment_id)
    |> assoc_constraint(:organization)
    |> assoc_constraint(:customer)
    |> assoc_constraint(:reverse_fulfillment)
  end

  defp validate_expires_at(changeset) do
    case get_field(changeset, :expires_at) do
      nil ->
        changeset

      expires_at ->
        if DateTime.compare(expires_at, DateTime.utc_now()) == :gt do
          changeset
        else
          add_error(changeset, :expires_at, "must be in the future (utc timezone)")
        end
    end
  end

  @doc """
  Updates a changeset for an iglu credit, allowing only used amount updates.
  """
  def update_used_amount_changeset(credit, attrs) do
    credit
    |> cast(attrs, [:used_amount])
    |> validate_required([:used_amount])
    |> validate_number(:used_amount, greater_than_or_equal_to: 0)
    |> validate_used_amount_not_exceeds_amount()
    |> check_constraint(:used_amount,
      name: :used_amount_must_not_exceed_amount,
      message: "cannot be greater than total amount"
    )
    |> optimistic_lock(:version)
  end

  @doc """
  Updates a changeset for an iglu credit, allowing only status updates.
  """
  def update_changeset(credit, attrs) do
    credit
    |> cast(attrs, [:status, :used_amount])
    |> validate_required([:status, :used_amount])
    |> validate_used_amount_not_exceeds_amount()
    |> validate_inclusion(:status, ~w(available used expired cancelled))
    |> validate_state_transition()
    |> optimistic_lock(:version)
  end

  @doc """
  Creates a changeset for cancelling an iglu credit.
  """
  def cancel_changeset(credit, attrs \\ %{}) do
    credit
    |> cast(attrs, [:status])
    |> put_change(:status, "cancelled")
    |> validate_inclusion(:status, ~w(available used expired cancelled))
    |> validate_state_transition()
    |> cancel_payments()
    |> optimistic_lock(:version)
  end

  defp cancel_payments(changeset) do
    prepare_changes(changeset, &cancel_associated_payments/1)
  end

  defp cancel_associated_payments(changeset) do
    changeset.data.payments
    |> Enum.reduce_while(changeset, &cancel_payment/2)
  end

  defp cancel_payment(payment, changeset) do
    case changeset.repo.update(Payment.cancel_changeset(payment)) do
      {:ok, _payment} -> {:cont, changeset}
      {:error, _} -> {:halt, changeset.repo.rollback(:payment_cancellation_failed)}
    end
  end

  defp validate_state_transition(changeset) do
    current_status = changeset.data.status
    validate_inclusion(changeset, :status, @allowed_transitions[current_status])
  end

  defp validate_used_amount_not_exceeds_amount(changeset) do
    amount = get_field(changeset, :amount)
    used_amount = get_field(changeset, :used_amount)

    if amount && used_amount && Decimal.compare(used_amount, amount) == :gt do
      add_error(changeset, :used_amount, "cannot be greater than total amount")
    else
      changeset
    end
  end
end
