defmodule Rms.Finance.IgluCreditPayment do
  use Ecto.Schema
  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Finance.Payment
  alias Rms.Finance.IgluCredit

  @type t :: %__MODULE__{}

  schema "iglu_credit_payments" do
    belongs_to :organization, Organization
    belongs_to :payment, Payment, defaults: {Rms.Repo, :add_organization_id, []}
    belongs_to :iglu_credit, IgluCredit

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for an iglu credit payment.
  """
  def changeset(credit_payment, attrs) do
    credit_payment
    |> cast(attrs, [])
    |> cast_assoc(:payment, require: true)
    |> validate_required([:organization_id, :iglu_credit_id])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:payment)
    |> assoc_constraint(:iglu_credit)
    |> unique_constraint([:payment_id, :iglu_credit_id])
    |> validate_iglu_credit()
  end

  defp validate_iglu_credit(changeset) do
    credit_payment = changeset.data

    iglu_credit =
      Rms.Finance.get_iglu_credit!(
        credit_payment.organization_id,
        credit_payment.iglu_credit_id
      )

    changeset
    |> validate_credit_balance(iglu_credit)
    |> validate_credit_not_expired(iglu_credit)
  end

  defp validate_credit_balance(changeset, iglu_credit) do
    remaining_balance = Rms.Finance.get_iglu_credit_remaining_balance(iglu_credit)
    payment = changeset.changes.payment.changes

    case Decimal.compare(remaining_balance, payment.amount) do
      :lt ->
        error = "insufficient balance"
        add_error(changeset, :iglu_credit, error)

      _ ->
        changeset
    end
  end

  defp validate_credit_not_expired(changeset, iglu_credit) do
    now = DateTime.utc_now()

    cond do
      is_nil(iglu_credit.expires_at) ->
        changeset

      DateTime.compare(iglu_credit.expires_at, now) == :lt ->
        add_error(changeset, :iglu_credit, "already expired")

      true ->
        changeset
    end
  end
end
