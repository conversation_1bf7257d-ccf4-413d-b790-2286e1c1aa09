defmodule Rms.Finance.Payment do
  use Ecto.Schema
  import Ecto.Changeset
  alias Decimal

  @statuses ~w(pending authorized settled canceled)

  @allowed_transitions %{
    "pending" => [nil, "pending", "authorized", "settled", "canceled"],
    "authorized" => [nil, "authorized", "settled", "canceled"],
    "settled" => [nil, "settled", "canceled"],
    "canceled" => [nil, "canceled"]
  }

  @payment_methods ~w(pix credit_card debit_card cash payment_link gift_card return_credit)

  @gift_card_metadata_required_keys %{
    "gift_promo" => ["card_number", "card_pin"],
    "shopify" => ["card_number"]
  }

  schema "payments" do
    field :status, :string, default: "pending"
    field :metadata, :map
    field :amount, :decimal
    field :method, :string

    belongs_to :transaction, Rms.Finance.Transaction
    belongs_to :organization, Rms.Accounts.Organization

    has_one :external_payment_reference, Rms.Integrations.ExternalPaymentReference,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_one :card_token, Rms.Finance.CardToken

    has_one :iglu_credit_payment, Rms.Finance.IgluCreditPayment
    has_one :iglu_credit, through: [:iglu_credit_payment, :iglu_credit]

    field :reference_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  @doc false
  def assoc_changeset(payment, attrs) do
    payment
    |> cast(attrs, [:amount, :status, :method, :metadata, :reference_at])
    |> cast_assoc(:external_payment_reference)
    |> validate_required([:amount, :status, :method])
    |> validate_inclusion(:method, @payment_methods)
    |> validate_status()
    |> validate_metadata()
    |> maybe_put_status()
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
    |> foreign_key_constraint(:transaction_id)
    |> foreign_key_constraint(:organization_id)
  end

  def cancel_changeset(payment, attrs \\ %{}) do
    payment
    |> change(%{status: "canceled", metadata: merge_metadata(payment, attrs)})
    |> validate_status()
    |> validate_transaction_status()
  end

  defp merge_metadata(payment, %{metadata: metadata} = attrs) do
    Map.merge(payment.metadata || %{}, Map.merge(metadata, Map.drop(attrs, [:metadata])))
  end

  defp merge_metadata(payment, attrs) do
    Map.merge(payment.metadata || %{}, attrs)
  end

  def update_metadata_changeset(payment, attrs) do
    payment
    |> cast(attrs, [:metadata])
    |> validate_required([:metadata])
    |> validate_metadata()
  end

  @doc false
  def changeset(payment, attrs) do
    payment
    |> assoc_changeset(attrs)
    |> cast(attrs, [:transaction_id])
    |> validate_required([:transaction_id])
    |> validate_transaction()
  end

  defp validate_transaction_status(changeset) do
    changeset
    |> prepare_changes(fn changeset ->
      transaction_id = fetch_field!(changeset, :transaction_id)
      payment = changeset.data
      organization_id = payment.organization_id
      method = payment.method

      case Rms.Finance.get_transaction!(organization_id, transaction_id) do
        %{status: "done"} when method == "return_credit" ->
          add_error(
            changeset,
            :status,
            "cannot cancel return_credit payment when transaction is done"
          )

        _ ->
          changeset
      end
    end)
  end

  defp validate_transaction(changeset) do
    changeset
    |> prepare_changes(fn changeset ->
      organization_id = fetch_field!(changeset, :organization_id)
      transaction_id = fetch_field!(changeset, :transaction_id)
      transaction = Rms.Finance.get_transaction!(organization_id, transaction_id)

      changeset
      |> validate_transaction_open(transaction)
      |> validate_transaction_total_payments(transaction)
    end)
  end

  defp validate_transaction_open(changeset, transaction) do
    if transaction.status != "open" do
      add_error(
        changeset,
        :transaction_id,
        "can't create payment for a #{transaction.status} transaction"
      )
    else
      changeset
    end
  end

  defp validate_transaction_total_payments(changeset, transaction) do
    transaction = Rms.Repo.preload(transaction, [:payments, :order])
    {total_payments, _total_settled_payments} = calculate_totals(transaction.payments)

    amount =
      case fetch_change(changeset, :amount) do
        {:ok, amount} -> amount
        _ -> Decimal.new(0)
      end

    total_payments = Decimal.add(total_payments, amount)

    if total_payment_order?(total_payments, transaction.order) do
      add_error(
        changeset,
        :amount,
        "the total amount of payments exceeds the order total."
      )
    else
      changeset
    end
  end

  defp total_payment_order?(total_payments, %{addons: [], total_price: total_price}) do
    Decimal.gt?(total_payments, Decimal.normalize(total_price))
  end

  defp total_payment_order?(total_payments, %{
         addons: _,
         total_price_with_addons: total_price_with_addons
       }) do
    Decimal.gt?(total_payments, Decimal.normalize(total_price_with_addons))
  end

  defp validate_status(changeset) do
    changeset
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:status, @allowed_transitions[changeset.data.status],
      message:
        "status transition not allowed #{Map.get(changeset.data, :status, "nil")} -> #{Map.get(changeset.changes, :status, "nil")}"
    )
    |> validate_change(:status, fn :status, status ->
      method = get_field(changeset, :method)

      if status == "authorized" and method not in ["credit_card", "payment_link", "gift_card"] do
        [
          status:
            "status can only be 'authorized' if method is credit_card, payment_link or gift_card"
        ]
      else
        []
      end
    end)
  end

  defp validate_metadata(changeset) do
    case {fetch_field!(changeset, :status), fetch_field!(changeset, :method)} do
      {_, "gift_card"} ->
        metadata = get_field(changeset, :metadata, %{})
        provider = Map.get(metadata, "provider")
        required_keys = Map.get(@gift_card_metadata_required_keys, provider, [])

        for required_key <- ["provider" | required_keys],
            is_nil(metadata[required_key]),
            reduce: changeset do
          acc ->
            add_error(
              acc,
              :"metadata.#{required_key}",
              "#{required_key} can't be nil for gift card."
            )
        end

      {"settled", "payment_link"} ->
        changeset

      {_, "payment_link"} ->
        metadata = get_field(changeset, :metadata, %{})

        for required_key <- ["link"],
            is_nil(metadata[required_key]),
            reduce: changeset do
          acc ->
            add_error(
              acc,
              :"metadata.#{required_key}",
              "#{required_key} can't be nil for payment links."
            )
        end

      {"settled", "credit_card"} ->
        metadata = get_field(changeset, :metadata, %{})

        for required_key <- ["nsu", "aut"],
            is_nil(metadata[required_key]),
            reduce: changeset do
          acc ->
            add_error(
              acc,
              :"metadata.#{required_key}",
              "#{required_key} can't be nil for settled payments."
            )
        end

      _ ->
        changeset
    end
  end

  defp maybe_put_status(changeset) do
    case fetch_field!(changeset, :method) do
      "cash" ->
        force_change(changeset, :status, "settled")

      _ ->
        changeset
    end
  end

  @doc false
  def calculate_totals(payments) do
    # Separating the payment calculation logic
    payments = Enum.reject(payments, &(&1.status == "canceled"))
    {settled, non_settled} = Enum.split_with(payments, &(&1.status == "settled"))

    total_settled_payments =
      Enum.reduce(settled, Decimal.new(0), &Decimal.add(&1.amount, &2))

    total_non_settled_payments =
      Enum.reduce(non_settled, Decimal.new(0), &Decimal.add(&1.amount, &2))

    total_payments = Decimal.add(total_non_settled_payments, total_settled_payments)

    {total_payments, total_settled_payments}
  end

  @doc false
  def event_payload(payment) do
    %{
      id: payment.id,
      status: payment.status,
      metadata: payment.metadata,
      amount: payment.amount,
      method: payment.method,
      transaction_id: payment.transaction_id,
      organization_id: payment.organization_id
    }
  end
end
