defmodule Rms.Finance.Subscription do
  use Ecto.Schema
  import Ecto.Changeset

  schema "subscriptions" do
    field :value, :decimal
    field :frequency, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :customer, Rms.Finance.Customer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer, attrs) do
    customer
    |> cast(attrs, [:value, :frequency, :customer_id])
    |> validate_required([:value, :frequency])
    |> foreign_key_constraint(:customer_id)
  end
end
