defmodule Rms.Finance.Transaction do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization
  alias Rms.Commerce.Orders.Order
  alias Rms.Finance.Customer
  alias Rms.Finance.Payment

  @statuses ~w(open done canceled)
  @allowed_transitions %{
    "open" => ["done", "canceled"],
    "done" => [],
    "canceled" => []
  }

  schema "transactions" do
    field :status, :string, default: "open"

    belongs_to :order, Order
    belongs_to :organization, Organization

    has_many :payments, Payment,
      defaults: {Rms.Repo, :add_organization_id, []},
      preload_order: [asc: :inserted_at]

    has_one :customer, Customer, defaults: {Rms.Repo, :add_organization_id, []}

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(transaction, attrs) do
    transaction
    |> cast(attrs, [:order_id, :status])
    |> cast_assoc(:customer)
    |> cast_assoc(:payments, with: &Payment.assoc_changeset/2)
    |> validate_required([:status, :order_id])
    |> validate_status()
    |> foreign_key_constraint(:order_id)
    |> foreign_key_constraint(:organization_id)
    |> unique_constraint(:order_id,
      message: "already has a open transaction"
    )
    |> put_status()
    |> maybe_update_order_status()
  end

  @doc false
  def cancel_changeset(transaction) do
    transaction
    |> change(%{status: "canceled"})
    |> validate_status()
    |> prepare_changes(fn changeset ->
      payments = fetch_field!(changeset, :payments)
      canceled_payments = Enum.map(payments, &Payment.cancel_changeset/1)

      put_assoc(changeset, :payments, canceled_payments)
    end)
  end

  @doc false
  def done_changeset(transaction, opts \\ []) do
    transaction
    |> change(%{status: "done"})
    |> validate_status()
    |> prepare_changes(fn changeset ->
      payments = fetch_field!(changeset, :payments)
      {_total_payments, total_settled_payments} = Payment.calculate_totals(payments)
      order = fetch_field!(changeset, :order)

      if payments_settle_order?(total_settled_payments, order) do
        changeset
      else
        add_error(
          changeset,
          :status,
          "sum of settled payments is not enough to close transaction"
        )
      end
    end)
    |> maybe_update_order_status(opts)
  end

  defp validate_status(changeset) do
    changeset
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:status, @allowed_transitions[changeset.data.status],
      message: "status transition not allowed"
    )
  end

  def done?(transaction) do
    payments = transaction.payments
    order = transaction.order
    {_total_payments, total_settled_payments} = Payment.calculate_totals(payments)

    if Decimal.eq?(total_settled_payments, Decimal.normalize(order.total_price)) do
      true
    else
      false
    end
  end

  defp put_status(changeset) do
    prepare_changes(changeset, fn changeset ->
      status = fetch_field!(changeset, :status)

      handle_status(changeset, status)
    end)
  end

  defp handle_status(changeset, "canceled"), do: changeset

  defp handle_status(changeset, _status) do
    payments = fetch_field!(changeset, :payments)

    handle_non_canceled_status(changeset, payments)
  end

  defp handle_non_canceled_status(changeset, payments)
       when is_nil(payments) or payments == [] do
    force_change(changeset, :status, "open")
  end

  defp handle_non_canceled_status(changeset, payments) do
    {total_payments, total_settled_payments} = Payment.calculate_totals(payments)
    order_id = fetch_field!(changeset, :order_id)
    order = Rms.Commerce.Orders.unsafe_get_order!(order_id)

    cond do
      payments_exceeds_order_total?(total_payments, order) ->
        add_error(
          changeset,
          :payments,
          "the total amount of payments exceeds the order total."
        )

      order_not_fully_settled?(total_settled_payments, order) ->
        force_change(changeset, :status, "open")

      payments_settle_order?(total_settled_payments, order) ->
        force_change(changeset, :status, "done")
    end
  end

  defp payments_exceeds_order_total?(total_payments, %{addons: [], total_price: total_price}) do
    Decimal.gt?(total_payments, Decimal.normalize(total_price))
  end

  defp payments_exceeds_order_total?(total_payments, %{
         addons: _,
         total_price_with_addons: total_price_with_addons
       }) do
    Decimal.gt?(total_payments, Decimal.normalize(total_price_with_addons))
  end

  defp order_not_fully_settled?(total_settled_payments, %{addons: [], total_price: total_price}) do
    Decimal.lt?(total_settled_payments, Decimal.normalize(total_price))
  end

  defp order_not_fully_settled?(total_settled_payments, %{
         addons: _,
         total_price_with_addons: total_price_with_addons
       }) do
    Decimal.lt?(total_settled_payments, Decimal.normalize(total_price_with_addons))
  end

  defp payments_settle_order?(total_payments, %{addons: [], total_price: total_price}) do
    Decimal.eq?(total_payments, Decimal.normalize(total_price))
  end

  defp payments_settle_order?(total_payments, %{
         addons: _,
         total_price_with_addons: total_price_with_addons
       }) do
    Decimal.eq?(total_payments, Decimal.normalize(total_price_with_addons))
  end

  defp maybe_update_order_status(changeset, opts \\ []) do
    prepare_changes(changeset, fn changeset ->
      status = fetch_field!(changeset, :status)

      if status == "done" do
        order_id = fetch_field!(changeset, :order_id)
        order = Rms.Commerce.Orders.unsafe_get_order!(order_id)

        Rms.Commerce.Orders.mark_order_as_paid(order, opts)
      end

      changeset
    end)
  end
end
