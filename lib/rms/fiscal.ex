defmodule Rms.Fiscal do
  import Ecto.Query

  alias Rms.Fiscal.LocationTax
  alias Rms.Fiscal.InterStateTaxes
  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Fiscal.FiscalSettings
  alias Rms.Fiscal.ProductTaxes
  alias Rms.Fiscal.XmlParser
  alias Rms.Storage
  alias Rms.Storage.UploadEntry
  alias Rms.Workers.ProcessProductTaxUpload
  alias Ecto.Multi
  alias Rms.Repo

  require Logger

  def list_invoice_serie(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, [])

    InvoiceSerie
    |> where([is], is.organization_id == ^organization_id)
    |> where(^add_serie_filter_where(query_params))
    |> Repo.all()
  end

  def get_invoice_serie!(organization_id, serie_id) do
    InvoiceSerie
    |> where(id: ^serie_id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  def add_serie_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["status", :status] ->
        dynamic([is], ^dynamic and is.status == ^value)

      {key, value}, dynamic when key in ["invoice_type", :invoice_type] ->
        dynamic([is], ^dynamic and is.invoice_type == ^value)

      {key, value}, dynamic when key in ["invoice_env", :invoice_env] ->
        dynamic([is], ^dynamic and is.invoice_env == ^value)

      {key, value}, dynamic when key in ["invoice_serie", :invoice_serie] ->
        dynamic([is], ^dynamic and is.invoice_serie == ^value)

      {key, value}, dynamic when key in ["location_id", :location_id] ->
        dynamic([is], ^dynamic and is.location_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def create_serie(organization_id, attrs) do
    %InvoiceSerie{organization_id: organization_id}
    |> InvoiceSerie.changeset(attrs)
    |> Repo.insert()
  end

  def update_invoice_serie(serie, attrs) do
    serie
    |> InvoiceSerie.update_changeset(attrs)
    |> Repo.update()
  end

  def paginated_fiscal_invoice(organization_id, opts \\ []) do
    query = build_fiscal_invoice_query(organization_id, opts)

    cursor_after = Keyword.get(opts, :after, nil)
    cursor_before = Keyword.get(opts, :before, nil)
    total_count = Keyword.has_key?(opts, :count)

    limit =
      case opts[:limit] do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    query =
      query
      |> order_by([fi, is, ip, p], desc: fi.id)
      |> distinct(true)

    paginate_opts =
      [
        after: cursor_after,
        before: cursor_before,
        limit: limit,
        include_total_count: total_count,
        cursor_fields: [{:id, :desc}]
      ]

    Repo.paginate(
      query,
      paginate_opts
    )
  end

  def list_fiscal_invoice(organization_id, opts \\ []) do
    build_fiscal_invoice_query(organization_id, opts)
    |> distinct([fi, is, ip, p], fi.id)
    |> Repo.all()
  end

  defp build_fiscal_invoice_query(organization_id, opts) do
    query_params = Keyword.get(opts, :query_params, [])

    preloads =
      Keyword.get(opts, :preloads, [
        :fulfillment,
        :customer,
        reverse_fulfillment: [:staff, customer: [:addresses]],
        invoice_payments: [:payment],
        invoice_items: [:line_item],
        serie: [location: [:address]]
      ])

    FiscalInvoice
    |> join(:left, [fi], is in assoc(fi, :serie))
    |> join(:left, [fi, is], ip in assoc(fi, :invoice_payments))
    |> join(:left, [fi, is, ip], p in assoc(ip, :payment))
    |> where([fi, is, ip, p], fi.organization_id == ^organization_id)
    |> where(^add_invoice_filter_where(query_params))
    |> preload(^preloads)
  end

  def get_fiscal_invoice!(organization_id, invoice_id, preload \\ []) do
    try do
      FiscalInvoice
      |> preload(^preload)
      |> where(id: ^invoice_id, organization_id: ^organization_id)
      |> Repo.one!()
    rescue
      no_results in Ecto.NoResultsError ->
        Logger.error(
          "Error fetching fiscal invoice with invoice_id #{invoice_id} and organization_id #{organization_id}"
        )

        reraise no_results, __STACKTRACE__

      multiple_results in Ecto.MultipleResultsError ->
        Logger.error(
          "Error fetching fiscal invoice with invoice_id #{invoice_id} and organization_id #{organization_id}"
        )

        reraise multiple_results, __STACKTRACE__

      exception ->
        reraise exception, __STACKTRACE__
    end
  end

  def get_fiscal_invoice_by_reverse_fulfillment_id(
        reverse_fulfillment_id,
        operation_type \\ "return"
      ) do
    Repo.get_by(FiscalInvoice,
      reverse_fulfillment_id: reverse_fulfillment_id,
      operation_type: operation_type
    )
  end

  def get_sale_fiscal_invoice_by_order_id(organization_id, order_id) do
    FiscalInvoice
    |> join(:inner, [fi], f in assoc(fi, :fulfillment))
    |> where(
      [fi, f],
      f.organization_id == ^organization_id and f.order_id == ^order_id and
        fi.operation_type == ^"sale"
    )
    |> Repo.all()
  end

  defp add_invoice_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn param, dynamic ->
      apply_invoice_filter(param, dynamic)
    end)
  end

  defp apply_invoice_filter({key, value}, dynamic)
       when key in ["invoice_number", :invoice_number] do
    dynamic([fi, is, ip, p], ^dynamic and fi.invoice_number == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic) when key in ["serie_id", :serie_id] do
    dynamic([fi, is, ip, p], ^dynamic and fi.serie_id == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic) when key in ["invoice_type", :invoice_type] do
    dynamic([fi, is, ip, p], ^dynamic and (is_nil(is.id) or is.invoice_type == ^value))
  end

  defp apply_invoice_filter({key, value}, dynamic) when key in ["invoice_env", :invoice_env] do
    dynamic([fi, is, ip, p], ^dynamic and is.invoice_env == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic)
       when key in ["invoice_serie", :invoice_serie] do
    dynamic([fi, is, ip, p], ^dynamic and is.invoice_serie == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic) when key in ["location_id", :location_id] do
    dynamic([fi, is, ip, p], ^dynamic and is.location_id == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic)
       when key in ["transaction_id", :transaction_id] do
    dynamic([fi, is, ip, p], ^dynamic and p.transaction_id == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic)
       when key in ["operation_type", :operation_type] do
    dynamic([fi, is, ip, p], ^dynamic and fi.operation_type == ^value)
  end

  defp apply_invoice_filter({key, value}, dynamic)
       when key in ["reverse_fulfillment_id", :reverse_fulfillment_id] do
    dynamic([fi, is, ip, p], ^dynamic and fi.reverse_fulfillment_id == ^value)
  end

  defp apply_invoice_filter({_, _}, dynamic) do
    # Not a where parameter
    dynamic
  end

  def create_fiscal_invoice(organization_id, attrs) do
    %FiscalInvoice{organization_id: organization_id}
    |> FiscalInvoice.changeset(attrs)
    |> Repo.insert()
  end

  def import_fiscal_invoice(order, attrs) do
    order = Repo.preload(order, transaction: :payments)

    invoice_payments =
      order.transaction.payments
      |> Enum.filter(fn payment -> payment.status == "settled" end)
      |> Enum.map(&%{payment_id: &1.id})

    attrs = put_in(attrs, ["invoice_payments"], invoice_payments)

    %FiscalInvoice{organization_id: order.organization_id}
    |> FiscalInvoice.import_changeset(attrs)
    |> then(fn
      changeset when order.status not in ["paid", "canceled"] ->
        Ecto.Changeset.add_error(changeset, :order, "must be paid")

      changeset ->
        changeset
    end)
    |> Repo.insert()
  end

  def reprocess_fiscal_invoice(organization_id, fiscal_invoice_id) do
    with %FiscalInvoice{status: "pending", service: "vinco"} = invoice <-
           get_fiscal_invoice!(organization_id, fiscal_invoice_id),
         {:ok, _} <- enqueue_issuer_worker(invoice) do
      {:ok, "Issue Enqueued"}
    end
  end

  defp enqueue_issuer_worker(fiscal_invoice) do
    %{fiscal_invoice_id: fiscal_invoice.id, organization_id: fiscal_invoice.organization_id}
    |> Rms.Workers.InvoiceIssuerWorker.new()
    |> Oban.insert()
  end

  def get_tax_config!(organization_id, location_id) do
    LocationTax
    |> where([lt], lt.organization_id == ^organization_id)
    |> where([lt], lt.location_id == ^location_id)
    |> Repo.one!()
  end

  def get_tax_config(organization_id, location_id) do
    LocationTax
    |> where([lt], lt.organization_id == ^organization_id)
    |> where([lt], lt.location_id == ^location_id)
    |> Repo.one()
  end

  def update_fiscal_invoice(invoice, attrs) do
    invoice
    |> FiscalInvoice.update_changeset(attrs)
    |> Repo.update()
  end

  def list_location_tax(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, [])

    LocationTax
    |> where([lc], lc.organization_id == ^organization_id)
    |> where(^add_location_tax_filter_where(query_params))
    |> Repo.all()
  end

  defp add_location_tax_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["crt", :crt] ->
        dynamic([lc], ^dynamic and lc.crt == ^value)

      {key, value}, dynamic when key in ["ie", :ie] ->
        dynamic([lc], ^dynamic and lc.ie == ^value)

      {key, value}, dynamic when key in ["name", :name] ->
        dynamic([lc], ^dynamic and lc.name == ^value)

      {key, value}, dynamic when key in ["location_id", :location_id] ->
        dynamic([lc], ^dynamic and lc.location_id == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def get_location_tax!(organization_id, location_tax_id) do
    LocationTax
    |> where(id: ^location_tax_id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  def get_location_tax_by_location!(organization_id, location_id) do
    LocationTax
    |> where(location_id: ^location_id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  def update_location_tax(location_tax, attrs) do
    location_tax
    |> LocationTax.update_changeset(attrs)
    |> Repo.update()
  end

  def create_location_tax(organization_id, attrs) do
    %LocationTax{organization_id: organization_id}
    |> LocationTax.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:ie, :crt, :name, :current_tax_uf]},
      conflict_target: [:location_id]
    )
  end

  def list_product_taxes(organization_id, opts \\ []) do
    query_params = Keyword.get(opts, :query_params, [])

    ProductTaxes
    |> where([pt], pt.organization_id == ^organization_id)
    |> where(^add_product_taxes_filter_where(query_params))
    |> Repo.all()
  end

  defp add_product_taxes_filter_where(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["ncm", :ncm] ->
        dynamic([pt], ^dynamic and pt.ncm == ^value)

      {key, value}, dynamic when key in ["uf", :uf] ->
        dynamic([pt], ^dynamic and pt.uf == ^value)

      {key, value}, dynamic when key in ["origin", :origin] ->
        dynamic([pt], ^dynamic and pt.origin == ^value)

      {key, value}, dynamic when key in ["cfop", :cfop] ->
        dynamic([pt], ^dynamic and pt.cfop == ^value)

      {key, value}, dynamic when key in ["cest", :cest] ->
        dynamic([pt], ^dynamic and pt.cest == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  def get_product_taxes!(organization_id, product_taxes_id) do
    ProductTaxes
    |> where(id: ^product_taxes_id, organization_id: ^organization_id)
    |> Repo.one!()
  end

  def update_product_taxes(product_taxes, attrs) do
    product_taxes
    |> ProductTaxes.update_changeset(attrs)
    |> Repo.update()
  end

  def create_product_taxes(organization_id, attrs) do
    %ProductTaxes{organization_id: organization_id}
    |> ProductTaxes.changeset(attrs)
    |> Repo.insert()
  end

  def delete_product_taxes(product_taxes) do
    product_taxes
    |> ProductTaxes.delete_changeset()
    |> Repo.delete()
  end

  @default_chunk_size 1000
  @type bulk_result :: %{
          success: non_neg_integer(),
          errors: [{term(), term()}],
          total: non_neg_integer()
        }

  @doc """
  Inserts product taxes entries, allowing partial success.

  Returns a map containing:
  - :success - number of successfully inserted entries
  - :errors - list of failed entries with their errors
  - :total - total number of entries processed

  ## Options
  - `:chunk_size` - Size of chunks to process at a time. Defaults to #{@default_chunk_size}

  ## Examples

      iex> partial_create_product_taxes([
        %{organization_id: 1, ncm: "123", sku: "ABC", uf: "SP"},
        %{organization_id: 1, ncm: "invalid", sku: "DEF", uf: "SP"}
      ])
      %{
        success: 1,
        errors: [
          {%{organization_id: 1, ncm: "invalid", sku: "DEF", uf: "SP"},
           %{ncm: ["is invalid"]}}
        ],
        total: 2
      }
  """
  @spec partial_create_product_taxes([map()], Keyword.t()) :: bulk_result()
  def partial_create_product_taxes(entries, opts \\ []) when is_list(entries) do
    now = DateTime.truncate(DateTime.utc_now(), :second)
    timestamps = %{now: now}
    chunk_size = Keyword.get(opts, :chunk_size, @default_chunk_size)

    entries
    |> Stream.chunk_every(chunk_size)
    |> Stream.map(&process_chunk_with_errors(&1, timestamps))
    |> Enum.reduce(
      %{success: 0, errors: [], total: 0},
      &merge_results/2
    )
  end

  defp process_chunk_with_errors(chunk, timestamps) do
    {valid_entries, invalid_entries} = validate_entries_with_errors(chunk)
    valid_entries = Enum.uniq_by(valid_entries, &{&1.organization_id, &1.ncm, &1.sku, &1.uf})

    case insert_entries_safely(valid_entries, timestamps) do
      {:ok, count} ->
        %{
          success: count,
          errors: invalid_entries,
          total: length(chunk)
        }

      {:error, failed_entries} ->
        %{
          success: 0,
          errors: invalid_entries ++ failed_entries,
          total: length(chunk)
        }
    end
  end

  defp validate_entries_with_errors(entries) do
    entries
    |> Enum.map(&{&1, build_changeset(&1)})
    |> Enum.split_with(fn {_, changeset} -> changeset.valid? end)
    |> then(fn {valid, invalid} ->
      {
        Enum.map(valid, fn {_entry, changeset} -> prepare_entry_for_insert(changeset) end),
        Enum.map(invalid, fn {entry, changeset} -> {entry, format_changeset_errors(changeset)} end)
      }
    end)
  end

  defp build_changeset(attrs) do
    organization_id = attrs["organization_id"] || attrs[:organization_id]

    %ProductTaxes{organization_id: organization_id}
    |> ProductTaxes.changeset(attrs)
  end

  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end

  defp insert_entries_safely([], _timestamps), do: {:ok, 0}

  defp insert_entries_safely(entries, timestamps) do
    result =
      Repo.transaction(fn ->
        {count, _} =
          Repo.insert_all(
            ProductTaxes,
            entries,
            [placeholders: timestamps] ++ conflict_resolution_opts()
          )

        count
      end)

    with {:error, reason} <- result do
      Logger.error("failed to insert entries: #{inspect(reason)}")
      {:error, Enum.map(entries, &{&1, %{database: ["insertion failed"]}})}
    end
  rescue
    error ->
      Logger.error("error inserting entries: #{inspect(error)}")
      {:error, Enum.map(entries, &{&1, %{database: ["unexpected error"]}})}
  end

  defp prepare_entry_for_insert(changeset) do
    changeset
    |> Ecto.Changeset.apply_changes()
    |> Map.from_struct()
    |> Map.drop([:__meta__, :organization, :id])
    |> Map.put(:inserted_at, {:placeholder, :now})
    |> Map.put(:updated_at, {:placeholder, :now})
  end

  defp conflict_resolution_opts do
    [
      on_conflict: {:replace_all_except, [:id, :organization_id, :inserted_at]},
      conflict_target:
        {:unsafe_fragment,
         "(organization_id, COALESCE(ncm, ''), COALESCE(sku, ''), COALESCE(uf, '')) WHERE ((ncm IS NOT NULL) OR (sku IS NOT NULL) OR (uf IS NOT NULL))"}
    ]
  end

  defp merge_results(chunk_result, acc) do
    %{
      success: acc.success + chunk_result.success,
      errors: acc.errors ++ chunk_result.errors,
      total: acc.total + chunk_result.total
    }
  end

  def get_interstate_taxes(origin_uf, destiny_uf) do
    InterStateTaxes
    |> where([it], it.uf_origin == ^origin_uf and it.uf_destiny == ^destiny_uf)
    |> Repo.one!()
  end

  def get_taxes(organization_id, sku, nil, uf) do
    get_taxes_with_non_nil_ncm(organization_id, sku, uf)
  end

  def get_taxes(organization_id, sku, "", uf) do
    get_taxes_with_non_nil_ncm(organization_id, sku, uf)
  end

  def get_taxes(organization_id, sku, ncm, uf) do
    ProductTaxes
    |> where([pt], pt.organization_id == ^organization_id)
    |> where(^add_sku_filter(sku))
    |> where(^add_ncm_filter(ncm))
    |> where(^add_uf_filter(uf))
    |> Repo.all()
    |> find_best_tax(%{sku: sku, ncm: ncm, uf: uf})
  end

  defp get_taxes_with_non_nil_ncm(organization_id, sku, uf) do
    result =
      ProductTaxes
      |> where([pt], pt.organization_id == ^organization_id)
      |> where(^add_sku_filter(sku))
      |> where([pt], not is_nil(pt.ncm))
      |> where(^add_uf_filter(uf))
      |> Repo.all()
      |> find_best_tax(%{sku: sku, ncm: nil, uf: uf})

    case result do
      {:error, _} ->
        {:error,
         %{reason: "ncm is empty #{organization_id}, #{sku}, #{uf}", status: :missing_ncm}}

      tax ->
        tax
    end
  end

  defp add_sku_filter(nil), do: dynamic([pt], is_nil(pt.sku))
  defp add_sku_filter(sku), do: dynamic([pt], pt.sku == ^sku or is_nil(pt.sku))

  defp add_ncm_filter(nil), do: dynamic([pt], is_nil(pt.ncm))
  defp add_ncm_filter(ncm), do: dynamic([pt], pt.ncm == ^ncm or is_nil(pt.ncm))

  defp add_uf_filter(nil), do: dynamic([pt], is_nil(pt.uf))
  defp add_uf_filter(uf), do: dynamic([pt], pt.uf == ^uf or is_nil(pt.uf))

  defp find_best_tax([], params) do
    {:error,
     %{
       reason:
         "no valid config found for sku <#{params.sku}>, ncm <#{params.ncm}> and uf <#{params.uf}>",
       status: :missing_taxes
     }}
  end

  defp find_best_tax(taxes, params) do
    taxes
    |> Enum.map(&score_tax/1)
    |> Enum.max_by(fn {score, _tax} -> score end)
    |> case do
      {_score, tax} ->
        tax

      nil ->
        {:error,
         "no valid config found for sku <#{params.sku}>, ncm <#{params.ncm}> and uf <#{params.uf}>"}
    end
  end

  defp score_tax(tax) do
    score =
      if(is_nil(tax.sku), do: 0, else: 4) +
        if(is_nil(tax.ncm), do: 0, else: 2) +
        if is_nil(tax.uf), do: 0, else: 1

    {score, tax}
  end

  @doc """
  Gets fiscal settings for a given organization and location.
  If location-specific settings exist, they are returned.
  Otherwise, organization-wide settings are returned.
  """
  def get_fiscal_settings(organization_id, location_id) do
    query =
      from fs in FiscalSettings,
        where: fs.organization_id == ^organization_id,
        where: fs.location_id == ^location_id or is_nil(fs.location_id),
        order_by: [desc: not is_nil(fs.location_id)],
        limit: 1

    Repo.one(query)
  end

  @doc """
  Creates fiscal settings for a specific location within an organization.
  """
  def create_location_fiscal_settings(organization_id, location_id, attrs) do
    location_id =
      if(is_binary(location_id), do: String.to_integer(location_id), else: location_id)

    %FiscalSettings{organization_id: organization_id, location_id: location_id}
    |> FiscalSettings.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates organization-wide fiscal settings.
  """
  def create_organization_fiscal_settings(organization_id, attrs) do
    %FiscalSettings{organization_id: organization_id}
    |> FiscalSettings.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates fiscal settings
  """
  def update_fiscal_settings(settings, attrs) do
    settings
    |> FiscalSettings.update_changeset(attrs)
    |> Repo.update()
  end

  defdelegate import_sales_invoice_for_order(order),
    to: Rms.Fiscal.ImportSalesInvoiceForOrder,
    as: :call

  @doc """
  Imports a fiscal invoice from an XML string and associates it with a fulfillment.

  Validates required fields from the XML and the associated order's status.
  """
  def import_fulfillment_fiscal_invoice(organization_id, fulfillment, fiscal_invoice_xml) do
    fulfillment = Repo.preload(fulfillment, order: [transaction: [:payments, :customer]])

    with {:ok, parsed_data} <- XmlParser.parse(fiscal_invoice_xml),
         {:ok, extracted_data} <- extract_invoice_data(parsed_data) do
      attrs =
        build_invoice_attrs(fulfillment, fiscal_invoice_xml, extracted_data)

      %FiscalInvoice{organization_id: organization_id}
      |> FiscalInvoice.import_changeset(attrs)
      |> Repo.insert()
    end
  end

  defp extract_invoice_data(parsed_data) do
    df_key = get_in(parsed_data, [:infProt, :chNFe])
    invoice_number_str = get_in(parsed_data, [:ide, :nNF])

    auth_date_str =
      get_in(parsed_data, [:infProt, :dhRecbto]) || get_in(parsed_data, [:ide, :dhEmi])

    qr_code = get_in(parsed_data, [:infNFeSupl, :qrCode])

    with {:ok, parsed_auth_date} <- parse_auth_date(auth_date_str) do
      {:ok,
       %{
         df_key: df_key,
         invoice_number: invoice_number_str,
         authorized_at: parsed_auth_date,
         qr_code: qr_code || ""
       }}
    end
  end

  defp parse_auth_date(str) do
    case DateTime.from_iso8601(str) do
      {:ok, datetime, _offset} -> {:ok, DateTime.shift_zone!(datetime, "Etc/UTC")}
      {:error, reason} -> {:error, {:invalid_auth_date, str, reason}}
    end
  end

  defp build_invoice_attrs(fulfillment, xml, extracted_data) do
    invoice_payments_list = build_invoice_payments_attrs(fulfillment.order)

    customer_id = extract_customer_id(fulfillment.order)

    Map.merge(
      %{
        xml: xml,
        customer_id: customer_id,
        fulfillment_id: fulfillment.id,
        operation_type: "sale",
        service: "external",
        status: "authorized",
        invoice_payments: invoice_payments_list,
        metadata: %{"import_source" => "manual_xml", "imported_at" => DateTime.utc_now()}
      },
      extracted_data
    )
  end

  defp build_invoice_payments_attrs(%{transaction: %{payments: payments}})
       when is_list(payments) do
    payments
    |> Enum.filter(fn payment -> payment.status == "settled" end)
    |> Enum.map(&%{payment_id: &1.id})
  end

  defp build_invoice_payments_attrs(_), do: []

  defp extract_customer_id(%{transaction: %{customer: %{id: id}}}) do
    id
  end

  defp extract_customer_id(_), do: nil

  @doc """
  Creates a new upload request for product taxes.
  Returns a presigned URL and upload entry for tracking the upload.
  """
  def create_product_tax_upload_request(organization_id) do
    Rms.Storage.generate_presigned_post(
      organization_id,
      prefix: "product_taxes",
      content_type: "text/csv",
      expires_in: 3600,
      # 10MB
      content_length: 10 * 1024 * 1024
    )
  end

  @doc """
  Confirms a product tax upload by checking if the file exists in S3.
  Returns one of:
  - {:ok, :processing} - File exists and was scheduled for processing
  - {:error, :not_found} - File doesn't exist
  - {:error, :expired} - Upload has expired
  - {:error, :invalid_status, status} - Upload is not in pending status
  - {:error, reason} - Other error occurred
  """
  def confirm_product_tax_upload(%UploadEntry{} = upload) do
    case upload.status do
      "pending" -> do_confirm_upload(upload)
      status -> {:error, :invalid_status, status}
    end
  end

  defp do_confirm_upload(upload) do
    case Storage.exists?(upload.s3_key) do
      {:ok, true} -> schedule_processing(upload)
      {:ok, false} -> handle_missing_file(upload)
      {:error, reason} -> handle_error(reason)
    end
  end

  defp schedule_processing(upload) do
    Multi.new()
    |> Multi.update(:update_upload, UploadEntry.status_changeset(upload, "processing"))
    |> Multi.run(:schedule_job, fn _repo, _changes ->
      %{upload_id: upload.id, organization_id: upload.organization_id}
      |> ProcessProductTaxUpload.new()
      |> Oban.insert()
    end)
    |> Repo.transaction()
    |> case do
      {:ok, _changes} -> {:ok, :processing}
      {:error, _operation, error, _changes} -> {:error, error}
    end
  end

  defp handle_missing_file(upload) do
    if DateTime.compare(DateTime.utc_now(), upload.expires_at) == :gt do
      case Storage.update_upload_entry(upload, %{status: "expired"}) do
        {:ok, _upload} -> {:error, :expired}
        {:error, error} -> {:error, error}
      end
    else
      {:error, :not_found}
    end
  end

  defp handle_error(reason) do
    Logger.error("error confirming upload: #{inspect(reason)}")
    {:error, reason}
  end
end
