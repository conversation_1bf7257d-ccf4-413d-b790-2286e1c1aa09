defmodule Rms.Fiscal.FiscalInvoice do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Accounts.Organization
  alias Rms.Finance.Customer

  schema "fiscal_invoices" do
    field :operation_type, :string
    field :service, :string, default: "vinco"
    field :invoice_number, :integer
    field :external_id, :string
    field :df_key, :string
    field :status, :string, default: "pending"

    field :authorized_at, :utc_datetime, source: :autorized_at
    field :canceled_at, :utc_datetime
    field :integrated_erp, :boolean, default: false

    field :qr_code, :string
    field :xml, :string
    field :danfe, :string

    field :metadata, :map

    belongs_to :customer, Customer
    belongs_to :serie, InvoiceSerie
    belongs_to :fulfillment, Fulfillment
    belongs_to :reverse_fulfillment, ReverseFulfillment, type: :binary_id
    belongs_to :organization, Organization

    has_many :fiscal_invoice_errors, Rms.Errors.FiscalInvoiceError,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :invoice_payments, Rms.Fiscal.InvoicePayment,
      defaults: {Rms.Repo, :add_organization_id, []}

    has_many :invoice_items, Rms.Fiscal.InvoiceItem,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :reference_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(invoice, attrs) do
    invoice
    |> cast(attrs, [
      :operation_type,
      :service,
      :status,
      :external_id,
      :invoice_number,
      :df_key,
      :qr_code,
      :xml,
      :danfe,
      :metadata,
      :customer_id,
      :fulfillment_id,
      :reverse_fulfillment_id,
      :serie_id,
      :authorized_at,
      :canceled_at,
      :integrated_erp,
      :reference_at
    ])
    |> validate_required([:operation_type])
    |> validate_inclusion(:operation_type, ~w(sale return cancellation transfer))
    |> validate_inclusion(:status, ~w(pending authorized canceled))
    |> validate_inclusion(:service, ~w(vinco external))
    |> assoc_required?()
    |> assoc_constraint(:customer)
    |> assoc_constraint(:fulfillment)
    |> assoc_constraint(:reverse_fulfillment)
    |> assoc_constraint(:organization)
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
  end

  def import_changeset(invoice, attrs) do
    invoice
    |> cast(attrs, [
      :operation_type,
      :service,
      :status,
      :external_id,
      :invoice_number,
      :df_key,
      :qr_code,
      :xml,
      :danfe,
      :metadata,
      :customer_id,
      :authorized_at,
      :integrated_erp,
      :fulfillment_id,
      :reference_at
    ])
    |> validate_required([
      :invoice_number,
      :operation_type
    ])
    |> validate_required_by_operation_type()
    |> cast_assoc(:invoice_payments)
    |> validate_inclusion(:operation_type, ~w(sale cancellation))
    |> validate_inclusion(:status, ~w(authorized canceled))
    |> validate_inclusion(:service, ~w(external))
    |> assoc_constraint(:customer)
    |> assoc_constraint(:organization)
  end

  def update_changeset(invoice, attrs) do
    invoice
    |> cast(attrs, [
      :service,
      :status,
      :external_id,
      :df_key,
      :qr_code,
      :xml,
      :danfe,
      :metadata,
      :authorized_at,
      :canceled_at,
      :integrated_erp,
      :reference_at
    ])
  end

  defp validate_required_by_operation_type(changeset) do
    case get_field(changeset, :operation_type, "sale") do
      "sale" ->
        changeset
        |> validate_required([
          :df_key,
          :service,
          :status,
          :xml
        ])

      "cancellation" ->
        changeset
        |> validate_required([
          :service,
          :status,
          :xml
        ])

      _ ->
        changeset
    end
  end

  defp assoc_required?(changeset) do
    case get_field(changeset, :operation_type, "sale") do
      "sale" ->
        changeset
        |> cast_assoc(:invoice_payments, required: true)
        |> cast_assoc(:invoice_items, required: true)
        |> assoc_constraint(:serie)

      "cancellation" ->
        changeset

      "return" ->
        changeset
        |> assoc_constraint(:serie)

      "transfer" ->
        changeset
        |> assoc_constraint(:serie)
    end
  end
end
