defmodule Rms.Fiscal.FiscalSettings do
  use Ecto.Schema
  import Ecto.Changeset

  schema "fiscal_settings" do
    field :handle_sale, :boolean, default: true
    field :handle_return, :boolean, default: true
    field :handle_transfer, :boolean, default: true
    field :transfer_without_taxes, :boolean, default: false
    field :environment, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location

    timestamps()
  end

  def changeset(fiscal_settings, attrs) do
    fiscal_settings
    |> cast(attrs, [
      :handle_sale,
      :handle_return,
      :handle_transfer,
      :environment,
      :transfer_without_taxes
    ])
    |> validate_required([
      :handle_sale,
      :handle_return,
      :handle_transfer,
      :environment,
      :organization_id
    ])
    |> validate_inclusion(:environment, ~w(prod dev))
    |> assoc_constraint(:organization)
    |> assoc_constraint(:location)
    |> unique_constraint([:organization_id, :location_id], error_key: :location_id)
    |> unique_constraint([:organization_id])
  end

  def update_changeset(fiscal_settings, attrs) do
    fiscal_settings
    |> cast(attrs, [
      :handle_sale,
      :handle_return,
      :handle_transfer,
      :transfer_without_taxes,
      :environment
    ])
    |> validate_required([
      :handle_sale,
      :handle_return,
      :handle_transfer,
      :environment
    ])
    |> validate_inclusion(:environment, ~w(prod dev))
  end
end
