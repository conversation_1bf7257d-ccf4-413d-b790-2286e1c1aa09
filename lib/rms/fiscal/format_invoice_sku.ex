defmodule Rms.Fiscal.FormatInvoiceSku do
  def execute(organization_id, sku) do
    organization_id
    |> get_sku_format()
    |> format(sku)
  end

  defp format("with_padded_suffix", sku) do
    parts = String.split(sku, "_")

    Enum.map_join(parts, "", fn piece ->
      if piece == List.last(parts) do
        String.pad_leading(piece, 5, "0")
      else
        piece
      end
    end)
  end

  defp format("none", sku) do
    sku
  end

  defp format(_, sku) do
    Regex.replace(~r/[^a-zA-Z0-9]/, sku, "")
  end

  defp get_sku_format(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "sku_format") do
      %{value: %{"data" => value}} -> value
      _ -> nil
    end
  end
end
