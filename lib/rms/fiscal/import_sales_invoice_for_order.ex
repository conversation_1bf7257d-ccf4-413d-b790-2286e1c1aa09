defmodule Rms.Fiscal.ImportSalesInvoiceForOrder do
  require Logger
  alias Rms.Integrations
  alias Rms.Integrations.Shopify
  alias Rms.Integrations.Vinco
  alias Rms.Repo
  alias Rms.Integrator
  alias Rms.Accounts

  def call(order) do
    order = Repo.preload(order, fulfillments: :fiscal_invoices)

    with {:ok, fulfillments} <- fetch_eligible_fulfillments(order),
         {:ok, fulfillment_invoices} <- fetch_sale_invoices(order, fulfillments) do
      fulfillment_invoices
      |> Enum.reduce(Ecto.Multi.new(), fn {fulfillment_id, {fulfillment, invoice_xml}}, multi ->
        Ecto.Multi.run(multi, fulfillment_id, fn _, _ ->
          Rms.Fiscal.import_fulfillment_fiscal_invoice(
            order.organization_id,
            fulfillment,
            invoice_xml
          )
        end)
      end)
      |> Repo.transaction()
    end
  end

  defp fetch_eligible_fulfillments(order) do
    fulfillments =
      Enum.reject(order.fulfillments, fn fulfillment ->
        Enum.any?(fulfillment.fiscal_invoices, &(&1.operation_type == "sale"))
      end)

    if Enum.empty?(fulfillments) do
      {:error, :no_fulfillments_without_fiscal_invoice}
    else
      {:ok, fulfillments}
    end
  end

  defp fetch_sale_invoices(%{source: "shopify"} = order, [fulfillment]) do
    case fetch_invoice_for_order(order) do
      {:ok, invoice_xml} ->
        {:ok, %{fulfillment.id => {fulfillment, invoice_xml}}}

      {:error, _reason} ->
        {:error, :invoice_not_found}
    end
  end

  defp fetch_sale_invoices(order, _fulfillments) do
    Logger.error(
      "tried to fetch sale invoices for multiple fulfillments",
      order_id: order.id,
      organization_id: order.organization_id
    )

    raise "unimplemented"
  end

  defp fetch_invoice_for_order(%{source: "shopify"} = order) do
    with {:error, :not_found} <- fetch_from_shopify(order),
         {:error, :not_found} <- fetch_from_integrator(order) do
      {:error, :invoice_not_found}
    else
      {:ok, invoice_xml} -> {:ok, invoice_xml}
    end
  end

  defp fetch_invoice_for_order(order) do
    fetch_from_integrator(order)
  end

  defp fetch_from_shopify(%{organization_id: org_id, external_id: ext_id} = order) do
    with {:ok, shopify_order} <- get_shopify_order(org_id, ext_id),
         {:ok, df_key} <- fetch_nfce_key(shopify_order),
         {:ok, invoice_xml} <- fetch_invoice_from_vinco(order, df_key) do
      {:ok, invoice_xml}
    else
      _ -> {:error, :not_found}
    end
  end

  defp fetch_from_integrator(%{
         organization_id: org_id,
         location_id: loc_id,
         name: order_name,
         source: order_source
       }) do
    organization_token = Accounts.get_api_token_by_organization_id!(org_id)

    case Integrator.fetch_fiscal_invoice(
           organization_token,
           org_id,
           loc_id,
           order_name,
           order_source
         ) do
      {:ok, %{"data" => %{"xml" => invoice_xml}}}
      when is_binary(invoice_xml) and invoice_xml != "" ->
        {:ok, invoice_xml}

      _ ->
        {:error, :not_found}
    end
  end

  defp get_shopify_order(org_id, ext_id) do
    shopify_credential = Integrations.get_shopify_credential!(org_id)
    shopify_client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    selection = """
      name
      customAttributes {
        key
        value
      }
    """

    Shopify.get_order!(shopify_client, ext_id, selection)
  end

  defp fetch_nfce_key(shopify_order) do
    shopify_order
    |> get_in(["data", "order", "customAttributes"])
    |> Enum.find(fn attr -> attr["key"] == "nfce-key" end)
    |> case do
      %{"value" => value} when is_binary(value) and value != "" -> {:ok, value}
      _ -> {:error, :nfce_key_not_found}
    end
  end

  defp fetch_invoice_from_vinco(order, df_key) do
    vinco_key = Vinco.get_key!(order.organization_id, order.location_id)

    case Vinco.get_fiscal_invoice("NFCe", df_key, "prod", vinco_key) do
      {:ok, %{"XmlDFe" => invoice_xml}} when is_binary(invoice_xml) -> {:ok, invoice_xml}
      _ -> {:error, :invoice_not_found}
    end
  end
end
