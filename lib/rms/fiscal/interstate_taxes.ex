defmodule Rms.Fiscal.InterStateTaxes do
  use Ecto.Schema

  import Ecto.Changeset

  schema "interstate_taxes" do
    field :uf_origin, :string
    field :uf_destiny, :string

    field :icms_percentage, :decimal

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(invoice, attrs) do
    invoice
    |> cast(attrs, [
      :uf_origin,
      :uf_destiny,
      :icms_percentage
    ])
    |> validate_required([
      :uf_origin,
      :uf_destiny,
      :icms_percentage
    ])
  end
end
