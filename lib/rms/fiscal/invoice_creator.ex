defmodule Rms.Fiscal.InvoiceCreator do
  alias Rms.Fiscal
  alias Rms.Finance.Transaction

  alias Ecto.Multi

  require OpenTelemetry.Tracer

  def execute(%Transaction{} = transaction) do
    attributes = [
      {"var.transaction_id", transaction.id}
    ]

    OpenTelemetry.Tracer.with_span "#{__MODULE__}.execute/1", %{
      attributes: attributes
    } do
      transaction =
        Rms.Repo.preload(transaction, [
          :payments,
          :customer,
          order: [fulfillments: [:line_items, :order]]
        ])

      Multi.new()
      |> create_invoices(transaction.order.fulfillments, transaction)
      |> Rms.Repo.transaction()
      |> case do
        {:error, multi_name, error, changes} ->
          Rms.Errors.LoggingError.execute({multi_name, error, changes})
          {:error, {multi_name, error, changes}}

        rest ->
          rest
      end
    end
  end

  defp create_invoices(multi, fulfillments, transaction) do
    Enum.reduce(fulfillments, multi, fn fulfillment, multi ->
      multi_name = buid_multi_name(fulfillment)

      multi
      |> Multi.run(multi_name, fn _, _ ->
        with {:ok, invoice} <- create_invoice(fulfillment, transaction),
             {:ok, _worker} <- enqueue_worker(invoice) do
          {:ok, invoice}
        end
      end)
    end)
  end

  defp create_invoice(%{shipping_method: shipping_method}, _transaction)
       when shipping_method != "in-store" do
    {:ok, "sent to e-commerce"}
  end

  defp create_invoice(%{shipping_method: "delivery"}, _transaction) do
    {:ok, "sent to e-commerce"}
  end

  defp create_invoice(
         %{
           id: id,
           shipping_method: shipping_method,
           order: %{location_id: location_id},
           line_items: line_items
         },
         transaction
       ) do
    with {:ok, settings} <- get_fiscal_settings(transaction.organization_id, location_id),
         :ok <- validate_handle_sale(settings),
         {:ok, nf_type} <- nf_type_by_customer(shipping_method, transaction.customer),
         {:ok, invoice_serie} <-
           invoice_serie(nf_type, transaction, location_id, settings.environment) do
      create("sale", invoice_serie, transaction, line_items, id)
    end
  end

  defp get_fiscal_settings(organization_id, location_id) do
    case Fiscal.get_fiscal_settings(organization_id, location_id) do
      nil -> {:error, "no fiscal settings found"}
      settings -> {:ok, settings}
    end
  end

  defp validate_handle_sale(%{handle_sale: true}), do: :ok

  defp validate_handle_sale(_),
    do: {:error, "emitting sale invoices is not enabled for this location"}

  defp buid_multi_name(%{shipping_method: shipping_method, id: id}) do
    "fulfillment:#{id}-shipping_method:#{shipping_method}"
  end

  defp nf_type_by_customer("in-store", nil), do: {:ok, "nfc"}
  defp nf_type_by_customer("in-store", %{document_type: "cpf"}), do: {:ok, "nfc"}
  defp nf_type_by_customer("in-store", %{document_type: "cnpj"}), do: {:ok, "nfc"}

  defp nf_type_by_customer("in-store", customer) do
    {:error, "invalid document type #{inspect(customer)}"}
  end

  defp nf_type_by_customer(_shipping_method, nil),
    do: {:error, "must exist a client for theses shipping methods"}

  defp nf_type_by_customer(_shipping_method, _customer), do: {:ok, "nf"}

  defp invoice_serie(nf_type, transaction, location_id, env) do
    case Fiscal.list_invoice_serie(transaction.organization_id,
           query_params: [
             {:status, "active"},
             {:invoice_type, nf_type},
             {:location_id, location_id},
             {:invoice_env, env}
           ]
         ) do
      [serie] ->
        {:ok, serie}

      _ ->
        {:error, "can not find a avaliable serie"}
    end
  end

  defp invoice_payments(payments) do
    payments
    |> Enum.filter(fn payment -> payment.status == "settled" end)
    |> Enum.map(fn payment ->
      %{payment_id: payment.id}
    end)
  end

  defp invoice_line_items(line_items) do
    Enum.map(line_items, fn line_item ->
      %{line_item_id: line_item.id}
    end)
  end

  defp create("sale", invoice_serie, transaction, line_items, fulfillment_id) do
    attrs = %{
      operation_type: "sale",
      serie_id: invoice_serie.id,
      fulfillment_id: fulfillment_id,
      customer_id: Map.get(transaction.customer || %{}, :id, nil),
      invoice_payments: invoice_payments(transaction.payments),
      invoice_items: invoice_line_items(line_items)
    }

    {:ok, f} = Fiscal.create_fiscal_invoice(transaction.organization_id, attrs)

    {:ok, Rms.Repo.preload(f, [:serie])}
  end

  defp enqueue_worker("sent to e-commerce") do
    {:ok, "sent to e-commerce"}
  end

  defp enqueue_worker(fiscal_invoice) do
    %{fiscal_invoice_id: fiscal_invoice.id, organization_id: fiscal_invoice.organization_id}
    |> Rms.Workers.InvoiceIssuerWorker.new()
    |> Oban.insert()
  end
end
