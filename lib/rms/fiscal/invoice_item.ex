defmodule Rms.Fiscal.InvoiceItem do
  use Ecto.Schema

  import Ecto.Changeset

  schema "invoices_items" do
    belongs_to :fiscal_invoice, Rms.Fiscal.FiscalInvoice
    belongs_to :line_item, Rms.Commerce.Orders.LineItem
    belongs_to :organization, Rms.Accounts.Organization

    field :reference_at, :utc_datetime
  end

  @doc false
  def changeset(line_item, attrs) do
    line_item
    |> cast(attrs, [:line_item_id, :reference_at])
    |> assoc_constraint(:fiscal_invoice)
    |> assoc_constraint(:line_item)
    |> assoc_constraint(:organization)
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
  end
end
