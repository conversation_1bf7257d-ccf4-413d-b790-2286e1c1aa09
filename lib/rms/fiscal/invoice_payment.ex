defmodule Rms.Fiscal.InvoicePayment do
  alias Rms.Finance
  alias Rms.Finance.Payment
  use Ecto.Schema

  import Ecto.Changeset

  schema "invoices_payments" do
    belongs_to :fiscal_invoice, Rms.Fiscal.FiscalInvoice
    belongs_to :payment, Rms.Finance.Payment
    belongs_to :organization, Rms.Accounts.Organization

    field :reference_at, :utc_datetime
  end

  @doc false
  def changeset(invoice_payment, attrs) do
    invoice_payment
    |> cast(attrs, [:payment_id, :reference_at])
    |> validate_payment()
    |> assoc_constraint(:fiscal_invoice)
    |> assoc_constraint(:payment)
    |> assoc_constraint(:organization)
    |> prepare_changes(fn changeset ->
      if is_nil(get_field(changeset, :reference_at)) do
        put_change(changeset, :reference_at, DateTime.utc_now() |> DateTime.truncate(:second))
      else
        changeset
      end
    end)
  end

  defp validate_payment(changeset) do
    changeset
    |> validate_required([:payment_id])
    |> prepare_changes(fn changeset ->
      %Payment{status: status} =
        Finance.get_payment!(
          fetch_field!(changeset, :organization_id),
          fetch_field!(changeset, :payment_id)
        )

      case status do
        "settled" ->
          changeset

        _ ->
          add_error(
            changeset,
            :invoice_payment,
            "payment not settled"
          )
      end
    end)
  end
end
