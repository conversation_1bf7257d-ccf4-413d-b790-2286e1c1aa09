defmodule Rms.Fiscal.InvoiceSerie do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Location
  alias Rms.Accounts.Organization

  schema "invoice_series" do
    field :invoice_type, :string
    field :invoice_serie, :integer
    field :invoice_env, :string
    field :status, :string
    field :available_number, :integer

    belongs_to :location, Location
    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(serie, attrs) do
    serie
    |> cast(attrs, [
      :invoice_type,
      :invoice_serie,
      :invoice_env,
      :status,
      :available_number,
      :location_id
    ])
    |> validate_required([
      :available_number,
      :invoice_type,
      :invoice_serie,
      :invoice_env,
      :status,
      :location_id
    ])
    |> validate_inclusion(:status, ~w(active inactive))
    |> validate_inclusion(:invoice_env, ~w(prod dev))
    |> validate_inclusion(:invoice_type, ~w(nf nfc))
    |> assoc_constraint(:location)
    |> assoc_constraint(:organization)
    |> validate_number()
    |> unique_constraint([:invoice_type, :invoice_env, :location_id])
  end

  @doc false
  def update_changeset(serie, attrs) do
    serie
    |> cast(attrs, [:status])
    |> unique_constraint([:invoice_type, :invoice_env, :location_id], error_key: :status)
  end

  defp validate_number(changeset) do
    case fetch_change(changeset, :available_number) do
      {:ok, _} ->
        case changeset.data.available_number do
          nil ->
            changeset

          _ ->
            add_error(
              changeset,
              :available_number,
              "cannot update the available number when there is already one"
            )
        end

      :error ->
        changeset
    end
  end
end
