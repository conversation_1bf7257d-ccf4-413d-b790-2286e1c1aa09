defmodule Rms.Fiscal.LocationTax do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Location
  alias Rms.Accounts.Organization

  schema "location_taxes" do
    field :ie, :string
    field :crt, :string
    field :name, :string
    field :current_tax_uf, :string

    belongs_to :location, Location
    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(location_tax, attrs) do
    location_tax
    |> cast(attrs, [
      :ie,
      :crt,
      :name,
      :location_id,
      :current_tax_uf
    ])
    |> validate_required([
      :ie,
      :crt,
      :name,
      :location_id
    ])
    |> foreign_key_constraint(:location_id)
    |> foreign_key_constraint(:organization_id)
  end

  def update_changeset(location_tax, attrs) do
    location_tax
    |> cast(attrs, [
      :ie,
      :crt,
      :name,
      :current_tax_uf
    ])
  end

  @doc false
  def assoc_changeset(location_tax, attrs) do
    location_tax
    |> cast(attrs, [
      :ie,
      :crt,
      :name,
      :current_tax_uf
    ])
    |> validate_required([:ie, :crt, :name])
    |> assoc_constraint(:location)
    |> assoc_constraint(:organization)
  end
end
