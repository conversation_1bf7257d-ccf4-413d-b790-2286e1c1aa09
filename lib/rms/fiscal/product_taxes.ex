defmodule Rms.Fiscal.ProductTaxes do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization

  schema "product_taxes" do
    field :sku, :string
    field :ncm, :string
    field :uf, :string
    field :origin, :string

    field :cfop, :string
    field :cest, :string

    field :cst_icms, :string
    field :cst_pis, :string
    field :cst_cofins, :string

    field :icms_percentage, :decimal
    field :fcp_percentage, :decimal
    field :pis_percentage, :decimal
    field :cofins_percentage, :decimal
    field :icms_base_reduction, :decimal, default: Decimal.new("1")
    field :additional_info_message, :string

    field :benefit_code, :string, default: nil

    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(tax, attrs) do
    tax
    |> cast(attrs, [
      :sku,
      :ncm,
      :uf,
      :origin,
      :cfop,
      :cest,
      :cst_icms,
      :cst_pis,
      :cst_cofins,
      :icms_percentage,
      :fcp_percentage,
      :pis_percentage,
      :cofins_percentage,
      :icms_base_reduction,
      :benefit_code,
      :additional_info_message
    ])
    |> validate_required([
      :origin,
      :cfop,
      :cst_icms,
      :cst_pis,
      :cst_cofins,
      :icms_percentage,
      :fcp_percentage,
      :pis_percentage,
      :cofins_percentage
    ])
    |> assoc_constraint(:organization)
    |> unique_constraint([:organization_id])
    |> unique_constraint(:sku,
      name: "product_taxes_org_id_ncm_uf_sku_partial_idx",
      message: "a tax with this SKU, NCM and UF combination already exists"
    )
  end

  @doc false
  def update_changeset(tax, attrs) do
    tax
    |> cast(attrs, [
      :sku,
      :ncm,
      :uf,
      :origin,
      :cfop,
      :cest,
      :cst_icms,
      :cst_pis,
      :cst_cofins,
      :icms_percentage,
      :fcp_percentage,
      :pis_percentage,
      :cofins_percentage,
      :icms_base_reduction,
      :benefit_code,
      :additional_info_message
    ])
    |> validate_required([
      :origin,
      :cfop,
      :cst_icms,
      :cst_pis,
      :cst_cofins,
      :icms_percentage,
      :fcp_percentage,
      :pis_percentage,
      :cofins_percentage
    ])
  end

  @doc false
  def delete_changeset(tax) do
    tax
    |> change()
  end
end
