defmodule Rms.Fiscal.SetTaxes do
  alias Rms.Fiscal

  @south_southeast ~w(SP RJ MG RS SC PR)
  @north_northeast_center_west ~w(AC AP AM PA RO RR TO AL BA CE MA PB PE PI RN SE GO MT MS ES DF)
  @special_origin_codes ~w(1 2 6 7)

  def set_taxes(line_item, base_value, origin_uf, destiny_uf, bc_replacement \\ true)

  def set_taxes(line_item, base_value, origin_uf, destiny_uf, bc_replacement) do
    with %Rms.Fiscal.ProductTaxes{} = taxes <-
           get_taxes(
             line_item,
             origin_uf
           ) do
      icms = set_icms(taxes, base_value, origin_uf, destiny_uf)

      new_base_value =
        if bc_replacement do
          Decimal.sub(base_value, icms.value_icms)
        else
          base_value
        end

      pis = set_pis(taxes, new_base_value)
      cofins = set_cofins(taxes, new_base_value)

      {:ok,
       %{
         benefit_code: taxes.benefit_code,
         cfop: taxes.cfop,
         cest: taxes.cest,
         ncm: taxes.ncm,
         icms: icms,
         pis: pis,
         cofins: cofins,
         additional_info_message: taxes.additional_info_message
       }}
    end
  end

  defp set_icms_percentage(origin_uf, destiny_uf, product_origin, _local_icms_percentage)
       when product_origin in @special_origin_codes and origin_uf != destiny_uf do
    Decimal.new("0.04")
  end

  defp set_icms_percentage(origin_uf, destiny_uf, _product_origin, local_icms_percentage)
       when origin_uf == destiny_uf do
    local_icms_percentage
  end

  defp set_icms_percentage(origin_uf, destiny_uf, _product_origin, _local_icms_percentage) do
    get_interstate_icms_percentage(origin_uf, destiny_uf)
  end

  defp get_interstate_icms_percentage(origin_uf, destiny_uf) do
    case {origin_uf in @south_southeast, destiny_uf in @north_northeast_center_west} do
      {true, true} -> Decimal.new("0.07")
      _ -> Decimal.new("0.12")
    end
  end

  defp set_icms(taxes, base_value, origin_uf, destiny_uf) do
    origin = taxes.origin

    icms_percentage = set_icms_percentage(origin_uf, destiny_uf, origin, taxes.icms_percentage)

    fcp_percentage = taxes.fcp_percentage

    base_value =
      if taxes.cst_icms in ["60", "400", "102", "500", "101"] do
        Decimal.new("0")
      else
        base_value
      end

    original_base_value = base_value
    base_value = Decimal.mult(base_value, taxes.icms_base_reduction)
    icms_reduction = Decimal.sub(original_base_value, base_value)

    base_reduction_percentual =
      Decimal.mult(Decimal.sub(Decimal.new("1"), taxes.icms_base_reduction), Decimal.new("100"))

    base_reduction? =
      if Decimal.compare(base_reduction_percentual, Decimal.new("0")) == :eq do
        false
      else
        true
      end

    %{
      origin: origin,
      cstcsosn: taxes.cst_icms,
      mod_bc: "3",
      tributary_group: "0",
      deseneration_reason: "9",
      mod_bc_st: "6",
      reduction_percentual: Decimal.round(base_reduction_percentual, 2),
      icms_reduction: Decimal.round(icms_reduction, 2),
      value_bc: Decimal.round(base_value, 2),
      percentage_icms: Decimal.round(Decimal.mult(Decimal.new("100"), icms_percentage), 2),
      value_icms: Decimal.round(Decimal.mult(base_value, icms_percentage), 2),
      percentage_fcp: Decimal.round(Decimal.mult(Decimal.new("100"), fcp_percentage), 2),
      value_fcp: Decimal.round(Decimal.mult(base_value, fcp_percentage), 2),
      base_reduction: base_reduction?
    }
  end

  defp set_pis(taxes, base_value) do
    pis_percentage = taxes.pis_percentage

    base_value =
      if Decimal.compare(pis_percentage, Decimal.new("0")) == :eq do
        Decimal.new("0")
      else
        base_value
      end

    %{
      cst: taxes.cst_pis,
      value_bc: base_value,
      percentage_pis: Decimal.round(Decimal.mult(Decimal.new("100"), pis_percentage), 2),
      value_pis: Decimal.round(Decimal.mult(base_value, pis_percentage), 2)
    }
  end

  defp set_cofins(taxes, base_value) do
    cofins_percentage = taxes.cofins_percentage

    base_value =
      if Decimal.compare(cofins_percentage, Decimal.new("0")) == :eq do
        Decimal.new("0")
      else
        base_value
      end

    %{
      cst: taxes.cst_cofins,
      value_bc: base_value,
      percentage_cofins: Decimal.round(Decimal.mult(Decimal.new("100"), cofins_percentage), 2),
      value_cofins: Decimal.round(Decimal.mult(base_value, cofins_percentage), 2)
    }
  end

  defp get_taxes(line_item, uf) do
    Fiscal.get_taxes(
      line_item.organization_id,
      line_item.sku,
      get_line_item_ncm(line_item),
      uf
    )
  end

  defp get_line_item_ncm(%{product_variant: %{product: %{ncm: ncm}}}) when not is_nil(ncm) do
    ncm
  end

  defp get_line_item_ncm(_line_item), do: nil
end
