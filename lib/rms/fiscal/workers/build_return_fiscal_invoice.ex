defmodule Rms.Fiscal.Workers.BuildReturnFiscalInvoice do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :fiscal_invoices

  require OpenTelemetry.Tracer
  require Logger

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: args}) do
    execute(args)
  end

  def execute(
        %{
          "reverse_fulfillment_id" => reverse_fulfillment_id,
          "organization_id" => organization_id,
          "env" => env
        } = _args
      ) do
    with {:ok, original_order} <- get_original_order(reverse_fulfillment_id),
         %{location: location} <-
           Rms.Commerce.Fulfillments.get_reverse_fulfillment_by_id!(
             organization_id,
             reverse_fulfillment_id
           )
           |> Rms.Repo.preload(location: [:address]),
         {:ok, fiscal_invoice} <- get_fiscal_invoice(original_order.id, organization_id),
         {:ok, returned_items} <- get_returned_items(reverse_fulfillment_id, organization_id),
         {:ok, invoice_serie} <- invoice_serie(organization_id, original_order.location_id, env),
         {:ok, parsed_xml} <- Rms.Fiscal.XmlParser.parse(fiscal_invoice.xml),
         {:ok, items} <-
           Rms.Integrations.Vinco.Prepare.Return.Items.execute(
             organization_id,
             invoice_serie,
             returned_items,
             parsed_xml
           ),
         {:ok, customer} <-
           Rms.Integrations.Vinco.Prepare.Return.Customer.execute(invoice_serie, parsed_xml),
         {:ok, issuer} <-
           Rms.Integrations.Vinco.Prepare.Return.Issuer.execute(invoice_serie),
         {:ok, total} <-
           Rms.Integrations.Vinco.Prepare.Total.prepare_total(%{det: items, emit: issuer}),
         {:ok, additional_info} <-
           Rms.Integrations.Vinco.Prepare.Return.AdditionalInfo.execute(
             fiscal_invoice,
             original_order,
             location
           ),
         {:ok, return_fiscal_invoice} <-
           create_fiscal_invoice(
             organization_id,
             fiscal_invoice,
             invoice_serie,
             reverse_fulfillment_id
           ) do
      returned_fiscal_invoice =
        %{
          Sincrono: true,
          IdKeySistema: "#{return_fiscal_invoice.id}",
          Venda: "#{return_fiscal_invoice.id}",
          PDV: "0",
          ide: %{
            cUF: issuer.enderEmit.cMun |> String.slice(0, 2),
            cMunFG: issuer.enderEmit.cMun,
            natOp: "Devolucao",
            mod: "55",
            serie: invoice_serie.invoice_serie,
            nNF: return_fiscal_invoice.invoice_number,
            dhEmi: date_time(),
            tpAmb: invoice_env(invoice_serie.invoice_env),
            NFref: [%{refNFe: fiscal_invoice.df_key |> String.replace_prefix("CFe", "")}],
            indFinal: "1",
            tpNF: "0",
            tpImp: "1",
            idDest: if(interstate?(parsed_xml), do: "2", else: "1"),
            tpEmis: "1",
            finNFe: "4",
            procEmi: "0",
            indPres: "1"
          },
          emit: issuer,
          dest: customer,
          det: items,
          total: %{ICMSTot: total},
          transp: %{
            modFrete: "9",
            vol: [
              %{
                qVol: "1"
              }
            ]
          },
          pag: %{
            detPag: [%{tPag: "90", vPag: "0"}]
          },
          infAdic: %{
            infCpl: additional_info
          },
          infRespTec: %{
            CNPJ: "46991783000113",
            xContato: "Iglu Tecnologia",
            email: "<EMAIL>",
            fone: "16991080570"
          }
        }
        |> add_aut_xml(issuer)

      {:ok, returned_fiscal_invoice}
    else
      error -> {:error, error}
    end
  end

  defp add_aut_xml(obj, issuer) do
    if issuer.enderEmit[:UF] == "BA" do
      Map.put(obj, :autXML, [%{CNPJ: "13937073000156"}])
    else
      obj
    end
  end

  defp interstate?(parsed_xml) do
    with origin_uf <- get_in(parsed_xml, [:emit, :enderEmit, :UF]),
         dest_uf <- get_in(parsed_xml, [:dest, :enderDest, :UF]),
         false <- is_nil(origin_uf) or origin_uf == "",
         false <- is_nil(dest_uf) or dest_uf == "" do
      origin_uf != dest_uf
    else
      _ -> false
    end
  end

  defp date_time() do
    date = DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase)

    month = String.pad_leading("#{date.month}", 2, "0")
    day = String.pad_leading("#{date.day}", 2, "0")
    hour = String.pad_leading("#{date.hour}", 2, "0")
    minute = String.pad_leading("#{date.minute}", 2, "0")
    second = String.pad_leading("#{date.second}", 2, "0")

    "#{date.year}-#{month}-#{day}T#{hour}:#{minute}:#{second}-03:00"
  end

  defp invoice_env(env) do
    if env == "prod" do
      "1"
    else
      "2"
    end
  end

  defp create_fiscal_invoice(
         organization_id,
         fiscal_invoice,
         invoice_serie,
         reverse_fulfillment_id
       ) do
    with nil <-
           Rms.Fiscal.get_fiscal_invoice_by_reverse_fulfillment_id(
             reverse_fulfillment_id,
             "return"
           ),
         {:ok, returned_fiscal_invoice} <-
           Rms.Fiscal.create_fiscal_invoice(
             organization_id,
             %{
               status: "pending",
               operation_type: "return",
               service: "vinco",
               reverse_fulfillment_id: reverse_fulfillment_id,
               serie_id: invoice_serie.id,
               metadata: %{
                 "NF referenciada" => fiscal_invoice.df_key,
                 fiscal_invoice_id: fiscal_invoice.id
               }
             }
           ),
         %Rms.Fiscal.FiscalInvoice{} = invoice <-
           Rms.Fiscal.get_fiscal_invoice!(organization_id, returned_fiscal_invoice.id) do
      {:ok, invoice}
    else
      %Rms.Fiscal.FiscalInvoice{} = invoice ->
        {:ok, invoice}

      error ->
        {:error, error}
    end
  end

  defp get_returned_items(reverse_fulfillment_id, organization_id) do
    returned_items =
      Rms.Commerce.Orders.OrderData.get_reverse_fulfillment_details(
        reverse_fulfillment_id,
        organization_id
      )

    {:ok, returned_items}
  end

  defp get_fiscal_invoice(original_order_id, organization_id) do
    case Rms.Fiscal.get_sale_fiscal_invoice_by_order_id(organization_id, original_order_id) do
      [fiscal_invoice | _] -> {:ok, fiscal_invoice}
      [] -> {:error, "Fiscal invoice not found"}
    end
  end

  defp get_original_order(reverse_fulfillment_id) do
    case Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
           reverse_fulfillment_id
         ) do
      [original_order | _] -> {:ok, original_order}
      [] -> {:error, "Original order not found"}
    end
  end

  defp invoice_serie(organization_id, location_id, env) do
    case Rms.Fiscal.list_invoice_serie(organization_id,
           query_params: [
             {:status, "active"},
             {:invoice_type, "nf"},
             {:location_id, location_id},
             {:invoice_env, env}
           ]
         ) do
      [serie] ->
        {:ok, Rms.Repo.preload(serie, [:organization, location: [:address]])}

      _ ->
        {:error, "can not find a avaliable serie"}
    end
  end
end
