defmodule Rms.Fiscal.Workers.BuildTransferFiscalInvoice do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :fiscal_invoices

  require OpenTelemetry.Tracer
  require Logger

  alias Rms.Integrations.Vinco.Prepare.Issuer

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: args}) do
    execute(args)
  end

  def execute(
        %{
          "reverse_fulfillment_id" => reverse_fulfillment_id,
          "organization_id" => organization_id,
          "env" => env
        } = _args
      ) do
    with {:ok, original_order} <- get_original_order(reverse_fulfillment_id),
         %{location: location} <-
           Rms.Commerce.Fulfillments.get_reverse_fulfillment_by_id!(
             organization_id,
             reverse_fulfillment_id
           )
           |> Rms.Repo.preload(location: [:address]),
         {:ok, invoice_serie} <- invoice_serie(organization_id, original_order.location_id, env),
         {:ok, returned_items} <- get_returned_items(reverse_fulfillment_id),
         organization <- Rms.Accounts.get_organization!(organization_id),
         issuer <- Issuer.prepare_issuer(organization, get_location_from_order(original_order)),
         receiver <-
           Issuer.prepare_issuer(organization, location)
           |> prepare_receiver(env),
         {:ok, items, additional_info} <-
           Rms.Integrations.Vinco.Prepare.Transfer.Items.execute(
             organization_id,
             original_order.location_id,
             invoice_serie,
             returned_items,
             issuer.enderEmit[:UF],
             receiver.enderDest[:UF]
           ),
         {:ok, additional_info} <-
           Rms.Integrations.Vinco.Prepare.Transfer.AdditionalInfo.execute(
             additional_info,
             original_order,
             location
           ),
         {:ok, total} <-
           Rms.Integrations.Vinco.Prepare.Total.prepare_total(%{det: items, emit: issuer}),
         {:ok, fiscal_invoice} <-
           create_fiscal_invoice(
             organization_id,
             invoice_serie,
             reverse_fulfillment_id
           ) do
      transferred_fiscal_invoice =
        %{
          Sincrono: true,
          IdKeySistema: "#{fiscal_invoice.id}",
          Venda: "#{fiscal_invoice.id}",
          PDV: "0",
          ide: %{
            cUF: issuer.enderEmit.cMun |> String.slice(0, 2),
            cMunFG: issuer.enderEmit.cMun,
            natOp: get_nat_operation(issuer, receiver),
            mod: "55",
            serie: invoice_serie.invoice_serie,
            nNF: fiscal_invoice.invoice_number,
            dhEmi: date_time(),
            tpAmb: invoice_env(invoice_serie.invoice_env),
            indFinal: "1",
            tpNF: "1",
            tpImp: "1",
            idDest: if(interstate?(issuer, receiver), do: "2", else: "1"),
            tpEmis: "1",
            finNFe: "1",
            procEmi: "0",
            indPres: "1"
          },
          emit: issuer,
          dest: receiver,
          det: items,
          total: %{ICMSTot: total},
          transp: %{
            modFrete: "9",
            vol: [
              %{
                qVol: "1"
              }
            ]
          },
          pag: %{
            detPag: [%{tPag: "90", vPag: "0"}]
          },
          infAdic: %{
            infCpl: additional_info
          },
          infRespTec: %{
            CNPJ: "46991783000113",
            xContato: "Iglu Tecnologia",
            email: "<EMAIL>",
            fone: "16991080570"
          }
        }
        |> add_aut_xml(issuer)

      {:ok, transferred_fiscal_invoice}
    else
      error -> {:error, error}
    end
  end

  defp prepare_receiver(receiver, env) do
    receiver
    |> Map.put(:indIEDest, "1")
    |> Map.put(:enderDest, receiver.enderEmit)
    |> Map.delete(:enderEmit)
    |> Map.merge(%{
      xNome:
        if(env == "dev",
          do: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",
          else: receiver.xNome
        )
    })
  end

  defp add_aut_xml(obj, issuer) do
    if issuer.enderEmit |> Map.get(:UF) == "BA" do
      Map.put(obj, :autXML, [%{CNPJ: "13937073000156"}])
    else
      obj
    end
  end

  defp date_time() do
    date = DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase)

    month = String.pad_leading("#{date.month}", 2, "0")
    day = String.pad_leading("#{date.day}", 2, "0")
    hour = String.pad_leading("#{date.hour}", 2, "0")
    minute = String.pad_leading("#{date.minute}", 2, "0")
    second = String.pad_leading("#{date.second}", 2, "0")

    "#{date.year}-#{month}-#{day}T#{hour}:#{minute}:#{second}-03:00"
  end

  defp get_nat_operation(issuer, receiver) do
    is_interstate = interstate?(issuer, receiver)

    case is_interstate do
      true -> "Transferencia interestadual"
      false -> "Transferencia interna"
    end
  end

  defp interstate?(issuer, receiver) do
    issuer.enderEmit[:UF] != receiver.enderDest[:UF]
  end

  defp invoice_env(env) do
    if env == "prod" do
      "1"
    else
      "2"
    end
  end

  defp create_fiscal_invoice(
         organization_id,
         invoice_serie,
         reverse_fulfillment_id
       ) do
    with [] <-
           Rms.Fiscal.list_fiscal_invoice(
             organization_id,
             query_params: [
               {:reverse_fulfillment_id, reverse_fulfillment_id},
               {:serie_id, invoice_serie.id},
               {:operation_type, "transfer"}
             ]
           ),
         {:ok, fiscal_invoice} <-
           Rms.Fiscal.create_fiscal_invoice(
             organization_id,
             %{
               status: "pending",
               operation_type: "transfer",
               service: "vinco",
               reverse_fulfillment_id: reverse_fulfillment_id,
               serie_id: invoice_serie.id
             }
           ),
         %Rms.Fiscal.FiscalInvoice{} = invoice <-
           Rms.Fiscal.get_fiscal_invoice!(organization_id, fiscal_invoice.id) do
      {:ok, invoice}
    else
      # Existing invoice found
      [invoice | _] ->
        {:ok, invoice}

      error ->
        {:error, error}
    end
  end

  defp get_returned_items(reverse_fulfillment_id) do
    returned_items =
      Rms.Commerce.Orders.OrderData.get_items_from_reverse_fulfillment(reverse_fulfillment_id)

    {:ok,
     Enum.map(returned_items, fn item ->
       %{line_item: item |> Rms.Repo.preload(product_variant: [:product])}
     end)}
  end

  defp get_original_order(reverse_fulfillment_id) do
    with [original_order | _] <-
           Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
             reverse_fulfillment_id
           ) do
      {:ok, original_order}
    end
  end

  defp get_location_from_order(order) do
    order |> Rms.Repo.preload(location: [:address]) |> Map.get(:location)
  end

  defp invoice_serie(organization_id, location_id, env) do
    case Rms.Fiscal.list_invoice_serie(organization_id,
           query_params: [
             {:status, "active"},
             {:invoice_type, "nf"},
             {:location_id, location_id},
             {:invoice_env, env}
           ]
         ) do
      [serie] ->
        {:ok, Rms.Repo.preload(serie, [:organization, location: [:address]])}

      _ ->
        {:error, "can not find a avaliable serie"}
    end
  end
end
