defmodule Rms.Fiscal.Workers.IssueTransferFiscalInvoice do
  use Oban.Pro.Workers.Workflow,
    recorded: true,
    unique: [
      states: [:available, :scheduled, :executing, :retryable, :completed],
      fields: [:args, :worker]
    ],
    queue: :fiscal_invoices

  require OpenTelemetry.Tracer
  require Logger

  alias Rms.Integrations.Vinco

  @impl Oban.Pro.Worker

  def process(%Job{args: args} = job) do
    with {:ok, transfer_vinco_object} <-
           fetch_vinco_transfer_object(job) do
      args
      |> Map.merge(%{
        "transfer_vinco_object" => transfer_vinco_object
      })
      |> execute()
    end
  end

  def execute(
        %{
          "reverse_fulfillment_id" => reverse_fulfillment_id,
          "organization_id" => _organization_id,
          "transfer_vinco_object" => transfer_vinco_object
        } = _args
      ) do
    fiscal_invoice =
      Rms.Fiscal.get_fiscal_invoice_by_reverse_fulfillment_id(reverse_fulfillment_id, "transfer")
      |> Rms.Repo.preload(:serie)

    with {:ok, response} <-
           create_vinco_fiscal_invoice(fiscal_invoice, transfer_vinco_object),
         {:ok, update_params} <- create_update_params(response),
         {:ok, updated_fiscal_invoice} <- update_fiscal_invoice(fiscal_invoice, update_params) do
      Rms.Events.emit("transfer_invoice.issued", %{
        id: updated_fiscal_invoice.id,
        organization_id: updated_fiscal_invoice.organization_id
      })

      updated_fiscal_invoice
    end
  end

  defp update_fiscal_invoice(fiscal_invoice, update_params) do
    case Rms.Fiscal.update_fiscal_invoice(fiscal_invoice, update_params) do
      {:ok, invoice} ->
        {:ok, invoice}

      error ->
        Logger.error("Failed to update fiscal invoice: #{inspect(error)}")
        error
    end
  end

  defp create_update_params(
         %{
           "ChaveDFe" => df_key,
           "CodStatus" => 100,
           "CodStatusLote" => _,
           "DFeProtocolo" => protocol,
           "IdAssincrono" => id,
           "Motivo" => _,
           "MotivoLote" => _,
           "ReportZip" => _,
           "TimeOutAssincrono" => _,
           "Token" => _,
           "XmlDFe" => xml_dfe,
           "XmlRecebimento" => xml_receiption,
           "XmlRecibo" => xml_receipt
         } = _fiscal_invoice
       ) do
    {:ok,
     %{
       df_key: df_key,
       status: "authorized",
       external_id: "#{id}",
       authorized_at: DateTime.utc_now(),
       xml: xml_dfe,
       metadata: %{
         "XmlRecebimento" => xml_receiption,
         "XmlRecibo" => xml_receipt,
         "DFeProtocolo" => protocol
       }
     }}
  end

  defp create_update_params(%{"Motivo" => reason, "CodStatus" => status}) do
    {:error, %{reason: reason, status: status}}
  end

  defp create_update_params(response) do
    {:error, response}
  end

  defp create_vinco_fiscal_invoice(fiscal_invoice, invoice) do
    case Vinco.create_fiscal_invoice(
           fiscal_invoice.serie.invoice_type,
           invoice.ide.cUF,
           invoice,
           get_vinco_config(fiscal_invoice.serie)
         ) do
      {:ok, response} ->
        {:ok, response}

      error ->
        Logger.error("Failed to create Vinco fiscal invoice: #{inspect(error)}")
        error
    end
  end

  defp get_vinco_config(serie) do
    case Vinco.get_key!(serie.organization_id, serie.location_id) do
      nil ->
        raise "no valid vinco config found for organization_id #{serie.organization_id} and location_id #{serie.location_id}"

      value ->
        value
    end
  end

  defp fetch_vinco_transfer_object(job) do
    job
    |> Oban.Pro.Workers.Workflow.all_jobs(names: ["build_transfer_fiscal_invoice"])
    |> List.first()
    |> fetch_recorded()
  end
end
