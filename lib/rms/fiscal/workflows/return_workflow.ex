defmodule Rms.Fiscal.Workflows.ReturnWorkflow do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :fiscal_invoices

  alias Oban.Pro.Workers.Workflow
  alias Rms.Fiscal.FiscalSettings

  import Ecto.Query

  @impl true
  def process(%{
        args:
          %{
            "reverse_fulfillment_id" => reverse_fulfillment_id,
            "organization_id" => organization_id,
            "env" => _env
          } = args
      }) do
    with {:ok, original_order} <- get_original_order(reverse_fulfillment_id),
         reverse_fulfillment <-
           Rms.Commerce.Fulfillments.get_reverse_fulfillment_by_id!(
             organization_id,
             reverse_fulfillment_id
           ) do
      _return_workflow =
        Workflow.new(workflow_name: "return_workflow")
        |> create_return_fiscal_invoice_steps(args)
        |> maybe_create_transfer_steps(original_order, reverse_fulfillment.location_id, args)
        |> Oban.insert_all()

      :ok
    else
      :error ->
        :error
    end
  end

  defp maybe_create_transfer_steps(
         workflow,
         %{location_id: original_order_location_id} = _original_order,
         reverse_fulfillment_location_id,
         args
       )
       when original_order_location_id != reverse_fulfillment_location_id do
    case get_fiscal_settings(args["organization_id"], original_order_location_id) do
      %FiscalSettings{handle_transfer: true} ->
        workflow
        |> Workflow.add(
          :build_transfer_fiscal_invoice,
          Rms.Fiscal.Workers.BuildTransferFiscalInvoice.new(%{
            reverse_fulfillment_id: args["reverse_fulfillment_id"],
            organization_id: args["organization_id"],
            env: args["env"]
          })
        )
        |> Workflow.add(
          :issue_transfer_fiscal_invoice,
          Rms.Fiscal.Workers.IssueTransferFiscalInvoice.new(%{
            reverse_fulfillment_id: args["reverse_fulfillment_id"],
            organization_id: args["organization_id"],
            env: args["env"]
          }),
          deps: [:build_transfer_fiscal_invoice]
        )

      %FiscalSettings{handle_transfer: false} ->
        workflow
    end
  end

  defp maybe_create_transfer_steps(workflow, _original_order, _location_id, _args) do
    workflow
  end

  defp get_fiscal_settings(organization_id, location_id) do
    FiscalSettings
    |> where([fs], fs.organization_id == ^organization_id and fs.location_id == ^location_id)
    |> Rms.Repo.one()
  end

  defp create_return_fiscal_invoice_steps(workflow, args) do
    workflow
    |> Workflow.add(
      :build_return_fiscal_invoice,
      Rms.Fiscal.Workers.BuildReturnFiscalInvoice.new(%{
        reverse_fulfillment_id: args["reverse_fulfillment_id"],
        organization_id: args["organization_id"],
        env: args["env"]
      })
    )
    |> Workflow.add(
      :issue_return_fiscal_invoice,
      Rms.Fiscal.Workers.IssueReturnFiscalInvoice.new(%{
        reverse_fulfillment_id: args["reverse_fulfillment_id"],
        organization_id: args["organization_id"],
        env: args["env"]
      }),
      deps: [:build_return_fiscal_invoice]
    )
  end

  defp get_original_order(reverse_fulfillment_id) do
    with [original_order | _] <-
           Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
             reverse_fulfillment_id
           ) do
      {:ok, original_order}
    end
  end
end
