defmodule Rms.Fiscal.XmlParser do
  def parse(xml) do
    parsed_xml = Floki.parse_document!(xml)

    {:ok,
     %{
       ide: invoice_info(Floki.find(parsed_xml, "ide")),
       det: invoice_itens(parsed_xml),
       emit: ivoice_issuer(Floki.find(parsed_xml, "emit")),
       dest: invoice_customer(Floki.find(parsed_xml, "dest")),
       total: invoice_total(Floki.find(parsed_xml, "total")),
       transp: invoice_transp(Floki.find(parsed_xml, "transp")),
       pag: invoice_payments(Floki.find(parsed_xml, "pag")),
       infProt: invoice_protocol(Floki.find(parsed_xml, "infprot")),
       infNFeSupl: invoice_supplementary(Floki.find(parsed_xml, "infnfesupl"))
     }}
  end

  defp convert_text(element, selector) do
    text =
      element
      |> Floki.find(selector)
      |> Floki.text()

    encoding_change =
      :unicode.characters_to_binary(text, :utf8, :latin1)

    if String.valid?(encoding_change) do
      encoding_change
    else
      text
    end
  end

  defp invoice_info(ide_xml) do
    %{
      cUF: convert_text(ide_xml, "cuf") |> String.slice(0..1),
      cNf: convert_text(ide_xml, "cnf"),
      cMunFG: convert_text(ide_xml, "cmunfg"),
      natOp: convert_text(ide_xml, "natop"),
      mod: convert_text(ide_xml, "mod"),
      serie: convert_text(ide_xml, "serie"),
      nNF: convert_text(ide_xml, "nnf"),
      dhEmi: convert_text(ide_xml, "dhemi"),
      tpAmb: convert_text(ide_xml, "tpamb"),
      indFinal: convert_text(ide_xml, "indfinal"),
      tpNF: convert_text(ide_xml, "tpnf"),
      tpImp: convert_text(ide_xml, "tpimp"),
      idDest: convert_text(ide_xml, "iddest"),
      tpEmis: convert_text(ide_xml, "tpemis"),
      finNFe: convert_text(ide_xml, "finnfe"),
      procEmi: convert_text(ide_xml, "procemi"),
      indPres: convert_text(ide_xml, "indpres")
    }
  end

  defp ivoice_issuer(emit_xml) do
    %{
      CNPJ: convert_text(emit_xml, "cnpj"),
      xNome: convert_text(emit_xml, "xnome"),
      IE: convert_text(emit_xml, "ie"),
      CRT: convert_text(emit_xml, "crt"),
      enderEmit: invoice_issuer_address(Floki.find(emit_xml, "enderemit"))
    }
  end

  defp invoice_issuer_address(issue_address_xml) do
    %{
      UF: convert_text(issue_address_xml, "uf"),
      cMun: convert_text(issue_address_xml, "cmun"),
      xMun: convert_text(issue_address_xml, "xmun"),
      CEP: convert_text(issue_address_xml, "cep"),
      xBairro: convert_text(issue_address_xml, "xbairro"),
      xLgr: convert_text(issue_address_xml, "xlgr"),
      nro: convert_text(issue_address_xml, "nro")
    }
  end

  defp invoice_customer(customer_xml) do
    %{
      xNome: convert_text(customer_xml, "xnome"),
      CPF: convert_text(customer_xml, "cpf"),
      CNPJ: convert_text(customer_xml, "cnpj"),
      email: convert_text(customer_xml, "email"),
      enderDest: invoice_customer_address(Floki.find(customer_xml, "enderdest"))
    }
  end

  defp invoice_customer_address(customer_address_xml) do
    %{
      xLgr: convert_text(customer_address_xml, "xlgr"),
      nro: convert_text(customer_address_xml, "nro"),
      xBairro: convert_text(customer_address_xml, "xbairro"),
      xMun: convert_text(customer_address_xml, "xmun"),
      cMun: convert_text(customer_address_xml, "cmun"),
      xCpl: convert_text(customer_address_xml, "xcpl"),
      xPais: convert_text(customer_address_xml, "xpais"),
      cPais: convert_text(customer_address_xml, "cpais"),
      UF: convert_text(customer_address_xml, "uf"),
      CEP: convert_text(customer_address_xml, "cep")
    }
  end

  defp invoice_total(total_xml) do
    icms_xml = Floki.find(total_xml, "icmstot")

    %{
      ICMStot: %{
        vPIS: convert_text(icms_xml, "vpis"),
        vCOFINS: convert_text(icms_xml, "vcofins"),
        vBC: convert_text(icms_xml, "vbc"),
        vProd: convert_text(icms_xml, "vprod"),
        vNF: convert_text(icms_xml, "vnf"),
        vICMS: convert_text(icms_xml, "vicms"),
        vDesc: convert_text(icms_xml, "vdesc"),
        vFCP: convert_text(icms_xml, "vfcp"),
        vICMSDeson: convert_text(icms_xml, "vicmsdeson"),
        vBCST: convert_text(icms_xml, "vbcst"),
        vST: convert_text(icms_xml, "vst"),
        vFCPST: convert_text(icms_xml, "vfcpst"),
        vFCPSTRet: convert_text(icms_xml, "vfcpstret"),
        vFrete: convert_text(icms_xml, "vfrete"),
        vSeg: convert_text(icms_xml, "vseg"),
        vII: convert_text(icms_xml, "vii"),
        vIPI: convert_text(icms_xml, "vipi"),
        vIPIDevol: convert_text(icms_xml, "vipidevol"),
        vOutro: convert_text(icms_xml, "voutro")
      }
    }
  end

  defp invoice_transp(transp_xml) do
    %{
      modFrete: convert_text(transp_xml, "modfrete"),
      vol: unwrap(Floki.find(transp_xml, "vol"))
    }
  end

  defp invoice_payments(pag_xml) do
    %{
      detPag: unwrap(Floki.find(pag_xml, "pag") |> Floki.find("detpag"))
    }
  end

  defp invoice_itens(parsed_xml) do
    Enum.map(Floki.find(parsed_xml, "det"), fn {"det", [{"nitem", index}], _} = det ->
      %{
        nItem: index,
        prod: %{
          cProd: convert_text(det, "cprod"),
          CEST: convert_text(det, "cest"),
          cEAN: convert_text(det, "cean"),
          cEANTrib: convert_text(det, "ceantrib"),
          indTot: convert_text(det, "indtot"),
          xProd: convert_text(det, "xprod"),
          NCM: convert_text(det, "ncm"),
          CFOP: convert_text(det, "cfop"),
          uCom: convert_text(det, "ucom"),
          uTrib: convert_text(det, "utrib"),
          qCom: convert_text(det, "qcom"),
          qTrib: convert_text(det, "qtrib"),
          vProd: convert_text(det, "vprod"),
          vDesc: convert_text(det, "vdesc"),
          vUnCom: convert_text(det, "vuncom"),
          vUnTrib: convert_text(det, "vuntrib")
        },
        imposto: %{
          ICMS:
            invoice_icms(
              Floki.find(det, "imposto")
              |> Floki.find("icms")
              |> hd()
              |> Floki.children()
            ),
          PIS:
            invoice_pis(
              Floki.find(det, "imposto")
              |> Floki.find("pis")
              |> hd()
              |> Floki.children()
              |> hd
            ),
          COFINS:
            invoice_cofins(
              Floki.find(det, "imposto")
              |> Floki.find("cofins")
              |> hd()
              |> Floki.children()
              |> hd
            ),
          ICMSUFDest: invoice_icms_uf_dest(Floki.find(det, "imposto") |> Floki.find("icmsufdest"))
        }
      }
    end)
  end

  defp invoice_icms_uf_dest(icms_uf_dest_xml) do
    %{
      pFCPUFDest: convert_text(icms_uf_dest_xml, "pfcpufdest"),
      pICMSInter: convert_text(icms_uf_dest_xml, "picmsinter"),
      pICMSInterPart: convert_text(icms_uf_dest_xml, "picmsinterpart"),
      pICMSUFDest: convert_text(icms_uf_dest_xml, "picmsufdest"),
      vBCFCPUFDest: convert_text(icms_uf_dest_xml, "vbcfcpufdest"),
      vBCUFDest: convert_text(icms_uf_dest_xml, "vbcufdest"),
      vFCPUFDest: convert_text(icms_uf_dest_xml, "vfcpufdest"),
      vICMSUFDest: convert_text(icms_uf_dest_xml, "vicmsufdest"),
      vICMSUFRemet: convert_text(icms_uf_dest_xml, "vicmsufremet")
    }
  end

  defp invoice_icms(icms_xml) do
    %{
      orig: convert_text(icms_xml, "orig"),
      CSTCSOSN: get_icms_cst(icms_xml),
      modBC: convert_text(icms_xml, "modbc"),
      vBC: convert_text(icms_xml, "vbc"),
      pICMS: convert_text(icms_xml, "picms"),
      vICMS: convert_text(icms_xml, "vicms"),
      pFCP: convert_text(icms_xml, "vuntrib"),
      vFCP: convert_text(icms_xml, "vuntrib"),
      vBCFCP: convert_text(icms_xml, "vbcfcp"),
      pRedBC: convert_text(icms_xml, "predbc")
    }
  end

  defp get_icms_cst(icms_xml) do
    case Floki.find(icms_xml, "cst") do
      [_] ->
        convert_text(icms_xml, "cst")

      [] ->
        case Floki.find(icms_xml, "csosn") do
          [_] -> convert_text(icms_xml, "csosn")
          [] -> []
        end
    end
  end

  defp invoice_pis(pis_xml) do
    %{
      CST: convert_text(pis_xml, "cst"),
      vBC: convert_text(pis_xml, "vbc"),
      pPIS: convert_text(pis_xml, "ppis"),
      vPIS: convert_text(pis_xml, "vpis")
    }
  end

  defp invoice_cofins(cofins_xml) do
    %{
      CST: convert_text(cofins_xml, "cst"),
      vBC: convert_text(cofins_xml, "vbc"),
      pCOFINS: convert_text(cofins_xml, "pcofins"),
      vCOFINS: convert_text(cofins_xml, "vcofins")
    }
  end

  defp unwrap(elements) do
    Enum.reduce(elements, [], fn {_tag, _, inner_elements}, acc ->
      result =
        Enum.reduce(inner_elements, %{}, fn {tag, _, [value]}, inner_acc ->
          inner_acc
          |> Map.put(formated_tag(tag), value)
        end)

      [result | acc]
    end)
  end

  defp formated_tag(tag) do
    case tag do
      "vpag" ->
        "vPag"

      "tpag" ->
        "tPag"

      "pesob" ->
        "pesoB"

      "pesol" ->
        "pesoL"

      "qvol" ->
        "qVol"

      "nvol" ->
        "nVol"

      tag ->
        tag
    end
  end

  defp invoice_protocol(inf_prot_xml) do
    # Ensure inf_prot_xml is a single element or handle list if necessary
    inf_prot_node = List.first(inf_prot_xml) || []

    %{
      tpAmb: convert_text(inf_prot_node, "tpamb"),
      verAplic: convert_text(inf_prot_node, "veraplic"),
      chNFe: convert_text(inf_prot_node, "chnfe"),
      dhRecbto: convert_text(inf_prot_node, "dhrecbto"),
      nProt: convert_text(inf_prot_node, "nprot"),
      digVal: convert_text(inf_prot_node, "digval"),
      cStat: convert_text(inf_prot_node, "cstat"),
      xMotivo: convert_text(inf_prot_node, "xmotivo")
    }
  end

  defp invoice_supplementary(inf_nfe_supl_xml) do
    # Ensure inf_nfe_supl_xml is a single element or handle list if necessary
    inf_nfe_supl_node = List.first(inf_nfe_supl_xml) || []

    %{
      qrCode: convert_text(inf_nfe_supl_node, "qrcode"),
      urlChave: convert_text(inf_nfe_supl_node, "urlchave")
    }
  end
end
