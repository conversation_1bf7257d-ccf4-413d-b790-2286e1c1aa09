defmodule Rms.Guardian do
  use <PERSON>, otp_app: :rms

  def subject_for_token(%{id: id}, _claims) do
    sub = to_string(id)
    {:ok, sub}
  end

  def subject_for_token(_, _) do
    {:error, :reason_for_error}
  end

  def resource_from_claims(%{"sub" => id} = claims) do
    case String.split(id, "|") do
      [provider, external_id] ->
        get_or_create_user(provider, external_id, claims)

      [id] ->
        {:ok, Rms.Accounts.unsafe_get_user!(id)}
    end
  end

  def resource_from_claims(_claims) do
    {:error, :reason_for_error}
  end

  defp get_or_create_user(provider, external_id, claims) do
    # First try to find the user
    case Rms.Accounts.get_user_by_provider_and_external_id(provider, external_id) do
      {:ok, user} ->
        # User found, return it
        {:ok, user}

      {:error, :not_found} ->
        # User not found, create it
        create_user(provider, external_id, claims)
    end
  end

  defp create_user(provider, external_id, claims) do
    Rms.Accounts.create_user(%{
      name: Map.get(claims, "name"),
      email: Map.get(claims, "email"),
      org_id: Map.get(claims, "org_id"),
      external_id: external_id,
      provider: provider
    })
  end
end
