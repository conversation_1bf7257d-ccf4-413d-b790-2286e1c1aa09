defmodule Rms.Integrations.AddonMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "addon_mappings" do
    field :external_id, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :addon, Rms.Commerce.Products.Addon

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(addon_mapping, attrs) do
    addon_mapping
    |> cast(attrs, [:organization_id, :addon_id, :external_id, :source])
    |> validate_required([:organization_id, :addon_id, :external_id, :source])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:addon)
  end
end
