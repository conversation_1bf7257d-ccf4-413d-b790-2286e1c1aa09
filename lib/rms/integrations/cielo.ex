defmodule Rms.Integrations.Cielo do
  alias Rms.Integrations.Cielo.Client, as: CieloClient

  @cielo_client Application.compile_env(:rms, :cielo_client, CieloClient)

  def get_payment_details(token, checkout_cielo_order_number) do
    @cielo_client.get_payment_details(token, checkout_cielo_order_number)
  end

  def create_payment_link(
        token,
        params
      ) do
    @cielo_client.create_payment_link(
      token,
      params
    )
  end

  def get_payment_link(
        token,
        link_id
      ) do
    @cielo_client.get_payment_link(
      token,
      link_id
    )
  end

  def create_credential(
        client_id,
        client_secret
      ) do
    @cielo_client.create_credential(
      client_id,
      client_secret
    )
  end
end
