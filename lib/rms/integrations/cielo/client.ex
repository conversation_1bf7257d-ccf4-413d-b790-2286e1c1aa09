defmodule Rms.Integrations.Cielo.Client do
  alias Rms.Integrations.Cielo.ClientBehaviour
  @behaviour ClientBehaviour

  @impl ClientBehaviour
  def create_payment_link(token, params) do
    Tesla.post!(
      client(token),
      "/api/public/v1/products/",
      params
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def create_credential(client_id, client_secret) do
    Tesla.post!(
      client(client_id, client_secret),
      "/oauth2/token",
      %{
        grant_type: "client_credentials",
        client_id: client_id,
        client_secret: client_secret
      }
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def get_payment_link(token, link_id) do
    Tesla.get!(
      client(token),
      "/api/public/v1/products/:link_id/payments",
      opts: [path_params: [link_id: link_id]]
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def get_payment_details(token, checkout_cielo_order_number) do
    Tesla.get!(
      client(token),
      "/api/public/v2/orders/:order_number",
      opts: [path_params: [order_number: checkout_cielo_order_number]]
    )
    |> normalize_response()
  end

  defp client(token) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://cieloecommerce.cielo.com.br"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Timeout, timeout: 30_000},
      {Tesla.Middleware.BearerAuth, token: token},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.LogError,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  defp client(client_id, client_secret) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://auth.braspag.com.br"},
      {Tesla.Middleware.BasicAuth, %{username: client_id, password: client_secret}},
      {Tesla.Middleware.Timeout, timeout: 30_000},
      Tesla.Middleware.EncodeFormUrlencoded,
      Tesla.Middleware.DecodeJson
    ]

    Tesla.client(middleware)
  end

  defp normalize_response(%{status: status, body: body}) when status in 200..299 do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
