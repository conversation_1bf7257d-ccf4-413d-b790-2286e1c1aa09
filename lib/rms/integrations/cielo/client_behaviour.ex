defmodule Rms.Integrations.Cielo.ClientBehaviour do
  @type result :: {:ok, map()} | {:error, any()}

  @callback get_payment_details(token :: String.t(), checkout_cielo_order_number :: String.t()) ::
              {:ok, map()} | {:error, atom()} | {:error, {atom(), any()}}
  @callback create_payment_link(
              token :: String.t(),
              params :: map()
            ) :: result()

  @callback get_payment_link(
              token :: String.t(),
              link_id :: String.t()
            ) :: result()

  @callback create_credential(
              client_id :: String.t(),
              client_secret :: String.t()
            ) :: result()
end
