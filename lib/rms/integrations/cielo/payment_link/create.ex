defmodule Rms.Integrations.Cielo.PaymentLink.Create do
  alias Rms.Integrations.Cielo

  def execute(organization_id, order, transaction_customer) do
    with {:ok, credential} <-
           Cielo.Token.Create.execute(organization_id, order.location_id),
         params <- build_params(order, transaction_customer),
         {:ok, payment} <- Cielo.create_payment_link(credential.token, params) do
      {:ok, payment["shortUrl"], payment["id"]}
    end
  end

  defp build_params(order, _transaction_customer) do
    %{
      "shipping" => %{
        "type" => "WithoutShipping",
        "name" => "frete da loja",
        "price" => "0"
      },
      "OrderNumber" => "#{order.id}",
      "type" => "Payment",
      "name" => "pedido - #{order.id}",
      "description" => "Pedido - #{order.id}",
      "showDescription" => false,
      "price" => format_value(order.total_price_with_addons),
      "expirationDate" => expiration_date(),
      "quantity" => 1,
      "softDescriptor" => "#{order.id}"
    }
  end

  defp format_value(value) do
    value
    |> Decimal.mult("100")
    |> Decimal.to_integer()
  end

  defp expiration_date() do
    Date.add(Date.utc_today(), 2)
  end
end
