defmodule Rms.Integrations.Cielo.PaymentLink.Update do
  alias Rms.Finance
  alias Rms.Integrations.Cielo

  def execute(%{"external_id" => external_id}) do
    with {:ok, payment} <- Finance.get_payment_by_external_reference("cielo-link", external_id),
         {:ok, credential} <- Cielo.Token.Create.execute(payment.organization_id, nil),
         {:ok, payload} <- Cielo.get_payment_link(credential.token, external_id),
         {:ok, first_order_id} <- get_first_paid_order_id(payload),
         {:ok, order_details} <- Cielo.get_payment_details(credential.token, first_order_id) do
      if check_payment(payment.amount, payload) do
        metadata = build_payment_metadata(payment, order_details)

        Finance.sync_payment(payment, %{
          status: "settled",
          metadata: metadata
        })
      else
        {:discard, "status"}
      end
    end
  end

  defp get_first_paid_order_id(%{"orders" => orders}) do
    case Enum.find(orders, &(&1["payment"]["status"] == "Paid")) do
      %{"orderNumber" => order_id} -> {:ok, order_id}
      nil -> {:error, :no_paid_orders}
    end
  end

  defp build_payment_metadata(payment, %{"payment" => payment_data}) do
    metadata = %{
      "payment_details" => %{
        "nsu" => payment_data["nsu"],
        "aut" => payment_data["authorizationCode"],
        "card_brand" => payment_data["brand"],
        "card_number" => payment_data["cardMaskedNumber"],
        "installments" => payment_data["numberOfPayments"],
        "method" => get_payment_method(payment_data["type"])
      }
    }

    Map.merge(payment.metadata || %{}, metadata)
  end

  defp get_payment_method("CreditCard"), do: "credit_card"
  defp get_payment_method("DebitCard"), do: "debit_card"
  defp get_payment_method("Pix"), do: "pix"
  defp get_payment_method(_), do: "credit_card"

  defp check_payment(total_value, data) do
    total_value = Decimal.mult(total_value, "100")

    total_orders_value =
      data["orders"]
      |> Enum.filter(fn order -> order["payment"]["status"] == "Paid" end)
      |> Enum.map(fn order -> Decimal.new(order["payment"]["price"]) end)
      |> Enum.reduce(Decimal.new(0), &Decimal.add/2)

    if Decimal.compare(total_orders_value, total_value) in [:eq, :gt] do
      true
    else
      false
    end
  end
end
