defmodule Rms.Integrations.Cielo.Token.Create do
  alias Rms.Integrations
  alias Rms.Integrations.Cielo

  def execute(organization_id, _location_id) do
    credential = Integrations.get_cielo_credential!(organization_id)
    create_credential(credential)
  end

  defp create_credential(%{expires_in: expires_in, updated_at: updated_at} = credential) do
    now = NaiveDateTime.utc_now()
    passed_time = NaiveDateTime.diff(now, updated_at, :second)

    if passed_time > expires_in do
      update_credential(credential)
    else
      {:ok, credential}
    end
  end

  defp update_credential(credential) do
    case Cielo.create_credential(credential.client_id, credential.client_secret) do
      {:ok, credential_payload} ->
        Integrations.create_cielo_credential(credential.organization_id, %{
          client_id: credential.client_id,
          client_secret: credential.client_secret,
          token: credential_payload["access_token"],
          expires_in: credential_payload["expires_in"]
        })

      error ->
        error
    end
  end
end
