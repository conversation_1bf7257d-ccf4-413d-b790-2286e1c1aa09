defmodule Rms.Integrations.CieloCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "cielo_credentials" do
    field :client_id, Rms.Vault.EncryptedBinary
    field :client_secret, Rms.Vault.EncryptedBinary

    field :token, Rms.Vault.EncryptedBinary

    field :expires_in, :integer

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(cielo_credential, attrs) do
    cielo_credential
    |> cast(attrs, [:client_id, :client_secret, :token, :expires_in])
    |> validate_required([:client_id, :client_secret, :token, :expires_in])
  end
end
