defmodule Rms.Integrations.CityCode do
  alias Rms.Integrations.Ibge.Client, as: IbgeClient
  alias Rms.Integrations.Viacep.Client, as: ViacepClient
  alias Rms.CitiesDatas

  @ibge_client Application.compile_env(:rms, :ibge_client, IbgeClient)
  @viacep_client Application.compile_env(:rms, :viacep_client, ViacepClient)

  def get_address_city_code(city_name, uf_initials, zip) do
    with :ok <- ensure_cities_data_updated(),
         {:ok, city_data} <- find_by_city_and_uf(city_name, uf_initials) do
      {:ok, "#{city_data.ibge_id}"}
    else
      {:error, _} when is_binary(zip) ->
        find_by_postal_code(zip)

      error ->
        error
    end
  end

  defp ensure_cities_data_updated do
    cities = CitiesDatas.get_all()

    cond do
      Enum.empty?(cities) ->
        update_cities_data()

      outdated?(List.first(cities)) ->
        update_cities_data()

      true ->
        :ok
    end
  end

  defp outdated?(city_data) do
    one_day_ago = DateTime.utc_now() |> DateTime.add(-1, :day)
    DateTime.compare(city_data.updated_at, one_day_ago) == :lt
  end

  defp update_cities_data do
    case @ibge_client.get_cities_data() do
      {:ok, cities_data} when is_list(cities_data) ->
        case CitiesDatas.upsert_cities_data(cities_data) do
          {:error, _} -> {:error, "Failed to update cities data"}
          _ -> :ok
        end

      error ->
        error
    end
  end

  defp find_by_city_and_uf(city_name, uf_initials) do
    try do
      {:ok, CitiesDatas.get_city_data!(city_name, uf_initials)}
    rescue
      error ->
        {:error, "error trying to find city data by city name and uf initials: #{inspect(error)}"}
    end
  end

  defp find_by_postal_code(zip) do
    case @viacep_client.get_info_by_postal_code(zip) do
      {:ok, %{"ibge" => ibge_id}} when not is_nil(ibge_id) ->
        {:ok, ibge_id}

      _error ->
        {:error, "error trying to find city data by postal code: #{zip}"}
    end
  end
end
