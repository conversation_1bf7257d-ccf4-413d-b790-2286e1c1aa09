defmodule Rms.Integrations.CustomerSync do
  use Ecto.Schema
  import Ecto.Changeset

  schema "customer_sync" do
    field :external_id, :string
    field :expected_count, :integer
    field :status, :string, default: "pending"
    field :message, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer_sync, attrs) do
    customer_sync
    |> cast(attrs, [:external_id, :expected_count, :status, :message, :source])
  end
end
