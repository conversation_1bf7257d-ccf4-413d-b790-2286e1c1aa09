defmodule Rms.Integrations.CustomerSyncEntry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "customer_sync_entries" do
    field :external_id, :string
    field :status, :string

    embeds_many :errors, Rms.Integrations.ErrorEntry

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :customer_sync, Rms.Integrations.CustomerSync

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer_sync_entry, attrs) do
    customer_sync_entry
    |> cast(attrs, [:external_id, :status])
    |> cast_embed(:errors)
    |> validate_required([:external_id, :status])
  end
end
