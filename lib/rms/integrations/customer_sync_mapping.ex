defmodule Rms.Integrations.CustomerSyncMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "customer_sync_mappings" do
    field :external_id, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :customer, Rms.Customers.Customer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(customer_sync_mapping, attrs) do
    customer_sync_mapping
    |> cast(attrs, [:customer_id, :organization_id, :source, :external_id])
    |> validate_required([:source, :external_id])
    |> unique_constraint([:organization_id, :source, :external_id], error_key: :external_id)
    |> assoc_constraint(:organization)
    |> assoc_constraint(:customer)
  end
end
