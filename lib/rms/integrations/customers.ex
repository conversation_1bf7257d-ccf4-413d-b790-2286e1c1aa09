defmodule Rms.Integrations.Customers do
  import Ecto.Query

  require Logger
  alias Rms.Customers.Customer
  alias Rms.Integrations.Customers.CustomerEndpoint
  alias Rms.Repo

  @doc """
  Creates a new customer endpoint for a given organization.

  ## Parameters

    - organization_id: The ID of the organization.
    - attrs: A map of attributes for the customer endpoint.

  ## Examples

      iex> create_customer_endpoint(1, %{endpoint: "https://example.com", unique_on: ["email"], headers: [["Authorization", "Bearer token"]], active: true})
      {:ok, %CustomerEndpoint{}}

      iex> create_customer_endpoint(1, %{endpoint: "invalid_url", unique_on: ["email"], headers: [["Authorization", "Bearer token"]], active: true})
      {:error, %Ecto.Changeset{}}

  """
  def create_customer_endpoint(organization_id, attrs) do
    %CustomerEndpoint{organization_id: organization_id}
    |> CustomerEndpoint.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Fetches a specific customer endpoint for a given organization by ID.

  ## Parameters

    - organization_id: The ID of the organization.
    - id: The ID of the customer endpoint.

  ## Examples

      iex> get_customer_endpoint!(1, 123)
      %CustomerEndpoint{}

      iex> get_customer_endpoint!(1, 999)
      nil

  """
  def get_customer_endpoint!(organization_id, id) do
    Repo.get_by!(CustomerEndpoint, id: id, organization_id: organization_id)
  end

  @doc """
  Lists all customer endpoints for a given organization.

  ## Parameters

    - organization_id: The ID of the organization.

  ## Examples

      iex> list_customer_endpoints(1)
      [%CustomerEndpoint{}, ...]

  """
  def list_customer_endpoints(organization_id) do
    CustomerEndpoint
    |> where(organization_id: ^organization_id)
    |> Repo.all()
  end

  @doc """
  Updates a specific customer endpoint with new attributes.

  ## Parameters

    - customer_endpoint: The customer endpoint to update.
    - attrs: A map of attributes to update the customer endpoint with.

  ## Examples

      iex> update_customer_endpoint(customer_endpoint, %{endpoint: "https://newexample.com"})
      {:ok, %CustomerEndpoint{}}

      iex> update_customer_endpoint(customer_endpoint, %{endpoint: "invalid_url"})
      {:error, %Ecto.Changeset{}}

  """
  def update_customer_endpoint(%CustomerEndpoint{} = customer_endpoint, attrs) do
    customer_endpoint
    |> CustomerEndpoint.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a specific customer endpoint.

  ## Parameters

    - customer_endpoint: The customer endpoint to delete.

  ## Examples

      iex> delete_customer_endpoint(customer_endpoint)
      {:ok, %CustomerEndpoint{}}

      iex> delete_customer_endpoint(customer_endpoint)
      {:error, %Ecto.Changeset{}}

  """
  def delete_customer_endpoint(%CustomerEndpoint{} = customer_endpoint) do
    Repo.delete(customer_endpoint)
  end

  @doc """
  Lists customers for the given organization based on the provided options.

  ## Parameters

    - organization_id: The ID of the organization.
    - opts: A keyword list of options for querying customers.

  ## Examples

      iex> list_customer(1, email: "<EMAIL>")
      [%Customer{}, ...]

  """
  def list_customer(organization_id, opts \\ []) do
    case get_active_customer_endpoint(organization_id) do
      nil -> []
      customer_endpoint -> fetch_customers_from_endpoint(organization_id, customer_endpoint, opts)
    end
  end

  @spec get_active_customer_endpoint(any()) :: any()
  def get_active_customer_endpoint(organization_id) do
    CustomerEndpoint
    |> where([ce], ce.organization_id == ^organization_id and ce.active == true)
    |> Repo.one()
  end

  defp fetch_customers_from_endpoint(
         organization_id,
         %{endpoint: "https://shopify.com"},
         query_params
       ) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, query} <- build_shopify_query(query_params),
         {:ok, %{items: customers}} <-
           Rms.Integrations.Shopify.fetch_customers(client,
             first: 10,
             query: query
           ) do
      customers
      |> Stream.map(fn customer -> transform_customer(customer, "shopify") end)
    else
      _ ->
        []
    end
  end

  defp fetch_customers_from_endpoint(_organization_id, customer_endpoint, query_params) do
    base_url = customer_endpoint.endpoint
    headers = customer_endpoint.headers |> Enum.map(fn [key, value] -> {key, value} end)

    opts = [query: query_params, headers: headers, opts: [recv_timeout: 1000]]

    with {:ok, %Tesla.Env{status: 200, body: body}} <- Tesla.get(base_url, opts),
         {:ok, customers} <- Jason.decode(body) do
      customers
      |> Stream.map(&transform_customer/1)
      |> Enum.take(10)
    else
      _ -> []
    end
  end

  defp transform_customer(%{"customer" => customer_map}) do
    %{
      name: customer_map["name"],
      document_type: customer_map["document_type"],
      document: customer_map["document"],
      email: customer_map["email"],
      primary_phone_number: customer_map["primary_phone_number"],
      addresses: transform_addresses(customer_map["name"], customer_map["addresses"] || [])
    }
  end

  defp transform_customer(%{"defaultAddress" => _default_address} = customer_map, "shopify") do
    document =
      get_in(customer_map, ["defaultAddress", "company"])
      |> validate_cpf()

    %{
      name: "#{customer_map["firstName"]} #{customer_map["lastName"]}",
      email: customer_map["email"],
      primary_phone_number: customer_map["phone"],
      document_type: "CPF",
      document: document,
      addresses: transform_address(customer_map["defaultAddress"], "shopify")
    }
  end

  defp transform_customer(customer_map, "shopify") do
    %{
      name: "#{customer_map["firstName"]} #{customer_map["lastName"]}",
      email: customer_map["email"],
      primary_phone_number: customer_map["phone"]
    }
  end

  defp validate_cpf(nil), do: ""
  defp validate_cpf(""), do: ""

  defp validate_cpf(cpf) do
    cpf = String.replace(cpf, ~r/[^\d]/, "")

    case cpf do
      "" -> ""
      _ -> if Brcpfcnpj.cpf_valid?(cpf), do: cpf, else: ""
    end
  end

  defp transform_addresses(receiver_name, addresses) do
    addresses
    |> Enum.reduce([], fn
      %{"country_name" => "BRASIL"} = address, acc ->
        formated_address = %{
          street: capitalize_name(address["street"]),
          city_name: capitalize_name(address["city"]),
          state: address["state"],
          zip: address["zip"],
          neighborhood: capitalize_name(address["neighborhood"]),
          number: address["number"],
          complement: address["complement"],
          receiver_name: receiver_name,
          country_name: "BRA"
        }

        [formated_address | acc]

      _, acc ->
        acc
    end)
    |> Enum.filter(fn address ->
      Rms.Addresses.Address.changeset(%Rms.Addresses.Address{}, address).valid?
    end)
  end

  defp transform_address(nil, "shopify") do
    []
  end

  defp transform_address(%{"zip" => zip} = default_address, "shopify") when is_binary(zip) do
    zip = Rms.Integrations.Shopify.ImportCustomerWorker.format_zip(zip)

    with {:ok, city_code} <-
           Rms.Integrations.CityCode.get_address_city_code(
             default_address["city"],
             default_address["provinceCode"],
             zip
           ),
         address =
           %{
             receiver_name: default_address["name"],
             city_name: default_address["city"],
             state: default_address["provinceCode"],
             country_name: default_address["country"],
             street: default_address["address1"],
             zip: zip,
             complement: default_address["address2"],
             city_code: city_code
           },
         %{valid?: true} <- Rms.Addresses.Address.changeset(%Rms.Addresses.Address{}, address) do
      [address]
    else
      error ->
        Logger.warning("error trying to transform address", error: inspect(error))

        []
    end
  end

  defp transform_address(_default_address, "shopify") do
    []
  end

  defp capitalize_name(name) when is_binary(name) do
    name
    |> String.downcase()
    |> String.split()
    |> Enum.map_join(" ", &String.capitalize/1)
  end

  defp capitalize_name(name), do: name

  @doc """
  Imports customers for a given organization.

  ## Parameters

    - organization_id: The ID of the organization.
    - customers: A list of customer maps to be imported.

  ## Examples

      iex> import_customers(1, [%{"name" => "John Doe", "email" => "<EMAIL>"}])
      :ok

      iex> import_customers(1, [%{"name" => "Jane Doe", "email" => "<EMAIL>"}])
      :ok

  """
  def import_customers(_organization_id, _customer, []) do
    {:error, "unique_field is required"}
  end

  def import_customers(organization_id, customer, unique_fields) do
    Repo.transaction(fn ->
      base_query = where(Customer, [c], c.organization_id == ^organization_id)

      existing_customer_query =
        for field <- unique_fields,
            {value, db_field} = transform_field(customer, field),
            not is_nil(value),
            reduce: base_query do
          query ->
            where(
              query,
              [c],
              field(c, ^db_field) == ^value
            )
        end

      try do
        existing_customer =
          Repo.one(lock(existing_customer_query, "FOR UPDATE"))
          |> Repo.preload([:addresses])

        result =
          if existing_customer do
            {addresses, customer} = Map.pop(customer, :addresses, [])
            {_document, customer} = Map.pop(customer, :document, nil)
            {_birth_date, customer} = Map.pop(customer, :birth_date, nil)

            create_new_addresses(existing_customer, addresses)
            existing_customer = Repo.preload(existing_customer, [:addresses], force: true)

            Rms.Customers.update_customer(existing_customer, customer)
          else
            Rms.Customers.import_customer(organization_id, customer)
          end

        with {:ok, customer} <- result do
          customer
        end
      rescue
        Ecto.MultipleResultsError -> :multiple_results
      end
    end)
  end

  defp create_new_addresses(customer, addresses) do
    Enum.each(addresses, fn address ->
      changeset =
        Rms.Addresses.Address.changeset(
          %Rms.Addresses.Address{
            organization_id: customer.organization_id,
            customer_id: customer.id
          },
          address
        )

      with {:ok, changed_address} <- Ecto.Changeset.apply_action(changeset, :insert),
           compare_keys = Map.keys(address),
           nil <- find_matching_address(customer.addresses, changed_address, compare_keys) do
        Repo.insert(changeset)
      end
    end)
  end

  def find_matching_address(addresses, map_params, compare_keys) do
    Enum.find(addresses, fn address ->
      db_address =
        Map.take(address, compare_keys)
        |> Enum.reject(fn {_, v} -> is_nil(v) or v == "" end)
        |> Map.new()

      import_address =
        Map.take(map_params, compare_keys)
        |> Enum.reject(fn {_, v} -> is_nil(v) or v == "" end)
        |> Map.new()

      db_address == import_address
    end)
  end

  defp transform_field(customer, field) when field in ~w(document primary_phone_number) do
    field = String.to_existing_atom(field)
    {customer[field], :"#{field}_hash"}
  end

  defp transform_field(customer, "email") do
    email =
      case customer[:email] do
        email when is_binary(email) -> String.downcase(email)
        _ -> nil
      end

    {email, :email_hash}
  end

  defp transform_field(customer, field) do
    field = String.to_existing_atom(field)
    {customer[field], field}
  end

  def build_shopify_query(opts) do
    allowed_opts = Keyword.take(opts, [:primary_phone_number, :email, :name, :document])

    query =
      Enum.map_join(allowed_opts, " AND ", fn
        {:primary_phone_number, value} ->
          "phone:+55#{value}"

        {:document, value} ->
          "#{value}"

        {:name, value} ->
          "#{value}"

        {key, value} ->
          "#{key}:#{value}"
      end)

    if query == "" do
      {:error, ""}
    else
      {:ok, query}
    end
  end
end
