defmodule Rms.Integrations.Erp.Amaro do
  alias Rms.Integrations.Erp.Amaro.Client, as: AmaroClient
  @amaro_client Application.compile_env(:rms, :amaro_client, AmaroClient)

  def insert_new_invoice(client, params) do
    @amaro_client.insert_new_invoice(client, params)
  end

  def insert_cancellation_invoice(client, params) do
    @amaro_client.insert_cancellation_invoice(client, params)
  end

  def client(shop_domain) do
    @amaro_client.client(shop_domain)
  end

  def get_product_taxes(client, sku) do
    @amaro_client.get_product_taxes(client, sku)
  end
end
