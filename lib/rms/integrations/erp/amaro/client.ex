defmodule Rms.Integrations.Erp.Amaro.Client do
  @behaviour Rms.Integrations.Erp.Amaro.ClientBehaviour

  def insert_new_invoice(client, params) do
    client
    |> Tesla.post!("/nf/issued", params)
    |> normalize_response()
  end

  def insert_cancellation_invoice(client, params) do
    client
    |> Tesla.post!("/nf/canceled", params)
    |> normalize_response()
  end

  def client(url) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://#{url}"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers, [{"Content-Type", "application/json"}]},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.LogError
    ]

    Tesla.client(middleware)
  end

  def get_product_taxes(client, sku) do
    client
    |> Tesla.get!("/product/taxes", query: [sku: sku])
    |> normalize_response()
  end

  defp normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
