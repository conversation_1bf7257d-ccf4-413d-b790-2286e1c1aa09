defmodule Rms.Integrations.Erp.Amaro.ClientBehaviour do
  @callback client(organization_id :: String.t()) :: Tesla.Client.t()
  @callback insert_new_invoice(client :: Tesla.Client.t(), params :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback insert_cancellation_invoice(client :: Tesla.Client.t(), params :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback get_product_taxes(client :: Tesla.Client.t(), sku :: String.t()) ::
              {:ok, map()} | {:error, any()}
end
