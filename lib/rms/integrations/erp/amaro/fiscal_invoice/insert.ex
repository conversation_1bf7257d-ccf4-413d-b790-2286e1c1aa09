defmodule Rms.Integrations.Erp.Amaro.FiscalInvoice.Insert do
  def execute(organization_id, invoice_id) do
    invoice = Rms.Fiscal.get_fiscal_invoice!(organization_id, invoice_id)
    insert(invoice, organization_id)
  end

  defp insert(%{id: invoice_id, operation_type: "cancellation"}, organization_id) do
    {fiscal_invoice, _transaction} = get_invoice_transaction(organization_id, invoice_id)

    erp_credential = Rms.Integrations.get_erp_credential!(organization_id)

    with {:ok, external_id} <- get_ecommerce_order_id(fiscal_invoice.fulfillment),
         {:ok, order_name} <- fetch_ecommerce_order_name(organization_id, external_id),
         client <- Rms.Integrations.Erp.Amaro.client(erp_credential.url),
         params <- build_params(fiscal_invoice, order_name),
         {:ok, _response} <-
           Rms.Integrations.Erp.Amaro.insert_cancellation_invoice(client, params) do
      {:ok, "fiscal invoice send successfully"}
    end
  end

  defp insert(%{id: invoice_id, operation_type: "sale"}, organization_id) do
    {fiscal_invoice, _transaction} = get_invoice_transaction(organization_id, invoice_id)

    erp_credential = Rms.Integrations.get_erp_credential!(organization_id)

    with {:ok, external_id} <- get_ecommerce_order_id(fiscal_invoice.fulfillment),
         {:ok, order_name} <- fetch_ecommerce_order_name(organization_id, external_id),
         client <- Rms.Integrations.Erp.Amaro.client(erp_credential.url),
         params <- build_params(fiscal_invoice, order_name),
         {:ok, _response} <- Rms.Integrations.Erp.Amaro.insert_new_invoice(client, params),
         {:ok, _updated_invoice} <-
           Rms.Fiscal.update_fiscal_invoice(fiscal_invoice, %{integrated_erp: true}) do
      {:ok, "fiscal invoice send successfully"}
    end
  end

  defp insert(%{id: invoice_id, operation_type: "return"}, organization_id) do
    {fiscal_invoice, _transaction} = get_invoice_transaction(organization_id, invoice_id)

    erp_credential = Rms.Integrations.get_erp_credential!(organization_id)

    with {:ok, original_order} <- get_original_order(fiscal_invoice.reverse_fulfillment_id),
         {:ok, order_name} <- get_ecommerce_order_name(original_order),
         client <- Rms.Integrations.Erp.Amaro.client(erp_credential.url),
         params <- build_params(fiscal_invoice, order_name),
         {:ok, _response} <- Rms.Integrations.Erp.Amaro.insert_new_invoice(client, params) do
      {:ok, "fiscal invoice send successfully"}
    end
  end

  defp build_params(fiscal_invoice, order_name) do
    cond do
      String.contains?(fiscal_invoice.xml, "Cfe") and fiscal_invoice.operation_type == "sale" ->
        %{
          shopifyOrderName: order_name,
          type: "sale_cfe",
          xml: fiscal_invoice.xml
        }

      fiscal_invoice.operation_type == "return" ->
        %{
          shopifyOrderName: order_name,
          type: "return",
          xml: fiscal_invoice.xml,
          filial_dev: fiscal_invoice.serie.invoices_series.location_id
        }

      fiscal_invoice.operation_type == "cancellation" ->
        %{
          shopifyOrderName: order_name,
          xml: fiscal_invoice.xml
        }

      true ->
        %{
          shopifyOrderName: order_name,
          type: "sale",
          xml: fiscal_invoice.xml
        }
    end
  end

  defp get_ecommerce_order_name(order) do
    if order.name do
      {:ok, order.name}
    else
      {:error, "ecomerce order name not found"}
    end
  end

  defp get_original_order(reverse_fulfillment_id) do
    case Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
           reverse_fulfillment_id
         ) do
      [original_order | _] -> {:ok, original_order}
      [] -> {:error, "Original order not found"}
    end
  end

  defp get_ecommerce_order_id(fulfillment) do
    if String.starts_with?(fulfillment.external_reference, "gid://shopify/Order/") and
         fulfillment.shipping_method == "in-store" do
      {:ok, fulfillment.external_reference}
    else
      {:error, "ecomerce order not found"}
    end
  end

  defp fetch_ecommerce_order_name(organization_id, external_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case Rms.Integrations.Shopify.get_order!(shopify_client, external_id) do
      {:ok, %{"data" => %{"order" => %{"name" => order_name}}}} when not is_nil(order_name) ->
        {:ok, order_name}

      error ->
        {:error, error}
    end
  end

  defp get_invoice_transaction(organization_id, invoice_id) do
    invoice =
      Rms.Fiscal.get_fiscal_invoice!(organization_id, invoice_id)
      |> Rms.Repo.preload([
        :serie,
        :fulfillment,
        invoice_payments: [
          payment: [
            transaction: [
              order: [
                :fulfillments
              ]
            ]
          ]
        ]
      ])

    {invoice, nil}
  end
end
