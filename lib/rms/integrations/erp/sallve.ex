defmodule Rms.Integrations.Erp.Sallve do
  alias Rms.Integrations.Erp.Sallve.Client, as: SallveClient
  @sallve_client Application.compile_env(:rms, :sallve_client, SallveClient)

  def insert_new_invoice(client, params) do
    @sallve_client.insert_new_invoice(client, params)
  end

  @doc """
  Creates a client to query Sallve's

  ## Examples

      iex> client("salva-01", "my token")
      %Tesla.Client{}

  """
  def client(shop_domain, token) do
    @sallve_client.client(shop_domain, token)
  end
end
