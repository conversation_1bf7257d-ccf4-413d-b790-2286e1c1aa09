defmodule Rms.Integrations.Erp.Sallve.Client do
  @behaviour Rms.Integrations.Erp.Sallve.ClientBehaviour

  def insert_new_invoice(client, params) do
    client
    |> Tesla.post!("/new-invoices", params)
    |> normalize_response()
  end

  def client(url, token) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://#{url}"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers, [{"x-api-key", token}]},
      Tesla.Middleware.OpenTelemetry
    ]

    Tesla.client(middleware)
  end

  defp normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
