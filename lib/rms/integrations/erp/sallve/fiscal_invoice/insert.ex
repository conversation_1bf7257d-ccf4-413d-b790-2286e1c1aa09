defmodule Rms.Integrations.Erp.Sallve.FiscalInvoice.Insert do
  def execute(organization_id, invoice_id) do
    fiscal_invoice =
      Rms.Fiscal.get_fiscal_invoice!(organization_id, invoice_id)
      |> Rms.Repo.preload([:serie, fulfillment: [:order]])

    erp_credential = Rms.Integrations.get_erp_credential!(organization_id)

    client = Rms.Integrations.Erp.Sallve.client(erp_credential.url, erp_credential.credential)

    with {:ok, order_name} <- get_ecommerce_order_id(fiscal_invoice.fulfillment.order),
         {:ok, params} <- build_params(fiscal_invoice, order_name),
         {:ok, _response} <- Rms.Integrations.Erp.Sallve.insert_new_invoice(client, params),
         {:ok, _updated_invoice} <-
           Rms.Fiscal.update_fiscal_invoice(fiscal_invoice, %{integrated_erp: true}) do
      {:ok, "fiscal invoice inserted and marked as integrated"}
    end
  end

  def execute_return(organization_id, invoice_id) do
    fiscal_invoice =
      Rms.Fiscal.get_fiscal_invoice!(organization_id, invoice_id)
      |> Rms.Repo.preload([:serie, fulfillment: [:order]])

    erp_credential = Rms.Integrations.get_erp_credential!(organization_id)

    client = Rms.Integrations.Erp.Sallve.client(erp_credential.url, erp_credential.credential)

    with {:ok, original_order} <- get_original_order(fiscal_invoice.reverse_fulfillment_id),
         {:ok, order_name} <- get_ecommerce_order_id(original_order),
         {:ok, params} <- build_params(fiscal_invoice, order_name),
         {:ok, _response} <- Rms.Integrations.Erp.Sallve.insert_new_invoice(client, params),
         {:ok, _updated_invoice} <-
           Rms.Fiscal.update_fiscal_invoice(fiscal_invoice, %{integrated_erp: true}) do
      {:ok, "fiscal invoice inserted and marked as integrated"}
    end
  end

  defp get_ecommerce_order_id(order) do
    if order.name do
      {:ok, order.name}
    else
      {:error, "ecomerce order name not found"}
    end
  end

  defp build_params(fiscal_invoice, order_name) do
    {
      :ok,
      %{
        shopifyOrderName: order_name,
        type: fiscal_invoice.operation_type,
        xml: fiscal_invoice.xml
      }
    }
  end

  defp get_original_order(reverse_fulfillment_id) do
    case Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
           reverse_fulfillment_id
         ) do
      [original_order | _] -> {:ok, original_order}
      [] -> {:error, "Original order not found"}
    end
  end
end
