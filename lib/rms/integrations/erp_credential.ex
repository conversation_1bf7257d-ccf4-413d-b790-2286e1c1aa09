defmodule Rms.Integrations.ErpCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "erp_credentials" do
    field :url, Rms.Vault.EncryptedBinary
    field :credential, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(shopify, attrs) do
    shopify
    |> cast(attrs, [:url, :credential])
    |> validate_required([:url, :credential, :organization_id])
    |> unique_constraint(:organization_id)
  end
end
