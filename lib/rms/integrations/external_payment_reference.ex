defmodule Rms.Integrations.ExternalPaymentReference do
  use Ecto.Schema
  import Ecto.Changeset

  schema "external_payment_references" do
    field :partner, :string
    field :external_id, :string

    belongs_to :payment, Rms.Finance.Payment
    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(epr, attrs) do
    epr
    |> cast(attrs, [:partner, :external_id])
    |> validate_required([:partner, :external_id])
  end
end
