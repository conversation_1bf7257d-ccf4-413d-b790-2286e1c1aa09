defmodule Rms.Integrations.GiftPromo do
  @behaviour Rms.Integrations.GiftPromo.ClientBehaviour

  @gift_promo_client Application.compile_env(
                       :rms,
                       :gift_promo_client,
                       Rms.Integrations.GiftPromo.Client
                     )

  @impl Rms.Integrations.GiftPromo.ClientBehaviour
  def client(params) do
    @gift_promo_client.client(params)
  end

  @impl Rms.Integrations.GiftPromo.ClientBehaviour
  def check_gift_card(client, params) do
    @gift_promo_client.check_gift_card(client, params)
  end

  @impl Rms.Integrations.GiftPromo.ClientBehaviour
  def validate_gift_card_password(client, params) do
    @gift_promo_client.validate_gift_card_password(client, params)
  end

  @impl Rms.Integrations.GiftPromo.ClientBehaviour
  def redeem_gift_card(client, params) do
    @gift_promo_client.redeem_gift_card(client, params)
  end

  @impl Rms.Integrations.GiftPromo.ClientBehaviour
  def refund_transaction(client, params) do
    @gift_promo_client.refund_transaction(client, params)
  end
end
