defmodule Rms.Integrations.GiftPromo.Client do
  alias Rms.Integrations.GiftPromo.ClientBehaviour
  @behaviour ClientBehaviour

  def validate(
        %{
          organization_id: organization_id,
          metadata: %{
            "card_number" => card_number,
            "card_pin" => card_pin
          }
        } = payment
      ) do
    payment = Rms.Repo.preload(payment, transaction: [:order])

    with %{store_id: store_id, access_key: access_key, username: username, password: password} <-
           Rms.Integrations.get_gift_promo_credentials!(organization_id, payment.location_id) do
      result =
        Rms.Integrations.GiftPromo.Client.client(
          %{username: username, password: password},
          Tesla.Adapter.Httpc
        )
        |> Rms.Integrations.GiftPromo.Client.check_gift_card(%{
          store_id: store_id,
          access_key: access_key,
          card_number: card_number,
          card_pin: card_pin
        })

      case result do
        {:ok, body} ->
          case body do
            %{"sucesso" => "1", "cartaoStatus" => "Ativo"} ->
              {:ok, body}

            _ ->
              {:error, body}
          end

        {:error, error} ->
          {:error, error}
      end
    end
  end

  defp get_random_nsu() do
    Enum.reduce(0..49, "", fn _, acc -> "#{:rand.uniform(9)}#{acc}" end)
  end

  @impl ClientBehaviour
  def validate_gift_card_password(client, %{
        store_id: store_id,
        access_key: access_key,
        card_number: card_number,
        card_pin: card_pin
      }) do
    nsu = get_random_nsu()
    capture_type = Application.get_env(:rms, Rms.Integrations.GiftPromo, [])[:capture_type]
    provider = Application.get_env(:rms, Rms.Integrations.GiftPromo, [])[:provider]

    case redeem_gift_card(client, %{
           store_id: store_id,
           access_key: access_key,
           card_number: card_number,
           card_pin: card_pin,
           value: 0.01,
           nsu: nsu,
           capture_type: capture_type,
           provider: provider
         }) do
      {:ok,
       %{
         "autorizacao" => authorization,
         "nsuHost" => nsu_host,
         "sucesso" => "1"
       }} ->
        refund_transaction(client, %{
          store_id: store_id,
          access_key: access_key,
          card_number: card_number,
          card_pin: card_pin,
          authorization: authorization,
          nsu: nsu,
          nsu_host: nsu_host,
          capture_type: capture_type,
          provider: provider
        })

      {:ok, %{"autorizacao" => "", "codigoErro" => "18", "mensagem" => _}} ->
        {:error, :bad_request}
        # {:error, %{"code" => "18", "message" => message}}
    end
  end

  @impl ClientBehaviour
  def check_gift_card(client, %{
        store_id: store_id,
        access_key: access_key,
        card_number: card_number,
        card_pin: card_pin
      }) do
    Tesla.post!(client, "/ConsultaCartao", %{
      lojaId: store_id,
      chaveAcesso: access_key,
      numeroCartao: card_number,
      pinCartao: card_pin
    })
    |> normalize_response()
  end

  @impl ClientBehaviour
  def redeem_gift_card(client, %{
        store_id: store_id,
        access_key: access_key,
        card_number: card_number,
        card_pin: card_pin,
        value: value,
        nsu: nsu,
        capture_type: capture_type,
        provider: provider
      }) do
    Tesla.post!(client, "/VendaResgate", %{
      lojaId: store_id,
      chaveAcesso: access_key,
      numeroCartao: card_number,
      pinCartao: card_pin,
      valor: value,
      nsu: nsu,
      meioCaptura: capture_type,
      provedor: provider
    })
    |> normalize_response()
  end

  @impl ClientBehaviour
  def refund_transaction(client, %{
        store_id: store_id,
        access_key: access_key,
        card_number: card_number,
        card_pin: card_pin,
        authorization: authorization,
        nsu: nsu,
        nsu_host: nsu_host,
        capture_type: capture_type,
        provider: provider
      }) do
    Tesla.post!(client, "/Estorno", %{
      lojaId: store_id,
      chaveAcesso: access_key,
      numeroCartao: card_number,
      pinCartao: card_pin,
      autorizacao: authorization,
      nsu: nsu,
      nsuHost: nsu_host,
      meioCaptura: capture_type,
      provedor: provider
    })
    |> normalize_response()
  end

  @impl ClientBehaviour
  def client(
        %{username: username, password: password},
        adapter \\ Application.get_env(:tesla, :adapter)
      ) do
    Tesla.client(
      [
        {Tesla.Middleware.BaseUrl,
         Application.get_env(:rms, Rms.Integrations.GiftPromo, [])[:base_url]},
        Tesla.Middleware.JSON,
        {Tesla.Middleware.BasicAuth, username: username, password: password},
        Tesla.Middleware.OpenTelemetry
      ],
      adapter
    )
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
