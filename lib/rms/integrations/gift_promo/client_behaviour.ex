defmodule Rms.Integrations.GiftPromo.ClientBehaviour do
  @type client :: Tesla.Client.t()
  @type error ::
          :bad_request
          | :forbidden
          | :not_found
          | :internal_server_error
          | {:validation_error, map()}
          | :unknown_error

  @type result :: {:ok, map()} | {:error, error()}

  @callback check_gift_card(client :: client(), params :: map()) :: result()
  @callback redeem_gift_card(client :: client(), params :: map()) :: result()
  @callback refund_transaction(client :: client(), params :: map()) :: result()
  @callback validate_gift_card_password(client :: client(), params :: map()) :: result()

  @callback client(%{username: charlist(), password: charlist()}) :: client()
end
