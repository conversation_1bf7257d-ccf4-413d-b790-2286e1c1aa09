defmodule Rms.Integrations.GiftPromo.Credential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "gift_promo_credential" do
    field :store_id, :string
    field :access_key, Rms.Vault.EncryptedBinary
    field :username, Rms.Vault.EncryptedBinary
    field :password, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(credential, attrs) do
    credential
    |> cast(attrs, [:location_id, :store_id, :access_key, :username, :password])
    |> validate_required([:location_id, :store_id, :access_key])
  end
end
