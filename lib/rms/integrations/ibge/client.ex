defmodule Rms.Integrations.Ibge.Client do
  alias Rms.Integrations.Ibge.ClientBehaviour
  @behaviour ClientBehaviour
  @ibge_url "https://servicodados.ibge.gov.br/api/v1"

  @impl ClientBehaviour
  def get_cities_data() do
    Tesla.get!(
      client(),
      "/localidades/municipios"
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def client() do
    middleware = [
      {Tesla.Middleware.BaseUrl, @ibge_url},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Opts,
       [
         adapter: [
           ssl_options: [
             verify: :verify_peer,
             versions: [:"tlsv1.2"],
             server_name_indication: ~c"servicodados.ibge.gov.br",
             cacerts: :public_key.cacerts_get(),
             customize_hostname_check: [
               match_fun: :public_key.pkix_verify_hostname_match_fun(:https)
             ]
           ]
         ]
       ]}
    ]

    Tesla.client(middleware, {Tesla.Adapter.Hackney, [recv_timeout: 30_000]})
  end

  defp normalize_response(%{status: status, body: body}) when status in 200..299 do
    transformed_data =
      body
      |> Enum.map(&transform_municipality/1)
      |> Enum.reject(&is_nil/1)

    {:ok, transformed_data}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end

  defp transform_municipality(%{
         "id" => id,
         "nome" => name,
         "microrregiao" => %{
           "mesorregiao" => %{
             "UF" => %{
               "sigla" => uf_initials,
               "nome" => uf_name
             }
           }
         }
       }) do
    %{
      ibge_id: id,
      name: name,
      uf_name: uf_name,
      uf_initials: uf_initials
    }
  end

  defp transform_municipality(_), do: nil
end
