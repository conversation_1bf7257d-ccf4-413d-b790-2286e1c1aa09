defmodule Rms.Integrations.InventoryItemMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "inventory_item_mappings" do
    field :external_id, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(inventory_item_mapping, attrs) do
    inventory_item_mapping
    |> cast(attrs, [:organization_id, :product_variant_id, :external_id, :source])
    |> validate_required([:organization_id, :product_variant_id, :external_id, :source])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:product_variant)
  end
end
