defmodule Rms.Integrations.LocationMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "location_mappings" do
    field :external_id, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(location_mapping, attrs) do
    location_mapping
    |> cast(attrs, [:organization_id, :location_id, :external_id, :source])
    |> validate_required([:organization_id, :location_id, :external_id, :source])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:location)
  end
end
