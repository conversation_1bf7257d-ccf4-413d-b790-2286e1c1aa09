defmodule Rms.Integrations.LocationSyncEntry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "location_sync_entries" do
    field :external_id, :string
    field :status, :string

    embeds_many :errors, Rms.Integrations.ErrorEntry

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location_sync, Rms.Integrations.LocationSync

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(location_sync_entry, attrs) do
    location_sync_entry
    |> cast(attrs, [:external_id, :status])
    |> cast_embed(:errors)
    |> validate_required([:external_id, :status])
  end
end
