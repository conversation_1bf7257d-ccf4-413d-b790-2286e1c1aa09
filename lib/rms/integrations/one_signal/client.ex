defmodule Rms.Integrations.OneSignal.Client do
  alias Rms.Integrations.OneSignal.ClientBehaviour
  @behaviour ClientBehaviour

  @impl ClientBehaviour
  def client() do
    middleware = [
      {Tesla.Middleware.BaseUrl, Application.get_env(:rms, :one_singal_url)},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Timeout, timeout: 30_000},
      {Tesla.Middleware.Headers,
       [{"Authorization", "Basic " <> Application.get_env(:rms, :one_singal_api_key)}]},
      Tesla.Middleware.OpenTelemetry
    ]

    Tesla.client(middleware)
  end

  @impl ClientBehaviour
  def send_push_notification(%{
        message: message,
        title: title,
        staffs_ids: staffs_ids,
        url: url,
        channel_id: channel_id
      }) do
    Tesla.post!(
      client(),
      "/notifications?c=push",
      %{
        app_id: Application.get_env(:rms, :one_singal_app_id),
        contents: %{en: message},
        headings: %{en: title},
        android_channel_id: channel_id,
        include_aliases: %{external_id: staffs_ids},
        target_channel: "push",
        url: url
      }
    )
    |> normalize_response()
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
