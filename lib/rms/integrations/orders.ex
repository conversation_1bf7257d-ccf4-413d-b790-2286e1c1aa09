defmodule Rms.Integrations.Orders do
  import Ecto.Query

  alias Rms.Repo
  alias Rms.Integrations.Orders.CancelationEndpoint
  alias Rms.Settings

  def list_cancelation_endpoints(organization_id) do
    CancelationEndpoint
    |> where(organization_id: ^organization_id)
    |> Repo.all()
  end

  def get_cancelation_endpoint!(organization_id, id) do
    CancelationEndpoint
    |> where(organization_id: ^organization_id, id: ^id)
    |> Repo.one!()
  end

  def get_active_cancelation_endpoint(organization_id) do
    CancelationEndpoint
    |> where(organization_id: ^organization_id, active: true)
    |> Repo.one()
  end

  def create_cancelation_endpoint(organization_id, attrs \\ %{}) do
    %CancelationEndpoint{organization_id: organization_id}
    |> CancelationEndpoint.changeset(attrs)
    |> Repo.insert()
  end

  def update_cancelation_endpoint(%CancelationEndpoint{} = cancelation_endpoint, attrs) do
    cancelation_endpoint
    |> CancelationEndpoint.changeset(attrs)
    |> Repo.update()
  end

  def delete_cancelation_endpoint(%CancelationEndpoint{} = cancelation_endpoint) do
    Repo.delete(cancelation_endpoint)
  end

  def cancel_order(cancelation_endpoint, order_id, reason \\ "order canceled via iGlu") do
    client = cancelation_endpoint_client(cancelation_endpoint)

    transformer =
      case Settings.get_organization_setting(
             cancelation_endpoint.organization_id,
             "cancelation_transformer"
           ) do
        %{value: %{"data" => transformer}} -> transformer
        _ -> nil
      end

    case Tesla.post(
           client,
           "",
           transform_payload(transformer, %{order_id: order_id, reason: reason})
         ) do
      {:ok, %Tesla.Env{status: 200, body: body}} ->
        {:ok, body}

      {:ok, %Tesla.Env{status: status, body: body}} when status >= 400 ->
        {:error, {status, body}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp cancelation_endpoint_client(cancelation_endpoint) do
    headers = cancelation_endpoint.headers |> Enum.map(fn [key, value] -> {key, value} end)

    middleware = [
      {Tesla.Middleware.BaseUrl, cancelation_endpoint.endpoint},
      {Tesla.Middleware.Headers, headers},
      Tesla.Middleware.JSON,
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.LogError
    ]

    Tesla.client(middleware)
  end

  defp transform_payload("areco", payload) do
    order = Rms.Commerce.Orders.unsafe_get_order!(payload[:order_id], [:location])

    %{idIglu: payload[:order_id], motivoCancelamento: payload[:reason], cnpj: order.location.cnpj}
  end

  defp transform_payload(_, payload), do: payload
end
