defmodule Rms.Integrations.Orders.CancelationEndpoint do
  use Ecto.Schema
  import Ecto.Changeset

  schema "cancelation_endpoints" do
    field :endpoint, :string
    field :headers, {:array, {:array, :string}}
    field :active, :boolean, default: false

    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  def changeset(cancelation_endpoint, attrs) do
    cancelation_endpoint
    |> cast(attrs, [:endpoint, :headers, :active])
    |> validate_required([:endpoint, :headers, :active])
    |> unique_constraint([:organization_id],
      message: "already exists an active cancelation endpoint for this organization"
    )
    |> validate_change(:endpoint, fn :endpoint, value ->
      with %URI{host: host, scheme: scheme} when scheme in ["http", "https"] <- URI.parse(value),
           {:error, _} <- :inet.parse_address(to_charlist(host)) do
        []
      else
        _ -> [endpoint: "is invalid"]
      end
    end)
    |> validate_change(:headers, fn :headers, value ->
      case Enum.all?(value, &(is_list(&1) and length(&1) == 2)) do
        true -> []
        false -> [headers: "is invalid"]
      end
    end)
  end
end
