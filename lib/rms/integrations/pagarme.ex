defmodule Rms.Integrations.PagarMe do
  alias Rms.Integrations.PagarMe.Client, as: PagarMeClient
  @pagarme_client Application.compile_env(:rms, :pagarme_client, PagarMeClient)

  @doc """
  Creates a payment link

  ## Examples

      iex> create_payment_link(client, customer_name, items)
      {:ok, payment_link}

      iex> create_payment_link(client, customer_name, items)
      {:error, reason}

  """
  def create_payment_link(client, customer, items, opts \\ []) do
    customer =
      case customer do
        %{document_type: document_type} when document_type in ~w(CNPJ cnpj) ->
          Map.merge(customer, %{type: "company"})

        %{document_type: document_type} when document_type in ~w(CPF cpf) ->
          Map.merge(customer, %{type: "individual"})

        %{document: document} when is_binary(document) ->
          Map.drop(customer, [:document])

        customer ->
          customer
      end

    total =
      Enum.reduce(items, 0, fn item, acc ->
        acc + item.amount * item.quantity
      end)

    params =
      %{
        customer: customer,
        items: items,
        payments: [
          %{
            payment_method: "checkout",
            checkout: %{
              expires_in: 48 * 60,
              billing_address_editable: true,
              customer_editable: true,
              accepted_payment_methods: ["credit_card", "pix", "debit_card"],
              pix: %{
                expires_in: 259_200
              },
              billing_address: Keyword.get(opts, :billing_address),
              credit_card: %{
                capture: true,
                statement_descriptor: "Link POS",
                installments:
                  Enum.map(1..Keyword.get(opts, :installments, 1), fn installment ->
                    %{
                      number: installment,
                      total: total
                    }
                  end)
              }
            }
          }
        ]
      }

    response = @pagarme_client.create_order(client, params)

    case response do
      {:ok, %{"checkouts" => [%{"payment_url" => payment_url} | _], "id" => external_id}} ->
        {:ok, payment_url, external_id}

      {:ok, _} ->
        {:error, :missing_payment_url}

      error ->
        error
    end
  end

  def get_order(client, external_id) do
    @pagarme_client.get_order(client, external_id)
  end

  def client(token) do
    @pagarme_client.client(token)
  end
end
