defmodule Rms.Integrations.PagarMe.Client do
  alias Rms.Integrations.PagarMe.ClientBehaviour
  @behaviour ClientBehaviour

  @impl ClientBehaviour
  def create_order(client, params) do
    client
    |> Tesla.post!("/orders", params)
    |> normalize_response()
  end

  @impl ClientBehaviour
  def get_order(client, order_id) do
    client
    |> Tesla.get!("/orders/:order_id", opts: [path_params: [order_id: order_id]])
    |> normalize_response()
  end

  @impl ClientBehaviour
  def client(token) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://api.pagar.me/core/v5"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.BasicAuth, %{username: token, password: ""}},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.PathParams,
      Tesla.Middleware.LogError
    ]

    Tesla.client(middleware)
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
