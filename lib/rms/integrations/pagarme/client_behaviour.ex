defmodule Rms.Integrations.PagarMe.ClientBehaviour do
  @type client :: Tesla.Client.t()
  @type error ::
          :bad_request
          | :forbidden
          | :not_found
          | :internal_server_error
          | {:validation_error, map()}
          | :unknown_error

  @type result :: {:ok, map()} | {:error, error()}

  @callback create_order(client :: client(), params :: map()) :: result()
  @callback get_order(client :: client(), order_id :: binary() | integer()) :: result()
  @callback client(token :: binary()) :: client()
end
