defmodule Rms.Integrations.PagarMe.UpdatePayment do
  require OpenTelemetry.Tracer
  require Logger

  alias Rms.Finance

  @doc """
  Updates a payment based on PagarMe order data.

  `organization_id`: An organization ID.
  `pagarme_order_data` is a map expected to contain PagarMe's order/payment details,
  specifically an "id" key (for the external reference) and a "status" key (e.g., "paid", "canceled").

  Returns:
  - `{:ok, :updated_payment}` if the payment was successfully synced.
  - `{:error, :not_found}` if the payment could not be found.
  - `{:error, changeset}` if `Finance.sync_payment/2` failed.
  - `{:error, {:invalid_status, status}}` if the new status is not "settled" or
    "canceled".
  """
  def execute(organization_id, pagarme_order_data) do
    OpenTelemetry.Tracer.with_span "pagarme update_payment", %{
      organization_id: organization_id,
      pagarme_order_data: inspect(pagarme_order_data)
    } do
      payment_status = pagarme_status(pagarme_order_data["status"])

      with {:ok, payment} <-
             Finance.get_payment_by_external_reference_and_organization(
               organization_id,
               "pagarme",
               pagarme_order_data["id"]
             ),
           :ok <- validate_status(payment_status) do
        pagarme_metadata = extract_charges_metadata(pagarme_order_data)
        metadata = Map.merge(payment.metadata, pagarme_metadata)

        Finance.sync_payment(payment, %{status: payment_status, metadata: metadata})
      end
    end
  end

  # Extract transaction metadata from PagarMe order data
  defp extract_charges_metadata(%{"charges" => []}) do
    %{}
  end

  defp extract_charges_metadata(%{"charges" => charges}) when is_list(charges) do
    charges_metadata =
      Enum.map(charges, fn charge ->
        case charge do
          %{
            "last_transaction" =>
              %{
                "acquirer_tid" => tid,
                "acquirer_nsu" => nsu,
                "acquirer_auth_code" => aut
              } = transaction
          } ->
            amount =
              transaction["amount"]
              |> Decimal.new()
              |> Decimal.div(100)

            %{
              "tid" => tid,
              "nsu" => nsu,
              "aut" => aut,
              "amount" => amount,
              "installments" => transaction["installments"]
            }

          _ ->
            Logger.warning("Charge without expected transaction data: #{inspect(charge)}")
            nil
        end
      end)
      |> Enum.reject(&is_nil/1)

    case charges_metadata do
      [] -> %{}
      _ -> %{"charges" => charges_metadata}
    end
  end

  defp extract_charges_metadata(_), do: %{}

  defp validate_status(internal_status) when internal_status in ["settled", "canceled"] do
    :ok
  end

  defp validate_status(internal_status) do
    {:error, {:invalid_status, internal_status}}
  end

  defp pagarme_status("paid") do
    "settled"
  end

  defp pagarme_status("canceled") do
    "canceled"
  end

  defp pagarme_status("pending") do
    "pending"
  end

  defp pagarme_status("failed") do
    "failed"
  end
end
