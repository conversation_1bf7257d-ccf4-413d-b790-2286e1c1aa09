defmodule Rms.Integrations.PagarMeCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "pagarme_credentials" do
    field :credential, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(pagar_me_credential, attrs) do
    pagar_me_credential
    |> cast(attrs, [:credential, :organization_id, :location_id])
    |> validate_required([:credential, :organization_id])
  end
end
