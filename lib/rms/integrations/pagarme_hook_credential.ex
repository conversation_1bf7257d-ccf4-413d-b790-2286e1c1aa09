defmodule Rms.Integrations.PagarMeHookCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "pagarme_hook_credentials" do
    field :shop_id, :string
    field :credential, Rms.Vault.EncryptedBinary
    field :credential_hash, Cloak.Ecto.SHA256

    belongs_to :organization, Rms.Accounts.Organization
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(pagar_me_credential, attrs) do
    pagar_me_credential
    |> cast(attrs, [:credential, :shop_id])
    |> validate_required([:credential, :shop_id])
    |> put_hashed_fields()
  end

  defp put_hashed_fields(changeset) do
    changeset
    |> put_change(:credential_hash, get_field(changeset, :credential))
  end
end
