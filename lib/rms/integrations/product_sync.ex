defmodule Rms.Integrations.ProductSync do
  use Ecto.Schema

  import Ecto.Changeset

  schema "product_sync" do
    field :external_id, :string
    field :expected_count, :integer
    field :status, :string, default: "in-progress"
    field :message, :string
    field :source, :string

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(product_sync, attrs) do
    product_sync
    |> cast(attrs, [:external_id, :expected_count, :source, :status, :message])
  end
end
