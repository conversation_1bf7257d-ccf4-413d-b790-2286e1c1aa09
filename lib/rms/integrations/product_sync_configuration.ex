defmodule Rms.Integrations.ProductSyncConfiguration do
  use Ecto.Schema
  import Ecto.Changeset

  schema "product_sync_configurations" do
    field :field_priorities, :map
    field :default_priority, {:array, :string}
    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  def changeset(product_sync_configuration, attrs) do
    product_sync_configuration
    |> cast(attrs, [:field_priorities, :default_priority, :organization_id])
    |> validate_required([:field_priorities, :default_priority, :organization_id])
    |> validate_field_priorities()
    |> assoc_constraint(:organization)
    |> unique_constraint(:organization_id)
  end

  defp validate_field_priorities(changeset) do
    validate_change(changeset, :field_priorities, fn :field_priorities, priorities ->
      if valid_priorities_format?(priorities) do
        []
      else
        [field_priorities: "must be a map with array of strings as values"]
      end
    end)
  end

  defp valid_priorities_format?(priorities) when is_map(priorities) do
    Enum.all?(priorities, fn {_key, value} ->
      is_list(value) and Enum.all?(value, &is_binary/1)
    end)
  end

  defp valid_priorities_format?(_), do: false
end
