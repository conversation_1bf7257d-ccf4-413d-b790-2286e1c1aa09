defmodule Rms.Integrations.ProductSyncEntry do
  use Ecto.Schema

  import Ecto.Changeset

  schema "product_sync_entries" do
    field :sku, :string
    field :external_id, :string
    field :status, :string

    embeds_many :errors, Rms.Integrations.ErrorEntry

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :product_sync, Rms.Integrations.ProductSync

    timestamps()
  end

  def changeset(product_sync_entry, attrs) do
    product_sync_entry
    |> cast(attrs, [:sku, :external_id, :status])
    |> cast_embed(:errors)
    |> validate_required([:external_id, :status])
  end
end
