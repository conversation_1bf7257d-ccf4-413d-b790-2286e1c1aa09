defmodule Rms.Integrations.ProductSyncMapping do
  use Ecto.Schema

  import Ecto.Changeset

  schema "product_sync_mappings" do
    belongs_to :organization, Rms.Accounts.Organization

    belongs_to :product, Rms.Commerce.Products.Product,
      defaults: {Rms.Repo, :add_organization_id, []}

    field :external_id, :string
    field :source, :string

    timestamps()
  end

  @doc false
  def changeset(product_sync_mapping, attrs) do
    product_sync_mapping
    |> cast(attrs, [:product_id, :organization_id, :source, :external_id])
    |> validate_required([:source, :external_id])
    |> unique_constraint([:organization_id, :source, :external_id], error_key: :external_id)
    |> assoc_constraint(:organization)
    |> assoc_constraint(:product)
  end
end
