defmodule Rms.Integrations.ProductVariantMapping do
  use Ecto.Schema
  import Ecto.Changeset

  schema "product_variant_mappings" do
    field :external_id, :string
    field :source, :string
    field :sold_out, :boolean, default: false

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :product_variant, Rms.Commerce.Products.ProductVariant

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(product_variant_mapping, attrs) do
    product_variant_mapping
    |> cast(attrs, [:organization_id, :product_variant_id, :external_id, :source, :sold_out])
    |> validate_required([:organization_id, :product_variant_id, :external_id, :source])
    |> assoc_constraint(:organization)
    |> assoc_constraint(:product_variant)
  end
end
