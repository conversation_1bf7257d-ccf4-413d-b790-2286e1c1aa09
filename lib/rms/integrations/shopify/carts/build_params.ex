defmodule Rms.Integrations.Shopify.Carts.BuildParams do
  alias Rms.Integrations.Shopify.Carts.BuildParams.Customer
  alias Rms.Integrations.Shopify.Carts.BuildParams.Discounts
  alias Rms.Integrations.Shopify.Carts.BuildParams.Items

  def execute(cart_info, external_id_map, organization_id, opts \\ []) do
    formatted_attributes = format_cart_attributes(opts[:cart_attributes])

    %{
      lines: Items.execute(cart_info, external_id_map),
      buyerIdentity: Customer.execute(cart_info, organization_id),
      discountCodes: Discounts.execute(cart_info, organization_id),
      attributes: formatted_attributes
    }
    |> Map.reject(fn {_k, v} -> is_nil(v) end)
  end

  defp format_cart_attributes(nil), do: nil

  defp format_cart_attributes(attributes) do
    Enum.map(attributes, fn {key, value} ->
      %{key: key, value: value}
    end)
  end
end
