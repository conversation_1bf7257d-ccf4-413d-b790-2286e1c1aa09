defmodule Rms.Integrations.Shopify.Carts.BuildParams.Customer do
  @postal_codes_ranges [
    {"SP", 1_000_000..19_999_999},
    {"RJ", 20_000_000..28_999_999},
    {"ES", 29_000_000..29_999_999},
    {"MG", 30_000_000..39_999_999},
    {"BA", 40_000_000..48_999_999},
    {"SE", 49_000_000..49_999_999},
    {"PE", 50_000_000..56_999_999},
    {"AL", 57_000_000..57_999_999},
    {"PB", 58_000_000..58_999_999},
    {"RN", 59_000_000..59_999_999},
    {"CE", 60_000_000..63_999_999},
    {"PI", 64_000_000..64_999_999},
    {"MA", 65_000_000..65_999_999},
    {"PA", 66_000_000..68_899_999},
    {"AP", 68_900_000..68_999_999},
    {"AM", 69_000_000..69_299_999},
    {"RR", 69_300_000..69_399_999},
    {"RO", 69_400_000..69_999_999},
    {"AC", 69_900_000..69_999_999},
    {"DF", 70_000_000..72_799_999},
    {"GO", 72_800_000..73_799_999},
    {"TO", 77_000_000..77_999_999},
    {"MT", 78_000_000..78_899_999},
    {"MS", 79_000_000..79_999_999},
    {"MG", 80_000_000..87_999_999},
    {"SC", 88_000_000..89_999_999},
    {"RS", 90_000_000..99_999_999}
  ]

  def execute(%{"customer_id" => nil} = cart_info, _organization_id) do
    %{
      countryCode: format_country(cart_info["country"]),
      deliveryAddressPreferences: build_delivery_address(cart_info)
    }
  end

  def execute(%{"customer_id" => customer_id} = cart_info, organization_id) do
    customer = Rms.Customers.get_customer!(organization_id, customer_id)

    %{
      email: customer.email,
      countryCode: format_country(cart_info["country"]),
      deliveryAddressPreferences: build_delivery_address(cart_info)
    }
  end

  def execute(cart_info, _organization_id) do
    %{
      countryCode: format_country(cart_info["country"]),
      deliveryAddressPreferences: build_delivery_address(cart_info)
    }
  end

  defp build_delivery_address(%{"postal_code" => postal_code, "country" => country}) do
    [
      %{
        deliveryAddress: %{
          company: "IGLUPOS",
          province: get_state_by_postal_code(postal_code),
          country: format_country(country),
          zip: postal_code
        }
      }
    ]
  end

  defp get_state_by_postal_code(postal_code) do
    postal_code_int = String.to_integer(postal_code)

    case Enum.find(@postal_codes_ranges, fn {_uf, range} -> postal_code_int in range end) do
      {uf, _range} ->
        uf

      nil ->
        raise "Postal Code invalid"
    end
  end

  defp format_country("BRA") do
    "BR"
  end

  defp format_country(_) do
    raise "Country invalid"
  end
end
