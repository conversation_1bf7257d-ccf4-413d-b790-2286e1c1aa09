defmodule Rms.Integrations.Shopify.Carts.DeliveryOptions do
  alias Rms.Integrations.Shopify.Products.GetVariantEcommerceStock
  alias Rms.Integrations.Shopify.Carts.Simulate

  require Logger

  @delivery_types ~w(SHIPPING LOCAL)

  @logistic_info_mock %{
    "delivery_time" => "1bd",
    "delivery_type" => "delivery",
    "id" => "delivery",
    "metadata" => %{
      "deliveryMethodType" => "SHIPPING",
      "estimatedCost" => %{
        "amount" => "0.00",
        "currencyCode" => "BRL"
      },
      "handle" => "delivery",
      "title" => "Fake Delivery"
    },
    "name" => "Fake Delivery",
    "price" => 0,
    "quantity" => 1
  }

  @type delivery_option :: %{
          delivery_time: String.t(),
          delivery_type: String.t(),
          id: String.t(),
          metadata: map(),
          name: String.t(),
          price: integer(),
          quantity: integer()
        }

  @type item :: %{
          delivery_options: list(delivery_option),
          item_index: integer(),
          product_variant_id: integer()
        }

  @type delivery_options :: %{
          items: list(item),
          pickup_points: list(map())
        }

  @spec execute(organization_id :: pos_integer(), cart_info :: map()) :: {:ok, delivery_options()}
  def execute(organization_id, cart_info) do
    if Rms.FeatureFlag.should_get_variant_stock?(organization_id) do
      execute_variant_stock_strategy(organization_id, cart_info)
    else
      execute_simulate_strategy(organization_id, cart_info)
    end
  end

  def execute_simulate_strategy(organization_id, cart_info) do
    product_variant_ids = Enum.map(cart_info["items"], & &1["product_variant_id"])

    external_id_map =
      Rms.Integrations.get_product_variant_external_ids(
        organization_id,
        product_variant_ids,
        "shopify"
      )

    {ecommerce_items, local_items} =
      filter_items(cart_info, external_id_map)

    ecommerce_items =
      Enum.map(ecommerce_items, fn item ->
        item
        |> Map.put("fulfillment_type", nil)
      end)

    ecommerce_cart_info =
      cart_info
      |> Map.put("items", ecommerce_items)
      |> Map.put("discounts", [])

    items_groups =
      Enum.group_by(ecommerce_cart_info["items"], fn %{"fulfillment_type" => fulfillment_type} ->
        {fulfillment_type, nil}
      end)

    simulations =
      Simulate.prepare_and_simulate_carts(
        organization_id,
        ecommerce_cart_info,
        items_groups,
        external_id_map
      )
      |> format_local_items_simulations(local_items)

    {:ok, format_response(organization_id, simulations)}
  end

  defp format_local_items_simulations(simulations, local_items) do
    Enum.reduce(local_items, simulations, fn local_item, acc ->
      [
        %{
          fulfillment_type: nil,
          requested_group: nil,
          items: [local_item],
          simulation: nil,
          message: nil
        }
        | acc
      ]
    end)
  end

  defp format_response(organization_id, simulations) do
    enable_delivery = enable_delivery?(organization_id)

    %{
      "items" =>
        Enum.reduce(simulations, [], fn simulation, acc ->
          get_simulation_items(organization_id, simulation, enable_delivery) ++ acc
        end),
      "pickup_points" => []
    }
  end

  defp filter_items(cart_info, external_id_map) do
    cart_info["items"]
    |> Enum.with_index(fn item, index -> Map.put(item, "item_index", index) end)
    |> Enum.split_with(fn item -> Map.has_key?(external_id_map, item["product_variant_id"]) end)
    |> (fn {ecommerce_items, local_items} ->
          ecommerce_acc =
            Enum.map(ecommerce_items, fn item ->
              external_id = external_id_map[item["product_variant_id"]]
              Map.put(item, "external_id", external_id)
            end)

          {ecommerce_acc, local_items}
        end).()
  end

  defp get_simulation_items(
         _organization_id,
         %{
           simulation: nil
         } = simulation,
         enable_delivery
       ) do
    Enum.reduce(simulation.items, [], fn item, acc ->
      formated_item =
        %{
          "delivery_options" =>
            get_logistic_info(
              item["quantity"],
              simulation,
              enable_delivery
            ),
          "item_index" => item["item_index"],
          "product_variant_id" => item["product_variant_id"]
        }

      [formated_item | acc]
    end)
  end

  defp get_simulation_items(
         _organization_id,
         %{
           simulation: %{
             cart: %{
               "lines" => %{
                 "edges" => edges
               }
             }
           }
         } = simulation,
         enable_delivery
       ) do
    Enum.reduce(edges, [], fn
      %{"node" => %{"cost" => _cost, "merchandise" => merchandise, "quantity" => 0}}, acc ->
        variant =
          Enum.find(simulation.items, fn item -> item["external_id"] == merchandise["id"] end)

        item =
          %{
            "delivery_options" =>
              get_logistic_info(
                variant["quantity"],
                simulation,
                enable_delivery
              ),
            "item_index" => variant["item_index"],
            "product_variant_id" => variant["product_variant_id"]
          }

        [item | acc]

      %{"node" => %{"cost" => _cost, "merchandise" => merchandise, "quantity" => quantity}},
      acc ->
        variant =
          Enum.find(simulation.items, fn item -> item["external_id"] == merchandise["id"] end)

        item =
          %{
            "delivery_options" =>
              get_logistic_info(
                quantity,
                simulation,
                enable_delivery
              ),
            "item_index" => variant["item_index"],
            "product_variant_id" => variant["product_variant_id"]
          }

        [item | acc]
    end)
  end

  defp get_logistic_info(
         quantity,
         %{
           simulation: %{delivery: %{"deliveryOptions" => delivery_options}}
         },
         true
       ) do
    delivery_options
    |> Enum.filter(fn delivery_option ->
      delivery_option["deliveryMethodType"] in @delivery_types
    end)
    |> Enum.map(fn delivery_option ->
      %{
        "delivery_time" => "1bd",
        "delivery_type" => "delivery",
        "id" => delivery_option["handle"],
        "metadata" => delivery_option,
        "name" => delivery_option["title"],
        "price" =>
          Decimal.to_integer(
            Decimal.mult(Decimal.new(delivery_option["estimatedCost"]["amount"]), 100)
          ),
        "quantity" => quantity
      }
    end)
  end

  defp get_logistic_info(
         _quantity,
         _simulation,
         false
       ) do
    []
  end

  defp get_logistic_info(
         _quantity,
         %{
           simulation: nil
         },
         _enable_delivery
       ) do
    []
  end

  defp enable_delivery?(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "enable_delivery") do
      %{value: %{"data" => enable_delivery}} -> enable_delivery
      _ -> true
    end
  end

  @doc """
    Returns the delivery options for the given cart info.

    The delivery options are retrieved from the product variant stock and
    filtered by the delivery types allowed for the organization.

    The delivery options are returned in the following format:
  """
  @spec execute_variant_stock_strategy(organization_id :: pos_integer(), cart_info :: map()) ::
          {:ok, delivery_options()}
  def execute_variant_stock_strategy(organization_id, cart_info) do
    product_variant_ids = Enum.map(cart_info["items"], & &1["product_variant_id"])

    product_variant_ids
    |> Enum.with_index()
    |> Enum.map(fn {product_variant_id, index} ->
      case GetVariantEcommerceStock.call(organization_id, product_variant_id) do
        {:ok, stock} ->
          %{
            "delivery_options" =>
              if(Map.get(stock, :available_for_sale), do: [@logistic_info_mock], else: []),
            "item_index" => index,
            "product_variant_id" => product_variant_id
          }

        {:error, reason} ->
          Logger.error(
            "[#{__MODULE__}] failed to get variant stock: #{inspect(reason)} cart_info: #{inspect(cart_info)}"
          )

          %{
            "delivery_options" => [],
            "item_index" => index,
            "product_variant_id" => product_variant_id
          }
      end
    end)
    |> then(fn items -> {:ok, %{"items" => items, "pickup_points" => []}} end)
  end
end
