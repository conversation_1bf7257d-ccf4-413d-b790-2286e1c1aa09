defmodule Rms.Integrations.Shopify.Carts.FormatCartRequest do
  require Lo<PERSON>

  @mocked_delivery_options %{
    "title" => "Mocked Free Delivery",
    "handle" => Ecto.UUID.generate(),
    "deliveryMethodType" => "SHIPPING",
    "estimatedCost" => %{"amount" => "0.0"}
  }

  def format_cart_response(%{"data" => response}, _organization_id) do
    case handle_user_errors(response, "cartCreate") do
      {:ok, %{"cart" => data}} ->
        {:ok,
         %{
           cart: data,
           delivery: []
         }}

      error ->
        error
    end
  end

  def format_cart_response(response, organization_id) do
    [_, fisrt_part, _, second_part, _] =
      response
      |> String.split("\r\n\r\n")
      |> Enum.filter(&(String.trim(&1) != ""))

    with {:ok, cart, message} <- format_cart(Jason.decode!(fisrt_part)),
         {:ok, delivery} <- format_delivery(Jason.decode!(second_part), organization_id) do
      {
        :ok,
        %{
          cart: cart,
          delivery: delivery,
          message: message
        }
      }
    end
  end

  def format_cart(%{"data" => data}) do
    case handle_user_errors(data, "cartCreate") do
      {:ok, %{"cart" => data}} ->
        {:ok, data, nil}

      {:error,
       {:unknown_error_code,
        %{
          "field" => ["input", "lines", _, "quantity"],
          "message" => message
        }, %{"cart" => data}}} ->
        {:ok, data, message}
    end
  end

  def format_delivery(
        %{
          "incremental" => [
            %{
              "data" => %{
                "deliveryGroups" => %{
                  "edges" => [
                    %{"node" => data}
                  ]
                }
              }
            }
          ]
        },
        _organization_id
      ) do
    {:ok, data}
  end

  def format_delivery(
        %{
          "incremental" => [
            %{
              "data" => %{
                "deliveryGroups" => %{
                  "edges" => []
                }
              }
            }
          ]
        },
        organization_id
      ) do
    if Rms.FeatureFlag.should_return_mocked_delivery_options?(organization_id) do
      {:ok,
       %{
         "deliveryOptions" => [@mocked_delivery_options],
         "selectedDeliveryOption" => %{"amount" => "0.0", "currencyCode" => "BRL"}
       }}
    else
      {:ok,
       %{
         "deliveryOptions" => [],
         "selectedDeliveryOption" => %{"amount" => "0.0", "currencyCode" => "BRL"}
       }}
    end
  end

  def format_delivery(_, _) do
    {:ok,
     %{
       "deliveryOptions" => [%{"estimatedCost" => %{"amount" => "0.0"}}],
       "selectedDeliveryOption" => %{
         "estimatedCost" => %{"amount" => "0.0", "currencyCode" => "BRL"}
       }
     }}
  end

  defp handle_user_errors(response, key) do
    handle_user_errors(get_in(response, [key]))
  end

  defp handle_user_errors(%{"userErrors" => [error | _rest]} = response) do
    Logger.warning("error simulating cart",
      error_detail: inspect(error),
      response: inspect(response)
    )

    {:error, {:unknown_error_code, error, response}}
  end

  defp handle_user_errors(%{"userErrors" => []} = response) do
    {:ok, response}
  end
end
