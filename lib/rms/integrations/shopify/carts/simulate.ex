defmodule Rms.Integrations.Shopify.Carts.Simulate do
  @delivery_types ~w(SHIPPING LOCAL)
  @pickup_types ~w(PICKUP_POINT PICK_UP)

  require OpenTelemetry.Ctx

  def execute(organization_id, cart_info) do
    product_variant_ids = Enum.map(cart_info["items"], & &1["product_variant_id"])

    external_id_map =
      Rms.Integrations.get_product_variant_external_ids(
        organization_id,
        product_variant_ids,
        "shopify"
      )

    {local_cart_items, local_ecommerce_cart_items, ecommerce_cart_items, removed_items} =
      filter_items(cart_info, external_id_map)

    ecommerce_cart_items = Enum.reverse(ecommerce_cart_items)
    local_ecommerce_cart_items = Enum.reverse(local_ecommerce_cart_items)
    simulation_cart_items = ecommerce_cart_items ++ local_ecommerce_cart_items

    ecommerce_cart_info =
      Map.put(cart_info, "items", simulation_cart_items)

    items_groups =
      Enum.group_by(ecommerce_cart_info["items"], fn
        %{"fulfillment_type" => "local-pickup", "metadata" => metadata} ->
          {"local-pickup", metadata}

        %{"fulfillment_type" => fulfillment_type} ->
          {fulfillment_type, nil}
      end)

    # total_items_groups =
    #   case ecommerce_cart_info["items"] do
    #     [] ->
    #       %{}

    #     items ->
    #       %{
    #         {"total", nil} => items
    #       }
    #   end

    # total_simulations_task =
    #   Task.async(fn ->
    #     prepare_and_simulate_carts(
    #       organization_id,
    #       ecommerce_cart_info,
    #       total_items_groups,
    #       external_id_map
    #     )
    #   end)

    simulations =
      prepare_and_simulate_carts(
        organization_id,
        ecommerce_cart_info,
        items_groups,
        external_id_map
      )

    # total_simulations =
    #   Task.await(total_simulations_task)

    discounts =
      simulations
      |> get_simulation_discounts_codes()
      |> filter_discounts_target_types(ecommerce_cart_items)

    total_values = {:error, "unused"}

    format_response(simulations, organization_id)
    |> format_removed_items(removed_items, organization_id)
    |> format_local_items(local_cart_items, organization_id)
    |> format_cart(cart_info, discounts, total_values)
  end

  def prepare_and_simulate_carts(organization_id, cart_info, items_groups, external_id_map) do
    shopify_client = build_shopify_client(organization_id)
    cart_attributes = build_cart_attributes(organization_id, cart_info)

    tasks =
      Enum.map(
        items_groups,
        &start_simulation_task(shopify_client, organization_id, cart_info, &1, external_id_map,
          cart_attributes: cart_attributes
        )
      )

    Enum.map(tasks, &Task.await(&1, 10_000))
  end

  defp build_shopify_client(organization_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    Rms.Integrations.Shopify.storefront_client(
      shopify_credential.shop,
      shopify_credential.credential,
      organization_id: organization_id
    )
  end

  defp build_cart_attributes(organization_id, cart_info) do
    # Build the base attributes map
    default_attributes = %{
      "iglu_organization_id" => to_string(organization_id),
      "iglu_location_id" =>
        if(cart_info["location_id"], do: to_string(cart_info["location_id"]), else: nil)
    }

    # Get the Shopify location ID if one exists
    shopify_location_id = get_shopify_location_id(organization_id, cart_info["location_id"])

    # Merge the default attributes with the Shopify location
    default_attributes
    |> Map.merge(%{"store_location" => shopify_location_id})
    |> Map.reject(fn {_key, value} -> is_nil(value) end)
  end

  # Extract Shopify location ID from location mappings
  defp get_shopify_location_id(_organization_id, nil), do: nil

  defp get_shopify_location_id(organization_id, location_id) do
    organization_id
    |> Rms.Accounts.get_location!(location_id, preload: [:location_mappings])
    |> Map.get(:location_mappings, [])
    |> Enum.find_value(fn
      %{source: "shopify", external_id: "gid://shopify/Location/" <> external_id} -> external_id
      _ -> nil
    end)
  end

  defp start_simulation_task(
         shopify_client,
         organization_id,
         cart_info,
         {{fulfillment_type, selected_group}, items},
         external_id_map,
         opts
       ) do
    ctx = OpenTelemetry.Ctx.get_current()
    remote_ip = RmsWeb.get_remote_ip()

    Task.async(fn ->
      OpenTelemetry.Ctx.attach(ctx)
      RmsWeb.put_remote_ip(remote_ip)

      formatted_cart =
        Rms.Integrations.Shopify.Carts.BuildParams.execute(
          Map.put(cart_info, "items", items),
          external_id_map,
          organization_id,
          opts
        )

      simulate_cart(
        shopify_client,
        formatted_cart,
        fulfillment_type,
        selected_group,
        items,
        organization_id
      )
    end)
  end

  defp simulate_cart(
         shopify_client,
         formatted_cart,
         fulfillment_type,
         selected_group,
         items,
         organization_id
       ) do
    case Rms.Integrations.Shopify.simulate_cart(shopify_client, formatted_cart, organization_id) do
      {:ok, simulation} ->
        %{
          fulfillment_type: fulfillment_type,
          requested_group: selected_group,
          items: items,
          simulation: simulation,
          message: nil
        }

      _ ->
        %{
          fulfillment_type: fulfillment_type,
          requested_group: selected_group,
          items: items,
          simulation: nil,
          message: nil
        }
    end
  end

  defp format_cart(
         {fulfilliable, unfulfillable},
         cart_info,
         {discounts, invalid_discounts},
         {:error, _}
       ) do
    delivery_groups =
      format_delivery_groups(fulfilliable, unfulfillable)
      |> Enum.map(fn cart ->
        updated_items =
          Enum.map(cart["cart_items"], fn item ->
            selling_price =
              Decimal.div(item["total_price"], Decimal.new(item["quantity"]))

            item
            |> Map.put("selling_price", selling_price)
            |> Map.put("price", selling_price)
          end)

        Map.put(cart, "cart_items", updated_items)
      end)

    {cart_values, _} =
      calculate_cart_values(delivery_groups, cart_info["discounts"], discounts)

    ecommerce_discounts =
      build_ecommerce_discounts(discounts ++ cart_values["ecommerce_items_discounts"])

    cart_discounts = cart_info |> Map.get("discounts", [])

    %{
      "customer_id" => cart_info["customer_id"],
      "location_id" => cart_info["location_id"],
      "shipping_address_id" => cart_info["shipping_address_id"],
      "delivery_groups" => delivery_groups,
      "ecommerce" => "shopify",
      "messages" =>
        format_delivery_messages(unfulfillable) ++ format_invalid_discounts(invalid_discounts),
      "staff_id" => cart_info["staff_id"],
      "total_delivery_price" => cart_values["total_delivery_price"],
      "total_items_list_price" => cart_values["total_items_list_price"],
      "total_items_selling_price" => cart_values["total_items_selling_price"],
      "total_manual_discount" => cart_values["total_manual_discount"],
      "total_price" => cart_values["total_price"],
      "discounts" => ecommerce_discounts ++ cart_discounts,
      "total_ecommerce_discounts" => cart_values["total_ecommerce_discounts"] || Decimal.new("0"),
      "metadata" => %{"notes" => cart_info["notes"]}
    }
  end

  defp format_cart(
         {fulfilliable, unfulfillable},
         cart_info,
         {discounts, _messages},
         {:ok, total_values}
       ) do
    delivery_groups = format_delivery_groups(fulfilliable, unfulfillable)

    {cart_values, _} =
      calculate_cart_values(delivery_groups, cart_info["discounts"], discounts, total_values)

    %{
      "customer_id" => cart_info["customer_id"],
      "location_id" => cart_info["location_id"],
      "shipping_address_id" => cart_info["shipping_address_id"],
      "delivery_groups" => delivery_groups,
      "ecommerce" => "shopify",
      "messages" => format_delivery_messages(unfulfillable),
      "staff_id" => cart_info["staff_id"],
      "total_delivery_price" => cart_values["total_delivery_price"],
      "total_items_list_price" => cart_values["total_items_list_price"],
      "total_items_selling_price" => cart_values["total_items_selling_price"],
      "total_manual_discount" => cart_values["total_manual_discount"],
      "total_price" => cart_values["total_price"],
      "discounts" => cart_info["discounts"] || [],
      "total_ecommerce_discounts" => cart_values["total_ecommerce_discounts"] || Decimal.new("0"),
      "metadata" => %{"notes" => cart_info["metadata"]}
    }
  end

  defp calculate_cart_values(delivery_groups, _discounts, ecommerce_discounts) do
    fulfillable_items =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.flat_map(fn group -> group["cart_items"] end)

    total_items_list_price =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.mult(Decimal.new(item["list_price"]), Decimal.new(item["quantity"]))
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_price =
      fulfillable_items
      |> Stream.map(fn item -> item["total_price"] end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_delivery_price =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.reject(&(&1["fulfillment_type"] == "in-store"))
      |> Stream.map(fn group ->
        hd(group["cart_items"])["logistics_info"]["estimatedCost"]["amount"] || 0
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_manual_discount = Decimal.new("0")

    ecommerce_items_discounts =
      fulfillable_items
      |> Enum.reduce([], fn item, acc ->
        discounts = item["ecommerce_discounts"] || []
        discounts ++ acc
      end)

    total_ecommerce_discounts =
      Enum.reduce(ecommerce_discounts, Decimal.new("0"), fn %{
                                                              :code => _,
                                                              :value => value
                                                            },
                                                            acc ->
        Decimal.add(acc, value)
      end)

    total_items_selling_price =
      Decimal.sub(
        total_items_price,
        total_manual_discount
      )

    total_price =
      Decimal.add(
        total_delivery_price,
        total_items_selling_price
      )
      |> Decimal.sub(total_ecommerce_discounts)

    {%{
       "total_price" => total_price,
       "total_items_list_price" => total_items_list_price,
       "total_manual_discount" => total_manual_discount,
       "total_delivery_price" => total_delivery_price,
       "total_items_selling_price" => total_items_selling_price,
       "total_ecommerce_discounts" => total_ecommerce_discounts,
       "ecommerce_items_discounts" => ecommerce_items_discounts
     }
     |> Map.new(fn
       {"ecommerce_items_discounts", v} ->
         {"ecommerce_items_discounts", v}

       {k, v} ->
         {k, v |> Decimal.round(2)}
     end), []}
  end

  defp calculate_cart_values(delivery_groups, discounts, _ecommerce_discounts, total_values) do
    fulfillable_items =
      delivery_groups
      |> Enum.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Enum.flat_map(fn group -> group["cart_items"] end)

    total_local_item_price =
      fulfillable_items
      |> Stream.reject(&(&1["avaliable_fulfillment_type"] != "LOCAL-IN-STORE"))
      |> Stream.map(fn item ->
        Decimal.new(item["total_price"])
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_list_price =
      fulfillable_items
      |> Stream.map(fn item ->
        Decimal.new(item["total_price"])
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_items_price =
      fulfillable_items
      |> Stream.map(fn item -> item["total_price"] end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_delivery_price =
      delivery_groups
      |> Stream.reject(&(&1["fulfillment_type"] == "unfulfillable"))
      |> Stream.reject(&(&1["fulfillment_type"] == "in-store"))
      |> Stream.map(fn group ->
        hd(group["cart_items"])["logistics_info"]["estimatedCost"]["amount"] || 0
      end)
      |> Enum.reduce(Decimal.new("0"), &Decimal.add(&1, &2))

    total_manual_discount =
      Decimal.min(
        Decimal.sub(
          total_items_price,
          Enum.reduce(discounts, total_items_price, fn
            %{"type" => type, "value" => value}, acc ->
              case type do
                "percentage" ->
                  Decimal.sub(acc, Decimal.mult(acc, Decimal.div(value, 100)))

                "fixed" ->
                  Decimal.sub(acc, value)

                _ ->
                  acc
              end
          end)
        ),
        total_items_price
      )

    total_price =
      Decimal.add(total_values["total_amount"], total_local_item_price)
      |> Decimal.add(total_delivery_price)

    {%{
       "total_price" => total_price,
       "total_items_list_price" => total_items_list_price,
       "total_manual_discount" =>
         Decimal.add(total_manual_discount, total_values["discounts_values"]),
       "total_delivery_price" => total_delivery_price,
       "total_items_selling_price" =>
         Decimal.add(total_values["total_amount"], total_local_item_price)
     }
     |> Map.new(fn {k, v} -> {k, v |> Decimal.round(2)} end), []}
  end

  defp get_simulation_discounts_codes(simulations) do
    {appliable_discounts, unappliable_discounts} =
      Enum.reduce(simulations, {[], []}, &reduce_simulations/2)

    codes_to_remove = Enum.map(appliable_discounts, fn %{code: code} -> code end)

    unappliable_discounts =
      unappliable_discounts
      |> Enum.uniq_by(fn %{code: code} -> code end)
      |> Enum.filter(fn %{code: code} -> code not in codes_to_remove end)

    {appliable_discounts, unappliable_discounts}
  end

  defp filter_discounts_target_types(
         {appliable_discounts, unappliable_discounts},
         ecommerce_items
       ) do
    if Enum.empty?(ecommerce_items) do
      filtered_appliable =
        Enum.reject(appliable_discounts, fn
          %{target_type: "SHIPPING_LINE"} -> true
          _ -> false
        end)

      {filtered_appliable, unappliable_discounts}
    else
      {appliable_discounts, unappliable_discounts}
    end
  end

  defp reduce_simulations(
         %{
           simulation: %{
             cart: %{
               "discountCodes" => discount_codes,
               "discountAllocations" => allocations
             }
           }
         },
         {appliable_discounts, unappliable_discounts}
       ) do
    Enum.reduce(discount_codes, {appliable_discounts, unappliable_discounts}, fn discount_code,
                                                                                 acc ->
      reduce_discount_codes(discount_code, allocations, acc)
    end)
    |> reduce_automatic_application(allocations)
  end

  defp reduce_simulations(
         _,
         {appliable_discounts, unappliable_discounts}
       ) do
    {appliable_discounts, unappliable_discounts}
  end

  defp reduce_discount_codes(
         %{"applicable" => false, "code" => code},
         _allocations,
         {appliable_discounts, unappliable_discounts}
       ) do
    {appliable_discounts, [%{code: code, value: "0"} | unappliable_discounts]}
  end

  defp reduce_discount_codes(
         %{"applicable" => true, "code" => code},
         [],
         {appliable_discounts, unappliable_discounts}
       ) do
    {[%{code: code, value: "0"} | appliable_discounts], unappliable_discounts}
  end

  defp reduce_discount_codes(
         %{"applicable" => true, "code" => code},
         allocations,
         {appliable_discounts, unappliable_discounts}
       ) do
    discounts =
      Enum.filter(allocations, fn discount ->
        String.downcase(discount["code"] || "") == String.downcase(code)
      end)
      |> Enum.reduce([], fn %{"discountedAmount" => %{"amount" => amount}}, acc ->
        [%{code: code, value: amount} | acc]
      end)

    {discounts ++ appliable_discounts, unappliable_discounts}
  end

  defp reduce_automatic_application({appliable_discounts, unappliable_discounts}, allocations) do
    Enum.reduce(allocations, {appliable_discounts, unappliable_discounts}, fn
      %{
        "discountedAmount" => %{"amount" => amount},
        "title" => code
      },
      {appliable_discounts, unappliable_discounts}
      when code not in [] ->
        {[%{code: code, value: amount, type: "automatic_ecommerce"}] ++ appliable_discounts,
         unappliable_discounts}

      _, {appliable_discounts, unappliable_discounts} ->
        {appliable_discounts, unappliable_discounts}
    end)
  end

  defp format_delivery_groups(fulfilliable, unfulfillable) do
    fulfilliable_cart_groups =
      fulfilliable
      |> Enum.group_by(fn item -> {item["fulfillment_type"], item["metadata"]["handle"]} end)
      |> Enum.map(fn {{fulfillment_type, _handle}, items} ->
        %{"fulfillment_type" => fulfillment_type, "pickup_point" => "", "cart_items" => items}
      end)

    case unfulfillable do
      [] ->
        fulfilliable_cart_groups

      _ ->
        [
          %{
            "fulfillment_type" => "unfulfillable",
            "pickup_point" => "",
            "cart_items" => unfulfillable
          }
          | fulfilliable_cart_groups
        ]
    end
  end

  defp format_delivery_messages([]) do
    []
  end

  defp format_delivery_messages(unfulfillable) do
    [
      %{
        "code" => "unfulfillable_items",
        "info" => %{
          "items" => unfulfillable
        },
        "message" => "Some items are not available for the selected fulfillment_type",
        "type" => "error"
      }
    ]
  end

  def format_invalid_discounts([]) do
    []
  end

  def format_invalid_discounts(discounts) do
    Enum.map(discounts, fn %{code: code, value: _} ->
      %{
        "code" => "coupon_not_found",
        "info" => %{
          "code" => code
        },
        "message" => "invalid discount code",
        "type" => "warning"
      }
    end)
  end

  defp format_removed_items({fulfilliable, unfulfillable}, removed_items, organization_id) do
    updated_unfulfillable =
      Enum.reduce(removed_items, unfulfillable, fn removed_item, acc ->
        variant =
          Rms.Commerce.Products.get_product_variant!(
            organization_id,
            removed_item["product_variant_id"]
          )

        item = %{
          "avaliable_fulfillment_type" => nil,
          "fulfillment_type" => removed_item[""],
          "item_index" => removed_item[""],
          "list_price" => variant.list_price,
          "logistics_info" => nil,
          "metadata" => nil,
          "original_metadata" => removed_item[""],
          "price" => Decimal.mult(variant.price, removed_item["quantity"]),
          "product_variant_id" => removed_item[""],
          "quantity" => removed_item["quantity"],
          "request_index" => 0,
          "selling_price" => variant.price,
          "total_price" => Decimal.mult(variant.price, removed_item["quantity"])
        }

        [item | acc]
      end)

    {fulfilliable, updated_unfulfillable}
  end

  defp format_local_items({fulfilliable, unfulfillable}, local_cart_items, organization_id) do
    updated_fulfilliable =
      Enum.reduce(local_cart_items, fulfilliable, fn local_cart_item, acc ->
        variant =
          Rms.Commerce.Products.get_product_variant!(
            organization_id,
            local_cart_item["product_variant_id"]
          )

        item = %{
          "avaliable_fulfillment_type" => "LOCAL-IN-STORE",
          "fulfillment_type" => local_cart_item["fulfillment_type"],
          "item_index" => local_cart_item["item_index"],
          "list_price" => variant.list_price,
          "logistics_info" => nil,
          "metadata" => nil,
          "original_metadata" => local_cart_item["metadata"],
          "price" => Decimal.mult(variant.price, local_cart_item["quantity"]),
          "product_variant_id" => local_cart_item["product_variant_id"],
          "quantity" => local_cart_item["quantity"],
          "request_index" => 0,
          "selling_price" => variant.price,
          "total_price" => Decimal.mult(variant.price, local_cart_item["quantity"])
        }

        [item | acc]
      end)

    {updated_fulfilliable, unfulfillable}
  end

  defp format_response(simulations, organization_id) do
    Enum.reduce(simulations, {[], []}, fn
      %{fulfillment_type: "in-store"} = simulation, {fulfillable, unfulfillable} ->
        {fulfillable_items, unfulfillable_items} =
          build_shopify_items(organization_id, "in-store", simulation)

        {fulfillable_items ++ fulfillable, unfulfillable_items ++ unfulfillable}

      %{simulation: %{delivery: nil}} = simulation, {fulfillable, unfulfillable} ->
        {fulfillable_items, unfulfillable_items} =
          build_shopify_items(organization_id, "unfulfillable", simulation)

        {fulfillable_items ++ fulfillable, unfulfillable_items ++ unfulfillable}

      %{simulation: %{delivery: %{"deliveryOptions" => []}}} = simulation,
      {fulfillable, unfulfillable} ->
        {fulfillable_items, unfulfillable_items} =
          build_shopify_items(organization_id, "unfulfillable", simulation)

        {fulfillable_items ++ fulfillable, unfulfillable_items ++ unfulfillable}

      simulation, {fulfillable, unfulfillable} ->
        {fulfillable_items, unfulfillable_items} =
          build_shopify_items(organization_id, simulation.fulfillment_type, simulation)

        {fulfillable_items ++ fulfillable, unfulfillable_items ++ unfulfillable}
    end)
  end

  defp build_shopify_items(organization_id, "in-store", simulation) do
    get_simulation_items(organization_id, "in-store", simulation, nil)
  end

  defp build_shopify_items(organization_id, "delivery", simulation) do
    logistic_info = get_logistic_info(simulation)
    get_simulation_items(organization_id, "delivery", simulation, logistic_info)
  end

  defp build_shopify_items(organization_id, _, simulation) do
    get_simulation_items(organization_id, "unfulfillable", simulation, nil)
  end

  defp get_logistic_info(%{
         fulfillment_type: "delivery",
         simulation: %{delivery: %{"deliveryOptions" => delivery_options}}
       }) do
    delivery_options
    |> Enum.filter(fn delivery_option ->
      delivery_option["deliveryMethodType"] in @delivery_types
    end)
    |> Enum.sort(fn a, b ->
      Decimal.compare(
        Decimal.new(a["estimatedCost"]["amount"] || Decimal.new("0")),
        Decimal.new(b["estimatedCost"]["amount"] || Decimal.new("0"))
      ) == :lt
    end)
    |> hd()
  end

  defp get_logistic_info(%{
         fulfillment_type: "local-pickup",
         simulation: %{delivery: %{"deliveryOptions" => delivery_options}}
       }) do
    delivery_options
    |> Enum.filter(fn delivery_option ->
      delivery_option["deliveryMethodType"] in @pickup_types
    end)
    |> Enum.sort(fn a, b ->
      Decimal.compare(
        Decimal.new(a["estimatedCost"]["amount"]),
        Decimal.new(b["estimatedCost"]["amount"])
      ) == :lt
    end)
    |> hd()
  end

  defp get_logistic_info(_) do
    nil
  end

  # Função para encontrar o item do Shopify
  defp find_shopify_item(edges, external_id) do
    Enum.find(edges, fn %{"node" => %{"merchandise" => merchandise}} ->
      merchandise["id"] == external_id
    end)
  end

  # Função para lidar com o item do Shopify encontrado
  defp handle_shopify_item(
         shopify_item,
         local_cart_item,
         variant,
         logistic_info,
         fulfillable,
         unfulfillable
       ) do
    %{
      "node" => %{
        "discountAllocations" => discount_alocations,
        "cost" => cost,
        "quantity" => shopify_quantity
      }
    } = shopify_item

    # Pegue a quantidade de local_cart_item antes de passar para a função
    local_quantity = local_cart_item["quantity"]

    item =
      build_item_based_on_quantity(
        shopify_quantity,
        discount_alocations,
        local_quantity,
        local_cart_item,
        variant,
        cost,
        logistic_info
      )

    {[item | fulfillable], unfulfillable}
  end

  # Função para construir o item baseado na quantidade, sem usar guards
  defp build_item_based_on_quantity(
         0,
         _discount_alocations,
         _local_quantity,
         local_cart_item,
         variant,
         _cost,
         _logistic_info
       ) do
    build_fulfillment_item(local_cart_item, variant)
  end

  defp build_item_based_on_quantity(
         shopify_quantity,
         _discount_alocations,
         local_quantity,
         local_cart_item,
         variant,
         _cost,
         _logistic_info
       )
       when shopify_quantity < local_quantity do
    build_fulfillment_item(local_cart_item, variant)
  end

  defp build_item_based_on_quantity(
         _shopify_quantity,
         discount_alocations,
         _local_quantity,
         local_cart_item,
         _variant,
         cost,
         logistic_info
       ) do
    %{
      "avaliable_fulfillment_type" => local_cart_item["fulfillment_type"],
      "fulfillment_type" => local_cart_item["fulfillment_type"],
      "item_index" => local_cart_item["item_index"],
      "list_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
      "logistics_info" => nil,
      "metadata" => logistic_info,
      "original_metadata" => local_cart_item["metadata"],
      "price" => Decimal.new(cost["totalAmount"]["amount"]),
      "product_variant_id" => local_cart_item["product_variant_id"],
      "quantity" => local_cart_item["quantity"],
      "request_index" => 0,
      "selling_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
      "total_price" => Decimal.new(cost["totalAmount"]["amount"]),
      "ecommerce_discounts" => reduce_automatic_items_application(discount_alocations)
    }
  end

  defp build_fulfillment_item(local_cart_item, variant) do
    %{
      "avaliable_fulfillment_type" => local_cart_item["fulfillment_type"],
      "fulfillment_type" => local_cart_item["fulfillment_type"],
      "item_index" => local_cart_item["item_index"],
      "list_price" => variant.list_price,
      "logistics_info" => nil,
      "metadata" => nil,
      "original_metadata" => local_cart_item["metadata"],
      "price" => Decimal.mult(variant.price, local_cart_item["quantity"]),
      "product_variant_id" => local_cart_item["product_variant_id"],
      "quantity" => local_cart_item["quantity"],
      "request_index" => 0,
      "selling_price" => variant.price,
      "total_price" => Decimal.mult(variant.price, local_cart_item["quantity"])
    }
  end

  defp reduce_automatic_items_application(allocations) do
    Enum.reduce(allocations, [], fn
      %{"title" => code}, acc ->
        [%{code: code, type: "automatic_ecommerce"} | acc]

      _, acc ->
        acc
    end)
  end

  defp get_simulation_items(
         organization_id,
         "in-store",
         %{
           simulation: %{
             cart: %{
               "lines" => %{
                 "edges" => edges
               }
             }
           }
         } = simulation,
         logistic_info
       ) do
    Enum.reduce(simulation.items, {[], []}, fn local_cart_item, {fulfillable, unfulfillable} ->
      shopify_item = find_shopify_item(edges, local_cart_item["external_id"])

      variant =
        Rms.Commerce.Products.get_product_variant!(
          organization_id,
          local_cart_item["product_variant_id"]
        )

      if shopify_item do
        handle_shopify_item(
          shopify_item,
          local_cart_item,
          variant,
          logistic_info,
          fulfillable,
          unfulfillable
        )
      else
        item = build_fulfillment_item(local_cart_item, variant)
        {[item | fulfillable], unfulfillable}
      end
    end)
  end

  defp get_simulation_items(
         organization_id,
         "in-store",
         simulation,
         _logistic_info
       ) do
    Enum.reduce(simulation.items, {[], []}, fn
      local_cart_item, {fulfillable, unfulfillable} ->
        variant =
          Rms.Commerce.Products.get_product_variant!(
            organization_id,
            local_cart_item["product_variant_id"]
          )

        item = %{
          "avaliable_fulfillment_type" => local_cart_item["fulfillment_type"],
          "fulfillment_type" => local_cart_item["fulfillment_type"],
          "item_index" => local_cart_item["item_index"],
          "list_price" => variant.list_price,
          "logistics_info" => nil,
          "metadata" => nil,
          "original_metadata" => local_cart_item["metadata"],
          "price" => Decimal.mult(variant.price, local_cart_item["quantity"]),
          "product_variant_id" => local_cart_item["product_variant_id"],
          "quantity" => local_cart_item["quantity"],
          "request_index" => 0,
          "selling_price" => variant.price,
          "total_price" => Decimal.mult(variant.price, local_cart_item["quantity"])
        }

        {[item | fulfillable], unfulfillable}
    end)
  end

  defp get_simulation_items(
         _organization_id,
         "delivery",
         %{
           simulation: %{
             cart: %{
               "lines" => %{
                 "edges" => edges
               }
             }
           }
         } = simulation,
         logistic_info
       ) do
    Enum.reduce(edges, {[], []}, fn
      %{"node" => %{"cost" => cost, "merchandise" => merchandise, "quantity" => 0}},
      {fulfillable, unfulfillable} ->
        variant =
          Enum.find(simulation.items, fn item -> item["external_id"] == merchandise["id"] end)

        item =
          %{
            "avaliable_fulfillment_type" => nil,
            "fulfillment_type" => simulation.fulfillment_type,
            "item_index" => variant["item_index"],
            "list_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
            "logistics_info" => nil,
            "metadata" => nil,
            "original_metadata" => variant["metadata"],
            "price" => Decimal.new(cost["totalAmount"]["amount"]),
            "product_variant_id" => variant["product_variant_id"],
            "quantity" => 0,
            "request_index" => 0,
            "selling_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
            "total_price" => Decimal.new(cost["totalAmount"]["amount"])
          }

        {fulfillable, [item | unfulfillable]}

      %{"node" => %{"cost" => cost, "merchandise" => merchandise, "quantity" => quantity}},
      {fulfillable, unfulfillable} ->
        variant =
          Enum.find(simulation.items, fn item -> item["external_id"] == merchandise["id"] end)

        item =
          %{
            "avaliable_fulfillment_type" => simulation.fulfillment_type,
            "fulfillment_type" => simulation.fulfillment_type,
            "item_index" => variant["item_index"],
            "list_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
            "logistics_info" => logistic_info,
            "metadata" => logistic_info,
            "original_metadata" => variant["metadata"],
            "price" => Decimal.new(cost["totalAmount"]["amount"]),
            "product_variant_id" => variant["product_variant_id"],
            "quantity" => quantity,
            "request_index" => 0,
            "selling_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
            "total_price" => Decimal.new(cost["totalAmount"]["amount"])
          }

        {[item | fulfillable], unfulfillable}
    end)
  end

  defp get_simulation_items(
         _organization_id,
         "unfulfillable",
         %{
           simulation: %{
             cart: %{
               "lines" => %{
                 "edges" => edges
               }
             }
           }
         } = simulation,
         _logistic_info
       ) do
    Enum.reduce(edges, {[], []}, fn %{
                                      "node" => %{
                                        "cost" => cost,
                                        "merchandise" => merchandise,
                                        "quantity" => _
                                      }
                                    },
                                    {fulfillable, unfulfillable} ->
      variant =
        Enum.find(simulation.items, fn item -> item["external_id"] == merchandise["id"] end)

      item =
        %{
          "avaliable_fulfillment_type" => nil,
          "fulfillment_type" => simulation.fulfillment_type,
          "item_index" => variant["item_index"],
          "list_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
          "logistics_info" => nil,
          "metadata" => nil,
          "original_metadata" => variant["metadata"],
          "price" => Decimal.new(cost["totalAmount"]["amount"]),
          "product_variant_id" => variant["product_variant_id"],
          "quantity" => variant["quantity"],
          "request_index" => 0,
          "selling_price" => Decimal.new(cost["amountPerQuantity"]["amount"]),
          "total_price" => Decimal.new(cost["totalAmount"]["amount"])
        }

      {fulfillable, [item | unfulfillable]}
    end)
  end

  defp get_simulation_items(
         organization_id,
         _delivery_type,
         simulation,
         _logistic_info
       ) do
    Enum.reduce(simulation.items, {[], []}, fn local_cart_item, {fulfillable, unfulfillable} ->
      variant =
        Rms.Commerce.Products.get_product_variant!(
          organization_id,
          local_cart_item["product_variant_id"]
        )

      item = %{
        "avaliable_fulfillment_type" => nil,
        "fulfillment_type" => local_cart_item["fulfillment_type"],
        "item_index" => local_cart_item["item_index"],
        "list_price" => variant.list_price,
        "logistics_info" => nil,
        "metadata" => nil,
        "original_metadata" => local_cart_item["metadata"],
        "price" => Decimal.mult(variant.price, local_cart_item["quantity"]),
        "product_variant_id" => local_cart_item["product_variant_id"],
        "quantity" => local_cart_item["quantity"],
        "request_index" => 0,
        "selling_price" => variant.price,
        "total_price" => Decimal.mult(variant.price, local_cart_item["quantity"])
      }

      {fulfillable, [item | unfulfillable]}
    end)
  end

  defp filter_items(cart_info, external_id_map) do
    cart_info["items"]
    |> Enum.with_index(fn item, index -> Map.put(item, "item_index", index) end)
    |> Enum.reduce({[], [], [], []}, fn item, {local_acc, present_acc, other_acc, removed_acc} ->
      cond do
        item["fulfillment_type"] == "in-store" and
            not Map.has_key?(external_id_map, item["product_variant_id"]) ->
          {[item | local_acc], present_acc, other_acc, removed_acc}

        item["fulfillment_type"] == "in-store" ->
          external_id = external_id_map[item["product_variant_id"]]

          {local_acc, [Map.put(item, "external_id", external_id) | present_acc], other_acc,
           removed_acc}

        item["fulfillment_type"] == "delivery" and
            not Map.has_key?(external_id_map, item["product_variant_id"]) ->
          {local_acc, present_acc, other_acc, [item | removed_acc]}

        item["fulfillment_type"] == "delivery" ->
          external_id = external_id_map[item["product_variant_id"]]

          {local_acc, present_acc, [Map.put(item, "external_id", external_id) | other_acc],
           removed_acc}

        item ->
          {local_acc, present_acc, other_acc, [item | removed_acc]}
      end
    end)
  end

  def get_total_values([
        %{
          simulation: %{
            cart: %{
              "cost" => %{
                "checkoutChargeAmount" => %{
                  "amount" => charge_amount
                },
                "subtotalAmount" => %{"amount" => subtotal_amount},
                "totalAmount" => %{"amount" => total_amount}
              }
            },
            message: nil,
            delivery: %{
              "selectedDeliveryOption" => %{
                "estimatedCost" => %{"amount" => value}
              }
            }
          }
        }
      ]) do
    acc = %{
      "subtotal_amount" => Decimal.new("0"),
      "total_amount" => Decimal.new("0"),
      "checkout_charge_amount" => Decimal.new("0"),
      "discounts_values" => Decimal.new("0")
    }

    charge_amount = Decimal.new(charge_amount)
    subtotal_amount = Decimal.new(subtotal_amount)
    total_amount = Decimal.sub(Decimal.new(total_amount), Decimal.new(value))

    discounts_values =
      Decimal.sub(Decimal.max(subtotal_amount, charge_amount), total_amount)
      |> Decimal.max(Decimal.new("0"))

    acc =
      acc
      |> Map.put("subtotal_amount", Decimal.add(subtotal_amount, acc["subtotal_amount"]))
      |> Map.put("total_amount", Decimal.add(total_amount, acc["total_amount"]))
      |> Map.put(
        "checkout_charge_amount",
        Decimal.add(charge_amount, acc["checkout_charge_amount"])
      )
      |> Map.put("discounts_values", Decimal.add(discounts_values, acc["discounts_values"]))

    {:ok, acc}
  end

  def get_total_values([
        %{
          simulation: %{
            cart: %{
              "cost" => %{
                "checkoutChargeAmount" => %{
                  "amount" => charge_amount
                },
                "subtotalAmount" => %{"amount" => subtotal_amount},
                "totalAmount" => %{"amount" => total_amount}
              }
            },
            message: nil,
            delivery: _
          }
        }
      ]) do
    acc = %{
      "subtotal_amount" => Decimal.new("0"),
      "total_amount" => Decimal.new("0"),
      "checkout_charge_amount" => Decimal.new("0"),
      "discounts_values" => Decimal.new("0")
    }

    charge_amount = Decimal.new(charge_amount)
    subtotal_amount = Decimal.new(subtotal_amount)
    total_amount = Decimal.sub(Decimal.new(total_amount), Decimal.new("0"))

    discounts_values =
      Decimal.sub(Decimal.max(subtotal_amount, charge_amount), total_amount)
      |> Decimal.max(Decimal.new("0"))

    acc =
      acc
      |> Map.put("subtotal_amount", Decimal.add(subtotal_amount, acc["subtotal_amount"]))
      |> Map.put("total_amount", Decimal.add(total_amount, acc["total_amount"]))
      |> Map.put(
        "checkout_charge_amount",
        Decimal.add(charge_amount, acc["checkout_charge_amount"])
      )
      |> Map.put("discounts_values", Decimal.add(discounts_values, acc["discounts_values"]))

    {:ok, acc}
  end

  def get_total_values(_) do
    {:error, "invalid total simulation"}
  end

  def build_ecommerce_discounts(discounts) when is_list(discounts) do
    discounts
    |> Enum.filter(fn discount -> discount[:type] == "automatic_ecommerce" end)
    |> Enum.map(fn discount ->
      %{"type" => discount.type, "value" => discount.code, "description" => ""}
    end)
  end

  def build_ecommerce_discounts(_discounts) do
    []
  end

  def build_simulate_cart_info(fulfillment) do
    %{
      "location_id" => fulfillment.order.location_id,
      "staff_id" => fulfillment.order.staff_id,
      "items" =>
        fulfillment.line_items
        |> Enum.filter(&(!&1.is_gift))
        |> Enum.map(fn li ->
          %{
            "quantity" => li.quantity,
            "product_variant_id" => li.product_variant_id,
            "fulfillment_type" => fulfillment.shipping_method,
            "discounts" =>
              Enum.map(li.discounts, fn d -> %{"type" => d.type, "value" => d.value} end)
          }
        end),
      "customer_id" => fulfillment.order.customer_id,
      "country" => "BRA",
      "postal_code" => "1000000",
      "discounts" =>
        Enum.map(fulfillment.order.discounts, fn d ->
          %{"type" => d.type, "value" => d.value}
        end)
    }
  end
end
