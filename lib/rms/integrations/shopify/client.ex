defmodule Rms.Integrations.Shopify.Client do
  @behaviour Rms.Integrations.Shopify.ClientBehaviour

  require Logger

  alias __MODULE__.GraphQL
  alias Rms.Integrations.Shopify.Config

  @default_admin_api_version "2024-01"
  @default_storefront_api_version "2024-07"
  @admin_graphql_api_path "admin/api/$version/graphql.json"
  @storefront_graphql_api_path "api/$version/graphql"

  @admin_api_version_2024_10 Config.get_api_admin_api_version_v2024_10()

  @admin_api_version_2025_04 Config.get_api_admin_api_version_v2025_04()

  @order_query """
  query getOrder($id: ID!) {
    order(id: $id) {
      $selection
    }
  }
  """

  @doc """
  Get an Order by ID.

  ## Examples

      iex> get_order(123)
      {:ok, %{}}

      iex> get_order(456)
      {:error, error_reason}

  """
  def get_order!(client, id, selection, opts \\ []) do
    opts = Keyword.merge(opts, operation_name: "getOrder", variables: %{id: id})
    query = String.replace(@order_query, "$selection", selection)
    post!(client, query, opts)
  end

  @find_orders_query """
  query {
    orders($filters) {
      nodes {
          id
          cancelledAt
          createdAt
          returnStatus
          displayFinancialStatus
          displayFulfillmentStatus
          currentTotalPriceSet {
            shopMoney {
              amount
            }
          }
          customer {
              email
              displayName
              phone
          }
          name
          retailLocation {
            id
            name
          }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
  """
  def find_orders(client, filters \\ []) do
    filters = normalize_filters(filters)
    query = String.replace(@find_orders_query, "$filters", Enum.join(filters, ", "))

    with {:ok, response} <- post!(client, query, api_version: "2025-01") do
      page_info = get_in(response, ["data", "orders", "pageInfo"])
      cursor = if page_info["hasNextPage"], do: page_info["endCursor"]

      result = %{
        orders: get_in(response, ["data", "orders", "nodes"]),
        last_cursor: cursor
      }

      {:ok, result}
    end
  end

  @mark_as_paid_mutation """
  mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
    orderMarkAsPaid(input: $input) {
      order {
        id
        displayFinancialStatus
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Marks an order as paid.

  ## Examples

      iex> mark_order_as_paid(client, "gid://shopify/Order/123")
      {:ok, %{}}

      iex> mark_order_as_paid(client, "gid://shopify/Order/456")
      {:error, error_reason}

  """
  def mark_order_as_paid(client, id) do
    post!(client, @mark_as_paid_mutation,
      operation_name: "orderMarkAsPaid",
      variables: %{input: %{id: id}}
    )
  end

  @return_create_mutation """
  mutation returnCreate($returnInput: ReturnInput!) {
    returnCreate(returnInput: $returnInput) {
      return {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @returnable_fulfillments_query """
  query ReturnableFulfillments($orderId: ID!) {
    returnableFulfillments(orderId: $orderId, first: 50) {
      edges {
        node {
        id
          fulfillment {
            id
            displayStatus
          }
          returnableFulfillmentLineItems(first: 50) {
            edges {
              node {
                fulfillmentLineItem {
                  id
                  quantity
                  lineItem {
                    id
                    sku
                    title
                    quantity
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  """

  @doc """
  Queries returnable fulfillments for an order.

  ## Examples

      iex> returnable_fulfillments(client, "gid://shopify/Order/123")
      {:ok, [%{"id" => "gid://shopify/ReturnableFulfillmentLineItem/123", "lineItem" => %{"id" => "gid://shopify/LineItem/456", "sku" => "SKU123"}}]}

      iex> returnable_fulfillments(client, "invalid_id")
      {:error, error_reason}

  """
  def returnable_fulfillments(client, order_id) do
    variables = %{orderId: order_id}

    result =
      post!(client, @returnable_fulfillments_query,
        operation_name: "ReturnableFulfillments",
        variables: variables,
        api_version: "2025-01"
      )

    handle_returnable_fulfillments_response(result)
  end

  defp handle_returnable_fulfillments_response(
         {:ok, %{"data" => %{"returnableFulfillments" => %{"edges" => edges}}}}
       ) do
    returnable_items = extract_returnable_items(edges)
    {:ok, returnable_items}
  end

  defp handle_returnable_fulfillments_response({:ok, %{"errors" => errors}}) do
    {:error, "shopify api error: #{inspect(errors)}"}
  end

  defp handle_returnable_fulfillments_response({:error, reason}) do
    {:error, "shopify api request failed: #{inspect(reason)}"}
  end

  defp extract_returnable_items(edges) do
    Enum.flat_map(edges, &extract_line_items_from_node/1)
  end

  defp extract_line_items_from_node(%{"node" => node}) do
    get_in(node, [
      "returnableFulfillmentLineItems",
      "edges",
      Access.all(),
      "node",
      "fulfillmentLineItem"
    ])
  end

  @doc """
  Creates a return in Shopify.

  ## Examples

      iex> return_create(client, %{orderId: "gid://shopify/Order/123", returnLineItems: [...]})
      {:ok, "gid://shopify/Return/123"}

      iex> return_create(client, %{})
      {:error, error_reason}

  """
  def return_create(client, return_input) do
    variables = %{returnInput: return_input}

    case post!(client, @return_create_mutation,
           operation_name: "returnCreate",
           variables: variables,
           api_version: "2025-01"
         ) do
      {:ok, %{"data" => %{"returnCreate" => response}}} ->
        handle_return_create_response(response)

      {:ok, %{"errors" => errors}} ->
        {:error, "shopify api error: #{inspect(errors)}"}

      {:error, reason} ->
        {:error, "shopify api request failed: #{inspect(reason)}"}
    end
  end

  defp handle_return_create_response(%{"userErrors" => user_errors})
       when length(user_errors) > 0 do
    error_messages = Enum.map_join(user_errors, ", ", fn %{"message" => message} -> message end)
    {:error, "shopify return creation failed: #{error_messages}"}
  end

  defp handle_return_create_response(%{"return" => %{"id" => return_id}}) do
    {:ok, return_id}
  end

  defp handle_return_create_response(response) do
    {:error, "unexpected response from shopify: #{inspect(response)}"}
  end

  @create_product_feed_mutation """
  mutation CreateProductFeed($input: ProductFeedInput!) {
    productFeedCreate(input: $input) {
      productFeed {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Creates a product feed.

  ## Examples

      iex> create_product_feed(client, input)
      {:ok, %{}}

      iex> create_product_feed(client, input)
      {:error, error_reason}

  """
  def create_product_feed(client, input) do
    post!(client, @create_product_feed_mutation,
      operation_name: "CreateProductFeed",
      variables: %{input: input}
    )
  end

  @create_webhook_mutation """
  mutation CreateWebhook($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
    webhookSubscriptionCreate(
        topic: $topic
        webhookSubscription: $webhookSubscription
    ) {
        userErrors {
            field
            message
        }
    }
  }
  """

  @doc """
  Creates a webhook.

  ## Examples
      iex> create_webhook(client, "ORDERS_CREATE", %{callbackUrl: "https://example.com/webhook"})
      {:ok, %{}}

      iex> create_webhook(client, "ORDERS_CREATE", %{callbackUrl: "https://example.com/webhook"})
      {:error, error_reason}
  """
  def create_webhook(client, topic, webhook_subscription) do
    variables = %{topic: topic, webhookSubscription: webhook_subscription}
    post!(client, @create_webhook_mutation, operation_name: "CreateWebhook", variables: variables)
  end

  @product_feeds_query """
  query ProductFeeds {
    productFeeds(first: 10) {
      nodes {
        country
        language
        status
        id
      }
    }
  }
  """

  @doc """
  Fetches the first 10 product feeds.

  ## Examples

      iex> get_product_feeds(client)
      {:ok, %{}}

      iex> get_product_feeds(client)
      {:error, error_reason}

  """
  def get_product_feeds(client) do
    post!(client, @product_feeds_query, operation_name: "ProductFeeds")
  end

  @product_full_sync_mutation """
  mutation ProductFullSync($id: ID!) {
    productFullSync(id: $id) {
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Runs a full sync for a product.

  ## Examples

      iex> run_product_full_sync(client, "gid://shopify/ProductFeed/123")
      {:ok, %{}}

      iex> run_product_full_sync(client, "gid://shopify/ProductFeed/456")
      {:error, error_reason}

  """
  def run_product_full_sync(client, id) do
    post!(client, @product_full_sync_mutation,
      operation_name: "ProductFullSync",
      variables: %{id: id}
    )
  end

  @doc """
  Creates a new draft order with given input.

  ## Examples

      iex> create_draft_order(client, %{purchasingEntity: %{customerId: "gid://shopify/Customer/123"}, lineItems: [...]})
      {:ok, %{id: "gid://shopify/DraftOrder/123", "ready" => true}}

      iex> create_draft_order(client, %{})
      {:error, error_reason}

  """
  def create_draft_order(client, input) do
    variables = %{input: input}
    mutation = GraphQL.mutation_draft_order_create()

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "draftOrderCreate",
             variables: variables,
             api_version: "2025-04"
           ),
         {:ok, %{"draftOrder" => data}} <- handle_user_errors(response, "draftOrderCreate") do
      {:ok, data}
    end
  end

  def new_create_draft_order(client, input) do
    variables = %{input: input}

    response =
      post!(
        client,
        GraphQL.mutation_draft_order_create(),
        operation_name: "draftOrderCreate",
        variables: variables,
        api_version: "2025-04"
      )

    with {:ok, %{"data" => response}} <- response,
         {:ok, %{"draftOrder" => data}} <- handle_user_errors(response, "draftOrderCreate") do
      {:ok, data}
    end
  end

  @doc """
  Calculates the properties of a draft order without creating it.

  ## Examples

      iex> calculate_draft_order(client, %{purchasingEntity: %{customerId: "gid://shopify/Customer/123"}, lineItems: [...]})
      {:ok, %{"customer" => %{"id" => "gid://shopify/Customer/123"}, "totalPriceSet" => %{...}}}

      iex> calculate_draft_order(client, %{})
      {:error, error_reason}

  """
  def calculate_draft_order(client, input) do
    variables = %{input: input}
    mutation = GraphQL.mutation_draft_order_calculate()

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "draftOrderCalculate",
             variables: variables,
             api_version: "2025-04"
           ),
         {:ok, %{"calculatedDraftOrder" => data}} <-
           handle_user_errors(response, "draftOrderCalculate") do
      {:ok, data}
    end
  end

  @draft_order_query """
  query draftOrder($id: ID!) {
    draftOrder(id: $id) {
      id
      ready
      status
      invoiceUrl
      order {
        id
        name
        fulfillmentOrders(first: 250) {
          nodes {
            id
            lineItems(first: 250) {
              nodes {
                id
                totalQuantity
              }
            }
          }
        }
      }
    }
  }
  """

  @doc """
  Gets a draft order with given ID.

  ## Examples

      iex> fetch_draft_order(client, "gid://shopify/DraftOrder/123")
      {:ok, %{id: "gid://shopify/DraftOrder/123", "ready" => true, "status" => "OPEN"}}

      iex> fetch_draft_order(client, id)
      {:error, error_reason}

  """
  def fetch_draft_order(client, id) do
    with {:ok, response} <-
           post!(client, @draft_order_query, operation_name: "draftOrder", variables: %{id: id}) do
      {:ok, get_in(response, ["data", "draftOrder"])}
    end
  end

  @location_query """
  query Locations($first: Int!, $query: String!) {
    locations(first: $first, query: $query) {
        nodes {
            address {
                address1
                address2
                city
                country
                countryCode
                formatted
                latitude
                longitude
                phone
                province
                provinceCode
                zip
            }
            isActive
            name
            id
        }
    }
  }
  """

  @doc """
  Gets a location with a given query.

  ## Examples

      iex> fetch_location(client, "gid://shopify/DraftOrder/123")
      {:ok, %{id: "gid://shopify/DraftOrder/123", "ready" => true, "status" => "OPEN"}}

      iex> fetch_location(client, id)
      {:error, error_reason}

  """
  def fetch_location(client, query) do
    with {:ok, response} <-
           post!(client, @location_query,
             operation_name: "Locations",
             variables: %{first: 1, query: query}
           ) do
      {:ok, get_in(response, ["data", "locations"])}
    end
  end

  @mark_as_completed_mutation """
  mutation draftOrderComplete($id: ID!) {
    draftOrderComplete(id: $id) {
      draftOrder {
        id
        ready
        status
        order {
          id
          name
          fulfillmentOrders(first: 250) {
            nodes {
              id
              lineItems(first: 250) {
                nodes {
                  id
                  totalQuantity
                }
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Marks a draft order as completed.

  ## Examples

      iex> mark_order_as_completed(client, "gid://shopify/DraftOrder/123")
      {:ok, %{id: "gid://shopify/DraftOrder/123", "status" => "COMPLETED", "ready" => true, "order" => %{"id" => "gid://shopify/Order/123"}}}

      iex> mark_order_as_completed(client, id)
      {:error, error_reason}

  """
  def mark_order_as_completed(client, id) do
    mutation = @mark_as_completed_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation, operation_name: "draftOrderComplete", variables: %{id: id}),
         {:ok, %{"draftOrder" => data}} <- handle_user_errors(response, "draftOrderComplete") do
      {:ok, data}
    end
  end

  @move_fulfillment_mutation """
  mutation FulfillmentOrderMove($fulfillmentId: ID!, $locationId: ID!) {
    fulfillmentOrderMove(id: $fulfillmentId, newLocationId: $locationId) {
      userErrors {
        field
        message
      }
      movedFulfillmentOrder {
          channelId
          createdAt
          fulfillAt
          fulfillBy
          id
          orderId
          orderName
          orderProcessedAt
          requestStatus
          status
          updatedAt
      }
    }
  }
  """

  def move_fulfillment(client, fulfillment_id, location_id) do
    mutation = @move_fulfillment_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "FulfillmentOrderMove",
             variables: %{
               fulfillmentId: fulfillment_id,
               locationId: location_id
             }
           ),
         {:ok, %{"fulfillmentOrderMove" => data}} <-
           handle_user_errors(response, "fulfillmentOrderMove") do
      {:ok, data}
    end
  end

  @fulfill_order_mutation """
    mutation FulfillmentCreateV2($fulfillment: FulfillmentV2Input!) {
    fulfillmentCreateV2(fulfillment: $fulfillment) {
      userErrors {
        field
        message
      }
      fulfillment {
          createdAt
          deliveredAt
          displayStatus
          estimatedDeliveryAt
          id
          inTransitAt
          legacyResourceId
          name
          requiresShipping
          status
          totalQuantity
          updatedAt
      }
    }
  }
  """

  def fulfill_order(client, input) do
    variables = %{fulfillment: input}
    mutation = @fulfill_order_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation, operation_name: "FulfillmentCreateV2", variables: variables),
         {:ok, %{"fulfillmentCreateV2" => data}} <-
           handle_user_errors(response, "fulfillmentCreateV2") do
      {:ok, data}
    end
  end

  @create_customer_mutation """
  mutation customerCreate($input: CustomerInput!) {
    customerCreate(input: $input) {
      userErrors {
        field
        message
      }
      customer {
        id
      }
    }
  }
  """

  @doc """
  Creates a new customer with given input.

  ## Examples

      iex> create_customer(client, %{email: "<EMAIL>", phone: "...", firstName: "...", lastName: "..."})
      {:ok, %{"id" => "gid://shopify/Customer/123"}}

      iex> create_customer(client, %{})
      {:error, error_reason}

  """
  def create_customer(client, input) do
    variables = %{input: input}
    mutation = @create_customer_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation, operation_name: "customerCreate", variables: variables),
         {:ok, %{"customer" => data}} <- handle_user_errors(response, "customerCreate") do
      {:ok, data}
    end
  end

  @doc """
  Updates an existing customer with given input.

  ## Examples

      iex> update_customer(client, %{id: "gid://shopify/Customer/123", email: nil})
      {:ok, %{"id" => "gid://shopify/Customer/123", "email" => nil}}

      iex> update_customer(client, %{})
      {:error, error_reason}

  """
  @update_customer_mutation """
  mutation updateCustomerMetafields($input: CustomerInput!) {
    customerUpdate(input: $input) {
      customer {
        id
        email
        phone
        firstName
        lastName
        addresses {
          id
          address1
          address2
          city
          country
          provinceCode
          countryCode
          zip
        }
      }
      userErrors {
        message
        field
      }
    }
  }
  """

  def update_customer(client, input) do
    variables = %{input: input}
    mutation = @update_customer_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "updateCustomerMetafields",
             variables: variables
           ),
         {:ok, %{"customer" => data}} <- handle_user_errors(response, "customerUpdate") do
      {:ok, data}
    end
  end

  @customers_query """
  query {
    customers($filters) {
      edges {
        node {
          id
          email
          phone
          firstName
          lastName
          defaultAddress {
            name
            address1
            address2
            city
            country
            provinceCode
            company
            zip
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
  """

  @customer_query """
  query customer($id: ID!) {
    customer(id: $id) {
      id
      firstName
      lastName
      email
      phone
      numberOfOrders
      amountSpent {
        amount
        currencyCode
      }
      createdAt
      updatedAt
      note
      verifiedEmail
      validEmailAddress
      tags
      lifetimeDuration
      defaultAddress {
        formattedArea
        address1
      }
      addresses {
        address1
      }
      image {
        src
      }
      canDelete
    }
  }
  """

  @doc """
  Gets a customer by ID.

  ## Examples

      iex> fetch_customer(client, "gid://shopify/Customer/*********")
      {:ok, %{"id" => "gid://shopify/Customer/*********", "email" => "<EMAIL>", ...}}

      iex> fetch_customer(client, "invalid_id")
      {:error, error_reason}

  """
  def fetch_customer(client, id) do
    with {:ok, %{"data" => %{"customer" => customer}}} <-
           post!(client, @customer_query, operation_name: "customer", variables: %{id: id}) do
      {:ok, customer}
    end
  end

  @doc """
  Gets a list of customers with given filter.

  ## Examples

      iex> fetch_customers(client, query: "id:123")
      {:ok, %{cursor: "eyJsYXN0X2lk...", items: [%{"id" => "gid://shopify/Customer/123", "email" => "<EMAIL>", ...}]}}

      iex> fetch_customers(client, query: "country:Brazil")
      {:ok, %{cursor: "eyJsYXN0X2lk...", items: [%{"id" => "gid://shopify/Customer/123", "email" => "<EMAIL>", ...}, ...]}}

      iex> fetch_customers(client, query: 123)
      {:error, error_reason}

  """

  def fetch_customers(client, filters \\ []) do
    filters = normalize_filters(filters)
    query = String.replace(@customers_query, "$filters", Enum.join(filters, ", "))

    with {:ok, response} <- post!(client, query) do
      page_info = get_in(response, ["data", "customers", "pageInfo"])
      cursor = if page_info["hasNextPage"], do: page_info["endCursor"]

      result = %{
        items: get_in(response, ["data", "customers", "edges", Access.all(), "node"]),
        cursor: cursor
      }

      {:ok, result}
    end
  end

  @create_storefront_mutation """
  mutation storefrontAccessTokenCreate($input: StorefrontAccessTokenInput!) {
    storefrontAccessTokenCreate(input: $input) {
      userErrors {
        field
        message
      }
      storefrontAccessToken {
        id
        title
        accessToken
      }
    }
  }
  """

  @doc """
  Creates a new storefront credential with given input.

  ## Examples

      iex> create_storefront_credential(client, %{email: "<EMAIL>", phone: "...", firstName: "...", lastName: "..."})
      {:ok, %{"id" => "gid://shopify/Customer/123"}}

      iex> create_storefront_credential(client, %{})
      {:error, error_reason}

  """
  def create_storefront_credential(client, input) do
    variables = %{input: input}
    mutation = @create_storefront_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "storefrontAccessTokenCreate",
             variables: variables
           ),
         {:ok, %{"storefrontAccessToken" => data}} <-
           handle_user_errors(response, "storefrontAccessTokenCreate") do
      {:ok, data}
    end
  end

  @doc """
  Creates a new cart with given input.

  ## Examples

      iex> simulate_cart(client, %{purchasingEntity: %{customerId: "gid://shopify/Customer/123"}, lineItems: [...]})
      {:ok, %{id: "gid://shopify/DraftOrder/123", "ready" => true}}

      iex> simulate_cart(client, %{})
      {:error, error_reason}

  """

  def simulate_cart(client, input) do
    variables = %{input: input}
    mutation = GraphQL.mutation_simulate_cart()

    storefront_post!(client, mutation, operation_name: "cartCreate", variables: variables)
  end

  @location_inventory_query """
  query InventoryItem($inventoryId: ID!, $locationId: ID!) {
    inventoryItem(id: $inventoryId) {
        id
        inventoryLevel(locationId: $locationId) {
            id
            location {
                id
                name
            }
            quantities(names: "available") {
                name
                quantity
            }
        }
    }
  }
  """

  def fetch_location_inventory(client, inventory_id, location_id) do
    with {:ok, response} <-
           post!(client, @location_inventory_query,
             operation_name: "InventoryItem",
             variables: %{
               first: 1,
               inventoryId: inventory_id,
               locationId: location_id
             }
           ) do
      {:ok, get_in(response, ["data", "inventoryItem"])}
    end
  end

  @variant_inventory_query """
  query VariantInventory($variantId: ID!, $locationId: ID!) {
    productVariant(id: $variantId) {
      inventoryItem {
        id
        inventoryLevel(locationId: $locationId) {
          quantities(names: "available") {
            name
            quantity
          }
        }
      }
    }
  }
  """

  @doc """
  Gets inventory information for a specific variant at a specific location.

  ## Examples

      iex> fetch_variant_inventory(client, "gid://shopify/ProductVariant/123", "gid://shopify/Location/456")
      {:ok, %{"productVariant" => %{"inventoryItem" => %{"inventoryLevel" => %{"quantities" => [%{"name" => "available", "quantity" => 10}]}}}}}

      iex> fetch_variant_inventory(client, "invalid", "invalid")
      {:error, error_reason}

  """
  def fetch_variant_inventory(client, variant_id, location_id) do
    with {:ok, response} <-
           post!(client, @variant_inventory_query,
             operation_name: "VariantInventory",
             variables: %{
               variantId: variant_id,
               locationId: location_id
             }
           ) do
      {:ok, get_in(response, ["data"])}
    end
  end

  @location_inventory_query_by_sku """
  query InventoryItems($first: Int!, $query: String!, $locationId: ID!) {
    inventoryItems(first: $first, query: $query) {
      nodes {
        id
        inventoryLevel(locationId: $locationId) {
            id
            location {
                id
                name
            }
            quantities(names: "available") {
                name
                quantity
            }
        }
      }
    }
  }
  """

  def fetch_location_inventory_by_sku(client, sku_query, location_id, opts \\ []) do
    first = Keyword.get(opts, :first, 1)

    with {:ok, response} <-
           post!(client, @location_inventory_query_by_sku,
             operation_name: "InventoryItems",
             variables: %{
               first: first,
               query: sku_query,
               locationId: location_id
             }
           ) do
      {:ok, get_in(response, ["data", "inventoryItems"])}
    end
  end

  @cancel_fulfillment_mutation """
  mutation FulfillmentCancel($FulfillmentId: ID!) {
    fulfillmentCancel(id: $FulfillmentId) {
      userErrors {
          field
          message
      }
      fulfillment {
          id
          status
      }
    }
  }
  """

  def cancel_fulfillment(client, id) do
    with {:ok, %{"data" => response}} <-
           post!(client, @cancel_fulfillment_mutation,
             operation_name: "FulfillmentCancel",
             variables: %{
               FulfillmentId: id
             }
           ),
         {:ok, %{"fulfillmentCancel" => data}} <-
           handle_user_errors(response, "fulfillmentCancel") do
      {:ok, data}
    end
  end

  @cancel_order_mutation """
  mutation OrderCancel($orderId: ID!, $refund: Boolean!, $restock: Boolean!, $reason: OrderCancelReason!, $notifyCustomer: Boolean!) {
    orderCancel(orderId: $orderId, refund: $refund, restock: $restock, reason: $reason, notifyCustomer: $notifyCustomer) {
      userErrors {
          field
          message
      }
      job {
          done
      }
    }
  }
  """

  def cancel_order(client, params) do
    with {:ok, %{"data" => response}} <-
           post!(client, @cancel_order_mutation, operation_name: "OrderCancel", variables: params),
         {:ok, %{"orderCancel" => data}} <-
           handle_user_errors(response, "orderCancel") do
      {:ok, data}
    end
  end

  @update_order_mutation """
  mutation orderUpdate($input: OrderInput!) {
    orderUpdate(input: $input) {
      userErrors {
        field
        message
      }
      order {
        id
      }
    }
  }
  """

  def update_order(client, input) do
    variables = %{input: input}
    mutation = @update_order_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation, operation_name: "orderUpdate", variables: variables),
         {:ok, %{"order" => data}} <- handle_user_errors(response, "orderUpdate") do
      {:ok, data}
    end
  end

  @update_draft_order_mutation """
  mutation draftOrderUpdate($id: ID!, $input: DraftOrderInput!) {
    draftOrderUpdate(id: $id, input: $input) {
      userErrors {
        field
        message
      }
      draftOrder {
        id
      }
    }
  }
  """

  def update_draft_order(client, id, input) do
    variables = %{id: id, input: input}
    mutation = @update_draft_order_mutation

    with {:ok, %{"data" => response}} <-
           post!(client, mutation, operation_name: "draftOrderUpdate", variables: variables),
         {:ok, %{"draftOrder" => data}} <- handle_user_errors(response, "draftOrderUpdate") do
      {:ok, data}
    end
  end

  @get_product """
    query Product($id: ID!) {
      product(id: $id) {
        id
        title
        description
        onlineStoreUrl
        createdAt
        updatedAt
        publishedAt
        productType
        vendor
        handle
        images(first: 10) {
          edges {
            node {
              id
              url
              height
              width
            }
          }
        }
        options {
          name
          values
        }
        seo {
          title
          description
        }
        tags
        variants(first: 10) {
          edges {
            node {
              id
              title
              price
              compareAtPrice
              sku
              barcode
              weight
              weightUnit
              inventoryPolicy
              createdAt
              updatedAt
              image {
                id
                url
                height
                width
              }
              selectedOptions {
                name
                value
              }
            }
          }
        }
      }
    }
  """

  def get_product(client, id) do
    variables = %{id: id}
    mutation = @get_product

    with {:ok, response} <-
           post!(client, mutation, operation_name: "Product", variables: variables) do
      {:ok, get_in(response, ["data", "product"])}
    end
  end

  @product_variants_query """
  query {
    productVariants($filters) {
      edges {
        node {
          id
          sku
          availableForSale
          title
          displayName
          inventoryQuantity
          selectedOptions {
            name
            value
          }
          product {
            id
            storefrontId
            title
          }
        }
      }
    }
  }
  """

  @doc """
  Gets a list of product variants with given filter.

  ## Examples

      iex> fetch_product_variants(client, query: "id:123")
      {:ok, %{cursor: "eyJsYXN0X2lk...", items: [%{"displayName" => "Bo - Óculos de Grau - Tortoise Nero", "id" => "gid://shopify/ProductVariant/123", ...}]}}

      iex> fetch_product_variants(client, query: 123)
      {:error, error_reason}

  """
  def fetch_product_variants(client, filters \\ []) do
    filters = normalize_filters(filters)

    query =
      String.replace(@product_variants_query, "$filters", Enum.join(filters, ", "))

    with {:ok, response} <- post!(client, query) do
      page_info = get_in(response, ["data", "productVariants", "pageInfo"])
      cursor = if page_info["hasNextPage"], do: page_info["endCursor"]

      result = %{
        items: get_in(response, ["data", "productVariants", "edges", Access.all(), "node"]),
        cursor: cursor
      }

      {:ok, result}
    end
  end

  @store_availability_query """
  query GetStoreAvailability($id: ID!, $selectedOptions: [SelectedOptionInput!]!) {
    product(id: $id) {
      variantBySelectedOptions(selectedOptions: $selectedOptions) {
        storeAvailability($filters) {
          edges {
            node {
              available
              pickUpTime
              location {
                id
                name
                address {
                  phone
                  latitude
                  longitude
                  address1
                  address2
                  city
                  country
                  countryCode
                  province
                  provinceCode
                  zip
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  }
  """

  @doc """
  Gets a list of store locations for given product variant with given filter.

  ## Examples

      iex> id = "gid://shopify/ProductVariant/123"
      iex> selected_options = [%{"name" => "Color", "value" => "Tortoise Nero"}]
      iex> fetch_product_store_availability(client, id, selected_options)
      {:ok, %{cursor: "eyJsYXN0X2lk...", items: [%{"available" => false, "location" => %{"id" => "gid://shopify/Location/123", "name" => "Matriz", ...}, ...]]}}

      iex> fetch_product_store_availability(client, "123", [])
      {:error, error_reason}

  """
  def fetch_product_store_availability(client, id, selected_options, filters \\ []) do
    filters = normalize_filters(filters, [:query])

    query =
      String.replace(@store_availability_query, "$filters", Enum.join(filters, ", "))

    operation_name = "GetStoreAvailability"
    variables = %{id: id, selectedOptions: selected_options}

    with {:ok, response} <-
           storefront_post!(client, query, operation_name: operation_name, variables: variables) do
      store_availability =
        get_in(response, [
          "data",
          "product",
          "variantBySelectedOptions",
          "storeAvailability"
        ])

      page_info = store_availability["pageInfo"]
      cursor = if page_info["hasNextPage"], do: page_info["endCursor"]

      result = %{
        items: get_in(store_availability, ["edges", Access.all(), "node"]),
        cursor: cursor
      }

      {:ok, result}
    end
  end

  def get_discount_code(client, code) do
    query = GraphQL.query_code_discount_node_by_code()

    with {:ok, %{"data" => response}} <-
           post!(client, query,
             operation_name: "CodeDiscountNodeByCode",
             variables: %{code: code},
             api_version: @admin_api_version_2024_10
           ) do
      {:ok, response}
    end
  end

  @doc """
  Creates a client to query Shopify's Admin GraphQL API

  ## Examples

      iex> client("salva-01", "my token")
      %Tesla.Client{}

  """
  def client(shop_domain, token, opts \\ []) when is_list(opts) do
    middleware = [
      {Tesla.Middleware.Opts, opts},
      {Tesla.Middleware.BaseUrl, "https://#{shop_domain}"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers, [{"X-Shopify-Access-Token", token}]},
      Tesla.Middleware.OpenTelemetry,
      Rms.Middleware.Cache
    ]

    Tesla.client(middleware)
  end

  def storefront_client(shop_domain, token, opts \\ []) when is_list(opts) do
    headers = [
      {"Shopify-Storefront-Private-Token", token},
      {"Accept", "multipart/mixed; boundary=graphql"}
    ]

    remote_ip = RmsWeb.get_remote_ip()

    headers =
      if is_binary(remote_ip) do
        [{"Shopify-Storefront-Buyer-IP", remote_ip} | headers]
      else
        headers
      end

    middleware = [
      {Tesla.Middleware.Opts, opts},
      {Tesla.Middleware.BaseUrl, "https://#{shop_domain}"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers, headers},
      Tesla.Middleware.OpenTelemetry,
      Rms.Middleware.Cache
    ]

    Tesla.client(middleware)
  end

  defp post!(client, query, opts \\ []) do
    operation_name = Keyword.get(opts, :operation_name)
    variables = Keyword.get(opts, :variables)
    api_version = Keyword.get(opts, :api_version, @default_admin_api_version)

    endpoint = String.replace(@admin_graphql_api_path, "$version", api_version)

    client
    |> Tesla.post!(endpoint, params(query, operation_name, variables))
    |> normalize_response()
  end

  defp storefront_post!(client, query, opts) do
    operation_name = Keyword.get(opts, :operation_name)
    variables = Keyword.get(opts, :variables)
    api_version = Keyword.get(opts, :api_version, @default_storefront_api_version)

    endpoint = String.replace(@storefront_graphql_api_path, "$version", api_version)

    client
    |> Tesla.post!(endpoint, params(query, operation_name, variables))
    |> normalize_response()
  end

  defp params(query, nil, nil), do: %{query: query}
  defp params(query, nil, variables), do: %{query: query, variables: variables}

  defp params(query, operation_name, variables),
    do: %{query: query, operationName: operation_name, variables: variables}

  defp normalize_response(%{status: 200, body: %{"errors" => [error | _rest]} = body}) do
    error_code = get_in(error, ["extensions", "code"]) || :unknown_error_code

    Logger.error("unexpected return from shopify",
      error_code: error_code,
      resp_body: inspect(body)
    )

    {:error, {error_code, error}}
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 401, body: _body}) do
    {:error, :unauthorized}
  end

  defp normalize_response(%{status: 402, body: _body}) do
    {:error, :payment_required}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: 423, body: _body}) do
    {:error, :store_locked}
  end

  defp normalize_response(%{status: status_code, body: _body}) when status_code in 500..599 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end

  defp handle_user_errors(response, key) do
    handle_user_errors(get_in(response, [key]))
  end

  defp handle_user_errors(%{"userErrors" => [error | _rest]}) do
    {:error, {:unknown_error_code, error}}
  end

  defp handle_user_errors(%{"userErrors" => []} = response) do
    {:ok, response}
  end

  defp normalize_filters(filters, unavailable_keys \\ []) do
    filters = Keyword.put_new(filters, :first, 10)

    for {key, value} <- filters,
        key not in unavailable_keys,
        value != nil,
        do: "#{key}: #{inspect(value)}"
  end

  @return_query """
  query getReturn($id: ID!) {
    return(id: $id) {
        id
        status
        reverseFulfillmentOrders(first: 10) {
            edges {
                node {
                    id
                    status
                    lineItems(first: 50) {
                        edges {
                            node {
                                id
                                totalQuantity
                                fulfillmentLineItem {
                                    lineItem {
                                        sku
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
  }
  """

  @doc """
  Get a Return by ID.

  ## Examples

      iex> get_return(client, "gid://shopify/Return/123")
      {:ok, %{}}

      iex> get_return(client, "invalid_id")
      {:error, error_reason}

  """
  def get_return(client, return_id) do
    variables = %{id: return_id}

    case post!(client, @return_query,
           operation_name: "getReturn",
           variables: variables,
           api_version: "2025-01"
         ) do
      {:ok, %{"data" => %{"return" => return}}} ->
        {:ok, return}

      {:ok, %{"errors" => errors}} ->
        {:error, "shopify api error: #{inspect(errors)}"}

      {:error, reason} ->
        {:error, "shopify api request failed: #{inspect(reason)}"}
    end
  end

  @reverse_fulfillment_order_dispose_mutation """
  mutation reverseFulfillmentOrderDispose($dispositionInputs: [ReverseFulfillmentOrderDisposeInput!]!) {
    reverseFulfillmentOrderDispose(dispositionInputs: $dispositionInputs) {
      reverseFulfillmentOrderLineItems {
        id
        dispositions {
          id
          type
          quantity
          location {
            id
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Disposes items in a reverse fulfillment order.

  ## Examples

      iex> reverse_fulfillment_order_dispose(client, %{reverseFulfillmentOrderId: "gid://shopify/ReverseFulfillmentOrder/123", dispositionInputs: [...]})
      {:ok, :disposed}

      iex> reverse_fulfillment_order_dispose(client, %{})
      {:error, error_reason}

  """
  def reverse_fulfillment_order_dispose(client, disposition_inputs) do
    variables = %{dispositionInputs: disposition_inputs}

    case post!(
           client,
           @reverse_fulfillment_order_dispose_mutation,
           operation_name: "reverseFulfillmentOrderDispose",
           variables: variables,
           api_version: "2025-01"
         ) do
      {:ok, %{"data" => %{"reverseFulfillmentOrderDispose" => response}}} ->
        handle_reverse_fulfillment_order_dispose_response(response)

      {:ok, %{"errors" => errors}} ->
        {:error, "shopify api error: #{inspect(errors)}"}

      {:error, reason} ->
        {:error, "shopify api request failed: #{inspect(reason)}"}
    end
  end

  defp handle_reverse_fulfillment_order_dispose_response(%{"userErrors" => user_errors})
       when length(user_errors) > 0 do
    error_messages = Enum.map_join(user_errors, ", ", fn %{"message" => message} -> message end)
    {:error, "shopify reverse fulfillment order dispose failed: #{error_messages}"}
  end

  defp handle_reverse_fulfillment_order_dispose_response(%{
         "reverseFulfillmentOrderLineItems" => line_items
       }) do
    {:ok, line_items}
  end

  defp handle_reverse_fulfillment_order_dispose_response(response) do
    {:error, "unexpected response from shopify: #{inspect(response)}"}
  end

  @return_close_mutation """
  mutation returnClose($id: ID!) {
    returnClose(id: $id) {
      return {
        id
        status
      }
      userErrors {
        field
        message
      }
    }
  }
  """

  @doc """
  Closes a return in Shopify.

  ## Examples

      iex> return_close(client, "gid://shopify/Return/123")
      {:ok, %{return_id: "gid://shopify/Return/123", status: "CLOSED"}}

      iex> return_close(client, "invalid_id")
      {:error, error_reason}

  """
  def return_close(client, return_id) do
    variables = %{id: return_id}

    case post!(client, @return_close_mutation,
           operation_name: "returnClose",
           variables: variables,
           api_version: "2025-01"
         ) do
      {:ok, %{"data" => %{"returnClose" => response}}} ->
        handle_return_close_response(response)

      {:ok, %{"errors" => errors}} ->
        {:error, "shopify api error: #{inspect(errors)}"}

      {:error, reason} ->
        {:error, "shopify api request failed: #{inspect(reason)}"}
    end
  end

  defp handle_return_close_response(%{"userErrors" => user_errors})
       when length(user_errors) > 0 do
    error_messages = Enum.map_join(user_errors, ", ", fn %{"message" => message} -> message end)
    {:error, "shopify return close failed: #{error_messages}"}
  end

  defp handle_return_close_response(%{"return" => %{"id" => return_id, "status" => "CLOSED"}}) do
    {:ok, %{return_id: return_id, status: "CLOSED"}}
  end

  defp handle_return_close_response(response) do
    {:error, "unexpected response from shopify: #{inspect(response)}"}
  end

  @doc """
  Fetches a gift card by its code.

  ## Examples

      iex> fetch_gift_card(client, "gift_card_code")
      {:ok, %{"data" => %{"giftCards" => ...}}}

      iex> fetch_gift_card(client, "non_existent_code")
      {:error, :not_found}

  """
  def fetch_gift_card(client, code) do
    post!(client, GraphQL.query_gift_cards(),
      operation_name: "FetchGiftCard",
      variables: %{query: code},
      api_version: "2025-04"
    )
  end

  @doc """
  Creates an order.

  ## Examples

      iex> create_order(client, order)
      {:ok, %{"order" => %{"id" => "gid://shopify/Order/123", ...}}}

      iex> create_order(client, order)
      {:ok, {:unknown_error_code, %{...}}}

      iex> create_order(client, order)
      {:error, %{"userErrors" => [...]}}}

  """
  def create_order(client, order) do
    with {:ok, response} <-
           post!(client, GraphQL.mutation_create_order(),
             operation_name: "orderCreate",
             variables: %{order: order},
             api_version: @admin_api_version_2024_10
           ) do
      case response["data"]["orderCreate"] do
        %{"userErrors" => []} = result -> {:ok, result["order"]}
        %{"userErrors" => _errors} = result -> {:error, result}
      end
    end
  end

  @doc """
  Updates an existing order with given input.

  ## Examples

      iex> order_update(client, %{id: "gid://shopify/Order/123", localizedFields: [%{key: "TAX_CREDENTIAL_BR", value: "1234567890123"}]})
      {:ok, %{"id" => "gid://shopify/Order/123", ...}}

      iex> order_update(client, %{})
      {:error, error_reason}

  """
  def order_update(client, input) do
    variables = %{input: input}
    mutation = GraphQL.mutation_order_update()

    with {:ok, response} <-
           post!(client, mutation,
             operation_name: "orderUpdate",
             variables: variables,
             api_version: @admin_api_version_2025_04
           ) do
      case response["data"]["orderUpdate"] do
        %{"userErrors" => []} = result -> {:ok, result["order"]}
        %{"userErrors" => _errors} = result -> {:error, result}
      end
    end
  end

  @doc """
  Credits a specified amount to a gift card.

  ## Examples

      iex> credit_gift_card(client, "gid://shopify/GiftCard/123", %{amount: 10.0, currencyCode: "BRL"})
      {:ok, %{"data" => %{"giftCardCreditTransaction" => ...}}}
  """

  def credit_gift_card(client, id, credit_input) do
    variables = %{id: id, creditInput: credit_input}
    mutation = GraphQL.mutation_gift_card_credit()

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "GiftCardCredit",
             variables: variables,
             api_version: "2025-04"
           ),
         {:ok, %{"giftCardCreditTransaction" => data}} <-
           handle_user_errors(response, "giftCardCredit") do
      {:ok, data}
    end
  end

  @doc """
  Debits a specified amount from a gift card.

  ## Examples

      iex> debit_gift_card(client, "gid://shopify/GiftCard/123", %{amount: 10.0, currencyCode: "USD"})
      {:ok, %{"giftCardDebitTransaction" => %{"id" => "gid://...", "amount" => %{"amount" => "10.0", "currencyCode" => "USD"}}}}

      iex> debit_gift_card(client, "gid://shopify/GiftCard/123", %{amount: 1000.0, currencyCode: "USD"})
      {:error, {:unknown_error_code, %{"message" => "Insufficient funds"}}}

  """
  def debit_gift_card(client, id, debit_input) do
    variables = %{id: id, debitInput: debit_input}
    mutation = GraphQL.mutation_gift_card_debit()

    with {:ok, %{"data" => response}} <-
           post!(client, mutation,
             operation_name: "GiftCardDebit",
             variables: variables,
             api_version: "2025-04"
           ),
         {:ok, %{"giftCardDebitTransaction" => data}} <-
           handle_user_errors(response, "giftCardDebit") do
      {:ok, data}
    end
  end

  def get_product_variant_stock(client, remote_id) do
    with {:ok, %{"data" => data}} <-
           storefront_post!(client, GraphQL.query_product_variant_stock(),
             operation_name: "ProductVariantStock",
             variables: %{id: remote_id},
             api_version: "2025-04"
           ) do
      {:ok, data["node"]}
    end
  end
end
