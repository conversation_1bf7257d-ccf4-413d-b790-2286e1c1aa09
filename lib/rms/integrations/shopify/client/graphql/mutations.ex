defmodule Rms.Integrations.Shopify.Client.GraphQL.Mutations do
  def draft_order_create do
    """
    mutation draftOrderCreate($input: DraftOrderInput!) {
      draftOrderCreate(input: $input) {
        draftOrder {
          id
          ready
        }
        userErrors {
          field
          message
        }
      }
    }
    """
  end

  def draft_order_calculate do
    """
    mutation CalculateDraftOrder($input: DraftOrderInput!) {
      draftOrderCalculate(input: $input) {
        calculatedDraftOrder {
          customer {
            id
            email
            firstName
            numberOfOrders
            defaultAddress {
              id
              firstName
              lastName
              address1
              address2
              city
              provinceCode
              zip
              countryCodeV2
            }
            taxExempt
          }
          lineItems {
            originalUnitPrice {
              amount
              currencyCode
            }
            originalTotal {
              amount
              currencyCode
            }
            product {
              id
              title
            }
            quantity
            sku
            title
            variantTitle
            variant {
              id
              sellableOnlineQuantity
            }
          }
          platformDiscounts {
            id
            automaticDiscount
            bxgyDiscount
            code
            discountClasses
            presentationLevel
            shortSummary
            summary
            totalAmount {
              amount
              currencyCode
            }
            totalAmountPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            allocations {
              id
              quantity
              reductionAmount {
                amount
                currencyCode
              }
              reductionAmountSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              target {
                __typename
                ... on DraftOrderLineItem {
                  variant {
                    id
                  }
                }
                ... on CalculatedDraftOrderLineItem {
                  variant {
                    id
                  }
                }
                ... on ShippingLine {
                  shippingRateHandle
                }
              }
            }
          }
          shippingLine {
            carrierIdentifier
            code
            title
            source
            shippingRateHandle
            originalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            discountedPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
          availableShippingRates {
            handle
            title
            price {
              amount
              currencyCode
            }
          }
          totalPriceSet {
            presentmentMoney {
              amount
              currencyCode
            }
            shopMoney {
              amount
              currencyCode
            }
          }
          totalLineItemsPriceSet {
            shopMoney {
              amount
              currencyCode
            }
          }
          totalShippingPriceSet {
            shopMoney {
              amount
              currencyCode
            }
          }
          totalDiscountsSet {
            presentmentMoney {
              amount
              currencyCode
            }
            shopMoney {
              amount
              currencyCode
            }
          }
          warnings {
            errorCode
            field
            message
          }
        }
        userErrors {
          field
          message
        }
      }
    }
    """
  end

  def create_order do
    """
    mutation orderCreate($order: OrderCreateOrderInput!) {
      orderCreate(order: $order) {
        order {
          id
          name
        }
        userErrors {
          field
          message
        }
      }
    }
    """
  end

  def order_update do
    """
    mutation orderUpdate($input: OrderInput!) {
      orderUpdate(input: $input) {
        order {
          id
          name
        }
        userErrors {
          field
          message
        }
      }
    }
    """
  end

  def simulate_cart do
    """
    mutation cartCreate($input: CartInput!) {
      cartCreate(input: $input) {
        cart {
              createdAt
              id
              cost {
                subtotalAmount {
                    amount
                    currencyCode
                }
                totalAmount {
                    amount
                }
                checkoutChargeAmount {
                    amount
                }
              }
              discountCodes {
                  applicable
                  code
              }
              discountAllocations {
                  discountedAmount {
                      amount
                  }
                  ... on CartAutomaticDiscountAllocation {
                      title
                      discountedAmount {
                          amount
                          currencyCode
                      }
                  }
                  ... on CartCodeDiscountAllocation {
                      code
                      discountedAmount {
                          amount
                          currencyCode
                      }
                  }
                  ... on CartCustomDiscountAllocation {
                      title
                      discountedAmount {
                          amount
                          currencyCode
                      }
                  }
              }
              lines(first: 250) {
                  edges {
                      node {
                          merchandise {
                              ... on ProductVariant {
                                  id
                              }
                          }
                          cost {
                              amountPerQuantity {
                                  amount
                              }
                              totalAmount {
                                  amount
                              }
                          }
                          discountAllocations {
                            ... on CartAutomaticDiscountAllocation {
                                title
                            }
                            ... on CartCodeDiscountAllocation {
                                code
                            }
                            ... on CartCustomDiscountAllocation {
                                title
                            }
                          }
                          quantity
                      }
                  }
              }
              ... DeliveryGroups @defer
        }
        userErrors {
          field
          message
        }
      }
      }
      fragment DeliveryGroups on Cart {
      deliveryGroups(first: 10, withCarrierRates: true) {
        edges {
          node {
            deliveryOptions {
              title
              handle
              deliveryMethodType
              estimatedCost {
                amount
              }
            }
            selectedDeliveryOption {
              title
              handle
              deliveryMethodType
              estimatedCost {
                amount
                currencyCode
              }
            }
          }
        }
      }
    }
    """
  end

  def gift_card_credit do
    """
    mutation GiftCardCredit($id: ID!, $creditInput: GiftCardCreditInput!) {
      giftCardCredit(id: $id, creditInput: $creditInput) {
        giftCardCreditTransaction {
          id
          amount {
            currencyCode
            amount
          }
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    """
  end

  def gift_card_debit do
    """
    mutation GiftCardDebit($id: ID!, $debitInput: GiftCardDebitInput!) {
      giftCardDebit(id: $id, debitInput: $debitInput) {
        giftCardDebitTransaction {
          id
          amount {
            currencyCode
            amount
          }
        }
        userErrors {
          code
          field
          message
        }
      }
    }
    """
  end
end
