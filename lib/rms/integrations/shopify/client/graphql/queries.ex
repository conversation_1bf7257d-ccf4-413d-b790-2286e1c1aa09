defmodule Rms.Integrations.Shopify.Client.GraphQL.Queries do
  def code_discount_node_by_code do
    """
    query CodeDiscountNodeByCode($code: String!) {
      codeDiscountNodeByCode(code: $code) {
        codeDiscount {
            ... on DiscountCodeBasic {
                customerGets {
                    value {
                        ... on DiscountAmount {
                            amount {
                                amount
                            }
                        }
                        ... on DiscountPercentage {
                            percentage
                        }
                    }
                }
            }
        }
      }
    }
    """
  end

  def gift_cards do
    """
    query FetchGiftCard($query: String!) {
      giftCards(first: 2, query: $query) {
        edges {
          node {
            id
            balance {
              amount
            }
            transactions(first: 5, reverse: true) {
              nodes {
                note
              }
            }
            enabled
            expiresOn
            deactivatedAt
          }
        }
      }
    }
    """
  end

  def product_variant_stock do
    """
    query ProductVariantStock($id: ID!) {
      node(id: $id) {
        ... on ProductVariant {
            availableForSale
            quantityAvailable
        }
      }
    }
    """
  end
end
