defmodule Rms.Integrations.Shopify.ClientBehaviour do
  @moduledoc """
  Behaviour for interacting with Shopify's Admin GraphQL API.
  """

  @callback find_orders(client :: Tesla.Client.t(), filters :: list()) ::
              {:ok, %{orders: list(map()), last_cursor: String.t() | nil}} | {:error, any()}
  @callback get_order!(
              client :: Tesla.Client.t(),
              id :: String.t(),
              selection :: String.t(),
              opts :: Keyword.t()
            ) ::
              {:ok, map()} | {:error, any()}
  @callback get_product(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback mark_order_as_paid(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback return_create(client :: Tesla.Client.t(), return_input :: map()) ::
              {:ok, String.t()} | {:error, any()}
  @callback get_return(client :: Tesla.Client.t(), return_id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback return_close(client :: Tesla.Client.t(), return_id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback reverse_fulfillment_order_dispose(client :: Tesla.Client.t(), dispose_input :: map()) ::
              {:ok, :disposed} | {:error, any()}
  @callback returnable_fulfillments(client :: Tesla.Client.t(), order_id :: String.t()) ::
              {:ok, list(map())} | {:error, any()}
  @callback create_product_feed(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback create_webhook(client :: Tesla.Client.t(), topic :: String.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback get_product_feeds(client :: Tesla.Client.t()) ::
              {:ok, map()} | {:error, any()}
  @callback run_product_full_sync(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback create_draft_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback new_create_draft_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback calculate_draft_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback move_fulfillment(client :: Tesla.Client.t(), input :: any(), input :: any()) ::
              {:ok, map()} | {:error, any()}
  @callback fulfill_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback create_storefront_credential(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_draft_order(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_location_inventory(
              client :: Tesla.Client.t(),
              id :: String.t(),
              id :: String.t()
            ) ::
              {:ok, map()} | {:error, any()}

  @callback fetch_location_inventory_by_sku(
              client :: Tesla.Client.t(),
              product_variant_id :: String.t(),
              location_id :: String.t(),
              opts :: Keyword.t()
            ) ::
              {:ok, map()} | {:error, any()}

  @callback fetch_variant_inventory(
              client :: Tesla.Client.t(),
              variant_id :: String.t(),
              location_id :: String.t()
            ) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_location(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback simulate_cart(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback mark_order_as_completed(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback create_customer(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback cancel_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback cancel_fulfillment(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback update_order(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback update_draft_order(client :: Tesla.Client.t(), id :: String.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_customers(client :: Tesla.Client.t(), filters :: keyword()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_customer(client :: Tesla.Client.t(), id :: String.t()) ::
              {:ok, map()} | {:error, any()}
  @callback update_customer(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_product_variants(client :: Tesla.Client.t(), filters :: keyword()) ::
              {:ok, map()} | {:error, any()}
  @callback fetch_product_store_availability(
              client :: Tesla.Client.t(),
              id :: String.t(),
              selected_options ::
                list(%{required(:name) => String.t(), required(:value) => String.t()}),
              filters :: keyword()
            ) ::
              {:ok, map()} | {:error, any()}

  @callback order_update(client :: Tesla.Client.t(), input :: map()) ::
              {:ok, map()} | {:error, any()}

  @callback client(shop_domain :: String.t(), token :: String.t()) :: Tesla.Client.t()

  @callback storefront_client(
              shop_domain :: String.t(),
              token :: String.t(),
              opts :: Keyword.t()
            ) :: Tesla.Client.t()

  @callback get_discount_code(client :: Tesla.Client.t(), code :: String.t()) ::
              {:ok, map()} | {:error, any()}

  @callback fetch_gift_card(client :: Tesla.Client.t(), code :: String.t()) ::
              {:ok, map()} | {:error, any()}

  @callback create_order(client :: Tesla.Client.t(), order :: map()) ::
              {:ok, map()} | {:error, any()}

  @callback debit_gift_card(client :: Tesla.Client.t(), id :: String.t(), debit_input :: map()) ::
              {:ok, map()} | {:error, any()}

  @callback credit_gift_card(client :: Tesla.Client.t(), id :: String.t(), credit_input :: map()) ::
              {:ok, map()} | {:error, any()}

  @callback get_product_variant_stock(client :: Tesla.Client.t(), remote_id :: String.t()) ::
              {:ok, map()} | {:error, any()}
end
