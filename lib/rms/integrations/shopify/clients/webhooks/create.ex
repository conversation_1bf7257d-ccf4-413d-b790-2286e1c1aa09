defmodule Rms.Integrations.Shopify.Clients.Webhooks.Create do
  alias Ecto.Multi
  alias Rms.Repo

  def execute(organization_id, shopify_client) do
    formatted_customer = transform_customer(shopify_client)

    {customer, shopify_id} = find_or_initialize_customer(organization_id, shopify_client)

    maybe_upsert_customer(organization_id, customer, shopify_id, formatted_customer)
  end

  # Cria cliente novo + mapeamento com Ecto.Multi
  defp maybe_upsert_customer(organization_id, nil, shopify_customer_id, formatted_customer) do
    formatted_customer =
      formatted_customer |> add_city_code_to_customer_addresses()

    Multi.new()
    |> Multi.run(:customer, fn _repo, _changes ->
      Rms.Customers.import_customer(organization_id, formatted_customer)
    end)
    |> Multi.run(:sync_mapping, fn _repo, %{customer: customer} ->
      Rms.Integrations.create_customer_sync_mapping(organization_id, %{
        customer_id: customer.id,
        source: "shopify",
        external_id: shopify_customer_id
      })
    end)
    |> Repo.transaction()
  end

  # Atualiza cliente e endereços existentes
  defp maybe_upsert_customer(_organization_id, customer, _shopify_customer_id, formatted_customer) do
    {shopify_addresses, customer_params} = Map.pop(formatted_customer, :addresses, [])

    [addresses_to_delete, addresses_to_create] =
      find_addresses_to_update(customer, shopify_addresses)

    customer
    |> Repo.preload([:addresses], force: true)
    |> Rms.Customers.maybe_update_customer_and_addresses(
      customer_params,
      [addresses_to_delete, addresses_to_create],
      skip_events: true
    )
  end

  defp find_addresses_to_update(customer, shopify_addresses) do
    customer_addresses = customer.addresses

    addresses_to_delete =
      Enum.filter(customer_addresses, fn customer_address ->
        not Enum.any?(shopify_addresses, &address_match?(customer_address, &1))
      end)

    addresses_to_create =
      Enum.filter(shopify_addresses, fn transformed_address ->
        not Enum.any?(customer_addresses, fn customer_address ->
          address_match?(customer_address, transformed_address)
        end)
      end)
      |> Enum.map(&add_city_code/1)

    [addresses_to_delete, addresses_to_create]
  end

  defp find_or_initialize_customer(organization_id, %{"admin_graphql_api_id" => external_id}) do
    case Rms.Integrations.get_customer_sync_mapping(organization_id, "shopify", external_id) do
      %Rms.Integrations.CustomerSyncMapping{customer_id: _} = mapping ->
        mapping = Repo.preload(mapping, customer: [:addresses])
        {mapping.customer, external_id}

      _ ->
        {nil, external_id}
    end
  end

  defp transform_customer(%{"addresses" => addresses} = customer_map) when is_list(addresses) do
    %{
      updated_at: customer_map["updated_at"],
      name: "#{customer_map["first_name"]} #{customer_map["last_name"]}",
      email: customer_map["email"],
      primary_phone_number: customer_map["phone"],
      addresses:
        addresses
        |> Enum.map(fn address -> transform_address(address, "shopify") end)
        |> Enum.reject(&is_nil/1)
    }
  end

  defp transform_customer(customer_map) do
    %{
      name: "#{customer_map["first_name"]} #{customer_map["last_name"]}",
      email: customer_map["email"],
      primary_phone_number: customer_map["phone"],
      updated_at: customer_map["updated_at"]
    }
  end

  defp transform_address(%{"zip" => zip} = address, "shopify") do
    with zip <- Rms.Integrations.Shopify.ImportCustomerWorker.format_zip(zip) do
      transformed = %{
        receiver_name: address["name"],
        city_name: address["city"],
        state: address["province_code"],
        country_name: address["country"],
        street: address["address1"],
        zip: zip,
        complement: address["address2"]
      }

      case Rms.Addresses.Address.changeset(%Rms.Addresses.Address{}, transformed) do
        %{valid?: true} -> transformed
        _ -> nil
      end
    end
  end

  defp transform_address(nil, _source), do: nil

  defp add_city_code_to_customer_addresses(customer) do
    customer
    |> Map.update(:addresses, [], fn addresses -> Enum.map(addresses, &add_city_code/1) end)
  end

  defp add_city_code(address) when is_map(address) do
    case Rms.Integrations.CityCode.get_address_city_code(
           address.city_name,
           address.state,
           address.zip
         ) do
      {:ok, city_code} -> Map.put(address, :city_code, city_code)
      error -> throw(error)
    end
  end

  defp add_city_code(_), do: nil

  defp address_match?(existing_address, new_address) do
    city_match = downcase(existing_address.city_name) == downcase(new_address.city_name)

    street_match =
      if has_any_data_or_equals?(new_address.street, existing_address.street) do
        String.contains?(
          String.trim(downcase(new_address.street)),
          String.trim(downcase(existing_address.street))
        ) &&
          String.contains?(
            String.trim(downcase(new_address.street)),
            String.trim(downcase(existing_address.number))
          )
      else
        false
      end

    compliment_match =
      if has_any_data_or_equals?(new_address.complement, existing_address.complement) do
        String.contains?(
          String.trim(downcase(existing_address.complement)),
          String.trim(downcase(new_address.complement))
        )
      else
        false
      end

    city_match && street_match && compliment_match
  end

  defp downcase(data) do
    String.downcase(data || "")
  end

  defp has_any_data_or_equals?(data1, data2) do
    (data1 && data2) || (data1 || "") == (data2 || "")
  end
end
