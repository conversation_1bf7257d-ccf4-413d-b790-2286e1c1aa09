defmodule Rms.Integrations.Shopify.DeleteWebhookHandler do
  @moduledoc """
  Handles the logic for Shopify delete webhooks (PRODUCT and VARIANT resources).
  """

  alias Rms.Repo
  alias Rms.Commerce.Products.ProductVariant
  alias Rms.Integrations.ProductSyncMapping
  alias Rms.Integrations.ProductVariantMapping

  require Rms.Integrations.ProductSyncMapping
  require Rms.Integrations.ProductVariantMapping

  import Ecto.Query

  @product_resource "PRODUCT"
  @variant_resource "VARIANT"

  @doc """
  Processes a delete webhook payload.

  Determines if it's a product or variant delete and calls the appropriate
  private function.
  """
  def handle_delete(organization_id, payload) do
    resource_type = get_in(payload, ["metadata", "resource"])

    case resource_type do
      @product_resource -> handle_product_delete(organization_id, payload)
      @variant_resource -> handle_variant_delete(organization_id, payload)
      _ -> {:error, :unknown_resource_type}
    end
  end

  defp handle_product_delete(organization_id, payload) do
    external_product_id = get_in(payload, ["product", "id"])

    case find_product_mapping(organization_id, external_product_id) do
      %ProductSyncMapping{product_id: product_id} ->
        archive_variants_by_product_id(product_id)

      nil ->
        :ok
    end
  end

  defp handle_variant_delete(organization_id, payload) do
    external_variant_id =
      get_in(payload, ["product", "variants", "edges", Access.at(0), "node", "id"])

    case find_variant_mapping(organization_id, external_variant_id) do
      %ProductVariantMapping{product_variant_id: variant_id} ->
        archive_variant_by_id(variant_id)

      _ ->
        :ok
    end
  end

  defp find_product_mapping(_organization, nil), do: nil

  defp find_product_mapping(organization_id, external_product_id) do
    Repo.get_by(ProductSyncMapping,
      organization_id: organization_id,
      external_id: external_product_id,
      source: "shopify"
    )
  end

  defp find_variant_mapping(_organization, nil), do: nil

  defp find_variant_mapping(organization_id, external_variant_id) do
    Repo.get_by(ProductVariantMapping,
      organization_id: organization_id,
      external_id: external_variant_id,
      source: "shopify"
    )
  end

  defp archive_variants_by_product_id(product_id) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    ProductVariant
    |> where([v], v.product_id == ^product_id)
    |> Repo.update_all(set: [archived_at: now])

    :ok
  end

  defp archive_variant_by_id(variant_id) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    case Repo.get(ProductVariant, variant_id) do
      nil ->
        :ok

      variant ->
        variant
        |> Ecto.Changeset.change(archived_at: now)
        |> Repo.update!()

        :ok
    end
  end
end
