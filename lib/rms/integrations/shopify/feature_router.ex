defmodule Rms.Integrations.Shopify.FeatureRouter do
  @behaviour Rms.FeatureRouter

  require Logger

  import Rms.FeatureRouter, only: [validate!: 3, call_flow!: 2]

  @impl true
  def features,
    do: %{
      create_order: %{
        flows: [:draft_order, :create_order]
      }
    }

  @impl true
  def route(feature_name, attrs, flows) do
    validate!(__MODULE__, feature_name, flows)
    do_route(feature_name, attrs, flows)
  end

  def do_route(:create_order, attrs, flows) do
    organization_id = Map.fetch!(attrs, :organization_id)
    fulfillments = Map.get(attrs, :fulfillments, [])
    should_create_order_flag? = Rms.FeatureFlag.should_create_order?(organization_id)
    fulfillments_length = length(fulfillments) == 2

    just_delivery? =
      length(fulfillments) == 1 && Enum.at(fulfillments, 0).shipping_method == "delivery"

    shipping_methods = ["in-store", "delivery"]
    has_shipping_methods = Enum.any?(fulfillments, &(&1.shipping_method in shipping_methods))

    debug_map = %{
      should_create_order_flag?: should_create_order_flag?,
      fulfillments_length: fulfillments_length,
      has_shipping_methods: has_shipping_methods,
      organization_id: organization_id
    }

    if should_create_order_flag? && (fulfillments_length or just_delivery?) &&
         has_shipping_methods do
      log_flow(:create_order, :create_order, debug_map)
      call_flow!(flows, :create_order)
    else
      call_flow!(flows, :draft_order)
    end
  end

  defp log_flow(flow, feature_name, debug_map) do
    Logger.info("""
      [#{__MODULE__}] flow #{flow} called to #{feature_name}.
      DebugMap: #{inspect(debug_map)}
    """)
  end
end
