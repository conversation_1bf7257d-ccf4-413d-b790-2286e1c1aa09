defmodule Rms.Integrations.Shopify.Fulfillments.BuildFulfillments do
  def execute(order) do
    fulfillments =
      order.line_items
      |> group_line_items()
      |> Enum.reduce_while([], fn {{shipping_settings, shipping_method}, line_items}, acc ->
        case build_fulfillment(shipping_settings, shipping_method, line_items, order) do
          {:ok, fulfillment} -> {:cont, [fulfillment | acc]}
          {:error, reason} -> {:halt, {:error, reason}}
        end
      end)

    case fulfillments do
      {:error, reason} ->
        {:error, reason}

      fulfillments ->
        {:ok, order |> Map.put(:fulfillments, fulfillments) |> Map.drop([:line_items])}
    end
  end

  defp build_fulfillment(shipping_settings, shipping_method, line_items, _order)
       when shipping_method in ["in-store", "local-pickup", "delivery"] do
    {:ok,
     %{
       ecommerce: "shopify",
       shipping_method: shipping_method,
       shipping_settings: %{
         price: 0,
         ecommerce: "shopify",
         settings: shipping_settings
       },
       line_items: line_items
     }}
  end

  defp build_fulfillment(_, _, _, _) do
    {:error, "shipping method not suported in fullfilments creation"}
  end

  defp group_line_items(line_items) do
    Enum.group_by(line_items, fn
      %{shipping_method: "in-store"} = line_item ->
        {
          nil,
          line_item.shipping_method
        }

      line_item ->
        {
          line_item.shipping_settings,
          line_item.shipping_method
        }
    end)
  end
end
