defmodule Rms.Integrations.Shopify.Fulfillments.CreateFulfillments do
  alias Oban.Pro.Workers.Workflow
  alias Rms.Integrations.Shopify
  alias Rms.Integrations.Shopify.FeatureRouter

  def execute(order) do
    opts = %{
      fulfillments: order.fulfillments
    }

    order.fulfillments
    |> filter_fulfillments(order.organization_id)
    |> Enum.with_index()
    |> Enum.reduce(Ecto.Multi.new(), fn {fulfillment, index}, acc ->
      acc
      |> Ecto.Multi.run("worker_#{fulfillment.shipping_method}_#{index}", fn _, _ ->
        {:ok, enqueue(fulfillment, opts)}
      end)
    end)
    |> Rms.Repo.transaction()
  end

  defp get_settings(organization_id) do
    case Rms.Settings.get_organization_setting(organization_id, "send_order_to_shopify") do
      %{value: %{"data" => value}} -> value
      _ -> true
    end
  end

  defp filter_fulfillments(fulfillments, organization_id) do
    Enum.filter(fulfillments, fn
      %{shipping_method: "in-store"} -> get_settings(organization_id)
      _fulfillment -> true
    end)
  end

  defp enqueue(%{shipping_method: shipping_method} = fulfillment, opts)
       when shipping_method != "local-pickup" do
    attrs =
      %{
        id: fulfillment.id,
        organization_id: fulfillment.organization_id
      }

    router_attrs =
      %{
        organization_id: attrs.organization_id,
        fulfillments: opts.fulfillments
      }

    Workflow.new(workflow_name: "shopify_fulfillment")
    |> Workflow.add(:create_customer, Shopify.CreateCustomerWorker.new(attrs))
    |> then(fn workflow -> router_workflow(workflow, attrs, router_attrs) end)
    |> Oban.insert_all()
  end

  defp enqueue(_fulfillment, _opts), do: []

  defp router_workflow(workflow, attrs, router_attrs) do
    FeatureRouter.route(
      :create_order,
      router_attrs,
      %{
        draft_order: fn ->
          Workflow.add(
            workflow,
            :create_draft_order,
            Shopify.CreateDraftOrderWorker.new(attrs),
            deps: [:create_customer]
          )
        end,
        create_order: fn -> workflow end
      }
    )
  end
end
