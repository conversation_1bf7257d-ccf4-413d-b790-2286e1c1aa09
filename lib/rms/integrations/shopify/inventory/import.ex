defmodule Rms.Integrations.Shopify.Inventory.Import do
  alias Rms.Integrations
  alias Rms.Integrations.InventoryItemMapping
  alias Rms.Integrations.Shopify

  def execute(organization_id, product_variant, location) do
    location = Rms.Repo.preload(location, [:location_mappings])

    product_variant =
      Rms.Repo.preload(product_variant, [:inventory_item_mappings, :product_variant_mappings])

    with {:ok, inventory_mapping} <-
           filter_shopify_inventory_mapping(product_variant.inventory_item_mappings),
         {:ok, location_mapping} <- filter_shopify_location_mapping(location.location_mappings),
         {:ok, inventory_info} <-
           get_inventory(
             organization_id,
             product_variant,
             inventory_mapping,
             location_mapping
           ) do
      upsert_inventory_mapping(product_variant, inventory_info)
      {:ok, inventory_info}
    end
  end

  def upsert_inventory_mapping(product_variant, inventory_info) do
    %InventoryItemMapping{}
    |> InventoryItemMapping.changeset(%{
      organization_id: product_variant.organization_id,
      product_variant_id: product_variant.id,
      source: "shopify",
      external_id: inventory_info["external_inventory_id"]
    })
    |> Rms.Repo.insert(
      conflict_target: [:organization_id, :external_id, :source],
      on_conflict: {:replace, [:updated_at]}
    )
  end

  defp filter_shopify_inventory_mapping(inventory_mappings) do
    case Enum.find(inventory_mappings, fn inventory_mapping ->
           inventory_mapping.source == "shopify"
         end) do
      nil ->
        {:ok, nil}

      mapping ->
        {:ok, mapping}
    end
  end

  defp filter_shopify_location_mapping(location_mappings) do
    case Enum.find(location_mappings, fn location_mapping ->
           location_mapping.source == "shopify"
         end) do
      nil ->
        {:error, "location_mapping not found"}

      mapping ->
        {:ok, mapping}
    end
  end

  defp get_inventory(organization_id, product_variant, nil, external_location) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    shopify_client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, variant_mapping} <-
           get_shopify_variant_mapping(product_variant.product_variant_mappings),
         {:ok,
          %{
            "productVariant" => %{
              "inventoryItem" => %{"id" => inventory_item_id, "inventoryLevel" => inventory_info}
            }
          }} <-
           Integrations.Shopify.fetch_variant_inventory(
             shopify_client,
             variant_mapping.external_id,
             external_location.external_id
           ) do
      format_inventory_info(%{
        "id" => inventory_item_id,
        "inventoryLevel" => inventory_info
      })
    else
      _ -> fallback_to_sku_search(shopify_client, product_variant, external_location)
    end
  end

  defp get_inventory(organization_id, _product_variant, external_inventory, external_location) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    shopify_client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case Integrations.Shopify.fetch_location_inventory(
           shopify_client,
           external_inventory.external_id,
           external_location.external_id
         ) do
      {:ok, inventory_info} ->
        format_inventory_info(inventory_info)

      response ->
        response
    end
  end

  defp get_shopify_variant_mapping(mappings) do
    case Enum.find(mappings, fn mapping -> mapping.source == "shopify" end) do
      nil -> :error
      mapping -> {:ok, mapping}
    end
  end

  defp fallback_to_sku_search(shopify_client, product_variant, external_location) do
    case Integrations.Shopify.fetch_location_inventory_by_sku(
           shopify_client,
           "sku:#{product_variant.sku}",
           external_location.external_id
         ) do
      {:ok, %{"nodes" => []}} ->
        {:error, "inventory not fund"}

      {:ok, %{"nodes" => [inventory_info]}} ->
        format_inventory_info(inventory_info)

      response ->
        response
    end
  end

  def format_inventory_info(nil) do
    {:error, "inventory not found"}
  end

  def format_inventory_info(%{
        "inventoryLevel" => nil
      }) do
    {:error, "location not found"}
  end

  def format_inventory_info(%{
        "id" => inventory_external_id,
        "inventoryLevel" => %{
          "quantities" => quantities
        }
      }) do
    {:ok,
     %{
       "external_inventory_id" => inventory_external_id,
       "quantity" =>
         Enum.reduce(
           quantities,
           Decimal.new("0"),
           fn quantity, acc ->
             Decimal.add(acc, quantity["quantity"])
           end
         )
         |> Decimal.to_integer()
         |> validate_quantity()
     }}
  end

  defp validate_quantity(quantity) when quantity < 0 do
    0
  end

  defp validate_quantity(quantity) do
    quantity
  end
end
