defmodule Rms.Integrations.Shopify.Locations.Import do
  alias Rms.Integrations

  def execute(organization_id, location_id) do
    location = Rms.Accounts.get_location!(organization_id, location_id)

    with {:ok, shopify_location} <- fetch_location(location),
         {:ok, _} <-
           Integrations.create_location_mapping(location, shopify_location["id"], "shopify") do
      {:ok, "mapping created"}
    end
  end

  defp fetch_location(location) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(location.organization_id)

    shopify_client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case Rms.Integrations.Shopify.fetch_location(shopify_client, "name:#{location.name}") do
      {:ok, %{"nodes" => [shopify_location]}} ->
        {:ok, shopify_location}

      {:ok, %{"nodes" => []}} ->
        {:error, "shopify_location not found"}

      error ->
        error
    end
  end
end
