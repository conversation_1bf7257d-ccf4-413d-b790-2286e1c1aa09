defmodule Rms.Integrations.Shopify.Orders do
  @moduledoc false

  alias Rms.Commerce.Orders
  alias Rms.Customers.Customer
  alias Rms.Integrations
  alias Rms.Integrations.Shopify
  alias Rms.Repo

  @order_selection """
    name
    id
    createdAt
    updatedAt
    displayFinancialStatus
    app {
      name
    }
    currentTotalPriceSet {
      shopMoney {
        amount
      }
    }
    subtotalPriceSet {
      shopMoney {
        amount
      }
    }
    totalDiscountsSet {
      shopMoney {
        amount
      }
    }
    totalShippingPriceSet {
      shopMoney {
        amount
      }
    }
    customer {
      id
      email
      phone
      firstName
      lastName
    }
    retailLocation {
      id
    }
    fulfillmentOrders(first: 200, query: "status:CLOSED") {
      nodes {
        status
        lineItems(first: 200) {
          nodes {
            image {
              url
            }
            productTitle
            variantTitle
            sku
            variant {
              id
            }
            lineItem {
              currentQuantity
              discountedUnitPriceAfterAllDiscountsSet {
                shopMoney {
                  amount
                }
              }
            }
          }
        }
        assignedLocation {
          location {
            id
          }
        }
        deliveryMethod {
          methodType
        }
      }
    }
    shippingAddress {
      address1
      address2
      city
      provinceCode
      zip
      country
      phone
      firstName
      lastName
    }
  """

  @shipping_methods %{
    "LOCAL" => "delivery",
    "NONE" => "in-store",
    "PICK_UP" => "local-pickup",
    "RETAIL" => "in-store",
    "SHIPPING" => "delivery"
  }
  @default_shipping_method "in-store"

  @doc """
  Imports a Order from Shopify, by global ID

  ## Examples

      iex> import_order(organization_id, "gid://shopify/Order/123")
      {:ok, %Rms.Commerce.Orders.Order{}}

      iex> import_order(organization_id, "gid://shopify/Order/faulty_order")
      {:error, %Ecto.Changeset{}}

      iex> import_order(organization_id, "gid://shopify/Order/another_faulty_order")
      {:error, reason}

  """
  def import_order(organization_id, shopify_id) do
    # Fetch Shopify data outside of transaction
    with {:ok, order_data} <- fetch_shopify_order(organization_id, shopify_id) do
      # Process the order data within a transaction
      import_order_transaction(order_data, organization_id)
    end
  end

  defp fetch_shopify_order(organization_id, shopify_id) do
    credential = Integrations.get_shopify_credential!(organization_id)
    client = Shopify.client(credential.shop, credential.credential)

    case Shopify.get_order!(client, shopify_id, @order_selection, api_version: "2025-01") do
      {:ok, %{"data" => %{"order" => order_data}}} ->
        {:ok, order_data}

      {:error, reason} ->
        {:error, reason}

      error ->
        {:error, {:unexpected_response, error}}
    end
  end

  defp import_order_transaction(
         %{"displayFinancialStatus" => "PAID"} = order_data,
         organization_id
       ) do
    Ecto.Multi.new()
    |> get_location_id_step(order_data, organization_id)
    |> get_customer_id_step(order_data, organization_id)
    |> get_variant_mappings_step(order_data, organization_id)
    |> create_order_step(order_data, organization_id)
    |> Ecto.Multi.run(:emit_event, fn _repo, %{order: order} ->
      Rms.Events.emit("order.imported", Orders.Order.event_payload(order))
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{order: order}} -> {:ok, order}
      {:error, _step, reason, _changes} -> {:error, reason}
    end
  end

  defp import_order_transaction(%{"displayFinancialStatus" => status}, _organization_id) do
    {:error, {:invalid_status, status}}
  end

  defp get_location_id_step(multi, order_data, organization_id) do
    Ecto.Multi.run(multi, :location_id, fn _repo, _changes ->
      get_location_id(order_data, organization_id)
    end)
  end

  defp get_customer_id_step(multi, order_data, organization_id) do
    Ecto.Multi.run(multi, :customer_id, fn _repo, _changes ->
      get_or_create_customer(order_data, organization_id)
    end)
  end

  defp get_variant_mappings_step(multi, order_data, organization_id) do
    Ecto.Multi.run(multi, :variant_mappings, fn _repo, _changes ->
      # Extract all fulfillment orders and collect variant IDs
      variant_ids = extract_variant_ids(order_data)

      # Create a lookup map of variant_id -> %{id: product_variant_id, sku: product_variant_sku}
      mappings =
        Integrations.get_product_variant_details_from_external_ids(
          organization_id,
          variant_ids,
          "shopify"
        )

      {:ok, mappings}
    end)
  end

  defp extract_variant_ids(order_data) do
    fulfillment_orders = get_in(order_data, ["fulfillmentOrders", "nodes"]) || []

    fulfillment_orders
    |> Enum.flat_map(&extract_nodes_from_fulfillment/1)
    |> Enum.filter(&is_binary/1)
  end

  defp extract_nodes_from_fulfillment(fulfillment) do
    nodes = get_in(fulfillment, ["lineItems", "nodes"]) || []
    Enum.map(nodes, fn node -> get_in(node, ["variant", "id"]) end)
  end

  defp create_order_step(multi, order_data, organization_id) do
    Ecto.Multi.run(multi, :order, fn _repo,
                                     %{
                                       location_id: location_id,
                                       customer_id: customer_id,
                                       variant_mappings: variant_mappings
                                     } ->
      fulfillment_orders = get_in(order_data, ["fulfillmentOrders", "nodes"]) || []

      {:ok, reference_at_dt, _} = DateTime.from_iso8601(order_data["createdAt"])
      reference_at_dt = DateTime.truncate(reference_at_dt, :second)

      order_params = %{
        external_id: order_data["id"],
        total_price: extract_price(order_data, ["currentTotalPriceSet"]),
        status: "paid",
        reference_at: reference_at_dt,
        updated_at: order_data["updatedAt"],
        customer_id: customer_id,
        sales_channel: get_in(order_data, ["app", "name"]),
        source: "shopify",
        name: order_data["name"],
        total_items_selling_price: extract_price(order_data, ["subtotalPriceSet"]),
        total_items_list_price: extract_price(order_data, ["subtotalPriceSet"]),
        total_discount: extract_price(order_data, ["totalDiscountsSet"]),
        total_delivery_price: extract_price(order_data, ["totalShippingPriceSet"]),
        shipping_address: format_shipping_address(order_data["shippingAddress"]),
        total_ecommerce_discounts: extract_price(order_data, ["totalDiscountsSet"]),
        total_items_manual_discount: Decimal.new("0"),
        location_id: location_id,
        fulfillments:
          Enum.map(
            fulfillment_orders,
            fn shopify_fulfillment ->
              line_items =
                map_line_items(
                  shopify_fulfillment,
                  location_id,
                  organization_id,
                  variant_mappings,
                  reference_at_dt
                )

              %{
                shipping_method:
                  map_shipping_method(
                    get_in(shopify_fulfillment, ["deliveryMethod", "methodType"])
                  ),
                ecommerce: "shopify",
                external_reference: order_data["id"],
                status: "completed",
                reference_at: reference_at_dt,
                line_items: line_items
              }
            end
          )
      }

      %Orders.Order{organization_id: organization_id}
      |> Orders.Order.changeset(order_params, allowed_location_ids: :all)
      |> Repo.insert()
    end)
  end

  defp get_location_id(order_data, organization_id) do
    order_data
    |> get_shopify_location_id()
    |> map_to_internal_location_id(organization_id)
  end

  defp get_shopify_location_id(order_data) do
    get_in(order_data, ["retailLocation", "id"]) ||
      get_in(order_data, [
        "fulfillmentOrders",
        "nodes",
        Access.at(0),
        "assignedLocation",
        "location",
        "id"
      ])
  end

  defp map_to_internal_location_id(nil, _organization_id),
    do: {:error, {:location_mapping_not_found, nil}}

  defp map_to_internal_location_id(shopify_location_id, organization_id) do
    case Integrations.get_location_mapping(organization_id, "shopify", shopify_location_id) do
      nil -> {:error, {:location_mapping_not_found, shopify_location_id}}
      mapping -> {:ok, mapping.location_id}
    end
  end

  defp get_or_create_customer(order_data, organization_id) do
    order_data
    |> get_in(["customer", "id"])
    |> find_or_create_customer(order_data, organization_id)
  end

  defp find_or_create_customer(nil, _order_data, _organization_id), do: {:ok, nil}

  defp find_or_create_customer(shopify_customer_id, order_data, organization_id) do
    case find_customer_mapping(shopify_customer_id, organization_id) do
      %{customer_id: customer_id} ->
        {:ok, customer_id}

      _ ->
        order_data
        |> extract_customer_data()
        |> create_customer_and_mapping(shopify_customer_id, organization_id)
    end
  end

  defp find_customer_mapping(shopify_customer_id, organization_id) do
    Integrations.get_customer_sync_mapping(organization_id, "shopify", shopify_customer_id)
  end

  defp extract_customer_data(order_data) do
    customer_data = get_in(order_data, ["customer"])
    first_name = get_in(customer_data, ["firstName"]) || ""
    last_name = get_in(customer_data, ["lastName"]) || ""
    name = String.trim("#{first_name} #{last_name}")
    email = get_in(customer_data, ["email"])
    phone = get_in(customer_data, ["phone"])

    %{name: name, email: email, phone: phone}
  end

  defp create_customer_and_mapping(customer_data, shopify_customer_id, organization_id) do
    customer =
      %Customer{organization_id: organization_id}
      |> Customer.shopify_import_changeset(%{
        name: customer_data.name,
        email: customer_data.email,
        primary_phone_number: customer_data.phone
      })
      |> Repo.insert!()

    attrs = %{
      customer_id: customer.id,
      source: "shopify",
      external_id: shopify_customer_id
    }

    case Integrations.create_customer_sync_mapping(organization_id, attrs) do
      {:ok, _mapping} -> {:ok, customer.id}
      {:error, changeset} -> {:error, changeset}
    end
  end

  defp extract_price(order_data, path) do
    case get_in(order_data, path ++ ["shopMoney", "amount"]) do
      nil -> Decimal.new("0")
      amount when is_binary(amount) -> Decimal.new(amount)
      amount when is_number(amount) -> Decimal.new("#{amount}")
      _ -> Decimal.new("0")
    end
  end

  defp format_shipping_address(address) when is_nil(address), do: nil

  defp format_shipping_address(address) do
    %{
      receiver_name: "#{address["firstName"] || ""} #{address["lastName"] || ""}",
      city_name: address["city"],
      state: address["provinceCode"],
      country_name: address["country"],
      street: address["address1"],
      complement: address["address2"],
      zip: address["zip"]
    }
  end

  defp map_shipping_method(method_type) do
    Map.get(@shipping_methods, method_type, @default_shipping_method)
  end

  defp map_line_items(
         shopify_fulfillment,
         location_id,
         organization_id,
         variant_mappings,
         reference_at_dt
       ) do
    line_items_nodes = get_in(shopify_fulfillment, ["lineItems", "nodes"]) || []
    shipping_method = extract_shipping_method(shopify_fulfillment)

    Enum.map(line_items_nodes, fn node ->
      product_variant_details = get_product_variant_details(node, variant_mappings)
      price = extract_price_from_node(node)
      quantity = get_in(node, ["lineItem", "currentQuantity"]) || 1

      base_line_item_attrs = %{
        price: price,
        list_price: price,
        quantity: quantity,
        shipping_method: shipping_method,
        location_id: location_id,
        organization_id: organization_id,
        reference_at: reference_at_dt,
        shipping_settings: %{},
        staff_id: nil
      }

      build_line_item(
        base_line_item_attrs,
        node,
        product_variant_details
      )
    end)
  end

  defp extract_shipping_method(shopify_fulfillment) do
    map_shipping_method(get_in(shopify_fulfillment, ["deliveryMethod", "methodType"]))
  end

  defp get_product_variant_details(node, variant_mappings) do
    external_variant_id = get_in(node, ["variant", "id"])

    if is_binary(external_variant_id) do
      Map.get(variant_mappings, external_variant_id)
    else
      nil
    end
  end

  defp extract_price_from_node(node) do
    price_value =
      get_in(node, [
        "lineItem",
        "discountedUnitPriceAfterAllDiscountsSet",
        "shopMoney",
        "amount"
      ])

    case price_value do
      nil -> Decimal.new("0")
      value when is_binary(value) -> Decimal.new(value)
      value when is_number(value) -> Decimal.new("#{value}")
      _ -> Decimal.new("0")
    end
  end

  defp build_line_item(
         base_attrs,
         node,
         product_variant_details
       ) do
    product_variant_id = if product_variant_details, do: product_variant_details.id, else: nil
    product_variant_sku = if product_variant_details, do: product_variant_details.sku, else: nil

    node_specific_attrs = %{
      sku: node["sku"] || product_variant_sku || "",
      product_name: node["productTitle"] || "",
      variant_name: node["variantTitle"] || "",
      image_url: get_in(node, ["image", "url"]),
      product_variant_id: product_variant_id
    }

    Map.merge(base_attrs, node_specific_attrs)
  end
end
