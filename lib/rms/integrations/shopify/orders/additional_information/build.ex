defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Build do
  alias Rms.Integrations
  alias Rms.Integrations.Shopify.Orders.AdditionalInformations

  def execute(
        %{
          shipping_method: shipping_method,
          organization_id: organization_id
        } = fulfillment
      ) do
    additional_infos = get_requested_info(organization_id, shipping_method)

    additional_infos
    |> Enum.reduce([], fn additional_infos, acc ->
      build_params(additional_infos, acc, fulfillment)
    end)
    |> Enum.reject(fn
      %{value: value} -> is_nil(value)
      _ -> false
    end)
  end

  def execute(_fulfillment) do
    []
  end

  def execute(
        %{
          shipping_method: shipping_method,
          organization_id: organization_id
        } = fulfillment,
        invoice
      ) do
    additional_infos = get_requested_info(organization_id, shipping_method)

    additional_infos
    |> Enum.reduce([], fn additional_infos, acc ->
      build_params(additional_infos, acc, fulfillment, invoice)
    end)
    |> Enum.reject(fn
      %{value: value} -> is_nil(value)
      _ -> false
    end)
  end

  defp build_params("payments", acc, fulfillment) do
    [AdditionalInformations.Payments.build(fulfillment) | acc]
  end

  defp build_params("discounts", acc, fulfillment) do
    AdditionalInformations.Discounts.build(fulfillment) ++ acc
  end

  defp build_params("staff_ID", acc, fulfillment) do
    [AdditionalInformations.Staff.build_id(fulfillment) | acc]
  end

  defp build_params("staff_name", acc, fulfillment) do
    [AdditionalInformations.Staff.build_name(fulfillment) | acc]
  end

  defp build_params("shipping_address", acc, fulfillment) do
    AdditionalInformations.ShippingAddress.build(fulfillment) ++ acc
  end

  defp build_params("location_id", acc, fulfillment) do
    AdditionalInformations.Location.build(fulfillment) ++ acc
  end

  defp build_params("iglu_order_id", acc, fulfillment) do
    [%{key: "iglu_order_id", value: "#{fulfillment.order_id}"} | acc]
  end

  defp build_params("order_type", acc, fulfillment) do
    [%{key: "type", value: fulfillment.shipping_method} | acc]
  end

  defp build_params("source", acc, _fulfillment) do
    [%{key: "source", value: "iglu"} | acc]
  end

  defp build_params("discount_info", acc, fulfillment) do
    AdditionalInformations.DiscountInfo.execute(fulfillment) ++ acc
  end

  defp build_params("customer_info", acc, fulfillment) do
    [AdditionalInformations.CustomerInfo.execute(fulfillment) | acc]
  end

  defp build_params(_, acc, _fulfillment) do
    acc
  end

  defp build_params("payments", acc, _fulfillment, invoice) do
    [AdditionalInformations.Payments.build(invoice) | acc]
  end

  defp build_params("invoice", acc, _fulfillment, invoice) do
    AdditionalInformations.Invoice.build(invoice) ++ acc
  end

  defp build_params("staff_ID", acc, fulfillment, _invoice) do
    [AdditionalInformations.Staff.build_id(fulfillment) | acc]
  end

  defp build_params("staff_name", acc, fulfillment, _invoice) do
    [AdditionalInformations.Staff.build_name(fulfillment) | acc]
  end

  defp build_params("shipping_address", acc, fulfillment, _invoice) do
    AdditionalInformations.ShippingAddress.build(fulfillment) ++ acc
  end

  defp build_params("discounts", acc, fulfillment, _invoice) do
    AdditionalInformations.Discounts.build(fulfillment) ++ acc
  end

  defp build_params("location_id", acc, fulfillment, _invoice) do
    AdditionalInformations.Location.build(fulfillment) ++ acc
  end

  defp build_params("iglu_order_id", acc, fulfillment, _invoice) do
    [%{key: "iglu_order_id", value: "#{fulfillment.order_id}"} | acc]
  end

  defp build_params("order_type", acc, fulfillment, _invoice) do
    [%{key: "type", value: fulfillment.shipping_method} | acc]
  end

  defp build_params("invoice_xml", acc, _fulfillment, invoice) do
    [%{key: "xml", value: invoice.xml} | acc]
  end

  defp build_params("source", acc, _fulfillment, _invoice) do
    [%{key: "source", value: "iglu"} | acc]
  end

  defp build_params(_, acc, _fulfillment, _invoice) do
    acc
  end

  defp get_requested_info(organization_id, order_type) do
    case Integrations.get_shopify_additional_information!(organization_id, order_type) do
      %AdditionalInformations{fields: fields} ->
        fields ++ ["iglu_order_id"]

      _ ->
        [
          "iglu_order_id",
          "payments",
          "source",
          "order_type",
          "location_id",
          "shipping_address",
          "staff_name",
          "staff_ID",
          "invoice",
          "discounts",
          "discount_info",
          "customer_info"
        ]
    end
  end
end
