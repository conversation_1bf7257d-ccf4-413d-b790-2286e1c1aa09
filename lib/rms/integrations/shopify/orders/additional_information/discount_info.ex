defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.DiscountInfo do
  @preload [order: [:discounts, :customer], line_items: [:discounts, :product_variant]]

  def execute(fulfillment) do
    fulfillment = Rms.Repo.preload(fulfillment, @preload)

    [
      %{
        key: "line_items_discount_info",
        value: Jason.encode!(build_line_items_discount_info(fulfillment))
      },
      %{
        key: "order_discounts_info",
        value: Jason.encode!(build_order_discount_info(fulfillment))
      }
    ]
  end

  def build_order_discount_info(fulfillment) do
    Enum.map(fulfillment.order.discounts, &format_discount/1)
  end

  defp build_line_items_discount_info(fulfillment) do
    fulfillment.line_items
    |> Enum.map(fn line_item ->
      %{
        sku: line_item.product_variant.sku,
        quantity: line_item.quantity,
        discounts: Enum.map(line_item.discounts, &format_discount/1)
      }
    end)
    |> Enum.filter(fn ln -> length(ln.discounts) > 0 end)
  end

  defp format_discount(discount) do
    %{type: discount.type, value: discount.value, description: discount.description}
  end
end
