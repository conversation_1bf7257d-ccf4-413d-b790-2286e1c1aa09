defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Discounts do
  def build(%{
        order: %{discounts: discounts}
      }) do
    [
      %{
        key: "discount_applied_by_iglu",
        value: "true"
      },
      %{
        key: "discount_codes",
        value: reduce_codes(discounts)
      }
    ]
  end

  defp reduce_codes(discounts) when is_list(discounts) do
    discounts
    |> Enum.map_join(",", fn
      %{type: type, value: value} when type in ["coupon", "automatic_ecommerce"] ->
        value

      discount ->
        discount.description
    end)
  end

  defp reduce_codes(_) do
    []
  end
end
