defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Invoice do
  def build(invoice) do
    [
      %{key: "invoice-issuer", value: "iglu:vinco"},
      %{key: "nfce-id", value: "#{invoice.id}"},
      %{key: "nfce-status", value: "AUTORIZADA"},
      %{key: "nfce-serie", value: "#{invoice.serie.invoice_serie}"},
      %{key: "nfce-number", value: "#{invoice.invoice_number}"},
      %{key: "nfce-key", value: "#{invoice.df_key}"},
      %{key: "nfce-creationDate", value: "#{date_time(invoice.authorized_at)}"}
    ]
  end

  defp date_time(date) do
    month = String.pad_leading("#{date.month}", 2, "0")
    day = String.pad_leading("#{date.day}", 2, "0")
    hour = String.pad_leading("#{date.hour}", 2, "0")
    minute = String.pad_leading("#{date.minute}", 2, "0")
    second = String.pad_leading("#{date.second}", 2, "0")

    "#{date.year}-#{month}-#{day}T#{hour}:#{minute}:#{second}-03:00"
  end
end
