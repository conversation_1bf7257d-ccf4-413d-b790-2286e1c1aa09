defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Location do
  def build(%{
        order: %{
          location: %{location_mappings: location_mappings, name: name}
        }
      }) do
    [
      %{key: "location_ID", value: filter_shopify_mapping(location_mappings)},
      %{key: "location_name", value: name}
    ]
  end

  def build(_) do
    [%{key: "location_ID", value: ""}, %{key: "location_name", value: ""}]
  end

  defp filter_shopify_mapping(mappings) do
    case Enum.find(mappings, fn mapping ->
           mapping.source == "shopify"
         end) do
      nil ->
        ""

      %{external_id: "gid://shopify/Location/" <> external_id} ->
        external_id

      %{external_id: external_id} ->
        external_id
    end
  end
end
