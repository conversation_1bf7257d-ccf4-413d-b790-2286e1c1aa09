defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Payments do
  def build(params) do
    organization_id = get_organization_id(params)
    ld_context_map = :ldclient_context.new(to_string(organization_id), "organization_id")
    ld_context = :ldclient_context.new_from_map(ld_context_map)

    case :ldclient.variation("new-shopify-payments", ld_context, false) do
      true -> Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV2.build(params)
      false -> Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV1.build(params)
    end
  end

  defp get_organization_id(%{order: %{organization_id: organization_id}}), do: organization_id

  defp get_organization_id(%{
         invoice_payments: [%{payment: %{organization_id: organization_id}} | _]
       }),
       do: organization_id

  defp get_organization_id(_), do: nil
end

defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV1 do
  def build(%{
        order: %{transaction: %{payments: payments}}
      }) do
    payments_string =
      Enum.map_join(payments, ",", fn payment ->
        build_payment(payment)
      end)

    %{key: "iglu_payments", value: "[#{payments_string}]"}
  end

  def build(%{invoice_payments: invoice_payments}) do
    payments_string =
      Enum.map_join(invoice_payments, ",", fn invoice_payment ->
        build_payment(invoice_payment.payment)
      end)

    %{key: "iglu_payments", value: "[#{payments_string}]"}
  end

  def build(_) do
    %{key: "iglu_payments", value: "[]"}
  end

  def build_payment(payment) when payment.method in ["credit_card", "debit_card"] do
    "{\"method\":\"#{format_method(payment.method)}\",\"installments\":\"#{payment.metadata["installments"] || 1}\",\"nsu\":\"#{payment.metadata["nsu"]}\",\"aut\":\"#{payment.metadata["aut"]}\",\"amount\":#{payment.amount},\"conciliation\":#{was_consiliation?(payment.metadata)}}"
  end

  def build_payment(payment) when payment.method in ["pix"] do
    case was_consiliation?(payment.metadata) do
      true ->
        "{\"method\":\"#{format_method(payment.method)}\",\"installments\":\"1\",\"amount\":#{payment.amount},\"conciliation\":#{true},\"pix_id\":#{payment.metadata["nsu"]}}"

      _ ->
        "{\"method\":\"#{format_method(payment.method)}\",\"installments\":\"1\",\"amount\":#{payment.amount},\"conciliation\":#{false}}"
    end
  end

  def build_payment(payment) do
    "{\"method\":\"#{format_method(payment.method)}\",\"installments\":\"1\",\"amount\":#{payment.amount},\"conciliation\":false}"
  end

  defp format_method(payment_method) do
    case payment_method do
      "credit_card" -> "credit"
      "debit_card" -> "debit"
      "gift_card" -> "gift-card"
      "payment_link" -> "link"
      "pix" -> "pix"
      "cash" -> "cash"
      "return_credit" -> "return-credit"
    end
  end

  defp was_consiliation?(metadata) do
    not Map.has_key?(metadata, "store_payment_receipt")
  end
end
