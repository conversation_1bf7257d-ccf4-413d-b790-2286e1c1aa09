defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV2 do
  def build(%{order: order}) do
    order = Rms.Repo.preload(order, transaction: [payments: [:external_payment_reference]])

    payments_list =
      Enum.flat_map(order.transaction.payments, fn payment ->
        payment
        |> build_payment()
        |> List.wrap()
      end)

    %{key: "iglu_payments", value: Jason.encode!(payments_list)}
  end

  def build(%{invoice_payments: invoice_payments}) do
    invoice_payments = Rms.Repo.preload(invoice_payments, payment: [:external_payment_reference])

    payments_by_id =
      invoice_payments
      |> Enum.map(& &1.payment)
      |> Map.new(&{&1.id, &1})

    payments_list =
      Enum.flat_map(invoice_payments, fn invoice_payment ->
        payments_by_id[invoice_payment.payment_id]
        |> build_payment()
        |> List.wrap()
      end)

    %{key: "iglu_payments", value: Jason.encode!(payments_list)}
  end

  def build(_) do
    %{key: "iglu_payments", value: "[]"}
  end

  def build_payment(payment) when payment.method in ["credit_card", "debit_card"] do
    %{
      method: format_method(payment.method),
      installments: to_string(payment.metadata["installments"] || 1),
      nsu: payment.metadata["nsu"],
      aut: payment.metadata["aut"],
      amount: payment.amount,
      conciliation: was_consiliation?(payment.metadata)
    }
  end

  def build_payment(payment) when payment.method in ["pix"] do
    base_map = %{
      method: format_method(payment.method),
      installments: to_string(1),
      amount: payment.amount,
      conciliation: was_consiliation?(payment.metadata)
    }

    case was_consiliation?(payment.metadata) do
      true -> Map.put(base_map, :pix_id, payment.metadata["nsu"])
      false -> base_map
    end
  end

  def build_payment(
        %{
          method: "payment_link",
          metadata: %{"charges" => charges},
          external_payment_reference: %{partner: "pagarme", external_id: external_id}
        } = payment
      )
      when is_list(charges) and length(charges) > 0 do
    base = %{
      method: format_method(payment.method),
      amount: payment.amount,
      installments: to_string(1),
      pagarmeId: external_id,
      conciliation: false
    }

    Enum.map(charges, fn
      %{
        "tid" => tid,
        "nsu" => nsu,
        "aut" => aut
      } = charge ->
        Map.merge(base, %{
          installments: charge["installments"] || 1,
          amount: Decimal.new(charge["amount"]),
          nsu: nsu,
          tid: tid,
          aut: aut
        })

      _ ->
        base
    end)
  end

  def build_payment(
        %{
          method: "payment_link",
          external_payment_reference: %{partner: "pagarme", external_id: external_id}
        } = payment
      ) do
    %{
      method: format_method(payment.method),
      installments: to_string(1),
      amount: payment.amount,
      pagarmeId: external_id,
      conciliation: false
    }
  end

  def build_payment(%{method: "payment_link"} = payment) do
    %{
      method: format_method(payment.method),
      installments: to_string(1),
      amount: payment.amount,
      conciliation: is_nil(payment.external_payment_reference)
    }
  end

  def build_payment(payment) do
    %{
      method: format_method(payment.method),
      installments: to_string(1),
      amount: payment.amount,
      conciliation: false
    }
  end

  defp format_method(payment_method) do
    case payment_method do
      "credit_card" -> "credit"
      "debit_card" -> "debit"
      "gift_card" -> "gift-card"
      "payment_link" -> "link"
      "pix" -> "pix"
      "cash" -> "cash"
      "return_credit" -> "return-credit"
    end
  end

  defp was_consiliation?(metadata) do
    not Map.has_key?(metadata, "store_payment_receipt")
  end
end
