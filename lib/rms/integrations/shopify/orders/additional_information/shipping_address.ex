defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.ShippingAddress do
  def build(%{
        order: %{shipping_address: nil}
      }) do
    []
  end

  def build(%{
        order: %{shipping_address: shipping_address}
      }) do
    [
      %{
        key: "pos_aditional_info_extra_endereco",
        value: shipping_address.street || "Não Informado"
      },
      %{
        key: "pos_aditional_info_extra_numero",
        value: shipping_address.number || "Não Informado"
      },
      %{
        key: "pos_aditional_info_extra_complemento",
        value: shipping_address.complement || "Não Informado"
      },
      %{
        key: "pos_aditional_info_extra_cidade",
        value: shipping_address.city_name || "Não Informado"
      },
      %{
        key: "pos_aditional_info_extra_bairro",
        value: shipping_address.neighborhood || "Não Informado"
      },
      %{key: "pos_aditional_info_extra_cep", value: shipping_address.zip || "Não Informado"},
      %{
        key: "pos_aditional_info_extra_address_state",
        value: shipping_address.state || "Não Informado"
      },
      %{
        key: "pos_aditional_info_extra_endereco_completo",
        value:
          "#{shipping_address.street}, #{shipping_address.number}, #{shipping_address.neighborhood}, #{shipping_address.city_name}, #{shipping_address.state}, #{shipping_address.zip}"
      }
    ]
  end

  def build(_) do
    []
  end
end
