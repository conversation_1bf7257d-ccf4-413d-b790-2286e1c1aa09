defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.Staff do
  def build_id(%{
        order: %{staff: %{external_id: external_id}}
      }) do
    %{key: "staff_ID", value: external_id}
  end

  def build_id(_) do
    %{key: "staff_ID", value: ""}
  end

  def build_name(%{
        order: %{staff: %{name: name}}
      }) do
    %{key: "staff_name", value: name}
  end

  def build_name(_) do
    %{key: "staff_name", value: ""}
  end
end
