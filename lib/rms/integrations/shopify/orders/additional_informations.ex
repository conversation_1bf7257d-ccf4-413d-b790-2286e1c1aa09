defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations do
  use Ecto.Schema

  import Ecto.Changeset

  alias Rms.Accounts.Organization

  schema "shopify_order_additional_infos" do
    field :order_type, :string
    field :fields, {:array, :string}, default: []

    belongs_to :organization, Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(additional_info, attrs) do
    additional_info
    |> cast(attrs, [:order_type, :fields])
    |> validate_required([:order_type, :fields])
    |> validate_inclusion(:order_type, ~w(in-store local-pickup delivery))
    |> foreign_key_constraint(:organization)
  end

  @doc false
  def update_changeset(additional_info, attrs) do
    additional_info
    |> cast(attrs, [:fields])
  end
end
