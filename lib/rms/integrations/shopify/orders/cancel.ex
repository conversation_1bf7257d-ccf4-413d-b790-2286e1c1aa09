defmodule Rms.Integrations.Shopify.Orders.Cancel do
  def execute(organization_id, external_reference, cancelation_params) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, order_data} <-
           get_shopify_order(shopify_client, external_reference),
         {:ok, _} <- cancel_fulfillment_orders(shopify_client, order_data, cancelation_params),
         {:ok, _} <- cancel_order(shopify_client, order_data, cancelation_params) do
      {:ok, "shopify order cancelled"}
    end
  end

  defp get_shopify_order(shopify_client, external_reference) do
    with {:ok, %{"data" => %{"order" => order_data}}} <-
           Rms.Integrations.Shopify.get_order!(
             shopify_client,
             external_reference,
             fulfillments_selection()
           ),
         false <- is_nil(order_data) do
      {:ok, order_data}
    else
      _ ->
        {:error, "shopify order not found"}
    end
  end

  defp cancel_fulfillment_orders(shopify_client, order_data, _cancelation_params) do
    result =
      Enum.reduce(order_data["fulfillmentOrders"]["nodes"], [], fn
        %{
          "fulfillments" => %{
            "nodes" => nodes
          }
        },
        acc ->
          nodes ++ acc

        _, acc ->
          acc
      end)
      |> Enum.reduce_while({:ok, []}, fn f, {:ok, acc} ->
        case Rms.Integrations.Shopify.cancel_fulfillment(shopify_client, f["id"]) do
          {:error, _} = error -> {:halt, error}
          {:ok, fulfillment} -> {:cont, [fulfillment | acc]}
        end
      end)

    case result do
      {:error, _} = error -> error
      [] -> {:error, %{reason: "can cancel a fulfillment", stacktrace: "#{__MODULE__}"}}
      line_items -> {:ok, line_items}
    end
  end

  defp cancel_order(shopify_client, order_data, _cancelation_params) do
    Rms.Integrations.Shopify.cancel_order(shopify_client, %{
      orderId: order_data["id"],
      refund: false,
      restock: true,
      reason: "OTHER",
      notifyCustomer: false
    })
  end

  defp fulfillments_selection() do
    """
      fulfillmentOrders(first: 250) {
          nodes {
              id
              status
              fulfillments(first: 250) {
                  nodes {
                      id
                      status
                  }
              }
              supportedActions {
                  action
                  externalUrl
              }
          }
      }
      cancelledAt
      id
    """
  end
end
