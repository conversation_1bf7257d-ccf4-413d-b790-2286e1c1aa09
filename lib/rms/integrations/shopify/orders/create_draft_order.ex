defmodule Rms.Integrations.Shopify.Orders.CreateDraftOrder do
  alias Rms.Integrations
  alias Rms.Integrations.Shopify
  alias Rms.Integrations.Shopify.Carts.Simulate, as: CartSimulate

  alias Rms.Integrations.Shopify.Orders.DraftOrder.BuildParams

  require Logger

  @preload [
    :shipping_settings,
    order: [:discounts, customer: [:addresses], location: [:address]],
    line_items: [:discounts, product_variant: [:product_variant_mappings]]
  ]

  def run(organization_id, fulfillment_id, external_customer_id) do
    fulfillment =
      organization_id
      |> Rms.Commerce.Fulfillments.get_fulfillment!(fulfillment_id)
      |> Rms.Repo.preload(@preload)

    shopify_credential = Integrations.get_shopify_credential!(fulfillment.organization_id)
    client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    issue_fulfillment(client, fulfillment, external_customer_id)
  end

  defp issue_fulfillment(client, %{shipping_method: "in-store"} = fulfillment, customer_id) do
    case create_draft_order(client, customer_id, fulfillment) do
      {:error,
       {:unknown_error_code,
        %{"field" => ["email"], "message" => "Email contains an invalid domain name"}}} ->
        handle_invalid_email(
          client,
          customer_id,
          fulfillment
        )

      {:ok, draft_order_id} ->
        Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{
          external_reference: draft_order_id
        })

      error ->
        error
    end
  end

  defp issue_fulfillment(client, %{shipping_method: "delivery"} = fulfillment, customer_id) do
    with {:ok, draft_order_id} <-
           create_draft_order(
             client,
             customer_id,
             fulfillment
           ) do
      Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{
        external_reference: draft_order_id
      })
    end
  end

  defp issue_fulfillment(_client, _fulfillment, _customer_id) do
    {:discard, "invalid shipping method"}
  end

  defp handle_invalid_email(
         client,
         customer_id,
         fulfillment
       ) do
    with {:ok, %{"email" => original_email}} <- Shopify.fetch_customer(client, customer_id),
         {:ok, _} <- Shopify.update_customer(client, %{id: customer_id, email: ""}) do
      with {:ok, draft_order_id} <- create_draft_order(client, customer_id, fulfillment) do
        Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{
          external_reference: draft_order_id
        })
      end
      |> tap(fn _ ->
        Shopify.update_customer(client, %{id: customer_id, email: original_email})
      end)
    else
      {:ok, %{items: []}} ->
        {:error, "Customer not found for ID: #{customer_id}"}

      error ->
        error
    end
  end

  defp create_draft_order(client, customer_id, fulfillment) do
    with {:ok, input} <- BuildParams.execute(fulfillment, customer_id: customer_id),
         {:ok, %{"id" => id}} <- Shopify.create_draft_order(client, input) do
      {:ok, id}
    end
  end

  def simulate_and_check_price(fulfillment) do
    cart_info = CartSimulate.build_simulate_cart_info(fulfillment)

    simulation_total_price =
      with simulated_cart <- CartSimulate.execute(fulfillment.organization_id, cart_info),
           new_cart <-
             Rms.Commerce.Discounts.Engine.execute(
               fulfillment.organization_id,
               simulated_cart,
               cart_info
             ) do
        Map.get(new_cart, "total_price")
      end

    fulfillment_total_amount =
      Decimal.sub(
        fulfillment.order.total_items_selling_price,
        fulfillment.order.total_ecommerce_discounts
      )

    result = simulation_total_price == fulfillment_total_amount

    unless result do
      Logger.info("""
      [#{__MODULE__}] Divergent simulation values:
        fulfillment_id: #{fulfillment.id}
        fulfillment_total_amount: #{fulfillment_total_amount}
        simulation_total_price: #{simulation_total_price}
      """)
    end

    {result, fulfillment.order.total_ecommerce_discounts}
  end

  def should_handle?(fulfillment) do
    has_order_discounts? = length(fulfillment.order.discounts) > 0
    has_line_items_discounts? = Enum.any?(fulfillment.line_items, &(length(&1.discounts) > 0))

    (has_order_discounts? or has_line_items_discounts?) and
      not (has_order_discounts? and has_line_items_discounts?)
  end

  def discount_code_amount(client, code) do
    with {:ok, %{"codeDiscountNodeByCode" => result}} <- Shopify.get_discount_code(client, code) do
      case result["codeDiscount"]["customerGets"]["value"] do
        %{"percentage" => percentage} -> {:percentage, percentage}
        %{"amount" => %{"amount" => amount}} -> {:fixed, amount}
      end
    end
  end
end
