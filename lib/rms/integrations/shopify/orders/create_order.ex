defmodule Rms.Integrations.Shopify.Orders.CreateOrder do
  alias Rms.Commerce.Fulfillments
  alias Rms.Integrations.Shopify.Orders.CreateOrder.BuildParams
  alias Rms.Integrations.Shopify
  alias Rms.Integrations

  require Logger

  @preload [
    :shipping_settings,
    order: [
      :discounts,
      [transaction: :payments],
      customer: [:addresses, :customer_mappings],
      location: [:location_mappings]
    ],
    line_items: [:discounts, product_variant: [:product_variant_mappings]]
  ]

  def run(organization_id, fulfillment_id) do
    fulfillment =
      organization_id
      |> Fulfillments.get_fulfillment!(fulfillment_id)
      |> Rms.Repo.preload(@preload)

    shopify_credential = Integrations.get_shopify_credential!(fulfillment.organization_id)
    client = Shopify.client(shopify_credential.shop, shopify_credential.credential)

    do_run(client, fulfillment)
  end

  defp do_run(client, fulfillment) do
    with {:ok, order_params} <- BuildParams.execute(fulfillment),
         {:ok, %{"id" => id, "name" => name}} <- Shopify.create_order(client, order_params) do
      Fulfillments.set_completed(fulfillment, %{
        external_reference: id,
        metadata: %{order_name: name}
      })
    else
      error ->
        log_error(fulfillment, error)
        error
    end
  end

  defp log_error(fulfillment, error) do
    Logger.error(
      "[#{__MODULE__}] Failed to create order for fulfillment #{fulfillment.id}: #{inspect(error)}"
    )
  end
end
