defmodule Rms.Integrations.Shopify.Orders.CreateOrder.BuildParams do
  alias Rms.Integrations.Shopify.Orders.CreateOrder.Transactions
  alias Rms.Integrations.Shopify.Orders.CreateOrder.LineItems
  alias Rms.Integrations.Shopify.Orders.CreateOrder.ShippingAddress
  alias Rms.Integrations.Shopify.Orders.CreateOrder.Fulfillment
  alias Rms.Integrations.Shopify.Orders.CreateOrder.ShippingLines
  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.Build

  def execute(%{shipping_method: "delivery"} = fulfillment) do
    if Rms.FeatureFlag.should_return_mocked_delivery_options?(fulfillment.organization_id) do
      with {:ok, base_input} <- build_base_input(fulfillment),
           {:ok, shipping_address} <- ShippingAddress.execute(fulfillment) do
        {:ok,
         base_input
         |> Map.put(:shippingAddress, shipping_address)
         |> Map.put(:financialStatus, "PAID")}
      end
    else
      with {:ok, base_input} <- build_base_input(fulfillment),
           {:ok, shipping_lines} <- ShippingLines.execute(fulfillment),
           {:ok, shipping_address} <- ShippingAddress.execute(fulfillment) do
        {:ok,
         base_input
         |> Map.put(:shippingAddress, shipping_address)
         |> Map.put(:shippingLines, shipping_lines)
         |> Map.put(:financialStatus, "PAID")}
      end
    end
  end

  def execute(fulfillment) do
    with {:ok, base_input} <- build_base_input(fulfillment),
         {:ok, fulfillment_attr} <- Fulfillment.execute(fulfillment) do
      {:ok, Map.put(base_input, :fulfillment, fulfillment_attr)}
    end
  end

  def build_base_input(fulfillment) do
    with {:ok, line_items} <- LineItems.execute(fulfillment),
         {:ok, transactions} <- Transactions.execute(line_items) do
      %{}
      |> Map.put(:lineItems, line_items)
      |> Map.put(:transactions, transactions)
      |> Map.put(:customAttributes, Build.execute(fulfillment))
      |> Map.put(:note, safe_notes_field(fulfillment))
      |> maybe_put_purchase_entity(fulfillment)
      |> then(fn input -> {:ok, input} end)
    end
  end

  def maybe_put_purchase_entity(map, %{
        order: %{customer: %{customer_mappings: customer_mappings}}
      }) do
    case Enum.find(customer_mappings, &(&1.source == "shopify")) do
      %{external_id: customer_id} -> Map.put(map, :customerId, customer_id)
      nil -> map
    end
  end

  def maybe_put_purchase_entity(map, _fulfillment), do: map

  def safe_notes_field(fulfillment),
    do: (fulfillment.order.metadata || %{}) |> Map.get(:notes, "")
end
