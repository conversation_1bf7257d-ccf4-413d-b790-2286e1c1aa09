defmodule Rms.Integrations.Shopify.Orders.CreateOrder.Fulfillment do
  def execute(fulfillment) do
    with {:ok, external_id} <- fetch_location_id(fulfillment.order.location) do
      {:ok, %{locationId: external_id}}
    end
  end

  def fetch_location_id(location) do
    case Enum.find(location.location_mappings, &(&1.location_id == location.id)) do
      nil -> {:error, %{reason: "Location mapping not found"}}
      mapping -> {:ok, mapping.external_id}
    end
  end
end
