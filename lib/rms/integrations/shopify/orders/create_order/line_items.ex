defmodule Rms.Integrations.Shopify.Orders.CreateOrder.LineItems do
  @mapping_error "Mapping not found for Shopify variant"
  @empty_line_items_error "At least 1 line item is required"

  @currency_code "BRL"

  def execute(fulfillment) do
    result =
      Enum.reduce_while(fulfillment.line_items, [], fn line_item, acc ->
        case build_line_item(line_item) do
          {:error, _} = error -> {:halt, error}
          {:ok, line_item} -> {:cont, [line_item | acc]}
        end
      end)

    case result do
      {:error, _} = error -> error
      [] -> {:error, %{reason: @empty_line_items_error, stacktrace: "#{__MODULE__}"}}
      line_items -> {:ok, line_items}
    end
  end

  defp build_line_item(line_item) do
    case fetch_mapping(line_item.product_variant.product_variant_mappings) do
      :error -> {:error, %{reason: @mapping_error, stacktrace: "#{__MODULE__}"}}
      {:ok, mapping} -> {:ok, build_line_item(line_item, mapping)}
    end
  end

  defp build_line_item(line_item, %{external_id: variant_id}) do
    %{}
    |> Map.put(:quantity, line_item.quantity)
    |> Map.put(:variantId, variant_id)
    |> Map.put(:priceSet, %{shopMoney: build_price_set(line_item)})
  end

  defp build_price_set(line_item) do
    %{}
    |> Map.put(:amount, Decimal.to_float(line_item.price))
    |> Map.put(:currencyCode, @currency_code)
  end

  defp fetch_mapping(product_variant_mappings) do
    case Enum.find(product_variant_mappings, &(&1.source == "shopify")) do
      nil -> :error
      mapping -> {:ok, mapping}
    end
  end
end
