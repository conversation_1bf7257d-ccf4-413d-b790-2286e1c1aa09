defmodule Rms.Integrations.Shopify.Orders.CreateOrder.ShippingLines do
  def execute(%{shipping_settings: nil}), do: {:error, "Shipping Settings not found"}

  def execute(%{shipping_settings: %{settings: nil}}),
    do: {:error, "Shipping Settings not found"}

  def execute(%{shipping_settings: shipping_settings}) do
    {:ok,
     [
       %{
         priceSet: %{
           shopMoney: %{
             amount: String.to_float(shipping_settings.settings["estimatedCost"]["amount"]),
             currencyCode: "BRL"
           }
         },
         title: shipping_settings.settings["title"]
       }
     ]}
  end
end
