defmodule Rms.Integrations.Shopify.Orders.DraftOrder.AppliedDiscount do
  alias Rms.Integrations.Shopify

  # Esse módulo não está bem estruturado e precisa ser refeito. Ele foi adaptado para suportar casos específicos de aplicação de desconto.
  def execute(fulfillment, client, all_automatic_discounts) do
    has_no_gift = Enum.all?(fulfillment.line_items, &(&1.is_gift == false))

    cond do
      all_automatic_discounts != Decimal.new("0.00") and
        fulfillment.order.total_items_manual_discount == Decimal.new("0.00") and
        has_coupon?(fulfillment) and has_no_gift ->
        build_discount_map(Decimal.new("0.0"), fulfillment)

      Decimal.compare(all_automatic_discounts, Decimal.new("0.00")) == :eq ->
        Decimal.sub(fulfillment.order.total_discount, all_automatic_discounts)
        |> build_discount_map(fulfillment)

      !has_no_gift and
          Decimal.compare(all_automatic_discounts, fulfillment.order.total_discount) == :eq ->
        fulfillment
        |> compute_gift_discount(client)
        |> build_discount_map(fulfillment)

      true ->
        execute_with_automatic_discount(fulfillment, client, all_automatic_discounts)
    end
  end

  defp build_discount_map(%Decimal{} = discount, fulfillment) do
    line_items_discounts_descriptions =
      Enum.flat_map(fulfillment.line_items, fn lt -> Enum.map(lt.discounts, & &1.description) end)

    discounts =
      Enum.filter(
        fulfillment.order.discounts,
        &(&1.type not in ["automatic_ecommerce", "coupon"])
      )

    %{
      description:
        discounts
        |> Enum.map_join(" | ", & &1.description)
        |> Kernel.<>(" | ")
        |> Kernel.<>(Enum.map_join(line_items_discounts_descriptions, " | ", & &1)),
      value:
        discount
        |> Decimal.abs()
        |> Decimal.to_float()
        |> Float.round(2),
      valueType: "FIXED_AMOUNT"
    }
  end

  def execute_with_automatic_discount(fulfillment, client, all_automatic_discounts) do
    discounts =
      Enum.filter(
        fulfillment.order.discounts,
        &(&1.type not in ["automatic_ecommerce", "coupon"])
      )

    line_items_discounts_descriptions =
      Enum.flat_map(fulfillment.line_items, fn lt -> Enum.map(lt.discounts, & &1.description) end)

    total_discount = fulfillment.order.total_discount

    total_ecommerce_discounts = fulfillment.order.total_ecommerce_discounts

    line_items_addons_discount =
      fulfillment.line_items
      |> Enum.filter(& &1.is_gift)
      |> Enum.reduce(Decimal.new("0"), fn ln, acc ->
        Decimal.add(acc, Decimal.mult(ln.list_price, ln.quantity))
      end)

    discount =
      all_automatic_discounts
      |> Decimal.add(total_ecommerce_discounts)
      |> Decimal.sub(total_discount)
      |> Decimal.abs()

    discount =
      with coupon when not is_nil(coupon) <- get_coupon(fulfillment),
           ### Como não enviamos os insumos para o shopify, não conseguimos simular quanto o cupom aplicou em cada um deles
           {:percentage, percentage} <- discount_code_amount(client, coupon.value) do
        percentage = round(percentage * 100)

        coupon_gift_applied_percentage =
          Decimal.sub(
            line_items_addons_discount,
            line_items_addons_discount
            |> Decimal.mult(percentage)
            |> Decimal.div(100)
          )

        Decimal.add(discount, coupon_gift_applied_percentage)
      else
        {:fixed, _fixed} ->
          discount = Decimal.add(all_automatic_discounts, total_ecommerce_discounts)

          if discount == total_discount do
            all_automatic_discounts
          else
            discount
          end

        _ ->
          if discount == total_discount do
            all_automatic_discounts
          else
            Decimal.add(discount, line_items_addons_discount)
          end
      end

    %{
      description:
        discounts
        |> Enum.map_join(" | ", & &1.description)
        |> Kernel.<>(" | ")
        |> Kernel.<>(Enum.map_join(line_items_discounts_descriptions, " | ", & &1)),
      value:
        discount
        |> Decimal.abs()
        |> Decimal.to_float()
        |> Float.round(2),
      valueType: "FIXED_AMOUNT"
    }
  end

  def compute_gift_discount(fulfillment, client) do
    line_items_addons_discount =
      fulfillment.line_items
      |> Enum.filter(& &1.is_gift)
      |> Enum.reduce(Decimal.new("0"), fn ln, acc ->
        Decimal.add(acc, Decimal.mult(ln.list_price, ln.quantity))
      end)

    with coupon when not is_nil(coupon) <- get_coupon(fulfillment),
         {:percentage, percentage} <- discount_code_amount(client, coupon.value) do
      percentage = round(percentage * 100)

      coupon_gift_applied_percentage =
        Decimal.sub(
          line_items_addons_discount,
          line_items_addons_discount
          |> Decimal.mult(percentage)
          |> Decimal.div(100)
        )

      Decimal.new(coupon_gift_applied_percentage)
    else
      _ -> line_items_addons_discount
    end
  end

  def get_coupon(fulfillment) do
    Enum.find(fulfillment.order.discounts, &(&1.type == "coupon"))
  end

  def has_coupon?(fulfillment) do
    Enum.any?(fulfillment.order.discounts, &(&1.type == "coupon"))
  end

  def discount_code_amount(client, code) do
    with {:ok, %{"codeDiscountNodeByCode" => result}} <- Shopify.get_discount_code(client, code) do
      case result["codeDiscount"]["customerGets"]["value"] do
        %{"percentage" => percentage} -> {:percentage, percentage}
        %{"amount" => %{"amount" => amount}} -> {:fixed, amount}
      end
    end
  end
end
