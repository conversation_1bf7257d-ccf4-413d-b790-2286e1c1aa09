defmodule Rms.Integrations.Shopify.Orders.DraftOrder.BuildParams do
  alias Rms.Integrations.Shopify.Orders.DraftOrder.{
    LineItems,
    LocalizationExtensions,
    Tags,
    ShippingLine,
    ShippingAddress
  }

  @in_store_details [%{"key" => "iglu_payments", "value" => "[]"}]

  def execute(fulfillment, opts \\ [])

  def execute(%{shipping_method: "in-store"} = fulfillment, opts) do
    build_base_input(fulfillment, opts)
  end

  def execute(%{shipping_method: "delivery"} = fulfillment, opts) do
    with {:ok, shipping_line} <- ShippingLine.execute(fulfillment),
         {:ok, shipping_address} <- ShippingAddress.execute(fulfillment),
         {:ok, base_input} <- build_base_input(fulfillment, opts) do
      base_input
      |> Map.put(:shippingLine, shipping_line)
      |> Map.put(:billingAddress, shipping_address)
      |> Map.put(:shippingAddress, shipping_address)
      |> Map.put(:useCustomerDefaultAddress, false)
      |> then(fn input -> {:ok, input} end)
    end
  end

  defp build_base_input(fulfillment, opts) do
    localization_extensions = LocalizationExtensions.execute(fulfillment)

    with {:ok, line_items} <- LineItems.execute(fulfillment) do
      %{
        localizationExtensions: localization_extensions,
        lineItems: line_items,
        tags: Tags.execute(fulfillment),
        customAttributes: @in_store_details,
        note: (fulfillment.order.metadata || %{}) |> Map.get(:notes, "")
      }
      |> maybe_add_email(fulfillment)
      |> maybe_put_purchase_entity(opts)
      |> then(fn base_input -> {:ok, base_input} end)
    end
  end

  defp maybe_add_email(map, %{fulfillment: %{order: %{email: email}}}) when not is_nil(email) do
    Map.put(map, :email, email)
  end

  defp maybe_add_email(map, _fulfillment), do: map

  def maybe_put_purchase_entity(map, opts) do
    if customer_id = Keyword.get(opts, :customer_id) do
      Map.put(map, :purchasingEntity, %{customerId: customer_id})
    else
      map
    end
  end
end
