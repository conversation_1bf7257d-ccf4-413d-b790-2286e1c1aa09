defmodule Rms.Integrations.Shopify.Orders.DraftOrder.LineItems do
  @mapping_error "Mapping not found for Shopify variant"
  @empty_line_items_error "At least 1 line item is required"

  def execute(fulfillment) do
    result =
      Enum.reduce_while(fulfillment.line_items, [], fn line_item, acc ->
        case build_line_item(line_item) do
          {:error, _} = error -> {:halt, error}
          {:ok, line_item} -> {:cont, [line_item | acc]}
        end
      end)

    case result do
      {:error, _} = error -> error
      [] -> {:error, %{reason: @empty_line_items_error, stacktrace: "#{__MODULE__}"}}
      line_items -> {:ok, line_items}
    end
  end

  defp build_line_item(line_item) do
    case fetch_mapping(line_item.product_variant.product_variant_mappings) do
      :error -> {:error, %{reason: @mapping_error, stacktrace: "#{__MODULE__}"}}
      {:ok, mapping} -> {:ok, build_line_item(line_item, mapping)}
    end
  end

  defp build_line_item(line_item, %{external_id: variant_id}) do
    item = %{
      quantity: line_item.quantity,
      variantId: variant_id,
      priceOverride: %{
        amount: Decimal.to_float(line_item.list_price),
        currencyCode: "BRL"
      }
    }

    item =
      if should_apply_discount?(line_item) do
        discount = calculate_discount(line_item)
        Map.put(item, :appliedDiscount, %{value: discount, valueType: "FIXED_AMOUNT"})
      else
        item
      end

    if should_apply_group?(line_item) do
      Map.put(item, :customAttributes, [%{key: "group", value: line_item.group_index}])
    else
      item
    end
  end

  defp fetch_mapping(product_variant_mappings) do
    case Enum.find(product_variant_mappings, &(&1.source == "shopify")) do
      nil -> :error
      mapping -> {:ok, mapping}
    end
  end

  defp should_apply_group?(%{group_index: nil}), do: false
  defp should_apply_group?(_), do: true

  defp should_apply_discount?(%{price: price, product_variant: %{line_price: price}}) do
    false
  end

  defp should_apply_discount?(_), do: true

  defp calculate_discount(line_item) do
    discount_value(line_item.price, line_item.list_price)
  end

  defp discount_value(price, list_price) do
    case Decimal.compare(price, list_price) do
      :lt ->
        Decimal.to_float(list_price) - Decimal.to_float(price)

      _ ->
        Decimal.to_float(Decimal.new("0"))
    end
  end
end
