defmodule Rms.Integrations.Shopify.Orders.DraftOrder.ShippingAddress do
  def execute(fulfillment) do
    case fulfillment.order.shipping_address do
      nil ->
        {:error, "Shipping Address not found"}

      shipping_address ->
        {first_name, last_name} = first_and_last_name(shipping_address.receiver_name)

        {:ok,
         %{
           "firstName" => first_name,
           "lastName" => last_name,
           "address1" =>
             "#{shipping_address.street} #{shipping_address.number}, #{shipping_address.neighborhood}",
           "city" => shipping_address.city_name,
           "provinceCode" => shipping_address.state,
           "countryCode" => format_country(shipping_address.country_name),
           "zip" => shipping_address.zip
         }}
    end
  end

  defp first_and_last_name(name) do
    case String.split(name, " ", parts: 2) do
      [first_name] -> {first_name, "(sobrenome não informado)"}
      [first_name, last_name] -> {first_name, last_name}
    end
  end

  defp format_country("BRA"), do: "BR"
  defp format_country("BR"), do: "BR"
  defp format_country("Brazil"), do: "BR"
  defp format_country(c), do: raise("Country invalid: #{c}")
end
