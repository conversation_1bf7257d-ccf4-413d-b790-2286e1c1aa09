defmodule Rms.Integrations.Shopify.Orders.DraftOrder.ShippingLine do
  def execute(%{shipping_settings: nil}), do: {:error, "Shipping Settings not found"}

  def execute(%{shipping_settings: %{settings: nil}}),
    do: {:error, "Shipping Settings not found"}

  def execute(%{shipping_settings: shipping_settings}) do
    {:ok,
     %{
       price: shipping_settings.settings["estimatedCost"]["amount"],
       title: shipping_settings.settings["title"]
     }}
  end
end
