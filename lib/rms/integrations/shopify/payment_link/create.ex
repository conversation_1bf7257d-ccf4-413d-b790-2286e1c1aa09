defmodule Rms.Integrations.Shopify.PaymentLink.Create do
  alias Rms.Integrations

  def execute(organization_id, order) do
    order = Rms.Repo.preload(order, [:fulfillments])

    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, draft_order_id} <- validate_payment_link(order),
         {:ok,
          %{
            "invoiceUrl" => payment_link
          }} <- Integrations.Shopify.fetch_draft_order(client, draft_order_id) do
      {:ok, payment_link, draft_order_id}
    end
  end

  defp validate_payment_link(order) do
    case order.fulfillments do
      [%{ecommerce: "shopify", external_reference: external_id}] when not is_nil(external_id) ->
        {:ok, external_id}

      _ ->
        {:error, "can not create payment link for this order"}
    end
  end
end
