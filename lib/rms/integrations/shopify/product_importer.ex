defmodule Rms.Integrations.Shopify.ProductImporter do
  @moduledoc """
  Handles the importing of Shopify products into the RMS system.
  """

  import Ecto.Query
  alias Rms.Commerce.Products.Product
  alias Rms.Commerce.Products.ProductVariant
  alias Rms.Integrations.ProductSyncEntry
  alias Rms.Integrations.ProductSyncMapping
  alias Rms.Integrations.ProductVariantMapping
  alias Rms.Repo

  def import_product(product_sync, external_product, sync_type \\ :full) do
    Ecto.Multi.new()
    |> fetch_product_mapping(product_sync, external_product)
    |> upsert_product(product_sync, external_product)
    |> create_or_update_product_mapping(external_product)
    |> archive_unused_variants(external_product, sync_type)
    |> upsert_variants(product_sync, external_product)
    |> Repo.transaction()
  end

  defp fetch_product_mapping(multi, product_sync, external_product) do
    Ecto.Multi.run(multi, :product_mapping, fn repo, _changes ->
      query =
        from pm in ProductSyncMapping,
          where:
            pm.organization_id == ^product_sync.organization_id and
              pm.external_id == ^external_product["id"] and
              pm.source == "shopify",
          left_join: p in assoc(pm, :product),
          preload: [product: {p, product_variants: []}]

      {:ok, repo.one(query)}
    end)
  end

  defp upsert_product(multi, product_sync, external_product) do
    Ecto.Multi.run(multi, :product, fn repo, %{product_mapping: product_mapping} ->
      product = find_product(repo, product_sync, product_mapping, external_product)
      {product, operation} = determine_product_operation(product, product_mapping, product_sync)

      changeset =
        Product.changeset(product, %{
          name: external_product["title"],
          tags: build_tags(external_product["productType"] || external_product["product_type"])
        })

      case upsert_and_log_entry(repo, product_sync, external_product["id"], changeset, operation) do
        {:ok, product} when not is_nil(product) ->
          {:ok, Repo.preload(product, [:product_variants])}

        error ->
          error
      end
    end)
  end

  defp build_tags("") do
    []
  end

  defp build_tags(nil) do
    []
  end

  defp build_tags(tag) do
    [tag]
  end

  defp find_product(_repo, _product_sync, product_mapping, _external_product)
       when not is_nil(product_mapping) do
    Map.get(product_mapping, :product)
  end

  defp find_product(
         repo,
         product_sync,
         nil,
         %{"variants" => %{"edges" => edges}} = _external_product
       )
       when is_list(edges) and length(edges) > 0 do
    edges
    |> extract_skus()
    |> find_product_by_skus(repo, product_sync)
  end

  defp find_product(_repo, _product_sync, _product_mapping, _external_product) do
    nil
  end

  defp find_product_by_skus([], _repo, _product_sync), do: nil

  defp find_product_by_skus(skus, repo, product_sync) do
    query =
      from p in Product,
        join: v in assoc(p, :product_variants),
        left_join: psm in assoc(p, :product_sync_mapping),
        where:
          v.sku in ^skus and p.organization_id == ^product_sync.organization_id and is_nil(psm.id),
        preload: [product_variants: v]

    repo.one(query)
  end

  defp extract_skus(edges) do
    edges
    |> Enum.map(fn %{"node" => %{"sku" => sku}} -> sku end)
    |> Enum.reject(&is_nil/1)
  end

  defp determine_product_operation(nil, nil, product_sync) do
    {%Product{organization_id: product_sync.organization_id}, :insert}
  end

  defp determine_product_operation(nil, _product_mapping, product_sync) do
    {%Product{organization_id: product_sync.organization_id}, :insert}
  end

  defp determine_product_operation(product, _product_mapping, _product_sync) do
    {product, :update}
  end

  defp create_or_update_product_mapping(multi, external_product) do
    Ecto.Multi.run(multi, :create_product_mapping, fn _repo,
                                                      %{
                                                        product: product,
                                                        product_mapping: product_mapping
                                                      } ->
      case product_mapping do
        nil ->
          create_product_sync_mapping(product.organization_id, %{
            external_id: external_product["id"],
            source: "shopify",
            product_id: product.id
          })

        _ ->
          {:ok, product_mapping}
      end
    end)
  end

  defp create_product_sync_mapping(organization_id, attrs) do
    %ProductSyncMapping{organization_id: organization_id}
    |> ProductSyncMapping.changeset(attrs)
    |> Repo.insert(
      on_conflict: {:replace, [:updated_at]},
      conflict_target: [:organization_id, :external_id, :source]
    )
  end

  defp archive_unused_variants(multi, _external_product, :incremental), do: multi

  defp archive_unused_variants(multi, external_product, :full) do
    Ecto.Multi.update_all(
      multi,
      :archived_unused_variants,
      fn %{product: product} ->
        archive_skus = calculate_archive_skus(product, external_product)

        now = NaiveDateTime.utc_now()

        from(pv in ProductVariant,
          where: pv.sku in ^archive_skus,
          where: pv.product_id == ^product.id,
          update: [set: [archived_at: ^now]]
        )
      end,
      []
    )
  end

  defp calculate_archive_skus(product, external_product) do
    variants = get_in(external_product, ["variants", "edges", Access.all(), "node"])

    current_skus = Enum.map(product.product_variants, & &1.sku) |> MapSet.new()
    importing_skus = variants |> get_in([Access.all(), "sku"]) |> MapSet.new()

    MapSet.difference(current_skus, importing_skus) |> MapSet.to_list()
  end

  defp upsert_variants(multi, product_sync, external_product) do
    Ecto.Multi.merge(multi, fn %{product: product} ->
      variants = get_in(external_product, ["variants", "edges", Access.all(), "node"])

      Enum.reduce(
        variants,
        Ecto.Multi.new(),
        &upsert_variant(&2, product_sync, product, external_product, &1)
      )
    end)
  end

  defp upsert_variant(multi, product_sync, product, external_product, variant) do
    {variant_changeset, external_id} =
      build_product_variant_from_shopify(product, external_product, variant)

    multi
    |> Ecto.Multi.run({:product_variant, external_id}, fn repo, _changes ->
      operation = if variant_changeset.data.id, do: :update, else: :insert
      upsert_and_log_entry(repo, product_sync, external_id, variant_changeset, operation)
    end)
    |> Ecto.Multi.run({:product_variant_mapping, external_id}, fn _repo, args ->
      case args[{:product_variant, external_id}] do
        nil ->
          {:ok, nil}

        product_variant ->
          %ProductVariantMapping{}
          |> ProductVariantMapping.changeset(%{
            organization_id: product_variant.organization_id,
            product_variant_id: product_variant.id,
            source: "shopify",
            external_id: external_id
          })
          |> Repo.insert(
            conflict_target: [:organization_id, :product_variant_id, :source],
            on_conflict: {:replace, [:external_id, :updated_at]}
          )
      end
    end)
    |> Ecto.Multi.run({:emit_event, external_id}, fn _repo, args ->
      operation = if variant_changeset.data.id, do: "updated", else: "created"

      case args[{:product_variant, external_id}] do
        nil ->
          {:ok, nil}

        product_variant ->
          Rms.Events.emit(
            "product.#{operation}",
            Rms.Commerce.Products.ProductVariant.event_payload(product_variant)
          )
      end
    end)
  end

  defp build_product_variant_from_shopify(product, external_product, external_variant) do
    image_urls =
      if is_nil(external_variant["image"]) do
        get_in(external_product, ["images", "edges", Access.all(), "node", "url"])
      else
        external_variant
        |> get_in(["image", "url"])
        |> List.wrap()
      end

    variation_types =
      external_variant["selectedOptions"]
      |> Enum.filter(fn variation_type ->
        variation_type["name"] != "Title"
      end)
      |> Enum.map(fn variation_type ->
        %{key: variation_type["name"], value: variation_type["value"]}
      end)

    barcode =
      if external_variant["barcode"] in [nil, ""],
        do: external_variant["sku"],
        else: external_variant["barcode"]

    attrs = %{
      bar_code: barcode,
      image_urls: Enum.reject(image_urls, &is_nil/1),
      list_price: external_variant["price"]["amount"],
      price: external_variant["price"]["amount"],
      sku: external_variant["sku"],
      name: product.name,
      tags: product.tags,
      variation_types: variation_types,
      archived_at: nil
    }

    empty_product_variant = %ProductVariant{
      product_id: product.id,
      organization_id: product.organization_id
    }

    product_variants = Repo.all(from pv in ProductVariant, where: pv.product_id == ^product.id)
    product = Map.put(product, :product_variants, product_variants)

    product_variants =
      Enum.filter(
        product.product_variants,
        &(&1.sku == external_variant["sku"])
      )

    product_variant =
      case product_variants do
        [] ->
          empty_product_variant

        [product_variant] ->
          product_variant

        [default_product_variant | rest] ->
          Enum.find(rest, default_product_variant, &is_nil(&1.archived_at))
      end

    changeset = ProductVariant.changeset(product_variant, attrs, "shopify")
    {changeset, external_variant["id"]}
  end

  defp upsert_and_log_entry(repo, product_sync, external_id, changeset, operation) do
    operation_func = if operation == :insert, do: &repo.insert/1, else: &repo.update/1

    case operation_func.(changeset) do
      {:ok, entity} ->
        sku = Map.get(entity, :sku)
        entry_attrs = %{sku: sku, external_id: external_id, status: "success", errors: []}
        create_product_sync_entry(product_sync, entry_attrs)
        {:ok, entity}

      {:error, changeset} ->
        errors = extract_changeset_errors(changeset)

        entry_attrs = %{
          sku: nil,
          external_id: external_id,
          status: "error",
          errors: [
            %{
              metadata: %{params: changeset.params, errors: errors},
              code: "changeset_error",
              detail: "invalid changeset"
            }
          ]
        }

        create_product_sync_entry(product_sync, entry_attrs)
        {:ok, nil}
    end
  end

  defp extract_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, &error_traverser/1)
  end

  defp error_traverser({msg, opts}) do
    Enum.reduce(opts, msg, fn {key, value}, acc ->
      String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
    end)
  end

  defp create_product_sync_entry(product_sync, attrs) do
    %ProductSyncEntry{product_sync_id: product_sync.id}
    |> ProductSyncEntry.changeset(attrs)
    |> Repo.insert()
  end
end
