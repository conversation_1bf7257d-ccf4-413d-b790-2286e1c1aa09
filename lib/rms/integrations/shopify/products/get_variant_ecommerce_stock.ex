defmodule Rms.Integrations.Shopify.Products.GetVariantEcommerceStock do
  alias Rms.Integrations.Shopify

  @doc """
  Fetches the stock of a product variant from Shopify.

  Returns a tuple, where the first element is `:ok` and the second element is a map with
  two keys: `available_for_sale` and `quantity_available`. The values indicate whether the
  product variant is available for sale and the quantity available, respectively.
  """

  @spec call(organization_id :: pos_integer, product_variant_id :: pos_integer) ::
          {:ok, %{available_for_sale: boolean, quantity_available: integer}}
          | {:error, String.t()}
  def call(organization_id, product_variant_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    client =
      Shopify.storefront_client(shopify_credential.shop, shopify_credential.credential,
        organization_id: organization_id
      )

    product_variant_mapping =
      Rms.Integrations.get_product_variant_external_ids(
        organization_id,
        [product_variant_id],
        "shopify"
      )

    external_id = product_variant_mapping[product_variant_id]

    if is_nil(external_id) do
      {:error, "Product variant not found in Shopify"}
    else
      with {:ok, data} <-
             Shopify.get_product_variant_stock(client, external_id) do
        {:ok,
         %{
           available_for_sale: data["availableForSale"],
           quantity_available: data["quantityAvailable"]
         }}
      end
    end
  end
end
