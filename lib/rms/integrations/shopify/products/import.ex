defmodule Rms.Integrations.Shopify.Product.Import do
  def execute(organization_id, external_id) do
    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case Rms.Integrations.Shopify.get_product(
           shopify_client,
           external_id
         ) do
      {:ok, nil} ->
        {:error, :not_found}

      {:ok, external_product} ->
        {:ok, format_product(external_product)}

      _ ->
        {:error, :not_found}
    end
  end

  defp format_product(product) do
    %{
      "id" => product["id"],
      "title" => product["title"],
      "description" => product["description"],
      "onlineStoreUrl" => product["onlineStoreUrl"],
      "createdAt" => product["createdAt"],
      "updatedAt" => product["updatedAt"],
      "publishedAt" => product["publishedAt"],
      "productType" => product["productType"],
      "vendor" => product["vendor"],
      "handle" => product["handle"],
      "images" => %{
        "edges" =>
          Enum.map(product["images"]["edges"], fn image_edge ->
            %{
              "node" => %{
                "id" => image_edge["node"]["id"],
                "url" => image_edge["node"]["url"],
                "height" => image_edge["node"]["height"],
                "width" => image_edge["node"]["width"]
              }
            }
          end)
      },
      "options" =>
        Enum.map(product["options"], fn option ->
          %{
            "name" => option["name"],
            "values" => option["values"]
          }
        end),
      "seo" => %{
        "title" => product["seo"]["title"],
        "description" => product["seo"]["description"]
      },
      "tags" => product["tags"],
      "variants" => %{
        "edges" =>
          Enum.map(product["variants"]["edges"], fn variant_edge ->
            %{
              "node" => %{
                "id" => variant_edge["node"]["id"],
                "title" => variant_edge["node"]["title"],
                "price" => %{
                  "amount" => variant_edge["node"]["price"],
                  "currencyCode" => "BRA"
                },
                "compareAtPrice" => variant_edge["node"]["compareAtPrice"],
                "sku" => variant_edge["node"]["sku"],
                "barcode" => variant_edge["node"]["barcode"],
                "quantityAvailable" => variant_edge["node"]["quantityAvailable"],
                "availableForSale" => variant_edge["node"]["availableForSale"],
                "weight" => variant_edge["node"]["weight"],
                "weightUnit" => variant_edge["node"]["weightUnit"],
                "inventoryPolicy" => variant_edge["node"]["inventoryPolicy"],
                "createdAt" => variant_edge["node"]["createdAt"],
                "updatedAt" => variant_edge["node"]["updatedAt"],
                "image" => variant_edge["node"]["image"],
                "selectedOptions" =>
                  Enum.map(variant_edge["node"]["selectedOptions"], fn selected_option ->
                    %{
                      "name" => selected_option["name"],
                      "value" => selected_option["value"]
                    }
                  end)
              }
            }
          end)
      }
    }
  end
end
