defmodule Rms.Integrations.Shopify.Returns.CloseReturnWorker do
  @moduledoc """
  Worker that closes a Shopify return using the returnClose mutation.
  """
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  require Logger

  alias Rms.Integrations
  alias Oban.Pro.Workflow

  @impl true
  def process(
        %{
          args: %{
            "organization_id" => organization_id
          }
        } = job
      ) do
    with {:ok, return_id} <- get_return_id(job),
         shopify_client = get_shopify_client(organization_id),
         {:ok, result} <- Integrations.Shopify.return_close(shopify_client, return_id) do
      Logger.info("successfully closed shopify return #{return_id}",
        organization_id: organization_id,
        return_id: return_id
      )

      {:ok, result}
    else
      {:error, error} ->
        Logger.error("failed to close shopify return: #{inspect(error)}",
          organization_id: organization_id
        )

        {:error, error}
    end
  end

  defp get_shopify_client(organization_id) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)
  end

  # Get the return ID from direct args if provided
  defp get_return_id(%{args: %{"return_id" => return_id}}) when not is_nil(return_id) do
    {:ok, return_id}
  end

  defp get_return_id(%{args: %{"return_id" => _}}) do
    {:error, :shopify_return_not_found}
  end

  # Get the return ID from workflow records if not provided in args
  defp get_return_id(job) do
    case Workflow.get_recorded(job, :create_return) do
      nil -> {:error, :shopify_return_not_found}
      %{return_id: return_id} -> {:ok, return_id}
      _ -> {:error, :invalid_return_id_format}
    end
  end
end
