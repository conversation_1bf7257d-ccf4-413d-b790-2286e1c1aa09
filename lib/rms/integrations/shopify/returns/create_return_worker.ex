defmodule Rms.Integrations.Shopify.Returns.CreateReturnWorker do
  @moduledoc """
  Worker that creates a return in Shopify using the returnCreate mutation.
  """
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  require Logger

  alias Rms.Commerce.Fulfillments
  alias Rms.Integrations

  @impl true
  def process(%{
        args: %{
          "reverse_fulfillment_id" => reverse_fulfillment_id,
          "fulfillment_id" => fulfillment_id,
          "organization_id" => organization_id
        }
      }) do
    reverse_fulfillment = get_reverse_fulfillment(organization_id, reverse_fulfillment_id)
    fulfillment = Fulfillments.get_fulfillment!(organization_id, fulfillment_id)
    shopify_credential = Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, order_id} <- get_shopify_order_id(fulfillment),
         {:ok, returnable_items} <-
           Integrations.Shopify.returnable_fulfillments(shopify_client, order_id),
         {:ok, return_input} <-
           build_return_input(reverse_fulfillment, fulfillment, order_id, returnable_items),
         {:ok, return_id} <- Integrations.Shopify.return_create(shopify_client, return_input) do
      Logger.info(
        "successfully created shopify return for reverse fulfillment #{reverse_fulfillment_id} and fulfillment #{fulfillment_id}"
      )

      {:ok, %{return_id: return_id}}
    else
      {:error, {:non_returnable_items, error_details}} ->
        Logger.error("failed to create shopify return: attempted to return non-returnable items",
          organization_id: organization_id,
          reverse_fulfillment_id: reverse_fulfillment_id,
          fulfillment_id: fulfillment_id,
          non_returnable_item_details: error_details
        )

        {:error, :non_returnable_items}

      {:error, error} ->
        Logger.error("failed to create shopify return: #{inspect(error)}",
          organization_id: organization_id,
          reverse_fulfillment_id: reverse_fulfillment_id,
          fulfillment_id: fulfillment_id
        )

        {:error, error}
    end
  end

  defp get_reverse_fulfillment(organization_id, reverse_fulfillment_id) do
    preload = [:location, line_items: [line_item: []]]

    Fulfillments.get_reverse_fulfillment_by_id!(
      organization_id,
      reverse_fulfillment_id,
      preload: preload
    )
  end

  defp get_shopify_order_id(fulfillment) do
    case fulfillment.external_reference do
      nil -> {:error, :shopify_order_id_not_found}
      "gid://shopify/DraftOrder/" <> _ -> {:error, :draft_order_id_not_allowed}
      order_id -> {:ok, order_id}
    end
  end

  defp build_return_input(reverse_fulfillment, fulfillment, order_id, returnable_items) do
    # Filter line items to only include those associated with this fulfillment
    filtered_line_items =
      reverse_fulfillment.line_items
      |> Enum.filter(fn rf_line_item ->
        rf_line_item.line_item.fulfillment_id == fulfillment.id
      end)

    case map_to_returnable_items(filtered_line_items, returnable_items) do
      {:ok, []} ->
        {:error, :no_valid_line_items}

      {:ok, return_line_items} ->
        input = %{
          orderId: order_id,
          returnLineItems: return_line_items,
          notifyCustomer: false
        }

        {:ok, input}

      {:error, _} = error ->
        error
    end
  end

  defp map_to_returnable_items(our_line_items, returnable_fulfillment_items) do
    # Create a map of SKUs to fulfillment line item IDs
    # Some items might have the same SKU but be from different fulfillments
    # So we need to group them by SKU
    fulfillment_line_item_map =
      returnable_fulfillment_items
      |> Enum.group_by(
        fn item -> get_in(item, ["lineItem", "sku"]) end,
        fn item ->
          %{
            id: item["id"],
            quantity: item["quantity"],
            line_item_id: get_in(item, ["lineItem", "id"])
          }
        end
      )

    # Map our line items to use the correct fulfillment line item IDs
    results = Enum.map(our_line_items, &map_line_item(&1, fulfillment_line_item_map))
    process_mapping_results(results)
  end

  defp map_line_item(rf_line_item, fulfillment_line_item_map) do
    line_item = rf_line_item.line_item
    sku = line_item.sku

    case Map.get(fulfillment_line_item_map, sku) do
      nil ->
        {:error, {:non_returnable_item, line_item.id, "SKU not found in returnable items"}}

      [] ->
        {:error, {:non_returnable_item, line_item.id, "No returnable items with this SKU"}}

      returnable_items ->
        find_returnable_item(returnable_items, rf_line_item)
    end
  end

  defp find_returnable_item(returnable_items, rf_line_item) do
    # Find a returnable item with enough quantity
    case Enum.find(returnable_items, fn item ->
           item.quantity >= rf_line_item.returned_quantity
         end) do
      nil ->
        {:error,
         {:non_returnable_item, rf_line_item.line_item.id, "Insufficient returnable quantity"}}

      returnable_item ->
        {:ok,
         %{
           # Use the fulfillment line item ID from the returnable item
           fulfillmentLineItemId: returnable_item.id,
           quantity: rf_line_item.returned_quantity,
           returnReason: map_return_reason(rf_line_item.reason),
           returnReasonNote: rf_line_item.reason
         }}
    end
  end

  defp process_mapping_results(results) do
    case Enum.split_with(results, fn result -> match?({:ok, _}, result) end) do
      {ok_items, []} ->
        {:ok, Enum.map(ok_items, fn {:ok, item} -> item end)}

      {_, errors} ->
        error_details = Enum.map(errors, &format_error_detail/1)
        {:error, {:non_returnable_items, error_details}}
    end
  end

  defp format_error_detail({:error, {:non_returnable_item, id, reason}}) do
    "#{id} (#{reason})"
  end

  defp format_error_detail({:error, {error_type, id, reason}}) when is_atom(error_type) do
    "#{id} (#{error_type}: #{reason})"
  end

  defp format_error_detail({:error, error}) do
    "unknown error (#{inspect(error)})"
  end

  defp map_return_reason(reason) do
    case reason do
      "Produto ficou pequeno" -> "SIZE_TOO_SMALL"
      "Produto ficou grande" -> "SIZE_TOO_LARGE"
      "Produto com defeito" -> "DEFECTIVE"
      "Produto recebido estava incorreto" -> "WRONG_ITEM"
      "Produto era diferente da descrição" -> "NOT_AS_DESCRIBED"
      "Cliente mudou de ideia" -> "UNWANTED"
      "Outro" -> "OTHER"
      _ -> "UNKNOWN"
    end
  end
end
