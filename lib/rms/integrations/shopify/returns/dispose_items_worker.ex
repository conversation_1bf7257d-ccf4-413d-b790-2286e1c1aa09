defmodule Rms.Integrations.Shopify.Returns.DisposeItemsWorker do
  use Oban.Pro.Worker, recorded: true, queue: :shopify

  alias Oban.Pro.Workflow
  alias Rms.Integrations.Shopify
  alias Rms.Integrations
  alias Rms.Commerce.Fulfillments

  def process(
        %{
          args: %{
            "reverse_fulfillment_id" => reverse_fulfillment_id,
            "organization_id" => organization_id
          }
        } = job
      ) do
    reverse_fulfillment =
      Fulfillments.get_reverse_fulfillment_by_id!(
        organization_id,
        reverse_fulfillment_id,
        preload: [location: [:location_mappings]]
      )

    with {:ok, return_id} <- get_return_id(job),
         shopify_client = get_shopify_client(organization_id),
         {:ok, return_data} <- Shopify.get_return(shopify_client, return_id),
         {:ok, disposition_inputs} <-
           build_disposition_inputs(return_data, reverse_fulfillment) do
      process_dispositions(shopify_client, disposition_inputs)
    end
  end

  # Get the return ID from workflow records or direct args
  defp get_return_id(%{args: %{"return_id" => return_id}}) when not is_nil(return_id) do
    {:ok, return_id}
  end

  defp get_return_id(%{args: %{"return_id" => _}}) do
    {:error, :shopify_return_not_found}
  end

  defp get_return_id(job) do
    case Workflow.get_recorded(job, :create_return) do
      nil -> {:error, :shopify_return_not_found}
      %{return_id: return_id} -> {:ok, return_id}
      _ -> {:error, :invalid_return_id_format}
    end
  end

  defp get_shopify_client(organization_id) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)
  end

  # Build disposition inputs from return data and our line items
  defp build_disposition_inputs(return_data, reverse_fulfillment) do
    # Extract reverse fulfillment orders and process them
    return_data
    |> get_in(["reverseFulfillmentOrders", "edges", Access.all(), "node"])
    |> filter_active_fulfillment_orders()
    |> process_active_fulfillment_orders(reverse_fulfillment)
  end

  defp filter_active_fulfillment_orders(nil), do: []

  defp filter_active_fulfillment_orders(reverse_fulfillment_orders),
    do: Enum.filter(reverse_fulfillment_orders, &(&1["status"] == "OPEN"))

  defp process_active_fulfillment_orders(active_fulfillment_orders, reverse_fulfillment) do
    case build_disposition_groups(
           active_fulfillment_orders,
           reverse_fulfillment
         ) do
      groups when map_size(groups) == 0 ->
        {:error, "no matching line items found between shopify return and our system"}

      groups ->
        {:ok, groups}
    end
  end

  defp build_disposition_groups(
         active_fulfillment_orders,
         reverse_fulfillment
       ) do
    line_items_with_skus =
      Fulfillments.get_reverse_fulfillment_line_items_with_skus(
        reverse_fulfillment.organization_id,
        reverse_fulfillment.id
      )

    Enum.reduce(active_fulfillment_orders, %{}, fn rforder, acc ->
      rforder_id = rforder["id"]
      shopify_line_items = get_in(rforder, ["lineItems", "edges", Access.all(), "node"])

      case build_dispositions_for_order(
             shopify_line_items,
             line_items_with_skus,
             reverse_fulfillment
           ) do
        [] -> acc
        dispositions -> Map.put(acc, rforder_id, dispositions)
      end
    end)
  end

  defp build_dispositions_for_order(nil, _our_line_items, _reverse_fulfillment), do: []

  defp build_dispositions_for_order(shopify_line_items, our_line_items, reverse_fulfillment) do
    # Track remaining quantities to avoid over-processing
    {dispositions, _remaining_items} =
      Enum.reduce(shopify_line_items, {[], our_line_items}, fn shopify_item,
                                                               {dispositions, remaining_items} ->
        # Get SKU from Shopify line item - handle nested structure
        sku = get_in(shopify_item, ["fulfillmentLineItem", "lineItem", "sku"])
        shopify_item_id = shopify_item["id"]
        shopify_quantity = shopify_item["totalQuantity"]

        # Find matching item from our system by SKU
        case find_and_update_matching_item(remaining_items, sku, shopify_quantity) do
          {nil, remaining_items} ->
            # No match found or no quantity left to process
            {dispositions, remaining_items}

          {matching_item, quantity_to_dispose, updated_remaining_items} ->
            # Create disposition with matching item data
            new_disposition =
              create_disposition(
                shopify_item_id,
                quantity_to_dispose,
                matching_item,
                reverse_fulfillment
              )

            # Add disposition to accumulator
            {[new_disposition | dispositions], updated_remaining_items}
        end
      end)

    dispositions
  end

  # Find a matching item by SKU and update remaining quantities
  defp find_and_update_matching_item(items, nil, _max_quantity), do: {nil, items}

  defp find_and_update_matching_item(items, sku, max_quantity) do
    case Enum.find_index(items, &(&1.sku == sku && &1.returned_quantity > 0)) do
      nil ->
        # No matching item found
        {nil, items}

      index ->
        # Found a matching item
        matching_item = Enum.at(items, index)
        available_quantity = matching_item.returned_quantity

        # Calculate how much we can dispose
        quantity_to_dispose = min(available_quantity, max_quantity)

        # Update the remaining quantity
        updated_matching_item =
          Map.update!(matching_item, :returned_quantity, &(&1 - quantity_to_dispose))

        updated_items = List.replace_at(items, index, updated_matching_item)

        {matching_item, quantity_to_dispose, updated_items}
    end
  end

  # Create a disposition entry for the Shopify API
  defp create_disposition(shopify_item_id, quantity, matching_item, reverse_fulfillment) do
    # Create disposition with proper structure matching Shopify's API requirements
    disposition = %{
      reverseFulfillmentOrderLineItemId: shopify_item_id,
      quantity: quantity,
      dispositionType: disposition_type(matching_item.return_to_inventory)
    }

    # Add locationId if restocking
    if matching_item.return_to_inventory do
      Map.put(disposition, :locationId, get_location_id(reverse_fulfillment))
    else
      disposition
    end
  end

  # Determine the disposition type based on whether item should be returned to inventory
  defp disposition_type(true), do: "RESTOCKED"
  defp disposition_type(false), do: "NOT_RESTOCKED"

  # Get the Shopify location ID from our location mappings
  defp get_location_id(reverse_fulfillment) do
    # Get the location from the preloaded association
    location = reverse_fulfillment.location

    # Find the Shopify location mapping from the preloaded mappings
    shopify_mapping =
      Enum.find(location.location_mappings, fn mapping ->
        mapping.source == "shopify"
      end)

    case shopify_mapping do
      nil ->
        raise "Missing Shopify location mapping for location #{location.id}. Please add a location mapping with source 'shopify'."

      mapping ->
        mapping.external_id
    end
  end

  defp process_dispositions(_client, []), do: {:ok, :no_dispositions_needed}

  defp process_dispositions(client, disposition_groups) do
    all_dispositions =
      disposition_groups
      |> Enum.flat_map(fn {_rforder_id, dispositions} -> dispositions end)

    # Make a single API call with all dispositions
    case Shopify.reverse_fulfillment_order_dispose(client, all_dispositions) do
      {:ok, line_items} ->
        # Process the response
        processed_items = extract_processed_items(line_items)
        {:ok, processed_items}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # Extract processed items info from the line items response
  defp extract_processed_items(line_items) do
    # The line_items is now a list of ReverseFulfillmentOrderLineItems, not edges/nodes
    Enum.flat_map(line_items, fn line_item ->
      item_id = Map.get(line_item, "id")
      dispositions = Map.get(line_item, "dispositions", [])

      Enum.map(dispositions, fn disposition ->
        %{
          id: Map.get(disposition, "id"),
          item_id: item_id,
          type: Map.get(disposition, "type"),
          quantity: Map.get(disposition, "quantity"),
          location_id: get_in(disposition, ["location", "id"])
        }
      end)
    end)
  end
end
