defmodule Rms.Integrations.Shopify.Returns.Workflow do
  @moduledoc """
  Workflow for syncing iGlu reverse fulfillments to Shopify returns.

  This workflow consists of three steps:
  1. Create a return in Shopify using returnCreate
  2. Dispose of items with the appropriate disposition type
  3. Close the return using returnClose
  """

  alias Oban.Pro.Workers.Workflow
  alias Rms.Integrations.Shopify.Returns.CloseReturnWorker
  alias Rms.Integrations.Shopify.Returns.CreateReturnWorker
  alias Rms.Integrations.Shopify.Returns.DisposeItemsWorker

  @doc """
  Builds a workflow for syncing a reverse fulfillment to Shopify.

  ## Parameters

  - `reverse_fulfillment`: The reverse fulfillment to sync to Shopify
  - `fulfillment_id`: The ID of the fulfillment being returned
  - `opts`: Additional options (e.g., return_id if already known)

  ## Returns

  An Oban.Pro.Workers.Workflow struct
  """
  def build_workflow(reverse_fulfillment, fulfillment_id, opts \\ []) do
    base_args = %{
      reverse_fulfillment_id: reverse_fulfillment.id,
      fulfillment_id: fulfillment_id,
      organization_id: reverse_fulfillment.organization_id
    }

    return_id = Keyword.get(opts, :return_id)
    args = if return_id, do: Map.put(base_args, :return_id, return_id), else: base_args
    dispose_items_deps = if is_nil(return_id), do: [:create_return], else: []

    Workflow.new(workflow_name: "shopify_return")
    |> add_create_return_step(args, return_id)
    |> Workflow.add(
      :dispose_items,
      DisposeItemsWorker.new(args),
      deps: dispose_items_deps
    )
    |> Workflow.add(
      :close_return,
      CloseReturnWorker.new(args),
      deps: [:dispose_items]
    )
  end

  defp add_create_return_step(workflow, base_args, nil) do
    Workflow.add(
      workflow,
      :create_return,
      CreateReturnWorker.new(base_args)
    )
  end

  defp add_create_return_step(workflow, _base_args, _return_id),
    do: workflow
end
