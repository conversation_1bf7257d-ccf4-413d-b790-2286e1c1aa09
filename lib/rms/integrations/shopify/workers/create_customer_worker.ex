defmodule Rms.Integrations.Shopify.CreateCustomerWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  alias Rms.Integrations

  @preload [order: [customer: [:addresses, :customer_mappings]]]

  @impl true
  def process(%{args: %{"customer_id" => id, "organization_id" => organization_id}}) do
    customer =
      organization_id
      |> Rms.Customers.get_customer!(id)
      |> Rms.Repo.preload([:addresses, :customer_mappings])

    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case find_customer_mapping(customer) do
      nil ->
        find_client(client, customer)

      external_id ->
        {:ok, %{"id" => external_id}}
    end
  end

  @impl true
  def process(%{args: %{"id" => id, "organization_id" => organization_id}}) do
    fulfillment =
      organization_id
      |> Rms.Commerce.Fulfillments.get_fulfillment!(id)
      |> Rms.Repo.preload(@preload)

    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    case find_customer_mapping(fulfillment.order.customer) do
      nil ->
        find_client(client, fulfillment.order.customer)

      external_id ->
        {:ok, %{"id" => external_id}}
    end
  end

  defp create_customer(client, customer) do
    {first_name, last_name} = first_and_last_name(customer.name)

    case Integrations.Shopify.create_customer(client, %{
           email: customer.email,
           phone: customer.primary_phone_number,
           firstName: first_name,
           lastName: last_name,
           addresses:
             for address <- customer.addresses do
               {first_name, last_name} = first_and_last_name(address.receiver_name)

               %{
                 address1: "#{address.street_type} #{address.street}",
                 address2: "#{address.number} - #{address.neighborhood} - #{address.complement}",
                 city: address.city_name,
                 provinceCode: address.state,
                 # Shouldn't address have a phone number to contact the receiver?
                 phone: customer.primary_phone_number,
                 zip: address.zip,
                 lastName: last_name,
                 firstName: first_name,
                 # Make it dynamic from `address.country_name`
                 countryCode: "BR"
               }
             end
         }) do
      {:ok, %{"id" => shopify_id}} ->
        create_customer_mapping(customer, shopify_id, "shopify")
        {:ok, %{"id" => shopify_id}}

      _ ->
        {:ok, %{"id" => nil}}
    end
  end

  defp find_client(_client, nil) do
    {:ok, %{"id" => nil}}
  end

  defp find_client(client, %{email: email} = customer) do
    email_match = email

    case fetch_customer_by_email(client, email) do
      {:ok, %{items: [%{"id" => shopify_id, "email" => ^email_match}]}} ->
        handle_customer_mapping({:ok, %{"id" => shopify_id}}, customer, shopify_id)

      _ ->
        create_customer(client, customer)
    end
  end

  defp fetch_customer_by_email(_client, nil) do
    {:error, :not_found}
  end

  defp fetch_customer_by_email(client, email) do
    Integrations.Shopify.fetch_customers(client, first: 1, query: "email:#{email}")
  end

  defp handle_customer_mapping(response, customer, shopify_id) do
    create_customer_mapping(customer, shopify_id, "shopify")
    response
  end

  defp find_customer_mapping(nil) do
    nil
  end

  defp find_customer_mapping(customer) do
    case Enum.find(customer.customer_mappings, fn mapping ->
           mapping.source == "shopify"
         end) do
      nil ->
        nil

      mapping ->
        mapping.external_id
    end
  end

  # Improve this because this can be a false-positive for
  # "Ana Cristina Santos", making the first name only "Ana", when
  # it should be "Ana Cristina"
  defp first_and_last_name(name) do
    case String.split(name, " ", parts: 2) do
      [first_name] -> {first_name, "(sobrenome não informado)"}
      [first_name, last_name] -> {first_name, last_name}
    end
  end

  def create_customer_mapping(customer, external_id, source) do
    %Rms.Integrations.CustomerSyncMapping{}
    |> Rms.Integrations.CustomerSyncMapping.changeset(%{
      organization_id: customer.organization_id,
      customer_id: customer.id,
      source: source,
      external_id: external_id
    })
    |> Rms.Repo.insert(
      conflict_target: [:organization_id, :external_id, :source],
      on_conflict: {:replace, [:updated_at]}
    )
  end
end
