defmodule Rms.Integrations.Shopify.CreateDraftOrderWorker do
  use Oban.Pro.Worker, recorded: true, queue: :shopify
  alias <PERSON>ban.Pro.Workflow
  alias Rms.Integrations.Shopify.Orders.CreateDraftOrder

  alias Rms.Errors.ObanLogging

  require Logger

  @impl true
  def process(%{attempt: attempt} = job) do
    if attempt > 5 do
      ObanLogging.log_error(job)
    end

    process_with_refactor(job)
  end

  def process_with_refactor(
        %{
          args: %{
            "id" => fulfillment_id,
            "organization_id" => organization_id
          }
        } = job
      ) do
    {:ok, %{"id" => customer_id}} = fetch_lazy(job)

    CreateDraftOrder.run(organization_id, fulfillment_id, customer_id)
  end

  defp fetch_lazy(%{args: %{"result" => result}}), do: {:ok, result}

  defp fetch_lazy(job) do
    job
    |> Workflow.all_jobs(names: ["create_customer"])
    |> List.first()
    |> fetch_recorded()
  end
end
