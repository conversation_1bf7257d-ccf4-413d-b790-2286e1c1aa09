defmodule Rms.Integrations.Shopify.CreateOrderWorker do
  use Oban.Pro.Worker, recorded: true, queue: :shopify

  alias Rms.Integrations.Shopify.Orders.CreateOrder

  require Logger

  def process(
        %{
          attempt: attempt,
          args: %{
            "organization_id" => organization_id,
            "fulfillment_id" => fulfillment_id
          }
        } = job
      ) do
    if attempt > 10 do
      Rms.Errors.ObanLogging.log_error(job)
    end

    with {:ok, fulfillment} <- CreateOrder.run(organization_id, fulfillment_id) do
      log_success(fulfillment)
      :ok
    end
  end

  defp log_success(fulfillment) do
    Logger.info("[#{__MODULE__}] Order created: #{fulfillment.external_reference}")
  end
end
