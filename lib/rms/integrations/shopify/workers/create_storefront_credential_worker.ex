defmodule Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorker do
  use Oban.Pro.Worker, queue: :shopify

  alias Rms.Integrations

  @impl true
  def process(%{args: %{"organization_id" => organization_id, "storefront_credential" => nil}}) do
    case Integrations.get_shopify_storefront_credential(organization_id) do
      nil ->
        create_storefront_credential(organization_id, nil)

      _ ->
        {:discard, "discard"}
    end
  end

  @impl true
  def process(%{
        args: %{
          "organization_id" => organization_id,
          "storefront_credential" => storefront_credential
        }
      }) do
    create_storefront_credential(organization_id, storefront_credential)
  end

  defp create_storefront_credential(organization_id, nil) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, %{"id" => id, "accessToken" => access_token}} <-
           Integrations.Shopify.create_storefront_credential(shopify_client, %{
             title: "IGLU_#{Ecto.UUID.autogenerate()}"
           }),
         {:ok, _credential} <-
           Integrations.create_shopify_storefront_credential(
             %{organization_id: organization_id},
             %{credential_id: id, credential: access_token}
           ) do
      {:ok, "created"}
    end
  end

  defp create_storefront_credential(organization_id, storefront_credential) do
    with {:ok, _credential} <-
           Integrations.create_shopify_storefront_credential(
             %{organization_id: organization_id},
             %{
               credential_id: "IGLU_#{Ecto.UUID.autogenerate()}",
               credential: storefront_credential
             }
           ) do
      {:ok, "created"}
    end
  end
end
