defmodule Rms.Integrations.Shopify.FulfillOrderWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  alias Rms.Integrations

  @impl true
  def process(%{
        args: %{
          "organization_id" => organization_id,
          "fulfillment_order_id" => fulfillment_order_id
        }
      }) do
    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    fulfill(client, fulfillment_order_id)
  end

  defp fulfill(client, fulfillment_order_id) do
    params = %{
      "lineItemsByFulfillmentOrder" => [
        %{
          "fulfillmentOrderId" => fulfillment_order_id
        }
      ],
      "notifyCustomer" => false
    }

    Integrations.Shopify.fulfill_order(client, params)
  end
end
