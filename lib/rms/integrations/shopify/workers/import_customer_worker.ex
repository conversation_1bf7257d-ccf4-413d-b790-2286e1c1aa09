defmodule Rms.Integrations.Shopify.ImportCustomerWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify, max_attempts: 3

  alias Rms.Addresses
  alias Rms.Customers
  alias Rms.Integrations

  @impl true
  def process(%{
        args: %{"id" => id, "organization_id" => organization_id, "customer" => customer}
      }) do
    customer_sync = Integrations.fetch_customer_sync!(id)

    case search_customer(organization_id, customer) do
      {:ok, kind, customers} ->
        update_customers(customer_sync, customers, customer, kind)

      {:error, :not_found} ->
        import_customer(customer_sync, organization_id, customer)
    end
  end

  defp search_customer(organization_id, %{
         "id" => id,
         "email" => email,
         "defaultAddress" => %{"company" => document}
       }) do
    with {:error, :not_found} <- search_by_id(organization_id, id),
         {:error, :not_found} <- search_by_email_and_document(organization_id, email, document) do
      {:error, :not_found}
    else
      {:ok, kind, customers} -> {:ok, kind, preload_customers(customers)}
    end
  end

  defp search_customer(organization_id, %{
         "id" => id,
         "email" => email
       }) do
    with {:error, :not_found} <- search_by_email_and_document(organization_id, email, nil),
         {:error, :not_found} <- search_by_id(organization_id, id) do
      {:error, :not_found}
    else
      {:ok, kind, customers} -> {:ok, kind, preload_customers(customers)}
    end
  end

  defp search_by_id(organization_id, id) do
    case Integrations.get_customer_sync_mapping(organization_id, "shopify", id) do
      nil -> {:error, :not_found}
      customer_sync_mapping -> {:ok, :id, [customer_sync_mapping.customer]}
    end
  end

  defp search_by_email_and_document(organization_id, email, nil) when not is_nil(email) do
    case Customers.list_customers(organization_id,
           email: email
         ) do
      [] -> {:error, :not_found}
      customers -> {:ok, :email_and_document, customers}
    end
  end

  defp search_by_email_and_document(organization_id, email, document) when not is_nil(email) do
    case Customers.list_customers(organization_id,
           email: email,
           document: document,
           document_type: "cpf"
         ) do
      [] -> {:error, :not_found}
      customers -> {:ok, :email_and_document, customers}
    end
  end

  defp search_by_email_and_document(_organization_id, _email, _document) do
    {:error, :not_found}
  end

  defp preload_customers(customers), do: Enum.map(customers, &Rms.Repo.preload(&1, [:addresses]))

  defp update_customers(customer_sync, customers, customer, kind) do
    Rms.Repo.transaction_with(fn ->
      Enum.reduce_while(customers, [], &update_customers(&1, &2, customer_sync, customer, kind))
    end)
  end

  defp update_customers(customer_db, acc, customer_sync, customer, kind) do
    case update_customer(customer_sync, customer_db, customer, kind) do
      {:ok, customer} -> {:cont, {:ok, [customer | acc]}}
      {:error, _} = error -> {:halt, error}
    end
  end

  defp update_customer(customer_sync, customer_db, customer, :id) do
    address = customer["defaultAddress"]
    document = get_in(address, ["company"])

    {document_type, document} =
      cond do
        Brcpfcnpj.cpf_valid?(document) -> {"cpf", document}
        Brcpfcnpj.cnpj_valid?(document) -> {"cnpj", document}
        :else -> {nil, nil}
      end

    attrs = %{
      name: "#{customer["firstName"]} #{customer["lastName"]}",
      document_type: document_type,
      document: document,
      email: customer["email"],
      primary_phone_number: customer["phone"]
    }

    with {:ok, _} <- Customers.update_customer(customer_db, attrs),
         {:ok, _} <- maybe_create_address(customer_db, address),
         {:ok, entry} <- create_sync_entry(customer_sync, customer["id"], "success") do
      {:ok, entry}
    else
      {:error, changeset} ->
        error = %{
          metadata: %{params: changeset.params, errors: extract_changeset_errors(changeset)},
          code: "changeset_error",
          detail: "invalid changeset"
        }

        create_sync_entry(customer_sync, customer["id"], "error", [error])
    end
  end

  defp update_customer(customer_sync, customer_db, customer, _) do
    address = customer["defaultAddress"]

    attrs = %{
      name: "#{customer["firstName"]} #{customer["lastName"]}",
      primary_phone_number: customer["phone"]
    }

    Rms.Repo.transaction_with(fn ->
      with {:ok, _} <- Customers.update_customer(customer_db, attrs),
           {:ok, _} <- maybe_create_address(customer_db, address),
           {:ok, entry} <- create_sync_entry(customer_sync, customer["id"], "success") do
        {:ok, entry}
      else
        {:error, changeset} ->
          error = %{
            metadata: %{params: changeset.params, errors: extract_changeset_errors(changeset)},
            code: "changeset_error",
            detail: "invalid changeset"
          }

          create_sync_entry(customer_sync, customer["id"], "error", [error])
      end
    end)
  end

  defp import_customer(customer_sync, organization_id, customer) do
    address = customer["defaultAddress"]
    document = get_in(address, ["company"])

    {document_type, document} =
      cond do
        Brcpfcnpj.cpf_valid?(document) -> {"cpf", document}
        Brcpfcnpj.cnpj_valid?(document) -> {"cnpj", document}
        :else -> {nil, nil}
      end

    address_params = %{
      receiver_name: address["name"],
      city_name: address["city"],
      state: address["provinceCode"],
      country_name: address["country"],
      street: address["address1"],
      zip: format_zip(address["zip"]),
      complement: address["address2"]
    }

    addresses =
      if Addresses.Address.changeset(%Addresses.Address{}, address_params).valid? do
        [
          address_params
        ]
      else
        []
      end

    customer_attrs = %{
      name: "#{customer["firstName"]} #{customer["lastName"]}",
      document_type: document_type,
      document: document,
      email: customer["email"],
      primary_phone_number: customer["phone"],
      addresses: addresses
    }

    mappings_attrs = %{external_id: customer["id"], source: "shopify"}

    Rms.Repo.transaction_with(fn ->
      with {:ok, customer_db} <-
             Customers.create_customer(organization_id, customer_attrs, skip_events: true),
           {:ok, _} <- create_mappings(organization_id, customer_db, mappings_attrs),
           {:ok, entry} <- create_sync_entry(customer_sync, customer["id"], "success") do
        {:ok, entry}
      else
        {:error, changeset} ->
          error = %{
            metadata: %{params: changeset.params, errors: extract_changeset_errors(changeset)},
            code: "changeset_error",
            detail: "invalid changeset"
          }

          create_sync_entry(customer_sync, customer["id"], "error", [error])
      end
    end)
  end

  defp maybe_create_address(customer, %{"zip" => zip} = default_address) do
    changeset =
      Addresses.Address.changeset(
        %Addresses.Address{
          organization_id: customer.organization_id,
          customer_id: customer.id
        },
        %{
          receiver_name: default_address["name"],
          city_name: default_address["city"],
          state: default_address["provinceCode"],
          country_name: default_address["country"],
          street: default_address["address1"],
          zip: format_zip(default_address["zip"]),
          complement: default_address["address2"]
        }
      )

    with true <- changeset.valid?,
         {:error, :not_found} <- Customers.get_customer_address_from_zip(customer, zip),
         {:ok, address} <- Ecto.Changeset.apply_action(changeset, :insert) do
      addresses = Enum.map([address | customer.addresses], &Map.from_struct/1)
      Customers.update_customer_address!(customer, addresses)
    else
      _ ->
        {:ok, "address not updated"}
    end
  end

  defp create_mappings(organization_id, customer, attrs) do
    attrs = Map.put_new(attrs, :customer_id, customer.id)
    Integrations.create_customer_sync_mapping(organization_id, attrs)
  end

  defp create_sync_entry(customer_sync, external_id, status, errors \\ []) do
    Integrations.create_customer_sync_entry(customer_sync, %{
      external_id: external_id,
      status: status,
      errors: errors
    })
  end

  defp extract_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, &error_traverser/1)
  end

  defp error_traverser({msg, opts}) do
    Enum.reduce(opts, msg, fn {key, value}, acc ->
      String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
    end)
  end

  def format_zip(zip) when is_binary(zip) do
    zip
    |> String.replace(~r/\D/, "")
  end

  def format_zip(_zip) do
    ""
  end
end
