defmodule Rms.Integrations.Shopify.ImportCustomersWorker do
  use Oban.Pro.Workers.Workflow, queue: :shopify

  alias Rms.Integrations

  @impl true
  def process(%Job{
        args: %{"customer_sync_id" => id, "organization_id" => organization_id} = args
      }) do
    # ensure the customer sync exists
    _customer_sync = Integrations.fetch_customer_sync!(id)

    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, result} <- fetch_customers(client, args) do
      jobs =
        for customer <- result.items do
          Integrations.Shopify.ImportCustomerWorker.new(%{
            id: id,
            organization_id: organization_id,
            customer: customer
          })
        end

      Ecto.Multi.new()
      |> Oban.insert_all(:customer_import_jobs, jobs)
      |> maybe_enqueue_next_page(id, organization_id, result)
      |> Rms.Repo.transaction()
    end
  end

  defp fetch_customers(client, args) do
    Integrations.Shopify.fetch_customers(client, first: 100, after: args["cursor"])
  end

  defp maybe_enqueue_next_page(multi, _id, _organization_id, %{cursor: nil}), do: multi

  defp maybe_enqueue_next_page(multi, id, organization_id, %{cursor: cursor}) do
    args = %{organization_id: organization_id, customer_sync_id: id, cursor: cursor}
    Ecto.Multi.insert(multi, :next_page, new(args))
  end
end
