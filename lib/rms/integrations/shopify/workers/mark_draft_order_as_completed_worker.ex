defmodule Rms.Integrations.Shopify.MarkDraftOrderAsCompletedWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  alias Rms.Commerce
  alias Rms.Integrations
  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.Build

  @preload [order: [:staff, :discounts, location: [:location_mappings], transaction: [:payments]]]
  @retry_until_ready_timeout_in_ms 5_000

  @impl true
  def process(%Job{args: %{"id" => id, "organization_id" => organization_id}}) do
    fulfillment =
      organization_id
      |> Rms.Commerce.Fulfillments.get_fulfillment!(id)
      |> Rms.Repo.preload(@preload)

    shopify_credential = Integrations.get_shopify_credential!(organization_id)
    client = Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)
    draft_order_id = fulfillment.external_reference

    case Integrations.Shopify.fetch_draft_order(client, draft_order_id) do
      {:ok,
       %{
         "id" => ^draft_order_id,
         "ready" => true,
         "status" => "COMPLETED",
         "order" => shopify_order
       }} ->
        handle_completed_draft_order(fulfillment, draft_order_id, shopify_order)

      {:ok, %{"id" => ^draft_order_id, "ready" => true}} ->
        update_draft_order(client, fulfillment, draft_order_id)

      {:ok, %{"id" => ^draft_order_id, "ready" => false}} ->
        {:snooze, @retry_until_ready_timeout_in_ms}

      {:error, _} = error ->
        error
    end
  end

  defp update_draft_order(client, fulfillment, draft_order_id) do
    with {:ok, _} <- update_draft_order_atributes(client, fulfillment, draft_order_id),
         {:ok,
          %{
            "order" => %{
              "id" => order_id,
              "fulfillmentOrders" => fulfillment_orders,
              "name" => name
            }
          }} <-
           mark_order_as_completed(client, draft_order_id),
         {:ok, _} <-
           update_fulfillment(fulfillment, %{
             status: "completed",
             external_reference: order_id,
             metadata: %{order_name: name, order_id: order_id, draft_order_id: draft_order_id}
           }) do
      {:ok, fulfillment_orders}
    end
  end

  defp update_draft_order_atributes(
         client,
         fulfillment,
         draft_order_id
       ) do
    Integrations.Shopify.update_draft_order(
      client,
      draft_order_id,
      build_update_params(fulfillment)
    )
  end

  defp mark_order_as_completed(client, id) do
    Integrations.Shopify.mark_order_as_completed(client, id)
  end

  defp update_fulfillment(fulfillment, attrs) do
    Commerce.Fulfillments.update_fulfillment(fulfillment, attrs)
  end

  defp build_update_params(fulfillment) do
    %{
      customAttributes: Build.execute(fulfillment)
    }
  end

  defp handle_completed_draft_order(fulfillment, draft_order_id, shopify_order) do
    %{
      "id" => order_id,
      "fulfillmentOrders" => fulfillment_orders,
      "name" => name
    } = shopify_order

    with {:ok, _} <-
           update_fulfillment(fulfillment, %{
             status: "completed",
             external_reference: order_id,
             metadata: %{order_name: name, order_id: order_id, draft_order_id: draft_order_id}
           }) do
      {:ok, fulfillment_orders}
    end
  end
end
