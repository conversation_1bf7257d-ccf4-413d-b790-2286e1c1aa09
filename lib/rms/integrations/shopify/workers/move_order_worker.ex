defmodule Rms.Integrations.Shopify.MoveOrderWorker do
  use Oban.Pro.Worker, recorded: true, queue: :shopify
  alias Oban.Pro.Workflow

  alias Rms.Integrations

  @preload [
    order: [location: [:location_mappings]]
  ]

  @order_selection """
  fulfillmentOrders(first: 100) {
    nodes {
      id
      fulfillAt
      status
      fulfillments(first: 100) {
        nodes {
          createdAt
          deliveredAt
          id
          status
        }
      }
      assignedLocation {
        location {
          id
        }
      }
    }
  }
  """

  @type fulfillment_orders :: %{String.t() => any()} | String.t()

  @impl true
  def process(%{args: args} = job) do
    with {:ok, fulfillment} <- fetch_fulfillment(args),
         :ok <- validate_shipping_method(fulfillment),
         {:ok, orders} <- fetch_orders(job, args),
         {:ok, client} <- build_shopify_client(args["organization_id"]),
         {:ok, mapping} <- get_location_mapping(fulfillment.order.location) do
      fulfill_orders(client, fulfillment, orders, mapping)
    else
      {:error, reason} -> {:error, reason}
      {:discard, reason} -> {:discard, reason}
    end
  end

  defp fetch_fulfillment(%{"id" => id, "organization_id" => org_id}) do
    {:ok,
     org_id
     |> Rms.Commerce.Fulfillments.get_fulfillment!(id)
     |> Rms.Repo.preload(@preload)}
  end

  defp validate_shipping_method(%{shipping_method: "in-store"}) do
    :ok
  end

  defp validate_shipping_method(%{shipping_method: method}) do
    {:discard, "Do not fulfill #{method} orders"}
  end

  defp fetch_orders(_job, %{"fulfillment_order_id" => id}) do
    {:ok, id}
  end

  defp fetch_orders(job, _) do
    case fetch_lazy(job) do
      {:ok, result} -> {:ok, result}
      error -> error
    end
  end

  defp build_shopify_client(org_id) do
    credential = Integrations.get_shopify_credential!(org_id)
    {:ok, Integrations.Shopify.client(credential.shop, credential.credential)}
  end

  defp get_location_mapping(%{location_mappings: mappings}) do
    case Enum.find(mappings, &(&1.source == "shopify")) do
      nil -> {:error, "inventory mapping not found"}
      mapping -> {:ok, mapping}
    end
  end

  defp fulfill_orders(client, fulfillment, fulfillment_orders, mapping) do
    with {:ok, _moved_orders} <- move_fulfillment_orders(client, fulfillment_orders, mapping),
         {:ok, order_data} <- fetch_order_data(client, fulfillment.external_reference),
         :ok <-
           enqueue_next_jobs(
             fulfillment.organization_id,
             fulfillment.id,
             mapping,
             get_in(order_data, ["data", "order"])
           ) do
      {:ok, "order fulfilled"}
    end
  end

  defp move_fulfillment_orders(client, %{"nodes" => orders}, mapping) do
    case Enum.reduce_while(orders, [], &move_single_order(client, &1, mapping, &2)) do
      {:error, _} = error -> error
      [] -> {:error, "Cannot move fulfillment orders"}
      results -> {:ok, results}
    end
  end

  defp move_fulfillment_orders(client, order_id, mapping) when is_binary(order_id) do
    case move_fulfillment(client, %{"id" => order_id}, mapping) do
      {:ok, result} -> {:ok, [result]}
      error -> error
    end
  end

  defp move_fulfillment(client, %{"id" => id}, %{external_id: external_id}) do
    case Integrations.Shopify.move_fulfillment(client, id, external_id) do
      {:ok, response} ->
        {:ok, response}

      {:error, {_, %{"field" => _, "message" => "Cannot move to the current origin location."}}} ->
        {:ok, "alredy in location"}

      error ->
        error
    end
  end

  defp move_fulfillment(_, _, _) do
    {:error, "invalid params"}
  end

  defp fetch_order_data(client, external_reference) do
    Integrations.Shopify.get_order!(client, external_reference, @order_selection)
  end

  defp move_single_order(client, order, mapping, acc) do
    case move_fulfillment(client, order, mapping) do
      {:error, _} -> {:cont, [acc]}
      {:ok, line_item} -> {:cont, [line_item | acc]}
    end
  end

  defp enqueue_next_jobs(organization_id, fulfillment_id, %{external_id: external_id}, %{
         "fulfillmentOrders" => %{
           "nodes" => fulfill_orders
         }
       }) do
    Enum.each(fulfill_orders, fn fulfill_order ->
      if fulfill_order["assignedLocation"]["location"]["id"] == external_id do
        %{
          id: fulfillment_id,
          organization_id: organization_id,
          fulfillment_order_id: fulfill_order["id"]
        }
        |> Rms.Integrations.Shopify.FulfillOrderWorker.new()
        |> Oban.insert()
      else
        %{
          id: fulfillment_id,
          organization_id: organization_id,
          fulfillment_order_id: fulfill_order["id"]
        }
        |> Rms.Integrations.Shopify.MoveOrderWorker.new()
        |> Oban.insert()
      end
    end)

    :ok
  end

  defp fetch_lazy(%{args: %{"result" => result}}), do: {:ok, result}

  defp fetch_lazy(job) do
    job
    |> Workflow.all_jobs(names: ["mark_order_as_complete"])
    |> List.first()
    |> fetch_recorded()
  end
end
