defmodule Rms.Integrations.Shopify.UpdateOrderNameWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :shopify

  alias Rms.Commerce

  import Ecto.Query

  @impl true
  def process(%Job{args: %{"id" => id, "organization_id" => organization_id}}) do
    fulfillment =
      organization_id
      |> Rms.Commerce.Fulfillments.get_fulfillment!(id)

    case update_order_name(fulfillment) do
      {:ok, _} ->
        {:ok, "Order updated"}

      error ->
        {:error, error}
    end
  end

  defp update_order_name(%{
         order_id: order_id,
         organization_id: organization_id,
         external_reference: _,
         metadata: %{"order_name" => shopify_order_name}
       })
       when not is_nil(shopify_order_name) do
    Rms.Repo.transaction(fn ->
      order =
        Commerce.Orders.Order
        |> where([o], o.id == ^order_id and o.organization_id == ^organization_id)
        |> lock("FOR UPDATE")
        |> Rms.Repo.one()

      updated_name =
        if order.name == nil or order.name == "" do
          shopify_order_name
        else
          "#{order.name}, #{shopify_order_name}"
        end

      Commerce.Orders.update_order_name(order, updated_name)
    end)
  end

  defp update_order_name(_) do
    {:error, "Invalid fulfillment format"}
  end
end
