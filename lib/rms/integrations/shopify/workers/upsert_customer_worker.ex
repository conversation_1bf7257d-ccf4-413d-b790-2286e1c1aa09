defmodule Rms.Integrations.Shopify.UpsertCustomerWorker do
  use Oban.Pro.Worker,
    queue: :shopify,
    unique: [states: [:available, :scheduled, :executing, :retryable]]

  @impl true
  def process(%Job{
        args: %{
          "organization_id" => organization_id,
          "shopify_event" => _shopify_event,
          "customer_external_id" => _customer_external_id,
          "external_customer" => external_customer
        }
      }) do
    organization_id |> Rms.Integrations.Shopify.Clients.Webhooks.Create.execute(external_customer)
  end
end
