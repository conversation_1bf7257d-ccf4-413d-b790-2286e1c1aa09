defmodule Rms.Integrations.Shopify.UpsertProductWorker do
  use Oban.Pro.Worker,
    queue: :shopify,
    unique: [
      keys: [:external_product_id],
      states: [:available, :scheduled, :executing, :retryable]
    ]

  @impl true
  def process(%Job{
        args: %{
          "organization_id" => organization_id,
          "sync_type" => sync_type,
          "full_sync_id" => full_sync_id,
          "external_product_id" => _external_product_id,
          "external_product" => external_product
        }
      }) do
    sync_type = String.to_existing_atom(sync_type)

    organization_id
    |> Rms.Integrations.create_product_sync!(
      %{external_id: full_sync_id, source: "shopify"},
      on_conflict: {:replace, [:source, :external_id]},
      conflict_target: [:organization_id, :source, :external_id]
    )
    |> Rms.Integrations.import_shopify_product(external_product, sync_type)
  end
end
