defmodule Rms.Integrations.ShopifyApp do
  use Ecto.Schema

  import Ecto.Changeset

  schema "shopify_apps" do
    field :client_id, :string
    field :client_secret, Rms.Vault.EncryptedBinary
    field :shop_domain, :string

    belongs_to :organization, Rms.Accounts.Organization
  end

  @doc false
  def changeset(shopify_app, attrs) do
    shopify_app
    |> cast(attrs, [:client_id, :client_secret, :shop_domain])
    |> validate_required([:client_id, :client_secret, :shop_domain, :organization_id])
  end
end
