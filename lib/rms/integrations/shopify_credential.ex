defmodule Rms.Integrations.ShopifyCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "shopify_credentials" do
    field :shop, :string
    field :credential, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(shopify, attrs) do
    shopify
    |> cast(attrs, [:shop, :credential])
    |> validate_required([:shop, :credential])
    |> unique_constraint(:organization_id)
  end
end
