defmodule Rms.Integrations.ShopifyStorefrontCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "shopify_storefront_credentials" do
    field :credential_id, Rms.Vault.EncryptedBinary
    field :credential, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(shopify, attrs) do
    shopify
    |> cast(attrs, [:credential_id, :credential])
    |> validate_required([:credential_id, :credential, :organization_id])
    |> unique_constraint(:organization_id)
  end
end
