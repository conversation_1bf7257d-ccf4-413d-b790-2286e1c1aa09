defmodule Rms.Integrations.SmartStore.Client do
  @behaviour Rms.Integrations.SmartStore.ClientBehaviour

  @impl Rms.Integrations.SmartStore.ClientBehaviour
  def client(api_key) when is_binary(api_key) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://smartstore-barramento.rsvcloud.com"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers, [{"X-Api-Key", api_key}]},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  @impl Rms.Integrations.SmartStore.ClientBehaviour
  def get_inventory(client, cnpj, sku) do
    response =
      client
      |> Tesla.get!("/stock/sku", query: [cnpj: cnpj, sku: sku])
      |> normalize_response()

    with {:ok, body} <- response do
      {:ok, %{"sku" => String.trim(body["sku"]), "quantity" => body["quantidade"]}}
    end
  end

  defp normalize_response(%{body: body} = response) when is_binary(body) do
    case Jason.decode(body) do
      {:ok, body} -> do_normalize_response(%{response | body: body})
      {:error, _} -> do_normalize_response(response)
    end
  end

  defp normalize_response(response), do: do_normalize_response(response)

  defp do_normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp do_normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp do_normalize_response(%{status: 400, body: body}) do
    {:error, {:bad_request, body}}
  end

  defp do_normalize_response(%{status: 403, body: body}) do
    {:error, {:forbidden, body}}
  end

  defp do_normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp do_normalize_response(%{status: status_code, body: body})
       when status_code >= 500 and status_code < 600 do
    {:error, {:internal_server_error, body}}
  end

  defp do_normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp do_normalize_response(%{status: _status_code, body: body}) do
    {:error, {:unknown_error, body}}
  end
end
