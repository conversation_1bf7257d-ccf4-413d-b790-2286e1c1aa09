defmodule Rms.Integrations.SmartStore.ClientBehaviour do
  @opaque client() :: Tesla.Client.t()
  @callback client(api_key :: binary()) :: client()

  @type errors() ::
          :not_found
          | {:forbidden, map()}
          | {:bad_request, map()}
          | {:internal_server_error, map()}
          | {:validation_error, map()}
          | {:unknown_error, map()}
  @type result() :: {:ok, map()} | {:error, errors()}

  @callback get_inventory(client :: client(), cnpj :: binary(), sku :: binary()) :: result()
end
