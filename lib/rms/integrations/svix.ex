defmodule Rms.Integrations.Svix do
  alias Rms.Integrations.Svix.Client, as: SvixClient
  @svix_client Application.compile_env(:rms, :svix_client, SvixClient)

  def client() do
    @svix_client.client()
  end

  def create_app(client, org_id, app_name) do
    @svix_client.create_app(client, org_id, app_name)
  end

  def send_message(client, org_id, payload) do
    @svix_client.send_message(client, org_id, payload)
  end
end
