defmodule Rms.Integrations.Svix.Client do
  @behaviour Rms.Integrations.Svix.ClientBehaviour

  @impl Rms.Integrations.Svix.ClientBehaviour
  def client() do
    token = Application.get_env(:rms, :svix_token)

    middleware = [
      {Tesla.Middleware.BaseUrl, "https://api.us.svix.com/api/v1"},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.BearerAuth, token: token},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  @impl Rms.Integrations.Svix.ClientBehaviour
  def create_app(client, org_id, app_name) do
    client
    |> Tesla.post!("/app/", %{name: app_name, uid: org_id})
    |> normalize_response()
  end

  @impl Rms.Integrations.Svix.ClientBehaviour
  def send_message(client, org_id, payload) do
    client
    |> Tesla.post!("/app/:org_id/msg/", payload, opts: [path_params: [org_id: org_id]])
    |> normalize_response()
  end

  defp normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: 409, body: _body}) do
    {:error, :duplicate_event}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
