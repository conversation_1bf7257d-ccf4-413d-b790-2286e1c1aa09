defmodule Rms.Integrations.Viacep.Client do
  alias Rms.Integrations.Viacep.ClientBehaviour
  @behaviour ClientBehaviour
  @viacep_url "https://viacep.com.br/ws"

  @impl ClientBehaviour
  def get_info_by_postal_code(postal_code) do
    Tesla.get!(
      client(),
      "/#{postal_code |> clean_postal_code()}/json"
    )
    |> normalize_response()
  end

  defp clean_postal_code(postal_code),
    do: postal_code |> String.trim() |> String.replace(~r/\D/, "") |> String.slice(0, 8)

  @impl ClientBehaviour
  def client() do
    middleware = [
      {Tesla.Middleware.BaseUrl, @viacep_url},
      Tesla.Middleware.JSON
    ]

    Tesla.client(middleware)
  end

  defp normalize_response(%{status: status, body: body}) when status in 200..299 do
    case body do
      %{"ibge" => _} ->
        {:ok, body}

      _ ->
        {:error, :not_found}
    end
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
