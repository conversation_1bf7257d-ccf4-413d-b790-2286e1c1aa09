defmodule Rms.Integrations.Vinco do
  import Ecto.Query
  alias Rms.Repo
  alias Rms.Integrations.Vinco.Client, as: VincoClient
  alias Rms.Integrations.Vinco.Credential, as: VincoCredential

  @vinco_client Application.compile_env(:rms, :vinco_client, VincoClient)

  def create_fiscal_invoice(
        invoice_type,
        origin_uf,
        vinco_invoice,
        vinco_id_key
      ) do
    vinco_url = Application.get_env(:rms, :vinco_url)

    @vinco_client.create_fiscal_invoice(
      %{invoice_type: invoice_type, origin_uf: origin_uf},
      vinco_invoice,
      vinco_id_key,
      vinco_url
    )
  end

  def cancel_fiscal_invoice(
        vinco_invoice,
        vinco_id_key
      ) do
    vinco_url = Application.get_env(:rms, :vinco_url)

    @vinco_client.cancel_fiscal_invoice(
      vinco_invoice,
      vinco_id_key,
      vinco_url
    )
  end

  def get_fiscal_invoice(document_type, df_key, env, vinco_id_key) do
    vinco_url = Application.get_env(:rms, :vinco_url)

    @vinco_client.get_fiscal_invoice(
      document_type,
      df_key,
      env,
      vinco_id_key,
      vinco_url
    )
  end

  def get_key!(organization_id, location_id) do
    VincoCredential
    |> where(location_id: ^location_id, organization_id: ^organization_id)
    |> Repo.one!()
    |> Map.get(:key)
  end
end
