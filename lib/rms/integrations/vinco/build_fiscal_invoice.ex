defmodule Rms.Integrations.Vinco.BuildFiscalInvoice do
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Integrations.Vinco.Prepare

  def execute(%FiscalInvoice{operation_type: "sale"} = fiscal_invoice) do
    fiscal_invoice = preload_params(fiscal_invoice)
    invoice_model = vinco_invoice_model(fiscal_invoice)

    with {:ok, invoice_with_customer} <-
           Prepare.Customer.execute(
             invoice_model,
             fiscal_invoice.serie,
             fiscal_invoice.customer
           ),
         {:ok, invoice_with_issuer} <-
           Prepare.Issuer.execute(invoice_with_customer, fiscal_invoice.serie),
         {:ok, invoice_with_payments} <-
           Prepare.Payments.execute(
             invoice_with_issuer,
             fiscal_invoice
           ),
         {:ok, invoice_with_items} <-
           Prepare.Items.execute(
             invoice_with_payments,
             fiscal_invoice.serie,
             fiscal_invoice.invoice_items
           ),
         {:ok, invoice_with_additional_info} <-
           Prepare.AdditionalInfo.execute(
             invoice_with_items,
             fiscal_invoice.serie,
             fiscal_invoice.invoice_items
           ) do
      Prepare.Total.execute(invoice_with_additional_info, fiscal_invoice.serie)
    end
  end

  defp preload_params(fiscal_invoice) do
    Rms.Repo.preload(fiscal_invoice, [
      :customer,
      :organization,
      invoice_items: [line_item: [fulfillment: [order: [:staff]], product_variant: [:product]]],
      invoice_payments: [:payment],
      serie: [:organization, location: [:address]]
    ])
  end

  defp vinco_invoice_model(
         %FiscalInvoice{
           operation_type: "sale",
           serie: %InvoiceSerie{invoice_type: type, invoice_env: env}
         } = fiscal_invoice
       ) do
    %{
      Sincrono: true,
      IdKeySistema: "#{fiscal_invoice.id}",
      Venda: "#{fiscal_invoice.id}",
      ide: %{
        cUF: "",
        cMunFG: "",
        natOp: "Operacao interna",
        mod: invoice_type(type),
        serie: fiscal_invoice.serie.invoice_serie,
        nNF: fiscal_invoice.invoice_number,
        dhEmi: date_time(),
        # Prod = 1 /Homologação = 2
        tpAmb: invoice_env(env),
        indFinal: "1",
        tpNF: "1",
        tpImp: "4",
        idDest: "1",
        tpEmis: "1",
        finNFe: "1",
        procEmi: "0",
        indPres: "1"
      },
      transp: %{
        modFrete: "9",
        vol: [
          %{
            qVol: "1"
          }
        ]
      },
      infRespTec: %{
        CNPJ: "46991783000113",
        xContato: "Iglu Tecnologia",
        email: "<EMAIL>",
        fone: "16991080570"
      }
    }
  end

  defp date_time() do
    date = DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase)

    month = String.pad_leading("#{date.month}", 2, "0")
    day = String.pad_leading("#{date.day}", 2, "0")
    hour = String.pad_leading("#{date.hour}", 2, "0")
    minute = String.pad_leading("#{date.minute}", 2, "0")
    second = String.pad_leading("#{date.second}", 2, "0")

    "#{date.year}-#{month}-#{day}T#{hour}:#{minute}:#{second}-03:00"
  end

  defp invoice_env(env) do
    if env == "prod" do
      "1"
    else
      "2"
    end
  end

  defp invoice_type(invoice_type) do
    case invoice_type do
      "nf" ->
        "55"

      "nfc" ->
        "65"
    end
  end
end
