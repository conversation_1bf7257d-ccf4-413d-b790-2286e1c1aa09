defmodule Rms.Integrations.Vinco.CancelFiscalInvoice do
  alias Rms.Integrations.Vinco
  alias Rms.Fiscal

  def execute(fiscal_invoice) do
    fiscal_invoice =
      Rms.Repo.preload(fiscal_invoice, [
        :customer,
        :organization,
        invoice_items: [line_item: [product_variant: [:product]]],
        invoice_payments: [:payment],
        serie: [:organization, location: [:address]]
      ])

    with {:ok, params} <-
           build_params(fiscal_invoice),
         {:ok, response} <-
           Vinco.cancel_fiscal_invoice(
             params,
             get_vinco_config(fiscal_invoice.serie)
           ),
         {:ok, cancelation_invoice} <-
           create_update_params(fiscal_invoice, response),
         {:ok, _updated_invoice} <-
           Fiscal.update_fiscal_invoice(fiscal_invoice, %{
             status: "canceled",
             canceled_at: DateTime.utc_now()
           }) do
      Rms.Events.emit("invoice.canceled", %{
        id: cancelation_invoice.id,
        organization_id: cancelation_invoice.organization_id
      })

      {:ok, cancelation_invoice}
    end
  end

  defp get_vinco_config(serie) do
    case Vinco.get_key!(serie.organization_id, serie.location_id) do
      nil ->
        raise "no valid vinco config found for organization_id #{serie.organization_id} and location_id #{serie.location_id}"

      value ->
        value
    end
  end

  defp create_update_params(invoice, %{
         "IdEvento" => _,
         "CodStatus" => code_status,
         "Motivo" => "Evento registrado e vinculado a NF-e",
         "ChaveEvento" => _,
         "ProtocoloEvento" => protocol,
         "XmlEvento" => xml
       })
       when code_status > 0 and code_status != 501 do
    Rms.Fiscal.create_fiscal_invoice(
      invoice.organization_id,
      %{
        status: "authorized",
        operation_type: "cancellation",
        service: "external",
        invoice_number: invoice.id,
        fulfillment_id: invoice.fulfillment_id,
        authorized_at: DateTime.utc_now(),
        xml: xml,
        metadata: %{
          "DFeProtocolo" => protocol,
          fiscal_invoice_id: invoice.id
        }
      }
    )
  end

  defp create_update_params(_, %{"Motivo" => reason, "CodStatus" => status}) do
    {:error, %{reason: reason, status: status}}
  end

  defp create_update_params(_, response) do
    {:error, response}
  end

  defp build_params(fiscal_invoice) do
    with {:ok, cnpj} <- cnpj(fiscal_invoice),
         {:ok, state_code} <- state_code(fiscal_invoice),
         {:ok, invoice_env} <- invoice_env(fiscal_invoice) do
      {:ok,
       %{
         "cOrgao" => state_code,
         "tpAmb" => invoice_env,
         "CNPJ" => cnpj,
         "chNFe" => fiscal_invoice.df_key,
         "dhEvento" => date_time(),
         "tpEvento" => "110111",
         "nSeqEvento" => "1",
         "verEvento" => "1.00",
         "DetalheDoEvento" => %{
           "nProt" => fiscal_invoice.metadata["DFeProtocolo"],
           "xJust" => "Cancelamento nota errada"
         }
       }}
    end
  end

  defp state_code(%{serie: %{location: %{address: %{city_code: city_code}}}})
       when not is_nil(city_code) do
    {:ok, String.to_integer(String.slice(city_code, 0..1))}
  end

  defp state_code(_) do
    {:error, "invalid city code"}
  end

  defp cnpj(%{serie: %{location: %{cnpj: cnpj}}}) when not is_nil(cnpj) do
    {:ok, cnpj}
  end

  defp cnpj(_) do
    {:error, "invalid cnpj"}
  end

  defp invoice_env(%{serie: %{invoice_env: env}}) do
    if env == "prod" do
      {:ok, "1"}
    else
      {:ok, "2"}
    end
  end

  defp invoice_env(_) do
    {:error, "invalid invoice env"}
  end

  defp date_time() do
    date = DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase)

    month = String.pad_leading("#{date.month}", 2, "0")
    day = String.pad_leading("#{date.day}", 2, "0")
    hour = String.pad_leading("#{date.hour}", 2, "0")
    minute = String.pad_leading("#{date.minute}", 2, "0")
    second = String.pad_leading("#{date.second}", 2, "0")

    "#{date.year}-#{month}-#{day}T#{hour}:#{minute}:#{second}-03:00"
  end
end
