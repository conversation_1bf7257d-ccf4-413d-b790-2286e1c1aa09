defmodule Rms.Integrations.Vinco.Client do
  alias Rms.Integrations.Vinco.ClientBehaviour
  @behaviour ClientBehaviour

  @impl ClientBehaviour
  def create_fiscal_invoice(%{invoice_type: "nf"}, vinco_invoice, vinco_id_key, vinco_url) do
    Tesla.post!(
      client(vinco_url(vinco_url), vinco_id_key),
      "/api/NFe/Enviar",
      vinco_invoice
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def create_fiscal_invoice(%{invoice_type: "nfc"}, vinco_invoice, vinco_id_key, vinco_url) do
    Tesla.post!(
      client(vinco_url(vinco_url), vinco_id_key),
      "/api/NFCe/Enviar",
      vinco_invoice
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def get_fiscal_invoice(document_type, df_key, env, vinco_id_key, vinco_url) do
    Tesla.get!(
      client(vinco_url(vinco_url), vinco_id_key),
      "/api/#{document_type}/VerificarComChaveSimplificado?chaveDFe=#{df_key}&ambiente=#{env}&simplificado=0"
    )
    |> normalize_response()
  end

  @impl ClientBehaviour
  def cancel_fiscal_invoice(vinco_invoice, vinco_id_key, vinco_url) do
    Tesla.post!(
      client(vinco_url(vinco_url), vinco_id_key),
      "/api/NFCe/Evento",
      vinco_invoice
    )
    |> normalize_response()
  end

  defp client(vinco_url, vinco_id_key) do
    middleware = [
      {Tesla.Middleware.BaseUrl, vinco_url},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Timeout, timeout: 60_000},
      {Tesla.Middleware.Headers,
       [
         {"IdKeyPartner", Application.get_env(:rms, :vinco_key_partner)},
         {"PartnerAuthorizationToken", Application.get_env(:rms, :vinco_token)},
         {"IdKeyEmpresa", vinco_id_key}
       ]},
      Tesla.Middleware.OpenTelemetry
    ]

    Tesla.client(middleware, {Tesla.Adapter.Finch, [name: Rms.Finch, receive_timeout: 60_000]})
  end

  defp vinco_url(nil) do
    Application.get_env(:rms, :vinco_url)
  end

  defp vinco_url(url) do
    url
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
