defmodule Rms.Integrations.Vinco.ClientBehaviour do
  @type result :: {:ok, map()} | {:error, any()}

  @callback create_fiscal_invoice(
              fiscal_invoice :: map(),
              vinco_invoice :: map(),
              vinco_id_key :: String.t(),
              vinco_url :: String.t()
            ) :: result()

  @callback cancel_fiscal_invoice(
              vinco_invoice :: map(),
              vinco_id_key :: String.t(),
              vinco_url :: String.t()
            ) :: result()

  @callback get_fiscal_invoice(
              document_type :: String.t(),
              df_key :: String.t(),
              env :: String.t(),
              vinco_id_key :: String.t(),
              vinco_url :: String.t()
            ) :: result()
end
