defmodule Rms.Integrations.Vinco.Credential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "vinco_credential" do
    field :key, Rms.Vault.EncryptedBinary

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location
    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(credential, attrs) do
    credential
    |> cast(attrs, [:location_id, :key])
    |> validate_required([:location_id, :key])
  end
end
