defmodule Rms.Integrations.Vinco.ImportFiscalInvoice do
  alias Rms.Integrations.Vinco
  alias Rms.Fiscal

  require Logger

  def execute(fiscal_invoice, df_key) do
    fiscal_invoice =
      Rms.Repo.preload(
        fiscal_invoice,
        [:serie]
      )

    with vinco_key <- get_vinco_config(fiscal_invoice.serie),
         {:ok, response} <-
           Rms.Integrations.Vinco.get_fiscal_invoice(
             get_invoice_type(fiscal_invoice),
             df_key,
             get_invoice_env(fiscal_invoice),
             vinco_key
           ),
         {:ok, update_params} <- create_update_params(response),
         {:ok, invoice} <- update_fiscal_invoice(fiscal_invoice, update_params) do
      emit_invoice_event(invoice)
      {:ok, invoice}
    end
  end

  defp emit_invoice_event(invoice) do
    invoice =
      Rms.Repo.preload(invoice, [
        :serie,
        :customer,
        :fulfillment,
        invoice_payments: [
          payment: [
            transaction: [
              order: [
                :fulfillments
              ]
            ]
          ]
        ]
      ])

    Rms.Events.emit("invoice.issued", %{
      id: invoice.id,
      invoice: RmsWeb.Fiscal.FiscalInvoiceJSON.render("raw_show.json", %{invoice: invoice}),
      organization_id: invoice.organization_id
    })
  end

  defp update_fiscal_invoice(fiscal_invoice, update_params) do
    case Fiscal.update_fiscal_invoice(fiscal_invoice, update_params) do
      {:ok, invoice} ->
        {:ok, invoice}

      error ->
        Logger.error("Failed to update fiscal invoice: #{inspect(error)}")
        error
    end
  end

  defp get_invoice_type(%{serie: %{invoice_type: type}}) do
    case type do
      "nf" ->
        "NFe"

      "nfc" ->
        "NFCe"
    end
  end

  defp get_invoice_env(%{serie: %{invoice_env: env}}) do
    case env do
      "dev" ->
        2

      "prod" ->
        1
    end
  end

  defp get_vinco_config(serie) do
    case Vinco.get_key!(serie.organization_id, serie.location_id) do
      nil ->
        raise "no valid vinco config found for organization_id #{serie.organization_id} and location_id #{serie.location_id}"

      value ->
        value
    end
  end

  defp create_update_params(%{
         "ChaveDFe" => df_key,
         "CodStatus" => 100,
         "DFeProtocolo" => protocol,
         "IdAssincrono" => id,
         "QrCode" => qr_code,
         "UrlDanfe" => url_danfe,
         "XmlDFe" => xml_dfe,
         "XmlRecebimento" => xml_receiption,
         "XmlRecibo" => xml_receipt
       }) do
    {:ok,
     %{
       df_key: df_key,
       status: "authorized",
       external_id: "#{id}",
       authorized_at: DateTime.utc_now(),
       qr_code: qr_code,
       xml: xml_dfe,
       danfe: url_danfe,
       metadata: %{
         "XmlRecebimento" => xml_receiption,
         "XmlRecibo" => xml_receipt,
         "DFeProtocolo" => protocol
       }
     }}
  end

  defp create_update_params(%{"Motivo" => reason, "CodStatus" => status}) do
    {:error, %{reason: reason, status: status}}
  end

  defp create_update_params(response) do
    {:error, response}
  end
end
