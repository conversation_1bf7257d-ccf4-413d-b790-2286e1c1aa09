defmodule Rms.Integrations.Vinco.IssueFiscalInvoice do
  alias Rms.Errors
  alias Rms.Fiscal
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Integrations.Vinco

  require Logger

  def execute(%FiscalInvoice{operation_type: "sale"} = fiscal_invoice) do
    fiscal_invoice =
      Rms.Repo.preload(fiscal_invoice, [
        :customer,
        :organization,
        invoice_items: [line_item: [product_variant: [:product]]],
        invoice_payments: [:payment],
        serie: [:organization, location: [:address]]
      ])

    with {:ok, invoice} <- Vinco.BuildFiscalInvoice.execute(fiscal_invoice),
         {:ok, response} <- create_vinco_fiscal_invoice(fiscal_invoice, invoice),
         {:ok, update_params} <- create_update_params(response),
         {:ok, updated_fiscal_invoice} <- update_fiscal_invoice(fiscal_invoice, update_params) do
      updated_fiscal_invoice
      |> Rms.Repo.preload([
        :serie,
        :customer,
        :fulfillment,
        invoice_payments: [
          payment: [
            transaction: [
              order: [
                :fulfillments
              ]
            ]
          ]
        ]
      ])
    else
      {:error, %{reason: reason, status: _} = error} ->
        if String.contains?(
             reason,
             "O elemento 'http://www.portalfiscal.inf.br/nfe:NCM' é inválido"
           ) do
          Errors.create_fiscal_invoice_error(fiscal_invoice, %{
            reason: reason,
            status: :invalid_ncm
          })
        else
          Errors.create_fiscal_invoice_error(fiscal_invoice, error)
        end

        {:error, error}

      {:error, _} = error ->
        Errors.create_fiscal_invoice_error(fiscal_invoice, %{
          reason: inspect(error),
          status: :unknown
        })

        error
    end
  end

  defp update_fiscal_invoice(fiscal_invoice, update_params) do
    case Fiscal.update_fiscal_invoice(fiscal_invoice, update_params) do
      {:ok, invoice} ->
        {:ok, invoice}

      error ->
        Logger.error("Failed to update fiscal invoice: #{inspect(error)}")
        error
    end
  end

  defp create_vinco_fiscal_invoice(fiscal_invoice, invoice) do
    case Vinco.create_fiscal_invoice(
           fiscal_invoice.serie.invoice_type,
           invoice.ide.cUF,
           invoice,
           get_vinco_config(fiscal_invoice.serie)
         ) do
      {:ok, response} ->
        {:ok, response}

      {:error, %{code: _, message: _} = error} ->
        Errors.create_fiscal_invoice_error(fiscal_invoice, error)
        Logger.error("Failed to create Vinco fiscal invoice: #{inspect(error)}")
        {:error, error}

      {:error, _} = error ->
        Logger.error("Failed to create Vinco fiscal invoice: #{inspect(error)}")
        {:error, error}
    end
  end

  defp get_vinco_config(serie) do
    case Vinco.get_key!(serie.organization_id, serie.location_id) do
      nil ->
        raise "no valid vinco config found for organization_id #{serie.organization_id} and location_id #{serie.location_id}"

      value ->
        value
    end
  end

  defp create_update_params(%{
         "ChaveDFe" => df_key,
         "CodStatus" => 100,
         "CodStatusLote" => _,
         "DFeProtocolo" => protocol,
         "IdAssincrono" => id,
         "Motivo" => _,
         "MotivoLote" => _,
         "QrCode" => qr_code,
         "ReportZip" => _,
         "TimeOutAssincrono" => _,
         "Token" => _,
         "UrlDanfe" => url_danfe,
         "XmlDFe" => xml_dfe,
         "XmlRecebimento" => xml_receiption,
         "XmlRecibo" => xml_receipt
       }) do
    {:ok,
     %{
       df_key: df_key,
       status: "authorized",
       external_id: "#{id}",
       authorized_at: DateTime.utc_now(),
       qr_code: qr_code,
       xml: xml_dfe,
       danfe: url_danfe,
       metadata: %{
         "XmlRecebimento" => xml_receiption,
         "XmlRecibo" => xml_receipt,
         "DFeProtocolo" => protocol
       }
     }}
  end

  defp create_update_params(%{"Motivo" => reason, "CodStatus" => status}) do
    {:error, %{reason: reason, status: status}}
  end

  defp create_update_params(response) do
    {:error, response}
  end
end
