defmodule Rms.Integrations.Vinco.Prepare.AdditionalInfo do
  alias Rms.Fiscal.InvoiceSerie

  def execute(
        vinco_invoice,
        %InvoiceSerie{invoice_type: _invoice_type, invoice_env: _env},
        invoice_items
      ) do
    [invoice_item | _] = invoice_items
    order = invoice_item.line_item.fulfillment.order
    order = Rms.Repo.preload(order, [:staff, :location, transaction: [:customer]])

    current_info = (vinco_invoice[:infAdic] || %{})[:infCpl] || ""

    additional_info =
      current_info
      |> append_if_not_empty("|")
      |> add_order_infos(order)
      |> add_cnpj_additional_info(order)

    {:ok, Map.put(vinco_invoice, :infAdic, %{infCpl: additional_info})}
  end

  defp append_if_not_empty("", _suffix), do: ""
  defp append_if_not_empty(str, suffix), do: "#{str} #{suffix}"

  defp add_order_infos(additional_info, order) do
    additional_info
    |> add_iglu_id(order)
    |> add_ecommerce_name(order)
    |> add_staff_name(order)
    |> add_location_info(order)
  end

  defp add_iglu_id(additional_info, order) do
    "#{additional_info} iGlu ID: #{order.id}" |> String.trim()
  end

  defp add_ecommerce_name(additional_info, %{name: nil} = _order) do
    additional_info
  end

  defp add_ecommerce_name(additional_info, order) do
    with %{organization_id: organization_id} <- order,
         %{value: %{"data" => ecommerce}} <-
           Rms.Settings.get_organization_setting(
             organization_id,
             "organization_order_integration_services"
           ) do
      add_ecommerce_name(additional_info, order, ecommerce)
    else
      _ -> add_default_ecommerce_name(additional_info, order)
    end
  end

  defp add_ecommerce_name(additional_info, order, "shopify") do
    "#{additional_info} | Shopify Name: #{order.name}"
  end

  defp add_ecommerce_name(additional_info, _order, _) do
    additional_info
  end

  defp add_default_ecommerce_name(additional_info, order) do
    if order.name do
      "#{additional_info} | Shopify Name: #{order.name}"
    else
      additional_info
    end
  end

  defp add_cnpj_additional_info(additional_info, %{
         transaction: %{customer: %{additional_information: info}}
       })
       when not is_nil(info) do
    "#{additional_info} | #{info}"
  end

  defp add_cnpj_additional_info(additional_info, _order) do
    additional_info
  end

  defp add_staff_name(additional_info, %{staff: nil} = _order) do
    additional_info
  end

  defp add_staff_name(additional_info, order) do
    "#{additional_info} | Vendedor: #{order.staff.name}"
  end

  defp add_location_info(additional_info, %{location: nil} = _order) do
    additional_info
  end

  defp add_location_info(additional_info, order) do
    "#{additional_info} |[Origem: #{order.location.name}]"
  end
end
