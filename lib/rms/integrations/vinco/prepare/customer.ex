defmodule Rms.Integrations.Vinco.Prepare.Customer do
  alias Rms.Fiscal.InvoiceSerie

  def execute(
        vinco_invoice,
        %InvoiceSerie{invoice_type: invoice_type, invoice_env: env},
        nil
      ) do
    case invoice_type do
      "nf" ->
        {:error, "A client for NF is mandatory"}

      "nfc" ->
        case env do
          "prod" ->
            {:ok, Map.put(vinco_invoice, :dest, %{})}

          _ ->
            {:ok,
             Map.put(vinco_invoice, :dest, %{
               xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
             })}
        end
    end
  end

  def execute(
        vinco_invoice,
        %InvoiceSerie{invoice_type: invoice_type, invoice_env: env},
        customer
      ) do
    prepare_customer(vinco_invoice, env, customer, invoice_type)
  end

  defp prepare_customer(vinco_invoice, "prod", customer, invoice_type) do
    with {:ok, client_with_name} <- get_name(%{}, customer, invoice_type),
         {:ok, client_with_document} <- get_document(client_with_name, customer, invoice_type),
         {:ok, client_with_address} <- get_address(client_with_document, customer, invoice_type),
         {:ok, client_with_email} <- get_email(client_with_address, customer, invoice_type) do
      {:ok, Map.put(vinco_invoice, :dest, client_with_email)}
    end
  end

  defp prepare_customer(vinco_invoice, "dev", customer, invoice_type) do
    with {:ok, client_with_document} <-
           get_document(
             %{xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"},
             customer,
             invoice_type
           ),
         {:ok, client_with_address} <- get_address(client_with_document, customer, invoice_type),
         {:ok, client_with_email} <- get_email(client_with_address, customer, invoice_type) do
      {:ok, Map.put(vinco_invoice, :dest, client_with_email)}
    end
  end

  defp get_name(invoice_params, customer, "nf") do
    case customer.name do
      nil ->
        {:error, "name is mandatory"}

      name ->
        {:ok, Map.put(invoice_params, :xNome, sanitize_name(name))}
    end
  end

  defp get_name(invoice_params, customer, "nfc") do
    case customer.name do
      nil ->
        {:ok, Map.put(%{}, :xNome, "")}

      name ->
        {:ok, Map.put(invoice_params, :xNome, sanitize_name(name))}
    end
  end

  defp sanitize_name(name) when is_binary(name) do
    name
    |> Unidecode.decode()
    |> String.replace(~r/[^\x00-\x7F]/u, "")
    |> String.replace(~r/\s+/, " ")
    |> String.trim()
  end

  defp sanitize_name(name), do: name

  defp get_document(invoice_params, customer, "nf") do
    case customer.document do
      nil ->
        {:error, "document is mandatory"}

      document ->
        case customer.document_type do
          "cpf" ->
            invoice_params =
              invoice_params
              |> Map.put(:CPF, document)
              |> Map.put(:indIEDest, "9")

            {:ok, invoice_params}

          "cnpj" ->
            invoice_params =
              invoice_params
              |> Map.put(:CNPJ, document)
              |> Map.put(:indIEDest, "9")

            {:ok, invoice_params}

          _ ->
            {:error, "invalid document type"}
        end
    end
  end

  defp get_document(invoice_params, customer, "nfc") do
    case customer.document do
      nil ->
        {:ok, invoice_params}

      document ->
        case customer.document_type do
          "cpf" ->
            invoice_params =
              invoice_params
              |> Map.put(:CPF, document)
              |> Map.put(:indIEDest, "9")

            {:ok, invoice_params}

          "cnpj" ->
            invoice_params =
              invoice_params
              |> Map.put(:CNPJ, document)
              |> Map.put(:indIEDest, "9")

            {:ok, invoice_params}

          _ ->
            {:ok, invoice_params}
        end
    end
  end

  defp get_address(invoice_params, customer, "nf") do
    case customer.address do
      nil ->
        {:error, "address is mandatory"}

      address ->
        address_params =
          %{}
          |> Map.put(:xLgr, address.street)
          |> Map.put(:nro, address.number)
          |> Map.put(:xCpl, address.complement)
          |> Map.put(:cMun, address.city_code)
          |> Map.put(:xBairro, address.neighborhood)
          |> Map.put(:xMun, address.city_name)
          |> Map.put(:UF, address.state)
          |> Map.put(:CEP, address.zip)
          |> Map.put(:xPais, address.country_name)

        {:ok, Map.put(invoice_params, :enderDest, address_params)}
    end
  end

  defp get_address(invoice_params, customer, "nfc") do
    case customer.address do
      nil ->
        {:ok, invoice_params}

      address ->
        address_params =
          %{}
          |> Map.put(:xLgr, address.street)
          |> Map.put(:nro, address.number)
          |> Map.put(:xCpl, address.complement)
          |> Map.put(:cMun, address.city_code)
          |> Map.put(:xBairro, address.neighborhood)
          |> Map.put(:xMun, address.city_name)
          |> Map.put(:UF, address.state)
          |> Map.put(:CEP, address.zip)
          |> Map.put(:xPais, address.country_name)

        {:ok, Map.put(invoice_params, :enderDest, address_params)}
    end
  end

  defp get_email(invoice_params, customer, "nf") do
    case customer.email do
      nil ->
        {:error, "email is mandatory"}

      email ->
        {:ok, Map.put(invoice_params, :email, email)}
    end
  end

  defp get_email(invoice_params, customer, "nfc") do
    case customer.email do
      nil ->
        {:ok, invoice_params}

      email ->
        {:ok, Map.put(invoice_params, :email, email)}
    end
  end
end
