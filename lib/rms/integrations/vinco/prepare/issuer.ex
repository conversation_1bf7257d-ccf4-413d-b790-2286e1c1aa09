defmodule Rms.Integrations.Vinco.Prepare.Issuer do
  alias Rms.Fiscal.InvoiceSerie

  def execute(vinco_invoice, %InvoiceSerie{
        invoice_type: _invoice_type,
        location: location,
        organization: organization
      }) do
    issue_params = prepare_issuer(organization, location)

    vinco_invoice =
      vinco_invoice
      |> update_in([:ide, :cUF], fn _ ->
        String.to_integer(String.slice(issue_params.enderEmit.cMun, 0..1))
      end)
      |> update_in([:ide, :cMunFG], fn _ -> String.to_integer(issue_params.enderEmit.cMun) end)
      |> Map.put(:emit, issue_params)

    {:ok, vinco_invoice}
  end

  def prepare_issuer(organization, location) do
    location_tax = Rms.Fiscal.get_tax_config!(organization.id, location.id)

    %{
      CNPJ: location.cnpj,
      xNome: location_tax.name,
      IE: location_tax.ie,
      CRT: location_tax.crt,
      enderEmit: %{
        UF: location.address.state,
        cMun: location.address.city_code,
        xMun: location.address.city_name,
        CEP: location.address.zip,
        xBairro: location.address.neighborhood,
        xLgr: location.address.street,
        nro: location.address.number
      }
    }
  end
end
