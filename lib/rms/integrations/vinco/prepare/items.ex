defmodule Rms.Integrations.Vinco.Prepare.Items do
  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Integrations.Vinco.Prepare.ItemBuilder

  def execute(
        vinco_invoice,
        %InvoiceSerie{
          invoice_type: _invoice_type,
          invoice_env: env,
          location_id: location_id,
          organization_id: organization_id
        },
        items
      ) do
    uf_origin = vinco_invoice.emit.enderEmit[:UF]
    uf_destiny = get_client_uf(vinco_invoice, uf_origin)

    with {:ok, {additional_info_list, built_vinco_items}} <-
           ItemBuilder.build_items(
             env,
             location_id,
             organization_id,
             items,
             uf_origin,
             uf_destiny
           ) do
      invoice_with_additional_info = add_additional_info(vinco_invoice, additional_info_list)
      add_items(invoice_with_additional_info, built_vinco_items)
    end
  end

  defp add_additional_info(vinco_invoice, [] = _additional_info_list) do
    vinco_invoice
  end

  defp add_additional_info(vinco_invoice, additional_info_list)
       when is_list(additional_info_list) do
    additional_info =
      additional_info_list
      |> Enum.join(" | ")
      |> String.trim_trailing(" | ")

    current_info = (vinco_invoice[:infAdic] || %{})[:infCpl] || ""

    additional_info =
      if current_info == "",
        do: additional_info,
        else: "#{current_info} | #{additional_info}"

    Map.put(
      vinco_invoice,
      :infAdic,
      Map.put(vinco_invoice[:infAdic] || %{}, :infCpl, additional_info)
    )
  end

  defp add_additional_info(vinco_invoice, _additional_info_list) do
    vinco_invoice
  end

  defp add_items(vinco_invoice, items) when is_list(items) do
    {:ok, Map.put(vinco_invoice, :det, items)}
  end

  defp add_items(_vinco_invoice, {:error, reason}) do
    {:error, reason}
  end

  defp add_items(_vinco_invoice, items) do
    {:error,
     "No valid config found. Invalid items format. Expected a list but got: #{inspect(items)}"}
  end

  defp get_client_uf(invoice, uf_origin) do
    invoice
    |> Map.get(:det, %{})
    |> Map.get(:enderDest, %{})
    |> Map.get(:UF, uf_origin)
  end
end
