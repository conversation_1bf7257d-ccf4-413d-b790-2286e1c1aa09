defmodule Rms.Integrations.Vinco.Prepare.PaymentDistributor do
  @moduledoc """
  Prepare payment distributor for Vinco fiscal invoices.

  We remove delivery items because the organization ecommerce will emit NF for this items.
  """
  alias Rms.Repo

  @preload [fulfillment: [order: [fulfillments: :line_items]]]

  def execute(fiscal_invoice, invoice_payments) do
    fiscal_invoice = Repo.preload(fiscal_invoice, @preload)
    order = fiscal_invoice.fulfillment.order

    delivery_fulfillment =
      Enum.find(
        fiscal_invoice.fulfillment.order.fulfillments,
        &(&1.shipping_method == "delivery")
      )

    total_delivery_line_items = total_price(delivery_fulfillment)
    shipping_value = order.total_delivery_price || Decimal.new(0)
    total_non_nf = Decimal.add(total_delivery_line_items, shipping_value)

    invoice_payments
    |> Enum.map(& &1.payment)
    |> Enum.map(&compute_payment_amount(&1, total_non_nf, order.total_price))
  end

  defp compute_payment_amount(payment, total_delivery_price, order_total_price) do
    proportion = Decimal.div(payment.amount, order_total_price)
    sub = Decimal.mult(total_delivery_price, proportion)
    nf_amount = Decimal.sub(payment.amount, sub)

    {payment, Decimal.round(nf_amount, 2)}
  end

  defp total_price(nil), do: Decimal.new(0)

  defp total_price(fulfillment) do
    fulfillment.line_items
    |> Enum.map(fn li -> li.price end)
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
  end
end
