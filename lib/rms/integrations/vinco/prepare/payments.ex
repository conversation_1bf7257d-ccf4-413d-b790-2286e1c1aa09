defmodule Rms.Integrations.Vinco.Prepare.Payments do
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Integrations.Vinco.Prepare.PaymentDistributor

  def execute(vinco_invoice, fiscal_invoice) do
    prepare_payments(vinco_invoice, fiscal_invoice)
  end

  defp prepare_payments(_vinco_invoice, %_{invoice_payments: []}) do
    {:error, "At least one payment required"}
  end

  defp prepare_payments(
         vinco_invoice,
         %FiscalInvoice{invoice_payments: invoice_payments} = fiscal_invoice
       ) do
    payments_and_amounts = PaymentDistributor.execute(fiscal_invoice, invoice_payments)

    vinco_payments =
      Enum.map(invoice_payments, fn invoice_payment ->
        {_payment, proportional_amount} =
          Enum.find(payments_and_amounts, fn {payment, _amount} ->
            payment.id == invoice_payment.payment_id
          end)

        prepare_payment(invoice_payment.payment, proportional_amount)
      end)

    {:ok, Map.put(vinco_invoice, :pag, %{detPag: vinco_payments})}
  end

  defp prepare_payment(payment, proportional_amount) do
    %{}
    |> get_payment_method(payment)
    |> get_payment_value(proportional_amount)
  end

  defp get_payment_value(invoice_params, proportional_amount) do
    invoice_params
    |> Map.put(:vPag, proportional_amount)
  end

  defp get_payment_method(invoice_params, payment) do
    vinco_payments(invoice_params, payment.method)
  end

  defp vinco_payments(invoice_params, "pix") do
    invoice_params
    |> Map.put(:tPag, "17")
    |> Map.put(:card, %{tpIntegra: 2})
  end

  defp vinco_payments(invoice_params, "credit_card") do
    invoice_params
    |> Map.put(:tPag, "03")
    |> Map.put(:card, %{tpIntegra: 2})
  end

  defp vinco_payments(invoice_params, "debit_card") do
    invoice_params
    |> Map.put(:tPag, "04")
    |> Map.put(:card, %{tpIntegra: 2})
  end

  defp vinco_payments(invoice_params, "cash") do
    invoice_params
    |> Map.put(:tPag, "01")
  end

  defp vinco_payments(invoice_params, method) do
    invoice_params
    |> Map.put(:tPag, "99")
    |> Map.put(:xPag, method)
  end
end
