defmodule Rms.Integrations.Vinco.Prepare.Return.AdditionalInfo do
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Commerce.Orders.Order
  alias Rms.Accounts.Location

  def execute(
        %FiscalInvoice{} = fiscal_invoice,
        %Order{} = original_order,
        %Location{} = location
      ) do
    order = Rms.Repo.preload(original_order, [:staff, :location])

    additional_info =
      "NFe referenciada - #{fiscal_invoice.df_key}"
      |> add_original_order_info(order)
      |> add_return_location_infos(location)

    {:ok, additional_info}
  end

  def execute(%FiscalInvoice{} = fiscal_invoice, %Order{} = original_order, nil) do
    order = Rms.Repo.preload(original_order, [:staff, :location])

    additional_info =
      "NFe referenciada - #{fiscal_invoice.df_key}"
      |> add_original_order_info(order)

    {:ok, additional_info}
  end

  defp add_ecommerce_name(additional_info, %{name: nil} = _order) do
    additional_info
  end

  defp add_ecommerce_name(additional_info, order) do
    with %{organization_id: organization_id} <- order,
         %{value: %{"data" => ecommerce}} <-
           Rms.Settings.get_organization_setting(
             organization_id,
             "organization_order_integration_services"
           ) do
      add_ecommerce_name(additional_info, order, ecommerce)
    end
  end

  defp add_ecommerce_name(additional_info, order, "shopify") do
    "#{additional_info} | Shopify Name: #{order.name}"
  end

  defp add_ecommerce_name(additional_info, _order, _) do
    additional_info
  end

  defp add_iglu_id(additional_info, order) do
    "#{additional_info} iGlu ID: #{order.id}" |> String.trim()
  end

  defp add_original_order_info(additional_info, order) do
    additional_info
    |> add_iglu_id(order)
    |> add_ecommerce_name(order)
    |> add_staff_name(order)
    |> add_original_order(order)
  end

  defp add_return_location_infos(additional_info, location) do
    additional_info
    |> add_return_location(location)
  end

  defp add_staff_name(additional_info, %{staff: staff}) when not is_nil(staff) do
    "#{additional_info} |[Vendedor: #{staff.name}]"
  end

  defp add_staff_name(additional_info, _), do: additional_info

  defp add_original_order(additional_info, order) do
    "#{additional_info} |[Origem: #{order.location.name}]"
  end

  defp add_return_location(additional_info, location) do
    "#{additional_info} |[Destino: #{location.name}] |[Devolucao: #{location.name}]"
  end
end
