defmodule Rms.Integrations.Vinco.Prepare.Return.Customer do
  alias Rms.Fiscal.InvoiceSerie

  def execute(
        %InvoiceSerie{organization: organization, location: location, invoice_env: env},
        parsed_xml
      ) do
    seller_info = seller_info(organization, location, env)

    customer =
      %{
        email: get_email(parsed_xml),
        indIEDest: get_ie_dest(parsed_xml),
        enderDest: get_address(parsed_xml, seller_info),
        xNome: get_name(parsed_xml, env)
      }
      |> add_cpf_cnpj(parsed_xml, seller_info)

    {:ok, customer}
  end

  defp has_nil_values?(map, required_keys) when is_map(map) do
    if Enum.empty?(required_keys) do
      Enum.any?(map, fn {_k, v} -> is_nil(v) || v == "" end)
    else
      Enum.any?(required_keys, fn key ->
        value = Map.get(map, key)
        is_nil(value) || value == ""
      end)
    end
  end

  defp get_address(parsed_xml, seller_info) do
    customer_address =
      parsed_xml.dest |> Map.get(:enderDest)

    required_fields = [
      :xBairro,
      :CEP,
      :cMun,
      :xMun,
      :UF,
      :xLgr,
      :nro
    ]

    if has_nil_values?(customer_address, required_fields) do
      seller_info |> Map.get(:enderEmit)
    else
      %{
        xBairro: customer_address |> Map.get(:xBairro),
        CEP: customer_address |> Map.get(:CEP),
        cMun: customer_address |> Map.get(:cMun),
        xMun: customer_address |> Map.get(:xMun),
        UF: customer_address |> Map.get(:UF),
        xLgr: customer_address |> Map.get(:xLgr),
        nro: customer_address |> Map.get(:nro)
      }
    end
  end

  defp add_cpf_cnpj(customer, parsed_xml, seller_info) do
    case parsed_xml.dest do
      %{CNPJ: cnpj} when is_binary(cnpj) and byte_size(cnpj) > 0 ->
        Map.merge(customer, %{CNPJ: cnpj})

      %{CPF: cpf} when is_binary(cpf) and byte_size(cpf) > 0 ->
        Map.merge(customer, %{CPF: cpf})

      _ ->
        Map.merge(customer, %{
          CNPJ: seller_info |> Map.get(:CNPJ),
          xNome: seller_info |> Map.get(:xNome),
          IE: seller_info |> Map.get(:IE),
          CRT: seller_info |> Map.get(:CRT)
        })
    end
  end

  defp get_email(parsed_xml) do
    parsed_xml.dest |> Map.get(:email)
  end

  defp get_name(parsed_xml, env) do
    case env do
      "dev" ->
        "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"

      _ ->
        get_in(parsed_xml, [:dest, :xNome]) |> validate_name()
    end
  end

  defp validate_name("") do
    "Consumidor Final"
  end

  defp validate_name(nil) do
    "Consumidor Final"
  end

  defp validate_name(name) do
    name
  end

  defp get_ie_dest(%{dest: dest}) do
    case dest do
      %{CNPJ: cnpj} when is_binary(cnpj) and byte_size(cnpj) > 0 -> "1"
      %{CPF: cpf} when is_binary(cpf) and byte_size(cpf) > 0 -> "9"
      _ -> "1"
    end
  end

  defp seller_info(organization, location, env) do
    location_tax = Rms.Fiscal.get_tax_config!(organization.id, location.id)

    %{
      CNPJ: location.cnpj,
      xNome:
        if(env == "dev",
          do: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",
          else: location_tax.name
        ),
      IE: location_tax.ie,
      CRT: location_tax.crt,
      enderEmit: %{
        UF: location.address.state,
        cMun: location.address.city_code,
        xMun: location.address.city_name,
        CEP: location.address.zip,
        xBairro: location.address.neighborhood,
        xLgr: location.address.street,
        nro: location.address.number
      }
    }
  end
end
