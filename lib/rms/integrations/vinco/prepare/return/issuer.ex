defmodule Rms.Integrations.Vinco.Prepare.Return.Issuer do
  alias Rms.Fiscal.InvoiceSerie

  def execute(%InvoiceSerie{
        location: location,
        organization: organization
      }) do
    issuer_params = prepare_issuer(organization, location)
    {:ok, issuer_params}
  end

  defp prepare_issuer(organization, location) do
    location_tax = Rms.Fiscal.get_tax_config!(organization.id, location.id)

    %{
      CNPJ: location.cnpj,
      xNome: location_tax.name,
      IE: location_tax.ie,
      CRT: location_tax.crt,
      enderEmit: %{
        UF: location.address.state,
        cMun: location.address.city_code,
        xMun: location.address.city_name,
        CEP: location.address.zip,
        xBairro: location.address.neighborhood,
        xLgr: location.address.street,
        nro: location.address.number
      }
    }
  end
end
