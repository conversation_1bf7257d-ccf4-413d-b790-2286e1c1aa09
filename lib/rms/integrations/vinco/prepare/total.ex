defmodule Rms.Integrations.Vinco.Prepare.Total do
  alias Rms.Fiscal.InvoiceSerie

  def execute(vinco_invoice, %InvoiceSerie{}) do
    with {:ok, total} <- prepare_total(vinco_invoice) do
      add_total_to_invoice(total, vinco_invoice)
    end
  end

  defp add_total_to_invoice(total, vinco_invoice) do
    vinco_invoice_with_total = Map.put(vinco_invoice, :total, %{ICMStot: total})
    {:ok, vinco_invoice_with_total}
  end

  def prepare_total(vinco_invoice) do
    vinco_items = vinco_invoice.det
    uf = get_in(vinco_invoice.emit, [:enderEmit, :UF])

    total_items =
      Enum.reduce(vinco_items, start_acc(), fn vinco_item, acc ->
        item_vnf = Decimal.sub(vinco_item.prod.vProd, vinco_item.prod.vDesc)

        acc
        |> Map.update!(:vPIS, fn _ -> Decimal.add(acc.vPIS, vinco_item.imposto[:PIS].vPIS) end)
        |> Map.update!(:vCOFINS, fn _ ->
          Decimal.add(acc.vCOFINS, vinco_item.imposto[:COFINS].vCOFINS)
        end)
        |> Map.update!(:vBC, fn _ -> get_vbc(vinco_item, uf, acc) end)
        |> Map.update!(:vProd, fn _ -> Decimal.add(acc.vProd, vinco_item.prod.vProd) end)
        |> Map.update!(:vICMSUFDest, fn _ ->
          Decimal.add(
            acc.vICMSUFDest,
            get_in(vinco_item.imposto, [:ICMSUFDest, :vICMSUFDest]) || "0"
          )
        end)
        |> Map.update!(:vICMS, fn _ -> Decimal.add(acc.vICMS, vinco_item.imposto[:ICMS].vICMS) end)
        |> Map.update!(:vFCP, fn _ -> Decimal.add(acc.vFCP, vinco_item.imposto[:ICMS].vFCP) end)
        |> Map.update!(:vNF, fn _ -> Decimal.add(acc.vNF, item_vnf) end)
        |> Map.update!(:vDesc, fn _ -> Decimal.add(acc.vDesc, vinco_item.prod.vDesc) end)
        |> Map.update!(:vOutro, fn _ ->
          Decimal.add(
            acc.vOutro,
            Decimal.add(
              vinco_item.prod |> Map.get(:vFrete, "0"),
              vinco_item.prod |> Map.get(:vOutro, "0")
            )
          )
        end)
        |> Map.update!(:vDesc, fn _ -> Decimal.add(acc.vDesc, vinco_item.prod.vDesc) end)
        |> get_reduction_rules(vinco_item.imposto[:ICMS])
      end)

    {:ok, total_items}
  end

  defp get_vbc(vinco_item, uf, acc) do
    if get_in(vinco_item.imposto, [:ICMS, :CSTCSOSN]) == "60" and uf == "CE" do
      Decimal.add(acc.vBC, Decimal.new(0))
    else
      Decimal.add(acc.vBC, vinco_item.imposto[:ICMS].vBC)
    end
  end

  defp get_reduction_rules(acc, %{vICMSDeson: vICMSDeson}) do
    acc
    |> Map.update!(:vICMSDeson, fn _ -> Decimal.add(acc.vICMSDeson, vICMSDeson) end)
  end

  defp get_reduction_rules(acc, _) do
    acc
  end

  defp start_acc() do
    %{
      vICMSUFDest: Decimal.new("0"),
      vPIS: Decimal.new("0"),
      vCOFINS: Decimal.new("0"),
      vBC: Decimal.new("0"),
      vProd: Decimal.new("0"),
      vNF: Decimal.new("0"),
      vICMS: Decimal.new("0"),
      vDesc: Decimal.new("0"),
      vFCP: Decimal.new("0"),
      vICMSDeson: Decimal.new("0"),
      vBCST: Decimal.new("0"),
      vST: Decimal.new("0"),
      vFCPST: Decimal.new("0"),
      vFCPSTRet: Decimal.new("0"),
      vFrete: Decimal.new("0"),
      vSeg: Decimal.new("0"),
      vII: Decimal.new("0"),
      vIPI: Decimal.new("0"),
      vIPIDevol: Decimal.new("0"),
      vOutro: Decimal.new("0")
    }
  end
end
