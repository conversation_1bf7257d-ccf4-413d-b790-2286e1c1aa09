defmodule Rms.Integrations.Vinco.Prepare.Transfer.Items do
  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Fiscal.FiscalSettings
  alias Rms.Integrations.Vinco.Prepare.ItemBuilder
  alias Rms.Repo

  import Ecto.Query

  def execute(
        organization_id,
        location_id,
        %InvoiceSerie{} = invoice_serie,
        items,
        uf_origin,
        uf_destiny
      ) do
    interstate? = uf_origin != uf_destiny

    items_for_builder = Enum.map(items, &set_value_for_transfer/1)

    with {:ok,
          %FiscalSettings{transfer_without_taxes: transfer_without_taxes?} = _fiscal_settings} <-
           get_fiscal_settings(organization_id, location_id),
         {:ok, {additional_info, built_items_from_builder}} <-
           ItemBuilder.build_items(
             invoice_serie.invoice_env,
             location_id,
             organization_id,
             items_for_builder,
             uf_origin,
             uf_destiny
           ) do
      new_items_with_taxes =
        Enum.map(built_items_from_builder, fn item ->
          item
          |> add_taxes(transfer_without_taxes?)
          |> build_item_transfer_specifics(interstate?)
        end)

      {:ok, new_items_with_taxes, additional_info}
    end
  end

  defp add_taxes(item, true) do
    Map.put(item, :imposto, %{
      ICMS: %{
        orig: "0",
        CSTCSOSN: "41",
        modBC: "3",
        vBC: "0",
        pICMS: "0",
        vICMS: "0",
        pFCP: "0",
        vFCP: "0"
      },
      PIS: %{CST: "08", vBC: "0", vPIS: "0", pPIS: "0"},
      COFINS: %{CST: "08", vBC: "0", vCOFINS: "0", pCOFINS: "0"}
    })
  end

  defp add_taxes(item, false) do
    current_imposto = item.imposto || %{}

    updated_imposto =
      current_imposto
      |> Map.put(:PIS, %{CST: "08", vBC: "0", vPIS: "0", pPIS: "0"})
      |> Map.put(:COFINS, %{CST: "08", vBC: "0", vCOFINS: "0", pCOFINS: "0"})

    %{item | imposto: updated_imposto}
  end

  defp get_fiscal_settings(organization_id, location_id) do
    fiscal_setting =
      FiscalSettings
      |> where([fs], fs.organization_id == ^organization_id and fs.location_id == ^location_id)
      |> Repo.one()

    case fiscal_setting do
      nil -> {:error, :fiscal_settings_not_found}
      settings -> {:ok, settings}
    end
  end

  defp update_cst_for_transfer(item, interstate?) do
    current_icms = item.imposto[:ICMS]
    cstcsosn_value = Map.get(current_icms, :CSTCSOSN, "")

    new_cst_value = if interstate?, do: "00", else: cstcsosn_value
    updated_icms = Map.put(current_icms || %{}, :CST, new_cst_value)
    %{item | imposto: Map.put(item.imposto || %{}, :ICMS, updated_icms)}
  end

  defp update_cfop_for_transfer(item, interstate?) do
    new_prod = Map.put(item.prod, :CFOP, if(interstate?, do: "6152", else: "5152"))
    %{item | prod: new_prod}
  end

  defp set_value_for_transfer(%{line_item: line_item} = _prod) do
    %{
      line_item: %{
        line_item
        | price: Decimal.div(line_item.price, 5),
          list_price: Decimal.div(line_item.list_price, 5)
      }
    }
  end

  defp build_item_transfer_specifics(item_from_add_taxes, interstate?) do
    item_from_add_taxes
    |> update_cfop_for_transfer(interstate?)
    |> update_cst_for_transfer(interstate?)
  end
end
