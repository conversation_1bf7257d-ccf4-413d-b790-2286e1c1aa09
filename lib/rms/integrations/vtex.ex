defmodule Rms.Integrations.VTEX do
  alias Rms.Integrations.VTEX.Client, as: VTEXClient
  @vtex_client Application.compile_env(:rms, :vtex_client, VTEXClient)

  def client(vtex_credential, env \\ "vtexcommercestable") do
    @vtex_client.client(vtex_credential, env)
  end

  def create_ecommerce_order(client, account_name, affiliate_id, sales_channel_id, order_params) do
    @vtex_client.create_ecommerce_order(
      client,
      account_name,
      affiliate_id,
      sales_channel_id,
      order_params
    )
  end

  def create_external_ecommerce_order(client, account_name, affiliate_id, order_params) do
    @vtex_client.create_external_ecommerce_order(client, account_name, affiliate_id, order_params)
  end

  def update_ecommerce_order(client, account_name, order_params) do
    @vtex_client.update_ecommerce_order(client, account_name, order_params)
  end

  def get_ecommerce_order(client, order_id) do
    @vtex_client.get_ecommerce_order(client, order_id)
  end

  def send_payment_information(client, transaction_id, order_id, params) do
    @vtex_client.send_payment_information(client, transaction_id, order_id, params)
  end

  def authorize_new_transaction(client, transaction_id, params) do
    @vtex_client.authorize_new_transaction(client, transaction_id, params)
  end

  def create_order_hook_configuration(client, params) do
    @vtex_client.create_order_hook_configuration(client, params)
  end

  def scroll_masterdata(client, entity, params) do
    @vtex_client.scroll_masterdata(client, entity, params)
  end

  def cancel_ecommerce_order(client, order_id, params) do
    @vtex_client.cancel_ecommerce_order(client, order_id, params)
  end

  def create_cart(client) do
    @vtex_client.create_cart(client)
  end

  def create_document(client, document, params) do
    @vtex_client.create_document(client, document, params)
  end

  def add_cart_items(client, cart_id, items) do
    @vtex_client.add_cart_items(client, cart_id, items)
  end

  def add_cart_subscriptions(client, cart_id, subscription) do
    @vtex_client.add_cart_subscriptions(client, cart_id, subscription)
  end

  def add_customer_to_cart(client, cart_id, customer_info) do
    @vtex_client.add_customer_to_cart(client, cart_id, customer_info)
  end

  def add_delivery_options_to_cart(client, cart_id, delivery_options) do
    @vtex_client.add_delivery_options_to_cart(client, cart_id, delivery_options)
  end

  def add_marketing_data_to_cart(client, cart_id, marketing_data) do
    @vtex_client.add_marketing_data_to_cart(client, cart_id, marketing_data)
  end

  def add_payment_to_cart(client, cart_id, payments) do
    @vtex_client.add_payment_to_cart(client, cart_id, payments)
  end

  def get_customer(client, email) do
    @vtex_client.get_customer(client, email)
  end

  @default_address_fields ~w(city userId street state complement neighborhood number country receiverName postalCode)
  def search_customer_address(client, userId, fields \\ @default_address_fields) do
    @vtex_client.search_customer_address(client, userId, fields)
  end

  def search_customer(client, email) do
    @vtex_client.search_customer(client, email)
  end

  def create_order(client, order_info) do
    @vtex_client.create_order(client, order_info)
  end

  def update_document(client, document, id, params) do
    @vtex_client.update_document(client, document, id, params)
  end

  def notify_payment(client, transaction_id, order_id, payment_info) do
    @vtex_client.notify_payment(client, transaction_id, order_id, payment_info)
  end

  def process_order(client, order_group) do
    @vtex_client.process_order(client, order_group)
  end

  def get_sku_by_id(client, sku_id) do
    @vtex_client.get_sku_by_id(client, sku_id)
  end

  def list_product_and_sku(client, from \\ 1, to \\ 20) do
    @vtex_client.list_product_and_sku(client, from, to)
  end

  def simulate_fulfillment(client, affiliate_id, sales_chanel_id, payload) do
    @vtex_client.simulate_fulfillment(client, affiliate_id, sales_chanel_id, payload)
  end

  def private_simulate_fulfillment(client, affiliate_id, payload) do
    @vtex_client.private_simulate_fulfillment(client, affiliate_id, payload)
  end

  def place_order(client, params) do
    @vtex_client.place_order(client, params)
  end

  def send_payment_notification(client, order_id, payment_id) do
    @vtex_client.send_payment_notification(client, order_id, payment_id)
  end
end
