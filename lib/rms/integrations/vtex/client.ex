defmodule Rms.Integrations.VTEX.Client do
  @behaviour Rms.Integrations.VTEX.ClientBehaviour

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def client(%Rms.Integrations.VTEXCredential{} = vtex_credential) do
    env = "vtexcommercestable"

    middleware = [
      {Tesla.Middleware.BaseUrl, "https://#{vtex_credential.account_name}.#{env}.com.br"},
      {Tesla.Middleware.Query, [sc: vtex_credential.sales_channel_id || 1]},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers,
       [
         {"X-VTEX-API-AppKey", vtex_credential.app_key},
         {"X-VTEX-API-AppToken", vtex_credential.app_token}
       ]},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.LogError,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def client(%Rms.Integrations.VTEXCredential{} = vtex_credential, env) when is_binary(env) do
    middleware = [
      {Tesla.Middleware.BaseUrl, "https://#{vtex_credential.account_name}.#{env}.com.br"},
      {Tesla.Middleware.Query, [sc: vtex_credential.sales_channel_id || 1]},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Headers,
       [
         {"X-VTEX-API-AppKey", vtex_credential.app_key},
         {"X-VTEX-API-AppToken", vtex_credential.app_token}
       ]},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.LogError,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def client(%Rms.Integrations.VTEXCredential{} = vtex_credential, opts) when is_list(opts) do
    default_opts = [env: "vtexcommercestable", use_sale_channel: true] |> Keyword.merge(opts)

    with [env: env, use_sale_channel: use_sale_channel] <- default_opts do
      case use_sale_channel do
        true ->
          middleware = [
            {Tesla.Middleware.BaseUrl, "https://#{vtex_credential.account_name}.#{env}.com.br"},
            {Tesla.Middleware.Query, [sc: vtex_credential.sales_channel_id || 1]},
            Tesla.Middleware.JSON,
            {Tesla.Middleware.Headers,
             [
               {"X-VTEX-API-AppKey", vtex_credential.app_key},
               {"X-VTEX-API-AppToken", vtex_credential.app_token}
             ]},
            Tesla.Middleware.OpenTelemetry,
            Tesla.Middleware.LogError,
            Tesla.Middleware.PathParams
          ]

          Tesla.client(middleware)

        false ->
          middleware = [
            {Tesla.Middleware.BaseUrl, "https://#{vtex_credential.account_name}.#{env}.com.br"},
            {Tesla.Middleware.Query, []},
            Tesla.Middleware.JSON,
            {Tesla.Middleware.Headers,
             [
               {"X-VTEX-API-AppKey", vtex_credential.app_key},
               {"X-VTEX-API-AppToken", vtex_credential.app_token}
             ]},
            Tesla.Middleware.OpenTelemetry,
            Tesla.Middleware.LogError,
            Tesla.Middleware.PathParams
          ]

          Tesla.client(middleware)
      end
    end
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_ecommerce_order(client, account_name, affiliate_id, sales_channel_id, order_params) do
    endpoint =
      "/api/checkout/pub/orders?an=#{account_name}&affiliateId=#{affiliate_id}&sc=#{sales_channel_id}"

    client
    |> Tesla.put!(endpoint, order_params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_external_ecommerce_order(client, account_name, affiliate_id, order_params) do
    ld_context_map = :ldclient_context.new(to_string(account_name), "vtex_account_name")
    ld_context = :ldclient_context.new_from_map(ld_context_map)

    if :ldclient.variation("vtex-order-creation", ld_context, true) do
      endpoint = "/api/order-integration/orders?an=#{account_name}&affiliateId=#{affiliate_id}"

      client
      |> Tesla.post!(endpoint, order_params)
      |> normalize_response()
    else
      {:error, {:kill_switch, :vtex_create_order}}
    end
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def authorize_new_transaction(client, transaction_id, params) do
    endpoint = "api/pvt/transactions/:transaction_id/authorization-request"

    client
    |> Tesla.post!(endpoint, params, opts: [path_params: [transaction_id: transaction_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def send_payment_information(client, transaction_id, order_id, params) do
    endpoint = "api/pub/transactions/:transaction_id/payments"

    client
    |> Tesla.post!(endpoint, params,
      query: [orderId: order_id],
      opts: [path_params: [transaction_id: transaction_id]]
    )
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_ecommerce_order(client, order_id) do
    endpoint = "/api/oms/pvt/orders/:order_id"

    client
    |> Tesla.get!(endpoint, opts: [path_params: [order_id: order_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def update_ecommerce_order(client, account_name, order_params) do
    endpoint =
      "/api/order-integration/orders/status?an=#{account_name}"

    client
    |> Tesla.put!(endpoint, order_params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def cancel_ecommerce_order(client, order_id, params) do
    endpoint =
      "/api/oms/pvt/orders/:order_id/cancel"

    client
    |> Tesla.post!(endpoint, params, opts: [path_params: [order_id: order_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_order_hook_configuration(client, params) do
    endpoint = "/api/orders/hook/config"

    client
    |> Tesla.post!(endpoint, params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def scroll_masterdata(client, entity, params) do
    endpoint = "/api/dataentities/#{entity}/scroll"

    Tesla.get(client, endpoint, query: params)
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_cart(client) do
    endpoint = "/api/checkout/pub/orderForm"

    client
    |> Tesla.get!(endpoint, query: [forceNewCart: true])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_cart_items(client, cart_id, items) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/items"

    client
    |> Tesla.patch!(endpoint, %{"orderItems" => items}, opts: [path_params: [cart_id: cart_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_cart_subscriptions(client, cart_id, subscription) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/attachments/subscriptionData"

    client
    |> Tesla.post!(endpoint, %{"subscriptions" => subscription},
      opts: [path_params: [cart_id: cart_id]]
    )
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_customer_to_cart(client, cart_id, customer_info) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/attachments/clientProfileData"

    client
    |> Tesla.post!(endpoint, customer_info, opts: [path_params: [cart_id: cart_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_delivery_options_to_cart(client, cart_id, delivery_options) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/attachments/shippingData"

    client
    |> Tesla.post!(endpoint, delivery_options, opts: [path_params: [cart_id: cart_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_payment_to_cart(client, cart_id, payments) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/attachments/paymentData"

    client
    |> Tesla.post!(endpoint, %{"payments" => payments}, opts: [path_params: [cart_id: cart_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_marketing_data_to_cart(client, cart_id, marketing_data) do
    endpoint = "/api/checkout/pub/orderForm/:cart_id/attachments/marketingData"

    client
    |> Tesla.post!(endpoint, marketing_data, opts: [path_params: [cart_id: cart_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_customer(client, email) do
    endpoint = "/api/checkout/pub/profiles"

    client
    |> Tesla.get!(endpoint, query: [email: email])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def search_customer_address(
        client,
        userId,
        fields \\ ~w(city userId street state complement neighborhood number country receiverName postalCode)
      ) do
    endpoint = "/api/dataentities/AD/search"

    query_params = [_fields: Enum.join(fields, ","), _where: "userId=#{userId}"]

    client
    |> Tesla.get!(endpoint, query: query_params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def search_customer(client, email, fields \\ ~w(priceTables email)) do
    endpoint = "/api/dataentities/CL/search"

    query_params = [email: email, _fields: Enum.join(fields, ",")]

    client
    |> Tesla.get!(endpoint, query: query_params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_document(client, document, params) do
    endpoint = "/api/dataentities/#{document}/documents"

    client
    |> Tesla.post!(endpoint, params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def update_document(client, document, id, params) do
    endpoint = "/api/dataentities/#{document}/documents/#{id}"

    client
    |> Tesla.patch!(endpoint, params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_order(client, order_info) do
    endpoint = "/api/checkout/pub/orderForm/:reference_id/transaction"

    payload = %{
      "referenceId" => order_info["referenceId"],
      "value" => order_info["value"],
      "referenceValue" => order_info["referenceValue"],
      "interestValue" => order_info["interestValue"],
      "savePersonalData" => order_info["savePersonalData"],
      "optinNewsLetter" => order_info["optinNewsLetter"]
    }

    client
    |> Tesla.post!(
      endpoint,
      payload,
      opts: [path_params: [reference_id: order_info["referenceId"]]]
    )
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def process_order(client, transaction_id) do
    endpoint = "/api/checkout/pub/gatewayCallback/:transaction_id"

    client
    |> Tesla.post!(endpoint, %{}, opts: [path_params: [transaction_id: transaction_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def notify_payment(client, transaction_id, order_id, payment_info) do
    endpoint =
      "/api/pub/transactions/:transaction_id/payments"

    payload = [
      %{
        "paymentSystem" => payment_info["paymentSystem"],
        "installments" => payment_info["installments"],
        "currencyCode" => payment_info["currencyCode"],
        "value" => payment_info["value"],
        "installmentsInterestRate" => payment_info["installmentsInterestRate"],
        "installmentsValue" => payment_info["installmentsValue"],
        "referenceValue" => payment_info["referenceValue"],
        "transaction" => %{
          "id" => transaction_id,
          "merchantName" => payment_info["transaction"]["merchantName"]
        }
      }
    ]

    client
    |> Tesla.post!(endpoint, payload,
      query: [orderId: order_id],
      opts: [path_params: [transaction_id: transaction_id]]
    )
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_sku_by_id(client, sku_id) do
    endpoint = "/api/catalog_system/pvt/sku/stockkeepingunitbyid/:sku_id"

    client
    |> Tesla.get!(endpoint, opts: [path_params: [sku_id: sku_id]])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def list_product_and_sku(client, from \\ 1, to \\ 20) do
    endpoint = "/api/catalog_system/pvt/products/GetProductAndSkuIds?_from=#{from}&_to=#{to}"

    client
    |> Tesla.get!(endpoint)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def catalog_search(client, from, to) do
    endpoint = "/api/catalog_system/pub/products/search"

    client
    |> Tesla.get!(endpoint, query: [_from: from, _to: to])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def simulate_fulfillment(client, affiliate_id, sales_channel_id, payload) do
    endpoint = "api/checkout/pub/orderForms/simulation"

    client
    |> Tesla.post!(endpoint, payload, query: [affiliateId: affiliate_id, sc: sales_channel_id])
    |> normalize_response
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def private_simulate_fulfillment(client, affiliate_id, payload) do
    endpoint = "api/checkout/pvt/orderForms/simulation"

    client
    |> Tesla.post!(endpoint, payload, query: [affiliateId: affiliate_id])
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def place_order(client, params) do
    endpoint = "/api/checkout/pub/orders"

    client
    |> Tesla.put!(endpoint, params)
    |> normalize_response()
  end

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def send_payment_notification(client, order_id, payment_id) do
    endpoint = "/api/oms/pvt/orders/:order_id/payments/:payment_id/payment-notification"

    client
    |> Tesla.post!(endpoint, %{},
      opts: [path_params: [order_id: order_id, payment_id: payment_id]]
    )
    |> normalize_response()
  end

  defp normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: body}) do
    {:error, {:bad_request, body}}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
