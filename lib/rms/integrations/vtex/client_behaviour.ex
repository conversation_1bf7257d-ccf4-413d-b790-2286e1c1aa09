defmodule Rms.Integrations.VTEX.ClientBehaviour do
  @opaque client() :: Tesla.Client.t()

  @type errors() ::
          :bad_request
          | :forbidden
          | :not_found
          | :internal_server_error
          | {:validation_error, map()}
          | :unknown_error
  @type result() :: {:ok, map()} | {:ok, list()} | {:error, errors()}

  @callback create_cart(client()) :: result()
  @callback add_cart_items(client(), binary(), map()) :: result()
  @callback add_cart_subscriptions(client(), binary(), map()) :: result()
  @callback add_customer_to_cart(client(), binary(), map()) :: result()
  @callback add_delivery_options_to_cart(client(), binary(), map()) :: result()
  @callback add_payment_to_cart(client(), binary(), map()) :: result()
  @callback add_marketing_data_to_cart(client(), binary(), map()) :: result()

  @callback get_customer(client(), binary()) :: result()
  @callback search_customer(client(), binary()) :: result()
  @callback search_customer_address(client(), binary(), list()) :: result()
  @callback update_document(client(), binary(), binary(), map()) :: result()

  @callback send_payment_information(client(), binary(), binary(), map()) :: result()
  @callback authorize_new_transaction(client(), binary(), map()) :: result()

  @callback create_order(client(), map()) :: result()
  @callback create_ecommerce_order(client(), binary(), binary(), binary(), map()) :: result()
  @callback create_external_ecommerce_order(client(), binary(), binary(), map()) :: result()
  @callback update_ecommerce_order(client(), binary(), map()) :: result()
  @callback cancel_ecommerce_order(client(), binary(), map()) :: result()
  @callback create_order_hook_configuration(client(), map()) :: result()
  @callback scroll_masterdata(client(), any(), any()) :: Tesla.Env.result()
  @callback get_ecommerce_order(client(), binary()) :: result()
  @callback process_order(client(), binary()) :: result()
  @callback create_document(client(), binary(), map()) :: result()

  @callback notify_payment(client(), binary(), binary(), map()) :: result()

  @callback get_sku_by_id(client(), binary()) :: result()
  @callback list_product_and_sku(client(), integer(), integer()) :: result()
  @callback catalog_search(client(), integer(), integer()) :: result()

  @callback simulate_fulfillment(client(), binary(), binary(), map()) :: result()
  @callback private_simulate_fulfillment(client(), affiliate_id :: binary(), payload :: map()) ::
              result()

  @callback place_order(client(), map()) :: result()
  @callback send_payment_notification(client(), order_id :: binary(), payment_id :: binary()) ::
              result()

  @callback client(map()) :: client()
  @callback client(map(), binary()) :: client()
  @callback client(map(), list()) :: client()
end
