defmodule Rms.Integrations.VTEX.Customers.CreateAddressWorker do
  use Oban.Pro.Worker, recorded: true, queue: :vtex
  alias Oban.Pro.Workflow

  @impl true
  def process(%Job{args: %{"customer" => customer, "organization_id" => organization_id}} = job) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)
    client = Rms.Integrations.VTEX.client(vtex_credential)

    case fetch_lazy(job) do
      {:ok, %{"DocumentId" => user_id}} ->
        results =
          Enum.map(customer["addresses"], fn address ->
            Rms.Integrations.VTEX.create_document(client, "AD", build_params(address, user_id))
          end)

        {:ok, results}

      _ ->
        {:error, "An unexpected error occurred"}
    end
  end

  defp build_params(address, user_id) do
    %{
      userId: user_id,
      addressName: "IGLU-#{address["id"]}",
      receiverName: address["receiver_name"],
      city: address["city_name"],
      state: address["state"],
      country: address["country_name"],
      neighborhood: address["neighborhood"],
      street: address["street"],
      street_type: address["street_type"],
      number: address["number"],
      postalCode: address["zip"],
      complement: address["complement"]
    }
  end

  defp fetch_lazy(%{args: %{"result" => result}}), do: {:ok, result}

  defp fetch_lazy(job) do
    job
    |> Workflow.all_jobs(names: ["create_customer"])
    |> List.first()
    |> fetch_recorded()
  end
end
