defmodule Rms.Integrations.VTEX.Customers.CreateCustomerWorker do
  use Oban.Pro.Workers.Workflow, recorded: true, queue: :vtex

  @impl true
  def process(%Job{args: %{"customer" => customer, "organization_id" => organization_id}}) do
    with {:ok, params} <- build_params(customer) do
      vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)
      client = Rms.Integrations.VTEX.client(vtex_credential)

      client
      |> Rms.Integrations.VTEX.create_document("CL", params)
      |> handle_response()
    end
  end

  defp build_params(%{"email" => email, "birthdate" => birthdate} = customer)
       when not is_nil(email) and is_nil(birthdate) do
    {first_name, last_name} = split_name(customer["name"])

    {:ok,
     %{
       firstName: first_name,
       lastName: last_name,
       documentType: customer["document_type"],
       document: customer["document"],
       email: email,
       phone: customer["primary_phone_number"],
       homePhone: customer["primary_phone_number"]
     }}
  end

  defp build_params(%{"email" => email, "birthdate" => birthdate} = customer)
       when not is_nil(email) do
    {first_name, last_name} = split_name(customer["name"])

    with {:ok, birth_date} <- Date.from_iso8601(birthdate) do
      {:ok,
       %{
         firstName: first_name,
         lastName: last_name,
         documentType: customer["document_type"],
         document: customer["document"],
         email: email,
         phone: customer["primary_phone_number"],
         homePhone: customer["primary_phone_number"],
         birthDateMonth: birth_date.month,
         birthDate: Date.to_string(birth_date)
       }}
    end
  end

  defp build_params(_customer), do: {:discard, "customer missing email"}

  defp split_name(name) do
    [first_name | last_name_parts] = String.split(name, " ")
    last_name = Enum.join(last_name_parts, " ")
    {first_name, last_name}
  end

  defp handle_response({:error, {:bad_request, %{"Message" => "duplicated entry"}}}) do
    {:discard, :duplicate_customer}
  end

  defp handle_response(response) do
    response
  end
end
