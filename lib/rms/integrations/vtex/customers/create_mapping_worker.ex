defmodule Rms.Integrations.VTEX.Customers.CreateMappingWorker do
  use Oban.Pro.Worker, recorded: true, queue: :vtex
  alias Oban.Pro.Workflow

  alias Rms.Integrations

  @impl true
  def process(%Job{args: %{"customer" => customer, "organization_id" => organization_id}} = job) do
    with {:ok, %{"DocumentId" => vtex_id}} <- fetch_lazy(job) do
      Integrations.create_customer_sync_mapping(organization_id, %{
        customer_id: customer["id"],
        external_id: to_string(vtex_id),
        source: "vtex"
      })
    end
  end

  defp fetch_lazy(%{args: %{"result" => result}}), do: {:ok, result}

  defp fetch_lazy(job) do
    job
    |> Workflow.all_jobs(names: ["create_customer"])
    |> List.first()
    |> fetch_recorded()
  end
end
