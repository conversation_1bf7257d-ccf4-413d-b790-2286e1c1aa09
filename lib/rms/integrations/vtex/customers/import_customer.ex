defmodule Rms.Integrations.VTEX.Customers.ImportCustomer do
  alias Rms.Repo
  alias Rms.Customers
  alias Rms.Integrations

  require Logger

  def import(organization_id, customer_attrs) do
    Repo.transaction(fn ->
      vtex_id = customer_attrs[:vtex_id]
      email = customer_attrs[:email]

      case search_customer(organization_id, vtex_id, email) do
        {:error, :not_found} ->
          create_customer(organization_id, customer_attrs)

        {:ok, existing_customer} ->
          existing_customer = Rms.Repo.preload(existing_customer, [:addresses])
          update_customer(existing_customer, customer_attrs)
      end
    end)
  end

  defp search_customer(organization_id, vtex_id, email) do
    with {:error, :not_found} <- search_by_id(organization_id, vtex_id) do
      search_by_email(organization_id, email)
    end
  end

  defp search_by_id(organization_id, vtex_id) when is_binary(vtex_id) do
    case Integrations.get_customer_sync_mapping(organization_id, "vtex", vtex_id) do
      nil -> {:error, :not_found}
      customer_sync_mapping -> {:ok, customer_sync_mapping.customer}
    end
  end

  defp search_by_id(_, _), do: {:error, :not_found}

  defp search_by_email(organization_id, email) when is_binary(email) do
    case Customers.list_customers(organization_id, email: email) do
      [customer] -> {:ok, customer}
      _ -> {:error, :not_found}
    end
  end

  defp search_by_email(_, _), do: {:error, :not_found}

  defp create_customer(organization_id, customer_attrs) do
    with {:ok, customer} <- Customers.create_customer(organization_id, customer_attrs),
         {:ok, _} <- create_sync_mapping(organization_id, customer, customer_attrs[:vtex_id]) do
      customer
    else
      {:error, changeset} ->
        handle_error(changeset)
    end
  end

  defp update_customer(existing_customer, customer_params) do
    {addresses, customer_attrs} = Map.pop(customer_params, :addresses, [])
    organization_id = existing_customer.organization_id

    with {:ok, customer} <- Customers.update_customer(existing_customer, customer_attrs),
         {:ok, customer} <- update_addresses(customer, addresses),
         {:ok, _} <- create_sync_mapping(organization_id, customer, customer_attrs[:vtex_id]) do
      customer
    else
      {:error, changeset} ->
        handle_error(changeset)
    end
  end

  defp update_addresses(customer, addresses) do
    current_addresses = Enum.map(customer.addresses, &Map.from_struct/1)
    new_addresses = Enum.map(addresses, &prepare_address(customer, &1))

    updated_addresses =
      Enum.reduce(new_addresses, current_addresses, fn new_address, acc ->
        case Enum.find_index(acc, &address_match?(&1, new_address)) do
          nil ->
            [new_address | acc]

          index ->
            List.update_at(acc, index, &Map.merge(&1, new_address))
        end
      end)

    Customers.update_customer_address!(customer, updated_addresses)
  end

  defp prepare_address(customer, address) do
    address
    |> Map.put(:organization_id, customer.organization_id)
    |> Map.put(:customer_id, customer.id)
    |> then(&Rms.Addresses.Address.changeset(%Rms.Addresses.Address{}, &1))
    |> Map.get(:changes)
  end

  defp address_match?(address1, address2) do
    address1.street == address2.street &&
      address1.number == address2.number &&
      address1.city_name == address2.city_name &&
      address1.state == address2.state &&
      address1.zip == address2.zip
  end

  defp create_sync_mapping(organization_id, customer, vtex_id) do
    Integrations.create_customer_sync_mapping(organization_id, %{
      customer_id: customer.id,
      external_id: to_string(vtex_id),
      source: "vtex"
    })
  end

  defp handle_error(changeset) do
    error = %{
      metadata: %{params: changeset.params, errors: extract_changeset_errors(changeset)},
      code: "changeset_error",
      detail: "invalid changeset"
    }

    Logger.error("failed to import customer #{inspect(error)}")

    Repo.rollback(changeset)
  end

  defp extract_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, &replace_error_placeholder/2)
    end)
  end

  defp replace_error_placeholder({key, value}, acc) do
    String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
  end
end
