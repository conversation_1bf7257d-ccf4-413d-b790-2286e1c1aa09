defmodule Rms.Integrations.VTEX.Customers.ScrollWorker do
  use Oban.Pro.Worker, queue: :vtex, max_attempts: 3

  alias Rms.Integrations.VTEX
  alias Rms.Integrations.VTEX.Customers.ImportCustomer

  require Logger

  @impl Oban.Pro.Worker
  def process(%{args: %{"organization_id" => organization_id} = args}) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    vtex_client =
      vtex_credential
      |> VTEX.client()
      |> Tesla.Client.middleware()
      |> Enum.reject(fn
        {Tesla.Middleware.Query, [sc: _]} -> true
        _ -> false
      end)
      |> Tesla.client()

    case fetch_customers(vtex_client, args["token"]) do
      {:ok, [], _} ->
        :ok

      {:ok, customers, token} ->
        customers
        |> Enum.map(&transform_customer/1)
        |> Enum.each(&import_customer(vtex_client, organization_id, &1))

        %{"token" => token, "organization_id" => organization_id}
        |> new()
        |> Oban.insert!()

        :ok

      {:error, reason} ->
        error_msg = "Failed start VTEX IMPORT CUSTOMER: #{inspect(reason)}"
        Logger.error(error_msg)

        {:error, error_msg}
    end
  end

  defp fetch_customers(vtex_client, token) when is_nil(token) do
    case VTEX.scroll_masterdata(vtex_client, "CL",
           _size: 250,
           _fields: "id,email,firstName,lastName,document,documentType,phone,homePhone"
         ) do
      {:ok, %{status: status, body: body, headers: headers}}
      when status >= 200 and status < 300 ->
        token = get_token(headers)

        {:ok, body, token}

      {:error, reason} ->
        {:error, reason}

      unknown ->
        error_msg = "unknown response: #{inspect(unknown)}"
        Logger.error(error_msg)
        {:error, error_msg}
    end
  end

  defp fetch_customers(vtex_client, token) do
    case VTEX.scroll_masterdata(vtex_client, "CL", _token: token) do
      {:ok, %{status: status, body: body}}
      when status >= 200 and status < 300 ->
        {:ok, body, token}

      {:error, reason} ->
        {:error, reason}

      unknown ->
        error_msg = "unknown response: #{inspect(unknown)}"
        Logger.error(error_msg)
        {:error, error_msg}
    end
  end

  defp transform_customer(customer_map) do
    document = format_document(customer_map["document"])

    {document, document_type} =
      cond do
        not is_binary(document) -> {nil, nil}
        Brcpfcnpj.cpf_valid?(document) -> {document, "cpf"}
        Brcpfcnpj.cnpj_valid?(document) -> {document, "cnpj"}
        true -> {nil, nil}
      end

    %{
      name: "#{customer_map["firstName"]} #{customer_map["lastName"]}",
      document_type: document_type,
      document: document,
      email: customer_map["email"],
      primary_phone_number: customer_map["phone"] || customer_map["homePhone"],
      vtex_id: customer_map["id"]
    }
  end

  defp search_and_add_customer_address(vtex_client, %{vtex_id: vtex_id} = customer)
       when not is_nil(vtex_id) do
    Logger.info("search customer address for vtex_id #{vtex_id}")

    case VTEX.search_customer_address(vtex_client, vtex_id) do
      {:ok, addresses} ->
        Logger.info("found #{length(addresses)} addresses for vtex_id #{customer[:vtex_id]}")

        addresses
        |> Enum.map(&transform_address/1)
        |> then(&put_in(customer, [:addresses], &1))

      error ->
        Logger.warning("failed to search customer address: #{inspect(error)}")
        customer
    end
  end

  defp search_and_add_customer_address(_vtex_client, customer), do: customer

  defp transform_address(address) do
    %{
      receiver_name: Map.get(address, "receiverName", nil),
      city_name: Map.get(address, "city", nil),
      state: Map.get(address, "state", nil),
      country_name: Map.get(address, "country", nil),
      neighborhood: Map.get(address, "neighborhood", nil),
      street: Map.get(address, "street", nil),
      number: Map.get(address, "number", nil),
      zip: Map.get(address, "postalCode", nil),
      complement: Map.get(address, "complement", nil)
    }
  end

  defp import_customer(vtex_client, organization_id, customer) do
    customer_with_address = search_and_add_customer_address(vtex_client, customer)

    case ImportCustomer.import(organization_id, customer_with_address) do
      {:ok, _} ->
        :ok

      {:error, reason} ->
        Logger.error("Failed to import customer: #{inspect(reason)}")
    end
  end

  defp get_token(headers) do
    Enum.find_value(headers, fn {key, value} ->
      if String.downcase(key) == "x-vtex-md-token", do: value
    end)
  end

  defp format_document(document) when is_binary(document) do
    String.replace(document, ~r/\D/, "")
  end

  defp format_document(document), do: document
end
