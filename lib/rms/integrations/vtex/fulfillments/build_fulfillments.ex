defmodule Rms.Integrations.VTEX.Fulfillments.BuildFulfillments do
  def execute(order) do
    fulfillments =
      order.line_items
      |> group_line_items()
      |> Enum.reduce_while([], fn {{_, _, shipping_method}, line_items}, acc ->
        case build_fulfillment(shipping_method, line_items, order) do
          {:ok, fulfillment} -> {:cont, [fulfillment | acc]}
          {:error, reason} -> {:halt, {:error, reason}}
        end
      end)

    case fulfillments do
      {:error, reason} ->
        {:error, reason}

      fulfillments ->
        {:ok, order |> Map.put(:fulfillments, fulfillments) |> Map.drop([:line_items])}
    end
  end

  defp build_fulfillment(shipping_method, line_items, _order)
       when shipping_method in ["in-store", "local-pickup", "delivery"] do
    {:ok,
     %{
       ecommerce: "vtex",
       shipping_method: shipping_method,
       shipping_settings: nil,
       line_items: line_items
     }}
  end

  defp build_fulfillment(_, _, _) do
    {:error, "shipping method not suported in fullfilments creation"}
  end

  defp group_line_items(line_items) do
    Enum.group_by(line_items, fn line_item ->
      {vtex_shiping_settings(line_item.shipping_settings)["id"],
       vtex_shiping_settings(line_item.shipping_settings)["deliveryChannel"],
       line_item.shipping_method}
    end)
  end

  defp vtex_shiping_settings(nil) do
    %{}
  end

  defp vtex_shiping_settings(settings) do
    settings
  end
end
