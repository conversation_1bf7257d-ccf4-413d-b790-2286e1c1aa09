defmodule Rms.Integrations.VTEX.AuthorizeTransactionWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 1
  alias Rms.Integrations.VTEX

  @impl true
  def process(%{
        args: %{"organization_id" => organization_id, "order_id" => order_id}
      }) do
    order = Rms.Commerce.Orders.get_order!(organization_id, order_id)

    vtex_credential =
      Rms.Integrations.get_vtex_credential!(organization_id)

    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credential, "vtexpayments")

    authorize_all_transactions(order, vtex_client)
  end

  defp authorize_all_transactions(%{addons: addons}, client) do
    addons
    |> Enum.reduce_while({:ok, []}, fn
      %{
        addons: %{
          metadata: %{
            "external_transaction_id" => transaction_id,
            "external_order_id" => external_order_id
          }
        }
      },
      {:ok, acc} ->
        params = build_params(transaction_id)

        case VTEX.authorize_new_transaction(client, transaction_id, params) do
          {:ok, _response} ->
            {:cont, {:ok, [external_order_id | acc]}}

          _ ->
            {:halt, {:error, "error"}}
        end
    end)
  end

  defp build_params(external_transaction_id) do
    %{
      transactionId: external_transaction_id,
      softDescriptor: "iGlu",
      prepareForRecurrency: false
    }
  end
end
