defmodule Rms.Integrations.VTEX.Orders.BuildParams do
  require Logger

  alias Rms.Integrations.VTEX.Orders.BuildParams

  def execute(fulfillment, opts \\ []) do
    with {:ok, {line_items, line_items_total_without_discount}} <-
           BuildParams.Items.execute(fulfillment),
         {:ok, customer} <-
           BuildParams.Customer.execute(fulfillment),
         {:ok, shipping_address, pickup_account_name} <-
           BuildParams.ShippingAddress.execute(fulfillment),
         {:ok, shipping_line} <-
           BuildParams.ShippingSettings.execute(fulfillment) do
      total_items_value =
        build_total_order(line_items_total_without_discount, shipping_line)

      fulfillment = Rms.Repo.preload(fulfillment, order: [:location, :staff])

      location = fulfillment.order.location.external_id || fulfillment.order.location.cnpj

      pickup_account_name =
        if pickup_account_name == :default,
          do: Keyword.get(opts, :default_pickup_account_name),
          else: pickup_account_name

      {
        :ok,
        %{
          marketplaceOrderId: "#{fulfillment.order_id}-#{fulfillment.id}",
          marketplaceOrderStatus: "approved",
          marketplacePaymentValue: Decimal.to_integer(total_items_value),
          priceDivergenceAllowanceRate: 0.50,
          connectorEndpoint: connector_endpoint(),
          connectorName: "iglu",
          allowFranchises: true,
          items: line_items,
          clientProfileData: customer,
          pickupAccountName: pickup_account_name,
          shippingData: %{
            selectedAddresses: shipping_address,
            logisticsInfo: shipping_line
          },
          openTextField:
            build_open_text_field(location, fulfillment.order_id, fulfillment.order.staff)
        }
      }
    end
  end

  defp build_open_text_field(location, order_id, nil) do
    "Pedido feito na loja via iglu Loja: #{location} - Pedido: #{order_id}"
  end

  defp build_open_text_field(location, order_id, staff) do
    "Pedido feito na loja via iglu Loja: #{location} - Pedido: #{order_id} Vendedor: #{staff.id} - #{staff.external_id}"
  end

  defp build_total_order(line_items_total_without_discount, shipping_lines) do
    shipping_value = build_total_shipping(shipping_lines)

    Decimal.add(line_items_total_without_discount, shipping_value)
  end

  defp build_total_shipping(shipping_lines) do
    Enum.reduce(shipping_lines, Decimal.new("0"), fn shipping_line, acc ->
      Decimal.add(acc, shipping_line.price)
    end)
  end

  defp connector_endpoint() do
    case callback_url() do
      {:ok, url} ->
        url

      {:error, message} ->
        Logger.warning("error setting connector endpoint: #{message}")
        "https://eobwd5czlvdt0fk.m.pipedream.net/"
    end
  end

  defp callback_url() do
    base_url =
      :rms
      |> Application.get_env(RmsWeb.Endpoint)
      |> get_in([:url])

    host = Keyword.get(base_url, :host)

    if base_url == [] || host == nil do
      {:error, "host not properly set in configuration, got #{inspect(base_url)}"}
    else
      schema = Keyword.get(base_url, :schema, "https")
      port = Keyword.get(base_url, :port, 443)

      host = "#{schema}://#{host}:#{port}"
      {:ok, host <> "/webhooks/vtex"}
    end
  end
end
