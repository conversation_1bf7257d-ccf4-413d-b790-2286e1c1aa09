defmodule Rms.Integrations.VTEX.Orders.BuildParams.CheckoutItems do
  @moduledoc """
  Builds item parameters specifically for the VTEX Checkout API.
  Handles manual discounts by using the manualPrice field.
  """

  def execute(line_items) do
    line_items
    |> Enum.reduce_while({[], Decimal.new("0")}, fn line_item, {items, total_price} ->
      case build_line_item(line_item) do
        {:ok, {item, item_total}} ->
          {:cont, {items ++ [item], Decimal.add(total_price, item_total)}}

        {:error, error} ->
          {:halt, {:error, error}}
      end
    end)
    |> validate_line_items()
  end

  defp build_line_item(line_item) do
    case find_vtex_mapping(line_item.product_variant.product_variant_mappings) do
      nil ->
        {:error, "mapping not found for variant #{line_item.product_variant_id}"}

      mapping ->
        build_item_with_price(mapping, line_item)
    end
  end

  defp build_item_with_price(mapping, line_item) do
    item = %{
      id: mapping.external_id,
      quantity: line_item.quantity,
      seller: "1",
      price: format_vtex_value(line_item.price)
    }

    total_price =
      line_item.price
      |> Decimal.new()
      |> Decimal.mult(Decimal.new(line_item.quantity))

    {:ok, {item, total_price}}
  end

  defp format_vtex_value(value) do
    value
    |> Decimal.round(2, :down)
    |> Decimal.mult(Decimal.new("100"))
    |> Decimal.to_integer()
  end

  defp find_vtex_mapping(product_variant_mappings) do
    Enum.find(product_variant_mappings, fn mapping ->
      mapping.source == "vtex"
    end)
  end

  defp validate_line_items({:error, error}), do: {:error, error}
  defp validate_line_items({[], _}), do: {:error, "at least one line item is required"}

  defp validate_line_items({items, total_price}),
    do: {:ok, {items, format_vtex_value(total_price)}}
end
