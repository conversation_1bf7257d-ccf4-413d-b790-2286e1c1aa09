defmodule Rms.Integrations.VTEX.Orders.BuildParams.Customer do
  alias Rms.Commerce.Orders.Order
  alias Rms.Customers.Customer

  def execute(%Order{} = order) do
    build_customer(order)
  end

  def execute(fulfillment) do
    build_customer(fulfillment.order)
  end

  defp build_customer(order) do
    case order.customer do
      %Customer{} = customer ->
        {first_name, last_name} = customer_name(customer.name)

        {:ok,
         %{
           email: customer.email,
           firstName: first_name,
           lastName: last_name,
           documentType: customer.document_type,
           document: customer.document,
           phone: customer.primary_phone_number
         }}

      _ ->
        {:error, %{reason: "A customer is required", stacktrace: "#{__MODULE__}"}}
    end
  end

  defp customer_name(name) do
    case String.split(name) do
      [first_name] -> {first_name, "(sobrenome não informado)"}
      [first_name | surname] -> {first_name, Enum.join(surname, " ")}
    end
  end
end
