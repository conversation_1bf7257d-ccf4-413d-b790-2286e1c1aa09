defmodule Rms.Integrations.VTEX.Orders.BuildParams.Items do
  def execute(fulfillment) do
    build_line_items(fulfillment.line_items)
  end

  defp build_line_items([]) do
    {:error, %{reason: "At least 1 line item is required", stacktrace: "#{__MODULE__}"}}
  end

  defp build_line_items(line_items) do
    line_items
    |> Enum.reduce_while({[], Decimal.new("0")}, fn line_item, {items, price} ->
      case build_line_item(line_item) do
        {:ok, {item, item_price_without_discount}} ->
          {:cont, {[item | items], Decimal.add(price, item_price_without_discount)}}

        {:error, item} ->
          {:halt, {:error, item}}
      end
    end)
    |> validate_line_items()
  end

  defp build_line_item(line_item) do
    line_item = Rms.Repo.preload(line_item, product_variant: [:product_variant_mappings])

    case find_vtex_mapping(line_item.product_variant.product_variant_mappings) do
      nil ->
        {:error, %{reason: "Mapping not found for VTEX variant", stacktrace: "#{__MODULE__}"}}

      mapping ->
        item = %{
          id: mapping.external_id,
          quantity: line_item.quantity,
          price: format_vtex_value(line_item.price),
          seller: "1"
        }

        total_price =
          line_item.price
          |> Decimal.add(line_item.manual_discount)
          |> Decimal.mult(line_item.quantity)

        {:ok, {item, total_price}}
    end
  end

  defp find_vtex_mapping(product_variant_mappings) do
    Enum.find(product_variant_mappings, fn mapping ->
      mapping.source == "vtex"
    end)
  end

  defp validate_line_items({:error, error}) do
    {:error, error}
  end

  defp validate_line_items({items, total_price}) do
    {:ok, {items, format_vtex_value(total_price)}}
  end

  defp format_vtex_value(value) do
    value
    |> Decimal.round(2, :down)
    |> Decimal.mult(Decimal.new("100"))
    |> Decimal.to_integer()
  end
end
