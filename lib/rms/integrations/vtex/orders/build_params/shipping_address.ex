defmodule Rms.Integrations.VTEX.Orders.BuildParams.ShippingAddress do
  def execute(fulfillment) do
    case fulfillment.shipping_method do
      "delivery" ->
        build_shipping_address(fulfillment.shipping_method, fulfillment.order)

      "local-pickup" ->
        build_shipping_address(fulfillment.shipping_method, fulfillment.line_items)
    end
  end

  defp build_shipping_address("delivery", order) do
    case order.shipping_address do
      %Rms.Commerce.Orders.Order.ShippingAddress{} = address ->
        {:ok,
         [
           %{
             receiverName: address.receiver_name,
             addressType: "residential",
             postalCode: address.zip,
             city: address.city_name,
             state: address.state,
             country: iso_country(address.country_name),
             street: address.street,
             number: if(address.number, do: address.number, else: "s/n"),
             neighborhood: address.neighborhood,
             complement: address.complement
           }
         ], nil}

      _ ->
        {:error, %{reason: "Order shipping address required", stacktrace: "#{__MODULE__}"}}
    end
  end

  defp build_shipping_address("local-pickup", line_items) do
    settings =
      line_items
      |> Enum.map(fn line_item ->
        line_item.shipping_settings["pickupStoreInfo"]["address"]
        |> Map.update!("geoCoordinates", &format_geocordinates/1)
      end)
      |> Enum.uniq()

    pickup_account_name = fetch_pickup_account_name(line_items)

    {:ok, settings, pickup_account_name || :default}
  end

  defp fetch_pickup_account_name([line_item | _]) do
    get_in(line_item.shipping_settings, ["deliveryIds", Access.at(0), "accountCarrierName"])
  end

  defp fetch_pickup_account_name([]), do: nil

  defp format_geocordinates([longitude, latitude]) do
    %{
      "latitude" => latitude,
      "longitude" => longitude
    }
  end

  defp iso_country(_) do
    "BRA"
  end
end
