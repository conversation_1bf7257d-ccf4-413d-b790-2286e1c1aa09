defmodule Rms.Integrations.VTEX.Orders.BuildParams.ShippingSettings do
  def execute(line_items) when is_list(line_items) do
    build_shipping_settings(line_items)
  end

  def execute(fulfillment) do
    build_shipping_settings(fulfillment.line_items)
  end

  defp build_shipping_settings(line_items) do
    settings =
      Enum.with_index(line_items)
      |> Enum.map(fn {line_item, index} ->
        %{
          itemIndex: index,
          selectedSla: line_item.shipping_settings["id"],
          price: line_item.shipping_settings["price"],
          selectedDeliveryChannel: line_item.shipping_settings["deliveryChannel"],
          shippingEstimate: line_item.shipping_settings["shippingEstimate"],
          lockTTL: line_item.shipping_settings["shippingEstimate"]
        }
      end)

    {:ok, settings}
  end
end
