defmodule Rms.Integrations.VTEX.CancelOrderWorker do
  use Oban.Pro.Worker, queue: :vtex, max_attempts: 3
  alias Rms.Commerce.Fulfillments

  require Logger

  @impl Oban.Pro.Worker
  def process(%{
        args: %{"marketplace_order_id" => marketplace_order_id}
      }) do
    [_order_id, fulfillment_id] = String.split(marketplace_order_id, "-")

    fulfillment_id
    |> Fulfillments.unsafe_get_fulfillment!()
    |> cancel_order()
  end

  defp retrieve_order_from_vtex(organization_id, order_id) do
    get_client(organization_id)
    |> Rms.Integrations.VTEX.get_ecommerce_order(order_id)
  end

  # Cancel
  defp cancel_order(fulfillment) do
    retrieve_order_from_vtex(fulfillment.organization_id, fulfillment.external_reference)
    |> execute_cancel_order(fulfillment)
  end

  defp execute_cancel_order({:ok, %{"status" => status}}, fulfillment)
       when status in ~w(cancel cancelled canceled) do
    Fulfillments.update_fulfillment(fulfillment, %{"status" => status})
  end

  defp execute_cancel_order({:ok, %{"status" => non_updateable_status}}, _) do
    err = "Status [#{non_updateable_status}] cannot be updated!"
    Logger.error(err)
    {:discard, "cannot update #{non_updateable_status}"}
  end

  defp execute_cancel_order({:error, reason}, _) do
    Logger.error("Invalid status for order, reason: [#{inspect(reason)}]")
    {:error, reason}
  end

  defp execute_cancel_order(_, _) do
    {:discard, "unhandled result"}
  end

  defp get_client(organization_id) do
    Rms.Integrations.get_vtex_credential!(organization_id)
    |> Rms.Integrations.VTEX.client()
  end
end
