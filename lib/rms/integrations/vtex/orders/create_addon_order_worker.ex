defmodule Rms.Integrations.VTEX.CreateAddonOrderWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 1
  alias Rms.Integrations.VTEX

  @impl true
  def process(%{
        args: %{"organization_id" => organization_id, "addons" => addons, "order_id" => order_id}
      }) do
    order = Rms.Commerce.Orders.get_order!(organization_id, order_id)

    addons_list =
      Enum.reduce_while(addons, {:ok, []}, fn addon, {:ok, acc} ->
        case VTEX.IssueAddonOrder.execute(organization_id, addon, order_id) do
          {:ok,
           %{
             "sequence" => sequence,
             "external_id" => external_order_id,
             "transaction_id" => transaction_id,
             "merchant_name" => merchant_name
           }} ->
            new_metadata =
              Map.get(addon, "metadata", %{})
              |> Map.put("external_sequence_id", sequence)
              |> Map.put("external_order_id", external_order_id)
              |> Map.put("external_transaction_id", transaction_id)
              |> Map.put("merchant_name", merchant_name)

            updated_addon = Map.put(addon, "metadata", new_metadata)
            {:cont, {:ok, [updated_addon | acc]}}

          _ ->
            {:halt, {:error, "error"}}
        end
      end)

    case addons_list do
      {:ok, addons} ->
        Rms.Commerce.Orders.update_order!(order, %{addons: addons})
        :ok

      error ->
        error
    end
  end
end
