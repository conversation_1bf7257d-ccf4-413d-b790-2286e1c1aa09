defmodule Rms.Integrations.VTEX.Orders.CreateOrderHookConfiguration do
  use Oban.Pro.Worker, queue: :vtex, max_attempts: 3

  require Logger

  @default_payload %{
    "filter" => %{
      "type" => "FromWorkflow",
      "status" => ["payment-approved", "ready-for-handling"]
    },
    "hook" => %{
      "url" => nil,
      "headers" => %{"x-vtex-api-appkey" => nil}
    }
  }

  @impl Oban.Pro.Worker
  def process(%{args: %{"organization_id" => organization_id}}) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    payload =
      @default_payload
      |> put_in(["hook", "headers", "x-vtex-api-appkey"], vtex_credential.app_key)
      |> put_in(["hook", "url"], connector_endpoint())

    organization_id
    |> Rms.Integrations.VTEX.client()
    |> Rms.Integrations.VTEX.create_order_hook_configuration(payload)
  end

  defp connector_endpoint() do
    case callback_url() do
      {:ok, url} ->
        url

      {:error, message} ->
        Logger.warning("error setting connector endpoint: #{message}")
        "https://eobwd5czlvdt0fk.m.pipedream.net/"
    end
  end

  defp callback_url() do
    base_url =
      :rms
      |> Application.get_env(RmsWeb.Endpoint)
      |> get_in([:url])

    host = Keyword.get(base_url, :host)

    if base_url == [] || host == nil do
      {:error, "host not properly set in configuration, got #{inspect(base_url)}"}
    else
      schema = Keyword.get(base_url, :schema, "https")
      port = Keyword.get(base_url, :port, 443)

      host = "#{schema}://#{host}:#{port}"
      {:ok, host <> "/webhooks/vtex/orders/hook"}
    end
  end
end
