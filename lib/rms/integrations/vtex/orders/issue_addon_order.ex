defmodule Rms.Integrations.VTEX.IssueAddonOrder do
  # alias Rms.Commerce.Fulfillments.Fulfillment

  # def execute(%Order{addons: [ _ ]} = order) do
  #   vtex_credential =
  #     Rms.Integrations.get_vtex_credential!(order.organization_id)
  #   opts = [default_pickup_account_name: vtex_credential.account_name]
  #   {:ok, fulfillment}
  # end

  def execute(organization_id, addon, order_id) do
    vtex_credential =
      Rms.Integrations.get_vtex_credential!(organization_id)

    order =
      Rms.Commerce.Orders.get_order!(organization_id, order_id) |> Rms.Repo.preload(:customer)

    with {:ok, order_params} <-
           build_order_params(addon, order) do
      create_vtex_order(vtex_credential, order_params)
    end
  end

  defp create_vtex_order(vtex_credential, order_params) do
    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credential)

    with {:ok,
          %{
            "orders" => [
              %{
                "orderId" => external_order_id
              }
            ],
            "transactionData" => %{
              "merchantTransactions" => [
                %{"transactionId" => transaction_id, "merchantName" => merchant_name}
              ]
            }
          }} <-
           Rms.Integrations.VTEX.create_ecommerce_order(
             vtex_client,
             vtex_credential.account_name,
             vtex_credential.affiliate_id,
             vtex_credential.sales_channel_id,
             order_params
           ),
         {:ok,
          %{
            "sequence" => sequence
          }} <-
           Rms.Integrations.VTEX.get_ecommerce_order(vtex_client, external_order_id) do
      {:ok,
       %{
         "sequence" => sequence,
         "external_id" => external_order_id,
         "transaction_id" => transaction_id,
         "merchant_name" => merchant_name
       }}
    end
  end

  def build_item(%{"metadata" => metadata} = addon) do
    %{
      id: addon["external_id"],
      quantity: addon["quantity"],
      seller: "1",
      price: format_vtex_value(addon["price"]),
      attachments: metadata |> Map.take(["name", "content"])
    }
  end

  def build_item(addon) do
    %{
      id: addon["external_id"],
      quantity: addon["quantity"],
      seller: "1",
      price: format_vtex_value(addon["price"])
    }
  end

  def build_order_params(addon, order) do
    [first_name, last_name] = String.split(order.customer.name, " ") |> Enum.take(2)

    {:ok,
     %{
       items: [build_item(addon)],
       clientProfileData: %{
         email: order.customer.email,
         firstName: first_name,
         lastName: last_name,
         documentType: "cpf",
         document: order.customer.document,
         phone: order.customer.primary_phone_number
       },
       shippingData: %{
         address: %{
           addressType: "residential",
           receiverName: order.shipping_address.receiver_name,
           postalCode: order.shipping_address.zip,
           city: order.shipping_address.city_name,
           state: order.shipping_address.state,
           country: "BRA",
           street: order.shipping_address.street,
           number: order.shipping_address.number,
           neighborhood: order.shipping_address.neighborhood,
           complement: order.shipping_address.complement
         },
         logisticsInfo: [
           %{
             selectedSla: addon["delivery_option"]["id"],
             price: addon["delivery_option"]["price"],
             selectedDeliveryChannel: addon["delivery_option"]["metadata"]["deliveryChannel"],
             shippingEstimate: addon["delivery_option"]["metadata"]["shippingEstimate"],
             lockTTL: addon["delivery_option"]["metadata"]["lockTTL"]
           }
         ]
       },
       paymentData: %{
         payments: [
           %{
             installments: 1,
             paymentSystem: 201,
             referenceValue: format_vtex_value(addon["price"]),
             value: format_vtex_value(addon["price"])
           }
         ]
       }
     }}
  end

  defp format_vtex_value(value) do
    value
    |> Decimal.round(2, :down)
    |> Decimal.mult(Decimal.new("100"))
    |> Decimal.to_integer()
  end
end
