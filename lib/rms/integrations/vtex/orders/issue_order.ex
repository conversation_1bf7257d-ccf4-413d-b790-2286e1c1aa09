defmodule Rms.Integrations.VTEX.IssueOrder do
  alias Rms.Commerce.Fulfillments.Fulfillment

  def execute(%Fulfillment{shipping_method: "in-store"} = fulfillment) do
    {:ok, fulfillment}
  end

  def execute(fulfillment) do
    vtex_credential =
      Rms.Integrations.get_vtex_credential!(fulfillment.organization_id)

    opts = [default_pickup_account_name: vtex_credential.account_name]

    with {:ok, order_params} <-
           build_order_params(fulfillment, opts),
         {:ok, vtex_response} <- create_vtex_order(vtex_credential, order_params) do
      update_fulfillment(fulfillment, vtex_response)
    end
  end

  defp create_vtex_order(vtex_credential, order_params) do
    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credential)

    Rms.Integrations.VTEX.create_external_ecommerce_order(
      vtex_client,
      vtex_credential.account_name,
      vtex_credential.affiliate_id,
      order_params
    )
  end

  defdelegate build_order_params(fulfillment, opts),
    to: Rms.Integrations.VTEX.Orders.BuildParams,
    as: :execute

  defp update_fulfillment(fulfillment, vtex_response) do
    external_id = get_in(vtex_response, ["fields", "mainOrderId"])

    Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{
      external_reference: external_id,
      status: "created"
    })
  end
end
