defmodule Rms.Integrations.VTEX.SendPaymentInformationWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 1
  alias Rms.Integrations.VTEX

  @impl true
  def process(%{args: %{"organization_id" => organization_id, "order_id" => order_id}}) do
    order = Rms.Commerce.Orders.get_order!(organization_id, order_id)

    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)
    vtex_client = Rms.Integrations.VTEX.client(vtex_credential, "vtexpayments")

    send_payment_information_for_addons(order, vtex_client)
  end

  defp send_payment_information_for_addons(%{addons: addons}, client) do
    addons
    |> Enum.reduce_while({:ok, []}, fn
      %{
        metadata: %{
          "external_order_id" => external_order_id,
          "merchant_name" => merchant_name,
          "external_transaction_id" => transaction_id
        },
        price: price
      },
      {:ok, acc} ->
        params = build_params(transaction_id, merchant_name, price)

        case VTEX.send_payment_information(client, transaction_id, external_order_id, params) do
          {:ok, _response} ->
            {:cont, {:ok, [external_order_id | acc]}}

          _ ->
            {:halt, {:error, "error"}}
        end
    end)
  end

  defp build_params(external_transaction_id, merchant_name, price) do
    [
      %{
        paymentSystem: 201,
        installments: 1,
        installmentsInterestRate: 0,
        value: price,
        installmentsValue: price,
        referenceValue: price,
        transaction: %{
          id: external_transaction_id,
          merchantName: merchant_name
        }
      }
    ]
  end
end
