defmodule Rms.Integrations.VTEX.UpdateOrderStatus do
  use Oban.Pro.Worker, queue: :vtex, max_attempts: 3

  require Logger

  @impl Oban.Pro.Worker
  def process(%{args: %{"vtex_order_id" => external_id, "organization_id" => organization_id}}) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    vtex_credential
    |> Rms.Integrations.VTEX.client()
    |> Rms.Integrations.VTEX.get_ecommerce_order(external_id)
    |> do_proccess()
  end

  defp do_proccess(%{"marketingData" => %{"utmSource" => "iglu"}, "orderFormId" => external_id})
       when not is_nil(external_id) do
    update_order(external_id)
  end

  defp do_proccess(_vtex_order), do: {:discard, "skip unrelated order"}

  defp update_order(external_id) do
    with {:ok, payment} <- Rms.Finance.get_payment_by_external_reference("vtex", external_id),
         {:ok, updated_payment} <- Rms.Finance.update_payment(payment, %{status: "settled"}),
         {:ok, _} <- Rms.Finance.close_transaction(updated_payment.transaction, skip_events: true) do
      :ok
    else
      error ->
        Logger.warning("failed to update order status: #{inspect(error)}")
        {:error, "failed to update order status"}
    end
  end
end
