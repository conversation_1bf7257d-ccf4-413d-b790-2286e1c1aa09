defmodule Rms.Integrations.VTEX.CreatePackingWorker do
  alias Rms.Commerce.Packings
  alias Rms.Repo
  use Oban.Pro.Workers.Workflow

  require Logger

  @impl true
  def process(%Oban.Job{
        args: %{"organization_id" => organization_id, "fulfillment_id" => fulfillment_id}
      }) do
    fulfillment =
      Rms.Commerce.Fulfillments.get_fulfillment!(organization_id, fulfillment_id, [
        :line_items,
        :order
      ])

    do_process(organization_id, fulfillment)
  end

  def do_process(_organization_id, %{shipping_method: "in-store"}),
    do: {:discard, "skip in-store packing"}

  def do_process(organization_id, fulfillment) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(fulfillment.organization_id)

    vtex_client =
      Rms.Integrations.VTEX.client(vtex_credential)

    with {:ok, order_details} <-
           Rms.Integrations.VTEX.get_ecommerce_order(vtex_client, fulfillment.external_reference),
         {:ok, inserted_packings} <- create_packings(organization_id, fulfillment, order_details) do
      Enum.each(inserted_packings, fn {_, packing} ->
        send_notifications(organization_id, packing)
      end)

      {:ok, inserted_packings}
    end
  end

  defp send_notifications(organization_id, packing) do
    packing = Repo.preload(packing, :dock)
    location_id = packing.dock.location_id

    message =
      case length(packing.packing_items) do
        1 -> "Sua loja recebeu um novo pedido para retirada de 1 item"
        n -> "Sua loja recebeu um novo pedido para retirada de #{n} itens"
      end

    case fetch_related_staff_ids(organization_id, location_id) do
      [_ | _] = related_staff ->
        Rms.Notifications.unsafe_send_push_notification(
          title: "Novo pedido para retirada (##{packing.id})",
          message: message,
          staffs_ids: related_staff,
          url: "iglu-mpos://mpos.iglu.com.br/home/<USER>/pack-detail/#{packing.id}",
          channel_id: "7cb08b55-6e17-4cf6-ae8e-99d4c75d5f71"
        )

      _ ->
        Logger.info(
          "No stocker staff found when creating packing #{packing.id} for location #{location_id}"
        )
    end
  end

  defp create_packings(organization_id, fulfillment, order_details) do
    with {:ok, packing_attrs} <- build_packings_attrs(organization_id, fulfillment, order_details) do
      Packings.create_packings(organization_id, fulfillment.id, packing_attrs)
      |> create_packing_normalizer()
    end
  end

  defp fetch_related_staff_ids(organization_id, location_id) do
    Rms.Accounts.list_staffs(organization_id,
      query_params: [location_id: location_id, roles: {:stocker, true}]
    )
    |> Enum.map(& &1.id)
  end

  defp fetch_dock_mapping(organization_id, order_details) do
    external_dock_to_dock_mapping =
      order_details["shippingData"]["logisticsInfo"]
      |> Enum.flat_map(& &1["deliveryIds"])
      |> Enum.map(& &1["dockId"])
      |> Enum.uniq()
      |> then(&Rms.Accounts.get_docks_from_external_ids(organization_id, &1))
      |> Map.new(fn {dock_id, external_id} -> {external_id, dock_id} end)

    if map_size(external_dock_to_dock_mapping) > 0 do
      {:ok, external_dock_to_dock_mapping}
    else
      {:cancel, "no docks associated with org and order"}
    end
  end

  defp fetch_external_id_to_line_item_mapping(organization_id, fulfillment, order_details) do
    product_variant_to_line_item_mapping =
      fulfillment.line_items
      |> Map.new(fn line_item ->
        {line_item.product_variant_id, line_item}
      end)

    external_id_to_line_item_mapping =
      fulfillment.line_items
      |> Enum.map(& &1.product_variant_id)
      |> then(&Rms.Integrations.get_product_variant_external_ids(organization_id, &1, "vtex"))
      |> Map.new(fn {pv_id, external_id} ->
        {external_id, product_variant_to_line_item_mapping[pv_id]}
      end)
      |> Map.reject(fn
        {_, nil} -> true
        _ -> false
      end)

    if length(order_details["shippingData"]["logisticsInfo"]) ==
         map_size(external_id_to_line_item_mapping) do
      {:ok, external_id_to_line_item_mapping}
    else
      {:cancel, "no 1<>1 mapping between line items and external order items"}
    end
  end

  defp build_packings_attrs(organization_id, fulfillment, order_details) do
    with {:ok, external_dock_to_dock_mapping} <-
           fetch_dock_mapping(organization_id, order_details),
         {:ok, external_id_to_line_item_mapping} <-
           fetch_external_id_to_line_item_mapping(organization_id, fulfillment, order_details) do
      packing_attrs =
        order_details["shippingData"]["logisticsInfo"]
        |> Enum.flat_map(fn item_info ->
          item_info["deliveryIds"]
          |> Enum.map(fn item_delivery_info ->
            dock_id = Map.get(external_dock_to_dock_mapping, item_delivery_info["dockId"])
            external_id = item_info["itemId"]

            %{
              packing_item: %{
                line_item: get_in(external_id_to_line_item_mapping, [external_id]),
                line_item_id:
                  get_in(external_id_to_line_item_mapping, [external_id, Access.key!(:id)]),
                quantity: item_delivery_info["quantity"]
              },
              dock_id: dock_id,
              courier_name: item_delivery_info["courierName"]
            }
          end)
        end)
        |> Enum.reject(fn
          %{packing_item: %{line_item: nil}} -> true
          _ -> false
        end)
        |> Enum.group_by(&{&1.dock_id, &1.courier_name}, & &1.packing_item)
        |> Enum.map(fn {{dock_id, courier_name}, packing_items} ->
          %{
            shipping_method: fulfillment.shipping_method,
            courier_name: courier_name,
            dock_id: dock_id,
            customer_id: fulfillment.order.customer_id,
            packing_items: packing_items,
            total_price: calculate_total_price(packing_items)
          }
        end)

      {:ok, packing_attrs}
    end
  end

  defp calculate_total_price(packing_items) do
    Enum.reduce(packing_items, Decimal.new("0"), fn packing_item, acc ->
      Decimal.add(acc, Decimal.mult(packing_item.quantity, packing_item.line_item.price))
    end)
  end

  defp create_packing_normalizer(result) do
    case result do
      {:ok, packings} ->
        {:ok, packings}

      {:error, _, changeset, _} ->
        {:error, changeset}

      _ ->
        result
    end
  end
end
