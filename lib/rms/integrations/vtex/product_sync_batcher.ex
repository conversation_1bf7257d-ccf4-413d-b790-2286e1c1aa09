defmodule Rms.Integrations.VTEX.ProductSyncBatcher do
  alias Rms.Workers.VTEXImportProductVariant
  use GenServer
  require Logger

  @batch_size 1000
  @flush_interval :timer.seconds(2)

  # Client API
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  def add_notification(organization_id, notification) do
    GenServer.call(__MODULE__, {:add, organization_id, notification})
  end

  def change_flush_interval(new_interval) do
    GenServer.cast(__MODULE__, {:change_flush_interval, new_interval})
  end

  def change_batch_size(new_batch_size) do
    GenServer.cast(__MODULE__, {:change_batch_size, new_batch_size})
  end

  def force_flush do
    GenServer.call(__MODULE__, :force_flush)
  end

  # Server Callbacks
  def init(opts) do
    flush_interval = Keyword.get(opts, :flush_interval, @flush_interval)
    schedule_flush(flush_interval)

    batch_size = Keyword.get(opts, :batch_size, @batch_size)

    {:ok,
     %{
       notifications: [],
       notifications_length: 0,
       subscribers: %{},
       last_flush: System.monotonic_time(:millisecond),
       flush_interval: flush_interval,
       batch_size: batch_size
     }}
  end

  def handle_cast({:change_flush_interval, new_interval}, state) do
    schedule_flush(new_interval)
    {:noreply, %{state | flush_interval: new_interval}}
  end

  def handle_cast({:change_batch_size, new_batch_size}, state) do
    {:noreply, %{state | batch_size: new_batch_size}}
  end

  def handle_call(:force_flush, _from, state) do
    new_state = flush(state)
    {:reply, :ok, new_state}
  end

  def handle_call({:add, organization_id, notification}, from, state) do
    notification_entry = %{
      organization_id: organization_id,
      sku_id: notification["IdSku"],
      sales_channel_id: notification["sales_channel_id"],
      affiliate_id: notification["affiliate_id"]
    }

    new_state = %{
      state
      | notifications: [notification_entry | state.notifications],
        notifications_length: state.notifications_length + 1,
        subscribers:
          Map.update(
            state.subscribers,
            {organization_id, notification["IdSku"]},
            [from],
            &[from | &1]
          )
    }

    if new_state.notifications_length >= state.batch_size do
      {:noreply, flush(new_state)}
    else
      {:noreply, new_state}
    end
  end

  def handle_info(:flush, state) do
    schedule_flush(state.flush_interval)
    {:noreply, flush(state)}
  end

  # Private Functions
  defp schedule_flush(flush_interval) do
    Process.send_after(self(), :flush, flush_interval)
  end

  defp flush(%{notifications: []} = state), do: state

  defp flush(state) do
    notifications_by_org =
      state.notifications
      |> Enum.group_by(& &1.organization_id)

    notifications_by_org
    |> Enum.each(fn {org_id, notifications} ->
      product_sync = create_product_sync!(org_id)

      notifications
      |> Enum.map(fn notification ->
        args = Map.put(notification, :product_sync_id, product_sync.id)

        VTEXImportProductVariant.new(args)
      end)
      |> then(&Oban.insert_all(&1))
    end)

    # Reply to all subscribers
    for {_key, subscribers} <- state.subscribers,
        subscriber <- subscribers do
      GenServer.reply(subscriber, :ok)
    end

    %{
      state
      | notifications: [],
        notifications_length: 0,
        subscribers: %{},
        last_flush: System.monotonic_time(:millisecond)
    }
  end

  defp create_product_sync!(organization_id) do
    Rms.Integrations.create_product_sync!(organization_id, %{
      source: "vtex",
      external_id: Ecto.UUID.autogenerate()
    })
  end
end
