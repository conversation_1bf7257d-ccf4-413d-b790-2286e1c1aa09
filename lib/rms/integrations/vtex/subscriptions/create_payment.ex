defmodule Rms.Integrations.VTEX.Subscriptions.CreatePayment do
  def execute(organization_id, params) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    client =
      Rms.Integrations.VTEX.client(vtex_credential)

    customer =
      Rms.Customers.get_customer!(organization_id, params["customer_id"])
      |> build_vtex_customer_info

    shipping_address =
      case params["shipping_address"] do
        nil ->
          raise "can't create payment without shipping address"

        _shipping_address ->
          params["shipping_address"]
          |> build_vtex_shipping_address()
      end

    addon =
      Rms.Commerce.Products.get_addon!(organization_id, params["addon_id"], [
        :addon_mappings
      ])

    order_items =
      addon
      |> build_vtex_order_items()

    subscription =
      addon.metadata
      |> build_vtex_subscriptions()

    with {:ok, %{"orderFormId" => cart_id}} <- Rms.Integrations.VTEX.create_cart(client),
         {:ok, _} <- Rms.Integrations.VTEX.add_cart_items(client, cart_id, order_items),
         {:ok, _} <-
           Rms.Integrations.VTEX.add_customer_to_cart(client, cart_id, customer),
         {:ok, _} <-
           Rms.Integrations.VTEX.add_delivery_options_to_cart(client, cart_id, shipping_address),
         {:ok, _} <-
           Rms.Integrations.VTEX.add_cart_subscriptions(client, cart_id, subscription) do
      if vtex_credential.alias do
        {:ok, "https://#{vtex_credential.alias}/checkout?orderFormId=#{cart_id}#/payments",
         cart_id}
      else
        {:ok,
         "https://#{vtex_credential.account_name}.myvtex.com/checkout?orderFormId=#{cart_id}#/payments",
         cart_id}
      end
    end
  end

  defp build_vtex_order_items(addon) do
    case get_vtex_mapping(addon.addon_mappings) do
      nil ->
        raise "can't create order without mapping"

      mapping ->
        [
          %{
            "id" => mapping.external_id,
            "quantity" => 1,
            "seller" => "1",
            "attachments" => [
              addon.metadata
            ]
          }
        ]
    end
  end

  defp get_vtex_mapping(mappings) do
    Enum.find(mappings, fn mapping ->
      mapping.source == "vtex"
    end)
  end

  defp build_vtex_customer_info(customer) do
    [first_name | last_name] = String.split(customer.name, " ")
    last_name = Enum.join(last_name, " ")

    %{
      "email" => customer.email,
      "firstName" => first_name,
      "lastName" => last_name,
      "document" => customer.document,
      "documentType" => customer.document_type,
      "phone" => customer.primary_phone_number
    }
  end

  defp build_vtex_shipping_address(shipping_address) do
    %{
      "clearAddressIfPostalCodeNotFound" => false,
      "selectedAddresses" => [
        %{
          "addressType" => "residential",
          "receiverName" => shipping_address["receiver_name"],
          "isDisposable" => false,
          "postalCode" => shipping_address["zip"],
          "city" => shipping_address["city_name"],
          "state" => shipping_address["state"],
          "country" => shipping_address["country_name"],
          "street" => shipping_address["street"],
          "number" => shipping_address["number"],
          "neighborhood" => shipping_address["neighborhood"],
          "complement" => shipping_address["complement"],
          "reference" => nil
        }
      ]
    }
  end

  defp build_vtex_subscriptions(%{
         "name" => _,
         "content" => %{
           "vtex.subscription.key.frequency" => cycle
         }
       }) do
    [interval, periodicity] = format_cycle(cycle)

    [
      %{
        "itemIndex" => 0,
        "plan" => %{
          "validity" => %{
            "begin" => "0001-01-01T00:00:00",
            "end" => nil
          },
          "frequency" => %{
            "periodicity" => periodicity,
            "interval" => interval
          },
          "type" => "RECURRING_PAYMENT"
        },
        "executionCount" => 0
      }
    ]
  end

  defp build_vtex_subscriptions(_) do
    raise "subscription cycle invalid"
  end

  defp format_cycle(cycle) do
    [number, day] = String.split(cycle, " ")
    format_cycle(number, day)
  end

  defp format_cycle(number, "day") do
    [
      number,
      "DAY"
    ]
  end

  defp format_cycle(number, "month") do
    [
      number,
      "MONTH"
    ]
  end

  defp format_cycle(number, "year") do
    [
      number,
      "YEAR"
    ]
  end

  defp format_cycle(number, _) do
    [
      number,
      "MONTH"
    ]
  end
end
