defmodule Rms.Integrations.VTEX.PaymentNotificationWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 3
  alias Rms.Integrations.VTEX

  require Logger

  @delay 30 * 60

  @impl Oban.Pro.Worker
  def process(%Job{
        args: %{"order_id" => order_id, "organization_id" => organization_id}
      }) do
    order = Rms.Commerce.Orders.get_order!(organization_id, order_id, [:fulfillments])

    case snooze?(order.fulfillments) do
      :wakeup ->
        do_process(order.fulfillments)

      {:snooze, time} ->
        {:snooze, time}
    end
  end

  defp do_process([fulfillment | _] = fulfillments) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(fulfillment.organization_id)
    client = VTEX.client(vtex_credential)

    with {:ok, order} <- VTEX.get_ecommerce_order(client, fulfillment.external_reference),
         {:ok, payment_id} <- extract_payment_id(order),
         {:ok, _} <-
           VTEX.send_payment_notification(
             client,
             fulfillment.external_reference,
             payment_id
           ) do
      fulfillments
      |> Enum.reject(&(&1.shipping_method == "in-store"))
      |> Enum.reduce(Ecto.Multi.new(), fn fulfillment, multi ->
        Ecto.Multi.update(
          multi,
          {:update_fulfillment, fulfillment.id},
          Rms.Commerce.Fulfillments.Fulfillment.update_changeset(fulfillment, %{
            status: "approved"
          })
        )
      end)
      |> Rms.Repo.transaction()
    end
  end

  defp snooze?(fulfillments) do
    current_time = DateTime.utc_now()
    delivery_fulfillments = Enum.reject(fulfillments, &(&1.shipping_method == "in-store"))

    delays =
      Enum.map(delivery_fulfillments, fn fulfillment ->
        DateTime.diff(current_time, fulfillment.inserted_at)
      end)

    case Enum.any?(delays, &(&1 <= @delay)) do
      true -> {:snooze, @delay - Enum.min(delays)}
      false -> :wakeup
    end
  end

  defp extract_payment_id(%{"paymentData" => %{"transactions" => transactions}}) do
    case get_in(transactions, [Access.at(0), "payments", Access.at(0), "id"]) do
      nil -> {:error, :payment_id_not_found}
      payment_id -> {:ok, payment_id}
    end
  end

  defp extract_payment_id(_), do: {:error, :invalid_order_response}
end
