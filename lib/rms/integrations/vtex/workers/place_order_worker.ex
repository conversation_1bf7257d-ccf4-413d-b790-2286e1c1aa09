defmodule Rms.Integrations.VTEX.PlaceOrderWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 3
  alias Rms.Integrations.VTEX
  alias Rms.Integrations.VTEX.Orders.BuildParams

  @impl Oban.Pro.Worker
  def process(%{
        args: %{
          "organization_id" => organization_id,
          "order_id" => order_id
        }
      }) do
    credential = Rms.Integrations.get_vtex_credential!(organization_id)
    client = VTEX.client(credential)
    payment_client = VTEX.client(credential, "vtexpayments")

    order =
      Rms.Commerce.Orders.get_order!(organization_id, order_id, [
        :customer,
        :staff,
        :location,
        :discounts,
        fulfillments: [
          :shipping_settings,
          line_items: [product_variant: [:product_variant_mappings]]
        ]
      ])

    with {:ok, order_params} <- build_order_params(credential, order),
         {:ok, order_data} <- VTEX.place_order(client, order_params[:params]),
         transaction_id =
           get_in(order_data, [
             "transactionData",
             "merchantTransactions",
             Access.at(0),
             "transactionId"
           ]),
         payment_params =
           build_payment_information(
             transaction_id,
             credential,
             order_params.total_price
           ),
         {:ok, _} <-
           VTEX.send_payment_information(
             payment_client,
             transaction_id,
             get_in(order_data, ["orders", Access.at(0), "orderId"]),
             payment_params
           ),
         {:ok, _} <-
           VTEX.process_order(client, get_in(order_data, ["orders", Access.at(0), "orderGroup"])) do
      # Match fulfillments with VTEX order IDs
      fulfillment_order_map =
        match_fulfillments_to_orders(order.fulfillments, order_data["orders"])

      order.fulfillments
      |> Enum.reject(&(&1.shipping_method == "in-store"))
      |> Enum.reduce(Ecto.Multi.new(), fn fulfillment, multi ->
        vtex_order_id = Map.get(fulfillment_order_map, fulfillment.id)

        Ecto.Multi.update(
          multi,
          {:update_fulfillment, fulfillment.id},
          Rms.Commerce.Fulfillments.Fulfillment.update_changeset(fulfillment, %{
            external_reference: vtex_order_id,
            status: "created"
          })
        )
      end)
      |> Rms.Repo.transaction()
    end
  end

  defp build_order_params(credential, order) do
    line_items =
      order.fulfillments
      |> Enum.reject(&(&1.shipping_method == "in-store"))
      |> Enum.flat_map(& &1.line_items)

    with {:ok, customer} <- BuildParams.Customer.execute(order),
         {:ok, {items, total_price}} <- BuildParams.CheckoutItems.execute(line_items),
         {:ok, shipping_address} <- build_shipping_address(order.shipping_address),
         {:ok, shipping_settings} <- BuildParams.ShippingSettings.execute(line_items) do
      merchant_context = build_merchant_context(order)
      marketing_data = build_marketing_data(order)
      open_text_field = build_open_text_field(order)

      {:ok,
       %{
         params: %{
           items: items,
           clientProfileData: customer,
           shippingData: %{
             address: hd(shipping_address),
             logisticsInfo: shipping_settings
           },
           paymentData: %{
             payments: [build_payment_params(credential, total_price)],
             updateStatus: "updated"
           },
           merchantContextData: merchant_context,
           marketingData: marketing_data,
           openTextField: open_text_field
         },
         total_price: total_price
       }}
    end
  end

  defp build_shipping_address(shipping_address) do
    case shipping_address do
      nil ->
        {:error, "order shipping address required"}

      address ->
        {:ok,
         [
           %{
             receiverName: address.receiver_name,
             addressType: "residential",
             postalCode: address.zip,
             city: address.city_name,
             state: address.state,
             country: "BRA",
             street: address.street,
             number: if(address.number, do: address.number, else: "s/n"),
             neighborhood: address.neighborhood,
             complement: address.complement
           }
         ]}
    end
  end

  defp build_merchant_context(%{staff: staff, location: location})
       when not is_nil(staff) and not is_nil(location) do
    %{
      salesAssociateData: %{
        salesAssociateId: "BB#{staff.external_id}#{location.external_id}"
      }
    }
  end

  defp build_merchant_context(_order), do: %{}

  defp build_open_text_field(%{staff: staff, location: location})
       when not is_nil(staff) and not is_nil(location) do
    %{value: "BB#{staff.external_id}#{location.external_id}"}
  end

  defp build_open_text_field(_order), do: nil

  defp build_marketing_data(order) do
    coupon =
      order.discounts
      |> Enum.filter(fn discount -> discount.type == "coupon" end)
      |> case do
        [first_code | _] -> Map.get(first_code, :value)
        _ -> nil
      end

    marketing_data = %{
      utmSource: "iglu",
      utmMedium: "point-of-sale"
    }

    case coupon do
      nil -> marketing_data
      value -> Map.put(marketing_data, :coupon, value)
    end
  end

  defp build_payment_params(credential, total_price) do
    %{
      paymentSystem: credential.payment_system,
      referenceValue: total_price,
      value: total_price,
      installments: 1
    }
  end

  defp build_payment_information(transaction_id, credential, total_price) do
    [
      %{
        paymentSystem: credential.payment_system,
        installments: 1,
        currencyCode: "BRL",
        value: total_price,
        referenceValue: total_price,
        transaction: %{
          id: transaction_id,
          merchantName: credential.account_name
        }
      }
    ]
  end

  defp match_fulfillments_to_orders(fulfillments, vtex_orders) do
    fulfillments
    |> Enum.reject(&(&1.shipping_method == "in-store"))
    |> Enum.reduce(%{}, fn fulfillment, acc ->
      first_line_item = List.first(fulfillment.line_items)
      find_matching_vtex_order(vtex_orders, first_line_item, fulfillment, acc)
    end)
  end

  defp find_matching_vtex_order(vtex_orders, first_line_item, fulfillment, acc) do
    matching_order =
      Enum.find(vtex_orders, fn order ->
        matches_logistics_info?(order["shippingData"]["logisticsInfo"], first_line_item)
      end)

    case matching_order do
      nil -> acc
      order -> Map.put(acc, fulfillment.id, order["orderId"])
    end
  end

  defp matches_logistics_info?(logistics_info, line_item) do
    Enum.any?(logistics_info, fn info ->
      info["selectedSla"] == line_item.shipping_settings["id"] &&
        info["selectedDeliveryChannel"] == line_item.shipping_settings["deliveryChannel"]
    end)
  end
end
