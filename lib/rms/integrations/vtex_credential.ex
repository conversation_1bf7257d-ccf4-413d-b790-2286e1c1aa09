defmodule Rms.Integrations.VTEXCredential do
  use Ecto.Schema
  import Ecto.Changeset

  schema "vtex_credentials" do
    field :account_name, :string
    field :main_account_name, :string
    field :alias, :string
    field :app_key, Rms.Vault.EncryptedBinary
    field :app_token, Rms.Vault.EncryptedBinary
    field :sales_channel_id, :integer
    field :affiliate_id, :string
    field :payment_system, :integer

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(vtex_credential, attrs) do
    vtex_credential
    |> cast(attrs, [
      :account_name,
      :app_key,
      :app_token,
      :affiliate_id,
      :sales_channel_id,
      :alias,
      :payment_system
    ])
    |> validate_required([
      :account_name,
      :app_key,
      :app_token,
      :affiliate_id,
      :sales_channel_id
    ])
    |> put_main_account_name()
    |> assoc_constraint(:organization)
  end

  defp put_main_account_name(changeset) do
    main_account_name =
      get_field(changeset, :main_account_name) || get_change(changeset, :account_name)

    changeset
    |> put_change(:main_account_name, main_account_name)
    |> validate_required([:main_account_name])
  end
end
