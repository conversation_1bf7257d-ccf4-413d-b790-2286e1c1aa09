defmodule Rms.Integrator do
  @moduledoc """
  Interface for interacting with the Integrator service.
  """

  alias Rms.Integrator.Client

  @integrator_client Application.compile_env(:rms, :integrator_client, Client)

  @doc """
  Fetches a fiscal invoice from the Integrator service.

  ## Parameters
  - organization_token (String): The organization's token for authentication.
  - organization_id (String): The ID of the organization to get the fiscal invoice for.
  - location_id (String): The ID of the location to get the fiscal invoice for.
  - external_id (String): The external ID of the invoice.
  - external_source (String, optional): The source of the external ID.

  ## Returns
  - `{:ok, map()}`: If the request is successful, returns a tuple with `:ok` and the fiscal invoice data.
  - `{:error, any()}`: If the request fails, returns a tuple with `:error` and the error details.

  ## Examples
      iex> Rms.Integrator.fetch_fiscal_invoice("org_token", "12345", "loc1", "inv123")
      {:ok, %{...}}

      iex> Rms.Integrator.fetch_fiscal_invoice("invalid_token", "12345", "loc1", "inv123")
      {:error, :unauthorized}
  """
  def fetch_fiscal_invoice(
        organization_token,
        organization_id,
        location_id,
        external_id,
        external_source \\ nil
      ) do
    @integrator_client.fetch_fiscal_invoice(
      organization_token,
      organization_id,
      location_id,
      external_id,
      external_source
    )
  end
end
