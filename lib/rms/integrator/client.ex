defmodule Rms.Integrator.Client do
  @moduledoc """
  HTTP client for interacting with the Integrator service.
  """

  @behaviour Rms.Integrator.ClientBehaviour

  @impl true
  def fetch_fiscal_invoice(
        organization_token,
        organization_id,
        location_id,
        external_id,
        external_source \\ nil
      ) do
    client(organization_token)
    |> Tesla.get!("/internal/api/fiscal_invoices/:organization_id",
      opts: [path_params: [organization_id: organization_id]],
      query: %{
        location_id: location_id,
        external_id: external_id,
        external_source: external_source
      }
    )
    |> normalize_response()
  end

  @impl true
  def client(organization_token) do
    middleware = [
      {Tesla.Middleware.BaseUrl, base_url()},
      Tesla.Middleware.JSON,
      {Tesla.Middleware.BasicAuth, %{username: "rms-backend", password: integrator_api_key()}},
      {Tesla.Middleware.Headers, [{"x-iglu-api-token", organization_token}]},
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.PathParams
    ]

    Tesla.client(middleware)
  end

  defp normalize_response(%{status: 200, body: body}) do
    {:ok, body}
  end

  defp normalize_response(%{status: 401, body: _body}) do
    {:error, :unauthorized}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: _body}) do
    {:error, :bad_request}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end

  defp base_url do
    Application.get_env(:rms, :integrator_url, "http://localhost:5000")
  end

  defp integrator_api_key do
    Application.get_env(:rms, :integrator_service_api_key)
  end
end
