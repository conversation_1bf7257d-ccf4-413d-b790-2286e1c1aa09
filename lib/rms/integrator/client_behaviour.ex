defmodule Rms.Integrator.ClientBehaviour do
  @type client :: Tesla.Client.t()
  @type error ::
          :bad_request
          | :forbidden
          | :not_found
          | :internal_server_error
          | {:validation_error, map()}
          | :unknown_error

  @type result :: {:ok, map()} | {:error, error()}

  @callback fetch_fiscal_invoice(
              organization_token :: String.t(),
              organization_id :: String.t(),
              location_id :: String.t(),
              external_id :: String.t(),
              external_source :: String.t() | nil
            ) :: result()

  @callback client(organization_token :: String.t()) :: client()
end
