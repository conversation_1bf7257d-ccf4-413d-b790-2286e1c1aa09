defmodule Rms.Messaging.Slack do
  @config Application.compile_env(:rms, __MODULE__)
  @backend_rms_logs_url @config[:backend_rms_logs_url]
  @enabled_envs @config[:enabled_envs]
  @current_env Application.compile_env(:rms, :environment)

  def client do
    middleware = [
      {Tesla.Middleware.BaseUrl, @backend_rms_logs_url},
      Tesla.Middleware.JSON
    ]

    Tesla.client(middleware)
  end

  def send_message(message) when is_binary(message) do
    Task.start(fn ->
      if @current_env in @enabled_envs do
        Tesla.post(client(), "", %{text: message})
      end
    end)
  end
end
