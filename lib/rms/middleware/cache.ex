defmodule Rms.Middleware.Cache do
  require OpenTelemetry.Tracer

  @behaviour Tesla.Middleware
  @config Application.compile_env(:rms, __MODULE__)

  @moduledoc """
  A Tesla middleware for caching requests based on an organization's feature flag settings. It leverages the `Rms.FeatureFlag` module to determine whether a request should be cached for a specific organization.

  ## Usage

  The middleware can be included in the Tesla client middleware stack to automatically handle caching logic based on organizational settings.

  ## Functions

    - `call/2`: This function is the primary entry point for the middleware. It checks if the caching feature is enabled for the given organization and proceeds with the caching logic if applicable.
  """

  def call(env, next, _opts) do
    organization_id = Keyword.get(env.opts, :organization_id)

    if Rms.FeatureFlag.should_cache_request?(organization_id) do
      do_call(env, next, organization_id)
    else
      Tesla.run(env, next)
    end
  end

  def do_call(env, next, organization_id) do
    OpenTelemetry.Tracer.with_span "#{__MODULE__}.call/3" do
      request_idempotent_key = Base.encode64(:crypto.hash(:sha256, env.body))
      operation_name = get_operation_name(env)

      OpenTelemetry.Tracer.set_attribute("organization_id", organization_id)
      OpenTelemetry.Tracer.set_attribute("request_idempotent_key", request_idempotent_key)
      OpenTelemetry.Tracer.set_attribute("operation_name", operation_name)

      case Cachex.get(:rms_global_cache, request_idempotent_key) do
        {:ok, nil} ->
          tap(Tesla.run(env, next), fn response ->
            Cachex.put(:rms_global_cache, request_idempotent_key, response,
              expire: get_ttl(operation_name)
            )
          end)

        {:ok, cached_response} ->
          OpenTelemetry.Tracer.set_attribute("hit_cache", true)
          cached_response
      end
    end
  end

  defp get_operation_name(env) do
    Jason.decode!(env.body)["operationName"]
  rescue
    _ -> nil
  end

  defp get_ttl(operation_name) do
    ttl_config = Keyword.get(@config, :ttl)

    OpenTelemetry.Tracer.set_attribute("operation_name", operation_name)

    if ttl = Map.get(ttl_config, operation_name) do
      OpenTelemetry.Tracer.set_attribute("ttl", ttl)
      ttl
    else
      :timer.seconds(1)
    end
  end
end
