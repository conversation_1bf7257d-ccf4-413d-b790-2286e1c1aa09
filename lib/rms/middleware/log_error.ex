defmodule Tesla.Middleware.LogError do
  @moduledoc """
  Add request body and response body to span on error
  """

  @behaviour Tesla.Middleware

  def call(env, next, _opts) do
    env
    |> Tesla.run(next)
    |> log_if_error_response(env)
  end

  defp log_if_error_response({:ok, %Tesla.Env{status: status} = response_env}, request_env)
       when status >= 400 do
    OpenTelemetry.Tracer.set_attribute(
      "request.body",
      inspect(request_env.body, limit: :infinity)
    )

    OpenTelemetry.Tracer.set_attribute(
      "response.body",
      inspect(response_env.body, limit: :infinity)
    )

    {:ok, response_env}
  end

  defp log_if_error_response(result, _request_env), do: result
end
