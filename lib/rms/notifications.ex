defmodule Rms.Notifications do
  alias Rms.Integrations.OneSignal.Client, as: OneSignalClient
  @one_signal_client Application.compile_env(:rms, :one_signal_client, OneSignalClient)

  alias Rms.Accounts

  def send_push_notification(organization_id, opts) do
    with :ok <- Accounts.validate_staffs_organization(organization_id, opts[:staffs_ids]) do
      @one_signal_client.send_push_notification(Enum.into(opts, %{}))
    end
  end

  def unsafe_send_push_notification(opts) do
    @one_signal_client.send_push_notification(Enum.into(opts, %{}))
  end
end
