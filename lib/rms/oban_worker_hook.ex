defmodule Rms.ObanWorkerHook do
  require Logger
  require OpenTelemetry.Tracer

  # before_new/2 - Extract organization_id and add it to worker metadata
  def before_new(args, opts) do
    metadata = opts[:meta]
    organization_id = extract_organization_id(args, metadata)

    if organization_id do
      opts =
        opts
        |> Keyword.put_new(:meta, %{})
        |> put_in([:meta, :organization_id], organization_id)

      {:ok, args, opts}
    else
      {:ok, args, opts}
    end
  end

  # before_process/1 - Transfer span attributes to Lo<PERSON>
  def before_process(job) do
    # Get organization_id
    organization_id = job.meta["organization_id"]
    # Get workflow_name with default value
    workflow_name = job.meta["workflow_name"] || "unnamed_workflow"

    # Define attributes for Logger and span
    attributes = %{
      "messaging.system" => "oban",
      "messaging.destination" => job.queue,
      "messaging.destination_kind" => "queue",
      "messaging.operation" => "process",
      "messaging.oban.job_id" => job.id,
      "messaging.oban.worker" => job.worker,
      "messaging.oban.priority" => job.priority,
      "messaging.oban.attempt" => job.attempt,
      "messaging.oban.max_attempts" => job.max_attempts,
      "messaging.oban.inserted_at" => format_datetime(job.inserted_at),
      "messaging.oban.scheduled_at" => format_datetime(job.scheduled_at),
      "messaging.oban.workflow_name" => workflow_name,
      "organization_id" => organization_id
    }

    # Add attributes to Logger metadata
    Logger.metadata(attributes)

    # Add organization_id to the current span (if it exists)
    if organization_id do
      OpenTelemetry.Tracer.set_attribute("var.organization_id", organization_id)
    end

    # Add workflow_name to the current span (if it exists)
    OpenTelemetry.Tracer.set_attribute("messaging.oban.workflow_name", workflow_name)

    {:ok, job}
  end

  # after_process/3 - Logs error outputs
  def after_process(state, _job, result) do
    if state in [:error, :discard, :cancel] do
      # Log the error
      log_message = "job failed with state #{state}"

      error_details =
        case result do
          {:error, %{__exception__: true} = exception} ->
            "\nerror: #{Exception.message(exception)}"

          {:error, error} ->
            "\nerror: #{inspect(error)}"

          error when is_exception(error) ->
            "\nerror: #{Exception.message(error)}"

          other ->
            "\nresult: #{inspect(other)}"
        end

      Logger.error(log_message <> error_details)
    end

    :ok
  end

  defp extract_organization_id(_args, %{organization_id: organization_id}),
    do: organization_id

  defp extract_organization_id(_args, %{"organization_id" => organization_id}),
    do: organization_id

  defp extract_organization_id(%{organization_id: organization_id}, _meta), do: organization_id

  defp extract_organization_id(%{"organization_id" => organization_id}, _meta),
    do: organization_id

  defp extract_organization_id(args, opts) when is_map(args) or is_list(args) do
    resource = args[:resource] || args["resource"]
    extract_organization_id(resource, opts)
  end

  defp extract_organization_id(_args, _meta), do: nil

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_iso8601(datetime)
end
