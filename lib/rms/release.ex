defmodule Rms.Release do
  @moduledoc """
  Used for executing DB release tasks when run in production without Mix
  installed.
  """
  @app :rms

  require Logger

  def migrate do
    for repo <- repos() do
      {:ok, _, _} =
        Ecto.Migrator.with_repo(repo, &Ecto.Migrator.run(&1, :up, all: true), mode: :temporary)
    end
  end

  def migrate_data(file_name) do
    for repo <- repos() do
      with {:ok, migration} <- eval_data_migration(repo, file_name),
           {:ok, _, _} <- Ecto.Migrator.with_repo(repo, &migration.run(&1)) do
        Logger.info("A migração de dados foi executada.")
      else
        {:error, message} -> Logger.error(inspect(message))
      end
    end
  end

  defp eval_data_migration(repo, file_name) do
    with file_path <- get_data_migration_path(repo, file_name),
         true <- File.regular?(file_path),
         {{:module, module, _, _}, _} <- Code.eval_file(file_path) do
      {:ok, module}
    else
      false -> {:error, "Não foi possível encontrar a migração de dados."}
      _ -> {:error, "A migração de dados aparenta ser inválida."}
    end
  end

  defp get_data_migration_path(repo, file_name) do
    repo
    |> Ecto.Migrator.migrations_path("data_migrations")
    |> Path.join(file_name)
  end

  def rollback(repo, version) do
    {:ok, _, _} =
      Ecto.Migrator.with_repo(repo, &Ecto.Migrator.run(&1, :down, to: version), mode: :temporary)
  end

  defp repos do
    Application.fetch_env!(@app, :ecto_repos)
  end
end
