defmodule Rms.Repo do
  use Ecto.Repo,
    otp_app: :rms,
    adapter: Ecto.Adapters.Postgres

  use Paginator, maximum_limit: 10_000

  def transaction_with(fun, opts \\ []) when is_function(fun, 0) and is_list(opts) do
    do_transaction_with(fun, opts)
  end

  defp do_transaction_with(fun, opts, retry_count \\ 0) do
    isolation_level = Keyword.get(opts, :isolation_level, :repeatable_read)
    parent_in_transaction? = in_transaction?()

    transaction(
      fn ->
        set_isolation_level(parent_in_transaction?, isolation_level)
        handle_transaction_result(fun.())
      end,
      opts
    )
  rescue
    error in [Postgrex.Error, Ecto.StaleEntryError] ->
      case error do
        error when retry_count + 1 > 1 ->
          reraise error, __STACKTRACE__

        %Postgrex.Error{postgres: %{code: :serialization_failure}} ->
          do_transaction_with(fun, opts, retry_count + 1)

        %Ecto.StaleEntryError{} ->
          do_transaction_with(fun, opts, retry_count + 1)

        _ ->
          reraise error, __STACKTRACE__
      end
  end

  defp set_isolation_level(parent_in_transaction?, isolation_level) do
    if not parent_in_transaction? and not in_test_env?() do
      isolation_levels = %{
        read_committed: "READ COMMITTED",
        repeatable_read: "REPEATABLE READ",
        serializable: "SERIALIZABLE"
      }

      Ecto.Adapters.SQL.query!(
        __MODULE__,
        "SET TRANSACTION ISOLATION LEVEL #{Map.fetch!(isolation_levels, isolation_level)}"
      )
    end
  end

  defp in_test_env? do
    Code.ensure_loaded?(Mix) && Mix.env() == :test
  end

  defp handle_transaction_result({:ok, result}), do: result
  defp handle_transaction_result({:error, reason}), do: rollback(reason)
  defp handle_transaction_result(:ok), do: :ok

  defp handle_transaction_result(other) do
    raise ArgumentError,
          "expected to return {:ok, _} or {:error, _}, got: #{inspect(other)}"
  end

  def inherit_fields(child, parent, fields) do
    Enum.reduce(fields, child, fn field, acc ->
      Map.update!(acc, field, fn _ -> Map.fetch!(parent, field) end)
    end)
  end

  def add_organization_id(child, parent), do: inherit_fields(child, parent, [:organization_id])
end
