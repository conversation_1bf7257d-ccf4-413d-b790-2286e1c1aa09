defmodule Rms.Reports do
  import Ecto.Query

  alias Rms.Commerce.Orders.Order
  alias Rms.Finance.Payment
  alias Rms.Repo

  def retail_transactions_report(organization_id, start_date, end_date, location_id \\ nil) do
    start_datetime =
      start_date
      |> DateTime.new!(~T[03:00:00], "Etc/UTC")
      |> DateTime.truncate(:second)

    end_datetime =
      end_date
      |> Date.add(1)
      |> DateTime.new!(~T[02:59:59], "Etc/UTC")
      |> DateTime.truncate(:second)

    base_query =
      Payment
      |> join(:inner, [p], t in assoc(p, :transaction))
      |> join(:inner, [p, t], o in assoc(t, :order))
      |> join(:left, [p, t, o], l in assoc(o, :location))
      |> join(:left, [p, t, o], s in Rms.Accounts.Staff, on: o.staff_id == s.id)
      |> join(:left, [p, t, o, l, s], ip in Rms.Fiscal.InvoicePayment, on: ip.payment_id == p.id)
      |> join(:left, [p, t, o, l, s, ip], fi in assoc(ip, :fiscal_invoice))
      |> where([p, t, o], p.reference_at >= ^start_datetime and p.reference_at <= ^end_datetime)
      |> where([p, t, o], o.organization_id == ^organization_id)

    query =
      if location_id do
        base_query
        |> where([p, t, o], o.location_id == ^location_id)
      else
        base_query
      end

    query
    |> select([p, t, o, l, s, ip, fi], %{
      order_id: t.order_id,
      external_order: o.name,
      location_name: l.name,
      amount: p.amount,
      method: p.method,
      status: p.status,
      staff_id: o.staff_id,
      staff_name: s.name,
      staff_external_id: s.external_id,
      df_key: fi.df_key,
      installments: fragment("COALESCE(?->>?, '0')", p.metadata, "installments"),
      inserted_at: p.inserted_at,
      reference_at: p.reference_at,
      aut: fragment("COALESCE(?->>?,'')", p.metadata, "aut"),
      nsu: fragment("COALESCE(?->>?,'')", p.metadata, "nsu")
    })
    |> Repo.all()
  end

  def retail_staff_transactions_report(organization_id, start_date, end_date, location_id \\ nil) do
    start_datetime =
      start_date
      |> DateTime.new!(~T[03:00:00], "Etc/UTC")
      |> DateTime.truncate(:second)

    end_datetime =
      end_date
      |> Date.add(1)
      |> DateTime.new!(~T[02:59:59], "Etc/UTC")
      |> DateTime.truncate(:second)

    base_query =
      Payment
      |> join(:inner, [p], t in assoc(p, :transaction))
      |> join(:inner, [p, t], o in assoc(t, :order))
      |> join(:inner, [p, t, o], s in assoc(o, :staff))
      |> join(:left, [p, t, o], l in assoc(o, :location))
      |> where([p, t, o], p.method != "return_credit")
      |> where([p, t, o], o.status not in ["canceled", "canceling", "open"])
      |> where([p, t, o], p.reference_at >= ^start_datetime and p.reference_at <= ^end_datetime)
      |> where([p, t, o], o.organization_id == ^organization_id)

    if location_id do
      base_query
      |> where([p, t, o], o.location_id == ^location_id)
    else
      base_query
    end
    |> group_by([p, t, o, s, l], [s.name, l.name])
    |> select([p, t, o, s, l], %{
      staff_name: s.name,
      location_name: l.name,
      received: sum(p.amount),
      orders:
        fragment(
          "COUNT(DISTINCT CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') THEN ? END)",
          o.status,
          o.id
        ),
      effective_amount:
        sum(
          fragment(
            "CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') AND ? NOT IN ('canceled', 'pending') THEN ? ELSE 0 END",
            o.status,
            p.status,
            p.amount
          )
        ),
      amount: sum(p.amount),
      average:
        fragment(
          "CASE WHEN COUNT(DISTINCT CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') THEN ? END) > 0
          THEN
            SUM(CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') AND ? NOT IN ('canceled', 'pending') THEN ? ELSE 0 END)
            /
            COUNT(DISTINCT CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') THEN ? END)
          ELSE 0 END",
          o.status,
          o.id,
          o.status,
          p.status,
          p.amount,
          o.status,
          o.id
        )
    })
    |> Repo.all()
  end

  def retail_stores_report(organization_id, start_date, end_date, location_id \\ nil) do
    start_datetime =
      start_date
      |> DateTime.new!(~T[03:00:00], "Etc/UTC")
      |> DateTime.truncate(:second)

    end_datetime =
      end_date
      |> Date.add(1)
      |> DateTime.new!(~T[02:59:59], "Etc/UTC")
      |> DateTime.truncate(:second)

    base_query =
      Payment
      |> join(:inner, [p], t in assoc(p, :transaction))
      |> join(:inner, [p, t], o in assoc(t, :order))
      |> join(:left, [p, t, o], l in assoc(o, :location))
      |> where([p, t, o], p.method != "return_credit")
      |> where([p, t, o], p.reference_at >= ^start_datetime and p.reference_at <= ^end_datetime)
      |> where([p, t, o], o.organization_id == ^organization_id)
      |> where([p, t, o], o.status not in ["canceled", "canceling", "open"])

    if location_id do
      base_query |> where([p, t, o, l], l.id == ^location_id)
    else
      base_query
    end
    |> group_by([p, t, o, l], l.name)
    |> select([p, t, o, l], %{
      location_name: l.name,
      orders:
        fragment(
          "COUNT(DISTINCT ?)",
          o.id
        ),
      effective_amount:
        sum(
          fragment(
            "CASE WHEN ? NOT IN ('canceled', 'canceling') AND ? NOT IN ('canceled', 'pending','open') THEN ? ELSE 0 END",
            o.status,
            p.status,
            p.amount
          )
        ),
      amount: sum(p.amount),
      average:
        fragment(
          "CASE WHEN COUNT(DISTINCT ?) > 0
          THEN
            SUM(CASE WHEN ? NOT IN ('canceled', 'canceling', 'open') AND ? NOT IN ('canceled', 'pending') THEN ? ELSE 0 END)
            /
            COUNT(DISTINCT ?)
          ELSE 0 END",
          o.id,
          o.status,
          p.status,
          p.amount,
          o.id
        )
    })
    |> Repo.all()
  end

  def selling_summary(start_datetime, end_datetime, location_id \\ nil, staff_id \\ nil) do
    query = build_base_query(start_datetime, end_datetime, location_id, staff_id)

    total_effective_amount = calculate_effective_amount(query)
    total_sellings = calculate_total_sellings(query)
    total_amount_mean = calculate_total_amount_mean(total_effective_amount, total_sellings)

    %{
      total_amount_sold: calculate_total_amount_sold(query),
      cancelled_amount: calculate_cancelled_amount(query),
      items_per_selling_mean: calculate_items_per_selling_mean(query),
      payment_report_summaries: calculate_payment_summaries(query),
      total_amount_mean: total_amount_mean,
      total_effective_amount: total_effective_amount,
      total_sellings: total_sellings
    }
  end

  def paginated_payments(organization_id, start_datetime, end_datetime, opts \\ []) do
    cursor_after = Keyword.get(opts, :after)
    cursor_before = Keyword.get(opts, :before)

    limit =
      case Keyword.get(opts, :limit) do
        limit when is_binary(limit) -> String.to_integer(limit)
        limit when is_integer(limit) -> limit
        _ -> 20
      end

    base_query =
      from(p in Payment,
        join: t in assoc(p, :transaction),
        join: o in assoc(t, :order),
        where:
          o.status not in ["canceled", "canceling", "open"] and
            p.status not in ["canceled", "pending"],
        where: p.reference_at >= ^start_datetime and p.reference_at <= ^end_datetime,
        where: o.organization_id == ^organization_id,
        select: %{
          id: p.id,
          inserted_at: p.inserted_at,
          reference_at: p.reference_at,
          order_id: o.id,
          amount: type(p.amount, :decimal),
          payment_method: p.method,
          date: p.reference_at,
          authorization_code: fragment("CASE
            WHEN ?->>'aut' IS NOT NULL AND ?->>'aut' != '' THEN ?->>'aut'
            WHEN ?->>'nsu' IS NOT NULL AND ?->>'nsu' != '' THEN ?->>'nsu'
            ELSE NULL
          END", p.metadata, p.metadata, p.metadata, p.metadata, p.metadata, p.metadata),
          mode:
            fragment(
              """
              CASE
                WHEN ? IN ('credit_card', 'debit_card', 'pix') AND ?->>'store_payment_receipt' IS NOT NULL THEN 'iglu'
                WHEN ? = 'payment_link' AND ?->>'link' IS NOT NULL THEN 'iglu'
                WHEN ? IN ('credit_card', 'debit_card', 'pix', 'payment_link') THEN 'manual'
              END
              """,
              p.method,
              p.metadata,
              p.method,
              p.metadata,
              p.method
            )
        },
        order_by: [desc: p.reference_at, desc: p.id]
      )

    query =
      base_query
      |> maybe_filter_by_location(Keyword.get(opts, :location_id))
      |> maybe_filter_by_payment_method(Keyword.get(opts, :payment_method))
      |> maybe_filter_by_staff(Keyword.get(opts, :staff_id))

    paginate_opts = [
      after: cursor_after,
      before: cursor_before,
      limit: limit,
      cursor_fields: [{:reference_at, :desc}, {:id, :desc}]
    ]

    Repo.paginate(query, paginate_opts)
  end

  defp calculate_total_amount_mean(total_effective_amount, total_sellings) do
    if total_sellings > 0,
      do: total_effective_amount / total_sellings,
      else: 0.0
  end

  defp build_base_query(start_datetime, end_datetime, location_id, staff_id) do
    Order
    |> join(:inner, [o], t in assoc(o, :transaction))
    |> join(:inner, [o, t], p in assoc(t, :payments))
    |> where([o, t, p], p.reference_at >= ^start_datetime and p.reference_at <= ^end_datetime)
    |> where([o, t, p], p.method != "return_credit")
    |> maybe_filter_by_location_selling(location_id)
    |> maybe_filter_by_staff_selling(staff_id)
  end

  defp maybe_filter_by_location_selling(query, nil), do: query

  defp maybe_filter_by_location_selling(query, location_id) do
    query |> where([o, t, p], o.location_id == ^location_id)
  end

  defp maybe_filter_by_staff_selling(query, nil), do: query

  defp maybe_filter_by_staff_selling(query, staff_id) do
    query |> where([o, t, p], o.staff_id == ^staff_id)
  end

  defp calculate_total_amount_sold(query) do
    query
    |> where([o, t, p], o.status in ["paid", "returned", "partially_returned"])
    |> group_by([o, t], o.id)
    |> select([o, t, p], %{
      order_id: o.id,
      total_payments: sum(p.amount)
    })
    |> Repo.all()
    |> Enum.reduce(Decimal.new(0), fn %{total_payments: amount}, acc ->
      Decimal.add(amount || Decimal.new(0), acc)
    end)
    |> decimal_to_float()
  end

  defp calculate_effective_amount(query) do
    query
    |> where(
      [o, t, p],
      o.status not in ["canceled", "canceling", "open"] and
        p.status not in ["canceled", "pending"]
    )
    |> group_by([o, t], o.id)
    |> select([o, t, p], %{
      order_id: o.id,
      total_payments: sum(p.amount)
    })
    |> Repo.all()
    |> Enum.reduce(Decimal.new(0), fn %{total_payments: amount}, acc ->
      Decimal.add(amount || Decimal.new(0), acc)
    end)
    |> decimal_to_float()
  end

  defp calculate_cancelled_amount(query) do
    query
    |> where([o, t, p], p.status == "canceled" or o.status in ["canceled", "canceling"])
    |> group_by([o, t], o.id)
    |> select([o, t, p], %{
      order_id: o.id,
      total_payments: sum(p.amount)
    })
    |> Repo.all()
    |> Enum.reduce(Decimal.new(0), fn %{total_payments: amount}, acc ->
      Decimal.add(amount || Decimal.new(0), acc)
    end)
    |> decimal_to_float()
  end

  defp calculate_total_sellings(query) do
    query
    |> where([o], o.status not in ["canceled", "canceling", "open"])
    |> select([o], %{id: o.id})
    |> distinct([o], o.id)
    |> subquery()
    |> select([s], count(s.id))
    |> Repo.one() || 0
  end

  defp calculate_items_per_selling_mean(query) do
    total_sellings = calculate_total_sellings(query)
    total_items = calculate_total_items(query)

    if total_sellings > 0,
      do: total_items / total_sellings,
      else: 0.0
  end

  defp calculate_total_items(query) do
    query
    |> where([o], o.status not in ["canceled", "canceling", "open"])
    |> join(:left, [o, t, p], f in assoc(o, :fulfillments))
    |> join(:left, [o, t, p, f], li in assoc(f, :line_items))
    |> group_by([o], o.id)
    |> select([o, t, p, f, li], %{
      order_id: o.id,
      items_quantity: fragment("COALESCE(SUM(COALESCE(?, 0)), 0)", li.quantity)
    })
    |> Repo.all()
    |> Enum.reduce(0, fn %{items_quantity: qty}, acc ->
      case qty do
        nil -> acc
        qty when is_number(qty) -> qty + acc
      end
    end)
  end

  defp calculate_payment_summaries(query) do
    payment_methods = ["cash", "pix", "debit_card", "payment_link", "credit_card", "gift_card"]

    query
    |> where(
      [o, t, p],
      o.status not in ["canceled", "canceling", "open"] and
        p.status not in ["canceled", "pending"]
    )
    |> group_by([o, t, p], p.method)
    |> select([o, t, p], %{
      type: p.method,
      quantity: count(p.id),
      total_amount: type(sum(p.amount), :decimal)
    })
    |> Repo.all()
    |> Enum.map(fn summary ->
      %{
        type: summary.type,
        quantity: summary.quantity,
        total_amount: decimal_to_float(summary.total_amount)
      }
    end)
    |> then(fn summaries ->
      summaries_map = Map.new(summaries, &{&1.type, &1})

      payment_methods
      |> Enum.map(fn method ->
        Map.get(summaries_map, method, %{
          type: method,
          quantity: 0,
          total_amount: 0.0
        })
      end)
      |> Enum.sort_by(& &1.total_amount, :desc)
    end)
  end

  defp maybe_filter_by_location(query, nil), do: query

  defp maybe_filter_by_location(query, location_id) do
    query |> where([p, t, o], o.location_id == ^location_id)
  end

  defp maybe_filter_by_staff(query, nil), do: query

  defp maybe_filter_by_staff(query, staff_id) do
    query |> where([p, t, o], o.staff_id == ^staff_id)
  end

  defp maybe_filter_by_payment_method(query, nil), do: query
  defp maybe_filter_by_payment_method(query, method), do: where(query, [p], p.method == ^method)

  defp decimal_to_float(nil), do: 0.0
  defp decimal_to_float(decimal) when is_integer(decimal), do: decimal * 1.0
  defp decimal_to_float(%Decimal{} = decimal), do: Decimal.to_float(decimal)
end
