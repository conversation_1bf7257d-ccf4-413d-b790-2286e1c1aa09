defmodule Rms.Reports.SoldItemsReport do
  import Ecto.Query
  alias Rms.Repo
  alias Rms.Finance.Payment

  def reatil_items_report(organization_id, start_date, end_date, query_params) do
    start_datetime =
      start_date
      |> DateTime.new!(~T[03:00:00], "Etc/UTC")
      |> DateTime.truncate(:second)

    end_datetime =
      end_date
      |> Date.add(1)
      |> DateTime.new!(~T[02:59:59], "Etc/UTC")
      |> DateTime.truncate(:second)

    base_query =
      base_query(organization_id, start_datetime, end_datetime, query_params)

    query =
      base_query
      |> group_by(^group_options(query_params["group_option"]))
      |> select(^select_options(query_params["group_option"]))

    Repo.all(query)
  end

  defp base_query(organization_id, start_date, end_date, query_params) do
    Payment
    |> join(:inner, [p], t in assoc(p, :transaction))
    |> join(:inner, [p, t], o in assoc(t, :order))
    |> join(:left, [p, t, o], l in assoc(o, :location))
    |> join(:left, [p, t, o, l], s in assoc(o, :staff))
    |> join(:left, [p, t, o, l, s], f in assoc(o, :fulfillments))
    |> join(:left, [p, t, o, l, s, f], li in assoc(f, :line_items))
    |> where([p, t, o, l, s, f, li], o.status not in ["canceled", "canceling", "open"])
    |> where([p, t, o, l, s, f, li], p.organization_id == ^organization_id)
    |> where(
      [p, t, o, l, s, f, li],
      p.reference_at >= ^start_date and p.reference_at <= ^end_date
    )
    |> where(^filter_options(query_params))
  end

  defp filter_options(params) do
    Enum.reduce(params, dynamic(true), fn
      {key, value}, dynamic when key in ["location_id", :location_id] ->
        dynamic([p, t, o, l, s, f, li], ^dynamic and l.id == ^value)

      {key, value}, dynamic when key in ["staff_id", :staff_id] ->
        dynamic([p, t, o, l, s, f, li], ^dynamic and s.id == ^value)

      {key, value}, dynamic when key in ["sku", :sku] ->
        dynamic([p, t, o, l, s, f, li], ^dynamic and li.sku == ^value)

      {_, _}, dynamic ->
        # Not a where parameter
        dynamic
    end)
  end

  defp group_options(group_option) do
    case group_option do
      "location" ->
        [
          dynamic([p, t, o, l, s, f, li], l.id),
          dynamic([p, t, o, l, s, f, li], l.name)
        ]

      "staff" ->
        [
          dynamic([p, t, o, l, s, f, li], s.id)
        ]

      _ ->
        [
          dynamic([p, t, o, l, s, f, li], li.sku),
          dynamic([p, t, o, l, s, f, li], li.variant_name)
        ]
    end
  end

  defp select_options(group_option) do
    base_fields = %{
      total_quantity: dynamic([p, t, o, l, s, f, li], sum(li.quantity)),
      total_value: dynamic([p, t, o, l, s, f, li], sum(li.quantity * li.list_price))
    }

    extra_fields =
      case group_option do
        "location" ->
          %{
            sku: dynamic([p, t, o, l, s, f, li], "Todos"),
            variant_name: dynamic([p, t, o, l, s, f, li], "Todos"),
            location_name: dynamic([p, t, o, l, s, f, li], l.name),
            staff_name: dynamic([], "Todos")
          }

        "staff" ->
          %{
            sku: dynamic([p, t, o, l, s, f, li], "Todos"),
            variant_name: dynamic([p, t, o, l, s, f, li], "Todos"),
            staff_name: dynamic([p, t, o, l, s, f, li], s.name),
            location_name: dynamic([], "Todas")
          }

        _ ->
          %{
            sku: dynamic([p, t, o, l, s, f, li], li.sku),
            variant_name: dynamic([p, t, o, l, s, f, li], li.variant_name),
            location_name: dynamic([p, t, o, l, s, f, li], "Todas"),
            staff_name: dynamic([], "Todos")
          }
      end

    Map.merge(base_fields, extra_fields)
  end
end
