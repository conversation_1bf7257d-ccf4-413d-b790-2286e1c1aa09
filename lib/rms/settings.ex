defmodule Rms.Settings do
  import Ecto.Query

  alias Rms.Repo
  alias Rms.Settings.LocationSetting
  alias Rms.Settings.OrganizationSetting

  @doc """
  Retrieves an organization setting based on the given `organization_id` and `key`.

  ## Parameters

  - `organization_id`: The ID of the organization for which the setting is being retrieved.
  - `key`: The key identifying the setting.

  ## Returns

  The `OrganizationSetting` struct if the setting is found, otherwise `nil`.

  ## Examples

      iex> Rms.Settings.get_organization_setting(1, "timezone")
      %Rms.Settings.OrganizationSetting{}

      iex> Rms.Settings.get_organization_setting(1, "non_existing_key")
      nil

  """
  def get_organization_setting(organization_id, key) do
    Repo.get_by(OrganizationSetting, organization_id: organization_id, key: key)
  end

  @doc """
  Retrieves a location setting based on the given `organization_id`, `location_id`, and `key`.

  ## Parameters

  - `organization_id`: The ID of the organization associated with the location setting.
  - `location_id`: The ID of the location for which the setting is being retrieved.
  - `key`: The key identifying the setting.

  ## Returns

  The `LocationSetting` struct if the setting is found, otherwise `nil`.

  ## Examples

      iex> Rms.Settings.get_location_setting(1, 2, "temperature_unit")
      %Rms.Settings.LocationSetting{}

      iex> Rms.Settings.get_location_setting(1, 2, "non_existing_key")
      nil

  """
  def get_location_setting(organization_id, location_id, key) do
    Repo.get_by(LocationSetting,
      location_id: location_id,
      organization_id: organization_id,
      key: key
    )
  end

  @doc """
  Creates an organization setting with the given `organization_id`, `key`, and `value`.

  ## Parameters

  - `organization_id`: The ID of the organization for which the setting is being created.
  - `key`: The key identifying the setting.
  - `value`: The value of the setting.

  ## Examples

      iex> Rms.Settings.create_organization_setting(1, "timezone", "UTC")
      {:ok, %Rms.Settings.OrganizationSetting{}}

      iex> Rms.Settings.create_organization_setting(1, "timezone", "UTC")
      {:error, %Ecto.Changeset{}}

  """
  def create_organization_setting(organization_id, key, value) do
    %OrganizationSetting{
      organization_id: organization_id
    }
    |> OrganizationSetting.changeset(%{
      key: key,
      value: %{data: value}
    })
    |> Repo.insert()
  end

  @doc """
  Upserts an organization setting with the given `organization_id`, `key`, and `value`.

  ## Parameters

  - `organization_id`: The ID of the organization for which the setting is being upserted.
  - `key`: The key identifying the setting.
  - `value`: The value of the setting.

  ## Examples

      iex> Rms.Settings.upsert_organization_setting(1, "timezone", "UTC")
      {:ok, %Rms.Settings.OrganizationSetting{}}

      iex> Rms.Settings.upsert_organization_setting(1, "timezone", "UTC")
      {:ok, %Rms.Settings.OrganizationSetting{}}

  """
  def upsert_organization_setting(organization_id, key, value) do
    %OrganizationSetting{organization_id: organization_id}
    |> OrganizationSetting.changeset(%{key: key, value: %{data: value}})
    |> Repo.insert(
      on_conflict: {:replace, [:value]},
      conflict_target: [:organization_id, :key]
    )
  end

  @doc """
  Creates a location setting with the given `location_id`, `key`, and `value`.

  ## Parameters

  - `location_id`: The ID of the location for which the setting is being created.
  - `organization_id`: The ID of the organization associated with the location setting.
  - `key`: The key identifying the setting.
  - `value`: The value of the setting.

  ## Examples

      iex> Rms.Settings.create_location_setting(2, 1, "temperature_unit", "Celsius")
      {:ok, %Rms.Settings.LocationSetting{}}

      iex> Rms.Settings.create_location_setting(2, 123, "temperature_unit", "Celsius")
      {:error, %Ecto.Changeset{}}

  """
  def create_location_setting(location_id, organization_id, key, value) do
    %LocationSetting{
      location_id: location_id,
      organization_id: organization_id
    }
    |> LocationSetting.changeset(%{
      key: key,
      value: %{data: value}
    })
    |> Repo.insert()
  end

  @doc """
  Updates an organization setting for the given `organization_id`, `key`, and `value`.

  ## Parameters

  - `organization_id`: The ID of the organization for which the setting is being updated.
  - `key`: The key identifying the setting to update.
  - `value`: The new value for the setting.

  ## Examples

      iex> Rms.Settings.update_organization_setting(1, "timezone", "EST")
      {:ok, %Rms.Settings.OrganizationSetting{}}

      iex> Rms.Settings.update_organization_setting(1, "non_existing_key", "EST")
      {:error, :not_found}

  """
  def update_organization_setting(organization_id, key, new_value) do
    case get_organization_setting(organization_id, key) do
      nil ->
        {:error, :not_found}

      %OrganizationSetting{} = setting ->
        setting
        |> OrganizationSetting.changeset(%{value: %{data: new_value}})
        |> Repo.update()
    end
  end

  @doc """
  Updates a location setting for the given `location_id`, `key`, and `value`.

  ## Parameters

  - `location_id`: The ID of the location for which the setting is being updated.
  - `key`: The key identifying the setting to update.
  - `value`: The new value for the setting.

  ## Examples

      iex> Rms.Settings.update_location_setting(2, "temperature_unit", "Fahrenheit")
      {:ok, %Rms.Settings.LocationSetting{}}

      iex> Rms.Settings.update_location_setting(2, "non_existing_key", "Fahrenheit")
      {:error, :not_found}

  """
  def update_location_setting(location_id, organization_id, key, new_value) do
    case get_location_setting(location_id, organization_id, key) do
      nil ->
        {:error, :not_found}

      %LocationSetting{} = setting ->
        setting
        |> LocationSetting.changeset(%{value: %{data: new_value}})
        |> Repo.update()
    end
  end

  @doc """
  Deletes a location setting.

  ## Parameters

  - `setting`: The `LocationSetting` to be deleted.

  ## Examples

      iex> Rms.Settings.delete_location_setting(%LocationSetting{})
      {:ok, setting}

      iex> Rms.Settings.delete_location_setting(%LocationSetting{})
      {:error, %Ecto.Changeset{}}

  """
  def delete_location_setting(%LocationSetting{} = setting) do
    Repo.delete(setting)
  end

  @doc """
  Deletes an organization setting.

  ## Parameters

  - `setting`: The `OrganizationSetting` to be deleted.

  ## Examples

      iex> Rms.Settings.delete_organization_setting(%OrganizationSetting{})
      {:ok, setting}

      iex> Rms.Settings.delete_organization_setting(%OrganizationSetting{})
      {:error, %Ecto.Changeset{}}

  """
  def delete_organization_setting(%OrganizationSetting{} = setting) do
    Repo.delete(setting)
  end

  @doc """
  Performs a bulk upsert of location settings.

  ## Parameters

  - `organization_id`: The ID of the organization associated with the location settings.
  - `location_id`: The ID of the location for which the settings are being upserted.
  - `settings`: A map where keys are setting names and values are the setting values.

  ## Examples

      iex> Rms.Settings.bulk_upsert_location_settings(1, 2, %{"timezone" => "UTC", "printer_ip" => "***********"})
      {:ok, [%Rms.Settings.LocationSetting{}, %Rms.Settings.LocationSetting{}]}

      iex> Rms.Settings.bulk_upsert_location_settings(1, 2, %{})
      {:ok, []}

  """
  def bulk_upsert_location_settings(organization_id, location_id, settings)
      when is_map(settings) do
    multi =
      Enum.reduce(settings, Ecto.Multi.new(), fn {key, value}, multi ->
        changeset =
          LocationSetting.changeset(
            %LocationSetting{organization_id: organization_id},
            %{location_id: location_id, key: key, value: %{data: value}}
          )

        Ecto.Multi.insert(
          multi,
          {:upsert, key},
          changeset,
          on_conflict: {:replace, [:value, :updated_at]},
          conflict_target: [:location_id, :key]
        )
      end)

    case Repo.transaction(multi) do
      {:ok, results} ->
        settings = Map.values(results)
        {:ok, Enum.map(settings, &%{key: &1.key, value: &1.value.data, type: "location_setting"})}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def list_settings(organization_id, location_id, requested_fields \\ []) do
    {loc_fields, org_fields} =
      Enum.split_with(requested_fields, &String.starts_with?(&1, "location_"))

    loc_fields = Enum.map(loc_fields, &String.replace_prefix(&1, "location_", ""))

    location_settings = list_location_settings(organization_id, location_id, loc_fields)

    org_fields = Enum.map(org_fields, &String.replace_prefix(&1, "organization_", ""))

    organization_settings = list_organization_settings(organization_id, org_fields)

    location_settings
    |> Enum.concat(organization_settings)
    |> Enum.map(fn
      %LocationSetting{} = ls ->
        %{key: ls.key, value: ls.value["data"], type: "location_setting"}

      %OrganizationSetting{} = os ->
        %{key: os.key, value: os.value["data"], type: "organization_setting"}
    end)
  end

  defp list_organization_settings(organization_id, org_fields) do
    organization_id
    |> organization_settings_query(org_fields)
    |> Repo.all()
  end

  defp list_location_settings(organization_id, location_id, loc_fields) do
    case location_id do
      nil ->
        []

      _ ->
        organization_id
        |> location_settings_query(location_id, loc_fields)
        |> Repo.all()
    end
  end

  defp organization_settings_query(organization_id, fields \\ [])

  defp organization_settings_query(organization_id, []) do
    where(OrganizationSetting, [os], os.organization_id == ^organization_id)
  end

  defp organization_settings_query(organization_id, fields) do
    organization_id
    |> organization_settings_query()
    |> where([ls], ls.key in ^fields)
  end

  defp location_settings_query(organization_id, location_id, fields \\ [])

  defp location_settings_query(organization_id, location_id, []) do
    case location_id do
      nil ->
        LocationSetting
        |> join(:inner, [ls], l in assoc(ls, :location))
        |> join(:inner, [ls, l], o in assoc(l, :organization), on: o.id == ^organization_id)

      location_id ->
        LocationSetting
        |> join(:inner, [ls], l in assoc(ls, :location))
        |> join(:inner, [ls, l], o in assoc(l, :organization), on: o.id == ^organization_id)
        |> where([ls, l, o], l.id == ^location_id)
    end
  end

  defp location_settings_query(organization_id, location_id, fields) do
    organization_id
    |> location_settings_query(location_id)
    |> where([ls], ls.key in ^fields)
  end

  def get_connected_ecommerce(organization_id) do
    case get_organization_setting(organization_id, "ecommerce") do
      %{value: value} when is_map(value) -> value["data"]
      _ -> nil
    end
  end

  @doc """
  Gets a setting from either organization or location settings, giving preference to organization settings.
  Uses a single query with a UNION to efficiently get both settings at once.

  ## Parameters

  - `organization_id`: The ID of the organization.
  - `location_id`: The ID of the location (can be nil).
  - `key`: The key of the setting to retrieve.

  ## Returns

  The value of the setting if found, otherwise nil.

  ## Examples

      iex> Rms.Settings.get_setting(1, 2, "enabled_fulfillments")
      %{"delivery" => true, "local_pickup" => false}

      iex> Rms.Settings.get_setting(1, nil, "enabled_fulfillments")
      %{"delivery" => true, "local_pickup" => true}

      iex> Rms.Settings.get_setting(1, 2, "nonexistent_setting")
      nil
  """
  def get_setting(organization_id, nil, key) do
    case get_organization_setting(organization_id, key) do
      %{value: %{"data" => value}} -> value
      _ -> nil
    end
  end

  def get_setting(organization_id, location_id, key) do
    case get_setting_with_fallback(organization_id, location_id, key) do
      %{value: %{"data" => value}} -> value
      _ -> nil
    end
  end

  defp get_setting_with_fallback(organization_id, location_id, key) do
    with nil <- get_organization_setting(organization_id, key) do
      get_location_setting(organization_id, location_id, key)
    end
  end
end
