defmodule Rms.Settings.LocationSetting do
  use Ecto.Schema
  import Ecto.Changeset

  schema "location_settings" do
    field :value, :map
    field :key, :string

    belongs_to :organization, Rms.Accounts.Organization
    belongs_to :location, Rms.Accounts.Location

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(location_setting, attrs) do
    location_setting
    |> cast(attrs, [:location_id, :key, :value])
    |> validate_required([:key, :value])
    |> validate_value()
    |> foreign_key_constraint(:location_id)
  end

  defp validate_value(changeset) do
    validate_change(changeset, :value, fn _, value ->
      case value do
        %{data: data} when not is_nil(data) -> []
        _ -> [value: "must contain non-nil data"]
      end
    end)
  end
end
