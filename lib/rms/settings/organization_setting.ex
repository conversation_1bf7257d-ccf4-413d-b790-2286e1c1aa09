defmodule Rms.Settings.OrganizationSetting do
  use Ecto.Schema
  import Ecto.Changeset

  schema "organization_settings" do
    field :value, :map
    field :key, :string

    belongs_to :organization, Rms.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(organization_setting, attrs) do
    organization_setting
    |> cast(attrs, [:key, :value])
    |> validate_required([:key])
  end
end
