defmodule Rms.Storage do
  @moduledoc """
  The Storage context.
  Handles file storage operations like generating presigned URLs and managing uploads.
  """

  alias Rms.Repo
  alias Rms.Storage.S3Client
  alias Rms.Storage.UploadEntry

  @storage_client Application.compile_env(:rms, :storage_client, S3Client)

  @doc """
  Generates a presigned URL for uploading a file to S3.

  ## Arguments
    * `organization_id` - Organization ID for the upload (required)

  ## Options
    * `:content_length` - Maximum allowed file size in bytes (default: 10MB)
    * `:expires_in` - URL expiration time in seconds (default: 3600)
    * `:content_type` - Expected content type of the file
    * `:prefix` - Optional prefix for the S3 key
  """
  def generate_presigned_post(organization_id, opts \\ []) do
    content_length = Keyword.get(opts, :content_length, 10 * 1024 * 1024)
    expires_in = Keyword.get(opts, :expires_in, 3600)
    prefix = Keyword.get(opts, :prefix, "uploads")

    s3_key = generate_s3_key(prefix)
    expires_at = DateTime.add(DateTime.utc_now(), expires_in, :second)

    client_opts = [
      content_length: content_length,
      expires_in: expires_in,
      content_type: Keyword.get(opts, :content_type)
    ]

    with {:ok, %{url: url, fields: fields}} <-
           @storage_client.generate_presigned_post(bucket(), s3_key, client_opts),
         {:ok, upload} <-
           create_upload_entry(organization_id, %{
             s3_key: s3_key,
             expires_at: expires_at
           }) do
      {:ok, %{url: url, fields: fields, upload: upload}}
    end
  end

  @doc """
  Creates an upload entry.
  """
  def create_upload_entry(organization_id, attrs) do
    %UploadEntry{organization_id: organization_id}
    |> UploadEntry.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets an upload entry by ID.
  """
  def get_upload_entry!(organization_id, id) do
    Repo.get_by!(UploadEntry, id: id, organization_id: organization_id)
  end

  @doc """
  Updates an upload entry.
  """
  def update_upload_entry(%UploadEntry{} = upload, attrs) do
    upload
    |> UploadEntry.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Marks an upload as uploaded.
  """
  def mark_as_uploaded(%UploadEntry{} = upload) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    upload
    |> UploadEntry.changeset(%{uploaded_at: now})
    |> Repo.update()
  end

  @doc """
  Checks if a file exists in storage.

  Returns `{:ok, true}` if the file exists, `{:ok, false}` if it doesn't,
  or `{:error, reason}` if there was an error checking.
  """
  def exists?(key) do
    @storage_client.exists?(key)
  end

  @doc """
  Generates a unique S3 key for a file.
  """
  def generate_s3_key(prefix) do
    uuid = Ecto.UUID.generate()
    Path.join([prefix, uuid])
  end

  defp bucket do
    Application.fetch_env!(:rms, :upload_bucket)
  end
end
