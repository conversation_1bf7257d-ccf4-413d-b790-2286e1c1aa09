defmodule Rms.Storage.ClientBehaviour do
  @moduledoc """
  Behaviour for storage client implementations.
  """

  @type presigned_url_opts :: [
          content_length: integer(),
          expires_in: integer(),
          content_type: String.t() | nil,
          prefix: String.t()
        ]

  @type presigned_response :: %{
          url: String.t(),
          fields: map()
        }

  @callback generate_presigned_post(
              bucket :: String.t(),
              key :: String.t(),
              opts :: presigned_url_opts()
            ) ::
              {:ok, presigned_response()} | {:error, term()}

  @callback exists?(key :: String.t()) :: {:ok, boolean()} | {:error, term()}
end
