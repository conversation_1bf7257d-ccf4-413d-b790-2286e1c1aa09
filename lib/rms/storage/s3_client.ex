defmodule Rms.Storage.S3Client do
  @moduledoc """
  S3 implementation of the storage client behaviour.
  """

  @behaviour Rms.Storage.ClientBehaviour

  @impl true
  def generate_presigned_post(bucket, key, opts) do
    config = ExAws.Config.new(:s3)
    max_size = Keyword.get(opts, :content_length, 10 * 1024 * 1024)
    expires_in = Keyword.get(opts, :expires_in, 3600)
    content_type = Keyword.get(opts, :content_type)

    conditions = [
      {"bucket", bucket},
      {"key", key}
    ]

    conditions =
      if content_type do
        [{"$Content-Type", content_type} | conditions]
      else
        conditions
      end

    post_opts = [
      expires_in: expires_in,
      conditions: conditions,
      content_length_range: [0, max_size]
    ]

    %{url: url, fields: fields} = ExAws.S3.presigned_post(config, bucket, key, post_opts)
    {:ok, %{url: url, fields: fields}}
  rescue
    error -> {:error, error}
  end

  @impl true
  def exists?(key) do
    bucket = Application.get_env(:rms, :upload_bucket)

    try do
      ExAws.S3.head_object(bucket, key)
      |> ExAws.request()
      |> case do
        {:ok, _} -> {:ok, true}
        {:error, {:http_error, 404, _}} -> {:ok, false}
        {:error, error} -> {:error, error}
      end
    rescue
      error -> {:error, error}
    end
  end
end
