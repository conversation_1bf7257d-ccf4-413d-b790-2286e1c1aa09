defmodule Rms.Storage.UploadEntry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "upload_entries" do
    field :s3_key, :string
    field :status, :string, default: "pending"
    field :expires_at, :utc_datetime
    field :uploaded_at, :utc_datetime
    field :error_messages, {:array, :string}

    belongs_to :organization, Rms.Accounts.Organization

    timestamps()
  end

  @statuses ~w(pending processing cancelled error expired completed)
  @required_fields ~w(s3_key status expires_at organization_id)a

  @allowed_transitions %{
    "pending" => ["processing", "cancelled", "expired"],
    "processing" => ["error", "cancelled", "completed"],
    "cancelled" => [],
    "error" => [],
    "expired" => [],
    "completed" => []
  }

  @doc false
  def changeset(upload_entry, attrs) do
    upload_entry
    |> cast(attrs, [:s3_key, :status, :expires_at, :uploaded_at, :error_messages])
    |> validate_required(@required_fields)
    |> validate_inclusion(:status, @statuses)
    |> validate_status_transition()
    |> unique_constraint(:s3_key)
  end

  @doc false
  def status_changeset(upload_entry, new_status) when new_status in @statuses do
    upload_entry
    |> change(%{status: new_status})
    |> validate_inclusion(:status, @statuses)
    |> validate_status_transition()
  end

  @doc false
  def error_changeset(upload_entry, error_messages) when is_list(error_messages) do
    upload_entry
    |> change(%{error_messages: error_messages, status: "error"})
    |> validate_status_transition()
  end

  defp validate_status_transition(changeset) do
    validate_inclusion(changeset, :status, @allowed_transitions[changeset.data.status] || [])
  end
end
