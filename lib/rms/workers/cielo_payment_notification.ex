defmodule Rms.Workers.CieloPaymentNotification do
  use Oban.Worker, queue: :cielo, max_attempts: 3

  alias Rms.Finance
  alias Rms.Integrations.Cielo

  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "checkout_cielo_order_number" => checkout_cielo_order_number,
          "product_id" => external_payment_reference
        }
      }) do
    with {:ok, payment} <- get_payment(external_payment_reference),
         {:ok, credential} <- Cielo.Token.Create.execute(payment.organization_id, nil),
         {:ok, cielo_payment} <-
           Cielo.get_payment_details(credential.token, checkout_cielo_order_number) do
      update_payment(payment, cielo_payment)
    end
  end

  defp get_payment(external_payment_reference) do
    case Finance.get_payment_by_external_reference("cielo-link", external_payment_reference) do
      {:ok, payment} -> {:ok, payment}
      _ -> {:error, :payment_not_found}
    end
  end

  defp update_payment(payment, %{"payment" => payment_data} = _cielo_payment) do
    metadata = %{
      "nsu" => payment_data["nsu"],
      "aut" => payment_data["authorizationCode"],
      "card_brand" => payment_data["brand"],
      "card_number" => payment_data["cardMaskedNumber"],
      "installments" => payment_data["numberOfPayments"],
      "method" => get_payment_method(payment_data["type"])
    }

    status = get_payment_status(payment_data)

    Finance.sync_payment(payment, %{
      status: status,
      metadata: Map.put(payment.metadata, "payment_details", metadata)
    })
  end

  defp get_payment_method("CreditCard"), do: "credit_card"
  defp get_payment_method("DebitCard"), do: "debit_card"
  defp get_payment_method("Pix"), do: "pix"
  defp get_payment_method(_), do: "credit_card"

  defp get_payment_status(%{"status" => status}) do
    case status do
      "Paid" -> "settled"
      "Denied" -> "canceled"
      _ -> "pending"
    end
  end
end
