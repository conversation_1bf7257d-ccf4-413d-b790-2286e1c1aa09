defmodule Rms.Workers.ConfirmProductTaxUploadWorker do
  @moduledoc """
  Oban worker for confirming product tax upload existence in S3 and managing upload status.
  Handles checking if the file exists in S3 and updating the upload status accordingly.
  """

  use Oban.Pro.Worker,
    queue: :fiscal_invoices,
    max_attempts: 3,
    unique: [period: 30]

  alias Rms.Fiscal
  alias Rms.Storage

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"upload_id" => upload_id, "organization_id" => organization_id}}) do
    upload = Storage.get_upload_entry!(organization_id, upload_id)

    case Fiscal.confirm_product_tax_upload(upload) do
      {:ok, :processing} -> :ok
      {:error, :not_found} -> {:snooze, 5}
      {:error, :expired} -> {:discard, :expired}
      {:error, :invalid_status, status} -> {:discard, "cannot confirm upload in #{status} status"}
      {:error, reason} -> {:error, reason}
    end
  end
end
