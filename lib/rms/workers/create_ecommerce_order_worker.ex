defmodule Rms.Workers.CreateEcommerceOrderWorker do
  use Oban.Pro.Worker

  @preload [
    :shipping_settings,
    order: [:customer],
    line_items: [product_variant: [:product_variant_mappings]]
  ]

  @impl Oban.Pro.Worker
  def process(%{args: %{"fulfillment_id" => id, "organization_id" => organization_id}}) do
    organization_id
    |> Rms.Commerce.Fulfillments.get_fulfillment!(id)
    |> Rms.Repo.preload(@preload)
    |> issue_order()
  end

  defp issue_order(%{ecommerce: "vtex"} = fulfillment) do
    Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
  end

  defp issue_order(_fulfillment), do: {:ok, :created}
end
