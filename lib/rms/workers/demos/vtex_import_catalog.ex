defmodule Rms.Workers.Demos.VTEXImportCatalog do
  use Oban.Pro.Worker, queue: :vtex
  require Logger

  alias Rms.Repo
  alias Rms.Integrations.VTEX
  alias Rms.Integrations.ProductVariantMapping

  def process(%Oban.Job{args: args}) do
    organization_id = args["organization_id"]
    from = args["from"] || 1
    to = args["to"] || 50

    with {:ok, data} <- list_catalog(organization_id, from, to) do
      product_count = Enum.count(data)

      Enum.each(data, fn product ->
        formatted_product = create_product(product)

        if formatted_product["product_variants"] == [] do
          Logger.info("Product has no variants, skipping")
        else
          insert_product_and_mapping(organization_id, formatted_product)
        end
      end)

      next_job(organization_id, from, product_count)
    end
  end

  defp insert_product_and_mapping(organization_id, formatted_product) do
    with {:ok, product} <-
           Rms.Commerce.Products.create_product(organization_id, formatted_product,
             source: "vtex"
           ) do
      Enum.each(product.product_variants, fn product_variant ->
        external_id =
          (Enum.find(formatted_product["product_variants"], fn pv ->
             pv["sku"] == product_variant.sku
           end) || %{"sku_id" => product_variant.sku})
          |> Map.get("sku_id")

        %ProductVariantMapping{}
        |> ProductVariantMapping.changeset(%{
          organization_id: product_variant.organization_id,
          product_variant_id: product_variant.id,
          source: "vtex",
          external_id: external_id
        })
        |> Repo.insert(
          conflict_target: [:organization_id, :external_id, :source],
          on_conflict: {:replace, [:updated_at]}
        )
      end)
    end
  end

  defp create_product(product) do
    %{
      "name" => product["productName"],
      "product_type" => "item",
      "product_variants" => create_product_variants(product["items"])
    }
  end

  defp create_product_variants(product_variants) do
    Enum.map(product_variants, &create_product_variant/1)
    |> Enum.reject(&is_nil/1)
  end

  defp create_product_variant(product_variant) do
    variations = product_variant["variations"]
    [sku_info] = product_variant["referenceId"]

    seller_info =
      case product_variant["sellers"] do
        [] -> nil
        [seller | _] -> seller
      end

    cond do
      is_nil(variations) ->
        nil

      is_nil(seller_info) ->
        nil

      true ->
        %{
          "name" => product_variant["name"],
          "list_price" => seller_info["commertialOffer"]["Price"],
          "sku" => sku_info["Value"],
          "sku_id" => product_variant["itemId"],
          "bar_code" => product_variant["ean"],
          "image_urls" => Enum.map(product_variant["images"], & &1["imageUrl"]),
          "variation_types" => Enum.map(variations, &%{key: &1, value: hd(product_variant[&1])})
        }
    end
  end

  defp next_job(_, _, 0), do: :ok

  defp next_job(organization_id, from, product_count) do
    new(%{
      "organization_id" => organization_id,
      "from" => from + product_count,
      "to" => from + product_count + 50 - 1
    })
    |> Oban.insert()

    :ok
  end

  defp list_catalog(organization_id, from, to) do
    vtex_credential = Rms.Demos.get_vtex_credential!(organization_id)

    client =
      vtex_credential
      |> VTEX.client()
      |> Tesla.Client.middleware()
      |> Enum.reject(fn
        {Tesla.Middleware.Query, [sc: _]} -> true
        {Tesla.Middleware.Headers, [_]} -> true
        _ -> false
      end)
      |> Tesla.client()

    VTEX.Client.catalog_search(client, from, to)
  end
end
