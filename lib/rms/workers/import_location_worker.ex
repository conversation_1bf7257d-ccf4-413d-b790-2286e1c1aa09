defmodule Rms.Workers.ImportLocationWorker do
  use Oban.Pro.Worker

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{"organization_id" => organization_id, "location_id" => location_id}
      }) do
    ecommerce = Rms.Settings.get_connected_ecommerce(organization_id)
    import_location(organization_id, location_id, ecommerce)
  end

  defp import_location(organization_id, location_id, "shopify") do
    Rms.Integrations.Shopify.Locations.Import.execute(organization_id, location_id)
  end

  defp import_location(_organization_id, _location_id, _) do
    {:discard, "discard"}
  end
end
