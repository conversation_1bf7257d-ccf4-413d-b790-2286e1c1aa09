defmodule Rms.Workers.InvoiceIssuerWorker do
  alias Rms.Fiscal.FiscalInvoice
  use Oban.Pro.Worker, queue: :pagarme

  alias Rms.Fiscal

  require OpenTelemetry.Tracer
  require Logger

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{"fiscal_invoice_id" => fiscal_invoice_id, "organization_id" => organization_id}
      }) do
    OpenTelemetry.Tracer.set_attributes([
      {"var.fiscal_invoice_id", fiscal_invoice_id},
      {"var.organization_id", organization_id}
    ])

    with %FiscalInvoice{} = fiscal_invoice <-
           Fiscal.get_fiscal_invoice!(organization_id, fiscal_invoice_id),
         %FiscalInvoice{} = invoice <-
           Rms.Integrations.Vinco.IssueFiscalInvoice.execute(fiscal_invoice) do
      Rms.Events.emit("invoice.issued", %{
        id: invoice.id,
        invoice: invoice_encoder(invoice),
        organization_id: invoice.organization_id
      })

      {:ok, invoice}
    else
      {:error, reason} ->
        Logger.error(
          "worker failed to issue invoice",
          fiscal_invoice_id: fiscal_invoice_id,
          organization_id: organization_id,
          error_detail: inspect(reason)
        )

        {:error, reason}
    end
  end

  defp invoice_encoder(invoice) do
    RmsWeb.Fiscal.FiscalInvoiceJSON.render("raw_show.json", %{invoice: invoice})
  end
end
