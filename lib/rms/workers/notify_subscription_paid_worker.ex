defmodule Rms.Workers.NotifySubscriptionPaidWorker do
  use Oban.Pro.Workers.Workflow, queue: :vtex, max_attempts: 1

  @impl true
  def process(%{
        args: %{"organization_id" => organization_id, "order_id" => order_id}
      }) do
    order =
      Rms.Commerce.Orders.get_order!(organization_id, order_id) |> Rms.Repo.preload([:staff])

    with {:ok, params} <- validate_params(order) do
      Enum.map(params, fn param ->
        Rms.Events.emit("subscription.paid", param)
      end)
    end
  end

  defp validate_params(%{
         staff: %{
           id: staff_id,
           external_id: staff_external_id
         },
         addons: addons
       }) do
    addons
    |> Enum.reduce_while({:ok, []}, fn
      %{
        metadata: %{
          "sku_id" => sku_id,
          "external_order_id" => external_order_id
        }
      },
      {:ok, acc} ->
        params = %{
          "vtex_order_id" => external_order_id,
          "staff_id" => staff_id,
          "sku_id" => sku_id,
          "document" => staff_external_id
        }

        {:cont, {:ok, [params | acc]}}

      _, {:ok, _acc} ->
        {:halt, {:error, "error"}}
    end)
  end

  defp validate_params(_order) do
    {:error, "order not found"}
  end
end
