defmodule Rms.Workers.PagarMeWorker do
  use Oban.Pro.Worker, queue: :pagarme

  alias Rms.Integrations.PagarMe.UpdatePayment

  @update_payment_events ~w/order.paid order.canceled/

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "payload" => %{"type" => type, "data" => pagarme_order_data} = _payload,
          "org_id" => org_id
        }
      })
      when type in @update_payment_events do
    case UpdatePayment.execute(org_id, pagarme_order_data) do
      {:ok, _} ->
        {:ok, :updated_payment}

      {:error, :not_found} ->
        {:discard, :not_found}

      {:error, {:invalid_status, status}} ->
        {:discard, {:invalid_status, status}}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:error, changeset}

      {:error, reason} ->
        {:error, reason}
    end
  end

  def process(_job) do
    {:discard, :invalid_args}
  end
end
