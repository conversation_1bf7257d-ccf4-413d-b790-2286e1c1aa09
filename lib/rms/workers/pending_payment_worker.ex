defmodule Rms.Workers.PendingPaymentWorker do
  use Oban.Pro.Worker, queue: :polling

  import Ecto.Query

  alias Rms.Integrations.ExternalPaymentReference
  alias Rms.Repo
  alias Rms.Workers.PollingWorker

  @impl Oban.Pro.Worker
  def process(_) do
    pending_payments()
    |> Enum.map(&PollingWorker.new(&1))
    |> Oban.insert_all()

    :ok
  end

  defp pending_payments do
    current_time = DateTime.utc_now()
    two_days_ago = DateTime.add(current_time, -2, :day)

    query =
      ExternalPaymentReference
      |> join(:inner, [epr], p in assoc(epr, :payment))
      |> where([epr, p], p.updated_at >= ^two_days_ago)
      |> where([epr, p], p.status not in ^["canceled", "settled"])
      |> select([epr, p], %{
        organization_id: epr.organization_id,
        external_id: epr.external_id,
        partner: epr.partner,
        entity: "payment"
      })

    Repo.all(query)
  end
end
