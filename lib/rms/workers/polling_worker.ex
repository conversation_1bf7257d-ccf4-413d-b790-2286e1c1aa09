defmodule Rms.Workers.PollingWorker do
  use Oban.Pro.Worker,
    queue: :polling,
    unique: [states: [:available, :scheduled, :executing, :retryable], fields: [:args, :worker]],
    max_attempts: 3

  alias Rms.Finance
  alias Rms.Integrations
  alias Rms.Integrations.Cielo.PaymentLink.Update, as: CieloUpdatePayment
  alias Rms.Integrations.PagarMe
  alias Rms.Integrations.PagarMe.UpdatePayment, as: PagarMeUpdatePayment
  alias Rms.Integrations.PagarMeCredential
  alias Rms.Repo

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"external_id" => external_id, "partner" => "pagarme"} = args}) do
    organization_id = args["organization_id"]

    with {:ok, pagarme_api_response} <- get_pagarme_order(organization_id, external_id),
         {:ok, _} <- PagarMeUpdatePayment.execute(organization_id, pagarme_api_response) do
      {:ok, :updated_payment}
    else
      {:error, :not_found} ->
        {:discard, :payment_not_found}

      {:error, {:invalid_status, _internal_status} = reason} ->
        {:discard, reason}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:error, changeset}

      {:error, other_update_reason} ->
        {:error, {:update_payment_failed, other_update_reason}}
    end
  end

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: %{"external_id" => _external_id, "partner" => "cielo-link"} = args}) do
    case CieloUpdatePayment.execute(args) do
      {:ok, _} -> {:ok, :updated_payment}
      {:error, :not_found} -> {:discard, :payment_not_found}
      {:error, :no_paid_orders} -> {:discard, :no_paid_orders}
      {:error, reason} -> {:error, {:update_payment_failed, reason}}
    end
  end

  @impl Oban.Pro.Worker
  def process(_) do
    :ok
  end

  defp get_pagarme_order(organization_id, external_id) do
    with {:ok, payment_from_db} <-
           Finance.get_payment_by_external_reference_and_organization(
             organization_id,
             "pagarme",
             external_id
           ),
         payment = Repo.preload(payment_from_db, transaction: [:order]),
         loc_id = payment.transaction.order.location_id,
         %PagarMeCredential{credential: credential} <-
           Integrations.get_pagarme_credential!(organization_id, loc_id) do
      pagarme_client = PagarMe.client(credential)
      PagarMe.get_order(pagarme_client, external_id)
    end
  end
end
