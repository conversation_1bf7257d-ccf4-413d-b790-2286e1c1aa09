defmodule Rms.Workers.ProcessProductTaxUpload do
  @moduledoc """
  Oban worker for processing product tax CSV uploads.
  <PERSON>les downloading the file from S3, parsing the CSV, and creating/updating product taxes.
  """

  use Oban.Worker,
    queue: :fiscal_invoices,
    max_attempts: 3,
    unique: [period: 30]

  alias Rms.Fiscal
  alias Rms.Fiscal.ProductTaxCSV
  alias Rms.Storage.UploadEntry
  alias Rms.Repo

  require Logger

  @chunk_size 1000
  # 1MB
  @s3_chunk_size 1_048_576
  @s3_concurrency 8
  # 1 minute
  @s3_timeout 60_000

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"upload_id" => upload_id}}) do
    upload = Repo.get!(UploadEntry, upload_id)

    # Only process uploads in pending state
    case upload.status do
      status when status in ~w(pending processing) -> process_upload(upload)
      status -> {:discard, "cannot process upload in #{status} status"}
    end
  end

  defp process_upload(upload) do
    # Mark as processing
    upload = update_upload_status!(upload, "processing")
    stream = generate_s3_stream(upload.s3_key)

    case ProductTaxCSV.parse_stream(stream, upload.organization_id) do
      {:ok, parsed_stream} ->
        process_parsed_stream(upload, parsed_stream)

      {:error, reason} = error ->
        handle_error(upload, reason)
        error
    end
  end

  defp process_parsed_stream(upload, parsed_stream) do
    parsed_stream
    |> Stream.chunk_every(@chunk_size)
    |> Stream.map(&Enum.to_list/1)
    |> Enum.reduce_while({:ok, {0, []}}, &process_chunk(&1, &2))
    |> handle_processing_result(upload)
  end

  defp handle_processing_result({:ok, {total_count, errors}}, upload) do
    error_count = length(errors)

    Logger.info("finished processing product tax upload",
      upload_id: upload.id,
      successful_records: total_count,
      failed_records: error_count,
      first_error: if(error_count > 0, do: List.first(errors))
    )

    if error_count > 0 do
      error_messages = format_error_messages(errors)
      update_upload_status!(upload, "error", error_messages)
    else
      update_upload_status!(upload, "completed")
    end

    {:ok, total_count}
  end

  defp handle_processing_result({:error, reason} = error, upload) do
    handle_error(upload, reason)
    error
  end

  defp format_error_messages(errors) do
    Enum.map(errors, fn {data, errors} ->
      line = data["_line_number"]

      error_details =
        Enum.map_join(errors, ", ", fn {field, [msg]} ->
          "#{field} #{msg}"
        end)

      "Line #{line}: #{error_details}"
    end)
  end

  defp handle_error(upload, reason) do
    Logger.error("failed to process product tax upload",
      upload_id: upload.id,
      error: inspect(reason)
    )

    update_upload_status!(upload, "error", [reason])
  end

  # Process each chunk of records, accumulating the total count and errors
  defp process_chunk(chunk, {:ok, {acc_count, acc_errors}}) do
    result = Fiscal.partial_create_product_taxes(chunk)

    # Log any errors for debugging
    unless Enum.empty?(result.errors) do
      Logger.warning("some product taxes failed to process",
        error_count: length(result.errors),
        first_error: List.first(result.errors)
      )
    end

    # Continue processing even with errors, but accumulate them
    {:cont, {:ok, {acc_count + result.success, acc_errors ++ result.errors}}}
  end

  # Updates upload status and related fields
  defp update_upload_status!(upload, status) do
    upload
    |> UploadEntry.status_changeset(status)
    |> Repo.update!()
  end

  defp update_upload_status!(upload, "error", error_messages) do
    upload
    |> UploadEntry.error_changeset(error_messages)
    |> Repo.update!()
  end

  # Generate a stream from S3 that reads line by line
  defp generate_s3_stream(s3_key) do
    bucket()
    |> ExAws.S3.download_file(
      s3_key,
      :memory,
      chunk_size: @s3_chunk_size,
      concurrency: @s3_concurrency,
      timeout: @s3_timeout
    )
    |> ExAws.stream!()
    |> Stream.chunk_while("", &chunk_fun/2, &after_fun/1)
    |> Stream.concat()
  end

  # Process chunks into lines
  defp chunk_fun(chunk, acc) do
    to_process = acc <> chunk
    {elements, remaining} = chunk_by_newline(to_process)
    {:cont, elements, remaining}
  end

  defp chunk_by_newline(string, elements \\ [], offset \\ 0) do
    case :binary.match(string, "\n", scope: {offset, byte_size(string) - offset}) do
      {newline_offset, _} ->
        line = binary_part(string, offset, newline_offset - offset + 1)
        chunk_by_newline(string, [line | elements], newline_offset + 1)

      :nomatch ->
        remaining = binary_part(string, offset, byte_size(string) - offset)
        {Enum.reverse(elements), remaining}
    end
  end

  # Handle any remaining data after the stream ends
  defp after_fun(""), do: {:cont, []}
  defp after_fun(remaining), do: {:cont, [remaining <> "\n"], []}

  defp bucket do
    Application.fetch_env!(:rms, :upload_bucket)
  end
end
