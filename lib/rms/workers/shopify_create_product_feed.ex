defmodule Rms.Workers.ShopifyCreateProductFeed do
  require Logger
  use Oban.Pro.Worker, queue: :shopify

  alias Rms.Integrations
  alias Rms.Integrations.Shopify

  @already_exists_error "Country and language codes already exist as a product feed for this shop."
  @unsupported_context_error "The shop doesn't support this country and language context."

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: args}) do
    organization_id = args["organization_id"]

    shopify_credential = Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, product_feed} <- create_product_feed(shopify_client) do
      sync_product_feed(shopify_client, product_feed)
    end
  end

  defp create_product_feed(client, attempt \\ 1) do
    input = input_by_attempt(attempt)

    with {:ok, response} <- Shopify.create_product_feed(client, input) do
      product_feed_id = get_in(response, ["data", "productFeedCreate", "productFeed", "id"])

      user_errors =
        get_in(response, ["data", "productFeedCreate", "userErrors", Access.all(), "message"])

      cond do
        product_feed_id ->
          {:ok, product_feed_id}

        @already_exists_error in user_errors ->
          {:ok, {:already_exists, input}}

        @unsupported_context_error in user_errors ->
          create_product_feed(client, 2)

        true ->
          Logger.error("unkown errors: #{inspect(user_errors)}")

          {:error, user_errors}
      end
    end
  end

  defp input_by_attempt(1), do: %{language: "PT_BR", country: "BR"}
  defp input_by_attempt(_), do: %{language: "EN", country: "BR"}

  defp sync_product_feed(client, {:already_exists, input}) do
    sync_existing_product_feed(client, input)
  end

  defp sync_product_feed(client, product_feed_id) do
    run_full_sync(client, product_feed_id)
  end

  defp sync_existing_product_feed(client, input) do
    with {:ok, feeds} <- Shopify.get_product_feeds(client),
         product_feed_id when is_binary(product_feed_id) <- find_feed_id(feeds, input) do
      run_full_sync(client, product_feed_id)
    end
  end

  defp find_feed_id(feeds, input) do
    Enum.find_value(feeds["data"]["productFeeds"]["nodes"], fn feed ->
      if feed["language"] == input.language and feed["country"] == input.country do
        feed["id"]
      else
        nil
      end
    end)
  end

  defp run_full_sync(client, id) do
    Shopify.run_product_full_sync(client, id)
  end
end
