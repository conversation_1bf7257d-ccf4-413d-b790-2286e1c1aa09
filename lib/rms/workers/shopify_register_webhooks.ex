defmodule Rms.Workers.ShopifyRegisterWebhooks do
  use Oban.Pro.Worker, queue: :shopify

  @impl Oban.Pro.Worker
  def process(%Oban.Job{args: args}) do
    organization_id = args["organization_id"]
    topic = args["topic"]

    shopify_credential = Rms.Integrations.get_shopify_credential!(organization_id)

    shopify_client =
      Rms.Integrations.Shopify.client(shopify_credential.shop, shopify_credential.credential)

    with {:ok, response} <-
           Rms.Integrations.Shopify.create_webhook(shopify_client, topic, %{
             callbackUrl: callback_url!(organization_id)
           }) do
      user_errors =
        get_in(response, [
          "data",
          "webhookSubscriptionCreate",
          "userErrors",
          Access.all(),
          "message"
        ])

      cond do
        Enum.empty?(user_errors) -> :ok
        "Address for this topic has already been taken" in user_errors -> :ok
        true -> {:error, user_errors}
      end
    end
  end

  defp callback_url!(organization_id) do
    base_url =
      :rms
      |> Application.get_env(RmsWeb.Endpoint)
      |> get_in([:url])

    host = Keyword.get(base_url, :host)

    if base_url == [] || host == nil do
      raise "host not properly set in configuration, got #{inspect(base_url)}"
    end

    schema = Keyword.get(base_url, :schema, "https")
    port = Keyword.get(base_url, :port, 443)

    host = "#{schema}://#{host}:#{port}"
    host <> "/webhooks/shopify?organization_id=#{organization_id}"
  end
end
