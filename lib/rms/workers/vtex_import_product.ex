defmodule Rms.Workers.VTEXImportProduct do
  use Oban.Pro.Worker, queue: :vtex

  alias Rms.Integrations.VTEX
  alias Rms.Workers.VTEXImportProductVariant

  def process(%Oban.Job{args: args}) do
    organization_id = args["organization_id"]
    from = args["from"] || 1
    to = args["to"] || 20
    affiliate_id = args["affiliate_id"]
    sales_channel_id = args["sales_channel_id"]

    with {:ok, data} <- list_product_and_sku(organization_id, from, to) do
      jobs =
        Enum.flat_map(data["data"], fn {_product_id, skus} ->
          Enum.map(skus, fn sku_id ->
            VTEXImportProductVariant.new(%{
              "sku_id" => sku_id,
              "organization_id" => organization_id,
              "product_sync_id" => args["product_sync_id"],
              "sales_channel_id" => sales_channel_id,
              "affiliate_id" => affiliate_id
            })
          end)
        end)

      additional_jobs =
        generate_additional_jobs(
          organization_id,
          affiliate_id,
          sales_channel_id,
          args["product_sync_id"],
          from,
          to,
          data["range"]["total"]
        )

      Oban.insert_all(jobs ++ additional_jobs)

      :ok
    end
  end

  defp generate_additional_jobs(
         organization_id,
         affiliate_id,
         sales_channel_id,
         product_sync_id,
         1,
         page_size,
         total
       ) do
    pages = (page_size + 1)..total//page_size

    Enum.map(pages, fn page_start ->
      new(%{
        "organization_id" => organization_id,
        "from" => page_start,
        "to" => min(total, page_start + page_size - 1),
        "affiliate_id" => affiliate_id,
        "sales_channel_id" => sales_channel_id,
        "product_sync_id" => product_sync_id
      })
    end)
  end

  defp generate_additional_jobs(_organization_id, _, _, _, _, _, _total), do: []

  defp list_product_and_sku(organization_id, from, to) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    client = VTEX.client(vtex_credential)

    VTEX.list_product_and_sku(client, from, to)
  end
end
