defmodule Rms.Workers.VTEXImportProductVariant do
  alias Rms.Integrations.AddonMapping
  alias Rms.Commerce.Products.Addon
  use Oban.Pro.Worker, queue: :vtex_product_change_notification, max_attempts: 3

  require Logger

  import Ecto.Query

  alias Ecto.Multi

  alias Rms.Repo
  alias Rms.Commerce.Products.ProductVariant
  alias Rms.Integrations.ProductVariantMapping
  alias Rms.Integrations.VTEX

  @impl Oban.Pro.Worker
  def process(%Oban.Job{
        args: %{
          "organization_id" => organization_id,
          "sku_id" => sku_id,
          "sales_channel_id" => sales_channel_id,
          "affiliate_id" => affiliate_id,
          "product_sync_id" => product_sync_id
        }
      }) do
    product_sync = Rms.Integrations.get_product_sync!(product_sync_id, preloads: [:organization])
    organization = product_sync.organization

    with {:ok, sku_data, sku_price, sku_list_price, simulation_data} <-
           get_sku_by_id(organization_id, affiliate_id, sku_id),
         true <- handle_product?(sku_data, sales_channel_id) do
      sku_data_import(
        sku_data,
        sku_price,
        sku_list_price,
        product_sync,
        organization,
        simulation_data: simulation_data
      )
    end
  end

  defp sku_data_import(
         sku_data,
         sku_price,
         sku_list_price,
         product_sync,
         organization,
         opts
       ) do
    sku_type = sku_type(sku_data["Attachments"])

    case sku_type do
      "subscription" ->
        subscription_importer(
          sku_data,
          sku_price,
          sku_list_price,
          product_sync,
          organization.id
        )

      _ ->
        product_importer(
          product_sync,
          organization,
          sku_data,
          sku_price,
          sku_list_price,
          opts
        )
    end
  end

  defp sku_type(nil) do
    "product"
  end

  defp sku_type([]) do
    "product"
  end

  defp sku_type(attachments) do
    if Enum.any?(attachments, &subscription?/1) do
      "subscription"
    else
      "product"
    end
  end

  defp subscription?(%{"Name" => "vtex.subscription." <> _, "IsActive" => true}), do: true
  defp subscription?(_), do: false

  defp get_sku_by_id(organization_id, affiliate_id, sku_id) do
    vtex_credential = Rms.Integrations.get_vtex_credential!(organization_id)

    client = VTEX.client(vtex_credential)

    with {:ok, sku_data} <- VTEX.get_sku_by_id(client, sku_id),
         {:ok, simulation} <-
           VTEX.private_simulate_fulfillment(client, affiliate_id, %{
             items: [%{id: sku_id, quantity: 1, seller: 1}]
           }) do
      selling_price = get_in(simulation, ["items", Access.at(0), "sellingPrice"])
      list_price = get_in(simulation, ["items", Access.at(0), "listPrice"])

      {:ok, sku_data, selling_price, list_price, simulation}
    end
  end

  defp handle_product?(sku_data, sales_channel_id) do
    if(sales_channel_id in sku_data["SalesChannels"],
      do: true,
      else: {:discard, :product_does_not_belong_to_trade_policy}
    )
  end

  defp product_importer(
         product_sync,
         organization,
         sku_data,
         sku_price,
         sku_list_price,
         opts
       ) do
    product_variant_mapping =
      Rms.Integrations.get_product_variant_mapping(
        organization.id,
        "vtex",
        inspect(sku_data["Id"])
      )

    cond do
      inactive_product?(product_variant_mapping, sku_data) ->
        {:ok, :inactive_product}

      not sku_data["IsActive"] ->
        Rms.Commerce.Products.archive_product_variant(product_variant_mapping.product_variant)

      true ->
        case handle_product_search(organization, sku_data) do
          {:ok, product} ->
            import_vtex_product(
              product_sync,
              product,
              organization,
              sku_data,
              sku_price,
              sku_list_price,
              opts
            )

          error ->
            error
        end
    end
  end

  defp subscription_importer(
         sku_data,
         sku_price,
         sku_list_price,
         product_sync,
         organization_id
       ) do
    addon_mapping =
      Rms.Integrations.get_addon_mapping(
        organization_id,
        "vtex",
        inspect(sku_data["Id"])
      )

    cond do
      inactive_product?(addon_mapping, sku_data) ->
        {:ok, :inactive_product}

      not sku_data["IsActive"] ->
        Rms.Commerce.Products.archive_product_variant(addon_mapping.addon)

      true ->
        import_vtex_addon(
          product_sync,
          addon_mapping,
          organization_id,
          sku_data,
          sku_price,
          sku_list_price
        )
    end
  end

  defp import_vtex_product(
         product_sync,
         product,
         organization,
         sku_data,
         sku_price,
         sku_list_price,
         opts
       ) do
    Multi.new()
    |> Multi.run(:product, fn _repo, _changes -> {:ok, product} end)
    |> create_product_variant(product_sync, sku_data, sku_price, sku_list_price)
    |> upsert_product_variant_mapping(organization, to_string(sku_data["Id"]), opts)
    |> Repo.transaction()
    |> case do
      {:error, multi_name, error, changes} -> {:error, {multi_name, error, changes}}
      {:ok, product} -> {:ok, product}
    end
  end

  defp import_vtex_addon(
         product_sync,
         addon_mapping,
         organization_id,
         sku_data,
         sku_price,
         sku_list_price
       ) do
    Multi.new()
    |> Multi.run(:product, fn _repo, _changes ->
      if product_mapping =
           Rms.Integrations.get_product_mapping(
             organization_id,
             "vtex",
             inspect(sku_data["ProductId"])
           ) do
        ensure_product_name_updated(product_mapping.product, sku_data)
      else
        product_attrs = %{
          name: sku_data["ProductName"]
        }

        with {:ok, product} <-
               Rms.Commerce.Products.create_product(organization_id, product_attrs,
                 source: "vtex"
               ),
             {:ok, _mapping} <-
               Rms.Integrations.create_product_sync_mapping(organization_id, %{
                 organization_id: organization_id,
                 external_id: inspect(sku_data["ProductId"]),
                 source: "vtex",
                 product_id: product.id
               }) do
          {:ok, product}
        end
      end
    end)
    |> create_addon(product_sync, addon_mapping, sku_data, sku_price, sku_list_price)
    |> upsert_addon_mapping(to_string(sku_data["Id"]))
    |> Repo.transaction()
    |> case do
      {:error, multi_name, error, changes} -> {:error, {multi_name, error, changes}}
      {:ok, product} -> {:ok, product}
    end
  end

  defp create_product_variant(multi, product_sync, sku_data, sku_price, sku_list_price) do
    external_id = inspect(sku_data["Id"])

    Multi.run(multi, :product_variant, fn repo, %{product: product} ->
      variant_changeset =
        build_product_variant_changeset(product, sku_data, sku_price, sku_list_price)

      operation = if variant_changeset.data.id, do: :update, else: :insert
      upsert_and_log_entry(repo, product_sync, variant_changeset, external_id, operation)
    end)
  end

  @doc false
  def build_product_variant_changeset(product, sku, sku_price, sku_list_price) do
    sku_id = sku["AlternateIds"]["RefId"]

    attrs = %{
      bar_code: sku["AlternateIds"]["Ean"],
      image_urls: get_in(sku, ["Images", Access.all(), "ImageUrl"]) || [],
      price: Decimal.div(Decimal.new(sku_price), 100),
      list_price: Decimal.div(Decimal.new(sku_list_price), 100),
      sku: sku_id,
      name: product.name,
      variation_types:
        (sku["SkuSpecifications"] || [])
        |> Enum.filter(&(&1["IsFilter"] && !Enum.empty?(&1["FieldValues"])))
        |> Enum.map(fn spec ->
          %{key: spec["FieldName"], value: Enum.at(spec["FieldValues"], 0)}
        end),
      archived_at: nil
    }

    empty_product_variant = %ProductVariant{
      product_id: product.id,
      organization_id: product.organization_id
    }

    product_variants = Repo.all(from pv in ProductVariant, where: pv.product_id == ^product.id)
    product = Map.put(product, :product_variants, product_variants)

    product_variants =
      Enum.filter(
        product.product_variants,
        &(&1.sku == sku_id)
      )

    product_variant =
      case product_variants do
        [] ->
          empty_product_variant

        [product_variant] ->
          product_variant

        [default_product_variant | rest] ->
          Enum.find(rest, default_product_variant, &is_nil(&1.archived_at))
      end

    ProductVariant.changeset(product_variant, attrs, "vtex")
  end

  defp create_addon(multi, product_sync, addon_mapping, sku_data, sku_price, sku_list_price) do
    external_id = inspect(sku_data["Id"])

    Multi.run(multi, :addon, fn repo, %{product: product} ->
      addon_changeset =
        build_addon_changeset(product, addon_mapping, sku_data, sku_price, sku_list_price)

      operation = if addon_changeset.data.id, do: :update, else: :insert

      upsert_and_log_entry(
        repo,
        product_sync,
        addon_changeset,
        external_id,
        operation,
        sku_data["AlternateIds"]["RefId"]
      )
    end)
  end

  defp build_addon_changeset(product, addon_mapping, sku, sku_price, sku_list_price) do
    attrs = %{
      name: product.name,
      price: Decimal.div(Decimal.new(sku_price), 100),
      list_price: Decimal.div(Decimal.new(sku_list_price), 100),
      type: "subscription",
      description: sku["ProductDescription"],
      image_url: sku["ImageUrl"],
      metadata: format_subscription_metadata(sku)
    }

    default_addon = %Addon{
      organization_id: product.organization_id
    }

    addon =
      case addon_mapping do
        %AddonMapping{} ->
          mapping = Repo.preload(addon_mapping, [:addon])
          mapping.addon

        _ ->
          default_addon
      end

    Addon.changeset(addon, attrs)
  end

  defp upsert_and_log_entry(repo, product_sync, changeset, external_id, operation, sku \\ nil) do
    operation_func = if operation == :insert, do: &repo.insert/1, else: &repo.update/1

    case operation_func.(changeset) do
      {:ok, entity} ->
        sku = Map.get(entity, :sku, sku)
        entry_attrs = %{sku: sku, external_id: external_id, status: "success", errors: []}
        Rms.Integrations.create_product_sync_entry(product_sync, entry_attrs)
        {:ok, entity}

      {:error, changeset} ->
        errors = extract_changeset_errors(changeset)

        entry_attrs = %{
          sku: Map.get(changeset.params, ["sku"], sku),
          external_id: external_id,
          status: "error",
          errors: [
            %{
              metadata: %{params: changeset.params, errors: errors},
              code: "changeset_error",
              detail: "invalid changeset"
            }
          ]
        }

        Rms.Integrations.create_product_sync_entry(product_sync, entry_attrs)
        {:ok, nil}
    end
  end

  defp extract_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, &error_traverser/1)
  end

  defp error_traverser({msg, opts}) do
    Enum.reduce(opts, msg, fn {key, value}, acc ->
      String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
    end)
  end

  defp upsert_product_variant_mapping(multi, organization, external_id, opts) do
    Multi.run(
      multi,
      :product_variant_mapping,
      fn
        _repo, %{product_variant: product_variant} when not is_nil(product_variant) ->
          %ProductVariantMapping{}
          |> ProductVariantMapping.changeset(%{
            organization_id: product_variant.organization_id,
            product_variant_id: product_variant.id,
            source: "vtex",
            external_id: external_id,
            sold_out: product_variant_sold_out?(opts[:simulation_data], organization)
          })
          |> Repo.insert(
            conflict_target: [:organization_id, :external_id, :source],
            on_conflict: {:replace, [:product_variant_id, :sold_out, :updated_at]}
          )

        _repo, _changes ->
          {:ok, nil}
      end
    )
  end

  defp upsert_addon_mapping(multi, external_id) do
    Multi.run(
      multi,
      :addon_mapping,
      fn
        _repo, %{addon: addon} when not is_nil(addon) ->
          %AddonMapping{}
          |> AddonMapping.changeset(%{
            organization_id: addon.organization_id,
            addon_id: addon.id,
            source: "vtex",
            external_id: external_id
          })
          |> Repo.insert(
            conflict_target: [:organization_id, :external_id, :source],
            on_conflict: {:replace, [:updated_at]}
          )

        _repo, _changes ->
          {:ok, nil}
      end
    )
  end

  defp ensure_product_name_updated(product, sku_data) do
    if product.name != sku_data["ProductName"] do
      Rms.Commerce.Products.update_product(product, %{name: sku_data["ProductName"]},
        source: "vtex"
      )
    else
      {:ok, product}
    end
  end

  defp inactive_product?(nil, %{"IsActive" => false}), do: true

  defp inactive_product?(%{product_variant: %{archived_at: archived_at}}, %{"IsActive" => false})
       when not is_nil(archived_at),
       do: true

  defp inactive_product?(%{addon: %{archived_at: archived_at}}, %{"IsActive" => false})
       when not is_nil(archived_at),
       do: true

  defp inactive_product?(_, _), do: false

  defp format_subscription_metadata(sku_data) do
    attachments = sku_data["Attachments"]
    sku_id = sku_data["AlternateIds"]["RefId"]

    Enum.find_value(attachments, fn
      %{
        "Id" => _,
        "Name" => "vtex.subscription." <> _ = name,
        "Keys" => _,
        "Fields" => [
          %{
            "FieldName" => "vtex.subscription.key.frequency",
            "MaxCaracters" => _,
            "DomainValues" => value
          }
        ],
        "IsActive" => true,
        "IsRequired" => _
      } ->
        %{
          "sku_id" => sku_id,
          "name" => name,
          "content" => %{
            "vtex.subscription.key.frequency" => value
          }
        }

      _ ->
        false
    end)
  end

  defp product_variant_sold_out?(simulation_data, organization) when is_map(simulation_data) do
    case get_in(simulation_data, ["logisticsInfo", Access.at(0), "stockBalance"]) do
      stock_balance when is_integer(stock_balance) ->
        organization.safety_net_quantity >= stock_balance

      _ ->
        false
    end
  end

  defp product_variant_sold_out?(_, _), do: false

  defp handle_product_search(organization, sku_data) do
    case search_product(organization, sku_data) do
      {:ok, :variant_mapping, product_variant} ->
        ensure_product_name_updated(product_variant.product, sku_data)

      {:ok, :product_mapping, product_mapping} ->
        ensure_product_name_updated(product_mapping.product, sku_data)

      {:ok, :existing_variant, existing_variant} ->
        ensure_product_name_updated(existing_variant.product, sku_data)

      {:error, :not_found} ->
        create_new_product(organization, sku_data)

      error ->
        error
    end
  end

  defp search_product(organization, sku_data) do
    cond do
      product_variant_mapping = find_by_variant_mapping(organization, sku_data) ->
        {:ok, :variant_mapping, product_variant_mapping}

      product_mapping = find_by_product_mapping(organization, sku_data) ->
        {:ok, :product_mapping, product_mapping}

      existing_variant = find_by_sku(organization, sku_data) ->
        create_product_mapping(organization, existing_variant, sku_data)

      true ->
        {:error, :not_found}
    end
  end

  defp find_by_variant_mapping(organization, sku_data) do
    case Rms.Integrations.get_product_variant_mapping(
           organization.id,
           "vtex",
           inspect(sku_data["Id"])
         ) do
      nil -> nil
      mapping -> Repo.preload(mapping.product_variant, [:product])
    end
  end

  defp find_by_product_mapping(organization, sku_data) do
    Rms.Integrations.get_product_mapping(
      organization.id,
      "vtex",
      inspect(sku_data["ProductId"])
    )
  end

  defp find_by_sku(organization, sku_data) do
    ref_id = get_in(sku_data, ["AlternateIds", "RefId"])

    if is_nil(ref_id) do
      nil
    else
      Repo.one(
        from pv in ProductVariant,
          where:
            pv.organization_id == ^organization.id and
              pv.sku == ^ref_id,
          preload: [:product]
      )
    end
  end

  defp create_product_mapping(organization, existing_variant, sku_data) do
    case Rms.Integrations.create_product_sync_mapping(organization.id, %{
           organization_id: organization.id,
           external_id: inspect(sku_data["ProductId"]),
           source: "vtex",
           product_id: existing_variant.product_id
         }) do
      {:ok, _product_mapping} -> {:ok, :existing_variant, existing_variant}
      error -> error
    end
  end

  defp create_new_product(organization, sku_data) do
    product_attrs = %{
      name: sku_data["ProductName"]
    }

    with {:ok, product} <-
           Rms.Commerce.Products.create_product(organization.id, product_attrs, source: "vtex"),
         {:ok, _mapping} <-
           Rms.Integrations.create_product_sync_mapping(organization.id, %{
             organization_id: organization.id,
             external_id: inspect(sku_data["ProductId"]),
             source: "vtex",
             product_id: product.id
           }) do
      {:ok, product}
    end
  end
end
