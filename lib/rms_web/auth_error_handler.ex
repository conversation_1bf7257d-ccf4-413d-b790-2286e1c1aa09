defmodule RmsWeb.AuthErrorHandler do
  import Plug.Conn

  @behaviour Guardian.Plug.ErrorHandler

  @impl Guardian.Plug.ErrorHandler
  def auth_error(conn, {_type, %Jason.DecodeError{}}, _opts) do
    body = Jason.encode!(%{message: "invalid token"})

    conn
    |> put_resp_content_type("application/json")
    |> send_resp(400, body)
  end

  def auth_error(conn, {_type, reason}, _opts) do
    body = Jason.encode!(%{message: reason})

    conn
    |> put_resp_content_type("application/json")
    |> send_resp(401, body)
  end
end
