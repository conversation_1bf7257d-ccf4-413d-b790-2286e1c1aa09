defmodule RmsWeb.AddonsController do
  use RmsWeb, :controller

  alias Rms.Commerce.Products

  action_fallback RmsWeb.FallbackController

  @preloads []
  @allowed_params ~w(after before limit type name)
  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_params()
      |> Keyword.put(:preloads, @preloads)

    page =
      Products.paginated_addons(resource.organization_id, opts)

    render(conn, "index.json", addons: page.entries, page_metadata: page.metadata)
  end

  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
