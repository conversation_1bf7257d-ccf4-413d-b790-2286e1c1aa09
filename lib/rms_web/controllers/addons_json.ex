defmodule RmsWeb.AddonsJSON do
  def render("index.json", %{addons: addons, page_metadata: page}) do
    %{
      addons: Enum.map(addons, &data/1),
      page: %{
        before: page.before,
        after: page.after,
        limit: page.limit
      }
    }
  end

  def render("show.json", %{addon: addon}) do
    data(addon)
  end

  def data(addon) do
    %{
      id: addon.id,
      name: addon.name,
      list_price: addon.list_price,
      price: addon.price,
      type: addon.type,
      image_url: addon.image_url,
      description: addon.description,
      metadata: format_addon_metadata(addon.metadata)
    }
  end

  defp format_addon_metadata(%{
         "name" => _,
         "content" => %{
           "vtex.subscription.key.frequency" => cycle
         }
       }) do
    %{
      subscription_cycle: format_cycle(cycle)
    }
  end

  defp format_addon_metadata(_) do
    %{
      subscription_cycle: nil
    }
  end

  defp format_cycle(cycle) do
    [number, day] = String.split(cycle, " ")
    format_cycle(number, day)
  end

  defp format_cycle(number, "day") do
    String.to_integer(number)
  end

  defp format_cycle(number, "month") do
    String.to_integer(number) * 30
  end

  defp format_cycle(number, "year") do
    String.to_integer(number) * 365
  end

  defp format_cycle(number, _) do
    number
  end
end
