defmodule RmsWeb.AddressController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  alias Rms.Addresses
  alias Rms.Addresses.Address
  alias Rms.Customers
  alias Rms.Customers.Customer

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    address = Addresses.get_address!(resource.organization_id, id)

    with {:ok, %Address{}} <- Addresses.delete_address!(address) do
      send_resp(conn, :no_content, "")
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    address =
      Addresses.get_address!(resource.organization_id, id)

    render(conn, "show.json", address: address)
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    address = Addresses.get_address!(resource.organization_id, id)

    with {:ok, %Address{} = updated_address} <- Addresses.update_address!(address, params) do
      render(conn, "show.json", address: updated_address)
    end
  end

  def update_customer_address(conn, %{"customer_id" => customer_id, "addresses" => addresses}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer = Customers.get_customer!(resource.organization_id, customer_id)

    with {:ok, %Customer{} = customer} <- Customers.update_customer_address!(customer, addresses) do
      json_customer = RmsWeb.CustomerJSON.render("customer.json", %{customer: customer})

      conn
      |> put_status(200)
      |> json(%{data: json_customer})
    end
  end
end
