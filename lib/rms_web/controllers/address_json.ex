defmodule RmsWeb.AddressJSON do
  def render("show.json", %{address: address}) when not is_nil(address) do
    %{
      id: address.id,
      receiver_name: address.receiver_name,
      city_name: address.city_name,
      city_code: address.city_code,
      state: address.state,
      country_name: address.country_name,
      neighborhood: address.neighborhood,
      street: address.street,
      street_type: address.street_type,
      number: address.number,
      zip: address.zip,
      complement: address.complement
    }
  end

  def render("show.json", _), do: nil
end
