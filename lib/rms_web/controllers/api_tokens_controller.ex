defmodule RmsWeb.ApiTokensController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def create(conn, _params) do
    with %Rms.Accounts.User{} = user <- Rms.Guardian.Plug.current_resource(conn),
         user = Rms.Repo.preload(user, [:organization]),
         {:ok, api_token} <- Rms.Accounts.create_api_token(user.organization) do
      json(conn, %{api_token: api_token.token})
    else
      %Rms.Accounts.ApiToken{} ->
        {:error, :forbidden}

      error ->
        error
    end
  end

  def delete(conn, _params) do
    with %Rms.Accounts.User{} = user <- Rms.Guardian.Plug.current_resource(conn),
         user = Rms.Repo.preload(user, [:organization]),
         {:ok, _api_token} <- Rms.Accounts.delete_api_token!(user.organization.id) do
      send_resp(conn, :no_content, "")
    else
      %Rms.Accounts.ApiToken{} ->
        {:error, :forbidden}

      error ->
        error
    end
  end
end
