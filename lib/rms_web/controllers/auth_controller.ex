defmodule RmsWeb.AuthController do
  use RmsWeb, :controller

  def index(conn, _params) do
    case Rms.Guardian.Plug.current_resource(conn) do
      %Rms.Accounts.ApiToken{} = api_token ->
        conn
        |> put_status(:ok)
        |> json(%{resource: %{id: api_token.organization_id, type: "machine"}})

      %Rms.Accounts.User{} = user ->
        user = Rms.Repo.preload(user, locations: :address)

        json = RmsWeb.UsersJSON.render("show.json", %{user: user})

        conn
        |> put_status(:ok)
        |> json(%{resource: json, type: "user"})
    end
  end
end
