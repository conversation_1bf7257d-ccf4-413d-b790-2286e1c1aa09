defmodule RmsWeb.BFF.OrderController do
  @moduledoc """
  Controller for handling order-related operations in the BFF API.

  This controller provides endpoints for creating orders, processing returns,
  and retrieving order information. It supports various scenarios including:
  - Creating a standard order from a cart
  - Creating an order with returned items (store credit applied)
  - Processing returns without creating a new order
  """
  use RmsWeb, :controller

  alias Rms.Commerce.Orders
  alias Rms.Commerce.Fulfillments
  alias RmsWeb.SchemaValidator

  require Logger

  action_fallback RmsWeb.FallbackController

  @preloads [
    :discounts,
    :order_customer,
    :staff,
    :cashier,
    transaction: [:payments],
    customer: [:addresses],
    fulfillments: [line_items: [:discounts, :reverse_fulfillment_line_items]]
  ]

  @cart_preloads [
    :discounts,
    :shipping_address,
    :cart_addons,
    delivery_groups: [cart_items: [:discounts, product_variant: :product]]
  ]

  @doc """
  Creates a new order based on the provided parameters.

  Handles different scenarios:
  - Standard order creation (no returns)
  - Order with returned items (store credit applied)
  - Returns-only transaction (no new order)
  """
  def create_order(conn, params) when not is_map_key(params, "returned_line_items") do
    create_standard_order(conn, params)
  end

  def create_order(conn, %{"returned_line_items" => []} = params) do
    create_standard_order(conn, params)
  end

  def create_order(
        conn,
        %{
          "returned_line_items" => returned_line_items,
          "cart_id" => nil
        } = params
      ) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    return_params = %{
      returned_line_items: returned_line_items,
      customer_id: params["customer_id"],
      location_id: params["location_id"],
      staff_id: params["staff_id"]
    }

    with {:ok, _} <- process_return_only(resource.organization_id, return_params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create_order(conn, %{"returned_line_items" => returned_line_items} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    opts = [allowed_location_ids: Rms.Accounts.assigned_location_ids(resource)]

    cart =
      Rms.Commerce.Carts.get_cart!(resource.organization_id, params["cart_id"], @cart_preloads)

    order_params = Rms.Commerce.Orders.format_cart(cart, params)

    combined_params = %{
      order_params: order_params,
      cart: cart,
      returned_line_items: returned_line_items,
      opts: opts,
      location_id: params["location_id"],
      staff_id: params["staff_id"],
      customer_id: params["customer_id"],
      transaction_customer: params["transaction_customer"]
    }

    with {:ok, order} <- create_order_with_returns(resource.organization_id, combined_params) do
      order = Rms.Repo.preload(order, @preloads)
      return_credit_info = Orders.OrderData.get_return_credit_info(order.id)

      conn
      |> put_status(:created)
      |> render(:show, order: order, return_credit_info: return_credit_info)
    end
  end

  @doc """
  Retrieves a single order by ID with preloaded associations.
  """
  def get_order(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    order = Orders.get_order!(resource.organization_id, id, @preloads)
    return_credit_info = Orders.OrderData.get_return_credit_info(id)

    render(conn, :show, order: order, return_credit_info: return_credit_info)
  end

  def external_orders(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, validated_params} <- SchemaValidator.validate(conn, :external_orders, params),
         {:ok, external_orders_data} <-
           Orders.get_external_orders(
             resource.organization_id,
             Keyword.new(validated_params)
           ) do
      render(conn, :external_orders,
        orders: external_orders_data.orders,
        metadata: external_orders_data.metadata
      )
    else
      {:error, :setting_not_found} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error: %{
            detail: "e-commerce integration settings not found or not configured."
          }
        })

      {:error, :setting_malformed} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error: %{
            detail: "e-commerce integration settings are improperly configured."
          }
        })

      error ->
        error
    end
  end

  @doc """
  Imports an order from Shopify using its GID, or returns the existing
  internal order if it has already been imported.
  """
  def import_shopify_order(conn, %{"shopify_order_id" => shopify_order_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    organization_id = resource.organization_id

    case find_or_import_shopify_order(organization_id, shopify_order_id) do
      {:ok, order} ->
        order = Rms.Repo.preload(order, @preloads)
        return_credit_info = Orders.OrderData.get_return_credit_info(order.id)
        render(conn, :show, order: order, return_credit_info: return_credit_info)

      {:error, {:location_mapping_not_found, location_id}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error: %{
            detail: "location mapping not found for shopify external id #{location_id}"
          }
        })

      errors ->
        errors
    end
  end

  def import_shopify_order(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: %{detail: "missing required parameter: shopify_order_id"}})
  end

  # Private functions

  defp create_standard_order(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    allowed_location_ids = Rms.Accounts.assigned_location_ids(resource)
    opts = [allowed_location_ids: allowed_location_ids]

    # Extract transaction_customer from params and add to opts if present
    opts =
      if params["transaction_customer"],
        do: Keyword.put(opts, :transaction_customer, params["transaction_customer"]),
        else: opts

    opts =
      if params["create_transaction"],
        do: Keyword.put(opts, :create_transaction, params["create_transaction"]),
        else: opts

    cart =
      Rms.Commerce.Carts.get_cart!(resource.organization_id, params["cart_id"], @cart_preloads)

    order_params = Orders.format_cart(cart, params)

    with {:ok, order} <- Orders.create_order(resource.organization_id, order_params, opts) do
      order = Rms.Repo.preload(order, @preloads)

      conn
      |> put_status(:created)
      |> render(:show, order: order)
    end
  end

  defp process_return_only(organization_id, params) do
    %{
      returned_line_items: returned_line_items,
      customer_id: customer_id,
      location_id: location_id,
      staff_id: staff_id
    } = params

    Rms.Repo.transaction_with(fn ->
      with {:ok, reverse_fulfillment} <-
             create_reverse_fulfillment(
               organization_id,
               returned_line_items,
               location_id,
               staff_id,
               customer_id
             ),
           {:ok, [_, updated_reverse_fulfillment]} <-
             generate_return_credit(reverse_fulfillment, customer_id),
           {:ok, completed_reverse_fulfillment} <-
             Fulfillments.complete_reverse_fulfillment(updated_reverse_fulfillment) do
        update_affected_orders(completed_reverse_fulfillment.id, organization_id)
        {:ok, completed_reverse_fulfillment}
      end
    end)
  end

  defp update_affected_orders(reverse_fulfillment_id, organization_id) do
    order_ids =
      Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
        reverse_fulfillment_id
      )
      |> Enum.map(& &1.id)

    Orders.update_order_statuses(order_ids, organization_id)
  end

  defp create_order_with_returns(organization_id, params) do
    %{
      order_params: order_params,
      returned_line_items: returned_line_items,
      opts: opts,
      location_id: location_id,
      staff_id: staff_id,
      customer_id: customer_id,
      transaction_customer: transaction_customer
    } = params

    Rms.Repo.transaction_with(fn ->
      with {:ok, fulfillment} <-
             create_reverse_fulfillment(
               organization_id,
               returned_line_items,
               location_id,
               staff_id,
               customer_id
             ),
           {:ok, [credit, _]} <- generate_return_credit(fulfillment, customer_id) do
        updated_opts =
          if transaction_customer,
            do: Keyword.put(opts, :transaction_customer, transaction_customer),
            else: opts

        create_credited_order(organization_id, order_params, credit, updated_opts)
      end
    end)
  end

  defp create_reverse_fulfillment(
         organization_id,
         returned_line_items,
         location_id,
         staff_id,
         customer_id
       ) do
    Fulfillments.create_reverse_fulfillment(
      organization_id,
      returned_line_items,
      location_id,
      staff_id,
      customer_id
    )
  end

  defp generate_return_credit(fulfillment, customer_id) do
    Fulfillments.CreditGenerator.generate_credit(
      fulfillment,
      customer_id
    )
  end

  defp create_credited_order(organization_id, order_params, credit, opts) do
    Orders.create_order_with_credit(
      organization_id,
      order_params,
      credit,
      opts
    )
  end

  defp find_or_import_shopify_order(organization_id, shopify_order_id) do
    case Orders.get_order_by_external_identifier(organization_id, shopify_order_id) do
      %Rms.Commerce.Orders.Order{} = existing_order ->
        {:ok, existing_order}

      nil ->
        Rms.Integrations.Shopify.Orders.import_order(organization_id, shopify_order_id)
    end
  end
end
