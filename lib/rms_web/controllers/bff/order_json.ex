defmodule RmsWeb.BFF.OrderJSON do
  alias Rms.Commerce.Orders.Order

  def show(%{order: order} = params) do
    order_data =
      order
      |> data()
      |> put_in([:return_credit_origin], get_return_credit_origin(params[:return_credit_info]))

    %{order: order_data}
  end

  @doc """
  Renders a list of external orders with metadata.
  """
  def external_orders(%{orders: orders, metadata: metadata}) do
    %{
      orders: for(order_map <- orders, do: external_order_data(order_map)),
      metadata: metadata
    }
  end

  defp data(%Order{} = order) do
    %{
      id: order.id,
      name: order.name,
      status: order.status,
      total_price: order.total_price,
      total_price_with_addons: Map.get(order, :total_price_with_addons, order.total_price),
      total_items_selling_price: Map.get(order, :total_items_selling_price, order.total_price),
      total_items_list_price: Map.get(order, :total_items_list_price, order.total_price),
      total_delivery_price: Map.get(order, :total_delivery_price, 0),
      total_discount: Map.get(order, :total_discount, 0),
      total_discount_with_addons:
        Map.get(order, :total_discount_with_addons, order.total_discount),
      discounts: render_discounts(order.discounts),
      fulfillments: render_fulfillments(order.fulfillments),
      addons: render_addons(order.addons),
      customer: RmsWeb.OrderCustomerJSON.data(order.order_customer),
      staff: RmsWeb.StaffsJSON.data(order.staff),
      cashier: RmsWeb.StaffsJSON.data(order.cashier),
      paid: order.status == "paid",
      fiscal_invoice: nil,
      transaction: RmsWeb.TransactionsJSON.data(order.transaction),
      external_id: order.external_id,
      inserted_at: order.inserted_at,
      reference_at: order.reference_at,
      shipping_address: order.shipping_address,
      returned_line_items: render_return_line_items(order.fulfillments),
      location_id: order.location_id,
      source: order.source,
      notes: (order.metadata || %{}) |> Map.get(:notes, "")
    }
  end

  defp calculate_returned_quantity(line_item) do
    line_item.reverse_fulfillment_line_items
    |> Enum.map(& &1.returned_quantity)
    |> Enum.sum()
  end

  defp render_return_line_items(fulfillments) do
    return_line_items = Enum.flat_map(fulfillments, &Map.get(&1, :line_items))

    Enum.map(return_line_items, fn line_item ->
      returned_quantity = calculate_returned_quantity(line_item)

      %{returned_quantity: returned_quantity, line_item: render_line_item(line_item)}
    end)
  end

  defp render_addons(addons) when is_list(addons) do
    Enum.map(addons, fn addon ->
      %{
        name: addon.name,
        price: addon.price,
        list_price: addon.list_price,
        image_url: addon.image_url,
        metadata: addon.metadata,
        type: addon.type
      }
    end)
  end

  defp render_discounts(discounts) when is_list(discounts) do
    discounts
    |> Enum.filter(fn discount ->
      Map.get(discount, :type, "não informado") != "automatic_ecommerce"
    end)
    |> Enum.map(&RmsWeb.DiscountsJSON.data/1)
  end

  defp render_fulfillments(fulfillments) when is_list(fulfillments) do
    Enum.map(fulfillments, fn fulfillment ->
      %{
        shipping_method: fulfillment.shipping_method,
        external_reference: fulfillment.external_reference,
        status: fulfillment.status,
        line_items: Enum.map(fulfillment.line_items || [], &render_line_item/1)
      }
    end)
  end

  def render_line_item(line_item) do
    returned_quantity = calculate_returned_quantity(line_item)

    %{
      id: line_item.id,
      quantity: line_item.quantity,
      current_quantity: line_item.quantity - returned_quantity,
      price: line_item.price,
      list_price: line_item.list_price,
      shipping_method: line_item.shipping_method,
      shipping_settings: line_item.shipping_settings,
      pickup_point_name: get_pickup_point_name(line_item.shipping_settings),
      image_url: line_item.image_url,
      variant_name: line_item.variant_name,
      product_name: line_item.product_name,
      product_variant_id: line_item.product_variant_id,
      discounts: render_discounts(line_item.discounts),
      sku: line_item.sku
    }
  end

  defp get_pickup_point_name(%{
         "deliveryChannel" => "pickup-in-point",
         "pickupStoreInfo" => %{"friendlyName" => pickup_point_name}
       }) do
    pickup_point_name
  end

  defp get_pickup_point_name(_) do
    nil
  end

  defp get_return_credit_origin(nil), do: []

  defp get_return_credit_origin(return_credit_info) do
    return_credit_info
    |> Enum.map(& &1.original_order_id)
    |> Enum.uniq()
  end

  defp external_order_data(%{"external_source" => "shopify"} = order_map) do
    inserted_at = parse_inserted_at(order_map["createdAt"])
    customer_data = build_customer_data(order_map["customer"])
    order_status = calculate_order_status(order_map)

    total_price =
      extract_shopify_price(order_map, ["currentTotalPriceSet", "shopMoney", "amount"])

    %{
      id: order_map["internal_order_id"],
      name: order_map["name"],
      status: order_status,
      total_price: total_price,
      total_price_with_addons: total_price,
      total_items_selling_price: nil,
      total_items_list_price: nil,
      total_delivery_price: nil,
      total_discount: nil,
      total_discount_with_addons: nil,
      discounts: [],
      fulfillments: [],
      addons: [],
      customer: customer_data,
      staff: nil,
      cashier: nil,
      paid: order_status == "paid",
      fiscal_invoice: nil,
      transaction: nil,
      external_id: order_map["id"],
      inserted_at: inserted_at,
      shipping_address: nil,
      returned_line_items: [],
      location_id: nil
    }
  end

  defp parse_inserted_at(nil), do: nil

  defp parse_inserted_at(created_at_string) do
    case DateTime.from_iso8601(created_at_string) do
      {:ok, dt, _} -> dt
      _ -> nil
    end
  end

  defp build_customer_data(nil), do: nil

  defp build_customer_data(customer) when is_map(customer) do
    %{
      id: nil,
      document_type: nil,
      document: nil,
      email: customer["email"],
      name: customer["displayName"],
      primary_phone_number: customer["phone"]
    }
  end

  defp build_customer_data(_), do: nil

  defp extract_shopify_price(order_map, path) do
    case get_in(order_map, path) do
      nil -> nil
      amount when is_binary(amount) or is_number(amount) -> Decimal.new(amount)
      _ -> nil
    end
  end

  defp calculate_order_status(shopify_order_map) do
    cond do
      shopify_order_map["cancelledAt"] != nil ->
        "canceled"

      shopify_order_map["displayFinancialStatus"] == "PAID" and
          shopify_order_map["displayFulfillmentStatus"] == "FULFILLED" ->
        "paid"

      shopify_order_map["displayFinancialStatus"] == "PARTIALLY_PAID" ->
        "partially_paid"

      true ->
        "open"
    end
  end
end
