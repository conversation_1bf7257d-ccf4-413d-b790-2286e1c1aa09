defmodule RmsWeb.CancelationIntegrationsController do
  use RmsWeb, :controller

  alias Rms.Integrations.Orders
  alias Rms.Integrations.Orders.CancelationEndpoint

  action_fallback RmsWeb.FallbackController

  def index(conn, _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cancelation_endpoints = Orders.list_cancelation_endpoints(resource.organization_id)
    render(conn, :index, cancelation_endpoints: cancelation_endpoints)
  end

  def create(conn, %{"cancelation_endpoint" => params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %CancelationEndpoint{} = cancelation_endpoint} <-
           Orders.create_cancelation_endpoint(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/integrations/cancelation/#{cancelation_endpoint}")
      |> render(:show, cancelation_endpoint: cancelation_endpoint)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cancelation_endpoint = Orders.get_cancelation_endpoint!(resource.organization_id, id)
    render(conn, :show, cancelation_endpoint: cancelation_endpoint)
  end

  def update(conn, %{"id" => id, "cancelation_endpoint" => cancelation_endpoint_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cancelation_endpoint = Orders.get_cancelation_endpoint!(resource.organization_id, id)

    with {:ok, %CancelationEndpoint{} = cancelation_endpoint} <-
           Orders.update_cancelation_endpoint(cancelation_endpoint, cancelation_endpoint_params) do
      render(conn, :show, cancelation_endpoint: cancelation_endpoint)
    end
  end

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cancelation_endpoint = Orders.get_cancelation_endpoint!(resource.organization_id, id)

    with {:ok, %CancelationEndpoint{}} <- Orders.delete_cancelation_endpoint(cancelation_endpoint) do
      send_resp(conn, :no_content, "")
    end
  end
end
