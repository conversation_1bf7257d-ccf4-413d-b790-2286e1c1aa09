defmodule RmsWeb.CancelationIntegrationsJSON do
  alias Rms.Integrations.Orders.CancelationEndpoint

  @doc """
  Renders a list of cancelation_endpoints.
  """
  def index(%{cancelation_endpoints: cancelation_endpoints}) do
    %{data: for(cancelation_endpoint <- cancelation_endpoints, do: data(cancelation_endpoint))}
  end

  @doc """
  Renders a single cancelation_endpoint.
  """
  def show(%{cancelation_endpoint: cancelation_endpoint}) do
    %{data: data(cancelation_endpoint)}
  end

  defp data(%CancelationEndpoint{} = cancelation_endpoint) do
    %{
      id: cancelation_endpoint.id,
      endpoint: cancelation_endpoint.endpoint,
      headers: cancelation_endpoint.headers,
      active: cancelation_endpoint.active,
      inserted_at: cancelation_endpoint.inserted_at,
      updated_at: cancelation_endpoint.updated_at
    }
  end
end
