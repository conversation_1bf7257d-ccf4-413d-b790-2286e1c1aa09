defmodule RmsWeb.CartController do
  alias Rms.Commerce.Carts
  alias Rms.Commerce.Carts.Cart
  use RmsWeb, :controller

  alias RmsWeb.SchemaValidator

  action_fallback RmsWeb.FallbackController

  @preloads [
    :shipping_address,
    :staff,
    :customer,
    :discounts,
    cart_addons: [:addon],
    delivery_groups: [cart_items: [:discounts, product_variant: [product: :product_variants]]]
  ]

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_search_params()
      |> Keyword.put(:preloads, @preloads)

    page =
      Carts.paginated_carts(resource.organization_id, opts)

    conn
    |> render("carts.json", %{carts: page.entries, page_metadata: page.metadata})
  end

  def delivery_options(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cart_info = cart_info(resource.organization_id, params)

    with {:ok, delivery_options} <-
           Carts.SimulateCart.fetch_delivery_options(resource.organization_id, cart_info) do
      conn
      |> put_status(:ok)
      |> put_view(RmsWeb.DeliveryOptionsJSON)
      |> render("delivery_options.json", %{
        delivery_options: delivery_options
      })
    end
  end

  def simulate(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    ecomm = Rms.Settings.get_connected_ecommerce(resource.organization_id)

    with {:ok, cart_info} <- SchemaValidator.validate(conn, :simulate_cart, params),
         {:ok, cart} <-
           Carts.simulate_and_save_cart(
             resource.organization_id,
             stringify_keys(cart_info),
             ecomm
           ) do
      cart =
        cart
        |> Rms.Repo.preload(@preloads)

      conn
      |> put_status(:ok)
      |> render("cart.json", %{cart: cart})
    end
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    cart = Carts.get_cart!(resource.organization_id, id)

    with {:ok, %Cart{} = cart} <- Carts.update_cart(cart, params) do
      cart = Rms.Repo.preload(cart, @preloads)

      conn
      |> render("cart.json", %{cart: cart})
    end
  end

  def group(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %Cart{} = cart} <- Carts.group_cart(resource.organization_id, id, params["items"]) do
      cart = Rms.Repo.preload(cart, @preloads)

      conn
      |> put_status(:ok)
      |> render("cart.json", %{cart: cart})
    end
  end

  defp cart_info(_organization_id, params), do: params

  @allowed_params ~w(after before limit saved state_of_sale staff_id customer_id location_id)
  defp prepare_search_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end

  def stringify_keys(map) do
    for {key, value} <- map, into: %{} do
      {to_string(key), value}
    end
  end
end
