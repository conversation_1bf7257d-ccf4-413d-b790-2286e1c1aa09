defmodule RmsWeb.CartJSON do
  alias Rms.Commerce.Products.ProductVariant

  def render("carts.json", %{carts: carts, page_metadata: page}) do
    %{
      carts: Enum.map(carts, &render("cart.json", %{cart: &1})),
      page: %{
        before: page.before,
        after: page.after,
        limit: page.limit
      }
    }
  end

  def render("cart.json", %{cart: cart}) do
    cart
    |> Map.take([
      :id,
      :total_items_list_price,
      :total_items_selling_price,
      :total_manual_discount,
      :total_delivery_price,
      :total_items_manual_discount,
      :total_ecommerce_discounts,
      :total_price,
      :max_delivery_time,
      :messages,
      :saved,
      :state_of_sale,
      :updated_at
    ])
    |> Map.merge(%{
      discounts:
        cart.discounts
        |> Enum.filter(fn discount ->
          Map.get(discount, :type, "não informado") != "automatic_ecommerce"
        end)
        |> Enum.map(&RmsWeb.DiscountsJSON.data/1),
      delivery_groups: Enum.map(cart.delivery_groups, &render_delivery_group/1),
      staff: RmsWeb.StaffsJSON.data(cart.staff),
      customer: RmsWeb.CustomerJSON.data(cart.customer),
      shipping_address: RmsWeb.AddressJSON.render("show.json", %{address: cart.shipping_address}),
      addons: render_cart_addons(cart.cart_addons),
      notes: (cart.metadata || %{}) |> Map.get(:notes, "")
    })
  end

  defp render_cart_addons(cart_addons) when is_list(cart_addons) do
    Enum.map(cart_addons, &RmsWeb.AddonsJSON.data(&1.addon))
  end

  defp render_cart_addons(_cart_addons), do: []

  def render_delivery_group(delivery_group) do
    delivery_group
    |> Map.take([:delivery_time, :fulfillment_type, :pickup_point])
    |> Map.merge(%{
      cart_items:
        delivery_group.cart_items
        |> Enum.sort_by(& &1.item_index)
        |> Enum.map(&render_cart_item/1)
    })
  end

  def render_cart_item(cart_item) do
    cart_item
    |> Map.take([
      :id,
      :quantity,
      :list_price,
      :selling_price,
      :total_price,
      :metadata,
      :is_gift,
      :group_index
    ])
    |> Map.merge(render_product_variant(cart_item.product_variant))
    |> Map.merge(render_cart_item_discounts(cart_item.discounts))
  end

  defp render_product_variant(nil) do
    %{
      product_variant: nil,
      product: nil
    }
  end

  defp render_product_variant(%ProductVariant{} = product_variant) do
    variant_with_type = add_default_variation_type(product_variant)

    %{
      product_variant: RmsWeb.ProductsJSON.render_product_variant(variant_with_type),
      product: RmsWeb.ProductsJSON.render("show.json", %{product: variant_with_type.product})
    }
  end

  defp render_cart_item_discounts(discounts) when is_list(discounts) do
    %{
      discounts:
        discounts
        |> Enum.filter(fn discount ->
          Map.get(discount, :type, "não informado") != "automatic_ecommerce"
        end)
        |> Enum.map(&RmsWeb.DiscountsJSON.data/1)
    }
  end

  defp render_cart_item_discounts(_discounts) do
    %{
      discounts: []
    }
  end

  defp add_default_variation_type(%{variation_types: []} = variant) do
    %{variant | variation_types: [%{key: "default", value: "default", metadata: %{}}]}
  end

  defp add_default_variation_type(variant), do: variant
end
