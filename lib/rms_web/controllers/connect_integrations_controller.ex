defmodule RmsWeb.ConnectIntegrationsController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def create(conn, %{"integration" => "shopify"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    shopify_credential = Map.take(params, ["shop", "credential"])

    shopify_app =
      params
      |> Map.take(["client_id", "client_secret"])
      |> Map.put("shop_domain", params["shop"])

    storefront_credential = params["storefront_credential"]

    with {:ok, _} <-
           Rms.Integrations.connect_shopify(
             resource,
             shopify_credential,
             shopify_app,
             storefront_credential
           ) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "pagarme"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <- Rms.Integrations.create_pagarme_credential(resource, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "pagarme_hook"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <- Rms.Integrations.create_pagarme_hook_credential(resource, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "vtex"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <- Rms.Integrations.connect_vtex(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "gift_promo"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <-
           Rms.Integrations.create_gift_promo_credential(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "vinco"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <-
           Rms.Integrations.create_vinco_credential(resource.organization_id, params),
         {:ok, _} <-
           Rms.Integrations.create_default_order_cancelation_endpoint(
             resource.organization_id,
             "vinco"
           ) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "erp"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <-
           Rms.Integrations.create_erp_credential(resource, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end

  def create(conn, %{"integration" => "cielo"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, _} <-
           Rms.Integrations.create_cielo_credential(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> json(%{success: true})
    end
  end
end
