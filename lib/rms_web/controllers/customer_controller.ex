defmodule RmsWeb.CustomerController do
  use RmsWeb, :controller

  alias Rms.Customers
  alias Rms.Customers.Customer
  alias RmsWeb.SchemaValidator

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customers = Customers.list_customers(resource.organization_id, prepare_params(params))
    render(conn, :index, customers: customers)
  end

  def paginated_customers(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, params} <- SchemaValidator.validate(conn, :paginated_customers, params) do
      paginated_customers =
        Customers.paginated_customers(resource.organization_id, Keyword.new(params))

      render(conn, :index,
        customers: paginated_customers.entries,
        metadata: paginated_customers.metadata
      )
    end
  end

  def create(conn, %{"customer" => customer_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %Customer{} = customer} <-
           Customers.create_customer(resource.organization_id, customer_params) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/customers/#{customer}")
      |> render(:show, customer: customer)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer = Customers.get_customer!(resource.organization_id, id)
    render(conn, :show, customer: customer)
  end

  def update(conn, %{"id" => id, "customer" => customer_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer = Customers.get_customer!(resource.organization_id, id)

    {_, customer_params} = Map.pop(customer_params, "addresses")

    with {:ok, %Customer{} = customer} <- Customers.update_customer(customer, customer_params) do
      render(conn, :show, customer: customer)
    end
  end

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer = Customers.get_customer!(resource.organization_id, id)

    with {:ok, %Customer{}} <- Customers.delete_customer(customer) do
      send_resp(conn, :no_content, "")
    end
  end

  @allowed_params ~w(document_type document email name primary_phone_number after before limit count search)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      case key do
        "document" ->
          {String.to_atom(key), Utils.only_digits(value)}

        "primary_phone_number" ->
          {String.to_atom(key), Utils.only_digits(value)}

        _ ->
          {String.to_atom(key), value}
      end
    end
  end
end
