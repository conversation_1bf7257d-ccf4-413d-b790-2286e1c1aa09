defmodule RmsWeb.CustomerIntegrationsController do
  use RmsWeb, :controller

  alias Rms.Integrations.Customers
  alias Rms.Integrations.Customers.CustomerEndpoint

  action_fallback RmsWeb.FallbackController

  def index(conn, _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer_endpoints = Customers.list_customer_endpoints(resource.organization_id)
    render(conn, :index, customer_endpoints: customer_endpoints)
  end

  def create(conn, %{"customer_endpoint" => customer_endpoint_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %CustomerEndpoint{} = customer_endpoint} <-
           Customers.create_customer_endpoint(resource.organization_id, customer_endpoint_params) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/integrations/customer/#{customer_endpoint}")
      |> render(:show, customer_endpoint: customer_endpoint)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer_endpoint = Customers.get_customer_endpoint!(resource.organization_id, id)

    render(conn, :show, customer_endpoint: customer_endpoint)
  end

  def update(conn, %{"id" => id, "customer_endpoint" => customer_endpoint_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer_endpoint = Customers.get_customer_endpoint!(resource.organization_id, id)

    with {:ok, %CustomerEndpoint{} = customer_endpoint} <-
           Customers.update_customer_endpoint(customer_endpoint, customer_endpoint_params) do
      render(conn, :show, customer_endpoint: customer_endpoint)
    end
  end

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    customer_endpoint = Customers.get_customer_endpoint!(resource.organization_id, id)

    with {:ok, %CustomerEndpoint{}} <- Customers.delete_customer_endpoint(customer_endpoint) do
      send_resp(conn, :no_content, "")
    end
  end
end
