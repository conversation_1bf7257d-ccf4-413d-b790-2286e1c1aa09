defmodule RmsWeb.CustomerIntegrationsJSON do
  def render("index.json", %{customer_endpoints: customer_endpoints}) do
    %{data: Enum.map(customer_endpoints, &data(&1))}
  end

  def render("show.json", %{customer_endpoint: customer_endpoint}) do
    %{data: data(customer_endpoint)}
  end

  defp data(customer_endpoint) do
    %{
      id: customer_endpoint.id,
      endpoint: customer_endpoint.endpoint,
      unique_on: customer_endpoint.unique_on,
      headers: customer_endpoint.headers,
      active: customer_endpoint.active,
      organization_id: customer_endpoint.organization_id,
      inserted_at: customer_endpoint.inserted_at,
      updated_at: customer_endpoint.updated_at
    }
  end
end
