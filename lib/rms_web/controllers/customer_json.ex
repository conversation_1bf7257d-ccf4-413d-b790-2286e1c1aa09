defmodule RmsWeb.CustomerJSON do
  alias Rms.Customers.Customer

  def render("customer.json", %{shopify_customer: customer}) do
    cpf = customer["cpf"]["value"]

    %{
      customer: %{
        name: customer["displayName"],
        document_type: if(cpf, do: "cpf", else: nil),
        document: cpf,
        email: customer["email"]
      }
    }
  end

  def render("customer.json", %{customer: customer}) do
    %{
      customer: data(customer)
    }
  end

  @doc """
  Renders a list of customers.
  """
  def index(%{customers: customers, metadata: metadata}) do
    %{
      data: for(customer <- customers, do: data(customer)),
      metadata: Map.take(metadata, [:after, :before, :limit, :total_count])
    }
  end

  def index(%{customers: customers}) do
    %{data: for(customer <- customers, do: data(customer))}
  end

  @doc """
  Renders a single customer.
  """
  def show(%{customer: customer}) do
    %{data: data(customer)}
  end

  def data(%Customer{} = customer) do
    %{
      id: customer.id,
      document_type: customer.document_type,
      document: customer.document,
      email: customer.email,
      name: customer.name,
      birthdate: customer.birthdate,
      primary_phone_number: customer.primary_phone_number,
      addresses: render_addresses(customer.addresses)
    }
  end

  def data(_), do: nil

  defp render_addresses(addresses) when is_list(addresses) do
    Enum.map(addresses, &RmsWeb.AddressJSON.render("show.json", %{address: &1}))
  end

  defp render_addresses(_), do: []
end
