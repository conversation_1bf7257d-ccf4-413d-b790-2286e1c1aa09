defmodule RmsWeb.DiscountsController do
  alias Rms.Commerce.Discounts
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def validate_authorization_request(conn, %{"id" => id, "code" => code}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    authorization_request = Discounts.get_authorization_request!(resource.organization_id, id)

    case Discounts.validate_token(authorization_request, code) do
      {:unauthorized, message} ->
        conn
        |> send_resp(401, message)

      {:error, message} ->
        conn
        |> send_resp(400, message)

      _ ->
        Discounts.update_authorization_request_status!(authorization_request, "accepted")

        conn
        |> send_resp(200, "")
    end
  end

  def create_authorization_request(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with authorization_request <-
           Discounts.create_authorization_request(resource.organization_id, params),
         {:ok, _} <- Discounts.send_authorization_request(authorization_request, params) do
      conn
      |> put_status(:ok)
      |> json(%{id: authorization_request.id})
    end
  end
end
