defmodule RmsWeb.DiscountsJSON do
  alias Rms.Commerce.Discounts.Discount
  alias Rms.Commerce.Discounts.LineItemDiscount
  alias Rms.Commerce.Discounts.CartItemDiscount

  def data(%Discount{} = discount) do
    %{
      type: discount.type,
      value: discount.value,
      description: discount.description
    }
  end

  def data(%LineItemDiscount{} = discount) do
    %{
      type: discount.type,
      value: discount.value,
      description: discount.description
    }
  end

  def data(%CartItemDiscount{} = discount) do
    %{
      type: discount.type,
      value: discount.value,
      description: discount.description
    }
  end

  def data(_), do: nil
end
