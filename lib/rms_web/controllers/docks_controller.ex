defmodule RmsWeb.DocksController do
  use RmsWeb, :controller

  alias Rms.Accounts
  alias Rms.Accounts.Dock

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts = prepare_params(params)

    page =
      Accounts.paginated_docks(resource.organization_id, opts)

    render(conn, "index.json", docks: page.entries, page_metadata: page.metadata)
  end

  def create(conn, dock_params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %Dock{} = dock} <-
           Accounts.create_dock(resource.organization_id, dock_params) do
      conn
      |> put_status(:created)
      |> render("show.json", dock: dock)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    dock =
      Accounts.get_dock!(resource.organization_id, id)

    render(conn, "show.json", dock: dock)
  end

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    dock = Accounts.get_dock!(resource.organization_id, id)

    with {:ok, %Dock{}} <- Accounts.delete_dock(dock) do
      send_resp(conn, :no_content, "")
    end
  end

  @allowed_params ~w(after before limit id name external_id location_id organization_id)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
