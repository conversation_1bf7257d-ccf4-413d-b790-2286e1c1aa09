defmodule RmsWeb.DocksJSON do
  def render("index.json", %{docks: docks, page_metadata: page_metadata}) do
    %{
      docks: Enum.map(docks, &render("show.json", %{dock: &1})),
      page: %{
        after: page_metadata.after,
        before: page_metadata.before,
        limit: page_metadata.limit
      }
    }
  end

  def render("show.json", %{dock: dock}) do
    data(dock)
  end

  def data(dock) do
    %{
      id: dock.id,
      name: dock.name,
      external_id: dock.external_id,
      organization_id: dock.organization_id,
      location_id: dock.location_id,
      inserted_at: dock.inserted_at
    }
  end
end
