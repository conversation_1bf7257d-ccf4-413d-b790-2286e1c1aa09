defmodule RmsWeb.FallbackController do
  @moduledoc """
  Translates controller action results into valid `Plug.Conn` responses.

  See `Phoenix.Controller.action_fallback/1` for more details.
  """
  use RmsWeb, :controller

  require Logger

  defp log_changeset_error(changeset) do
    struct_name = Map.get(changeset.data, :__struct__, "unknown")
    struct_id = Map.get(changeset.data, :id, "unknown")
    changeset_errors = Ecto.Changeset.traverse_errors(changeset, &translate_error/1)

    Logger.error(
      "Constraints violated for #{struct_name} id: #{struct_id} errors: #{inspect(changeset_errors)}"
    )
  end

  defp translate_error({msg, opts}) do
    Enum.reduce(opts, msg, fn {key, value}, acc ->
      String.replace(acc, "%{#{key}}", fn _ -> to_string(value) end)
    end)
  end

  # This clause handles errors returned by Ecto's insert/update/delete.
  def call(conn, {:error, %Ecto.Changeset{} = changeset}) do
    log_changeset_error(changeset)

    conn
    |> put_status(:unprocessable_entity)
    |> put_view(json: RmsWeb.ChangesetJSON)
    |> render(:error, changeset: changeset)
  end

  # This clause handles errors returned by Ecto.Multi's insert/update/delete/run.
  def call(conn, {:error, _, %Ecto.Changeset{} = changeset, _}) do
    log_changeset_error(changeset)

    conn
    |> put_status(:unprocessable_entity)
    |> put_view(json: RmsWeb.ChangesetJSON)
    |> render(:error, changeset: changeset)
  end

  # This clause is an example of how to handle resources that cannot be found.
  def call(conn, {:error, :not_found}) do
    conn
    |> put_status(:not_found)
    |> put_view(html: RmsWeb.ErrorHTML, json: RmsWeb.ErrorJSON)
    |> render(:"404")
  end

  # This clause handles the forbidden error
  def call(conn, {:error, :forbidden}) do
    conn
    |> put_status(:forbidden)
    |> put_view(html: RmsWeb.ErrorHTML, json: RmsWeb.ErrorJSON)
    |> render(:"403")
  end

  # This clause handles the unauthorized error
  def call(conn, {:error, :unauthorized}) do
    conn
    |> put_status(:unauthorized)
    |> put_view(html: RmsWeb.ErrorHTML, json: RmsWeb.ErrorJSON)
    |> render(:"401")
  end

  # This clause handles the unprocessable entity error
  def call(conn, {:error, :unprocessable_entity}) do
    conn
    |> put_status(:unprocessable_entity)
    |> put_view(html: RmsWeb.ErrorHTML, json: RmsWeb.ErrorJSON)
    |> render(:"422")
  end

  # This clause handles all other error tuples as a bad request
  def call(conn, {:error, _reason}) do
    conn
    |> put_status(:bad_request)
    |> put_view(html: RmsWeb.ErrorHTML, json: RmsWeb.ErrorJSON)
    |> render(:"400")
  end
end
