defmodule RmsWeb.Fiscal.FiscalInvoiceController do
  use RmsWeb, :controller

  alias Rms.Fiscal
  alias Rms.Emails.FiscalInvoice

  require Logger

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    fiscal_invoices =
      Fiscal.list_fiscal_invoice(resource.organization_id,
        query_params: params
      )

    case Map.get(params, "format_type") do
      "none" ->
        render(conn, "raw_index.json", invoices: fiscal_invoices)

      _ ->
        render(conn, "index.json", invoices: fiscal_invoices)
    end
  end

  def show(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    fiscal_invoice = Fiscal.get_fiscal_invoice!(resource.organization_id, id)

    case Map.get(params, "format_type") do
      "none" ->
        render(conn, "raw_show.json", invoice: fiscal_invoice)

      _ ->
        render(conn, "show.json", invoice: fiscal_invoice)
    end
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    invoice = Fiscal.get_fiscal_invoice!(resource.organization_id, id)

    with {:ok, updated_invoice} <- Fiscal.update_fiscal_invoice(invoice, params) do
      render(conn, "show.json", invoice: updated_invoice)
    end
  end

  def import_fiscal_invoice(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    order =
      Rms.Commerce.Orders.get_order!(resource.organization_id, params["order_id"], [:customer])

    case Fiscal.import_fiscal_invoice(order, params) do
      {:ok, %{operation_type: "sale"} = fiscal_invoice} ->
        send_fiscal_invoice_to_customer(fiscal_invoice, order.customer)

        conn
        |> put_status(:created)
        |> render("show.json", invoice: fiscal_invoice)

      {:ok, _fiscal_invoice} ->
        conn
        |> put_status(:created)
        |> json(%{success: true})

      error ->
        error
    end
  end

  def import_vinco_fiscal_invoice(conn, %{"id" => id, "df_key" => df_key} = _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    invoice = Fiscal.get_fiscal_invoice!(resource.organization_id, id)

    with {:ok, updated_invoice} <-
           Rms.Integrations.Vinco.ImportFiscalInvoice.execute(invoice, df_key) do
      render(conn, "raw_show.json", invoice: updated_invoice)
    end
  end

  def import_fulfillment_invoice(conn, %{"fulfillment_id" => fulfillment_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    fiscal_invoice_xml = Map.get(params, "fiscal_invoice_xml")

    fulfillment =
      Rms.Commerce.Fulfillments.get_fulfillment!(resource.organization_id, fulfillment_id)

    with {:ok, fiscal_invoice} <-
           Fiscal.import_fulfillment_fiscal_invoice(
             resource.organization_id,
             fulfillment,
             fiscal_invoice_xml
           ) do
      conn
      |> put_status(:created)
      |> render("show.json", invoice: fiscal_invoice)
    end
  end

  def reprocess_fiscal_invoice(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    Logger.info(
      "reprocessing invoice for invoice_id #{id} and organization_id #{resource.organization_id}"
    )

    case Fiscal.reprocess_fiscal_invoice(resource.organization_id, id) do
      {:ok, _fiscal_invoice} ->
        conn
        |> put_status(:created)
        |> json(%{success: true})

      _ ->
        Logger.error(
          "failed to reprocess invoice for invoice_id #{id} and organization_id #{resource.organization_id}"
        )

        {:error, :unprocessable_entity}
    end
  end

  defp send_fiscal_invoice_to_customer(fiscal_invoice, customer) when not is_nil(customer) do
    FiscalInvoice.send_invoice_email(fiscal_invoice, customer.email)
  end

  defp send_fiscal_invoice_to_customer(_fiscal_invoice, _customer), do: :ok

  def send_email(conn, %{"id" => id, "email" => email}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    Logger.info(
      "sending email for invoice_id #{id} and organization_id #{resource.organization_id}"
    )

    invoice = Fiscal.get_fiscal_invoice!(resource.organization_id, id)

    case FiscalInvoice.send_invoice_email(invoice, email) do
      {:ok, _email} ->
        conn
        |> put_status(:ok)
        |> json(%{message: "email sent successfully"})

      {:error, reason} ->
        Logger.error("Failed to send invoice email: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
