defmodule RmsWeb.Fiscal.FiscalInvoiceJSON do
  def render("index.json", %{invoices: invoices}) do
    invoices_list =
      Enum.reduce(invoices, [], fn invoice, acc ->
        case render("show.json", %{invoice: invoice}) do
          nil -> acc
          rendered_invoice -> [rendered_invoice | acc]
        end
      end)
      |> Enum.reverse()

    %{invoices: invoices_list}
  end

  def render("raw_index.json", %{invoices: invoices}) do
    invoices_list =
      Enum.reduce(invoices, [], fn invoice, acc ->
        [data(invoice) | acc]
      end)
      |> Enum.reverse()

    %{invoices: invoices_list}
  end

  def render("raw_show.json", %{invoice: invoice}) do
    invoice =
      Rms.Repo.preload(invoice, [
        :customer,
        :organization,
        :fulfillment,
        reverse_fulfillment: [:staff, customer: [:addresses]],
        invoice_payments: [:payment],
        invoice_items: [:line_item],
        serie: [location: [:address]]
      ])

    data(invoice)
  end

  def render("show.json", %{invoice: invoice}) do
    invoice =
      Rms.Repo.preload(invoice, [
        :customer,
        :organization,
        :fiscal_invoice_errors,
        invoice_items: [line_item: [product_variant: [:product]]],
        invoice_payments: [:payment],
        serie: [:location]
      ])

    build(invoice)
  end

  defp format_fiscal_invoice_errors(fiscal_invoice_errors) do
    Enum.map(fiscal_invoice_errors, fn fiscal_invoice_error ->
      %{
        "id" => fiscal_invoice_error.id,
        "reason" => fiscal_invoice_error.reason,
        "status" => fiscal_invoice_error.status,
        "stacktrace" => fiscal_invoice_error.stacktrace,
        "attempt" => fiscal_invoice_error.attempt
      }
    end)
  end

  defp build(%{xml: nil, fiscal_invoice_errors: fiscal_invoice_errors} = _invoice)
       when is_nil(fiscal_invoice_errors) or fiscal_invoice_errors == [] do
    nil
  end

  defp build(%{xml: nil} = invoice) do
    %{
      "id" => invoice.id,
      "errors" => format_fiscal_invoice_errors(invoice.fiscal_invoice_errors),
      "nfce" => nil
    }
  end

  defp build(%{xml: xml} = invoice) when not is_nil(xml) do
    parsed_xml = Floki.parse_document!(xml)

    %{
      "id" => invoice.id,
      "paymentList" => format_payments_list(invoice.invoice_payments),
      "errors" => format_fiscal_invoice_errors(invoice.fiscal_invoice_errors),
      "nfce" => %{
        "logoUrl" => get_logo(invoice.serie),
        "name" => Floki.find(parsed_xml, "emit xnome") |> Floki.text(),
        "cnpj" => Floki.find(parsed_xml, "emit cnpj") |> Floki.text(),
        "address" =>
          "#{Floki.find(parsed_xml, "enderemit xlgr") |> Floki.text()}, #{Floki.find(parsed_xml, "enderemit nro") |> Floki.text()}, #{Floki.find(parsed_xml, "enderemit xbairro") |> Floki.text()}, #{Floki.find(parsed_xml, "enderemit xmun") |> Floki.text()}, #{Floki.find(parsed_xml, "enderemit uf") |> Floki.text()} - #{Floki.find(parsed_xml, "enderemit cep") |> Floki.text()}",
        "totals" => %{
          "value" => Floki.find(parsed_xml, "icmstot vnf") |> Floki.text(),
          "discounts" => Floki.find(parsed_xml, "icmstot vdesc") |> Floki.text(),
          "additions" => Floki.find(parsed_xml, "icmstot voutro") |> Floki.text(),
          "paid" => Floki.find(parsed_xml, "icmstot vnf") |> Floki.text()
        },
        "paymentList" => format_vinco_payments(Floki.find(parsed_xml, "detpag")),
        "customer" => render_customer(parsed_xml),
        "items" => format_vinco_items(Floki.find(parsed_xml, "det")),
        "nfc" => %{
          "accessKey" => invoice.df_key,
          "accessUrl" => Floki.find(parsed_xml, "urlchave") |> Floki.text(),
          "number" => Floki.find(parsed_xml, "ide nnf") |> Floki.text(),
          "serie" => Floki.find(parsed_xml, "ide serie") |> Floki.text(),
          "dateTime" => Floki.find(parsed_xml, "ide dhemi") |> Floki.text(),
          "authorization" => %{
            "protocol" => invoice.metadata["DFeProtocolo"],
            "dateTime" => Floki.find(parsed_xml, "ide dhemi") |> Floki.text()
          }
        },
        "qrcode" => invoice.qr_code,
        "tax" => %{
          "value" => format_vinco_taxes(Floki.find(parsed_xml, "icmstot"))
        },
        "extraInfo" => Floki.find(parsed_xml, "infadic infcpl") |> Floki.text(),
        "logoBase64" => nil
      }
    }
  end

  defp build(_), do: nil

  defp render_customer(parsed_xml) do
    address = Floki.find(parsed_xml, "dest enderdest") |> Floki.text()

    case address do
      "" ->
        %{
          "name" => Floki.find(parsed_xml, "dest xnome") |> Floki.text(),
          "cpf" => Floki.find(parsed_xml, "dest cpf") |> Floki.text(),
          "email" => Floki.find(parsed_xml, "dest email") |> Floki.text()
        }

      _ ->
        %{
          "name" => Floki.find(parsed_xml, "dest xnome") |> Floki.text(),
          "cpf" => Floki.find(parsed_xml, "dest cpf") |> Floki.text(),
          "email" => Floki.find(parsed_xml, "dest email") |> Floki.text(),
          "address" => render_customer_address(parsed_xml)
        }
    end
  end

  defp render_customer_address(parsed_xml) do
    %{
      "street" => Floki.find(parsed_xml, "dest enderdest xlgr") |> Floki.text(),
      "number" => Floki.find(parsed_xml, "dest enderdest nro") |> Floki.text(),
      "complement" => Floki.find(parsed_xml, "dest enderdest xcpl") |> Floki.text(),
      "neighborhood" => Floki.find(parsed_xml, "dest enderdest xbairro") |> Floki.text(),
      "cityCode" => Floki.find(parsed_xml, "dest enderdest cmun") |> Floki.text(),
      "cityName" => Floki.find(parsed_xml, "dest enderdest xmun") |> Floki.text(),
      "state" => Floki.find(parsed_xml, "dest enderdest uf") |> Floki.text(),
      "zip" => Floki.find(parsed_xml, "dest enderdest cep") |> Floki.text(),
      "countryName" => Floki.find(parsed_xml, "dest enderdest xpais") |> Floki.text()
    }
  end

  defp format_vinco_payments(vinco_payments) do
    Enum.map(vinco_payments, fn payment ->
      tpag = Floki.find(payment, "tpag") |> Floki.text()
      vpag = Floki.find(payment, "vpag") |> Floki.text()

      case tpag do
        "17" -> %{"method" => "Pagamento Instantâneo (PIX)", "amount" => vpag}
        "03" -> %{"method" => "Cartão de Crédito", "amount" => vpag}
        "04" -> %{"method" => "Cartão de Débito", "amount" => vpag}
        "01" -> %{"method" => "Dinheiro", "amount" => vpag}
        _ -> %{"method" => "Outros", "amount" => vpag}
      end
    end)
  end

  defp format_vinco_items(vinco_items) do
    Enum.map(vinco_items, fn item ->
      %{
        "sku" => Floki.find(item, "prod cprod") |> Floki.text(),
        "name" => Floki.find(item, "prod xprod") |> Floki.text(),
        "quantity" => Floki.find(item, "prod qcom") |> Floki.text(),
        "unitValue" => Floki.find(item, "prod vuncom") |> Floki.text(),
        "totalValue" => Floki.find(item, "prod vprod") |> Floki.text()
      }
    end)
  end

  defp format_vinco_taxes(vinco_taxes) do
    vpis = Floki.find(vinco_taxes, "vpis") |> Floki.text() |> Decimal.new()
    vcofins = Floki.find(vinco_taxes, "vcofins") |> Floki.text() |> Decimal.new()
    vicms = Floki.find(vinco_taxes, "vicms") |> Floki.text() |> Decimal.new()

    Decimal.add(vpis, vcofins)
    |> Decimal.add(vicms)
  end

  defp format_payments_list(invoice_payments) do
    Enum.map(invoice_payments, fn invoice_payment ->
      payment = invoice_payment.payment

      if payment.method in ["credit_card", "debit_card"] do
        %{
          "amountCents" => Decimal.mult(Decimal.new("100"), payment.amount),
          "method" => payment.method,
          "installments" => "1",
          "nsu" => payment.metadata["nsu"],
          "aut" => payment.metadata["aut"]
        }
      else
        %{
          "amountCents" => Decimal.mult(Decimal.new("100"), payment.amount),
          "method" => payment.method
        }
      end
    end)
  end

  defp get_logo(_serie), do: nil

  defp data(invoice) do
    RmsWeb.RetailManagement.RetailFiscalInvoicesJSON.detail_data(invoice)
  end
end
