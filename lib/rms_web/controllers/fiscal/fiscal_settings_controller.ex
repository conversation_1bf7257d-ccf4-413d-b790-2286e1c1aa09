defmodule RmsWeb.Fiscal.FiscalSettingsController do
  use RmsWeb, :controller

  alias Rms.Fiscal

  action_fallback RmsWeb.FallbackController

  def create(conn, %{"location_id" => location_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, fiscal_settings} <-
           Fiscal.create_location_fiscal_settings(resource.organization_id, location_id, params) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/fiscal/settings/#{location_id}")
      |> render(:show, fiscal_settings: fiscal_settings)
    end
  end

  def show(conn, %{"location_id" => location_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, settings} <- get_fiscal_settings(resource.organization_id, location_id) do
      render(conn, :show, fiscal_settings: settings)
    end
  end

  def update(conn, %{"location_id" => location_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, settings} <- get_fiscal_settings(resource.organization_id, location_id),
         {:ok, updated_settings} <-
           Fiscal.update_fiscal_settings(settings, params) do
      render(conn, :show, fiscal_settings: updated_settings)
    end
  end

  defp get_fiscal_settings(organization_id, location_id) do
    case Fiscal.get_fiscal_settings(organization_id, location_id) do
      nil -> {:error, :not_found}
      fiscal_settings -> {:ok, fiscal_settings}
    end
  end
end
