defmodule RmsWeb.Fiscal.FiscalSettingsJSON do
  alias Rms.Fiscal.FiscalSettings

  def show(%{fiscal_settings: fiscal_settings}) do
    %{
      data: data(fiscal_settings)
    }
  end

  def data(%FiscalSettings{} = fiscal_settings) do
    %{
      id: fiscal_settings.id,
      handle_sale: fiscal_settings.handle_sale,
      handle_return: fiscal_settings.handle_return,
      handle_transfer: fiscal_settings.handle_transfer,
      environment: fiscal_settings.environment,
      location_id: fiscal_settings.location_id,
      inserted_at: fiscal_settings.inserted_at,
      updated_at: fiscal_settings.updated_at
    }
  end

  def data(nil), do: nil
end
