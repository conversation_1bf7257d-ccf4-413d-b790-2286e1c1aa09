defmodule RmsWeb.Fiscal.InvoiceSerieController do
  use RmsWeb, :controller

  alias Rms.Fiscal
  alias Rms.Fiscal.InvoiceSerie

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    series =
      Fiscal.list_invoice_serie(resource.organization_id,
        query_params: params
      )

    render(conn, "index.json", series: series)
  end

  @spec create(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %InvoiceSerie{} = serie} <- Fiscal.create_serie(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> render("show.json", serie: serie)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    serie = Fiscal.get_invoice_serie!(resource.organization_id, id)

    render(conn, "show.json", serie: serie)
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    serie = Fiscal.get_invoice_serie!(resource.organization_id, id)

    with {:ok, %InvoiceSerie{} = updated_serie} <- Fiscal.update_invoice_serie(serie, params) do
      render(conn, "show.json", serie: updated_serie)
    end
  end
end
