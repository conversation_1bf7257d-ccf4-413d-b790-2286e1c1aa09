defmodule RmsWeb.Fiscal.InvoiceSerieJSON do
  def render("index.json", %{series: series}) do
    %{
      series: Enum.map(series, &render("show.json", %{serie: &1}))
    }
  end

  def render("show.json", %{serie: serie}) do
    %{
      id: serie.id,
      status: serie.status,
      type: serie.invoice_type,
      env: serie.invoice_env,
      serie_number: serie.invoice_serie,
      available_number: serie.available_number,
      location_id: serie.location_id
    }
  end
end
