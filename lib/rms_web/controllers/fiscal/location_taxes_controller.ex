defmodule RmsWeb.Fiscal.LocationTaxesController do
  use RmsWeb, :controller

  alias Rms.Fiscal.LocationTax
  alias Rms.Fiscal

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    location_taxes =
      Fiscal.list_location_tax(resource.organization_id,
        query_params: params
      )

    render(conn, "index.json", location_taxes: location_taxes)
  end

  @spec create(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %LocationTax{} = location_tax} <-
           Fiscal.create_location_tax(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> render("show.json", location_tax: location_tax)
    end
  end

  def show(conn, %{"location_id" => location_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    location_tax = Fiscal.get_location_tax_by_location!(resource.organization_id, location_id)

    render(conn, "show.json", location_tax: location_tax)
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    location_tax = Fiscal.get_location_tax!(resource.organization_id, id)

    render(conn, "show.json", location_tax: location_tax)
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    location_tax = Fiscal.get_location_tax!(resource.organization_id, id)

    with {:ok, %LocationTax{} = updated_location_tax} <-
           Fiscal.update_location_tax(location_tax, params) do
      render(conn, "show.json", location_tax: updated_location_tax)
    end
  end
end
