defmodule RmsWeb.Fiscal.LocationTaxesJSON do
  def render("index.json", %{location_taxes: location_taxes}) do
    %{
      location_taxes: Enum.map(location_taxes, &render("show.json", %{location_tax: &1}))
    }
  end

  def render("show.json", %{location_tax: location_tax}) do
    %{
      id: location_tax.id,
      ie: location_tax.ie,
      crt: location_tax.crt,
      name: location_tax.name,
      current_tax_uf: location_tax.current_tax_uf
    }
  end
end
