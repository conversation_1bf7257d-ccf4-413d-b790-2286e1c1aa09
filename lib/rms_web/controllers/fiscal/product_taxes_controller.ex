defmodule RmsWeb.Fiscal.ProductTaxesController do
  use RmsWeb, :controller

  alias Rms.Fiscal.ProductTaxes
  alias Rms.Fiscal

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    product_taxes =
      Fiscal.list_product_taxes(resource.organization_id,
        query_params: params
      )

    render(conn, "index.json", product_taxes: product_taxes)
  end

  @spec create(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %ProductTaxes{} = product_tax} <-
           Fiscal.create_product_taxes(resource.organization_id, params) do
      conn
      |> put_status(:created)
      |> render("show.json", product_tax: product_tax)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product_tax = Fiscal.get_product_taxes!(resource.organization_id, id)

    render(conn, "show.json", product_tax: product_tax)
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product_tax = Fiscal.get_product_taxes!(resource.organization_id, id)

    with {:ok, %ProductTaxes{} = updated_product_tax} <-
           Fiscal.update_product_taxes(product_tax, params) do
      render(conn, "show.json", product_tax: updated_product_tax)
    end
  end

  def delete(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product_tax = Fiscal.get_product_taxes!(resource.organization_id, id)

    with {:ok, %ProductTaxes{}} <- Fiscal.delete_product_taxes(product_tax) do
      send_resp(conn, :no_content, "")
    end
  end
end
