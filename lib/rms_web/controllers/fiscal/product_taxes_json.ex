defmodule RmsWeb.Fiscal.ProductTaxesJSON do
  def render("index.json", %{product_taxes: product_taxes}) do
    %{
      product_taxes: Enum.map(product_taxes, &render("show.json", %{product_tax: &1}))
    }
  end

  def render("show.json", %{product_tax: product_tax}) do
    %{
      id: product_tax.id,
      ncm: product_tax.ncm,
      sku: product_tax.sku,
      uf: product_tax.uf,
      origin: product_tax.origin,
      cfop: product_tax.cfop,
      cest: product_tax.cest,
      cst_icms: product_tax.cst_icms,
      cst_pis: product_tax.cst_pis,
      cst_cofins: product_tax.cst_cofins,
      icms_percentage: product_tax.icms_percentage,
      fcp_percentage: product_tax.fcp_percentage,
      pis_percentage: product_tax.pis_percentage,
      cofins_percentage: product_tax.cofins_percentage
    }
  end
end
