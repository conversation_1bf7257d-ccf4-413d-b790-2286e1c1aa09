defmodule RmsWeb.Fiscal.UploadController do
  use RmsWeb, :controller

  require Logger

  alias Rms.Fiscal
  alias Rms.Workers.ConfirmProductTaxUploadWorker

  action_fallback RmsWeb.FallbackController

  def create_upload_request(conn, _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    case Fiscal.create_product_tax_upload_request(resource.organization_id) do
      {:ok, %{url: url, fields: fields, upload: upload}} ->
        # Schedule confirmation job
        %{upload_id: upload.id, organization_id: upload.organization_id}
        |> ConfirmProductTaxUploadWorker.new()
        |> Oban.insert()

        conn
        |> put_status(:created)
        |> json(%{
          url: url,
          fields: fields,
          upload_id: upload.id,
          expires_at: upload.expires_at
        })

      {:error, reason} ->
        Logger.error("failed to generate upload url: #{inspect(reason)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: "failed to generate upload url"})
    end
  end

  def confirm_upload(conn, %{"id" => upload_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    upload = Rms.Storage.get_upload_entry!(resource.organization_id, upload_id)

    case Fiscal.confirm_product_tax_upload(upload) do
      {:ok, :processing} ->
        json(conn, %{status: "processing"})

      {:error, :not_found} ->
        json(conn, %{status: "pending"})

      {:error, :expired} ->
        json(conn, %{status: "expired"})

      {:error, :invalid_status, status} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: "cannot confirm upload in #{status} status"})

      {:error, reason} ->
        Logger.error("failed to confirm upload: #{inspect(reason)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{error: "failed to confirm upload"})
    end
  end
end
