defmodule RmsWeb.FulfillmentsJSON do
  alias Rms.Commerce.Fulfillments.Fulfillment

  def data(%Fulfillment{} = fulfillment) do
    %{
      shipping_method: fulfillment.shipping_method,
      external_reference: fulfillment.external_reference,
      status: fulfillment.status,
      line_items: render_line_items(fulfillment)
    }
  end

  defp render_line_items(%{line_items: line_items}) do
    Enum.map(line_items, &render_line_item/1)
  end

  defp render_line_items(_), do: nil

  defp render_line_item(line_item) do
    RmsWeb.OrderJSON.render_line_item(line_item)
  end
end
