defmodule RmsWeb.GiftCardsController do
  use RmsWeb, :controller

  action_fallback RmsWeb.GiftCardsFallback

  alias Rms.Finance.GiftCards
  alias RmsWeb.SchemaValidator

  def check_balance(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, params} <- SchemaValidator.validate(conn, :check_balance, params),
         opts = Map.to_list(params),
         {provider, opts} = Keyword.pop!(opts, :provider),
         {card_number, opts} = Keyword.pop!(opts, :card_number),
         {:ok, balance} <-
           GiftCards.check_balance(resource.organization_id, provider, card_number, opts) do
      conn
      |> put_status(:ok)
      |> json(%{balance: balance})
    end
  end
end
