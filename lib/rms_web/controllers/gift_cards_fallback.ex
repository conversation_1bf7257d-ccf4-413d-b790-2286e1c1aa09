defmodule RmsWeb.GiftCardsFallback do
  use RmsWeb, :controller

  require Logger

  def call(conn, {:error, :card_not_found}) do
    conn
    |> put_status(:not_found)
    |> json(%{error: %{code: "CARD_NOT_FOUND"}})
  end

  def call(conn, {:error, :card_not_active}) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: %{code: "CARD_NOT_ACTIVE"}})
  end

  def call(conn, {:error, :card_expired}) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: %{code: "CARD_EXPIRED"}})
  end

  def call(conn, {:error, :card_canceled}) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: %{code: "CARD_CANCELED"}})
  end

  def call(conn, {:error, :wrong_card_pin}) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: %{code: "WRONG_CARD_PIN"}})
  end

  def call(conn, {:error, %Ecto.Changeset{}} = error),
    do: RmsWeb.FallbackController.call(conn, error)

  def call(conn, {:error, reason}) do
    Logger.error("unexpected error from gift cards controller #{inspect(reason)}")

    conn
    |> put_status(:internal_server_error)
    |> json(%{error: %{code: "INTERNAL_SERVER_ERROR"}})
  end
end
