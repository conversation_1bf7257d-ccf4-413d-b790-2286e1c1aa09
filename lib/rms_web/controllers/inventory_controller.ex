defmodule RmsWeb.InventoryController do
  use RmsWeb, :controller

  alias Rms.Commerce.Products

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id

    params = prepare_params(params)

    inventory_items =
      Products.list_inventory_items(organization_id, query_params: params)

    render(conn, :index, inventory_items: inventory_items)
  end

  def show(conn, %{"id" => id}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    inventory_item = Products.get_inventory_item!(organization_id, id)

    render(conn, :show, inventory_item: inventory_item)
  end

  def show_by_sku(conn, %{"sku" => sku}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    inventory_item = Products.get_inventory_item_by_sku!(organization_id, sku)

    render(conn, :show, inventory_item: inventory_item)
  end

  def create(conn, %{"inventory_item" => inventory_item_attrs}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id

    with {:ok, inventory_item} <-
           Products.create_inventory_item(organization_id, inventory_item_attrs) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", ~p"/api/inventory/#{inventory_item}")
      |> render(:show, inventory_item: inventory_item)
    end
  end

  def update(conn, %{"id" => id, "inventory_item" => inventory_item_attrs}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    inventory_item = Products.get_inventory_item!(organization_id, id)

    with {:ok, inventory_item} <-
           Products.update_inventory_item(inventory_item, inventory_item_attrs) do
      render(conn, :show, inventory_item: inventory_item)
    end
  end

  def update_by_sku(conn, %{"sku" => sku, "inventory_item" => inventory_item_attrs}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    inventory_item = Products.get_inventory_item_by_sku!(organization_id, sku)

    with {:ok, inventory_item} <-
           Products.update_inventory_item(inventory_item, inventory_item_attrs) do
      render(conn, :show, inventory_item: inventory_item)
    end
  end

  def create_by_sku(conn, %{"sku" => sku, "inventory_item" => inventory_item_attrs}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id

    with {:ok, inventory_item} <-
           Products.create_inventory_item_by_sku(organization_id, sku, inventory_item_attrs) do
      conn
      |> put_status(:created)
      |> render(:show, inventory_item: inventory_item)
    end
  end

  def create_bulk_by_sku(conn, %{"items" => items}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id

    with {_, results} <- Products.create_inventory_items_by_sku(organization_id, items) do
      conn
      |> put_status(:created)
      |> render(:bulk_create, results: results)
    end
  end

  def update_bulk_by_sku(conn, %{"items" => items}) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id

    case Products.update_inventory_items_by_sku(organization_id, items) do
      {:ok, results} ->
        render(conn, :bulk_update, results: results)

      {:error, {_, sku}, changeset, _} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:bulk_update, changeset: changeset, sku: sku)
    end
  end

  @allowed_params ~w(after before limit location_id product_variant_id product_variant_ids)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
