defmodule RmsWeb.InventoryJSON do
  alias Rms.Commerce.Products.InventoryItem

  def index(%{inventory_items: inventory_items}) do
    %{data: for(inventory_item <- inventory_items, do: data(inventory_item))}
  end

  def show(%{inventory_item: inventory_item}) do
    %{data: data(inventory_item)}
  end

  def bulk_create(%{results: results}) do
    %{data: for(inventory_item <- results, do: data(inventory_item))}
  end

  def bulk_update(%{results: results}) do
    %{data: for({_sku, %InventoryItem{} = inventory_item} <- results, do: data(inventory_item))}
  end

  def bulk_update(%{changeset: changeset, sku: sku}) do
    %{errors: [%{sku: sku, errors: error_data(changeset)}]}
  end

  defp data(%{} = inventory_item) do
    %{
      id: inventory_item.id,
      quantity: inventory_item.quantity,
      location_id: inventory_item.location_id,
      product_variant_id: inventory_item.product_variant_id
    }
  end

  defp error_data(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
        opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
      end)
    end)
  end
end
