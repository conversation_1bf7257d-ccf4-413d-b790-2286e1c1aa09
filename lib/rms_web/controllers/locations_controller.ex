defmodule RmsWeb.LocationsController do
  use RmsWeb, :controller

  alias Rms.Accounts
  alias Rms.Accounts.Location

  action_fallback RmsWeb.FallbackController

  @preloads [:address, :location_tax]

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts = prepare_params(params) ++ [preload: @preloads]

    page =
      Accounts.paginated_locations(resource.organization_id, opts)

    render(conn, "index.json", locations: page.entries, page_metadata: page.metadata)
  end

  def create(conn, location_params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %Location{} = location} <-
           Accounts.create_location(resource.organization_id, location_params) do
      conn
      |> put_status(:created)
      |> render("show.json", location: location)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    location =
      Accounts.get_location!(resource.organization_id, id, preload: @preloads)

    render(conn, "show.json", location: location)
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    location =
      Accounts.get_location!(resource.organization_id, id, preload: @preloads)

    with {:ok, %Location{} = updated_location} <- Accounts.update_location(location, params) do
      render(conn, "show.json", location: updated_location)
    end
  end

  def delete(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    location = Accounts.get_location!(resource.organization_id, id, allow_archived?: true)

    delete_fn =
      if Map.get(params, "hard", false) do
        &Accounts.delete_location/1
      else
        &Accounts.archive_location/1
      end

    with {:ok, %Location{}} <- delete_fn.(location) do
      send_resp(conn, :no_content, "")
    end
  end

  @allowed_params ~w(after before limit allow_archived name cnpj preload)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
