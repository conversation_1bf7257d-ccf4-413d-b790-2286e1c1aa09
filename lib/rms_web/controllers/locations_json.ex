defmodule RmsWeb.LocationsJSON do
  alias Rms.Fiscal.LocationTax

  def render("index.json", %{locations: locations, page_metadata: page_metadata}) do
    %{
      locations: Enum.map(locations, &render("show.json", %{location: &1})),
      page: %{
        after: page_metadata.after,
        before: page_metadata.before,
        limit: page_metadata.limit
      }
    }
  end

  def render("show.json", %{location: location}) do
    data(location)
  end

  def data(location) do
    %{
      id: location.id,
      name: location.name,
      cnpj: location.cnpj,
      organization_id: location.organization_id,
      archived_at: location.archived_at,
      inserted_at: location.inserted_at,
      updated_at: location.updated_at,
      address: location.address,
      location_tax: render_location_tax(location.location_tax)
    }
  end

  defp render_location_tax(%LocationTax{} = location_tax) do
    RmsWeb.Fiscal.LocationTaxesJSON.render("show.json", %{location_tax: location_tax})
  end

  defp render_location_tax(_), do: nil
end
