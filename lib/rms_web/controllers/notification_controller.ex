defmodule RmsWeb.NotificationController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def create_push_notification(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts = prepare_params(params)

    case Rms.Notifications.send_push_notification(resource.organization_id, opts) do
      {:ok, _} ->
        conn
        |> put_status(:created)
        |> json(%{message: "Notification sent successfully"})

      {:error, :invalid_staffs} ->
        conn
        |> put_status(:bad_request)
        |> json(%{message: "Invalid staffs_ids"})

      error ->
        error
    end
  end

  @allowed_params ~w(message title staffs_ids url channel_id)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
