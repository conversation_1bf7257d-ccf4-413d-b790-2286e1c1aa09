defmodule RmsWeb.OrderController do
  alias Rms.Commerce.Orders
  alias Rms.Commerce.Workflows.OrderCancellationWorkflow
  alias RmsWeb.SchemaValidator

  alias Oban.Pro.Relay

  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  @preloads [
    :discounts,
    :order_customer,
    :staff,
    :cashier,
    :location,
    transaction: [:payments],
    customer: [:addresses],
    fulfillments: [line_items: [:discounts]]
  ]

  @cart_preloads [
    :discounts,
    :shipping_address,
    :cart_addons,
    delivery_groups: [cart_items: [:discounts, product_variant: :product]]
  ]

  def index(conn, params) do
    with {:ok, valid_params} <- SchemaValidator.validate(conn, :index, params) do
      resource =
        Rms.Guardian.Plug.current_resource(conn)

      opts =
        valid_params
        |> Map.to_list()
        |> Keyword.put(:preloads, @preloads)

      page =
        Orders.paginated_orders(resource.organization_id, opts)

      render(conn, :index, orders: page.entries, page_metadata: page.metadata)
    end
  end

  def show(conn, %{"id" => "gid://shopify/Order/" <> _ = id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, order} <- Orders.create_new(resource.organization_id, id) do
      order = Rms.Repo.preload(order, @preloads)

      conn
      |> put_status(:ok)
      |> render("order.json", order: order)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    order = Orders.get_order!(resource.organization_id, id, @preloads)

    conn
    |> put_status(:ok)
    |> render("order.json", order: order)
  end

  def get_by_df_key(conn, %{"df_key" => df_key}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    order =
      Orders.get_order_by_df_key!(resource.organization_id, df_key)
      |> Rms.Repo.preload(@preloads)

    render(conn, "order.json", order: order)
  end

  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    allowed_location_ids = Rms.Accounts.assigned_location_ids(resource)

    opts = [allowed_location_ids: allowed_location_ids]

    related_cart =
      Rms.Commerce.Carts.get_cart!(resource.organization_id, params["cart_id"], @cart_preloads)

    params =
      Rms.Commerce.Orders.format_cart(related_cart, params)

    opts = Keyword.put(opts, :cart, related_cart)

    with {:ok, order} <- Orders.create_order(resource.organization_id, params, opts) do
      order = Rms.Repo.preload(order, @preloads)

      conn
      |> put_status(:created)
      |> render("order.json", order: order)
    end
  end

  def update(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    order = Orders.get_order!(resource.organization_id, id)

    with {:ok, updated_order} <- Orders.update_order_name(order, params["name"]) do
      updated_order = Rms.Repo.preload(updated_order, @preloads)

      conn
      |> put_status(:ok)
      |> render("order.json", order: updated_order)
    end
  end

  def cancel(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    order = Orders.get_order!(resource.organization_id, id)

    timeout = Map.get(params, "timeout", 10_000)

    result =
      await_workflow(order, Map.get(params, "reason", "cancelation requested by user"), timeout)

    updated_order = Orders.get_order!(resource.organization_id, id, @preloads)

    conn
    |> put_status(:ok)
    |> render(:cancel, order: updated_order, errors: result)
  end

  defp await_workflow(order, reason, timeout) do
    order
    |> OrderCancellationWorkflow.build_workflow(reason: reason)
    |> then(& &1.changesets)
    |> Enum.map(&Relay.async/1)
    |> Relay.await_many(timeout)
    |> Enum.map(fn
      {:error, :result_too_large} ->
        %{code: :unknown_result, detail: "unknown result"}

      {:error, :timeout} ->
        %{code: :timeout, detail: "await timeout"}

      {:error, :snoozed} ->
        %{code: :timeout, detail: "await timeout"}

      {:error, :discarded} ->
        %{code: :workflow_discarded, detail: "workflow has been discarded"}

      {:error, error} when is_exception(error) ->
        %{code: :exception, detail: "exception running workflow"}

      {:error, error} when is_binary(error) ->
        %{code: :error, detail: error}

      {:error, _error} ->
        %{code: :unknown_error, detail: "unknown error"}

      _ ->
        nil
    end)
    |> Enum.reject(&is_nil/1)
  end
end
