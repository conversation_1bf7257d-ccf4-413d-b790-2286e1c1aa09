defmodule RmsWeb.OrderCustomerJSON do
  alias Rms.Commerce.Orders.OrderCustomer

  def render("customer.json", %{shopify_customer: customer}) do
    cpf = customer["cpf"]["value"]

    %{
      customer: %{
        name: customer["displayName"],
        document_type: if(cpf, do: "cpf", else: nil),
        document: cpf,
        email: customer["email"]
      }
    }
  end

  def render("customer.json", %{customer: customer}) do
    %{
      customer: data(customer)
    }
  end

  @doc """
  Renders a list of customers.
  """
  def index(%{customers: customers}) do
    %{data: for(customer <- customers, do: data(customer))}
  end

  @doc """
  Renders a single customer.
  """
  def show(%{customer: customer}) do
    %{data: data(customer)}
  end

  def data(%OrderCustomer{} = customer) do
    %{
      id: customer.customer_id,
      document_type: customer.document_type,
      document: customer.document,
      email: customer.email,
      name: customer.name,
      primary_phone_number: customer.primary_phone_number
    }
  end

  def data(_), do: nil
end
