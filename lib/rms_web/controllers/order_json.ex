defmodule RmsWeb.OrderJSON do
  alias Rms.Commerce.Orders.Order

  def index(%{orders: orders, page_metadata: page_metadata}) do
    %{
      orders: Enum.map(orders, &data/1),
      page: %{
        before: page_metadata.before,
        after: page_metadata.after,
        limit: page_metadata.limit
      }
    }
  end

  def cancel(%{order: order, errors: errors}) do
    %{order: data(order), errors: errors}
  end

  def render("order.json", %{shopify_order: order_data} = assigns) do
    shopify_customer = Map.get(order_data, "customer")

    customer =
      if shopify_customer do
        "customer.json"
        |> RmsWeb.CustomerJSON.render(%{shopify_customer: shopify_customer})
        |> get_in([:customer])
      end

    %{
      order: %{
        id: order_data["id"],
        name: order_data["name"],
        amount: order_data["currentTotalPriceSet"]["shopMoney"]["amount"],
        customer: customer,
        paid: false,
        fiscal_invoice: Map.get(assigns, "fiscal_invoice")
      }
    }
  end

  def render("order.json", %{order: order}) do
    %{order: data(order)}
  end

  defp data(%Order{} = order) do
    %{
      id: order.id,
      name: order.name,
      status: order.status,
      total_price: order.total_price,
      total_price_with_addons: Map.get(order, :total_price_with_addons, order.total_price),
      total_items_selling_price: Map.get(order, :total_items_selling_price, order.total_price),
      total_items_list_price: Map.get(order, :total_items_list_price, order.total_price),
      total_delivery_price: Map.get(order, :total_delivery_price, 0),
      total_discount: Map.get(order, :total_discount, 0),
      total_ecommerce_discounts: Map.get(order, :total_ecommerce_discounts, 0),
      total_items_manual_discount: Map.get(order, :total_items_manual_discount, 0),
      total_discount_with_addons:
        Map.get(order, :total_discount_with_addons, order.total_discount),
      discounts: render_discounts(order.discounts),
      fulfillments: render_fulfillments(order.fulfillments),
      addons: render_addons(order.addons),
      customer: RmsWeb.OrderCustomerJSON.data(order.order_customer),
      staff: RmsWeb.StaffsJSON.data(order.staff),
      cashier: RmsWeb.StaffsJSON.data(order.cashier),
      paid: order.status == "paid",
      fiscal_invoice: nil,
      transaction: RmsWeb.TransactionsJSON.data(order.transaction),
      external_id: order.external_id,
      inserted_at: order.inserted_at,
      reference_at: order.reference_at,
      location_id: order.location_id,
      shipping_address: order.shipping_address,
      source: order.source,
      notes: (order.metadata || %{}) |> Map.get(:notes, "")
    }
  end

  defp render_addons(addons) when is_list(addons) do
    Enum.map(addons, fn addon ->
      %{
        name: addon.name,
        price: addon.price,
        list_price: addon.list_price,
        image_url: addon.image_url,
        metadata: addon.metadata,
        type: addon.type
      }
    end)
  end

  defp render_discounts(discounts) when is_list(discounts) do
    discounts
    |> Enum.filter(fn discount ->
      Map.get(discount, :type, "não informado") != "automatic_ecommerce"
    end)
    |> Enum.map(&RmsWeb.DiscountsJSON.data/1)
  end

  defp render_fulfillments(fulfillments) when is_list(fulfillments) do
    Enum.map(fulfillments, &RmsWeb.FulfillmentsJSON.data/1)
  end

  def render_line_item(line_item) do
    %{
      id: line_item.id,
      quantity: line_item.quantity,
      price: line_item.price,
      list_price: line_item.list_price,
      shipping_method: line_item.shipping_method,
      shipping_settings: line_item.shipping_settings,
      pickup_point_name: get_pickup_point_name(line_item.shipping_settings),
      image_url: line_item.image_url,
      variant_name: line_item.variant_name,
      product_name: line_item.product_name,
      product_variant_id: line_item.product_variant_id,
      discounts: render_discounts(line_item.discounts),
      sku: line_item.sku
    }
  end

  defp get_pickup_point_name(%{
         "deliveryChannel" => "pickup-in-point",
         "pickupStoreInfo" => %{"friendlyName" => pickup_point_name}
       }) do
    pickup_point_name
  end

  defp get_pickup_point_name(_) do
    nil
  end
end
