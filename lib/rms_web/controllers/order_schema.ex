defmodule RmsWeb.OrderSchema do
  use Params

  defparams(
    index(%{
      # Filters
      search: :string,
      status: :string,
      location_id: :integer,
      payment_method: :string,
      inserted_at: :utc_datetime,
      staff_id: :integer,
      source: [field: :string, default: "iglu"],

      # Pagination
      after: :string,
      before: :string,
      limit: [field: :integer, default: 20]
    })
  )
end
