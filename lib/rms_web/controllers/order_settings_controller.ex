defmodule RmsWeb.OrderSettingsController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  alias Rms.Commerce.Orders

  def update(conn, %{"order_settings" => order_settings_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, order_settings} <-
           Orders.upsert_order_settings(resource.organization_id, order_settings_params) do
      conn
      |> put_status(:ok)
      |> render(:show, order_settings: order_settings)
    end
  end

  def show(conn, _params) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    order_settings = Orders.get_order_settings!(organization_id)
    render(conn, :show, order_settings: order_settings)
  end
end
