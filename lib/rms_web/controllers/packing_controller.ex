defmodule RmsWeb.PackingController do
  alias Rms.Commerce.Packings

  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  @preloads [
    :dock,
    :customer,
    :staff,
    packing_items: [line_item: [:discounts]]
  ]

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_params()
      |> Keyword.put(:preloads, @preloads)

    page =
      Packings.paginated_packings(resource.organization_id, opts)

    render(conn, :index, packings: page.entries, page_metadata: page.metadata)
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    packing =
      Packings.get_packing!(resource.organization_id, id, @preloads)

    render(conn, :show, packing: packing)
  end

  @allowed_params ~w(after before limit status location_id shipping_method external_reference inserted_at)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
