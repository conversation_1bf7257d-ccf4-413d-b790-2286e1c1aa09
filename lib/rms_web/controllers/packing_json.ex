defmodule RmsWeb.PackingJSON do
  alias Rms.Commerce.Packings.Packing
  alias Rms.Commerce.Packings.PackingItem
  alias Rms.Accounts.Dock

  def index(%{packings: packings, page_metadata: page_metadata}) do
    %{
      packings: Enum.map(packings, &data/1),
      page: %{
        before: page_metadata.before,
        after: page_metadata.after,
        limit: page_metadata.limit
      }
    }
  end

  def show(%{packing: packing}) do
    %{packing: data(packing)}
  end

  defp data(%Packing{} = packing) do
    %{
      id: packing.id,
      shipping_method: packing.shipping_method,
      courier_name: packing.courier_name,
      external_reference: packing.external_reference,
      status: packing.status,
      fulfillment_id: packing.fulfillment_id,
      dock: data(packing.dock),
      customer: RmsWeb.CustomerJSON.data(packing.customer),
      staff: RmsWeb.StaffsJSON.data(packing.staff),
      packing_items: Enum.map(packing.packing_items, &data/1),
      total_price: packing.total_price,
      inserted_at: packing.inserted_at,
      updated_at: packing.updated_at
    }
  end

  defp data(%Dock{} = dock) do
    %{
      name: dock.name,
      external_id: dock.external_id,
      location_id: dock.location_id
    }
  end

  defp data(%PackingItem{} = packing_item) do
    %{
      line_item: RmsWeb.OrderJSON.render_line_item(packing_item.line_item),
      quantity: packing_item.quantity
    }
  end
end
