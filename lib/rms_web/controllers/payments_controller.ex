defmodule RmsWeb.PaymentsController do
  use RmsWeb, :controller

  require Logger

  import Rms.Guardian.Plug, only: [current_resource: 1]

  action_fallback RmsWeb.FallbackController

  plug :payment_tagger

  def create(conn, params) do
    resource = current_resource(conn)

    with {:ok, payment} <-
           Rms.Finance.create_payment(resource.organization_id, params) do
      render(conn, "payment.json", payment: payment)
    end
  end

  def create_subscriptions(conn, params) do
    resource = current_resource(conn)

    case Rms.Integrations.create_subscriptions_payment(resource.organization_id, params) do
      {:ok, payment_link, _cart_id} ->
        conn
        |> put_status(200)
        |> json(%{payment_link: payment_link})
    end
  end

  def show(conn, %{"id" => id}) do
    resource = current_resource(conn)

    with {:ok, payment} <- get_payment!(resource, id) do
      render(conn, :show, payment: payment)
    end
  end

  def cancel(conn, %{"id" => id} = params) do
    resource = current_resource(conn)

    with {:ok, payment} <- get_payment!(resource, id),
         {:ok, canceled_payment} <-
           Rms.Finance.cancel_payment(
             payment,
             Map.take(params, ["customer_refund_receipt", "store_refund_receipt"])
           ) do
      render(conn, :show, payment: canceled_payment)
    end
  end

  defp payment_tagger(conn, _) do
    Logger.metadata(
      payment_id: conn.path_params["id"],
      payment_method: conn.params["method"],
      transaction_id: conn.params["transaction_id"]
    )

    conn
  end

  defp get_payment!(resource, payment_id) do
    payment = Rms.Finance.get_payment!(resource.organization_id, payment_id)

    {:ok, payment}
  rescue
    Ecto.NoResultsError ->
      Logger.error(
        "Payment not found for organization #{resource.organization_id} and payment_id #{payment_id}"
      )

      {:error, :not_found}

    Ecto.MultipleResultsError ->
      Logger.error(
        "Multiple payments found for organization #{resource.organization_id} and payment_id #{payment_id}"
      )

      {:error, :not_found}

    exception ->
      reraise Exception.message(exception), __STACKTRACE__
  end
end
