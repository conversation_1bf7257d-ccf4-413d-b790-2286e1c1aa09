defmodule RmsWeb.PaymentsJSON do
  def show(%{payment: payment}) do
    %{data: data(payment)}
  end

  def render("payment.json", %{payment: payment}) do
    data(payment)
  end

  def render("payments.json", %{payments: payments}) do
    Enum.map(payments, &data/1)
  end

  def data(payment) do
    %{
      id: payment.id,
      method: payment.method,
      amount: payment.amount,
      status: payment.status,
      transaction_id: payment.transaction_id,
      metadata: payment.metadata,
      inserted_at: payment.inserted_at
    }
  end
end
