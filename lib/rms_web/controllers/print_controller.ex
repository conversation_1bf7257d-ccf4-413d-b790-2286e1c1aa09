defmodule RmsWeb.PrintController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  alias Rms.Settings

  def print(conn, %{"location_id" => location_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    organization_id = resource.organization_id
    setting = Settings.get_location_setting(organization_id, location_id, "tailscale_printer_ip")

    with {:ok, tailscale_address} <- get_tailscale_address(setting),
         {:ok, _response} <- make_print_request!(tailscale_address, params) do
      send_resp(conn, :ok, "")
    end
  end

  defp get_tailscale_address(nil), do: {:error, :not_found}
  defp get_tailscale_address(%{value: value}), do: {:ok, value["data"]}

  defp make_print_request!(address, params) do
    middleware = [
      Tesla.Middleware.OpenTelemetry,
      Tesla.Middleware.Logger,
      Tesla.Middleware.JSON
    ]

    middleware
    |> Tesla.client(get_tesla_adapter())
    |> Tesla.post!("http://#{address}:2345", params)
    |> normalize_response()
  end

  defp get_tesla_adapter() do
    case Application.get_env(:tesla, :adapter) do
      Tesla.Mock -> Tesla.Mock
      _ -> {Tesla.Adapter.Hackney, proxy: {:socks5, ~c"localhost", 1055}, pool: false}
    end
  end

  defp normalize_response(%{status: status, body: body}) when status >= 200 and status < 300 do
    {:ok, body}
  end

  defp normalize_response(%{status: 412, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: 400, body: body}) do
    {:error, {:bad_request, body}}
  end

  defp normalize_response(%{status: 403, body: _body}) do
    {:error, :forbidden}
  end

  defp normalize_response(%{status: 404, body: _body}) do
    {:error, :not_found}
  end

  defp normalize_response(%{status: status_code, body: _body})
       when status_code >= 500 and status_code < 600 do
    {:error, :internal_server_error}
  end

  defp normalize_response(%{status: 422, body: body}) do
    {:error, {:validation_error, body}}
  end

  defp normalize_response(%{status: _status_code, body: _body}) do
    {:error, :unknown_error}
  end
end
