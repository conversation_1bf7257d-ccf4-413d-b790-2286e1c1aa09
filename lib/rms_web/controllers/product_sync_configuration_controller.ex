defmodule RmsWeb.ProductSyncConfigurationController do
  use RmsWeb, :controller

  alias Rms.Integrations

  action_fallback RmsWeb.FallbackController

  def show(conn, _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    config = Integrations.get_product_sync_configuration!(resource.organization_id)

    render(conn, "show.json", product_sync_configuration: config)
  end

  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    params = Map.put(params, "organization_id", resource.organization_id)

    with {:ok, config} <- Integrations.create_product_sync_configuration(params) do
      conn
      |> put_status(:created)
      |> render("show.json", product_sync_configuration: config)
    end
  end

  def update(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    config = Integrations.get_product_sync_configuration!(resource.organization_id)

    with {:ok, config} <- Integrations.update_product_sync_configuration(config, params) do
      render(conn, "show.json", product_sync_configuration: config)
    end
  end
end
