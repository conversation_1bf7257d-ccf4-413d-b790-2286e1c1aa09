defmodule RmsWeb.ProductSyncConfigurationJSON do
  def render("show.json", %{product_sync_configuration: config}) do
    %{
      data: %{
        id: config.id,
        field_priorities: config.field_priorities,
        default_priority: config.default_priority,
        organization_id: config.organization_id,
        inserted_at: config.inserted_at,
        updated_at: config.updated_at
      }
    }
  end
end
