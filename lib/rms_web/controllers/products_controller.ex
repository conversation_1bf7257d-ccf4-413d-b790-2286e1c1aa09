defmodule RmsWeb.ProductsController do
  use RmsWeb, :controller

  alias Rms.Commerce.Products

  action_fallback RmsWeb.FallbackController

  @preloads [:product_variants]
  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_params()
      |> Keyword.put(:preloads, @preloads)

    page =
      Products.paginated_products(resource.organization_id, opts)

    render(conn, "index.json", products: page.entries, page_metadata: page.metadata)
  end

  def show(conn, %{"id" => product_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product = Products.get_product!(resource.organization_id, product_id, @preloads)

    render(conn, "show.json", product: product)
  end

  def get_by_barcode(conn, %{"barcode" => barcode}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product = Products.get_product_by_barcode!(resource.organization_id, barcode)

    render(conn, "show.json", product: product)
  end

  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, product} <-
           Products.create_product(resource.organization_id, params, source: "api") do
      conn
      |> put_status(:created)
      |> render("show.json", product: product)
    end
  end

  def update(conn, %{"id" => product_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    product = Products.get_product!(resource.organization_id, product_id, @preloads)

    with {:ok, product} <-
           Products.update_product(product, params, source: "api") do
      conn
      |> put_status(:ok)
      |> render("show.json", product: product)
    end
  end

  def delete(conn, %{"id" => product_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    product = Products.get_product!(resource.organization_id, product_id)

    with {:ok, _product} <- Products.delete_product(product) do
      send_resp(conn, :no_content, "")
    end
  end

  def import(conn, %{"external_id" => external_id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    ecommerce = Rms.Settings.get_connected_ecommerce(resource.organization_id)

    with {:ok, _product} <-
           Rms.Integrations.import_ecommerce_product(
             resource.organization_id,
             external_id,
             ecommerce
           ) do
      conn
      |> put_status(:created)
      |> json(%{data: "success"})
    end
  end

  def archive(conn, %{"id" => product_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    product =
      Products.get_product!(resource.organization_id, product_id, @preloads)

    opts =
      params
      |> prepare_params()

    with {:ok, _} <- Products.archive(product, opts) do
      conn
      |> put_status(:ok)
      |> render("show.json", product: product)
    end
  end

  def create_gift_handler_configuration(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    case Products.import_gift_handler_configurations(resource.organization_id, params["Bundles"]) do
      {:ok, _} ->
        conn
        |> put_status(:created)
        |> json(%{success: true})

      _ ->
        conn
        |> put_status(422)
        |> json(%{success: false})
    end
  end

  @allowed_params ~w(search after before limit product_types variant_ids)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
