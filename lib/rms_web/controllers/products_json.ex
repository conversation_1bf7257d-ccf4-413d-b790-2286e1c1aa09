defmodule RmsWeb.ProductsJSON do
  def render("index.json", %{products: products, page_metadata: page}) do
    %{
      products: render_products(products),
      page: %{
        before: page.before,
        after: page.after,
        limit: page.limit
      }
    }
  end

  def render("show.json", %{product: product}) do
    product_variants = add_default_variation_type(product.product_variants)

    %{
      id: product.id,
      name: product.name,
      ncm: product.ncm,
      tags: product.tags,
      product_type: product.product_type,
      variants: render_product_variants(product_variants),
      variant_metadata: render_variant_metadata(product_variants)
    }
  end

  defp render_products(products) do
    Enum.map(products, &render("show.json", %{product: &1}))
  end

  defp render_product_variants(product_variants) do
    Enum.map(product_variants, &render_product_variant/1)
  end

  def render_product_variant(product_variant) do
    %{
      id: product_variant.id,
      product_id: product_variant.product_id,
      sku: product_variant.sku,
      bar_code: product_variant.bar_code,
      image_urls: product_variant.image_urls,
      variant_types: render_variant_types(product_variant.variation_types),
      price: product_variant.price,
      list_price: product_variant.list_price,
      quantity: nil
    }
  end

  defp render_variant_types(variation_types) do
    Enum.map(variation_types, &render_variation_type/1)
  end

  defp render_variation_type(variation_type) do
    %{
      variation_type.key => variation_type.value,
      metadata: variation_type.metadata
    }
  end

  defp render_variant_metadata(product_variants) do
    product_variants
    |> Enum.flat_map(& &1.variation_types)
    |> Enum.group_by(& &1.key, &Map.merge(&1.metadata, %{value: &1.value}))
    |> Enum.map(fn {key, values} -> {key, Enum.uniq(values)} end)
    |> Enum.into(%{})
  end

  defp add_default_variation_type(product_variants) when is_list(product_variants) do
    Enum.map(product_variants, fn
      %{variation_types: []} = variant ->
        %{variant | variation_types: [%{key: "default", value: "default", metadata: %{}}]}

      variant ->
        variant
    end)
    |> Enum.sort(fn
      variant1, variant2 ->
        variant1.name > variant2.name
    end)
  end

  defp add_default_variation_type(_), do: []
end
