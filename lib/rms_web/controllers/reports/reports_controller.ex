defmodule RmsWeb.Reports.ReportsController do
  use RmsWeb, :controller

  alias Rms.Reports

  action_fallback RmsWeb.FallbackController

  def payments(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, start_datetime} <- parse_datetime(params["start_date"]),
         {:ok, end_datetime} <- parse_datetime(params["end_date"]) do
      opts = [
        limit: parse_limit(params["limit"]),
        after: params["after"],
        before: params["before"],
        location_id: params["location_id"],
        staff_id: params["staff_id"],
        payment_method: params["payment_method"]
      ]

      page =
        Reports.paginated_payments(resource.organization_id, start_datetime, end_datetime, opts)

      render(conn, :payments, transactions: page.entries, page_metadata: page.metadata)
    end
  end

  def selling_summary(conn, params) do
    with {:ok, start_datetime} <- parse_datetime(params["start_date"]),
         {:ok, end_datetime} <- parse_datetime(params["end_date"]) do
      summary =
        Reports.selling_summary(
          start_datetime,
          end_datetime,
          params["location_id"],
          params["staff_id"]
        )

      render(conn, :selling_summary, summary: summary)
    else
      {:error, :invalid_date} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          error:
            "Invalid date format. Expected format: ISO8601 timestamp (e.g., 2025-02-25T03:00:00.000Z)"
        })
    end
  end

  defp parse_datetime(nil), do: {:error, :invalid_date}

  defp parse_datetime(date) do
    case DateTime.from_iso8601(date) do
      {:ok, datetime, _offset} -> {:ok, datetime}
      _error -> {:error, :invalid_date}
    end
  end

  defp parse_limit(nil), do: 20
  defp parse_limit(limit) when is_binary(limit), do: String.to_integer(limit)
  defp parse_limit(limit) when is_integer(limit), do: limit
end
