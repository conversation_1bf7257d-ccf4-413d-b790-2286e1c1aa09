defmodule RmsWeb.RetailManagement.RetailFiscalInvoicesController do
  @preloads [
    :fulfillment,
    :customer,
    invoice_payments: [:payment],
    invoice_items: [:line_item],
    serie: [location: [:address]]
  ]

  use RmsWeb, :controller

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_params()
      |> Keyword.put(:preloads, @preloads)
      |> Keyword.put(:query_params, params)

    page = Rms.Fiscal.paginated_fiscal_invoice(resource.organization_id, opts)

    render(conn, :index, fiscal_invoices: page.entries, page_metadata: page.metadata)
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    fiscal_invoice = Rms.Fiscal.get_fiscal_invoice!(resource.organization_id, id, @preloads)

    conn
    |> put_status(:ok)
    |> render("retail_fiscal_invoice.json", fiscal_invoice: fiscal_invoice)
  end

  @allowed_params ~w(after before count limit status transaction_id location_id invoice_serie invoice_env invoice_type serie_id invoice_number)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
