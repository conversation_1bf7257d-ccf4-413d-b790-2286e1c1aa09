defmodule RmsWeb.RetailManagement.RetailFiscalInvoicesJSON do
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Fiscal.InvoiceSerie
  alias Rms.Accounts.Location
  alias Rms.Finance.Payment
  alias Rms.Commerce.Orders.LineItem
  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Fiscal.InvoiceItem

  def index(%{fiscal_invoices: fiscal_invoices, page_metadata: page_metadata}) do
    %{
      fiscal_invoices: Enum.map(fiscal_invoices, &data/1),
      page: %{
        before: page_metadata.before,
        after: page_metadata.after,
        total_count: page_metadata.total_count,
        limit: page_metadata.limit
      }
    }
  end

  def render("retail_fiscal_invoices.json", %{fiscal_invoices: data}) do
    %{
      data: Enum.map(data, &data/1)
    }
  end

  def render("retail_fiscal_invoice.json", %{fiscal_invoice: data}) do
    %{
      fiscal_invoice: detail_data(data)
    }
  end

  def data(nil), do: nil

  def data(%FiscalInvoice{} = fiscal_invoice) do
    %{
      id: fiscal_invoice.id,
      operation_type: fiscal_invoice.operation_type,
      service: fiscal_invoice.service,
      invoice_number: fiscal_invoice.invoice_number,
      status: fiscal_invoice.status,
      df_key: fiscal_invoice.df_key,
      integrated_erp: fiscal_invoice.integrated_erp,
      external_id: fiscal_invoice.external_id,
      authorized_at: fiscal_invoice.authorized_at,
      canceled_at: fiscal_invoice.canceled_at,
      qr_code: fiscal_invoice.qr_code,
      order_id: render_order_id(fiscal_invoice.fulfillment),
      location: render_location_name(fiscal_invoice.serie),
      total_value: nil,
      xml: fiscal_invoice.xml
    }
  end

  def detail_data(%FiscalInvoice{} = fiscal_invoice) do
    %{
      id: fiscal_invoice.id,
      operation_type: fiscal_invoice.operation_type,
      service: fiscal_invoice.service,
      invoice_number: fiscal_invoice.invoice_number,
      status: fiscal_invoice.status,
      df_key: fiscal_invoice.df_key,
      external_id: fiscal_invoice.external_id,
      authorized_at: fiscal_invoice.authorized_at,
      canceled_at: fiscal_invoice.canceled_at,
      qr_code: fiscal_invoice.qr_code,
      total_value: nil,
      reverse_fulfillment: render_reverse_fulfillment(fiscal_invoice.reverse_fulfillment),
      xml: fiscal_invoice.xml,
      integrated_erp: fiscal_invoice.integrated_erp,
      customer: render_customer(fiscal_invoice.customer),
      order_id: render_order_id(fiscal_invoice.fulfillment),
      staff_id: render_staff_id(fiscal_invoice),
      location: render_location(fiscal_invoice.serie),
      serie: render_serie(fiscal_invoice.serie),
      payments: render_payments(fiscal_invoice.invoice_payments),
      line_items: render_line_items(fiscal_invoice.invoice_items)
    }
  end

  defp render_reverse_fulfillment(nil), do: nil

  defp render_reverse_fulfillment(%ReverseFulfillment{} = reverse_fulfillment) do
    %{
      id: reverse_fulfillment.id,
      customer: render_customer(reverse_fulfillment.customer)
    }
  end

  defp render_staff_id(%FiscalInvoice{
         reverse_fulfillment: %ReverseFulfillment{staff_id: staff_id}
       }) do
    staff_id
  end

  defp render_staff_id(%FiscalInvoice{
         invoice_items: [%InvoiceItem{line_item: %LineItem{staff_id: staff_id}} | _]
       }) do
    staff_id
  end

  defp render_staff_id(_) do
    nil
  end

  defp render_customer(%Rms.Finance.Customer{} = customer) do
    %{
      name: customer.name,
      document_type: customer.document_type,
      document: customer.document,
      email: customer.email,
      address: render_address(customer)
    }
  end

  defp render_customer(%Rms.Customers.Customer{} = customer) do
    %{
      name: customer.name,
      document_type: customer.document_type,
      document: customer.document,
      email: customer.email,
      address: render_address(customer)
    }
  end

  defp render_customer(_), do: nil

  defp render_address(%{addresses: [address | _]}) do
    render_address(%{address: address})
  end

  defp render_address(%{address: nil}) do
    nil
  end

  defp render_address(%{address: address}) do
    %{
      city_name: address.city_name,
      state: address.state,
      country_name: address.country_name,
      neighborhood: address.neighborhood,
      street: address.street,
      number: address.number,
      zip: address.zip,
      complement: address.complement
    }
  end

  defp render_address(_), do: nil

  defp render_location(%InvoiceSerie{location: %Location{} = location}) do
    RmsWeb.LocationsJSON.data(location)
  end

  defp render_location(_) do
    nil
  end

  defp render_location_name(%InvoiceSerie{location: %Location{} = location}) do
    location.name
  end

  defp render_location_name(_) do
    nil
  end

  defp render_line_items(invoice_items) do
    Enum.map(invoice_items, fn invoice_item -> render_line_item(invoice_item.line_item) end)
  end

  defp render_line_item(%LineItem{} = line_item) do
    RmsWeb.RetailManagement.RetailOrdersJSON.render_line_item(line_item)
  end

  defp render_line_item(_) do
    nil
  end

  defp render_payments(invoice_payments) do
    Enum.map(invoice_payments, fn invoice_payment -> render_payment(invoice_payment.payment) end)
  end

  defp render_payment(%Payment{} = payment) do
    RmsWeb.PaymentsJSON.data(payment)
  end

  defp render_payment(_) do
    nil
  end

  defp render_order_id(%Fulfillment{order_id: order_id} = _fulfillment) do
    order_id
  end

  defp render_order_id(_) do
    nil
  end

  defp render_serie(%InvoiceSerie{} = serie) do
    %{
      id: serie.id,
      status: serie.status,
      type: serie.invoice_type,
      env: serie.invoice_env,
      available_number: serie.available_number,
      invoice_serie: serie.invoice_serie,
      location_id: serie.location_id
    }
  end

  defp render_serie(_) do
    nil
  end
end
