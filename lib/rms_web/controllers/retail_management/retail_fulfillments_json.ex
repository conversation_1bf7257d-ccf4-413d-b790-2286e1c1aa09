defmodule RmsWeb.RetailManagement.RetailFulfillmentsJSON do
  alias Rms.Commerce.Fulfillments.Fulfillment

  def data(nil), do: nil

  def data(%Fulfillment{} = fulfillment) do
    %{
      shipping_method: fulfillment.shipping_method,
      external_reference: fulfillment.external_reference,
      status: fulfillment.status,
      line_items: render_line_items(fulfillment),
      fiscal_invoices: render_fiscal_invoices(fulfillment)
    }
  end

  defp render_line_items(%{line_items: %Ecto.Association.NotLoaded{}}), do: []
  defp render_line_items(%{line_items: nil}), do: []

  defp render_line_items(%{line_items: line_items}) when is_list(line_items) do
    Enum.map(line_items, &render_line_item/1)
  end

  defp render_line_items(_), do: []

  defp render_line_item(nil), do: nil

  defp render_line_item(line_item) do
    RmsWeb.RetailManagement.RetailOrdersJSON.render_line_item(line_item)
  end

  defp render_fiscal_invoices(%{fiscal_invoices: %Ecto.Association.NotLoaded{}}), do: []
  defp render_fiscal_invoices(%{fiscal_invoices: nil}), do: []

  defp render_fiscal_invoices(%{fiscal_invoices: fiscal_invoices})
       when is_list(fiscal_invoices) do
    Enum.map(fiscal_invoices, &render_fiscal_invoice/1)
  end

  defp render_fiscal_invoices(_), do: []

  defp render_fiscal_invoice(nil), do: nil

  defp render_fiscal_invoice(fiscal_invoice) do
    RmsWeb.RetailManagement.RetailFiscalInvoicesJSON.data(fiscal_invoice)
  end
end
