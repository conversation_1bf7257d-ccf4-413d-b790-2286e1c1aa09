defmodule RmsWeb.RetailManagement.RetailOrdersJSON do
  alias Rms.Commerce.Orders.Order

  def index(%{orders: orders, page_metadata: page_metadata}) do
    %{
      orders: Enum.map(orders, &data/1),
      page: %{
        before: page_metadata.before,
        after: page_metadata.after,
        total_count: page_metadata.total_count,
        limit: page_metadata.limit
      }
    }
  end

  def render("retail_order.json", %{order: order}) do
    %{order: data(order)}
  end

  defp data(%Order{} = order) do
    %{
      id: order.id,
      name: order.name,
      status: order.status,
      total_price: order.total_price,
      total_items_selling_price: Map.get(order, :total_items_selling_price, order.total_price),
      total_items_list_price: Map.get(order, :total_items_list_price, order.total_price),
      total_delivery_price: Map.get(order, :total_delivery_price, 0),
      total_discount: Map.get(order, :total_discount, 0),
      discounts: render_discounts(order.discounts),
      fulfillments: render_fulfillments(order.fulfillments),
      customer: RmsWeb.OrderCustomerJSON.data(order.order_customer),
      staff: RmsWeb.StaffsJSON.data(order.staff),
      paid: order.status == "paid",
      transaction: RmsWeb.TransactionsJSON.data(order.transaction),
      external_id: order.external_id,
      inserted_at: order.inserted_at,
      shipping_address: order.shipping_address
    }
  end

  defp render_discounts(discounts) when is_list(discounts) do
    discounts
    |> Enum.filter(fn discount ->
      Map.get(discount, :type, "não informado") != "automatic_ecommerce"
    end)
    |> Enum.map(&RmsWeb.DiscountsJSON.data/1)
  end

  defp render_fulfillments(fulfillments) when is_list(fulfillments) do
    Enum.map(fulfillments, &RmsWeb.RetailManagement.RetailFulfillmentsJSON.data/1)
  end

  def render_line_item(line_item) do
    %{
      id: line_item.id,
      quantity: line_item.quantity,
      price: line_item.price,
      list_price: line_item.list_price,
      shipping_method: line_item.shipping_method,
      shipping_settings: line_item.shipping_settings,
      pickup_point_name: get_pickup_point_name(line_item.shipping_settings),
      image_url: line_item.image_url,
      variant_name: line_item.variant_name,
      product_name: line_item.product_name,
      reverse_fulfillment_line_items:
        render_reverse_fulfillment_line_items(line_item.reverse_fulfillment_line_items),
      sku: line_item.sku
    }
  end

  defp render_reverse_fulfillment_line_items(%Ecto.Association.NotLoaded{}), do: []

  defp render_reverse_fulfillment_line_items(line_items) when is_list(line_items) do
    Enum.map(line_items, fn line_item ->
      render_reverse_fulfillment(line_item.reverse_fulfillment)
    end)
  end

  defp render_reverse_fulfillment(%Ecto.Association.NotLoaded{}), do: %{fiscal_invoice: nil}

  defp render_reverse_fulfillment(nil), do: %{fiscal_invoice: nil}

  defp render_reverse_fulfillment(reverse_fulfillment) do
    %{
      fiscal_invoice:
        RmsWeb.RetailManagement.RetailFiscalInvoicesJSON.data(reverse_fulfillment.fiscal_invoice)
    }
  end

  defp get_pickup_point_name(%{
         "deliveryChannel" => "pickup-in-point",
         "pickupStoreInfo" => %{"friendlyName" => pickup_point_name}
       }) do
    pickup_point_name
  end

  defp get_pickup_point_name(_) do
    nil
  end
end
