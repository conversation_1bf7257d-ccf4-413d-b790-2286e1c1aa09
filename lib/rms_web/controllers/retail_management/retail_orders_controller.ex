defmodule RmsWeb.RetailManagement.RetailOrdersController do
  use RmsWeb, :controller

  alias Rms.Commerce.Orders

  @preloads [
    :staff,
    :discounts,
    :order_customer,
    transaction: [:payments],
    customer: [:addresses],
    fulfillments: [
      line_items: [
        reverse_fulfillment_line_items: [
          reverse_fulfillment: [
            fiscal_invoice: [
              :fulfillment,
              :customer,
              invoice_payments: [:payment],
              serie: [:location]
            ]
          ]
        ]
      ],
      fiscal_invoices: [
        :fulfillment,
        :customer,
        invoice_payments: [:payment],
        serie: [:location]
      ]
    ]
  ]

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    opts =
      params
      |> prepare_params()
      |> Keyword.put(:preloads, @preloads)
      |> Keyword.put(:all?, true)

    page =
      Orders.paginated_orders(resource.organization_id, opts)

    render(conn, :index, orders: page.entries, page_metadata: page.metadata)
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    order = Orders.get_order!(resource.organization_id, id, @preloads)

    conn
    |> put_status(:ok)
    |> render("retail_order.json", order: order)
  end

  @allowed_params ~w(after before count limit status location_id payment_method inserted_at staff_id)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
