defmodule RmsWeb.RetailManagement.RetailStaffTransactionReportJSON do
  def index(%{data: transactions}) do
    %{data: for(transaction <- transactions, do: data(transaction))}
  end

  defp data(transaction) do
    %{
      orders: transaction.orders,
      received: transaction.received,
      staff_name: transaction.staff_name,
      location_name: transaction.location_name,
      average: transaction.average,
      effective_amount: transaction.effective_amount
    }
  end
end
