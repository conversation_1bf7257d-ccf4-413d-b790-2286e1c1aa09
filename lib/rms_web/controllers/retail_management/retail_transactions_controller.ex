defmodule RmsWeb.RetailManagement.RetailTransactionsController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def index(conn, params) do
    with {:ok, start_date} <- Date.from_iso8601(params["start_date"]),
         {:ok, end_date} <- Date.from_iso8601(params["end_date"]) do
      resource = Rms.Guardian.Plug.current_resource(conn)

      transactions =
        Rms.Reports.retail_transactions_report(
          resource.organization_id,
          start_date,
          end_date,
          params["location_id"]
        )

      render(conn, :index, data: transactions)
    else
      {:error, reason} when reason in [:invalid_format, :invalid_date, :invalid_calendar] ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid date format. Use ISO8601 (YYYY-MM-DD)"})
    end
  end
end
