defmodule RmsWeb.RetailManagement.RetailTransactionsJSON do
  def index(%{data: transactions}) do
    %{data: for(transaction <- transactions, do: data(transaction))}
  end

  defp data(transaction) do
    %{
      order_id: transaction.order_id,
      external_order: transaction.external_order,
      amount: transaction.amount,
      method: transaction.method,
      status: transaction.status,
      installments: transaction.installments,
      inserted_at: transaction.inserted_at,
      aut: transaction.aut,
      nsu: transaction.nsu,
      staff_id: transaction.staff_id,
      staff_name: transaction.staff_name,
      staff_external_id: transaction.staff_external_id,
      location_name: transaction.location_name,
      df_key: transaction.df_key
    }
  end
end
