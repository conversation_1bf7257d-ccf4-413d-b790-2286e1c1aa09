defmodule RmsWeb.ReverseFulfillmentController do
  use RmsWeb, :controller

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    reverse_fulfillment =
      Rms.Commerce.Fulfillments.get_reverse_fulfillment_by_id!(resource.organization_id, id)
      |> Rms.Repo.preload([
        :location,
        :staff,
        line_items: [line_item: [fulfillment: [order: [:location, :staff]]]]
      ])

    conn
    |> put_status(:ok)
    |> render("show.json", reverse_fulfillment: reverse_fulfillment)
  end
end
