defmodule RmsWeb.ReverseFulfillmentJSON do
  def render("show.json", %{reverse_fulfillment: reverse_fulfillment}) do
    %{
      reverse_fulfillment: %{
        id: reverse_fulfillment.id,
        status: reverse_fulfillment.status,
        location: render_location(reverse_fulfillment.location),
        staff: render_staff(reverse_fulfillment.staff),
        line_items: render_reverse_fulfillment_line_items(reverse_fulfillment.line_items)
      }
    }
  end

  def render_reverse_fulfillment_line_items(reverse_fulfillment_line_items) do
    Enum.map(reverse_fulfillment_line_items, fn rfl ->
      %{
        id: rfl.id,
        line_item: render_line_item(rfl.line_item)
      }
    end)
  end

  def render_line_item(line_item) do
    %{
      id: line_item.id,
      quantity: line_item.quantity,
      fulfillment: render_fulfillment(line_item.fulfillment)
    }
  end

  def render_fulfillment(fulfillment) do
    %{
      id: fulfillment.id,
      order: render_order(fulfillment.order)
    }
  end

  def render_order(order) do
    %{
      id: order.id,
      location: render_location(order.location),
      staff: render_staff(order.staff)
    }
  end

  def render_location(location) do
    %{
      id: location.id,
      name: location.name,
      cnpj: location.cnpj
    }
  end

  def render_staff(nil), do: nil

  def render_staff(staff) do
    %{
      id: staff.id,
      name: staff.name
    }
  end
end
