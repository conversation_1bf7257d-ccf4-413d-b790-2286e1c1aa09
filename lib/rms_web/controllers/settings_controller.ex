defmodule RmsWeb.SettingsController do
  use RmsWeb, :controller

  alias Rms.Settings

  action_fallback RmsWeb.FallbackController

  def show(conn, params) do
    {organization_id, location_id, requested_fields} = format_params(conn, params)

    settings_list =
      Settings.list_settings(organization_id, location_id, requested_fields)

    conn
    |> put_status(:ok)
    |> render("settings.json", settings_list: settings_list)
  end

  defp format_params(conn, params) do
    organization_id = Rms.Guardian.Plug.current_resource(conn).organization_id
    location_id = Map.get(params, "location_id", nil)

    requested_fields =
      Map.get(params, "fields", "") |> String.split(",") |> Enum.reject(fn x -> x == "" end)

    {organization_id, location_id, requested_fields}
  end

  def create(conn, %{"type" => "organization_setting"} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, setting} <-
           Rms.Settings.create_organization_setting(
             resource.organization_id,
             params["key"],
             params["value"]
           ) do
      conn
      |> put_status(:created)
      |> render("setting.json", setting: setting)
    end
  end

  def create(conn, %{"type" => "location_setting", "location_id" => location_id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, setting} <-
           Rms.Settings.create_location_setting(
             location_id,
             resource.organization_id,
             params["key"],
             params["value"]
           ) do
      conn
      |> put_status(:created)
      |> render("setting.json", setting: setting)
    end
  end

  def create(conn, params) do
    conn = put_status(conn, :unprocessable_entity)

    case params do
      %{"type" => "location_setting"} ->
        json(conn, %{errors: %{location_id: ["is required"]}})

      %{"type" => _type} ->
        json(conn, %{
          errors: %{type: ["expected type to be either organization_setting or location_setting"]}
        })

      _ ->
        json(conn, %{errors: %{type: ["is required"]}})
    end
  end

  def update(conn, %{"type" => "organization_setting", "key" => key, "value" => value}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, setting} <-
           Rms.Settings.update_organization_setting(resource.organization_id, key, value) do
      conn
      |> put_status(:ok)
      |> render("setting.json", setting: setting)
    end
  end

  def update(
        conn,
        %{
          "type" => "location_setting",
          "location_id" => location_id,
          "key" => key,
          "value" => value
        }
      ) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, setting} <-
           Rms.Settings.update_location_setting(
             resource.organization_id,
             location_id,
             key,
             value
           ) do
      conn
      |> put_status(:ok)
      |> render("setting.json", setting: setting)
    end
  end

  def delete(conn, %{"type" => "organization_setting", "key" => key}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:setting, setting} when not is_nil(setting) <-
           {:setting, Rms.Settings.get_organization_setting(resource.organization_id, key)},
         {:ok, _setting} <-
           Rms.Settings.delete_organization_setting(setting) do
      send_resp(conn, :no_content, "")
    else
      {:setting, nil} ->
        {:error, :not_found}

      err ->
        err
    end
  end

  def delete(conn, %{"type" => "location_setting", "location_id" => location_id, "key" => key}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:setting, setting} when not is_nil(setting) <-
           {:setting,
            Rms.Settings.get_location_setting(resource.organization_id, location_id, key)},
         {:ok, _setting} <- Rms.Settings.delete_location_setting(setting) do
      send_resp(conn, :no_content, "")
    else
      {:setting, nil} ->
        {:error, :not_found}

      err ->
        err
    end
  end

  def bulk_upsert(conn, %{"location_id" => location_id, "settings" => settings})
      when is_map(settings) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, updated_settings} <-
           Settings.bulk_upsert_location_settings(
             resource.organization_id,
             location_id,
             settings
           ) do
      conn
      |> put_status(:ok)
      |> render("settings.json", settings_list: updated_settings)
    end
  end

  def bulk_upsert(conn, _params) do
    conn
    |> put_status(:unprocessable_entity)
    |> json(%{
      errors: %{
        location_id: ["is required"],
        settings: ["is required and must be a map"]
      }
    })
  end
end
