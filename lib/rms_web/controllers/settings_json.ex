defmodule RmsWeb.SettingsJSON do
  alias Rms.Settings.OrganizationSetting
  alias Rms.Settings.LocationSetting

  def render("settings.json", %{settings_list: settings}) do
    Enum.reduce(settings, %{}, fn setting, acc ->
      Map.put(acc, setting.key, setting.value)
    end)
  end

  def render("setting.json", %{setting: %OrganizationSetting{} = setting}) do
    %{
      key: setting.key,
      value: setting.value["data"] || setting.value[:data],
      type: "organization_setting"
    }
  end

  def render("setting.json", %{setting: %LocationSetting{} = setting}) do
    %{
      key: setting.key,
      value: setting.value["data"] || setting.value[:data],
      type: "location_setting",
      location_id: setting.location_id
    }
  end
end
