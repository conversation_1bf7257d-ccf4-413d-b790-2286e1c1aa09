defmodule RmsWeb.StaffsController do
  use RmsWeb, :controller

  alias Rms.Repo
  alias Rms.Accounts
  alias Rms.Accounts.Staff
  action_fallback RmsWeb.FallbackController

  @preloads [:staff_locations, :staff_role]

  def index(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    allow_archived? = Map.get(params, "allow_archived", false)

    opts =
      params
      |> prepare_params()
      |> Keyword.merge(
        allow_archived?: allow_archived?,
        query_params: params,
        preloads: @preloads
      )

    page =
      Accounts.paginated_staff(resource.organization_id, opts)

    render(conn, "index.json", staffs: page.entries, page_metadata: page.metadata)
  end

  @spec create(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def create(conn, %{"staff" => staff_params} = _params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, %Staff{} = staff} <- Accounts.create_staff(resource.organization_id, staff_params) do
      staff = Repo.preload(staff, @preloads)

      conn
      |> put_status(:created)
      |> render("show.json", staff: staff)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    staff = Accounts.get_staff!(resource.organization_id, id, preloads: @preloads)

    render(conn, "show.json", staff: staff)
  end

  def update(conn, %{"id" => id, "staff" => staff_params}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    staff = Accounts.get_staff!(resource.organization_id, id, preloads: @preloads)

    with {:ok, %Staff{} = updated_staff} <- Accounts.update_staff(staff, staff_params) do
      render(conn, "show.json", staff: updated_staff)
    end
  end

  def delete(conn, %{"id" => id} = params) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    staff = Accounts.get_staff!(resource.organization_id, id, allow_archived?: true)

    delete_fn =
      if Map.get(params, "hard", false) do
        &Accounts.delete_staff/1
      else
        &Accounts.archive_staff/1
      end

    with {:ok, %Staff{}} <- delete_fn.(staff) do
      send_resp(conn, :no_content, "")
    end
  end

  @allowed_params ~w(after before limit location_id location_cnpj staff_name staff_external_id)
  defp prepare_params(params) do
    for {key, value} when key in @allowed_params <- params do
      # We can use the unsafe form because we only convert @allowed_params
      {String.to_atom(key), value}
    end
  end
end
