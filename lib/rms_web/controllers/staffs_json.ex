defmodule RmsWeb.StaffsJSON do
  alias Rms.Accounts.Staff
  alias Rms.Accounts.StaffRole

  def render("index.json", %{staffs: staffs, page_metadata: page_metadata}) do
    %{
      staffs: Enum.map(staffs, &render("show.json", %{staff: &1})),
      page: %{
        after: page_metadata.after,
        before: page_metadata.before,
        limit: page_metadata.limit
      }
    }
  end

  def render("show.json", %{staff: staff}) do
    data(staff)
    |> Map.put(:location_ids, Enum.map(staff.staff_locations, & &1.location_id))
  end

  def data(%Staff{} = staff) do
    %{
      id: staff.id,
      name: staff.name,
      staff_role: render_staff_role(staff.staff_role),
      external_id: staff.external_id,
      erp_id: staff.erp_id,
      archived_at: staff.archived_at,
      inserted_at: staff.inserted_at,
      updated_at: staff.updated_at,
      staff_locations: render_staff_locations(staff.staff_locations)
    }
  end

  def data(_), do: nil

  defp render_staff_role(%StaffRole{} = staff_role) do
    %{
      seller: staff_role.seller,
      cashier: staff_role.cashier,
      stocker: staff_role.stocker
    }
  end

  defp render_staff_role(_), do: nil

  defp render_staff_locations(staff_locations) when is_list(staff_locations) do
    Enum.map(staff_locations, &render_staff_location/1)
  end

  defp render_staff_locations(_), do: []

  defp render_staff_location(%Rms.Accounts.StaffLocation{} = staff_location) do
    %{
      id: staff_location.id,
      location_id: staff_location.location_id
    }
  end
end
