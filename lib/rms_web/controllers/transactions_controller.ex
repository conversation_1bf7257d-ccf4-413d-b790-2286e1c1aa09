defmodule RmsWeb.TransactionsController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  def create(
        conn,
        %{
          "order_id" => order_id,
          "payments" => [%{"method" => "payment_link"}]
        } = params
      ) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    order = Rms.Commerce.Orders.get_order!(resource.organization_id, order_id)

    with {:ok, payment_link, {partner, external_id}} <-
           Rms.Integrations.create_payment_link(
             resource.organization_id,
             order,
             params["customer"]
           ),
         params = update_params(params, order, payment_link),
         {:ok, %{transaction: transaction, external_reference: _}} <-
           create_payment(params, resource.organization_id, partner, external_id) do
      transaction = Rms.Repo.preload(transaction, [:payments])

      conn
      |> put_status(:created)
      |> render("transaction.json",
        payment_link: payment_link,
        transaction: transaction
      )
    end
  end

  def create(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    with {:ok, transaction} <- Rms.Finance.create_transaction(resource.organization_id, params) do
      transaction = Rms.Repo.preload(transaction, [:payments])
      render(conn, "transaction.json", transaction: transaction)
    end
  end

  def show(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    transaction =
      resource.organization_id
      |> Rms.Finance.get_transaction!(id)
      |> Rms.Repo.preload([:payments])

    render(conn, "transaction.json", transaction: transaction)
  end

  def close(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    transaction = Rms.Finance.get_transaction!(resource.organization_id, id)

    with {:ok, transaction} <- Rms.Finance.close_transaction(transaction) do
      render(conn, "transaction.json", transaction: transaction)
    end
  end

  def cancel(conn, %{"id" => id}) do
    resource = Rms.Guardian.Plug.current_resource(conn)
    transaction = Rms.Finance.get_transaction!(resource.organization_id, id)

    with {:ok, transaction} <- Rms.Finance.cancel_transaction(transaction) do
      render(conn, "transaction.json", transaction: transaction)
    end
  end

  defp create_payment(params, organization_id, partner, external_id) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:transaction, fn _, _ ->
      Rms.Finance.create_transaction(organization_id, params)
    end)
    |> Ecto.Multi.run(:external_reference, fn _, %{transaction: transaction} ->
      [payment] = transaction.payments

      Rms.Integrations.create_external_payment_reference(payment, partner, external_id)
    end)
    |> Rms.Repo.transaction()
  end

  defp update_params(params, order, payment_link) do
    params
    |> update_in(["payments", Access.at(0), "metadata"], fn metadata ->
      Map.put(metadata || %{}, "link", payment_link)
    end)
    |> update_in(["payments", Access.at(0), "amount"], fn _ -> order.total_price end)
  end
end
