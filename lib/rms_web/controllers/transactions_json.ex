defmodule RmsWeb.TransactionsJSON do
  alias Rms.Finance.Transaction

  def render("transaction.json", %{payment_link: payment_link} = assigns) do
    assigns = Map.drop(assigns, [:payment_link])

    "transaction.json"
    |> render(assigns)
    |> Map.merge(%{payment_link: payment_link})
  end

  def render("transaction.json", %{transaction: transaction}) do
    data(transaction)
  end

  def data(%Transaction{} = transaction) do
    payments = get_payments(transaction)

    %{
      id: transaction.id,
      payments: payments,
      status: transaction.status
    }
  end

  def data(_), do: nil

  defp get_payments(%{payments: payments}) when is_list(payments),
    do: Enum.map(payments, &RmsWeb.PaymentsJSON.data(&1))

  defp get_payments(_), do: nil
end
