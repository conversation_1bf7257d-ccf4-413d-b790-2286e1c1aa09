defmodule RmsWeb.UsersController do
  use RmsWeb, :controller

  alias Rms.Accounts

  action_fallback RmsWeb.FallbackController

  def assign_location(conn, params) do
    resource = Rms.Guardian.Plug.current_resource(conn)

    user = get_user(resource, params)

    with {:ok, _} <- Accounts.assign_location(user, params["location_id"]) do
      user = Rms.Repo.preload(user, locations: [:address])

      conn
      |> put_status(:created)
      |> render("show.json", user: user)
    end
  end

  defp get_user(%{__struct__: module, id: id, organization_id: organization_id} = user, %{
         "user_id" => user_id
       }) do
    if module == Accounts.User && user_id == to_string(id) do
      user
    else
      Accounts.get_user!(organization_id, user_id)
    end
  end
end
