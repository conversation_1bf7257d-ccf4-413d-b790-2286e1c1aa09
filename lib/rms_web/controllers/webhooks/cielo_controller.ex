defmodule RmsWeb.Webhooks.CieloController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  require Lo<PERSON>

  def notify_payment(conn, %{
        "checkout_cielo_order_number" => checkout_cielo_order_number,
        "product_id" => external_payment_reference
      }) do
    Logger.info(
      "cielo payment notification received with product_id #{inspect(external_payment_reference)}"
    )

    %{
      checkout_cielo_order_number: checkout_cielo_order_number,
      product_id: external_payment_reference
    }
    |> Rms.Workers.CieloPaymentNotification.new()
    |> Oban.insert!()

    conn
    |> put_status(:ok)
    |> json(%{})
  end

  def notify_payment(conn, params) do
    Logger.warning("unhandled cielo payment notification received with params #{inspect(params)}")

    conn
    |> put_status(:ok)
    |> json(%{})
  end
end
