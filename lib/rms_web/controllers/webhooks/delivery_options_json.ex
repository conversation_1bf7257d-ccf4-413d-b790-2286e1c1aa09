defmodule RmsWeb.DeliveryOptionsJSON do
  def render("delivery_options.json", %{delivery_options: delivery_options}) do
    %{
      items: render_items(Map.get(delivery_options, "items", [])),
      pickup_points: Map.get(delivery_options, "pickup_points", [])
    }
  end

  def render_items(items) do
    Enum.map(items, &render_item/1)
  end

  def render_item(item) do
    %{
      product_variant_id: Map.get(item, "product_variant_id"),
      delivery_options: render_delivery_options(Map.get(item, "delivery_options", [])),
      item_index: Map.get(item, "item_index")
    }
  end

  def render_delivery_options(delivery_options) do
    Enum.map(delivery_options, &render_delivery_option/1)
  end

  def render_delivery_option(delivery_option) do
    %{
      id: Map.get(delivery_option, "id"),
      name: Map.get(delivery_option, "name"),
      price: Map.get(delivery_option, "price"),
      delivery_time: Map.get(delivery_option, "delivery_time"),
      delivery_type: Map.get(delivery_option, "delivery_type"),
      pickup_point: Map.get(delivery_option, "pickup_point"),
      metadata: Map.get(delivery_option, "metadata"),
      quantity: Map.get(delivery_option, "quantity")
    }
  end
end
