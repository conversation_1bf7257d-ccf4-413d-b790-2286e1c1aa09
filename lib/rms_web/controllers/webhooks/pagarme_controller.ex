defmodule RmsWeb.Webhooks.PagarMeController do
  use RmsWeb, :controller

  import Plug.Conn

  plug RmsWeb.Plugs.Webhooks.PagarmeAuthenticate

  action_fallback RmsWeb.FallbackController

  def create(conn, params) do
    org_id = conn.assigns.organization_id

    with {:ok, _rsponse} <- enqueue_worker(params, org_id) do
      conn
      |> put_status(:ok)
      |> json(%{success: true})
    end
  end

  defp enqueue_worker(params, org_id) do
    %{
      payload: params,
      org_id: org_id
    }
    |> Rms.Workers.PagarMeWorker.new()
    |> Oban.insert()
  end
end
