defmodule RmsWeb.Webhooks.VTEXController do
  use RmsWeb, :controller

  action_fallback RmsWeb.FallbackController

  require Logger

  alias Rms.Integrations
  alias Rms.Integrations.VTEX.Customers.ImportCustomer

  def notify_product_change(
        conn,
        %{
          "app_key_suffix" => app_key_suffix,
          "An" => account_name
        } = params
      ) do
    vtex_credential = Integrations.get_vtex_credential_by_name!(account_name)

    app_key_suffix_from_db =
      vtex_credential.app_key
      |> String.split("-")
      |> List.last()

    if Plug.Crypto.secure_compare(app_key_suffix_from_db, app_key_suffix) do
      notification = Map.drop(params, ["organization_id", "app_key_suffix"])

      enriched_notification =
        Map.merge(notification, %{
          "sales_channel_id" => vtex_credential.sales_channel_id,
          "affiliate_id" => vtex_credential.affiliate_id
        })

      case Rms.Integrations.VTEX.ProductSyncBatcher.add_notification(
             vtex_credential.organization_id,
             enriched_notification
           ) do
        :ok ->
          conn
          |> put_status(:ok)
          |> json(%{success: true})

        {:error, reason} ->
          Logger.error("failed to process product notification: #{inspect(reason)}")

          conn
          |> put_status(:unprocessable_entity)
          |> json(%{success: false, error: "Failed to process notification"})
      end
    else
      conn
      |> put_status(:unauthorized)
      |> json(%{success: false})
    end
  end

  def order_status_notification(conn, %{
        "marketplace_order_id" => marketplace_order_id,
        "action" => "cancel"
      }) do
    Integrations.VTEX.CancelOrderWorker.new(%{"marketplace_order_id" => marketplace_order_id})
    |> Oban.insert!()

    conn
    |> put_status(:ok)
    |> json(%{
      orderId: marketplace_order_id,
      receipt: Ecto.UUID.autogenerate(),
      date: DateTime.utc_now()
    })
  end

  def order_status_notification(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: "Invalid request"})
  end

  def order_seller_cancellation(conn, %{"marketplace_order_id" => marketplace_order_id}) do
    %{"marketplace_order_id" => marketplace_order_id}
    |> Integrations.VTEX.CancelOrderWorker.new()
    |> Oban.insert!()

    conn
    |> put_status(:ok)
    |> json(%{
      orderId: marketplace_order_id,
      receipt: Ecto.UUID.autogenerate(),
      date: DateTime.utc_now()
    })
  end

  def order_seller_cancellation(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{error: "Invalid request"})
  end

  def order_processing_result(conn, params) do
    Logger.info("order processing result params=#{inspect(params)}")

    with {:ok, external_reference} <- extract_external_reference(params),
         {:ok, fulfillment} <- get_fulfillment_by_marketplace_order_id(params),
         {:ok, _updated_fulfillment} <-
           update_fulfillment_external_reference(fulfillment, external_reference) do
      Logger.info(
        "successfully updated fulfillment (#{fulfillment.id}) with external reference=#{external_reference}"
      )

      conn
      |> put_status(:ok)
      |> json(%{success: true})
    else
      {:error, :missing_external_reference} ->
        Logger.warning("failed to process VTEX order result=missing_external_reference")

        conn
        |> put_status(:ok)
        |> json(%{success: false})

      {:error, reason} ->
        Logger.error("failed to process VTEX order result=#{inspect(reason)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{success: false})
    end
  end

  defp extract_external_reference(%{"fields" => %{"mainOrderId" => external_reference}})
       when is_binary(external_reference) do
    {:ok, external_reference}
  end

  defp extract_external_reference(_), do: {:error, :missing_external_reference}

  defp get_fulfillment_by_marketplace_order_id(%{"marketplaceOrderId" => marketplace_order_id}) do
    case String.split(marketplace_order_id, "-") do
      [order_id, fulfillment_id] ->
        fulfillment = Rms.Commerce.Fulfillments.unsafe_get_fulfillment!(fulfillment_id)

        if to_string(fulfillment.order_id) == order_id do
          {:ok, fulfillment}
        else
          {:error, "fulfillment order_id mismatch"}
        end

      _ ->
        {:error, "invalid marketplaceOrderId format"}
    end
  end

  defp update_fulfillment_external_reference(fulfillment, external_reference) do
    Rms.Commerce.Fulfillments.update_fulfillment(fulfillment, %{
      external_reference: external_reference
    })
  end

  def notify_customer_change(conn, params) do
    Logger.info("vtex customer change notification received with params #{inspect(params)}")
    vtex_credential = Integrations.get_vtex_credential_by_name!(params["account_name"])

    with [appkey] <- Plug.Conn.get_req_header(conn, "x-vtex-api-appkey"),
         true <- Plug.Crypto.secure_compare(appkey, vtex_credential.app_key),
         params = Map.update(params, "addresses", nil, &sanitize_params/1),
         params = sanitize_params(params),
         {:ok, _} <-
           ImportCustomer.import(
             vtex_credential.organization_id,
             params
           ) do
      conn
      |> put_status(:ok)
      |> json(%{})
    else
      false ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "invalid app key"})

      {:error, reason} ->
        {:error, reason}

      [] ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error: "invalid app key"})
    end
  end

  defp sanitize_params(params) when is_map(params) do
    params
    |> Enum.reject(fn {_, value} -> value == "" or is_nil(value) end)
    |> Map.new(fn
      {key, value} when is_binary(value) ->
        atom_key = String.to_existing_atom(key)
        encoding_change = :unicode.characters_to_binary(value, :utf8, :latin1)

        parsed_value =
          if(String.valid?(encoding_change), do: encoding_change, else: value)

        {atom_key, parsed_value}

      {key, value} ->
        atom_key = String.to_existing_atom(key)
        {atom_key, value}
    end)
  end

  defp sanitize_params(params) when is_list(params), do: Enum.map(params, &sanitize_params/1)
  defp sanitize_params(params), do: params

  def notify_order_status(conn, %{
        "OrderId" => vtex_order_id,
        "State" => state,
        "Origin" => %{"Account" => account_name, "x-vtex-api-appkey" => appkey}
      }) do
    vtex_credential = Integrations.get_vtex_credential_by_name!(account_name)

    cond do
      not Plug.Crypto.secure_compare(appkey, vtex_credential.app_key) ->
        {:error, :unauthorized}

      not check_state(state) ->
        {:error, :unprocessable_entity}

      true ->
        %{"vtex_order_id" => vtex_order_id, "organization_id" => vtex_credential.organization_id}
        |> Rms.Integrations.VTEX.UpdateOrderStatus.new()
        |> Oban.insert!()

        conn
        |> put_status(:ok)
        |> json(%{})
    end
  end

  def check_state("ready-for-handling"), do: true
  def check_state("payment-approved"), do: true
  def check_state(_), do: false
end
