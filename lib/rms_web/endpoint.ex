defmodule RmsWeb.Endpoint do
  use Phoenix.Endpoint, otp_app: :rms

  # The session will be stored in the cookie and signed,
  # this means its contents can be read but not tampered with.
  # Set :encryption_salt if you would also like to encrypt it.
  @session_options [
    store: :cookie,
    key: "_rms_key",
    signing_salt: "o/Rzr9Op",
    same_site: "Lax"
  ]

  socket "/live", Phoenix.LiveView.Socket, websocket: [connect_info: [session: @session_options]]

  plug PlugHeartbeat, path: "/healthz"

  plug Plug.Telemetry, event_prefix: [:phoenix, :endpoint]
  plug RemoteIp
  plug :put_remote_ip

  # Serve at "/" the static files from "priv/static" directory.
  #
  # You should set gzip to true if you are running phx.digest
  # when deploying your static files in production.
  plug Plug.Static,
    at: "/",
    from: :rms,
    gzip: false,
    only: RmsWeb.static_paths()

  # Code reloading can be explicitly enabled under the
  # :code_reloader configuration of your endpoint.
  if code_reloading? do
    plug Phoenix.CodeReloader
    plug Phoenix.Ecto.CheckRepoStatus, otp_app: :rms
  end

  plug Phoenix.LiveDashboard.RequestLogger,
    param_key: "request_logger",
    cookie_key: "request_logger"

  plug Plug.RequestId
  plug Plug.Telemetry, event_prefix: [:phoenix, :endpoint]

  plug Plug.Parsers,
    parsers: [:urlencoded, :multipart, :json],
    pass: ["*/*"],
    json_decoder: Phoenix.json_library(),
    body_reader: {RmsWeb.Plugs.CacheBodyReader, :read_body, []}

  plug Plug.MethodOverride
  plug Plug.Head
  plug Plug.Session, @session_options
  plug RmsWeb.Router

  defp put_remote_ip(conn, _opts) do
    if :inet.is_ip_address(conn.remote_ip) do
      conn.remote_ip
      |> :inet.ntoa()
      |> to_string()
      |> RmsWeb.put_remote_ip()
    end

    conn
  end
end
