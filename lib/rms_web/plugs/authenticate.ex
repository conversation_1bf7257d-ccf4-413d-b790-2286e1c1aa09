defmodule RmsWeb.Plugs.Authenticate do
  use Plug.Builder
  import Plug.Conn

  plug :ensure_authenticated
  plug :load_resource
  plug :authenticate_machine

  defp ensure_authenticated(conn, opts) do
    case get_req_header(conn, "authorization") do
      [] -> conn
      _ -> Guardian.Plug.EnsureAuthenticated.call(conn, opts)
    end
  end

  defp load_resource(conn, opts) do
    case get_req_header(conn, "authorization") do
      [] -> conn
      _ -> Guardian.Plug.LoadResource.call(conn, opts)
    end
  end

  defp authenticate_machine(conn, _opts) do
    case {Rms.Guardian.Plug.current_resource(conn), get_req_header(conn, "x-iglu-api-token")} do
      {nil, []} ->
        conn
        |> RmsWeb.AuthErrorHandler.auth_error({:error, :unauthorized}, [])
        |> halt()

      {nil, iglu_api_token} ->
        api_token = Rms.Accounts.get_api_token!(iglu_api_token)
        Guardian.Plug.put_current_resource(conn, api_token)

      _ ->
        conn
    end
  end
end
