defmodule RmsWeb.Plugs.CacheBodyReader do
  def read_body(conn, opts) do
    read_body_result = Plug.Conn.read_body(conn, opts)

    with {:ok, body, conn} <- read_body_result,
         "POST" <- conn.method,
         "/webhooks/" <> _ <- conn.request_path do
      conn = update_in(conn.assigns[:raw_body], &[body | &1 || []])

      {:ok, body, conn}
    else
      _ ->
        read_body_result
    end
  end
end
