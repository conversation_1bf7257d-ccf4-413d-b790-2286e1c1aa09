defmodule RmsWeb.Plugs.Webhooks.PagarmeAuthenticate do
  alias Rms.Integrations
  import Plug.Conn

  alias Rms.Integrations

  def init(opts), do: opts

  def call(conn, _opts) do
    shop_id = shop_id(conn)

    with ["Basic " <> encoded_credentials] <- get_req_header(conn, "authorization"),
         {:ok, credential} <- Base.decode64(encoded_credentials),
         %Rms.Integrations.PagarMeHookCredential{organization_id: org_id} <-
           Integrations.get_pagarme_hook_credential(shop_id, credential) do
      assign(conn, :organization_id, org_id)
    else
      _ ->
        conn
        |> put_status(401)
        |> halt()
    end
  end

  defp shop_id(conn) do
    body = conn.body_params

    get_in(body, ["account", "id"])
  end
end
