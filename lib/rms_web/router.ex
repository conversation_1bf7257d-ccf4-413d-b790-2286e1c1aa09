defmodule RmsWeb.Router do
  use RmsWeb, :router

  import Oban.Web.Router

  pipeline :api do
    plug Guardian.Plug.Pipeline,
      module: Rms.Guardian,
      error_handler: RmsWeb.AuthErrorHandler

    plug Guardian.Plug.VerifySession
    plug :accepts, ["json"]
    plug Guardian.Plug.VerifyHeader
    plug RmsWeb.Plugs.LogIdentifier
  end

  pipeline :authenticated_api do
    plug RmsWeb.Plugs.Authenticate
  end

  pipeline :oban_web do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :basic_auth
  end

  scope "/api", RmsWeb do
    pipe_through :api
  end

  pipeline :webhook do
    plug :accepts, ["json", "x-www-form-urlencoded"]
  end

  scope "/bff", RmsWeb.BFF do
    pipe_through [:api, :authenticated_api]
    get "/get_order/:id", OrderController, :get_order
    post "/create_order", OrderController, :create_order
    get "/external-orders", OrderController, :external_orders
    post "/import_shopify_order", OrderController, :import_shopify_order
  end

  scope "/api", RmsWeb do
    pipe_through [:api, :authenticated_api]

    get "/me", AuthController, :index
    post "/users/locations", UsersController, :assign_location

    scope "/reports", Reports do
      get "/payments", ReportsController, :payments
      get "/sellings_summary", ReportsController, :selling_summary
    end

    post "/organizations/order_settings", OrderSettingsController, :update
    put "/organizations/order_settings", OrderSettingsController, :update
    get "/organizations/order_settings", OrderSettingsController, :show

    get "/customers/paginated", CustomerController, :paginated_customers
    resources "/customers", CustomerController, except: [:new, :edit]

    patch "/customers/:customer_id/addresses", AddressController, :update_customer_address

    resources "/addresses", AddressController, except: [:new]

    post "/remote_print/:location_id", PrintController, :print

    resources "/orders", OrderController, except: [:new, :edit, :delete]
    post "/orders/:id/cancel", OrderController, :cancel
    get "/orders/by_df_key/:df_key", OrderController, :get_by_df_key
    resources "/locations", LocationsController, except: [:edit, :new]
    resources "/staffs", StaffsController, except: [:edit, :new]
    resources "/settings", SettingsController, except: [:edit, :new], singleton: true
    put "/settings/bulk_upsert", SettingsController, :bulk_upsert
    resources "/products", ProductsController, except: [:edit, :new]
    get "/products/by_barcode/:barcode", ProductsController, :get_by_barcode
    post "/products/import", ProductsController, :import
    post "/products/archive/:id", ProductsController, :archive

    post "/products/gift_handler_configuration",
         ProductsController,
         :create_gift_handler_configuration

    resources "/product_sync/configurations", ProductSyncConfigurationController,
      only: [:show, :create, :update],
      singleton: true

    resources "/addons", AddonsController, only: [:index]

    resources "/carts", CartController, only: [:update, :index]
    post "/carts/delivery_options", CartController, :delivery_options
    post "/carts/simulate", CartController, :simulate
    post "/carts/:id/group", CartController, :group

    resources "/packings", PackingController, only: [:index, :show]

    post "/discounts/authorization/validate", DiscountsController, :validate_authorization_request
    post "/discounts/authorization/create", DiscountsController, :create_authorization_request

    resources "/transactions", TransactionsController, only: [:create, :show]
    post "/transactions/:id/close", TransactionsController, :close
    post "/transactions/:id/cancel", TransactionsController, :cancel

    post "/payments", PaymentsController, :create
    post "/payments/subscriptions", PaymentsController, :create_subscriptions
    get "/payments/:id", PaymentsController, :show
    post "/payments/:id/cancel", PaymentsController, :cancel

    post "/gift_cards/check_balance", GiftCardsController, :check_balance

    post "/connect/:integration", ConnectIntegrationsController, :create
    resources "/integrations/customer", CustomerIntegrationsController, except: [:new, :edit]

    resources "/integrations/cancelation", CancelationIntegrationsController,
      except: [:new, :edit]

    post "/api_tokens", ApiTokensController, :create
    delete "/api_tokens", ApiTokensController, :delete

    resources "/inventory", InventoryController, except: [:new, :edit, :delete]

    get "/inventory/by_sku/:sku", InventoryController, :show_by_sku

    resources "/reverse_fulfillments", ReverseFulfillmentController, only: [:show]

    put "/inventory/by_sku/bulk", InventoryController, :update_bulk_by_sku
    post "/inventory/by_sku/bulk", InventoryController, :create_bulk_by_sku
    patch "/inventory/by_sku/bulk", InventoryController, :update_bulk_by_sku

    post "/inventory/by_sku/:sku", InventoryController, :create_by_sku
    patch "/inventory/by_sku/:sku", InventoryController, :update_by_sku
    put "/inventory/by_sku/:sku", InventoryController, :update_by_sku

    resources "/docks", DocksController, only: [:create, :show, :index, :delete]

    scope "/fiscal", Fiscal do
      resources "/series", InvoiceSerieController, except: [:new, :edit]
      resources "/invoices", FiscalInvoiceController, only: [:index, :show, :update]
      post "/invoices/import", FiscalInvoiceController, :import_fiscal_invoice
      post "/invoices/vinco_import/:id", FiscalInvoiceController, :import_vinco_fiscal_invoice

      post "/invoices/fulfillments/:fulfillment_id/import",
           FiscalInvoiceController,
           :import_fulfillment_invoice

      post "/invoices/reprocess/:id", FiscalInvoiceController, :reprocess_fiscal_invoice
      post "/invoices/:id/email", FiscalInvoiceController, :send_email
      resources "/product_taxes", ProductTaxesController, except: [:new, :edit]
      resources "/location_taxes", LocationTaxesController, except: [:edit, :new, :delete]
      get "/location_taxes/by_location/:location_id", LocationTaxesController, :show

      post "/settings/:location_id", FiscalSettingsController, :create

      resources "/settings", FiscalSettingsController,
        only: [:show, :update],
        param: "location_id"

      post "/product_taxes/upload_requests", UploadController, :create_upload_request
      post "/product_taxes/uploads/:id/confirm", UploadController, :confirm_upload
    end

    post "/notification/push", NotificationController, :create_push_notification

    resources "/demo", DemoController, only: [:create]
  end

  scope "/webhooks", RmsWeb.Webhooks do
    pipe_through :webhook

    post "/pagarme", PagarMeController, :create
    post "/cielo/notify_payment", CieloController, :notify_payment

    post "/shopify", ShopifyController, :create

    post "/vtex/product_changes/:app_key_suffix", VTEXController, :notify_product_change

    post "/vtex/pvt/orders/:marketplace_order_id/:action",
         VTEXController,
         :order_status_notification

    post "/vtex/order-integration/notification/processing-result",
         VTEXController,
         :order_processing_result

    post "/vtex/orders/order-group/:marketplace_order_id/notifications/seller-cancellation",
         VTEXController,
         :order_seller_cancellation

    post "/vtex/customers", VTEXController, :notify_customer_change

    post "/vtex/orders/hook", VTEXController, :notify_order_status
  end

  scope "/rms", RmsWeb.RetailManagement do
    pipe_through [:api, :authenticated_api]

    resources "/orders", RetailOrdersController
    resources "/fiscal-invoice", RetailFiscalInvoicesController
    get "/reports/transactions", RetailTransactionsController, :index
    get "/reports/stores", RetailStoresReportController, :index
    get "/reports/items", RetailItemsReportController, :index
    get "/reports/staff-transactions", RetailStaffTransactionReportController, :index
  end

  scope "/oban_web" do
    pipe_through :oban_web

    oban_dashboard("/default", oban_name: Oban, as: :oban_default)
    oban_dashboard("/events", oban_name: Rms.Events, as: :oban_events)
  end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:rms, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through [:fetch_session, :protect_from_forgery]

      live_dashboard "/dashboard", metrics: RmsWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview

      oban_dashboard("/oban_web", oban_name: Oban, as: :oban_default_dev)
      oban_dashboard("/oban_web_events", oban_name: Rms.Events, as: :oban_events_dev)
    end
  end

  defp basic_auth(conn, _opts) do
    username = Application.get_env(:rms, :oban_pro_user)
    password = Application.get_env(:rms, :oban_pro_password)
    Plug.BasicAuth.basic_auth(conn, username: username, password: password)
  end
end
