defmodule RmsWeb.SchemaValidator do
  use Params

  @doc """
  Validates the given `params` against the schema specified in `schema`.

  Example:

      iex> RmsWeb.SchemaValidator.validate(conn, :order, %{...})
      {:ok, %{...}}

      iex> RmsWeb.SchemaValidator.validate(conn, :order, %{...})
      {:error, %Ecto.Changeset{...}}

  """
  @spec validate(Plug.Conn.t(), atom, map) :: {:ok, map} | {:error, Ecto.Changeset.t()}
  def validate(conn, schema, params) do
    case Code.ensure_loaded(build_schema_module(conn)) do
      {:module, module} ->
        apply_schema(module, schema, [params])

      {:error, :nofile} ->
        raise ArgumentError, message: "Schema file not found"

      error ->
        raise ArgumentError, message: "Error to load schema: #{inspect(error)}"
    end
  end

  defp apply_schema(module, schema, [params]) do
    case apply(module, schema, [params]) do
      %Ecto.Changeset{valid?: true} = chst ->
        {:ok, Params.to_map(chst)}

      %Ecto.Changeset{valid?: false} = chst ->
        {:error, chst}
    end
  end

  defp build_schema_module(conn) do
    conn.private.phoenix_controller
    |> Atom.to_string()
    |> String.replace("Controller", "Schema")
    |> String.to_atom()
  end
end
