<!doctype html>
<html>
    <head>
    <style>
        .logo {
            width: 72px;
            height: auto;
        }

        .email-container {
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        .content {
            margin: 0 auto;
            max-width: 600px;
            border: 1px solid #DEE3E7;
            padding: 20px;
            border-radius: 15px;
        }

        .content .header {
            text-align: left;
        }

        .content .title {
            font-family: Helvetica;
            font-size: 24px;
            font-weight: 700;
            line-height: 24px;
            text-align: left;
            margin-bottom: 24px;
            color: #272E35;
        }

        .content .subtitle {
            font-family: Helvetica;
            font-size: 16px;
            font-weight: 400;
            line-height: 16px;
            text-align: left;
            color: #555F6D;
            margin-top: 10px;
        }

        .content .code {
            font-family: Helvetica;
            font-size: 24px;
            font-weight: 700;
            line-height: 28.8px;
            letter-spacing: 0.2em;
            text-align: center;
            color: #272E35;
            background-color: #DEE3E7;
            margin-top: 24px;
            padding: 8px 12px 8px 12px;
            border-radius: 8px;
        }

        .content .hr {
            border-top: 1px solid #DEE3E7;
            margin: 20px 0;
        }

        .content .body .body-header{
            font-family: Helvetica;
            font-size: 18px;
            font-weight: 700;
            line-height: 18px;
            text-align: left;
            color: #272E35;
            margin-bottom: 24px;
        }

        .content .body .discount .tag {
            padding: 4px 8px 4px 8px;
            display: inline-block;
            border-radius: 4px;
            font-family: Helvetica;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.1em;
            margin-bottom: 8px;
            text-align: left;
            color: #0023AF;
            background-color: #EBF0FF;
        }
        .content .body .discount {
            margin-top: 8px;
        }

        .content .body .discount .icon {
            padding: 8px;
            border-radius: 50%;
            height: 16px;
            margin-right: 8px;
            background-color: #F0F3F5;
        }
        .content .body .discount .icon img {
            height: 16px;
        }

        .content .body .discount .discount-title {
            color: #272E35;
            font-family: Helvetica;
            font-size: 14px;
            font-weight: 700;
            line-height: 16.8px;
            text-align: left;
        }

        .content .body .discount .discount-description {
            font-family: Helvetica;
            font-size: 12px;
            font-weight: 400;
            line-height: 14.4px;
            text-align: left;
            color: #555F6D;
        }


        .content .body .cart{
            margin-top: 20px;
        }

        .content .body .tag {
            padding: 4px 8px 4px 8px;
            display: inline-block;
            border-radius: 4px;
            font-family: Helvetica;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.1em;
            margin-bottom: 8px;
            text-align: left;
            color: #0023AF;
            background-color: #EBF0FF;
        }
        .content .body .cart .icon {
            padding: 8px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
            background-color: #F0F3F5;
        }
        .content .body .cart .icon img {
            height: 16px;
        }

        .content .body .cart .cart-title {
            color: #272E35;
            font-family: Helvetica;
            font-size: 14px;
            font-weight: 400;
            line-height: 14.4px;
            text-align: left;

        }

        .content .body .cart .cart-description {
            font-family: Helvetica;
            font-size: 12px;
            font-weight: 400;
            line-height: 14.4px;
            text-align: left;
        }
        .content .body .cart .cart-description .from {
            display: inline-block;
            font-family: Helvetica;
            text-decoration: line-through;
            font-size: 14px;
            font-weight: 700;
            line-height: 16.8px;
            text-align: left;
            color: #272E35;
            vertical-align: middle;
        }

        .content .body .cart .cart-description .arrow {
            display: inline-block;
            width: 16px;
            color: #9EA8B3;
            margin: 0 4px;
            vertical-align: middle;
        }

        .content .body .cart .cart-description .arrow img {
            width: 16px;
        }

        .content .body .cart .cart-description .to {
            display: inline-block;
            font-family: Helvetica;
            font-size: 14px;
            font-weight: 700;
            line-height: 16.8px;
            text-align: left;
            color: #16610F;
            vertical-align: middle;
        }

        .footer {
            text-align: center;
            font-size: 14px;
            margin-top: 24px;
            color: #9EA8B3;
        }
    </style>

    </head>
    <body>
        <div class="email-container">
            <div class="content">
                <div class="header">
                    <img src="https://iglu-public.s3.amazonaws.com/logo_iglu.png" alt="logo" class="logo">
                </div>
                <div class="hr"></div>
                <div class="title"><%= @cart.staff.name |> String.split(" ") |> Enum.map_join(" ", & String.capitalize(&1)) %> solicitou aplicar descontos em um carrinho</div>
                <div class="body">
                    <div class="tag">
                        <%= if length(@cart.discounts) > 1 do %>
                            Descontos
                        <% else %>
                            Desconto
                        <% end %>
                    </div>
                    <%= for discount <- @cart.discounts do %>
                        <div class="discount">
                            <table>
                                <tr>
                                <td rowspan="2"><div class="icon">
                                    <img src="https://iglu-public.s3.amazonaws.com/discount.png" alt="discount">
                                <td><div class="discount-description">"<%= discount.description %>"</div></td>
                                </tr>
                                <tr>
                                <td><div class="discount-title"><%= discount.value %> de desconto</div></td>
                                </tr>
                            </table>
                        </div>
                    <% end %>
                    <div class="cart">
                        <div class="tag">Carrinho</div>
                        <table>
                            <tr>
                            <td rowspan="2"><div class="icon">
                                <img src="https://iglu-public.s3.amazonaws.com/shopping_cart.png" alt="cart">
                            <td>
                                <div class="cart-title">
                                    <%= if length(@cart.items) > 1 do %>
                                        <%= length(@cart.items) %> itens
                                    <% else %>
                                        <%= length(@cart.items) %> item
                                    <% end %>
                                </div>
                            </td>
                            </tr>
                            <tr>
                            <td><div class="cart-description">
                                <div class="from">R$ <%= @cart.from %></div>
                                <div class="arrow">
                                    <img src="https://iglu-public.s3.amazonaws.com/arrow.png" alt="arrow">
                                </div>
                                <div class="to">R$ <%= @cart.to %></div>
                            </div></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="hr"></div>
                <div class="subtitle">Para aceitar a solicitação, compartilhe com o(a) vendedor(a) o código abaixo:</div>
                <div class="code"><%= @code %></div>
            </div>
            <div class="footer">
                <p>iGlu Tecnologia<br>Rua Surubim, 577, 18º andar - Cidade Monções, São Paulo - SP</p>
            </div>
        </div>
    </body>
</html>
