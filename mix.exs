defmodule Rms.MixProject do
  use Mix.Project

  def project do
    [
      app: :rms,
      version: "0.1.0",
      elixir: "~> 1.14",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      releases: [
        rms: [
          applications: [
            opentelemetry_exporter: :permanent,
            opentelemetry: :temporary
          ]
        ]
      ],
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Rms.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:prod), do: ["lib"]
  defp elixirc_paths(:dev), do: ["lib", "test/support/factory.ex"]
  defp elixirc_paths(:test), do: ["lib", "test/support"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:phoenix, "~> 1.7.10"},
      {:phoenix_ecto, "~> 4.4"},
      {:ecto_sql, "~> 3.10"},
      {:ecto, "~> 3.10", override: true},
      {:postgrex, "~> 0.8", override: true},
      {:phoenix_live_dashboard, "~> 0.8.2"},
      {:swoosh, "~> 1.3"},
      {:finch, "~> 0.13"},
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.1.1"},
      {:plug_cowboy, "~> 2.5"},
      {:plug_heartbeat, "~> 1.0"},
      {:guardian, "~> 2.0"},
      {:cloak, "1.1.1"},
      {:cloak_ecto, "~> 1.2.0"},
      {:tesla, "~> 1.4"},
      {:ex_machina, "~> 2.7.0", only: [:dev, :test]},
      {:brcpfcnpj, "~> 1.0"},
      {:excellent_migrations, "~> 0.1", only: [:dev, :test], runtime: false},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:oban, "~> 2.17"},
      {:paginator, "~> 1.2.0"},
      {:mox, "~> 1.2", only: :test},
      {:tz, "~> 0.26.5"},
      {:elixir_xml_to_map, "~> 2.0"},
      {:opentelemetry_exporter, "~> 1.8"},
      {:opentelemetry, "~> 1.5"},
      {:opentelemetry_api, "~> 1.4.0"},
      {:hackney, "~> 1.8"},
      {:opentelemetry_tesla, "~> 2.2.0"},
      {:opentelemetry_phoenix, "~> 1.2"},
      {:opentelemetry_cowboy, "~> 0.3"},
      {:opentelemetry_oban, "~> 1.0"},
      {:opentelemetry_finch, "~> 0.1"},
      {:opentelemetry_ecto, "~> 1.0"},
      {:phoenix_swoosh, "~> 1.0"},
      {:premailex, "~> 0.3.0"},
      {:ldclient, "~> 3.0.0", hex: :launchdarkly_server_sdk},
      {:oban_web, "~> 2.10", repo: "oban"},
      {:oban_pro, "~> 1.5", repo: "oban"},
      {:uniq, "~> 0.6.1"},
      {:faker, "~> 0.18.0", only: :test},
      {:params, "~> 2.3.0"},
      {:remote_ip, "~> 1.2"},
      {:logger_json, "~> 6.1"},
      {:nimble_csv, "~> 1.2"},
      {:ex_aws, "~> 2.5"},
      {:ex_aws_s3, "~> 2.5"},
      {:sweet_xml, "~> 0.7"},
      {:prometheus_telemetry, "~> 0.4"},
      {:cachex, "~> 4.0"},
      {:unidecode, "~> 1.0"}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup"],
      "ecto.setup": ["ecto.create", "ecto.load", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.migrate": ["excellent_migrations.migrate", "ecto.dump"],
      "ecto.rollback": ["ecto.rollback", "ecto.dump"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.migrate --quiet", "test"]
    ]
  end
end
