defmodule Rms.Repo.Migrations.BackfillPlainfieldsOnCustomer do
  use Ecto.Migration

  import Ecto.Query

  alias Rms.Customers.Customer

  @disable_ddl_transaction true
  @disable_migration_lock true

  @chunk_size 5_000

  def run(repo) do
    ## Necessário para carregar a config do Cloak
    Rms.Vault.start_link()

    query =
      from(t in Customer,
        where: is_nil(t.document_plain) and not is_nil(t.document),
        or_where: is_nil(t.email_plain) and not is_nil(t.email),
        or_where: is_nil(t.primary_phone_number_plain) and not is_nil(t.primary_phone_number),
        select: [:id, :document, :email, :primary_phone_number]
      )

    repo.transaction(fn ->
      customers = repo.stream(query)

      customers
      |> Stream.chunk_every(@chunk_size)
      |> Stream.each(fn stream -> do_update(repo, stream) end)
      |> Stream.run()
    end, timeout: :infinity)
  end

  defp do_update(repo, customers) do
    {ids, new_documents, new_emails, new_phone_numbers} =
      customers
      |> Enum.map(fn c -> [c.id, c.document, c.email, c.primary_phone_number] end)
      |> List.zip()
      |> Enum.map(&Tuple.to_list/1)
      |> then(fn [ids, documents, emails, phones] -> {ids, documents, emails, phones} end)

    Customer
    |> join(
      :inner,
      [c],
      cn in fragment(
        "SELECT * FROM unnest(?::integer[], ?::text[], ?::text[], ?::text[]) AS id_to_new_document(id, new_document, new_email, new_phone_number)",
        type(^ids, {:array, :integer}),
        type(^new_documents, {:array, :string}),
        type(^new_emails, {:array, :string}),
        type(^new_phone_numbers, {:array, :string})
      ),
      on: c.id == cn.id
    )
    |> update([_, cn],
      set: [
        document_plain: cn.new_document,
        email_plain: cn.new_email,
        primary_phone_number_plain: cn.new_phone_number
      ]
    )
    |> repo.update_all([], log: :info)
  end
end
