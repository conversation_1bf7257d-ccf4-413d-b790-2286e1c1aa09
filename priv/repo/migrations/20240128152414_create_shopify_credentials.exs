defmodule Rms.Repo.Migrations.CreateShopifyCredentials do
  use Ecto.Migration

  def change do
    create table(:shopify_credentials) do
      add :shop, :string
      add :credential, :string
      add :organization_id, references(:organizations, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:shopify_credentials, [:organization_id])
    create unique_index(:shopify_credentials, [:shop])
  end
end
