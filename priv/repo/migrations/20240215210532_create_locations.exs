defmodule Rms.Repo.Migrations.CreateLocations do
  use Ecto.Migration

  def change do
    create table(:locations) do
      add :name, :string
      add :cnpj, :string, null: false
      add :organization_id, references(:organizations, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:locations, [:organization_id])
    create unique_index(:locations, [:cnpj])
  end
end
