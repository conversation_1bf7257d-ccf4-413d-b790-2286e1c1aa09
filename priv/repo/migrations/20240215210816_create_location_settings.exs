defmodule Rms.Repo.Migrations.CreateLocationSettings do
  use Ecto.Migration

  def change do
    create table(:location_settings) do
      add :key, :string
      add :value, :map
      add :location_id, references(:locations, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:location_settings, [:location_id])
    create unique_index(:location_settings, [:location_id, :key])
  end
end
