defmodule Rms.Repo.Migrations.CreateOrganizationSettings do
  use Ecto.Migration

  def change do
    create table(:organization_settings) do
      add :key, :string
      add :value, :map
      add :organization_id, references(:organizations, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:organization_settings, [:organization_id])
    create unique_index(:organization_settings, [:organization_id, :key])
  end
end
