defmodule Rms.Repo.Migrations.CreateOrders do
  use Ecto.Migration

  def change do
    create table(:orders) do
      add :total_amount, :decimal, null: false
      add :status, :string, null: false
      add :external_id, :string
      add :location_id, references(:locations, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:orders, [:external_id])
    create index(:orders, [:location_id])
  end
end
