defmodule Rms.Repo.Migrations.CreateTransactions do
  use Ecto.Migration

  def change do
    create table(:transactions) do
      add :status, :string
      add :order_id, references(:orders, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:transactions, [:order_id, :status])
    create unique_index(:transactions, [:order_id], where: "status = 'open'")
  end
end
