defmodule Rms.Repo.Migrations.CreatePayments do
  use Ecto.Migration

  def change do
    create table(:payments) do
      add :amount, :decimal, null: false
      add :status, :string, null: false
      add :method, :string, null: false
      add :metadata, :map
      add :transaction_id, references(:transactions, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:payments, [:transaction_id])
  end
end
