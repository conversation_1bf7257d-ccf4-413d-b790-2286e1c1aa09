defmodule Rms.Repo.Migrations.CreateTransactionCustomers do
  use Ecto.Migration

  def change do
    create table(:transaction_customers) do
      add :name, :string
      add :document_type, :string, null: false
      add :document, :binary, null: false
      add :transaction_id, references(:transactions, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:transaction_customers, [:transaction_id])
  end
end
