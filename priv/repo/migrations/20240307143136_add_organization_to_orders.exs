defmodule Rms.Repo.Migrations.AddOrganizationToOrders do
  use Ecto.Migration

  def change do
    alter table(:orders) do
      add :organization_id, references(:organizations, on_delete: :nothing)
    end

    execute(
      """
        UPDATE orders
        SET organization_id = locations.organization_id
        FROM locations
        WHERE orders.location_id = locations.id
      """,
      ""
    )

    alter table(:orders) do
      modify :organization_id, :bigint, null: false
    end
  end
end
