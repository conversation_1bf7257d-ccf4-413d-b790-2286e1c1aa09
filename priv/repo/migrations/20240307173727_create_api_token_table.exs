defmodule Rms.Repo.Migrations.CreateApiTokenTable do
  use Ecto.Migration

  def change do
    create table(:api_tokens) do
      add :token, :binary, null: false
      add :token_hash, :binary, null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:api_tokens, [:organization_id])
    create unique_index(:api_tokens, [:token_hash])
  end
end
