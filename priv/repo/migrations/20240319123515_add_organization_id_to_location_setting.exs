defmodule Rms.Repo.Migrations.AddOrganizationIdToLocationSetting do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def up do
    drop constraint(:location_settings, :location_settings_location_id_fkey)

    create unique_index(:locations, [:id, :organization_id], concurrently: true)

    alter table(:location_settings) do
      add :organization_id, :id
    end

    execute "UPDATE location_settings SET organization_id = (SELECT organization_id FROM locations WHERE locations.id = location_settings.location_id)"

    alter table(:location_settings) do
      # excellent_migrations:safety-assured-for-next-line not_null_added column_type_changed
      modify :organization_id, :id, null: false
      # excellent_migrations:safety-assured-for-next-line not_null_added column_type_changed
      modify :location_id,
             references(:locations, with: [organization_id: :organization_id], match: :full),
             null: false
    end
  end

  def down do
    drop constraint(:location_settings, :location_settings_location_id_fkey)

    drop unique_index(:locations, [:id, :organization_id], concurrently: true)

    alter table(:location_settings) do
      # excellent_migrations:safety-assured-for-next-line column_removed
      remove :organization_id

      # excellent_migrations:safety-assured-for-next-line column_type_changed
      modify :location_id, references(:locations), null: true
    end
  end
end
