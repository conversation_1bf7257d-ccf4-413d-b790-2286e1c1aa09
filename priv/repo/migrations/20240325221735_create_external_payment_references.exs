defmodule Rms.Repo.Migrations.CreateExternalPaymentReferences do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:external_payment_references) do
      add :partner, :string
      add :external_id, :string
      add :payment_id, references(:payments, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create unique_index(:external_payment_references, [:payment_id], concurrently: true)
  end
end
