defmodule Rms.Repo.Migrations.CreatePagarmeHookCredentials do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:pagarme_hook_credentials) do
      add :shop_id, :string
      add :credential, :binary
      add :organization_id, references(:organizations, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create unique_index(:pagarme_hook_credentials, [:organization_id], concurrently: true)
  end
end
