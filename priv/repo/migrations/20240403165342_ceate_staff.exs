defmodule Rms.Repo.Migrations.CeateStaff do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:staffs) do
      add :name, :string
      add :external_id, :string
      add :archived_at, :utc_datetime
      add :organization_id, references(:organizations, on_delete: :nothing), null: false
      timestamps(type: :utc_datetime)
    end

    create index(:staffs, [:archived_at], where: "archived_at IS NULL", concurrently: true)
    create unique_index(:staffs, [:external_id, :organization_id], concurrently: true)
  end
end
