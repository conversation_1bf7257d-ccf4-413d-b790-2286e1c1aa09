defmodule Rms.Repo.Migrations.CeateStaffLocation do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:staffs, [:id, :organization_id], concurrently: true)

    create table(:staffs_locations) do
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :staff_id,
          references(:staffs, with: [organization_id: :organization_id], on_delete: :delete_all)

      add :location_id,
          references(:locations,
            with: [organization_id: :organization_id],
            on_delete: :delete_all
          )
    end
  end
end
