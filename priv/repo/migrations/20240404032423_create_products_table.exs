defmodule Rms.Repo.Migrations.CreateProductsRelatedTables do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:products) do
      add :name, :string
      add :ncm, :string
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps()
    end

    create index(:products, [:organization_id], concurrently: true)
    create unique_index(:products, [:id, :organization_id], concurrently: true)
  end
end
