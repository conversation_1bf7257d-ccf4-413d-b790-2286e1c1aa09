defmodule Rms.Repo.Migrations.CreateProductVariantsTable do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:product_variants) do
      add :name, :string
      add :list_price, :decimal, null: false
      add :sku, :string
      add :bar_code, :string
      add :image_urls, {:array, :string}
      add :variation_types, {:array, :map}

      add :product_id,
          references(:products, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps()
    end

    create index(:product_variants, [:product_id], concurrently: true)
    create index(:product_variants, [:organization_id], concurrently: true)
    create unique_index(:product_variants, [:id, :organization_id], concurrently: true)
  end
end
