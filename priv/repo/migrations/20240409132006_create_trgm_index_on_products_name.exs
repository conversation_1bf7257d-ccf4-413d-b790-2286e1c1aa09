defmodule Rms.Repo.Migrations.CreateTrgmIndexOnProductsName do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "CREATE INDEX CONCURRENTLY products_name_trgm_idx ON products USING gin (name gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS products_name_trgm_idx"

    execute "CREATE INDEX CONCURRENTLY product_variants_name_trgm_idx ON product_variants USING gin (name gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS product_variants_name_trgm_idx"

    execute "CREATE INDEX CONCURRENTLY product_variants_bar_code_trgm_idx ON product_variants USING gin (bar_code gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS product_variants_bar_code_trgm_idx"
  end
end
