defmodule Rms.Repo.Migrations.CreateLineItems do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:line_items) do
      add :price, :decimal, null: false
      add :quantity, :integer, null: false
      add :shipping_method, :string, null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :product_variant_id, references(:product_variants, on_delete: :nothing),
        null: false,
        with: [organization_id: :organization_id]

      add :order_id, references(:orders, on_delete: :nothing),
        null: false,
        with: [organization_id: :organization_id]

      add :staff_id, references(:staffs, on_delete: :nothing),
        with: [organization_id: :organization_id]

      add :location_id, references(:locations, on_delete: :nothing),
        null: false,
        with: [organization_id: :organization_id]

      timestamps(type: :utc_datetime)
    end

    create unique_index(:line_items, [:id, :organization_id], concurrently: true)
    create index(:line_items, [:product_variant_id], concurrently: true)
    create index(:line_items, [:order_id], concurrently: true)
    create index(:line_items, [:staff_id], concurrently: true)
    create index(:line_items, [:location_id], concurrently: true)
  end
end
