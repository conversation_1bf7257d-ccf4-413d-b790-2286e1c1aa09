defmodule Rms.Repo.Migrations.UpdatePagarmeHookCredentialIndex do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    alter table(:pagarme_hook_credentials) do
      add :credential_hash, :binary
    end

    drop unique_index(:pagarme_hook_credentials, [:organization_id], concurrently: true)
    create unique_index(:pagarme_hook_credentials, [:shop_id], concurrently: true)
  end
end
