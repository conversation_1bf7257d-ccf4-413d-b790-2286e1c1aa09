defmodule Rms.Repo.Migrations.CreateLocationUsers do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:users, [:id, :organization_id], concurrently: true)

    create table(:location_users) do
      add :user_id,
          references(:users, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      add :location_id,
          references(:locations, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false
    end

    create unique_index(:location_users, [:user_id, :location_id], concurrently: true)
  end
end
