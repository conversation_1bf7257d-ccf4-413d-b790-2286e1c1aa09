defmodule Rms.Repo.Migrations.MakeProductVariantsReferenceDeleteAll do
  use Ecto.Migration

  def change do
    alter table(:product_variants) do
      # excellent_migrations:safety-assured-for-next-line column_type_changed not_null_added
      modify :product_id,
             references(:products,
               on_delete: :delete_all,
               with: [organization_id: :organization_id]
             ),
             from:
               references(:products,
                 on_delete: :nothing,
                 with: [organization_id: :organization_id]
               ),
             null: false
    end
  end
end
