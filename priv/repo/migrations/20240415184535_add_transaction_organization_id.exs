defmodule Rms.Repo.Migrations.AddTransactionOrganizationId do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed column_type_changed not_null_added column_reference_added

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:orders, [:id, :organization_id], concurrently: true)

    alter table(:transactions) do
      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          )
    end

    execute(
      """
        UPDATE transactions
        SET organization_id = orders.organization_id
        FROM orders
        WHERE transactions.order_id = orders.id
      """,
      ""
    )

    alter table(:transactions) do
      modify :organization_id, :bigint, null: false, from: {:bigint, null: true}

      modify :order_id,
             references(:orders, with: [organization_id: :organization_id], on_delete: :nothing),
             from: references(:orders, on_delete: :nothing)
    end
  end
end
