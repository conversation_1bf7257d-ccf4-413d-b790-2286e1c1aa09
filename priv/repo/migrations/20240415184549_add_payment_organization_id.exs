defmodule Rms.Repo.Migrations.AddPaymentOrganizationId do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed column_type_changed not_null_added column_reference_added

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:transactions, [:id, :organization_id], concurrently: true)

    alter table(:payments) do
      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          )
    end

    execute(
      """
        UPDATE payments
        SET organization_id = transactions.organization_id
        FROM transactions
        WHERE payments.transaction_id = transactions.id
      """,
      ""
    )

    alter table(:payments) do
      modify :organization_id, :bigint, null: false, from: {:bigint, null: true}

      modify :transaction_id,
             references(:transactions,
               with: [organization_id: :organization_id],
               on_delete: :nothing
             ),
             from: references(:transactions, on_delete: :nothing)
    end
  end
end
