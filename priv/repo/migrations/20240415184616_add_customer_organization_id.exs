defmodule Rms.Repo.Migrations.AddCustomerOrganizationId do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed column_type_changed not_null_added column_reference_added

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    alter table(:transaction_customers) do
      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          )
    end

    execute(
      """
        UPDATE transaction_customers
        SET organization_id = transactions.organization_id
        FROM transactions
        WHERE transaction_customers.transaction_id = transactions.id
      """,
      ""
    )

    alter table(:transaction_customers) do
      modify :organization_id, :bigint, null: false, from: {:bigint, null: true}

      modify :transaction_id,
             references(:transactions,
               with: [organization_id: :organization_id],
               on_delete: :nothing
             ),
             from: references(:transactions, on_delete: :nothing)
    end
  end
end
