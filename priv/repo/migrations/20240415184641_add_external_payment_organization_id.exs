defmodule Rms.Repo.Migrations.AddExternalPaymentOrganizationId do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed column_type_changed not_null_added column_reference_added

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:payments, [:id, :organization_id], concurrently: true)

    alter table(:external_payment_references) do
      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          )
    end

    execute(
      """
        UPDATE external_payment_references
        SET organization_id = payments.organization_id
        FROM payments
        WHERE external_payment_references.payment_id = payments.id
      """,
      ""
    )

    alter table(:external_payment_references) do
      modify :organization_id, :bigint, null: false, from: {:bigint, null: true}

      modify :payment_id,
             references(:payments, with: [organization_id: :organization_id], on_delete: :nothing),
             from: references(:payments, on_delete: :nothing)
    end
  end
end
