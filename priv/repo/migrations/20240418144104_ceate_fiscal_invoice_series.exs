defmodule Rms.Repo.Migrations.CeateFiscalInvoiceSeries do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:invoice_series) do
      add :invoice_type, :string, null: false
      add :invoice_serie, :integer, null: false
      add :invoice_env, :string, null: false
      add :status, :string, null: false
      add :available_number, :integer, null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :location_id,
          references(:locations, with: [organization_id: :organization_id], on_delete: :nothing),
          null: false

      timestamps(type: :utc_datetime)
    end

    create index(:invoice_series, [:invoice_type], concurrently: true)
    create index(:invoice_series, [:status], concurrently: true)
    create index(:invoice_series, [:organization_id], concurrently: true)
    create index(:invoice_series, [:location_id], concurrently: true)

    create unique_index(:invoice_series, [:invoice_type, :invoice_env, :location_id],
             where: "status = 'active'",
             concurrently: true
           )
  end
end
