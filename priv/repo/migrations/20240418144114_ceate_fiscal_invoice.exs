defmodule Rms.Repo.Migrations.CeateFiscalInvoice do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:invoice_series, [:id, :organization_id], concurrently: true)

    create table(:fiscal_invoices) do
      add :invoice_number, :integer, null: false
      add :operation_type, :string, null: false
      add :service, :string, null: false
      add :external_id, :string
      add :df_key, :string
      add :status, :string, null: false

      add :autorized_at, :utc_datetime
      add :canceled_at, :utc_datetime

      add :qr_code, :text
      add :xml, :text
      add :danfe, :text

      add :metadata, :map

      add :customer_id, references(:transaction_customers, on_delete: :nothing)
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :serie_id,
          references(:invoice_series,
            with: [organization_id: :organization_id],
            on_delete: :nothing
          ),
          null: false

      timestamps(type: :utc_datetime)
    end

    create index(:fiscal_invoices, [:operation_type], concurrently: true)
    create index(:fiscal_invoices, [:invoice_number], concurrently: true)
  end
end
