defmodule Rms.Repo.Migrations.CeateFiscalInvoiceNumberTrigger do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed

  def up do
    execute("""
    CREATE OR REPLACE FUNCTION update_sequence()
    RETURNS TRIGGER AS $$
    DECLARE
    a_number INTEGER;
    BEGIN
      SELECT available_number INTO a_number FROM invoice_series WHERE id = NEW.serie_id FOR UPDATE;
      NEW.invoice_number = a_number;
      UPDATE invoice_series SET available_number = available_number + 1 WHERE id = NEW.serie_id;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)

    execute("""
    CREATE TRIGGER invoce_numbers_insert_trigger
    BEFORE INSERT ON fiscal_invoices
    FOR EACH ROW EXECUTE FUNCTION update_sequence();
    """)
  end

  def down do
    execute("DROP TRIGGER IF EXISTS invoce_numbers_insert_trigger ON invoce_numbers;")
    execute("DROP FUNCTION IF EXISTS update_sequence();")
  end
end
