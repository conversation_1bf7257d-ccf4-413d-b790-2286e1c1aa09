defmodule Rms.Repo.Migrations.CreateVtexCredentials do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:vtex_credentials) do
      add :account_name, :string, null: false
      add :app_key, :binary, null: false
      add :app_token, :binary, null: false
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:vtex_credentials, [:organization_id], concurrently: true)
    create index(:vtex_credentials, [:account_name], concurrently: true)
  end
end
