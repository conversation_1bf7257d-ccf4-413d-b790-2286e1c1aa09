defmodule Rms.Repo.Migrations.CreateInvoicePayment do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:fiscal_invoices, [:id, :organization_id], concurrently: true)

    create table(:invoices_payments) do
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :fiscal_invoice_id,
          references(:fiscal_invoices,
            with: [organization_id: :organization_id],
            on_delete: :nothing
          )

      add :payment_id,
          references(:payments,
            with: [organization_id: :organization_id],
            on_delete: :nothing
          )
    end
  end
end
