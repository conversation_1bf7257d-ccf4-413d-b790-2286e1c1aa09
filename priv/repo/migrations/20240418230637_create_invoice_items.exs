defmodule Rms.Repo.Migrations.CreateInvoiceItems do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:invoices_items) do
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :fiscal_invoice_id,
          references(:fiscal_invoices,
            with: [organization_id: :organization_id],
            on_delete: :nothing
          )

      add :line_item_id,
          references(:line_items,
            with: [organization_id: :organization_id],
            on_delete: :nothing
          )
    end
  end
end
