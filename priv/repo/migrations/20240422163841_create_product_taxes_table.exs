defmodule Rms.Repo.Migrations.CreateProductTaxesTable do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:product_taxes) do
      add :ncm, :string
      add :uf, :string
      add :origin, :string, null: false

      add :cfop, :string, null: false
      add :cest, :string, null: false

      add :cst_icms, :string, null: false
      add :cst_pis, :string, null: false
      add :cst_cofins, :string, null: false

      add :icms_percentage, :decimal, null: false
      add :fcp_percentage, :decimal, null: false
      add :pis_percentage, :decimal, null: false
      add :cofins_percentage, :decimal, null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:product_taxes, [:organization_id],
             where: "ncm IS NULL and uf IS NULL",
             concurrently: true
           )

    create unique_index(:product_taxes, [:uf, :organization_id],
             where: "ncm IS NULL",
             concurrently: true
           )

    create unique_index(:product_taxes, [:ncm, :organization_id],
             where: "uf IS NULL",
             concurrently: true
           )

    create unique_index(:product_taxes, [:ncm, :uf, :organization_id], concurrently: true)
  end
end
