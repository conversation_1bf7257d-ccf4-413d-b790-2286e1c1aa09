defmodule Rms.Repo.Migrations.CreateInterstateTaxes do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:interstate_taxes) do
      add :uf_origin, :string
      add :uf_destiny, :string

      add :icms_percentage, :decimal, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:interstate_taxes, [:uf_origin, :uf_destiny], concurrently: true)
  end
end
