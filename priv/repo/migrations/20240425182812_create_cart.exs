defmodule Rms.Repo.Migrations.CreateCart do
  use Ecto.Migration

  def change do
    create table(:carts) do
      add :status, :string, null: false
      add :note, :string
      add :total_amount, :decimal, null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :location_id, references(:locations, on_delete: :delete_all),
        with: [organization_id: :organization_id]

      add :customer_id, references(:customers, on_delete: :delete_all),
        with: [organization_id: :organization_id]

      add :staff_id, references(:staffs, on_delete: :nothing),
        with: [organization_id: :organization_id]

      timestamps(type: :utc_datetime)
    end
  end
end
