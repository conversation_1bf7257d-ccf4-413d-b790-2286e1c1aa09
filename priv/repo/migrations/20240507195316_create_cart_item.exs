defmodule Rms.Repo.Migrations.CreateCartItem do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:cart_items) do
      add :price, :decimal, null: false
      add :quantity, :integer, null: false
      add :shipping_method, :string

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :product_variant_id, references(:product_variants, on_delete: :nothing),
        null: false,
        with: [organization_id: :organization_id]

      add :cart_id, references(:carts, on_delete: :delete_all),
        null: false,
        with: [organization_id: :organization_id]

      add :staff_id, references(:staffs, on_delete: :nothing),
        with: [organization_id: :organization_id]

      add :location_id, references(:locations, on_delete: :nothing),
        with: [organization_id: :organization_id]

      timestamps(type: :utc_datetime)
    end

    create unique_index(:cart_items, [:id, :organization_id], concurrently: true)
    create index(:cart_items, [:product_variant_id], concurrently: true)
    create index(:cart_items, [:cart_id], concurrently: true)
    create index(:cart_items, [:staff_id], concurrently: true)
    create index(:cart_items, [:location_id], concurrently: true)
  end
end
