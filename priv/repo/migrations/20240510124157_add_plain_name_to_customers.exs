defmodule Rms.Repo.Migrations.AddPlainNameToCustomers do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "CREATE EXTENSION IF NOT EXISTS citext", "DROP EXTENSION IF EXISTS citext"

    alter table(:customers) do
      add :plain_name, :citext
    end

    execute "CREATE INDEX CONCURRENTLY customers_plain_name_trgm_idx ON customers USING gin (plain_name gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS customers_plain_name_trgm_idx"
  end
end
