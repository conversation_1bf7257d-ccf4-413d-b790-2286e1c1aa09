defmodule Rms.Repo.Migrations.DropUniquenessOnCustomers do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    drop unique_index(
           :customers,
           [:organization_id, :document_type, :document_hash],
           concurrently: true
         )

    drop unique_index(:customers, [:organization_id, :email_hash], concurrently: true)
  end
end
