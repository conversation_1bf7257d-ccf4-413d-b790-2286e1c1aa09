defmodule Rms.Repo.Migrations.AddIndexOnLocationsName do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "CREATE INDEX CONCURRENTLY locations_name_trgm_idx ON locations USING gin (name gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS locations_name_trgm_idx"
  end
end
