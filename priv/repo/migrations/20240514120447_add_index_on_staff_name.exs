defmodule Rms.Repo.Migrations.AddIndexOnStaffName do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "CREATE INDEX CONCURRENTLY staffs_name_trgm_idx ON staffs USING gin (name gin_trgm_ops)",
            "DROP INDEX CONCURRENTLY IF EXISTS staffs_name_trgm_idx"
  end
end
