defmodule Rms.Repo.Migrations.CreateLocationFiscalConfigurations do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:location_taxes) do
      add :ie, :string, null: false
      add :crt, :string, null: false
      add :name, :string, null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :location_id,
          references(:locations, with: [organization_id: :organization_id], on_delete: :nothing),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:location_taxes, [:location_id], concurrently: true)
  end
end
