defmodule Rms.Repo.Migrations.FixReferencesWith do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  # excellent_migrations:safety-assured-for-this-file column_type_changed not_null_added column_reference_added
  def change do
    create unique_index(:customers, [:id, :organization_id], concurrently: true)

    alter table(:line_items) do
      modify :product_variant_id,
             references(:product_variants,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             null: false,
             from:
               references(:product_variants,
                 on_delete: :nothing
               )

      modify :order_id,
             references(:orders, on_delete: :nothing, with: [organization_id: :organization_id]),
             null: false,
             from: references(:orders, on_delete: :nothing)

      modify :staff_id,
             references(:staffs, on_delete: :nothing, with: [organization_id: :organization_id]),
             from: references(:staffs, on_delete: :nothing)

      modify :location_id,
             references(:locations,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             null: false,
             from:
               references(:locations,
                 on_delete: :nothing
               )
    end

    alter table(:carts) do
      modify :location_id,
             references(:locations,
               on_delete: :delete_all,
               with: [organization_id: :organization_id]
             ),
             from: references(:locations, on_delete: :delete_all)

      modify :customer_id,
             references(:customers,
               on_delete: :delete_all,
               with: [organization_id: :organization_id]
             ),
             from: references(:customers, on_delete: :delete_all)

      modify :staff_id,
             references(:staffs, on_delete: :nothing, with: [organization_id: :organization_id]),
             from: references(:staffs, on_delete: :nothing)
    end

    create unique_index(:carts, [:id, :organization_id], concurrently: true)

    alter table(:cart_items) do
      modify :product_variant_id,
             references(:product_variants,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             null: false,
             from: references(:product_variants, on_delete: :nothing)

      modify :cart_id,
             references(:carts, on_delete: :delete_all, with: [organization_id: :organization_id]),
             null: false,
             from: references(:carts, on_delete: :delete_all)

      modify :staff_id,
             references(:staffs, on_delete: :nothing, with: [organization_id: :organization_id]),
             from: references(:staffs, on_delete: :nothing)

      modify :location_id,
             references(:locations,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             from: references(:locations, on_delete: :nothing)
    end
  end
end
