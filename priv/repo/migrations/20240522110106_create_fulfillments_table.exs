defmodule Rms.Repo.Migrations.CreateFulfillmentsTable do
  use Ecto.Migration

  def change do
    create table(:fulfillments) do
      add :shipping_method, :string, null: false
      add :external_reference, :string

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :order_id,
          references(:orders, on_delete: :delete_all, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
