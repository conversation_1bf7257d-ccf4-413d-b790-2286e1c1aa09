defmodule Rms.Repo.Migrations.CreateFulfillmentsItemsTable do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create unique_index(:fulfillments, [:id, :organization_id, :order_id], concurrently: true)
    create unique_index(:fulfillments, [:id, :organization_id], concurrently: true)

    create unique_index(:line_items, [:id, :organization_id, :order_id], concurrently: true)

    create table(:fulfillment_items) do
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :order_id,
          references(:orders, on_delete: :delete_all, with: [organization_id: :organization_id]),
          null: false

      add :fulfillment_id,
          references(:fulfillments,
            with: [organization_id: :organization_id, order_id: :order_id],
            on_delete: :delete_all
          )

      add :line_item_id,
          references(:line_items,
            with: [organization_id: :organization_id, order_id: :order_id],
            on_delete: :delete_all
          )

      timestamps(type: :utc_datetime)
    end

    create unique_index(:fulfillment_items, [:line_item_id], concurrently: true)
  end
end
