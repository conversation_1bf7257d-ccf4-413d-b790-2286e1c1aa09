defmodule Rms.Repo.Migrations.CreateShippingSettingsTable do
  use Ecto.Migration

  def change do
    create table(:shipping_settings) do
      add :price, :decimal, null: false
      add :time_period, :string
      add :ecommerce, :string

      add :settings, :map

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :fulfillment_id,
          references(:fulfillments,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          )

      timestamps(type: :utc_datetime)
    end
  end
end
