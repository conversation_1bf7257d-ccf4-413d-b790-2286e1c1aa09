defmodule Rms.Repo.Migrations.CreateProductSyncTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:product_sync) do
      add :external_id, :string
      add :expected_count, :integer
      add :status, :string
      add :message, :string
      add :error_code, :string

      add :organization_id, references(:organizations)

      timestamps(type: :utc_datetime)
    end

    create unique_index(:product_sync, [:external_id], concurrently: true)
    create unique_index(:product_sync, [:id, :organization_id], concurrently: true)

    create table(:product_sync_entries) do
      add :sku, :string
      add :external_id, :string
      add :status, :string
      add :errors, {:array, :map}

      add :organization_id, references(:organizations)
      add :product_sync_id, references(:product_sync, with: [organization_id: :organization_id])

      timestamps(type: :utc_datetime)
    end

    create index(:product_sync_entries, [:sku], concurrently: true)
    create index(:product_sync_entries, [:product_sync_id], concurrently: true)
    create index(:product_sync_entries, [:external_id], concurrently: true)
  end
end
