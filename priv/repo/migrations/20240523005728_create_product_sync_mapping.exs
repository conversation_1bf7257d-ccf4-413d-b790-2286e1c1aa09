defmodule Rms.Repo.Migrations.CreateProductSyncMapping do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:product_sync_mappings) do
      add :organization_id, references(:organizations, on_delete: :delete_all)

      add :product_id, references(:products, with: [organization_id: :organization_id])
      add :external_id, :string

      timestamps(type: :utc_datetime)
    end

    create unique_index(:product_sync_mappings, [:external_id], concurrently: true)
  end
end
