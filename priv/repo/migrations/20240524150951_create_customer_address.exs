defmodule Rms.Repo.Migrations.CreateCustomerAddress do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:addresses) do
      add :receiver_name, :string
      add :city_name, :string
      add :state, :string
      add :country_name, :string
      add :neighborhood, :string
      add :street, :string
      add :street_type, :string
      add :number, :string
      add :zip, :string
      add :complement, :string

      add :organization_id, references(:organizations, on_delete: :nothing)

      add :customer_id,
          references(:customers, with: [organization_id: :organization_id], on_delete: :nothing),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
