defmodule Rms.Repo.Migrations.LocationAddress do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed column_type_changed not_null_added column_reference_added

  def change do
    alter table(:addresses) do
      add :location_id,
          references(:locations, with: [organization_id: :organization_id], on_delete: :nothing)

      modify :customer_id, :bigint, null: true, from: :bigint
    end
  end
end
