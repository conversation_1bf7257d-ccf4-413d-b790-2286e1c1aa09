defmodule Rms.Repo.Migrations.ProductVariantMapping do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:product_variant_mappings) do
      add :organization_id, references(:organizations), null: false

      add :product_variant_id,
          references(:product_variants, with: [organization_id: :organization_id]),
          null: false

      add :external_id, :string, null: false
      add :source, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create index(:product_variant_mappings, [:organization_id], concurrently: true)

    create index(:product_variant_mappings, [:organization_id, :product_variant_id],
             concurrently: true
           )

    create unique_index(:product_variant_mappings, [:organization_id, :external_id, :source],
             concurrently: true
           )
  end
end
