defmodule Rms.Repo.Migrations.AddNameAndSourceToProductSyncTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    alter table(:product_sync) do
      add :name, :string
      add :source, :string
    end

    drop unique_index(:product_sync, [:external_id], concurrently: true)

    create unique_index(:product_sync, [:organization_id, :source, :external_id],
             concurrently: true
           )
  end
end
