defmodule Rms.Repo.Migrations.AddSourceToProductMapping do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    alter table(:product_sync_mappings) do
      add :source, :string
    end

    drop unique_index(:product_sync_mappings, [:external_id], concurrently: true)

    create unique_index(:product_sync_mappings, [:organization_id, :source, :external_id],
             concurrently: true
           )
  end
end
