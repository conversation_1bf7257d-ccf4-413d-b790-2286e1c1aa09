defmodule Rms.Repo.Migrations.CreateInventoryItemsTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:inventory_items) do
      add :quantity, :integer, null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :location_id,
          references(:locations,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      add :product_variant_id,
          references(:product_variants,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:inventory_items, [:location_id, :product_variant_id], concurrently: true)
  end
end
