defmodule Rms.Repo.Migrations.AddUniqueIndexOnProductMapping do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create unique_index(
             :product_variant_mappings,
             [
               :organization_id,
               :source,
               :product_variant_id
             ],
             concurrently: true
           )

    create unique_index(:product_sync_mappings, [:organization_id, :source, :product_id],
             concurrently: true
           )
  end
end
