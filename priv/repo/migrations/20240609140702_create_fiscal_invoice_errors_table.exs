defmodule Rms.Repo.Migrations.CreateFiscalInvoiceErrorsTable do
  use Ecto.Migration

  def change do
    create table(:fiscal_invoice_errors) do
      add :error, :string, null: false
      add :stacktrace, :string, null: true

      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      add :fiscal_invoice_id,
          references(:fiscal_invoices,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          ),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
