defmodule Rms.Repo.Migrations.CreateOrderErrorsTable do
  use Ecto.Migration

  def change do
    create table(:order_errors) do
      add :error, :string, null: false
      add :stacktrace, :string, null: true

      add :order_id,
          references(:orders, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      add :organization_id, references(:organizations, on_delete: :nothing), null: false
      timestamps(type: :utc_datetime)
    end
  end
end
