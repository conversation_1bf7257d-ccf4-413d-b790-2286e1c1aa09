defmodule Rms.Repo.Migrations.CreateAddonsTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:addons) do
      add :organization_id, references(:organizations), null: false

      add :name, :string
      add :list_price, :decimal, null: false
      add :type, :string, null: false
      add :description, :string
      add :image_url, :string

      add :metadata, :map

      add :archived_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create unique_index(:addons, [:id, :organization_id], concurrently: true)

    create index(:addons, [:archived_at],
             where: "archived_at IS NULL",
             concurrently: true
           )
  end
end
