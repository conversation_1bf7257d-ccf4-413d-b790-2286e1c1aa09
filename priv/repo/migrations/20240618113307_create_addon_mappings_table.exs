defmodule Rms.Repo.Migrations.CreateAddonMappingsTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:addon_mappings) do
      add :organization_id, references(:organizations), null: false

      add :addon_id,
          references(:addons, with: [organization_id: :organization_id]),
          null: false

      add :external_id, :string, null: false
      add :source, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create index(:addon_mappings, [:organization_id], concurrently: true)

    create index(:addon_mappings, [:organization_id, :addon_id], concurrently: true)

    create unique_index(:addon_mappings, [:organization_id, :external_id, :source],
             concurrently: true
           )
  end
end
