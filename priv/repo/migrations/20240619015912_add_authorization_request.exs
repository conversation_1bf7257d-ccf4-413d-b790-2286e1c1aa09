defmodule Rms.Repo.Migrations.AddAuthorizationRequest do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:authorization_requests) do
      add :code, :string, null: false
      add :status, :string, null: false

      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          ),
          null: false

      add :location_id,
          references(:locations, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      add :staff_id,
          references(:staffs, on_delete: :nothing, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:authorization_requests, [:id, :organization_id], concurrently: true)
  end
end
