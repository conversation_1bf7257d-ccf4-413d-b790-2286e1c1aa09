defmodule Rms.Repo.Migrations.AddDiscount do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:discounts) do
      add :type, :string, null: false
      add :value, :decimal
      add :code, :string
      add :description, :string

      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          ),
          null: false

      add :authorization_request_id,
          references(:authorization_requests,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          )

      timestamps(type: :utc_datetime)
    end

    create unique_index(:discounts, [:id, :organization_id], concurrently: true)
  end
end
