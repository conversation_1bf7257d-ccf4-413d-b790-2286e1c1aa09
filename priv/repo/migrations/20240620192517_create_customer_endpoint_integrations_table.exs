defmodule Rms.Repo.Migrations.CreateCustomerEndpointIntegrationsTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:customer_endpoint_integrations) do
      add :endpoint, :string, null: false
      add :unique_on, {:array, :string}, null: false
      add :headers, {:array, {:array, :string}}, null: false
      add :active, :boolean, null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:customer_endpoint_integrations, [:organization_id],
             where: "active = true",
             concurrently: true
           )
  end
end
