defmodule Rms.Repo.Migrations.CreateSimulatedCart do
  use Ecto.Migration
  @disable_ddl_transaction true
  @disable_migration_lock true
  def change do
    create table(:carts, primary_key: false) do
      add :id, :uuid, primary_key: true, null: false
      add :status, :string, null: false
      add :note, :string

      add :total_price, :decimal, null: false
      add :total_items_list_price, :decimal, null: false
      add :total_items_selling_price, :decimal, null: false
      add :total_manual_discount, :decimal, null: false
      add :total_delivery_price, :decimal, null: false
      add :max_delivery_time, :integer, null: false

      add :messages, :map
      add :metadata, :map
      add :ecommerce, :string

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :location_id,
          references(:locations,
            with: [organization_id: :organization_id]
          )

      add :customer_id,
          references(:customers,
            with: [organization_id: :organization_id]
          )

      add :staff_id,
          references(:staffs, with: [organization_id: :organization_id])

      timestamps(type: :utc_datetime)
    end

    create unique_index(:carts, [:id, :organization_id], concurrently: true)
  end
end
