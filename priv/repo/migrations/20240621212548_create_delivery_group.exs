defmodule Rms.Repo.Migrations.CreateDeliveryGroup do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:delivery_groups, primary_key: false) do
      add :id, :uuid, primary_key: true, null: false
      add :delivery_time, :integer, null: false
      add :fulfillment_type, :string, null: false
      add :pickup_point, :string

      add :metadata, :map

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :cart_id,
          references(:carts,
            with: [organization_id: :organization_id],
            type: :uuid
          ),
          null: false
    end

    create unique_index(:delivery_groups, [:id, :organization_id], concurrently: true)
  end
end
