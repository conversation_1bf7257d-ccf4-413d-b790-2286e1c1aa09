defmodule Rms.Repo.Migrations.CreateSimulatedCartItem do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true
  def change do
    create table(:cart_items, primary_key: false) do
      add :id, :uuid, primary_key: true, null: false
      add :quantity, :integer, null: false

      add :list_price, :decimal, null: false
      add :selling_price, :decimal, null: false
      add :total_price, :decimal, null: false

      add :metadata, :map

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :product_variant_id,
          references(:product_variants,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          ),
          null: false

      add :delivery_group_id,
          references(:delivery_groups,
            on_delete: :nothing,
            with: [organization_id: :organization_id],
            type: :uuid
          ),
          null: false
    end

    create unique_index(:cart_items, [:id, :organization_id], concurrently: true)
  end
end
