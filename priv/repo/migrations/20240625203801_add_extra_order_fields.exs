defmodule Rms.Repo.Migrations.AddExtraOrderFields do
  use Ecto.Migration
  @disable_migration_lock true
  @disable_ddl_transaction true

  # excellent_migrations:safety-assured-for-this-file column_renamed

  def change do
    rename table(:orders), :total_amount, to: :total_price
    rename table(:orders), :delivery_price, to: :total_delivery_price
    rename table(:orders), :discounts_total, to: :total_discount

    alter table(:orders) do
      add :total_selling_price, :decimal
      add :total_list_price, :decimal
    end

    alter table(:discounts) do
      add :order_id, references(:orders, with: [organization_id: :organization_id])
    end
  end
end
