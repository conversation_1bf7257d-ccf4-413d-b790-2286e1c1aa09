defmodule Rms.Repo.Migrations.AddClientOrderSnapshot do
  use Ecto.Migration

  def change do
    create table(:order_customers) do
      add :document_type, :string
      add :document, :binary
      add :document_hash, :binary
      add :email, :binary
      add :email_hash, :binary
      add :name, :binary
      add :name_hash, :binary
      add :primary_phone_number, :binary
      add :primary_phone_number_hash, :binary

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :order_id,
          references(:orders, on_delete: :delete_all, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
