defmodule Rms.Repo.Migrations.ChangePickupPointType do
  use Ecto.Migration
  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "ALTER TABLE delivery_groups ALTER COLUMN pickup_point TYPE json USING pickup_point::json"

    alter table(:cart_items) do
      add :item_index, :integer
    end

    alter table(:delivery_groups) do
      add :delivery_price, :decimal
    end
  end
end
