defmodule Rms.Repo.Migrations.CreateCustomerSyncTables do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:customer_sync) do
      add :external_id, :string
      add :expected_count, :integer
      add :status, :string
      add :message, :string
      add :source, :string
      add :organization_id, references(:organizations), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:customer_sync, [:id, :organization_id], concurrently: true)

    create unique_index(:customer_sync, [:external_id, :source, :organization_id],
             concurrently: true
           )

    create table(:customer_sync_entries) do
      add :external_id, :string
      add :status, :string
      add :errors, :map
      add :organization_id, references(:organizations), null: false

      add :customer_sync_id,
          references(:customer_sync, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end

    create index(:customer_sync_entries, [:external_id, :organization_id], concurrently: true)
    create index(:customer_sync_entries, [:customer_sync_id], concurrently: true)

    create table(:customer_sync_mappings) do
      add :external_id, :string
      add :source, :string
      add :organization_id, references(:organizations), null: false

      add :customer_id, references(:customers, with: [organization_id: :organization_id]),
        null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:customer_sync_mappings, [:external_id, :source, :organization_id],
             concurrently: true
           )
  end
end
