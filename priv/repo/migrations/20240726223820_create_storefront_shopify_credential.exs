defmodule Rms.Repo.Migrations.CreateStorefrontShopifyCredential do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:shopify_storefront_credentials) do
      add :credential_id, :binary
      add :credential, :binary
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:shopify_storefront_credentials, [:organization_id], concurrently: true)
  end
end
