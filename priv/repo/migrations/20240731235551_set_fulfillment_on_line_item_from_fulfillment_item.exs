defmodule Rms.Repo.Migrations.SetFulfillmentOnLineItemFromFulfillmentItem do
  use Ecto.Migration

  import Ecto.Query

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed

  def change do
    execute "UPDATE line_items li
             SET fulfillment_id = fi.fulfillment_id
             FROM fulfillment_items fi
             WHERE li.id = fi.line_item_id"
  end
end
