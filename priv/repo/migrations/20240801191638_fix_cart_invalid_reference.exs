defmodule Rms.Repo.Migrations.FixCartInvalidReference do
  use Ecto.Migration

  def change do
    alter table(:carts) do
      # excellent_migrations:safety-assured-for-next-line column_reference_added
      modify :shipping_address_id,
             references(:addresses,
               with: [organization_id: :organization_id],
               on_delete: :delete_all
             ),
             from: references(:addresses, with: [organization_id: :organization_id])
    end
  end
end
