defmodule Rms.Repo.Migrations.CreateRolesRelatedToStaff do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:staff_roles) do
      add :seller, :boolean, null: false
      add :stocker, :boolean, null: false

      add :staff_id,
          references(:staffs, with: [organization_id: :organization_id], on_delete: :delete_all),
          null: false

      add :organization_id,
          references(:organizations, on_delete: :delete_all),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:staff_roles, [:staff_id, :organization_id], concurrently: true)
  end
end
