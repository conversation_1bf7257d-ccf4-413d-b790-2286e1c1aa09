defmodule Rms.Repo.Migrations.CreateLocationMapping do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:location_mappings) do
      add :organization_id, references(:organizations), null: false

      add :location_id,
          references(:locations, with: [organization_id: :organization_id]),
          null: false

      add :external_id, :string, null: false
      add :source, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create index(:location_mappings, [:organization_id], concurrently: true)

    create index(:location_mappings, [:organization_id, :location_id], concurrently: true)

    create unique_index(:location_mappings, [:organization_id, :external_id, :source],
             concurrently: true
           )
  end
end
