defmodule Rms.Repo.Migrations.CreateLocationSyncTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:location_sync) do
      add :external_id, :string
      add :expected_count, :integer
      add :status, :string
      add :message, :string
      add :source, :string
      add :organization_id, references(:organizations), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:location_sync, [:id, :organization_id], concurrently: true)

    create unique_index(:location_sync, [:external_id, :source, :organization_id],
             concurrently: true
           )
  end
end
