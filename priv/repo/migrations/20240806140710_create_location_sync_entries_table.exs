defmodule Rms.Repo.Migrations.CreateLocationSyncEntriesTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:location_sync_entries) do
      add :external_id, :string
      add :status, :string
      add :errors, :map
      add :organization_id, references(:organizations), null: false

      add :location_sync_id,
          references(:location_sync, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end

    create index(:location_sync_entries, [:external_id, :organization_id], concurrently: true)
    create index(:location_sync_entries, [:location_sync_id], concurrently: true)
  end
end
