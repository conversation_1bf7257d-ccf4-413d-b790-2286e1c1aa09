defmodule Rms.Repo.Migrations.LocationDockAssosiation do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:docks) do
      add :external_id, :string, null: false
      add :name, :string, null: false
      add :organization_id, references(:organizations), null: false

      add :location_id,
          references(:locations, with: [organization_id: :organization_id]),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:docks, [:id, :organization_id], concurrently: true)

    create unique_index(:docks, [:organization_id, :location_id, :external_id],
             concurrently: true
           )
  end
end
