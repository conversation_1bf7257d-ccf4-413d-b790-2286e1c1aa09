defmodule Rms.Repo.Migrations.CreateStaffRolesForExistingStaff do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def change do
    execute "INSERT INTO staff_roles (staff_id, organization_id, seller, stocker, updated_at, inserted_at)
             SELECT id, organization_id, true, false, NOW(), NOW()
             FROM staffs
             WHERE id NOT IN (SELECT staff_id FROM staff_roles)"
  end
end
