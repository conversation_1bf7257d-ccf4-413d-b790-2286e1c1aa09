defmodule Rms.Repo.Migrations.BackfillMainAccountNameInVtexCredential do
  # excellent_migrations:safety-assured-for-this-file operation_update
  use Ecto.Migration
  import Ecto.Query

  @disable_ddl_transaction true
  @disable_migration_lock true

  def up do
    flush()

    from(vc in "vtex_credentials",
      where: is_nil(vc.main_account_name),
      update: [set: [main_account_name: vc.account_name]]
    )
    |> Rms.Repo.update_all([], log: :info)
  end

  defp down do
    # no-op
    :ok
  end
end
