defmodule Rms.Repo.Migrations.CreateErpCredentialTables do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:erp_credentials) do
      add :url, :binary
      add :credential, :binary
      add :organization_id, references(:organizations, on_delete: :nothing), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:erp_credentials, [:organization_id], concurrently: true)
  end
end
