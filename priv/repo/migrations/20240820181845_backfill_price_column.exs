defmodule Rms.Repo.Migrations.BackfillPriceColumn do
  # excellent_migrations:safety-assured-for-this-file operation_update
  use Ecto.Migration
  import Ecto.Query

  @disable_ddl_transaction true
  @disable_migration_lock true

  def up do
    flush()

    from(pv in "product_variants",
      where: is_nil(pv.price),
      update: [set: [price: pv.list_price]]
    )
    |> Rms.Repo.update_all([], log: :info)
  end

  defp down do
    # no-op
    :ok
  end
end
