defmodule Rms.Repo.Migrations.CreateCancelationEndpointIntegrations do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:cancelation_endpoints) do
      add :endpoint, :string, null: false
      add :headers, {:array, {:array, :string}}, null: false
      add :active, :boolean, default: false, null: false
      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps()
    end

    create index(:cancelation_endpoints, [:organization_id], concurrently: true)

    create unique_index(:cancelation_endpoints, [:organization_id, :active],
             where: "active = true",
             concurrently: true
           )
  end
end
