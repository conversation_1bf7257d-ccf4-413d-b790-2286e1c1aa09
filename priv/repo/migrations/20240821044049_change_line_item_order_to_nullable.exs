defmodule Rms.Repo.Migrations.ChangeLineItemOrderToNullable do
  use Ecto.Migration

  def change do
    alter table(:line_items) do
      # excellent_migrations:safety-assured-for-next-line column_type_changed
      modify :order_id,
             references(:orders,
               on_delete: :delete_all,
               with: [organization_id: :organization_id]
             ),
             null: true,
             from:
               {references(:orders,
                  on_delete: :nothing,
                  with: [organization_id: :organization_id]
                ), null: false}
    end
  end
end
