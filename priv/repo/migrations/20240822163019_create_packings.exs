defmodule Rms.Repo.Migrations.CreatePackings do
  use Ecto.Migration

  def change do
    create table(:packings) do
      add :shipping_method, :string, null: false
      add :external_reference, :string
      add :status, :string

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :dock_id,
          references(:docks, with: [organization_id: :organization_id], on_delete: :nothing),
          null: false

      add :fulfillment_id,
          references(:fulfillments,
            with: [organization_id: :organization_id],
            on_delete: :delete_all
          ),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
