defmodule Rms.Repo.Migrations.CreatePackingItems do
  use Ecto.Migration

  def change do
    create table(:packing_items) do
      add :quantity, :integer, null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :fulfillment_id,
          references(:fulfillments,
            with: [organization_id: :organization_id],
            on_delete: :delete_all
          ),
          null: false

      add :packing_id,
          references(:packings,
            with: [fulfillment_id: :fulfillment_id, organization_id: :organization_id],
            on_delete: :delete_all
          ),
          null: false

      add :line_item_id,
          references(:line_items,
            with: [fulfillment_id: :fulfillment_id, organization_id: :organization_id],
            on_delete: :delete_all
          ),
          null: false

      timestamps(type: :utc_datetime)
    end
  end
end
