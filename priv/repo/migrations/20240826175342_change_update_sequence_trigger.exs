defmodule Rms.Repo.Migrations.ChangeUpdateSequenceTrigger do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed

  def up do
    execute("""
    CREATE OR REPLACE FUNCTION update_sequence()
    RETURNS TRIGGER AS $$
    DECLARE
    a_number INTEGER;
    BEGIN
      IF NEW.service != 'external' THEN
        SELECT available_number INTO a_number FROM invoice_series WHERE id = NEW.serie_id FOR UPDATE;
        NEW.invoice_number = a_number;
        UPDATE invoice_series SET available_number = available_number + 1 WHERE id = NEW.serie_id;
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """)

    execute("""
    DROP TRIGGER IF EXISTS invoce_numbers_insert_trigger ON fiscal_invoices;
    """)

    execute("""
    CREATE TRIGGER invoce_numbers_insert_trigger
    BEFORE INSERT ON fiscal_invoices
    FOR EACH ROW EXECUTE FUNCTION update_sequence();
    """)
  end

  def down do
    execute("""
    DROP TRIGGER IF EXISTS invoce_numbers_insert_trigger ON fiscal_invoices;
    """)

    execute("""
    DROP FUNCTION IF EXISTS update_sequence();
    """)
  end
end
