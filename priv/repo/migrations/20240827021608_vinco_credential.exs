defmodule Rms.Repo.Migrations.VincoCredential do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:vinco_credential) do
      add :key, :binary
      add :organization_id, references(:organizations, on_delete: :nothing)

      add :location_id,
          references(:locations, on_delete: :nothing, with: [organization_id: :organization_id])

      timestamps(type: :utc_datetime)
    end

    create unique_index(:vinco_credential, [:organization_id, :location_id], concurrently: true)
  end
end
