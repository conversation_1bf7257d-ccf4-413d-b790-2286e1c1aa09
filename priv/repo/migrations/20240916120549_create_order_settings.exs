defmodule Rms.Repo.Migrations.CreateOrderSettings do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:order_settings) do
      add :organization_id, references(:organizations), null: false
      add :prefix, :string, null: false
      add :suffix, :string, null: false
      add :current_order_number, :integer, null: false

      timestamps()
    end

    create unique_index(:order_settings, [:organization_id], concurrently: true)
  end
end
