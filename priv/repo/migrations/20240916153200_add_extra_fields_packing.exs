defmodule Rms.Repo.Migrations.AddExtraFieldsPacking do
  use Ecto.Migration

  def change do
    alter table(:packings) do
      add :courier_name, :string

      add :customer_id,
          references(:customers, with: [organization_id: :organization_id], on_delete: :nothing)

      add :staff_id,
          references(:staffs, with: [organization_id: :organization_id], on_delete: :nothing)

      # excellent_migrations:safety-assured-for-next-line column_added_with_default
      add :total_price, :decimal, default: 0, null: false
    end
  end
end
