defmodule Rms.Repo.Migrations.AddSkuProductTaxes do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file table_dropped

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    alter table(:product_taxes) do
      add :sku, :string
    end

    # 00
    drop_if_exists unique_index(:product_taxes, [:organization_id],
                     where: "ncm IS NULL and uf IS NULL",
                     concurrently: true
                   )

    # 01
    drop_if_exists unique_index(:product_taxes, [:uf, :organization_id],
                     where: "ncm IS NULL",
                     concurrently: true
                   )

    # 10
    drop_if_exists unique_index(:product_taxes, [:ncm, :organization_id],
                     where: "uf IS NULL",
                     concurrently: true
                   )

    # 11
    drop_if_exists unique_index(:product_taxes, [:ncm, :uf, :organization_id], concurrently: true)

    # 111
    create unique_index(:product_taxes, [:sku, :ncm, :uf, :organization_id], concurrently: true)

    # 110
    create unique_index(:product_taxes, [:sku, :ncm, :organization_id],
             where: "uf IS NULL",
             concurrently: true
           )

    # 101
    create unique_index(:product_taxes, [:sku, :uf, :organization_id],
             where: "ncm IS NULL",
             concurrently: true
           )

    # 100
    create unique_index(:product_taxes, [:sku, :organization_id],
             where: "ncm IS NULL and uf IS NULL",
             concurrently: true
           )

    # 011
    create unique_index(:product_taxes, [:ncm, :uf, :organization_id],
             where: "sku IS NULL",
             concurrently: true
           )

    # 010
    create unique_index(:product_taxes, [:ncm, :organization_id],
             where: "uf IS NULL and sku IS NULL",
             concurrently: true
           )

    # 001
    create unique_index(:product_taxes, [:uf, :organization_id],
             where: "ncm IS NULL and sku IS NULL",
             concurrently: true
           )

    # 000
    create unique_index(:product_taxes, [:organization_id],
             where: "ncm IS NULL and uf IS NULL and sku IS NULL",
             concurrently: true
           )
  end
end
