defmodule Rms.Repo.Migrations.MakeExternalIdUniqueWhereArchivedAtIsNull do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    drop_if_exists unique_index(:staffs, [:external_id, :organization_id], concurrently: true)

    create unique_index(:staffs, [:external_id, :organization_id],
             where: "archived_at IS NULL",
             concurrently: true
           )
  end
end
