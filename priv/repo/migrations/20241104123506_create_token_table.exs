defmodule Rms.Repo.Migrations.CreateTokenTable do
  use Ecto.Migration

  def change do
    create table(:card_tokens) do
      add :token, :binary

      add :organization_id, references(:organizations, on_delete: :nothing)

      add :customer_id, references(:customers, on_delete: :nothing),
        with: [organization_id: :organization_id]

      add :payment_id, references(:payments, on_delete: :nothing),
        with: [organization_id: :organization_id]

      timestamps(type: :utc_datetime)
    end
  end
end
