defmodule Rms.Repo.Migrations.CreateSubscriptionTable do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:subscriptions) do
      add :value, :decimal
      add :frequency, :string

      add :organization_id, references(:organizations), null: false

      add :customer_id, references(:customers, with: [organization_id: :organization_id]),
        null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:subscriptions, [:id, :organization_id], concurrently: true)
  end
end
