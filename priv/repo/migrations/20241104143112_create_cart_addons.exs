defmodule Rms.Repo.Migrations.CreateCartAddons do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:cart_addons, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :name, :string, null: false
      add :price, :decimal, null: false
      add :quantity, :integer, null: false
      add :type, :string, null: false
      add :description, :text

      add :cart_id,
          references(:carts,
            type: :uuid,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      add :addon_id,
          references(:addons, on_delete: :delete_all, with: [organization_id: :organization_id]),
          null: false

      add :organization_id,
          references(:organizations, on_delete: :delete_all),
          null: false

      timestamps()
    end

    create index(:cart_addons, [:cart_id], concurrently: true)
    create index(:cart_addons, [:addon_id], concurrently: true)
    create index(:cart_addons, [:organization_id], concurrently: true)
  end
end
