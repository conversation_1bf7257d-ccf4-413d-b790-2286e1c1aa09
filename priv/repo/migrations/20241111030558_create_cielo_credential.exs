defmodule Rms.Repo.Migrations.CreateCieloCredential do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:cielo_credentials) do
      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :token, :binary
      add :expires_in, :integer

      add :client_id, :binary
      add :client_secret, :binary

      timestamps()
    end

    create unique_index(:cielo_credentials, [:organization_id], concurrently: true)
  end
end
