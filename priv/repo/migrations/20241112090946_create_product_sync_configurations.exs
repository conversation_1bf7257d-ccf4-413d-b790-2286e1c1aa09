defmodule Rms.Repo.Migrations.CreateProductSyncConfigurations do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:product_sync_configurations) do
      add :field_priorities, :map, null: false
      add :default_priority, {:array, :string}, null: false
      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:product_sync_configurations, [:organization_id], concurrently: true)
  end
end
