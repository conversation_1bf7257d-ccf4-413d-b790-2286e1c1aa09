defmodule Rms.Repo.Migrations.ShopifyAdditionalInformation do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:shopify_order_additional_infos) do
      add :order_type, :string, null: false
      add :fields, {:array, :string}, default: []

      add :organization_id, references(:organizations), null: false

      timestamps()
    end

    create unique_index(:shopify_order_additional_infos, [:order_type, :organization_id],
             concurrently: true
           )
  end
end
