defmodule Rms.Repo.Migrations.CreateGiftHandlerConfiguration do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create table(:gift_handler_configurations) do
      add :gifts, :map

      add :product_variant_id,
          references(:product_variants,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      add :organization_id, references(:organizations), null: false

      timestamps()
    end

    create unique_index(:gift_handler_configurations, [:product_variant_id, :organization_id],
             concurrently: true
           )
  end
end
