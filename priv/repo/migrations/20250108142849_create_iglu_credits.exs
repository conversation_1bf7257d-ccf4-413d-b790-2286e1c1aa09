defmodule Rms.Repo.Migrations.CreateIgluCredits do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:iglu_credits) do
      add :amount, :decimal, null: false
      add :status, :string, null: false, default: "available"
      add :reason, :string, null: false
      add :expires_at, :utc_datetime
      add :metadata, :map

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :customer_id,
          references(:customers,
            on_delete: :nilify_all,
            with: [organization_id: :organization_id]
          )

      timestamps(type: :utc_datetime)
    end

    create index(:iglu_credits, [:organization_id], concurrently: true)
    create index(:iglu_credits, [:customer_id, :organization_id], concurrently: true)
    create index(:iglu_credits, [:status], concurrently: true)
  end
end
