defmodule Rms.Repo.Migrations.CreateReverseFulfillments do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:reverse_fulfillments, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :status, :string, null: false, default: "pending"
      add :metadata, :map

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:reverse_fulfillments, [:organization_id, :id], concurrently: true)

    create index(:reverse_fulfillments, [:organization_id, :status], concurrently: true)
  end
end
