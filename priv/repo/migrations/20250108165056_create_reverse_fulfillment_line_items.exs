defmodule Rms.Repo.Migrations.CreateReverseFulfillmentLineItems do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:reverse_fulfillment_line_items) do
      add :returned_quantity, :integer, null: false
      add :reason, :string
      add :metadata, :map

      add :reverse_fulfillment_id,
          references(:reverse_fulfillments,
            type: :binary_id,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :line_item_id,
          references(:line_items,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          ),
          null: false

      timestamps(type: :utc_datetime)
    end

    create index(:reverse_fulfillment_line_items, [:organization_id, :line_item_id],
             concurrently: true
           )

    create index(:reverse_fulfillment_line_items, [:organization_id, :reverse_fulfillment_id],
             concurrently: true
           )
  end
end
