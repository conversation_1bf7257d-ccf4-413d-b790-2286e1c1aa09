defmodule Rms.Repo.Migrations.AddReverseFulfillmentToIgluCredit do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    alter table(:iglu_credits) do
      add :reverse_fulfillment_id,
          references(:reverse_fulfillments,
            type: :uuid,
            on_delete: :restrict,
            with: [organization_id: :organization_id]
          ),
          null: false
    end

    create index(:iglu_credits, [:reverse_fulfillment_id, :organization_id], concurrently: true)
  end
end
