defmodule Rms.Repo.Migrations.CreateIgluCreditPayments do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create_if_not_exists unique_index(:iglu_credits, [:id, :organization_id], concurrently: true)

    create table(:iglu_credit_payments) do
      add :organization_id, references(:organizations), null: false

      add :payment_id,
          references(:payments, with: [organization_id: :organization_id]),
          null: false

      add :iglu_credit_id, references(:iglu_credits, with: [organization_id: :organization_id]),
        null: false

      timestamps(type: :utc_datetime)
    end

    # Indexes for foreign keys
    create_if_not_exists index(:iglu_credit_payments, [:organization_id], concurrently: true)
    create_if_not_exists index(:iglu_credit_payments, [:payment_id], concurrently: true)
    create_if_not_exists index(:iglu_credit_payments, [:iglu_credit_id], concurrently: true)

    # Unique constraint to prevent duplicate credit-payment combinations
    create_if_not_exists unique_index(
                           :iglu_credit_payments,
                           [:payment_id, :iglu_credit_id],
                           concurrently: true
                         )
  end
end
