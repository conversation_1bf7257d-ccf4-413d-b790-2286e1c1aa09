defmodule Rms.Repo.Migrations.AddCustomerIdToOrderCustoemrs do
  use Ecto.Migration
  #
  # excellent_migrations:safety-assured-for-this-file not_null_added column_type_changed raw_sql_executed

  def change do
    alter table(:order_customers) do
      add :customer_id, references(:customers, with: [organization_id: :organization_id]),
        null: true
    end

    execute """
    UPDATE order_customers
    SET customer_id = orders.customer_id
    FROM orders
    WHERE order_customers.order_id = orders.id
    """

    alter table(:order_customers) do
      modify :customer_id, :id, null: false
    end
  end
end
