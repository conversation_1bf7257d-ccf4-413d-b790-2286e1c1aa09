defmodule Rms.Repo.Migrations.CreateLineItemsReturnSummary do
  use Ecto.Migration
  # excellent_migrations:safety-assured-for-this-file raw_sql_executed

  def up do
    # Create the auxiliary table
    execute """
    CREATE TABLE line_items_return_summary (
      line_item_id bigint PRIMARY KEY,
      original_quantity integer NOT NULL,
      total_returned_quantity integer NOT NULL DEFAULT 0,
      available_quantity integer NOT NULL,
      CONSTRAINT check_available_quantity CHECK (available_quantity >= 0)
    );
    """

    # Create the function to update the auxiliary table
    execute """
    CREATE OR REPLACE FUNCTION update_line_items_return_summary()
    RETURNS TRIGGER AS $$
    BEGIN
      INSERT INTO line_items_return_summary (line_item_id, original_quantity, total_returned_quantity, available_quantity)
      SELECT
        li.id AS line_item_id,
        li.quantity AS original_quantity,
        COALESCE(SUM(rfli.returned_quantity), 0) AS total_returned_quantity,
        li.quantity - COALESCE(SUM(rfli.returned_quantity), 0) AS available_quantity
      FROM
        line_items li
      LEFT JOIN
        reverse_fulfillment_line_items rfli ON li.id = rfli.line_item_id
      WHERE
        li.id = COALESCE(NEW.line_item_id, OLD.line_item_id)
      GROUP BY
        li.id, li.quantity
      ON CONFLICT (line_item_id) DO UPDATE SET
        total_returned_quantity = EXCLUDED.total_returned_quantity,
        available_quantity = EXCLUDED.available_quantity;

      RETURN NULL;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create trigger to update the auxiliary table on changes
    execute """
    CREATE TRIGGER update_line_items_return_summary_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reverse_fulfillment_line_items
    FOR EACH ROW
    EXECUTE FUNCTION update_line_items_return_summary();
    """

    # Populate the auxiliary table with initial data
    execute """
    INSERT INTO line_items_return_summary (line_item_id, original_quantity, total_returned_quantity, available_quantity)
    SELECT
      li.id AS line_item_id,
      li.quantity AS original_quantity,
      COALESCE(SUM(rfli.returned_quantity), 0) AS total_returned_quantity,
      li.quantity - COALESCE(SUM(rfli.returned_quantity), 0) AS available_quantity
    FROM
      line_items li
    LEFT JOIN
      reverse_fulfillment_line_items rfli ON li.id = rfli.line_item_id
    GROUP BY
      li.id, li.quantity;
    """
  end

  def down do
    execute "DROP TRIGGER IF EXISTS update_line_items_return_summary_trigger ON reverse_fulfillment_line_items;"
    execute "DROP FUNCTION IF EXISTS update_line_items_return_summary();"
    execute "DROP TABLE IF EXISTS line_items_return_summary;"
  end
end
