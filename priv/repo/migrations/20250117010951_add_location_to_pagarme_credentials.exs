defmodule Rms.Repo.Migrations.AddLocationToPagarmeCredentials do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    alter table(:pagarme_credentials) do
      add :location_id, references(:locations, on_delete: :nothing)
    end

    drop unique_index(:pagarme_credentials, [:organization_id], concurrently: true)

    create unique_index(:pagarme_credentials, [:organization_id, :location_id],
             concurrently: true
           )
  end
end
