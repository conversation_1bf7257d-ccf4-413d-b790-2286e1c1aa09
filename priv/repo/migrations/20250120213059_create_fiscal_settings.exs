defmodule Rms.Repo.Migrations.CreateFiscalSettings do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:fiscal_settings) do
      add :handle_sale, :boolean, null: false, default: true
      add :handle_return, :boolean, null: false, default: true
      add :handle_transfer, :boolean, null: false, default: true
      add :environment, :string, null: false
      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      add :location_id,
          references(:locations,
            on_delete: :delete_all,
            with: [organization_id: :organization_id]
          )

      timestamps()
    end

    create unique_index(:fiscal_settings, [:organization_id, :location_id],
             where: "location_id IS NOT NULL",
             concurrently: true
           )

    create unique_index(:fiscal_settings, [:organization_id],
             where: "location_id IS NULL",
             concurrently: true
           )
  end
end
