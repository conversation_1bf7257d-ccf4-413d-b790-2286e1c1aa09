defmodule Rms.Repo.Migrations.CreateCartItemDiscount do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:cart_item_discounts) do
      add :type, :string, null: false
      add :value, :string
      add :code, :string
      add :description, :string

      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          ),
          null: false

      add :authorization_request_id,
          references(:authorization_requests,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          )

      add :cart_item_id,
          references(:cart_items,
            on_delete: :nothing,
            with: [organization_id: :organization_id],
            type: :uuid
          ),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:cart_item_discounts, [:id, :organization_id], concurrently: true)

    create index(:cart_item_discounts, [:cart_item_id], concurrently: true)
  end
end
