defmodule Rms.Repo.Migrations.CreateLineItemDiscount do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:line_item_discounts) do
      add :type, :string, null: false
      add :value, :string
      add :code, :string
      add :description, :string

      add :organization_id,
          references(:organizations,
            on_delete: :nothing
          ),
          null: false

      add :authorization_request_id,
          references(:authorization_requests,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          )

      add :line_item_id,
          references(:line_items,
            on_delete: :nothing,
            with: [organization_id: :organization_id]
          ),
          null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:line_item_discounts, [:id, :organization_id], concurrently: true)

    create index(:line_item_discounts, [:line_item_id], concurrently: true)
  end
end
