defmodule Rms.Repo.Migrations.MakeLocationNotNullInStaffsLocations do
  use Ecto.Migration

  def change do
    drop constraint(:staffs_locations, "staffs_locations_location_id_fkey")

    alter table(:staffs_locations) do
      # excellent_migrations:safety-assured-for-next-line column_reference_added column_type_changed not_null_added
      modify :location_id,
             references(:locations, with: [organization_id: :organization_id]),
             null: false
    end
  end
end
