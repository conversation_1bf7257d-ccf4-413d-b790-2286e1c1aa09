defmodule Rms.Repo.Migrations.AddLocationInReverseFulfillment do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file not_null_added column_reference_added column_removed
  def up do
    alter table(:reverse_fulfillments) do
      add :location_id, references(:locations, on_delete: :nothing)
    end
  end

  def down do
    alter table(:reverse_fulfillments) do
      remove :location_id
    end
  end
end
