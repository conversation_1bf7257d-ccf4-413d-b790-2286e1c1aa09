defmodule Rms.Repo.Migrations.PopulateLocationInReverseFulfillment do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def up do
    execute "
        WITH original_orders AS (
      select ic.reverse_fulfillment_id, o.location_id from orders o
		join transactions t on t.order_id = o.id
		join payments p on p.transaction_id = t.id
		join iglu_credit_payments icp on icp.payment_id = p.id
		join iglu_credits ic on icp.iglu_credit_id = ic.id
    )
    UPDATE reverse_fulfillments rf
    SET location_id = oo.location_id
    FROM original_orders oo
    WHERE rf.id = oo.reverse_fulfillment_id;"
  end

  def down do
    "no-op"
  end
end
