defmodule Rms.Repo.Migrations.AddUsedAmountToIgluCredits do
  use Ecto.Migration

  def up do
    alter table(:iglu_credits) do
      add :version, :integer, default: 1, null: false
      add :used_amount, :decimal, null: false, default: 0
    end

    # excellent_migrations:safety-assured-for-next-line check_constraint_added
    create constraint(:iglu_credits, :used_amount_must_not_exceed_amount,
             check: "used_amount <= amount"
           )
  end

  def down do
    drop constraint(:iglu_credits, :used_amount_must_not_exceed_amount)

    alter table(:iglu_credits) do
      # excellent_migrations:safety-assured-for-next-line column_removed
      remove :used_amount
      # excellent_migrations:safety-assured-for-next-line column_removed
      remove :version
    end
  end
end
