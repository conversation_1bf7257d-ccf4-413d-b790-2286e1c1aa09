defmodule Rms.Repo.Migrations.AddStaffIdReverseFulfillment do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file not_null_added column_reference_added column_removed
  def up do
    alter table(:reverse_fulfillments) do
      add :staff_id, references(:staffs, on_delete: :nothing)
    end
  end

  def down do
    alter table(:reverse_fulfillments) do
      remove :staff_id
    end
  end
end
