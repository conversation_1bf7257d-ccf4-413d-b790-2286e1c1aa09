defmodule Rms.Repo.Migrations.PopulateStaffIdReverseFulfillment do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file raw_sql_executed
  def up do
    execute "
    WITH mapping AS (
      select ic.reverse_fulfillment_id, o.staff_id from orders o
      join transactions t on t.order_id = o.id
      join payments p on p.transaction_id = t.id
      join iglu_credit_payments icp on icp.payment_id = p.id
      join iglu_credits ic on icp.iglu_credit_id = ic.id
    )
    UPDATE reverse_fulfillments rf
    SET staff_id = m.staff_id
    FROM mapping m
    WHERE rf.id = m.reverse_fulfillment_id;"
  end

  def down do
    "no-op"
  end
end
