defmodule Rms.Repo.Migrations.AlterFiscalInvoiceErrorReasonToText do
  use Ecto.Migration

  # excellent_migrations:safety-assured-for-this-file column_type_changed
  def up do
    # excellent_migrations:safety-assured-for-this-file raw_sql_executed
    # Drop existing trigger and function
    execute "DROP TRIGGER IF EXISTS set_attempt_number ON fiscal_invoice_errors;"
    execute "DROP FUNCTION IF EXISTS next_attempt_number() CASCADE;"

    # Create function with proper attempt calculation
    execute """
    CREATE OR REPLACE FUNCTION next_attempt_number()
    RETURNS TRIGGER AS $$
    DECLARE
      max_attempt INTEGER;
    BEGIN
      SELECT COALESCE(MAX(attempt), 0) INTO max_attempt
      FROM fiscal_invoice_errors
      WHERE fiscal_invoice_id = NEW.fiscal_invoice_id;

      NEW.attempt := max_attempt + 1;

      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create trigger with BEFORE INSERT
    execute """
    CREATE TRIGGER set_attempt_number
    BEFORE INSERT ON fiscal_invoice_errors
    FOR EACH ROW
    EXECUTE FUNCTION next_attempt_number();
    """
  end

  def down do
    execute "DROP TRIGGER IF EXISTS set_attempt_number ON fiscal_invoice_errors;"
    execute "DROP FUNCTION IF EXISTS next_attempt_number();"
  end
end
