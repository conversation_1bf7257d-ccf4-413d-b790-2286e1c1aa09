defmodule Rms.Repo.Migrations.AllowNullProductVariantIdOnLineItems do
  use Ecto.Migration

  def change do
    # excellent_migrations:safety-assured-for-this-file column_type_changed
    alter table(:line_items) do
      modify :product_variant_id,
             references(:product_variants,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             null: true,
             from:
               references(:product_variants,
                 on_delete: :nothing,
                 with: [organization_id: :organization_id]
               )
    end
  end
end
