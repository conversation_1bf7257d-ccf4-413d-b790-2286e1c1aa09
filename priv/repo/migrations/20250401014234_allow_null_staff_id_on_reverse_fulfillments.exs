defmodule Rms.Repo.Migrations.AllowNullStaffIdOnReverseFulfillments do
  use Ecto.Migration

  def change do
    # excellent_migrations:safety-assured-for-this-file column_type_changed
    alter table(:reverse_fulfillments) do
      modify :staff_id,
             references(:staffs,
               on_delete: :nothing,
               with: [organization_id: :organization_id]
             ),
             null: true,
             from:
               references(:staffs,
                 on_delete: :nothing,
                 with: [organization_id: :organization_id]
               )
    end
  end
end
