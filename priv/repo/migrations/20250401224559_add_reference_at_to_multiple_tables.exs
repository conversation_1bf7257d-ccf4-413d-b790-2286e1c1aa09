defmodule Rms.Repo.Migrations.AddReferenceAtToMultipleTables do
  use Ecto.Migration

  def change do
    alter table(:orders) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:payments) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:transactions) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:line_items) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:fiscal_invoices) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:invoices_items) do
      add :reference_at, :utc_datetime, null: true
    end

    alter table(:invoices_payments) do
      add :reference_at, :utc_datetime, null: true
    end
  end
end
