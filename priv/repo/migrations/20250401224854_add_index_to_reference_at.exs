defmodule Rms.Repo.Migrations.AddIndexToReferenceAt do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def change do
    create index(:orders, [:reference_at], concurrently: true)
    create index(:payments, [:reference_at], concurrently: true)
    create index(:transactions, [:reference_at], concurrently: true)
    create index(:line_items, [:reference_at], concurrently: true)
    create index(:fiscal_invoices, [:reference_at], concurrently: true)
    create index(:invoices_items, [:reference_at], concurrently: true)
    create index(:invoices_payments, [:reference_at], concurrently: true)
  end
end
