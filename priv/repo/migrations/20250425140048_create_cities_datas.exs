defmodule Rms.Repo.Migrations.CreateCitiesDatas do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:cities_datas, primary_key: false) do
      add :ibge_id, :integer, primary_key: true
      add :name, :string, null: false
      add :uf_name, :string, null: false
      add :uf_initials, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:cities_datas, [:name, :uf_initials], concurrently: true)
  end
end
