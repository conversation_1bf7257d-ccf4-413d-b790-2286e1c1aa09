defmodule Rms.Repo.Migrations.CreateUploadEntries do
  use Ecto.Migration

  @disable_migration_lock true
  @disable_ddl_transaction true

  def change do
    create table(:upload_entries) do
      add :s3_key, :string, null: false
      add :status, :string, null: false, default: "pending"
      add :expires_at, :utc_datetime, null: false
      add :uploaded_at, :utc_datetime
      add :error_messages, {:array, :string}

      add :organization_id, references(:organizations, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:upload_entries, [:s3_key], concurrently: true)
    create index(:upload_entries, [:status], concurrently: true)
    create index(:upload_entries, [:organization_id], concurrently: true)
  end
end
