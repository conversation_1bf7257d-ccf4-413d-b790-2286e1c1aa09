defmodule Rms.Repo.Migrations.AddProductTaxesUniqueCombinationIndex do
  use Ecto.Migration

  @disable_ddl_transaction true
  @disable_migration_lock true

  def up do
    # excellent_migrations:safety-assured-for-next-line raw_sql_executed
    execute """
    CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS product_taxes_org_id_ncm_uf_sku_partial_idx
    ON product_taxes (organization_id, COALESCE(ncm, ''), COALESCE(uf, ''), COALESCE(sku, ''))
    WHERE (ncm IS NOT NULL OR sku IS NOT NULL OR uf IS NOT NULL);
    """

    drop_if_exists index(:product_taxes, [:ncm, :uf, :organization_id],
                     name: :product_taxes_ncm_uf_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:ncm, :organization_id],
                     name: :product_taxes_ncm_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:sku, :uf, :organization_id],
                     name: :product_taxes_sku_uf_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:sku, :organization_id],
                     name: :product_taxes_sku_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:uf, :organization_id],
                     name: :product_taxes_uf_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:sku, :ncm, :uf, :organization_id],
                     name: :product_taxes_sku_ncm_uf_organization_id_index,
                     concurrently: true
                   )

    drop_if_exists index(:product_taxes, [:sku, :ncm, :organization_id],
                     name: :product_taxes_sku_ncm_organization_id_index,
                     concurrently: true
                   )
  end

  def down do
    drop_if_exists unique_index(:product_taxes, [:organization_id, :ncm, :sku, :uf],
                     name: :product_taxes_org_id_ncm_uf_sku_partial_idx,
                     concurrently: true
                   )

    create_if_not_exists index(:product_taxes, [:ncm, :uf, :organization_id],
                           name: :product_taxes_ncm_uf_organization_id_index,
                           unique: true,
                           where: "sku IS NULL",
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:ncm, :organization_id],
                           name: :product_taxes_ncm_organization_id_index,
                           unique: true,
                           where: "(uf IS NULL) AND (sku IS NULL)",
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:sku, :uf, :organization_id],
                           name: :product_taxes_sku_uf_organization_id_index,
                           unique: true,
                           where: "ncm IS NULL",
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:sku, :organization_id],
                           name: :product_taxes_sku_organization_id_index,
                           unique: true,
                           where: "(ncm IS NULL) AND (uf IS NULL)",
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:uf, :organization_id],
                           name: :product_taxes_uf_organization_id_index,
                           unique: true,
                           where: "(ncm IS NULL) AND (sku IS NULL)",
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:sku, :ncm, :uf, :organization_id],
                           name: :product_taxes_sku_ncm_uf_organization_id_index,
                           unique: true,
                           concurrently: true
                         )

    create_if_not_exists index(:product_taxes, [:sku, :ncm, :organization_id],
                           name: :product_taxes_sku_ncm_organization_id_index,
                           unique: true,
                           where: "uf IS NULL",
                           concurrently: true
                         )
  end
end
