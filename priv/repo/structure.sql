--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8 (Postgres.app)
-- Dumped by pg_dump version 16.8 (Postgres.app)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: events; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA events;


--
-- Name: citext; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS citext WITH SCHEMA public;


--
-- Name: EXTENSION citext; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION citext IS 'data type for case-insensitive character strings';


--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: unaccent; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS unaccent WITH SCHEMA public;


--
-- Name: EXTENSION unaccent; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION unaccent IS 'text search dictionary that removes accents';


--
-- Name: oban_job_state; Type: TYPE; Schema: events; Owner: -
--

CREATE TYPE events.oban_job_state AS ENUM (
    'available',
    'scheduled',
    'executing',
    'retryable',
    'completed',
    'discarded',
    'cancelled'
);


--
-- Name: oban_job_state; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.oban_job_state AS ENUM (
    'available',
    'scheduled',
    'executing',
    'retryable',
    'completed',
    'discarded',
    'cancelled'
);


--
-- Name: oban_state_to_bit(events.oban_job_state); Type: FUNCTION; Schema: events; Owner: -
--

CREATE FUNCTION events.oban_state_to_bit(state events.oban_job_state) RETURNS jsonb
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
SELECT CASE
       WHEN state = 'scheduled' THEN '0'::jsonb
       WHEN state = 'available' THEN '1'::jsonb
       WHEN state = 'executing' THEN '2'::jsonb
       WHEN state = 'retryable' THEN '3'::jsonb
       WHEN state = 'completed' THEN '4'::jsonb
       WHEN state = 'cancelled' THEN '5'::jsonb
       WHEN state = 'discarded' THEN '6'::jsonb
       END;
$$;


--
-- Name: next_attempt_number(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.next_attempt_number() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  max_attempt INTEGER;
BEGIN
  SELECT COALESCE(MAX(attempt), 0) INTO max_attempt
  FROM fiscal_invoice_errors
  WHERE fiscal_invoice_id = NEW.fiscal_invoice_id;

  NEW.attempt := max_attempt + 1;

  RETURN NEW;
END;
$$;


--
-- Name: oban_state_to_bit(public.oban_job_state); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.oban_state_to_bit(state public.oban_job_state) RETURNS jsonb
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
SELECT CASE
       WHEN state = 'scheduled' THEN '0'::jsonb
       WHEN state = 'available' THEN '1'::jsonb
       WHEN state = 'executing' THEN '2'::jsonb
       WHEN state = 'retryable' THEN '3'::jsonb
       WHEN state = 'completed' THEN '4'::jsonb
       WHEN state = 'cancelled' THEN '5'::jsonb
       WHEN state = 'discarded' THEN '6'::jsonb
       END;
$$;


--
-- Name: update_line_items_return_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_line_items_return_summary() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO line_items_return_summary (line_item_id, original_quantity, total_returned_quantity, available_quantity)
  SELECT
    li.id AS line_item_id,
    li.quantity AS original_quantity,
    COALESCE(SUM(rfli.returned_quantity), 0) AS total_returned_quantity,
    li.quantity - COALESCE(SUM(rfli.returned_quantity), 0) AS available_quantity
  FROM
    line_items li
  LEFT JOIN
    reverse_fulfillment_line_items rfli ON li.id = rfli.line_item_id
  WHERE
    li.id = COALESCE(NEW.line_item_id, OLD.line_item_id)
  GROUP BY
    li.id, li.quantity
  ON CONFLICT (line_item_id) DO UPDATE SET
    total_returned_quantity = EXCLUDED.total_returned_quantity,
    available_quantity = EXCLUDED.available_quantity;

  RETURN NULL;
END;
$$;


--
-- Name: update_sequence(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_sequence() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
a_number INTEGER;
BEGIN
  IF NEW.service != 'external' THEN
    SELECT available_number INTO a_number FROM invoice_series WHERE id = NEW.serie_id FOR UPDATE;
    NEW.invoice_number = a_number;
    UPDATE invoice_series SET available_number = available_number + 1 WHERE id = NEW.serie_id;
  END IF;
  RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: oban_crons; Type: TABLE; Schema: events; Owner: -
--

CREATE TABLE events.oban_crons (
    name text NOT NULL,
    expression text NOT NULL,
    worker text NOT NULL,
    opts jsonb NOT NULL,
    insertions timestamp without time zone[] DEFAULT ARRAY[]::timestamp without time zone[] NOT NULL,
    paused boolean DEFAULT false NOT NULL,
    lock_version integer DEFAULT 1,
    inserted_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: oban_jobs; Type: TABLE; Schema: events; Owner: -
--

CREATE TABLE events.oban_jobs (
    id bigint NOT NULL,
    state events.oban_job_state DEFAULT 'available'::events.oban_job_state NOT NULL,
    queue text DEFAULT 'default'::text NOT NULL,
    worker text NOT NULL,
    args jsonb DEFAULT '{}'::jsonb NOT NULL,
    errors jsonb[] DEFAULT ARRAY[]::jsonb[] NOT NULL,
    attempt integer DEFAULT 0 NOT NULL,
    max_attempts integer DEFAULT 20 NOT NULL,
    inserted_at timestamp without time zone DEFAULT timezone('UTC'::text, now()) NOT NULL,
    scheduled_at timestamp without time zone DEFAULT timezone('UTC'::text, now()) NOT NULL,
    attempted_at timestamp without time zone,
    completed_at timestamp without time zone,
    attempted_by text[],
    discarded_at timestamp without time zone,
    priority integer DEFAULT 0 NOT NULL,
    tags text[] DEFAULT ARRAY[]::text[],
    meta jsonb DEFAULT '{}'::jsonb,
    cancelled_at timestamp without time zone,
    uniq_key text GENERATED ALWAYS AS (
CASE
    WHEN ((meta -> 'uniq_bmp'::text) @> events.oban_state_to_bit(state)) THEN (meta ->> 'uniq_key'::text)
    ELSE NULL::text
END) STORED,
    CONSTRAINT attempt_range CHECK (((attempt >= 0) AND (attempt <= max_attempts))),
    CONSTRAINT positive_max_attempts CHECK ((max_attempts > 0)),
    CONSTRAINT queue_length CHECK (((char_length(queue) > 0) AND (char_length(queue) < 128))),
    CONSTRAINT worker_length CHECK (((char_length(worker) > 0) AND (char_length(worker) < 128)))
);


--
-- Name: TABLE oban_jobs; Type: COMMENT; Schema: events; Owner: -
--

COMMENT ON TABLE events.oban_jobs IS '12';


--
-- Name: oban_jobs_id_seq; Type: SEQUENCE; Schema: events; Owner: -
--

CREATE SEQUENCE events.oban_jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: oban_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: events; Owner: -
--

ALTER SEQUENCE events.oban_jobs_id_seq OWNED BY events.oban_jobs.id;


--
-- Name: oban_peers; Type: TABLE; Schema: events; Owner: -
--

CREATE UNLOGGED TABLE events.oban_peers (
    name text NOT NULL,
    node text NOT NULL,
    started_at timestamp without time zone NOT NULL,
    expires_at timestamp without time zone NOT NULL
);


--
-- Name: oban_producers; Type: TABLE; Schema: events; Owner: -
--

CREATE UNLOGGED TABLE events.oban_producers (
    uuid uuid NOT NULL,
    name text NOT NULL,
    node text NOT NULL,
    queue text NOT NULL,
    meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    started_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: TABLE oban_producers; Type: COMMENT; Schema: events; Owner: -
--

COMMENT ON TABLE events.oban_producers IS '1.5.0';


--
-- Name: oban_queues; Type: TABLE; Schema: events; Owner: -
--

CREATE TABLE events.oban_queues (
    name text NOT NULL,
    opts jsonb DEFAULT '{}'::jsonb NOT NULL,
    "only" jsonb DEFAULT '{}'::jsonb NOT NULL,
    lock_version integer DEFAULT 1,
    inserted_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: addon_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addon_mappings (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    addon_id bigint NOT NULL,
    external_id character varying(255) NOT NULL,
    source character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: addon_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.addon_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: addon_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.addon_mappings_id_seq OWNED BY public.addon_mappings.id;


--
-- Name: addons; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addons (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    name character varying(255),
    list_price numeric NOT NULL,
    type character varying(255) NOT NULL,
    description character varying(255),
    image_url character varying(255),
    metadata jsonb,
    archived_at timestamp(0) without time zone,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    price numeric NOT NULL
);


--
-- Name: addons_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.addons_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: addons_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.addons_id_seq OWNED BY public.addons.id;


--
-- Name: addresses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addresses (
    id bigint NOT NULL,
    receiver_name character varying(255),
    city_name character varying(255),
    state character varying(255),
    country_name character varying(255),
    neighborhood character varying(255),
    street character varying(255),
    street_type character varying(255),
    number character varying(255),
    zip character varying(255),
    complement character varying(255),
    organization_id bigint,
    customer_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    location_id bigint,
    city_code character varying(255)
);


--
-- Name: addresses_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.addresses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: addresses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.addresses_id_seq OWNED BY public.addresses.id;


--
-- Name: api_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.api_tokens (
    id bigint NOT NULL,
    token bytea NOT NULL,
    token_hash bytea NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: api_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.api_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: api_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.api_tokens_id_seq OWNED BY public.api_tokens.id;


--
-- Name: authorization_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.authorization_requests (
    id bigint NOT NULL,
    code character varying(255) NOT NULL,
    status character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    staff_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: authorization_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.authorization_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: authorization_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.authorization_requests_id_seq OWNED BY public.authorization_requests.id;


--
-- Name: cancelation_endpoints; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cancelation_endpoints (
    id bigint NOT NULL,
    endpoint character varying(255) NOT NULL,
    headers character varying(255)[] NOT NULL,
    active boolean DEFAULT false NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: cancelation_endpoints_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cancelation_endpoints_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cancelation_endpoints_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cancelation_endpoints_id_seq OWNED BY public.cancelation_endpoints.id;


--
-- Name: card_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.card_tokens (
    id bigint NOT NULL,
    token bytea,
    organization_id bigint,
    customer_id bigint,
    payment_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: card_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.card_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: card_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.card_tokens_id_seq OWNED BY public.card_tokens.id;


--
-- Name: cart_addons; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_addons (
    id uuid NOT NULL,
    name character varying(255) NOT NULL,
    price numeric NOT NULL,
    quantity integer NOT NULL,
    type character varying(255) NOT NULL,
    description text,
    cart_id uuid NOT NULL,
    addon_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    metadata jsonb,
    list_price numeric,
    image_url character varying(255)
);


--
-- Name: cart_item_discounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_item_discounts (
    id bigint NOT NULL,
    type character varying(255) NOT NULL,
    value character varying(255),
    code character varying(255),
    description character varying(255),
    organization_id bigint NOT NULL,
    authorization_request_id bigint,
    cart_item_id uuid NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: cart_item_discounts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cart_item_discounts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cart_item_discounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cart_item_discounts_id_seq OWNED BY public.cart_item_discounts.id;


--
-- Name: cart_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cart_items (
    id uuid NOT NULL,
    quantity integer NOT NULL,
    list_price numeric NOT NULL,
    selling_price numeric NOT NULL,
    total_price numeric NOT NULL,
    metadata jsonb,
    organization_id bigint NOT NULL,
    product_variant_id bigint NOT NULL,
    delivery_group_id uuid NOT NULL,
    item_index integer,
    is_gift boolean DEFAULT false NOT NULL,
    group_index character varying(255)
);


--
-- Name: carts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.carts (
    id uuid NOT NULL,
    status character varying(255) NOT NULL,
    note character varying(255),
    total_price numeric NOT NULL,
    total_items_list_price numeric NOT NULL,
    total_items_selling_price numeric NOT NULL,
    total_manual_discount numeric NOT NULL,
    total_delivery_price numeric NOT NULL,
    max_delivery_time integer NOT NULL,
    messages jsonb,
    metadata jsonb,
    ecommerce character varying(255),
    organization_id bigint NOT NULL,
    location_id bigint,
    customer_id bigint,
    staff_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    saved boolean,
    shipping_address_id bigint,
    state_of_sale character varying(255) DEFAULT 'seller'::character varying,
    total_ecommerce_discounts numeric DEFAULT 0 NOT NULL,
    total_items_manual_discount numeric DEFAULT 0 NOT NULL
);


--
-- Name: cielo_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cielo_credentials (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    token bytea,
    expires_in integer,
    client_id bytea,
    client_secret bytea,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: cielo_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.cielo_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cielo_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.cielo_credentials_id_seq OWNED BY public.cielo_credentials.id;


--
-- Name: cities_datas; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.cities_datas (
    ibge_id integer NOT NULL,
    name character varying(255) NOT NULL,
    uf_name character varying(255) NOT NULL,
    uf_initials character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: customer_endpoint_integrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_endpoint_integrations (
    id bigint NOT NULL,
    endpoint character varying(255) NOT NULL,
    unique_on character varying(255)[] NOT NULL,
    headers character varying(255)[] NOT NULL,
    active boolean NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: customer_endpoint_integrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customer_endpoint_integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customer_endpoint_integrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customer_endpoint_integrations_id_seq OWNED BY public.customer_endpoint_integrations.id;


--
-- Name: customer_sync; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_sync (
    id bigint NOT NULL,
    external_id character varying(255),
    expected_count integer,
    status character varying(255),
    message character varying(255),
    source character varying(255),
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: customer_sync_entries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_sync_entries (
    id bigint NOT NULL,
    external_id character varying(255),
    status character varying(255),
    errors jsonb,
    organization_id bigint NOT NULL,
    customer_sync_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: customer_sync_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customer_sync_entries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customer_sync_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customer_sync_entries_id_seq OWNED BY public.customer_sync_entries.id;


--
-- Name: customer_sync_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customer_sync_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customer_sync_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customer_sync_id_seq OWNED BY public.customer_sync.id;


--
-- Name: customer_sync_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customer_sync_mappings (
    id bigint NOT NULL,
    external_id character varying(255),
    source character varying(255),
    organization_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: customer_sync_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customer_sync_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customer_sync_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customer_sync_mappings_id_seq OWNED BY public.customer_sync_mappings.id;


--
-- Name: customers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.customers (
    id bigint NOT NULL,
    document_type public.citext,
    document bytea,
    document_hash bytea,
    email bytea,
    email_hash bytea,
    name bytea,
    name_hash bytea,
    primary_phone_number bytea,
    primary_phone_number_hash bytea,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    plain_name public.citext,
    birthdate date,
    document_plain character varying(255),
    email_plain character varying(255),
    primary_phone_number_plain character varying(255)
);


--
-- Name: customers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.customers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.customers_id_seq OWNED BY public.customers.id;


--
-- Name: delivery_groups; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.delivery_groups (
    id uuid NOT NULL,
    delivery_time integer NOT NULL,
    fulfillment_type character varying(255) NOT NULL,
    metadata jsonb,
    organization_id bigint NOT NULL,
    cart_id uuid NOT NULL,
    delivery_price numeric,
    pickup_point jsonb
);


--
-- Name: discounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.discounts (
    id bigint NOT NULL,
    type character varying(255) NOT NULL,
    value character varying(255),
    description character varying(255),
    organization_id bigint NOT NULL,
    authorization_request_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    order_id bigint,
    cart_id uuid
);


--
-- Name: discounts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.discounts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: discounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.discounts_id_seq OWNED BY public.discounts.id;


--
-- Name: docks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.docks (
    id bigint NOT NULL,
    external_id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: docks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.docks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: docks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.docks_id_seq OWNED BY public.docks.id;


--
-- Name: erp_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.erp_credentials (
    id bigint NOT NULL,
    url bytea,
    credential bytea,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: erp_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.erp_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: erp_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.erp_credentials_id_seq OWNED BY public.erp_credentials.id;


--
-- Name: external_payment_references; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.external_payment_references (
    id bigint NOT NULL,
    partner character varying(255),
    external_id character varying(255),
    payment_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id bigint NOT NULL
);


--
-- Name: external_payment_references_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.external_payment_references_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: external_payment_references_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.external_payment_references_id_seq OWNED BY public.external_payment_references.id;


--
-- Name: fiscal_invoice_errors; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fiscal_invoice_errors (
    id bigint NOT NULL,
    stacktrace character varying(255),
    organization_id bigint NOT NULL,
    fiscal_invoice_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    status character varying(255),
    attempt integer DEFAULT 1 NOT NULL,
    reason text
);


--
-- Name: fiscal_invoice_errors_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fiscal_invoice_errors_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fiscal_invoice_errors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fiscal_invoice_errors_id_seq OWNED BY public.fiscal_invoice_errors.id;


--
-- Name: fiscal_invoices; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fiscal_invoices (
    id bigint NOT NULL,
    invoice_number integer NOT NULL,
    operation_type character varying(255) NOT NULL,
    service character varying(255) NOT NULL,
    external_id character varying(255),
    df_key character varying(255),
    status character varying(255) NOT NULL,
    autorized_at timestamp(0) without time zone,
    canceled_at timestamp(0) without time zone,
    qr_code text,
    xml text,
    danfe text,
    metadata jsonb,
    customer_id bigint,
    organization_id bigint NOT NULL,
    serie_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    fulfillment_id bigint,
    reverse_fulfillment_id uuid,
    integrated_erp boolean DEFAULT false,
    reference_at timestamp(0) without time zone
);


--
-- Name: fiscal_invoices_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fiscal_invoices_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fiscal_invoices_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fiscal_invoices_id_seq OWNED BY public.fiscal_invoices.id;


--
-- Name: fiscal_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fiscal_settings (
    id bigint NOT NULL,
    handle_sale boolean DEFAULT true NOT NULL,
    handle_return boolean DEFAULT true NOT NULL,
    handle_transfer boolean DEFAULT true NOT NULL,
    environment character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    transfer_without_taxes boolean DEFAULT false
);


--
-- Name: fiscal_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fiscal_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fiscal_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fiscal_settings_id_seq OWNED BY public.fiscal_settings.id;


--
-- Name: fulfillment_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillment_items (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    order_id bigint NOT NULL,
    fulfillment_id bigint,
    line_item_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: fulfillment_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fulfillment_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fulfillment_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fulfillment_items_id_seq OWNED BY public.fulfillment_items.id;


--
-- Name: fulfillments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fulfillments (
    id bigint NOT NULL,
    shipping_method character varying(255) NOT NULL,
    external_reference character varying(255),
    organization_id bigint NOT NULL,
    order_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    ecommerce character varying(255),
    status character varying(255),
    metadata jsonb,
    reference_at timestamp(0) without time zone
);


--
-- Name: fulfillments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.fulfillments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fulfillments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.fulfillments_id_seq OWNED BY public.fulfillments.id;


--
-- Name: gift_handler_configurations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gift_handler_configurations (
    id bigint NOT NULL,
    gifts jsonb,
    product_variant_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: gift_handler_configurations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gift_handler_configurations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gift_handler_configurations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gift_handler_configurations_id_seq OWNED BY public.gift_handler_configurations.id;


--
-- Name: gift_promo_credential; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gift_promo_credential (
    id bigint NOT NULL,
    store_id character varying(255),
    access_key bytea,
    username bytea,
    password bytea,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    location_id bigint
);


--
-- Name: gift_promo_credential_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gift_promo_credential_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gift_promo_credential_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gift_promo_credential_id_seq OWNED BY public.gift_promo_credential.id;


--
-- Name: iglu_credit_payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.iglu_credit_payments (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    payment_id bigint NOT NULL,
    iglu_credit_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: iglu_credit_payments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.iglu_credit_payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: iglu_credit_payments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.iglu_credit_payments_id_seq OWNED BY public.iglu_credit_payments.id;


--
-- Name: iglu_credits; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.iglu_credits (
    id bigint NOT NULL,
    amount numeric NOT NULL,
    status character varying(255) DEFAULT 'available'::character varying NOT NULL,
    reason character varying(255) NOT NULL,
    expires_at timestamp(0) without time zone,
    metadata jsonb,
    organization_id bigint NOT NULL,
    customer_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    reverse_fulfillment_id uuid NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    used_amount numeric DEFAULT 0 NOT NULL,
    CONSTRAINT used_amount_must_not_exceed_amount CHECK ((used_amount <= amount))
);


--
-- Name: iglu_credits_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.iglu_credits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: iglu_credits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.iglu_credits_id_seq OWNED BY public.iglu_credits.id;


--
-- Name: interstate_taxes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.interstate_taxes (
    id bigint NOT NULL,
    uf_origin character varying(255),
    uf_destiny character varying(255),
    icms_percentage numeric NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: interstate_taxes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.interstate_taxes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: interstate_taxes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.interstate_taxes_id_seq OWNED BY public.interstate_taxes.id;


--
-- Name: inventory_item_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory_item_mappings (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    product_variant_id bigint NOT NULL,
    external_id character varying(255) NOT NULL,
    source character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: inventory_item_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.inventory_item_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: inventory_item_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.inventory_item_mappings_id_seq OWNED BY public.inventory_item_mappings.id;


--
-- Name: inventory_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.inventory_items (
    id bigint NOT NULL,
    quantity integer NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    product_variant_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: inventory_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.inventory_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: inventory_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.inventory_items_id_seq OWNED BY public.inventory_items.id;


--
-- Name: invoice_series; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoice_series (
    id bigint NOT NULL,
    invoice_type character varying(255) NOT NULL,
    invoice_serie integer NOT NULL,
    invoice_env character varying(255) NOT NULL,
    status character varying(255) NOT NULL,
    available_number integer NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: invoice_series_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invoice_series_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invoice_series_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invoice_series_id_seq OWNED BY public.invoice_series.id;


--
-- Name: invoices_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoices_items (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    fiscal_invoice_id bigint,
    line_item_id bigint,
    reference_at timestamp(0) without time zone
);


--
-- Name: invoices_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invoices_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invoices_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invoices_items_id_seq OWNED BY public.invoices_items.id;


--
-- Name: invoices_payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoices_payments (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    fiscal_invoice_id bigint,
    payment_id bigint,
    reference_at timestamp(0) without time zone
);


--
-- Name: invoices_payments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.invoices_payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: invoices_payments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.invoices_payments_id_seq OWNED BY public.invoices_payments.id;


--
-- Name: line_item_discounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.line_item_discounts (
    id bigint NOT NULL,
    type character varying(255) NOT NULL,
    value character varying(255),
    code character varying(255),
    description character varying(255),
    organization_id bigint NOT NULL,
    authorization_request_id bigint,
    line_item_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: line_item_discounts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.line_item_discounts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: line_item_discounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.line_item_discounts_id_seq OWNED BY public.line_item_discounts.id;


--
-- Name: line_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.line_items (
    id bigint NOT NULL,
    price numeric NOT NULL,
    quantity integer NOT NULL,
    shipping_method character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    product_variant_id bigint,
    order_id bigint,
    staff_id bigint,
    location_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    shipping_settings jsonb,
    list_price numeric,
    image_url character varying(255),
    variant_name character varying(255),
    product_name character varying(255),
    sku character varying(255),
    fulfillment_id bigint,
    manual_discount numeric DEFAULT 0,
    group_index character varying(255),
    is_gift boolean DEFAULT false NOT NULL,
    reference_at timestamp(0) without time zone
);


--
-- Name: line_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.line_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: line_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.line_items_id_seq OWNED BY public.line_items.id;


--
-- Name: line_items_return_summary; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.line_items_return_summary (
    line_item_id bigint NOT NULL,
    original_quantity integer NOT NULL,
    total_returned_quantity integer DEFAULT 0 NOT NULL,
    available_quantity integer NOT NULL,
    CONSTRAINT check_available_quantity CHECK ((available_quantity >= 0))
);


--
-- Name: location_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_mappings (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    external_id character varying(255) NOT NULL,
    source character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: location_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_mappings_id_seq OWNED BY public.location_mappings.id;


--
-- Name: location_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_settings (
    id bigint NOT NULL,
    key character varying(255),
    value jsonb,
    location_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id integer NOT NULL
);


--
-- Name: location_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_settings_id_seq OWNED BY public.location_settings.id;


--
-- Name: location_sync; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_sync (
    id bigint NOT NULL,
    external_id character varying(255),
    expected_count integer,
    status character varying(255),
    message character varying(255),
    source character varying(255),
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: location_sync_entries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_sync_entries (
    id bigint NOT NULL,
    external_id character varying(255),
    status character varying(255),
    errors jsonb,
    organization_id bigint NOT NULL,
    location_sync_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: location_sync_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_sync_entries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_sync_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_sync_entries_id_seq OWNED BY public.location_sync_entries.id;


--
-- Name: location_sync_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_sync_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_sync_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_sync_id_seq OWNED BY public.location_sync.id;


--
-- Name: location_taxes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_taxes (
    id bigint NOT NULL,
    ie character varying(255) NOT NULL,
    crt character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    location_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    current_tax_uf character varying(255)
);


--
-- Name: location_taxes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_taxes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_taxes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_taxes_id_seq OWNED BY public.location_taxes.id;


--
-- Name: location_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.location_users (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    location_id bigint NOT NULL,
    organization_id bigint NOT NULL
);


--
-- Name: location_users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.location_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.location_users_id_seq OWNED BY public.location_users.id;


--
-- Name: locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.locations (
    id bigint NOT NULL,
    name character varying(255),
    cnpj character varying(255) NOT NULL,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    archived_at timestamp(0) without time zone,
    external_id character varying(255)
);


--
-- Name: locations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.locations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: locations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.locations_id_seq OWNED BY public.locations.id;


--
-- Name: oban_crons; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.oban_crons (
    name text NOT NULL,
    expression text NOT NULL,
    worker text NOT NULL,
    opts jsonb NOT NULL,
    insertions timestamp without time zone[] DEFAULT ARRAY[]::timestamp without time zone[] NOT NULL,
    paused boolean DEFAULT false NOT NULL,
    lock_version integer DEFAULT 1,
    inserted_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: oban_jobs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.oban_jobs (
    id bigint NOT NULL,
    state public.oban_job_state DEFAULT 'available'::public.oban_job_state NOT NULL,
    queue text DEFAULT 'default'::text NOT NULL,
    worker text NOT NULL,
    args jsonb DEFAULT '{}'::jsonb NOT NULL,
    errors jsonb[] DEFAULT ARRAY[]::jsonb[] NOT NULL,
    attempt integer DEFAULT 0 NOT NULL,
    max_attempts integer DEFAULT 20 NOT NULL,
    inserted_at timestamp without time zone DEFAULT timezone('UTC'::text, now()) NOT NULL,
    scheduled_at timestamp without time zone DEFAULT timezone('UTC'::text, now()) NOT NULL,
    attempted_at timestamp without time zone,
    completed_at timestamp without time zone,
    attempted_by text[],
    discarded_at timestamp without time zone,
    priority integer DEFAULT 0 NOT NULL,
    tags text[] DEFAULT ARRAY[]::text[],
    meta jsonb DEFAULT '{}'::jsonb,
    cancelled_at timestamp without time zone,
    uniq_key text GENERATED ALWAYS AS (
CASE
    WHEN ((meta -> 'uniq_bmp'::text) @> public.oban_state_to_bit(state)) THEN (meta ->> 'uniq_key'::text)
    ELSE NULL::text
END) STORED,
    CONSTRAINT attempt_range CHECK (((attempt >= 0) AND (attempt <= max_attempts))),
    CONSTRAINT positive_max_attempts CHECK ((max_attempts > 0)),
    CONSTRAINT queue_length CHECK (((char_length(queue) > 0) AND (char_length(queue) < 128))),
    CONSTRAINT worker_length CHECK (((char_length(worker) > 0) AND (char_length(worker) < 128)))
);


--
-- Name: TABLE oban_jobs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.oban_jobs IS '12';


--
-- Name: oban_jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.oban_jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: oban_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.oban_jobs_id_seq OWNED BY public.oban_jobs.id;


--
-- Name: oban_peers; Type: TABLE; Schema: public; Owner: -
--

CREATE UNLOGGED TABLE public.oban_peers (
    name text NOT NULL,
    node text NOT NULL,
    started_at timestamp without time zone NOT NULL,
    expires_at timestamp without time zone NOT NULL
);


--
-- Name: oban_producers; Type: TABLE; Schema: public; Owner: -
--

CREATE UNLOGGED TABLE public.oban_producers (
    uuid uuid NOT NULL,
    name text NOT NULL,
    node text NOT NULL,
    queue text NOT NULL,
    meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    started_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: TABLE oban_producers; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.oban_producers IS '1.5.0';


--
-- Name: oban_queues; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.oban_queues (
    name text NOT NULL,
    opts jsonb DEFAULT '{}'::jsonb NOT NULL,
    "only" jsonb DEFAULT '{}'::jsonb NOT NULL,
    lock_version integer DEFAULT 1,
    inserted_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: order_customers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_customers (
    id bigint NOT NULL,
    document_type character varying(255),
    document bytea,
    document_hash bytea,
    email bytea,
    email_hash bytea,
    name bytea,
    name_hash bytea,
    primary_phone_number bytea,
    primary_phone_number_hash bytea,
    organization_id bigint NOT NULL,
    order_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    customer_id integer NOT NULL,
    plain_name character varying(255)
);


--
-- Name: order_customers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_customers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: order_customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_customers_id_seq OWNED BY public.order_customers.id;


--
-- Name: order_errors; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_errors (
    id bigint NOT NULL,
    error character varying(255) NOT NULL,
    stacktrace character varying(255),
    order_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: order_errors_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_errors_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: order_errors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_errors_id_seq OWNED BY public.order_errors.id;


--
-- Name: order_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.order_settings (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    prefix character varying(255) NOT NULL,
    suffix character varying(255) NOT NULL,
    current_order_number integer NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: order_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.order_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: order_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.order_settings_id_seq OWNED BY public.order_settings.id;


--
-- Name: orders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.orders (
    id bigint NOT NULL,
    total_price numeric NOT NULL,
    status character varying(255) NOT NULL,
    external_id character varying(255),
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id bigint NOT NULL,
    customer_id bigint,
    staff_id bigint,
    sales_channel character varying(255),
    location_id bigint,
    shipping_address jsonb,
    total_delivery_price numeric,
    total_discount numeric,
    total_items_selling_price numeric,
    total_items_list_price numeric,
    name character varying(255),
    source character varying(255) DEFAULT 'iglu'::character varying,
    cashier_id bigint,
    addons jsonb,
    total_price_with_addons numeric,
    total_discount_with_addons numeric,
    total_ecommerce_discounts numeric DEFAULT 0 NOT NULL,
    total_items_manual_discount numeric DEFAULT 0 NOT NULL,
    reference_at timestamp(0) without time zone,
    metadata jsonb
);


--
-- Name: orders_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.orders_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: orders_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.orders_id_seq OWNED BY public.orders.id;


--
-- Name: organization_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organization_settings (
    id bigint NOT NULL,
    key character varying(255),
    value jsonb,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: organization_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.organization_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: organization_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.organization_settings_id_seq OWNED BY public.organization_settings.id;


--
-- Name: organizations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organizations (
    id bigint NOT NULL,
    name character varying(255),
    cnpj character varying(255),
    org_id character varying(255),
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    safety_net_quantity integer DEFAULT 5
);


--
-- Name: organizations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;


--
-- Name: packing_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.packing_items (
    id bigint NOT NULL,
    quantity integer NOT NULL,
    organization_id bigint NOT NULL,
    fulfillment_id bigint NOT NULL,
    packing_id bigint NOT NULL,
    line_item_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: packing_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.packing_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: packing_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.packing_items_id_seq OWNED BY public.packing_items.id;


--
-- Name: packings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.packings (
    id bigint NOT NULL,
    shipping_method character varying(255) NOT NULL,
    external_reference character varying(255),
    status character varying(255),
    organization_id bigint NOT NULL,
    dock_id bigint NOT NULL,
    fulfillment_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    courier_name character varying(255),
    customer_id bigint,
    staff_id bigint,
    total_price numeric DEFAULT 0 NOT NULL
);


--
-- Name: packings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.packings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: packings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.packings_id_seq OWNED BY public.packings.id;


--
-- Name: pagarme_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pagarme_credentials (
    id bigint NOT NULL,
    credential bytea,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    location_id bigint
);


--
-- Name: pagarme_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pagarme_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pagarme_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pagarme_credentials_id_seq OWNED BY public.pagarme_credentials.id;


--
-- Name: pagarme_hook_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pagarme_hook_credentials (
    id bigint NOT NULL,
    shop_id character varying(255),
    credential bytea,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    credential_hash bytea
);


--
-- Name: pagarme_hook_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pagarme_hook_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pagarme_hook_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pagarme_hook_credentials_id_seq OWNED BY public.pagarme_hook_credentials.id;


--
-- Name: payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payments (
    id bigint NOT NULL,
    amount numeric NOT NULL,
    status character varying(255) NOT NULL,
    method character varying(255) NOT NULL,
    metadata jsonb,
    transaction_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id bigint NOT NULL,
    reference_at timestamp(0) without time zone
);


--
-- Name: payments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.payments_id_seq OWNED BY public.payments.id;


--
-- Name: product_sync; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_sync (
    id bigint NOT NULL,
    external_id character varying(255),
    expected_count integer,
    status character varying(255),
    message character varying(255),
    error_code character varying(255),
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    name character varying(255),
    source character varying(255)
);


--
-- Name: product_sync_configurations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_sync_configurations (
    id bigint NOT NULL,
    field_priorities jsonb NOT NULL,
    default_priority character varying(255)[] NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: product_sync_configurations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_sync_configurations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_sync_configurations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_sync_configurations_id_seq OWNED BY public.product_sync_configurations.id;


--
-- Name: product_sync_entries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_sync_entries (
    id bigint NOT NULL,
    sku character varying(255),
    external_id character varying(255),
    status character varying(255),
    errors jsonb[],
    organization_id bigint,
    product_sync_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: product_sync_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_sync_entries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_sync_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_sync_entries_id_seq OWNED BY public.product_sync_entries.id;


--
-- Name: product_sync_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_sync_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_sync_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_sync_id_seq OWNED BY public.product_sync.id;


--
-- Name: product_sync_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_sync_mappings (
    id bigint NOT NULL,
    organization_id bigint,
    product_id bigint,
    external_id character varying(255),
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    source character varying(255)
);


--
-- Name: product_sync_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_sync_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_sync_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_sync_mappings_id_seq OWNED BY public.product_sync_mappings.id;


--
-- Name: product_taxes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_taxes (
    id bigint NOT NULL,
    ncm character varying(255),
    uf character varying(255),
    origin character varying(255) NOT NULL,
    cfop character varying(255) NOT NULL,
    cest character varying(255),
    cst_icms character varying(255) NOT NULL,
    cst_pis character varying(255) NOT NULL,
    cst_cofins character varying(255) NOT NULL,
    icms_percentage numeric NOT NULL,
    fcp_percentage numeric NOT NULL,
    pis_percentage numeric NOT NULL,
    cofins_percentage numeric NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    sku character varying(255),
    icms_base_reduction numeric DEFAULT 1 NOT NULL,
    benefit_code character varying(255),
    additional_info_message text
);


--
-- Name: product_taxes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_taxes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_taxes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_taxes_id_seq OWNED BY public.product_taxes.id;


--
-- Name: product_variant_mappings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variant_mappings (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    product_variant_id bigint NOT NULL,
    external_id character varying(255) NOT NULL,
    source character varying(255) NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    sold_out boolean DEFAULT false
);


--
-- Name: product_variant_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_variant_mappings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_variant_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_variant_mappings_id_seq OWNED BY public.product_variant_mappings.id;


--
-- Name: product_variants; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_variants (
    id bigint NOT NULL,
    name character varying(255),
    list_price numeric NOT NULL,
    sku character varying(255),
    bar_code character varying(255),
    image_urls character varying(255)[],
    variation_types jsonb[],
    product_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    archived_at timestamp(0) without time zone,
    price numeric NOT NULL,
    sync_metadata jsonb DEFAULT '{}'::jsonb,
    tags character varying(255)[]
);


--
-- Name: product_variants_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.product_variants_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: product_variants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.product_variants_id_seq OWNED BY public.product_variants.id;


--
-- Name: products; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.products (
    id bigint NOT NULL,
    name character varying(255),
    ncm character varying(255),
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    product_type character varying(255) DEFAULT 'item'::character varying,
    tags character varying(255)[]
);


--
-- Name: products_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.products_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: products_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.products_id_seq OWNED BY public.products.id;


--
-- Name: reverse_fulfillment_line_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reverse_fulfillment_line_items (
    id bigint NOT NULL,
    returned_quantity integer NOT NULL,
    reason character varying(255),
    metadata jsonb,
    reverse_fulfillment_id uuid NOT NULL,
    organization_id bigint NOT NULL,
    line_item_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    return_to_inventory boolean DEFAULT false NOT NULL
);


--
-- Name: reverse_fulfillment_line_items_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.reverse_fulfillment_line_items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reverse_fulfillment_line_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.reverse_fulfillment_line_items_id_seq OWNED BY public.reverse_fulfillment_line_items.id;


--
-- Name: reverse_fulfillments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reverse_fulfillments (
    id uuid NOT NULL,
    status character varying(255) DEFAULT 'pending'::character varying NOT NULL,
    metadata jsonb,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    location_id bigint NOT NULL,
    staff_id bigint,
    customer_id bigint
);


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


--
-- Name: shipping_settings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shipping_settings (
    id bigint NOT NULL,
    price numeric NOT NULL,
    time_period character varying(255),
    ecommerce character varying(255),
    settings jsonb,
    organization_id bigint NOT NULL,
    fulfillment_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: shipping_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shipping_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shipping_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shipping_settings_id_seq OWNED BY public.shipping_settings.id;


--
-- Name: shopify_apps; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shopify_apps (
    id bigint NOT NULL,
    client_id character varying(255) NOT NULL,
    client_secret bytea NOT NULL,
    shop_domain character varying(255) NOT NULL,
    organization_id bigint NOT NULL
);


--
-- Name: shopify_apps_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shopify_apps_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shopify_apps_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shopify_apps_id_seq OWNED BY public.shopify_apps.id;


--
-- Name: shopify_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shopify_credentials (
    id bigint NOT NULL,
    shop character varying(255) NOT NULL,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    credential bytea NOT NULL
);


--
-- Name: shopify_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shopify_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shopify_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shopify_credentials_id_seq OWNED BY public.shopify_credentials.id;


--
-- Name: shopify_order_additional_infos; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shopify_order_additional_infos (
    id bigint NOT NULL,
    order_type character varying(255) NOT NULL,
    fields character varying(255)[] DEFAULT ARRAY[]::character varying[],
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: shopify_order_additional_infos_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shopify_order_additional_infos_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shopify_order_additional_infos_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shopify_order_additional_infos_id_seq OWNED BY public.shopify_order_additional_infos.id;


--
-- Name: shopify_storefront_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shopify_storefront_credentials (
    id bigint NOT NULL,
    credential_id bytea,
    credential bytea,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: shopify_storefront_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shopify_storefront_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shopify_storefront_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shopify_storefront_credentials_id_seq OWNED BY public.shopify_storefront_credentials.id;


--
-- Name: staff_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.staff_roles (
    id bigint NOT NULL,
    seller boolean NOT NULL,
    stocker boolean NOT NULL,
    staff_id bigint NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    cashier boolean DEFAULT false NOT NULL
);


--
-- Name: staff_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.staff_roles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: staff_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.staff_roles_id_seq OWNED BY public.staff_roles.id;


--
-- Name: staffs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.staffs (
    id bigint NOT NULL,
    name character varying(255),
    external_id character varying(255),
    archived_at timestamp(0) without time zone,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    erp_id character varying(255)
);


--
-- Name: staffs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.staffs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: staffs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.staffs_id_seq OWNED BY public.staffs.id;


--
-- Name: staffs_locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.staffs_locations (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    staff_id bigint,
    location_id bigint NOT NULL
);


--
-- Name: staffs_locations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.staffs_locations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: staffs_locations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.staffs_locations_id_seq OWNED BY public.staffs_locations.id;


--
-- Name: subscriptions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.subscriptions (
    id bigint NOT NULL,
    value numeric,
    frequency character varying(255),
    organization_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: subscriptions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.subscriptions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: subscriptions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.subscriptions_id_seq OWNED BY public.subscriptions.id;


--
-- Name: transaction_customers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.transaction_customers (
    id bigint NOT NULL,
    name character varying(255),
    document_type character varying(255) NOT NULL,
    document bytea NOT NULL,
    transaction_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id bigint NOT NULL,
    email bytea,
    address jsonb,
    phone_number bytea,
    additional_information character varying(255)
);


--
-- Name: transaction_customers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.transaction_customers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: transaction_customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.transaction_customers_id_seq OWNED BY public.transaction_customers.id;


--
-- Name: transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.transactions (
    id bigint NOT NULL,
    status character varying(255),
    order_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    organization_id bigint NOT NULL,
    reference_at timestamp(0) without time zone
);


--
-- Name: transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.transactions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.transactions_id_seq OWNED BY public.transactions.id;


--
-- Name: upload_entries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.upload_entries (
    id bigint NOT NULL,
    s3_key character varying(255) NOT NULL,
    status character varying(255) DEFAULT 'pending'::character varying NOT NULL,
    expires_at timestamp(0) without time zone NOT NULL,
    uploaded_at timestamp(0) without time zone,
    error_messages character varying(255)[],
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: upload_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.upload_entries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: upload_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.upload_entries_id_seq OWNED BY public.upload_entries.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    name character varying(255),
    email character varying(255),
    external_id character varying(255) NOT NULL,
    organization_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    provider character varying(255) NOT NULL
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: vinco_credential; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vinco_credential (
    id bigint NOT NULL,
    key bytea,
    organization_id bigint,
    location_id bigint,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


--
-- Name: vinco_credential_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.vinco_credential_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: vinco_credential_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.vinco_credential_id_seq OWNED BY public.vinco_credential.id;


--
-- Name: vtex_credentials; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vtex_credentials (
    id bigint NOT NULL,
    account_name character varying(255) NOT NULL,
    app_key bytea NOT NULL,
    app_token bytea NOT NULL,
    organization_id bigint NOT NULL,
    inserted_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL,
    sales_channel_id integer,
    affiliate_id character varying(255),
    alias character varying(255),
    main_account_name character varying(255),
    payment_system integer
);


--
-- Name: vtex_credentials_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.vtex_credentials_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: vtex_credentials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.vtex_credentials_id_seq OWNED BY public.vtex_credentials.id;


--
-- Name: oban_jobs id; Type: DEFAULT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_jobs ALTER COLUMN id SET DEFAULT nextval('events.oban_jobs_id_seq'::regclass);


--
-- Name: addon_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addon_mappings ALTER COLUMN id SET DEFAULT nextval('public.addon_mappings_id_seq'::regclass);


--
-- Name: addons id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addons ALTER COLUMN id SET DEFAULT nextval('public.addons_id_seq'::regclass);


--
-- Name: addresses id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses ALTER COLUMN id SET DEFAULT nextval('public.addresses_id_seq'::regclass);


--
-- Name: api_tokens id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens ALTER COLUMN id SET DEFAULT nextval('public.api_tokens_id_seq'::regclass);


--
-- Name: authorization_requests id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authorization_requests ALTER COLUMN id SET DEFAULT nextval('public.authorization_requests_id_seq'::regclass);


--
-- Name: cancelation_endpoints id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cancelation_endpoints ALTER COLUMN id SET DEFAULT nextval('public.cancelation_endpoints_id_seq'::regclass);


--
-- Name: card_tokens id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_tokens ALTER COLUMN id SET DEFAULT nextval('public.card_tokens_id_seq'::regclass);


--
-- Name: cart_item_discounts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_item_discounts ALTER COLUMN id SET DEFAULT nextval('public.cart_item_discounts_id_seq'::regclass);


--
-- Name: cielo_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cielo_credentials ALTER COLUMN id SET DEFAULT nextval('public.cielo_credentials_id_seq'::regclass);


--
-- Name: customer_endpoint_integrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_endpoint_integrations ALTER COLUMN id SET DEFAULT nextval('public.customer_endpoint_integrations_id_seq'::regclass);


--
-- Name: customer_sync id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync ALTER COLUMN id SET DEFAULT nextval('public.customer_sync_id_seq'::regclass);


--
-- Name: customer_sync_entries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_entries ALTER COLUMN id SET DEFAULT nextval('public.customer_sync_entries_id_seq'::regclass);


--
-- Name: customer_sync_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_mappings ALTER COLUMN id SET DEFAULT nextval('public.customer_sync_mappings_id_seq'::regclass);


--
-- Name: customers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers ALTER COLUMN id SET DEFAULT nextval('public.customers_id_seq'::regclass);


--
-- Name: discounts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts ALTER COLUMN id SET DEFAULT nextval('public.discounts_id_seq'::regclass);


--
-- Name: docks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.docks ALTER COLUMN id SET DEFAULT nextval('public.docks_id_seq'::regclass);


--
-- Name: erp_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.erp_credentials ALTER COLUMN id SET DEFAULT nextval('public.erp_credentials_id_seq'::regclass);


--
-- Name: external_payment_references id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.external_payment_references ALTER COLUMN id SET DEFAULT nextval('public.external_payment_references_id_seq'::regclass);


--
-- Name: fiscal_invoice_errors id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoice_errors ALTER COLUMN id SET DEFAULT nextval('public.fiscal_invoice_errors_id_seq'::regclass);


--
-- Name: fiscal_invoices id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices ALTER COLUMN id SET DEFAULT nextval('public.fiscal_invoices_id_seq'::regclass);


--
-- Name: fiscal_settings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_settings ALTER COLUMN id SET DEFAULT nextval('public.fiscal_settings_id_seq'::regclass);


--
-- Name: fulfillment_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items ALTER COLUMN id SET DEFAULT nextval('public.fulfillment_items_id_seq'::regclass);


--
-- Name: fulfillments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillments ALTER COLUMN id SET DEFAULT nextval('public.fulfillments_id_seq'::regclass);


--
-- Name: gift_handler_configurations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_handler_configurations ALTER COLUMN id SET DEFAULT nextval('public.gift_handler_configurations_id_seq'::regclass);


--
-- Name: gift_promo_credential id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_promo_credential ALTER COLUMN id SET DEFAULT nextval('public.gift_promo_credential_id_seq'::regclass);


--
-- Name: iglu_credit_payments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credit_payments ALTER COLUMN id SET DEFAULT nextval('public.iglu_credit_payments_id_seq'::regclass);


--
-- Name: iglu_credits id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credits ALTER COLUMN id SET DEFAULT nextval('public.iglu_credits_id_seq'::regclass);


--
-- Name: interstate_taxes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.interstate_taxes ALTER COLUMN id SET DEFAULT nextval('public.interstate_taxes_id_seq'::regclass);


--
-- Name: inventory_item_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_item_mappings ALTER COLUMN id SET DEFAULT nextval('public.inventory_item_mappings_id_seq'::regclass);


--
-- Name: inventory_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_items ALTER COLUMN id SET DEFAULT nextval('public.inventory_items_id_seq'::regclass);


--
-- Name: invoice_series id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_series ALTER COLUMN id SET DEFAULT nextval('public.invoice_series_id_seq'::regclass);


--
-- Name: invoices_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_items ALTER COLUMN id SET DEFAULT nextval('public.invoices_items_id_seq'::regclass);


--
-- Name: invoices_payments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_payments ALTER COLUMN id SET DEFAULT nextval('public.invoices_payments_id_seq'::regclass);


--
-- Name: line_item_discounts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_discounts ALTER COLUMN id SET DEFAULT nextval('public.line_item_discounts_id_seq'::regclass);


--
-- Name: line_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items ALTER COLUMN id SET DEFAULT nextval('public.line_items_id_seq'::regclass);


--
-- Name: location_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_mappings ALTER COLUMN id SET DEFAULT nextval('public.location_mappings_id_seq'::regclass);


--
-- Name: location_settings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_settings ALTER COLUMN id SET DEFAULT nextval('public.location_settings_id_seq'::regclass);


--
-- Name: location_sync id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync ALTER COLUMN id SET DEFAULT nextval('public.location_sync_id_seq'::regclass);


--
-- Name: location_sync_entries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync_entries ALTER COLUMN id SET DEFAULT nextval('public.location_sync_entries_id_seq'::regclass);


--
-- Name: location_taxes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_taxes ALTER COLUMN id SET DEFAULT nextval('public.location_taxes_id_seq'::regclass);


--
-- Name: location_users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_users ALTER COLUMN id SET DEFAULT nextval('public.location_users_id_seq'::regclass);


--
-- Name: locations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations ALTER COLUMN id SET DEFAULT nextval('public.locations_id_seq'::regclass);


--
-- Name: oban_jobs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_jobs ALTER COLUMN id SET DEFAULT nextval('public.oban_jobs_id_seq'::regclass);


--
-- Name: order_customers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_customers ALTER COLUMN id SET DEFAULT nextval('public.order_customers_id_seq'::regclass);


--
-- Name: order_errors id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_errors ALTER COLUMN id SET DEFAULT nextval('public.order_errors_id_seq'::regclass);


--
-- Name: order_settings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_settings ALTER COLUMN id SET DEFAULT nextval('public.order_settings_id_seq'::regclass);


--
-- Name: orders id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders ALTER COLUMN id SET DEFAULT nextval('public.orders_id_seq'::regclass);


--
-- Name: organization_settings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organization_settings ALTER COLUMN id SET DEFAULT nextval('public.organization_settings_id_seq'::regclass);


--
-- Name: organizations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);


--
-- Name: packing_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items ALTER COLUMN id SET DEFAULT nextval('public.packing_items_id_seq'::regclass);


--
-- Name: packings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings ALTER COLUMN id SET DEFAULT nextval('public.packings_id_seq'::regclass);


--
-- Name: pagarme_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_credentials ALTER COLUMN id SET DEFAULT nextval('public.pagarme_credentials_id_seq'::regclass);


--
-- Name: pagarme_hook_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_hook_credentials ALTER COLUMN id SET DEFAULT nextval('public.pagarme_hook_credentials_id_seq'::regclass);


--
-- Name: payments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments ALTER COLUMN id SET DEFAULT nextval('public.payments_id_seq'::regclass);


--
-- Name: product_sync id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync ALTER COLUMN id SET DEFAULT nextval('public.product_sync_id_seq'::regclass);


--
-- Name: product_sync_configurations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_configurations ALTER COLUMN id SET DEFAULT nextval('public.product_sync_configurations_id_seq'::regclass);


--
-- Name: product_sync_entries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_entries ALTER COLUMN id SET DEFAULT nextval('public.product_sync_entries_id_seq'::regclass);


--
-- Name: product_sync_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_mappings ALTER COLUMN id SET DEFAULT nextval('public.product_sync_mappings_id_seq'::regclass);


--
-- Name: product_taxes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_taxes ALTER COLUMN id SET DEFAULT nextval('public.product_taxes_id_seq'::regclass);


--
-- Name: product_variant_mappings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_mappings ALTER COLUMN id SET DEFAULT nextval('public.product_variant_mappings_id_seq'::regclass);


--
-- Name: product_variants id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variants ALTER COLUMN id SET DEFAULT nextval('public.product_variants_id_seq'::regclass);


--
-- Name: products id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products ALTER COLUMN id SET DEFAULT nextval('public.products_id_seq'::regclass);


--
-- Name: reverse_fulfillment_line_items id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillment_line_items ALTER COLUMN id SET DEFAULT nextval('public.reverse_fulfillment_line_items_id_seq'::regclass);


--
-- Name: shipping_settings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_settings ALTER COLUMN id SET DEFAULT nextval('public.shipping_settings_id_seq'::regclass);


--
-- Name: shopify_apps id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_apps ALTER COLUMN id SET DEFAULT nextval('public.shopify_apps_id_seq'::regclass);


--
-- Name: shopify_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_credentials ALTER COLUMN id SET DEFAULT nextval('public.shopify_credentials_id_seq'::regclass);


--
-- Name: shopify_order_additional_infos id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_order_additional_infos ALTER COLUMN id SET DEFAULT nextval('public.shopify_order_additional_infos_id_seq'::regclass);


--
-- Name: shopify_storefront_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_storefront_credentials ALTER COLUMN id SET DEFAULT nextval('public.shopify_storefront_credentials_id_seq'::regclass);


--
-- Name: staff_roles id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staff_roles ALTER COLUMN id SET DEFAULT nextval('public.staff_roles_id_seq'::regclass);


--
-- Name: staffs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs ALTER COLUMN id SET DEFAULT nextval('public.staffs_id_seq'::regclass);


--
-- Name: staffs_locations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs_locations ALTER COLUMN id SET DEFAULT nextval('public.staffs_locations_id_seq'::regclass);


--
-- Name: subscriptions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions ALTER COLUMN id SET DEFAULT nextval('public.subscriptions_id_seq'::regclass);


--
-- Name: transaction_customers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transaction_customers ALTER COLUMN id SET DEFAULT nextval('public.transaction_customers_id_seq'::regclass);


--
-- Name: transactions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions ALTER COLUMN id SET DEFAULT nextval('public.transactions_id_seq'::regclass);


--
-- Name: upload_entries id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.upload_entries ALTER COLUMN id SET DEFAULT nextval('public.upload_entries_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: vinco_credential id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vinco_credential ALTER COLUMN id SET DEFAULT nextval('public.vinco_credential_id_seq'::regclass);


--
-- Name: vtex_credentials id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vtex_credentials ALTER COLUMN id SET DEFAULT nextval('public.vtex_credentials_id_seq'::regclass);


--
-- Name: oban_jobs non_negative_priority; Type: CHECK CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE events.oban_jobs
    ADD CONSTRAINT non_negative_priority CHECK ((priority >= 0)) NOT VALID;


--
-- Name: oban_crons oban_crons_pkey; Type: CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_crons
    ADD CONSTRAINT oban_crons_pkey PRIMARY KEY (name);


--
-- Name: oban_jobs oban_jobs_pkey; Type: CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_jobs
    ADD CONSTRAINT oban_jobs_pkey PRIMARY KEY (id);


--
-- Name: oban_peers oban_peers_pkey; Type: CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_peers
    ADD CONSTRAINT oban_peers_pkey PRIMARY KEY (name);


--
-- Name: oban_producers oban_producers_pkey; Type: CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_producers
    ADD CONSTRAINT oban_producers_pkey PRIMARY KEY (uuid);


--
-- Name: oban_queues oban_queues_pkey; Type: CONSTRAINT; Schema: events; Owner: -
--

ALTER TABLE ONLY events.oban_queues
    ADD CONSTRAINT oban_queues_pkey PRIMARY KEY (name);


--
-- Name: addon_mappings addon_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addon_mappings
    ADD CONSTRAINT addon_mappings_pkey PRIMARY KEY (id);


--
-- Name: addons addons_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addons
    ADD CONSTRAINT addons_pkey PRIMARY KEY (id);


--
-- Name: addresses addresses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_pkey PRIMARY KEY (id);


--
-- Name: api_tokens api_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens
    ADD CONSTRAINT api_tokens_pkey PRIMARY KEY (id);


--
-- Name: authorization_requests authorization_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authorization_requests
    ADD CONSTRAINT authorization_requests_pkey PRIMARY KEY (id);


--
-- Name: cancelation_endpoints cancelation_endpoints_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cancelation_endpoints
    ADD CONSTRAINT cancelation_endpoints_pkey PRIMARY KEY (id);


--
-- Name: card_tokens card_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_tokens
    ADD CONSTRAINT card_tokens_pkey PRIMARY KEY (id);


--
-- Name: cart_addons cart_addons_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_addons
    ADD CONSTRAINT cart_addons_pkey PRIMARY KEY (id);


--
-- Name: cart_item_discounts cart_item_discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_item_discounts
    ADD CONSTRAINT cart_item_discounts_pkey PRIMARY KEY (id);


--
-- Name: cart_items cart_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT cart_items_pkey PRIMARY KEY (id);


--
-- Name: carts carts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_pkey PRIMARY KEY (id);


--
-- Name: cielo_credentials cielo_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cielo_credentials
    ADD CONSTRAINT cielo_credentials_pkey PRIMARY KEY (id);


--
-- Name: cities_datas cities_datas_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cities_datas
    ADD CONSTRAINT cities_datas_pkey PRIMARY KEY (ibge_id);


--
-- Name: customer_endpoint_integrations customer_endpoint_integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_endpoint_integrations
    ADD CONSTRAINT customer_endpoint_integrations_pkey PRIMARY KEY (id);


--
-- Name: customer_sync_entries customer_sync_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_entries
    ADD CONSTRAINT customer_sync_entries_pkey PRIMARY KEY (id);


--
-- Name: customer_sync_mappings customer_sync_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_mappings
    ADD CONSTRAINT customer_sync_mappings_pkey PRIMARY KEY (id);


--
-- Name: customer_sync customer_sync_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync
    ADD CONSTRAINT customer_sync_pkey PRIMARY KEY (id);


--
-- Name: customers customers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_pkey PRIMARY KEY (id);


--
-- Name: delivery_groups delivery_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.delivery_groups
    ADD CONSTRAINT delivery_groups_pkey PRIMARY KEY (id);


--
-- Name: discounts discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_pkey PRIMARY KEY (id);


--
-- Name: docks docks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.docks
    ADD CONSTRAINT docks_pkey PRIMARY KEY (id);


--
-- Name: erp_credentials erp_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.erp_credentials
    ADD CONSTRAINT erp_credentials_pkey PRIMARY KEY (id);


--
-- Name: external_payment_references external_payment_references_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.external_payment_references
    ADD CONSTRAINT external_payment_references_pkey PRIMARY KEY (id);


--
-- Name: fiscal_invoice_errors fiscal_invoice_errors_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoice_errors
    ADD CONSTRAINT fiscal_invoice_errors_pkey PRIMARY KEY (id);


--
-- Name: fiscal_invoices fiscal_invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_pkey PRIMARY KEY (id);


--
-- Name: fiscal_settings fiscal_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_settings
    ADD CONSTRAINT fiscal_settings_pkey PRIMARY KEY (id);


--
-- Name: fulfillment_items fulfillment_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items
    ADD CONSTRAINT fulfillment_items_pkey PRIMARY KEY (id);


--
-- Name: fulfillments fulfillments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillments
    ADD CONSTRAINT fulfillments_pkey PRIMARY KEY (id);


--
-- Name: gift_handler_configurations gift_handler_configurations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_handler_configurations
    ADD CONSTRAINT gift_handler_configurations_pkey PRIMARY KEY (id);


--
-- Name: gift_promo_credential gift_promo_credential_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_promo_credential
    ADD CONSTRAINT gift_promo_credential_pkey PRIMARY KEY (id);


--
-- Name: iglu_credit_payments iglu_credit_payments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credit_payments
    ADD CONSTRAINT iglu_credit_payments_pkey PRIMARY KEY (id);


--
-- Name: iglu_credits iglu_credits_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credits
    ADD CONSTRAINT iglu_credits_pkey PRIMARY KEY (id);


--
-- Name: interstate_taxes interstate_taxes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.interstate_taxes
    ADD CONSTRAINT interstate_taxes_pkey PRIMARY KEY (id);


--
-- Name: inventory_item_mappings inventory_item_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_item_mappings
    ADD CONSTRAINT inventory_item_mappings_pkey PRIMARY KEY (id);


--
-- Name: inventory_items inventory_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_pkey PRIMARY KEY (id);


--
-- Name: invoice_series invoice_series_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_series
    ADD CONSTRAINT invoice_series_pkey PRIMARY KEY (id);


--
-- Name: invoices_items invoices_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_items
    ADD CONSTRAINT invoices_items_pkey PRIMARY KEY (id);


--
-- Name: invoices_payments invoices_payments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_payments
    ADD CONSTRAINT invoices_payments_pkey PRIMARY KEY (id);


--
-- Name: line_item_discounts line_item_discounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_discounts
    ADD CONSTRAINT line_item_discounts_pkey PRIMARY KEY (id);


--
-- Name: line_items line_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_pkey PRIMARY KEY (id);


--
-- Name: line_items_return_summary line_items_return_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items_return_summary
    ADD CONSTRAINT line_items_return_summary_pkey PRIMARY KEY (line_item_id);


--
-- Name: location_mappings location_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_mappings
    ADD CONSTRAINT location_mappings_pkey PRIMARY KEY (id);


--
-- Name: location_settings location_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_settings
    ADD CONSTRAINT location_settings_pkey PRIMARY KEY (id);


--
-- Name: location_sync_entries location_sync_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync_entries
    ADD CONSTRAINT location_sync_entries_pkey PRIMARY KEY (id);


--
-- Name: location_sync location_sync_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync
    ADD CONSTRAINT location_sync_pkey PRIMARY KEY (id);


--
-- Name: location_taxes location_taxes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_taxes
    ADD CONSTRAINT location_taxes_pkey PRIMARY KEY (id);


--
-- Name: location_users location_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_users
    ADD CONSTRAINT location_users_pkey PRIMARY KEY (id);


--
-- Name: locations locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_pkey PRIMARY KEY (id);


--
-- Name: oban_jobs non_negative_priority; Type: CHECK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE public.oban_jobs
    ADD CONSTRAINT non_negative_priority CHECK ((priority >= 0)) NOT VALID;


--
-- Name: oban_crons oban_crons_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_crons
    ADD CONSTRAINT oban_crons_pkey PRIMARY KEY (name);


--
-- Name: oban_jobs oban_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_jobs
    ADD CONSTRAINT oban_jobs_pkey PRIMARY KEY (id);


--
-- Name: oban_peers oban_peers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_peers
    ADD CONSTRAINT oban_peers_pkey PRIMARY KEY (name);


--
-- Name: oban_producers oban_producers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_producers
    ADD CONSTRAINT oban_producers_pkey PRIMARY KEY (uuid);


--
-- Name: oban_queues oban_queues_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.oban_queues
    ADD CONSTRAINT oban_queues_pkey PRIMARY KEY (name);


--
-- Name: order_customers order_customers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_customers
    ADD CONSTRAINT order_customers_pkey PRIMARY KEY (id);


--
-- Name: order_errors order_errors_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_errors
    ADD CONSTRAINT order_errors_pkey PRIMARY KEY (id);


--
-- Name: order_settings order_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_settings
    ADD CONSTRAINT order_settings_pkey PRIMARY KEY (id);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: organization_settings organization_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organization_settings
    ADD CONSTRAINT organization_settings_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: packing_items packing_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items
    ADD CONSTRAINT packing_items_pkey PRIMARY KEY (id);


--
-- Name: packings packings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_pkey PRIMARY KEY (id);


--
-- Name: pagarme_credentials pagarme_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_credentials
    ADD CONSTRAINT pagarme_credentials_pkey PRIMARY KEY (id);


--
-- Name: pagarme_hook_credentials pagarme_hook_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_hook_credentials
    ADD CONSTRAINT pagarme_hook_credentials_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: product_sync_configurations product_sync_configurations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_configurations
    ADD CONSTRAINT product_sync_configurations_pkey PRIMARY KEY (id);


--
-- Name: product_sync_entries product_sync_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_entries
    ADD CONSTRAINT product_sync_entries_pkey PRIMARY KEY (id);


--
-- Name: product_sync_mappings product_sync_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_mappings
    ADD CONSTRAINT product_sync_mappings_pkey PRIMARY KEY (id);


--
-- Name: product_sync product_sync_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync
    ADD CONSTRAINT product_sync_pkey PRIMARY KEY (id);


--
-- Name: product_taxes product_taxes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_taxes
    ADD CONSTRAINT product_taxes_pkey PRIMARY KEY (id);


--
-- Name: product_variant_mappings product_variant_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_mappings
    ADD CONSTRAINT product_variant_mappings_pkey PRIMARY KEY (id);


--
-- Name: product_variants product_variants_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variants
    ADD CONSTRAINT product_variants_pkey PRIMARY KEY (id);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);


--
-- Name: reverse_fulfillment_line_items reverse_fulfillment_line_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillment_line_items
    ADD CONSTRAINT reverse_fulfillment_line_items_pkey PRIMARY KEY (id);


--
-- Name: reverse_fulfillments reverse_fulfillments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillments
    ADD CONSTRAINT reverse_fulfillments_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: shipping_settings shipping_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_settings
    ADD CONSTRAINT shipping_settings_pkey PRIMARY KEY (id);


--
-- Name: shopify_apps shopify_apps_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_apps
    ADD CONSTRAINT shopify_apps_pkey PRIMARY KEY (id);


--
-- Name: shopify_credentials shopify_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_credentials
    ADD CONSTRAINT shopify_credentials_pkey PRIMARY KEY (id);


--
-- Name: shopify_order_additional_infos shopify_order_additional_infos_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_order_additional_infos
    ADD CONSTRAINT shopify_order_additional_infos_pkey PRIMARY KEY (id);


--
-- Name: shopify_storefront_credentials shopify_storefront_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_storefront_credentials
    ADD CONSTRAINT shopify_storefront_credentials_pkey PRIMARY KEY (id);


--
-- Name: staff_roles staff_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staff_roles
    ADD CONSTRAINT staff_roles_pkey PRIMARY KEY (id);


--
-- Name: staffs_locations staffs_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs_locations
    ADD CONSTRAINT staffs_locations_pkey PRIMARY KEY (id);


--
-- Name: staffs staffs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs
    ADD CONSTRAINT staffs_pkey PRIMARY KEY (id);


--
-- Name: subscriptions subscriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_pkey PRIMARY KEY (id);


--
-- Name: transaction_customers transaction_customers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transaction_customers
    ADD CONSTRAINT transaction_customers_pkey PRIMARY KEY (id);


--
-- Name: transactions transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_pkey PRIMARY KEY (id);


--
-- Name: upload_entries upload_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.upload_entries
    ADD CONSTRAINT upload_entries_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: vinco_credential vinco_credential_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vinco_credential
    ADD CONSTRAINT vinco_credential_pkey PRIMARY KEY (id);


--
-- Name: vtex_credentials vtex_credentials_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vtex_credentials
    ADD CONSTRAINT vtex_credentials_pkey PRIMARY KEY (id);


--
-- Name: oban_jobs_args_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_args_index ON events.oban_jobs USING gin (args);


--
-- Name: oban_jobs_batch_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_batch_index ON events.oban_jobs USING btree (state, ((meta ->> 'batch_id'::text)), ((meta ->> 'callback'::text))) WHERE (meta ? 'batch_id'::text);


--
-- Name: oban_jobs_chain_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_chain_index ON events.oban_jobs USING btree (state, ((meta ->> 'chain_id'::text)), ((meta ->> 'on_hold'::text))) WHERE (meta ? 'chain_id'::text);


--
-- Name: oban_jobs_meta_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_meta_index ON events.oban_jobs USING gin (meta);


--
-- Name: oban_jobs_state_meta_workflow_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_state_meta_workflow_index ON events.oban_jobs USING btree (state, ((meta ->> 'workflow_id'::text)), ((meta ->> 'name'::text))) WHERE (meta ? 'workflow_id'::text);


--
-- Name: oban_jobs_state_queue_priority_scheduled_at_id_index; Type: INDEX; Schema: events; Owner: -
--

CREATE INDEX oban_jobs_state_queue_priority_scheduled_at_id_index ON events.oban_jobs USING btree (state, queue, priority, scheduled_at, id);


--
-- Name: oban_jobs_unique_index; Type: INDEX; Schema: events; Owner: -
--

CREATE UNIQUE INDEX oban_jobs_unique_index ON events.oban_jobs USING btree (uniq_key) WHERE (uniq_key IS NOT NULL);


--
-- Name: addon_mappings_organization_id_addon_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX addon_mappings_organization_id_addon_id_index ON public.addon_mappings USING btree (organization_id, addon_id);


--
-- Name: addon_mappings_organization_id_external_id_source_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX addon_mappings_organization_id_external_id_source_index ON public.addon_mappings USING btree (organization_id, external_id, source);


--
-- Name: addon_mappings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX addon_mappings_organization_id_index ON public.addon_mappings USING btree (organization_id);


--
-- Name: addons_archived_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX addons_archived_at_index ON public.addons USING btree (archived_at) WHERE (archived_at IS NULL);


--
-- Name: addons_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX addons_id_organization_id_index ON public.addons USING btree (id, organization_id);


--
-- Name: addresses_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX addresses_id_organization_id_index ON public.addresses USING btree (id, organization_id);


--
-- Name: addresses_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX addresses_location_id_index ON public.addresses USING btree (location_id);


--
-- Name: api_tokens_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX api_tokens_organization_id_index ON public.api_tokens USING btree (organization_id);


--
-- Name: api_tokens_token_hash_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX api_tokens_token_hash_index ON public.api_tokens USING btree (token_hash);


--
-- Name: authorization_requests_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX authorization_requests_id_organization_id_index ON public.authorization_requests USING btree (id, organization_id);


--
-- Name: cancelation_endpoints_organization_id_active_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX cancelation_endpoints_organization_id_active_index ON public.cancelation_endpoints USING btree (organization_id, active) WHERE (active = true);


--
-- Name: cancelation_endpoints_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX cancelation_endpoints_organization_id_index ON public.cancelation_endpoints USING btree (organization_id);


--
-- Name: cart_addons_addon_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX cart_addons_addon_id_index ON public.cart_addons USING btree (addon_id);


--
-- Name: cart_addons_cart_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX cart_addons_cart_id_index ON public.cart_addons USING btree (cart_id);


--
-- Name: cart_addons_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX cart_addons_organization_id_index ON public.cart_addons USING btree (organization_id);


--
-- Name: cart_item_discounts_cart_item_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX cart_item_discounts_cart_item_id_index ON public.cart_item_discounts USING btree (cart_item_id);


--
-- Name: cart_item_discounts_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX cart_item_discounts_id_organization_id_index ON public.cart_item_discounts USING btree (id, organization_id);


--
-- Name: cart_items_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX cart_items_id_organization_id_index ON public.cart_items USING btree (id, organization_id);


--
-- Name: carts_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX carts_id_organization_id_index ON public.carts USING btree (id, organization_id);


--
-- Name: cielo_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX cielo_credentials_organization_id_index ON public.cielo_credentials USING btree (organization_id);


--
-- Name: cities_datas_name_uf_initials_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX cities_datas_name_uf_initials_index ON public.cities_datas USING btree (name, uf_initials);


--
-- Name: customer_endpoint_integrations_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX customer_endpoint_integrations_organization_id_index ON public.customer_endpoint_integrations USING btree (organization_id) WHERE (active = true);


--
-- Name: customer_sync_entries_customer_sync_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customer_sync_entries_customer_sync_id_index ON public.customer_sync_entries USING btree (customer_sync_id);


--
-- Name: customer_sync_entries_external_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customer_sync_entries_external_id_organization_id_index ON public.customer_sync_entries USING btree (external_id, organization_id);


--
-- Name: customer_sync_external_id_source_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX customer_sync_external_id_source_organization_id_index ON public.customer_sync USING btree (external_id, source, organization_id);


--
-- Name: customer_sync_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX customer_sync_id_organization_id_index ON public.customer_sync USING btree (id, organization_id);


--
-- Name: customer_sync_mappings_external_id_source_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX customer_sync_mappings_external_id_source_organization_id_index ON public.customer_sync_mappings USING btree (external_id, source, organization_id);


--
-- Name: customers_document_plain_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customers_document_plain_index ON public.customers USING btree (document_plain);


--
-- Name: customers_email_plain_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customers_email_plain_index ON public.customers USING btree (email_plain);


--
-- Name: customers_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX customers_id_organization_id_index ON public.customers USING btree (id, organization_id);


--
-- Name: customers_plain_name_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customers_plain_name_trgm_idx ON public.customers USING gin (plain_name public.gin_trgm_ops);


--
-- Name: customers_primary_phone_number_plain_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX customers_primary_phone_number_plain_index ON public.customers USING btree (primary_phone_number_plain);


--
-- Name: delivery_groups_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX delivery_groups_id_organization_id_index ON public.delivery_groups USING btree (id, organization_id);


--
-- Name: discounts_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX discounts_id_organization_id_index ON public.discounts USING btree (id, organization_id);


--
-- Name: docks_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX docks_id_organization_id_index ON public.docks USING btree (id, organization_id);


--
-- Name: docks_organization_id_location_id_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX docks_organization_id_location_id_external_id_index ON public.docks USING btree (organization_id, location_id, external_id);


--
-- Name: erp_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX erp_credentials_organization_id_index ON public.erp_credentials USING btree (organization_id);


--
-- Name: external_payment_references_payment_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX external_payment_references_payment_id_index ON public.external_payment_references USING btree (payment_id);


--
-- Name: fiscal_invoices_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fiscal_invoices_id_organization_id_index ON public.fiscal_invoices USING btree (id, organization_id);


--
-- Name: fiscal_invoices_invoice_number_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX fiscal_invoices_invoice_number_index ON public.fiscal_invoices USING btree (invoice_number);


--
-- Name: fiscal_invoices_operation_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX fiscal_invoices_operation_type_index ON public.fiscal_invoices USING btree (operation_type);


--
-- Name: fiscal_invoices_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX fiscal_invoices_reference_at_index ON public.fiscal_invoices USING btree (reference_at);


--
-- Name: fiscal_settings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fiscal_settings_organization_id_index ON public.fiscal_settings USING btree (organization_id) WHERE (location_id IS NULL);


--
-- Name: fiscal_settings_organization_id_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fiscal_settings_organization_id_location_id_index ON public.fiscal_settings USING btree (organization_id, location_id) WHERE (location_id IS NOT NULL);


--
-- Name: fulfillment_items_line_item_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fulfillment_items_line_item_id_index ON public.fulfillment_items USING btree (line_item_id);


--
-- Name: fulfillments_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fulfillments_id_organization_id_index ON public.fulfillments USING btree (id, organization_id);


--
-- Name: fulfillments_id_organization_id_order_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fulfillments_id_organization_id_order_id_index ON public.fulfillments USING btree (id, organization_id, order_id);


--
-- Name: fulfillments_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX fulfillments_reference_at_index ON public.fulfillments USING btree (reference_at);


--
-- Name: gift_handler_configurations_product_variant_id_organization_id_; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX gift_handler_configurations_product_variant_id_organization_id_ ON public.gift_handler_configurations USING btree (product_variant_id, organization_id);


--
-- Name: iglu_credit_payments_iglu_credit_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credit_payments_iglu_credit_id_index ON public.iglu_credit_payments USING btree (iglu_credit_id);


--
-- Name: iglu_credit_payments_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credit_payments_organization_id_index ON public.iglu_credit_payments USING btree (organization_id);


--
-- Name: iglu_credit_payments_payment_id_iglu_credit_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX iglu_credit_payments_payment_id_iglu_credit_id_index ON public.iglu_credit_payments USING btree (payment_id, iglu_credit_id);


--
-- Name: iglu_credit_payments_payment_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX iglu_credit_payments_payment_id_index ON public.iglu_credit_payments USING btree (payment_id);


--
-- Name: iglu_credits_customer_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credits_customer_id_organization_id_index ON public.iglu_credits USING btree (customer_id, organization_id);


--
-- Name: iglu_credits_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX iglu_credits_id_organization_id_index ON public.iglu_credits USING btree (id, organization_id);


--
-- Name: iglu_credits_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credits_organization_id_index ON public.iglu_credits USING btree (organization_id);


--
-- Name: iglu_credits_reverse_fulfillment_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX iglu_credits_reverse_fulfillment_id_index ON public.iglu_credits USING btree (reverse_fulfillment_id) WHERE ((status)::text <> 'cancelled'::text);


--
-- Name: iglu_credits_reverse_fulfillment_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credits_reverse_fulfillment_id_organization_id_index ON public.iglu_credits USING btree (reverse_fulfillment_id, organization_id);


--
-- Name: iglu_credits_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX iglu_credits_status_index ON public.iglu_credits USING btree (status);


--
-- Name: interstate_taxes_uf_origin_uf_destiny_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX interstate_taxes_uf_origin_uf_destiny_index ON public.interstate_taxes USING btree (uf_origin, uf_destiny);


--
-- Name: inventory_item_mappings_organization_id_external_id_source_inde; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX inventory_item_mappings_organization_id_external_id_source_inde ON public.inventory_item_mappings USING btree (organization_id, external_id, source);


--
-- Name: inventory_item_mappings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX inventory_item_mappings_organization_id_index ON public.inventory_item_mappings USING btree (organization_id);


--
-- Name: inventory_item_mappings_organization_id_product_variant_id_inde; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX inventory_item_mappings_organization_id_product_variant_id_inde ON public.inventory_item_mappings USING btree (organization_id, product_variant_id);


--
-- Name: inventory_items_location_id_product_variant_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX inventory_items_location_id_product_variant_id_index ON public.inventory_items USING btree (location_id, product_variant_id);


--
-- Name: invoice_series_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX invoice_series_id_organization_id_index ON public.invoice_series USING btree (id, organization_id);


--
-- Name: invoice_series_invoice_type_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoice_series_invoice_type_index ON public.invoice_series USING btree (invoice_type);


--
-- Name: invoice_series_invoice_type_invoice_env_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX invoice_series_invoice_type_invoice_env_location_id_index ON public.invoice_series USING btree (invoice_type, invoice_env, location_id) WHERE ((status)::text = 'active'::text);


--
-- Name: invoice_series_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoice_series_location_id_index ON public.invoice_series USING btree (location_id);


--
-- Name: invoice_series_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoice_series_organization_id_index ON public.invoice_series USING btree (organization_id);


--
-- Name: invoice_series_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoice_series_status_index ON public.invoice_series USING btree (status);


--
-- Name: invoices_items_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoices_items_reference_at_index ON public.invoices_items USING btree (reference_at);


--
-- Name: invoices_payments_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX invoices_payments_reference_at_index ON public.invoices_payments USING btree (reference_at);


--
-- Name: line_item_discounts_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX line_item_discounts_id_organization_id_index ON public.line_item_discounts USING btree (id, organization_id);


--
-- Name: line_item_discounts_line_item_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_item_discounts_line_item_id_index ON public.line_item_discounts USING btree (line_item_id);


--
-- Name: line_items_id_fulfillment_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX line_items_id_fulfillment_id_organization_id_index ON public.line_items USING btree (id, fulfillment_id, organization_id);


--
-- Name: line_items_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX line_items_id_organization_id_index ON public.line_items USING btree (id, organization_id);


--
-- Name: line_items_id_organization_id_order_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX line_items_id_organization_id_order_id_index ON public.line_items USING btree (id, organization_id, order_id);


--
-- Name: line_items_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_items_location_id_index ON public.line_items USING btree (location_id);


--
-- Name: line_items_order_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_items_order_id_index ON public.line_items USING btree (order_id);


--
-- Name: line_items_product_variant_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_items_product_variant_id_index ON public.line_items USING btree (product_variant_id);


--
-- Name: line_items_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_items_reference_at_index ON public.line_items USING btree (reference_at);


--
-- Name: line_items_staff_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX line_items_staff_id_index ON public.line_items USING btree (staff_id);


--
-- Name: location_mappings_organization_id_external_id_source_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_mappings_organization_id_external_id_source_index ON public.location_mappings USING btree (organization_id, external_id, source);


--
-- Name: location_mappings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX location_mappings_organization_id_index ON public.location_mappings USING btree (organization_id);


--
-- Name: location_mappings_organization_id_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX location_mappings_organization_id_location_id_index ON public.location_mappings USING btree (organization_id, location_id);


--
-- Name: location_settings_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX location_settings_location_id_index ON public.location_settings USING btree (location_id);


--
-- Name: location_settings_location_id_key_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_settings_location_id_key_index ON public.location_settings USING btree (location_id, key);


--
-- Name: location_sync_entries_external_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX location_sync_entries_external_id_organization_id_index ON public.location_sync_entries USING btree (external_id, organization_id);


--
-- Name: location_sync_entries_location_sync_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX location_sync_entries_location_sync_id_index ON public.location_sync_entries USING btree (location_sync_id);


--
-- Name: location_sync_external_id_source_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_sync_external_id_source_organization_id_index ON public.location_sync USING btree (external_id, source, organization_id);


--
-- Name: location_sync_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_sync_id_organization_id_index ON public.location_sync USING btree (id, organization_id);


--
-- Name: location_taxes_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_taxes_location_id_index ON public.location_taxes USING btree (location_id);


--
-- Name: location_users_user_id_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX location_users_user_id_location_id_index ON public.location_users USING btree (user_id, location_id);


--
-- Name: locations_archived_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX locations_archived_at_index ON public.locations USING btree (archived_at) WHERE (archived_at IS NULL);


--
-- Name: locations_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX locations_id_organization_id_index ON public.locations USING btree (id, organization_id);


--
-- Name: locations_name_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX locations_name_trgm_idx ON public.locations USING gin (name public.gin_trgm_ops);


--
-- Name: locations_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX locations_organization_id_index ON public.locations USING btree (organization_id);


--
-- Name: oban_jobs_args_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_args_index ON public.oban_jobs USING gin (args);


--
-- Name: oban_jobs_batch_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_batch_index ON public.oban_jobs USING btree (state, ((meta ->> 'batch_id'::text)), ((meta ->> 'callback'::text))) WHERE (meta ? 'batch_id'::text);


--
-- Name: oban_jobs_chain_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_chain_index ON public.oban_jobs USING btree (state, ((meta ->> 'chain_id'::text)), ((meta ->> 'on_hold'::text))) WHERE (meta ? 'chain_id'::text);


--
-- Name: oban_jobs_meta_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_meta_index ON public.oban_jobs USING gin (meta);


--
-- Name: oban_jobs_state_meta_workflow_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_state_meta_workflow_index ON public.oban_jobs USING btree (state, ((meta ->> 'workflow_id'::text)), ((meta ->> 'name'::text))) WHERE (meta ? 'workflow_id'::text);


--
-- Name: oban_jobs_state_queue_priority_scheduled_at_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX oban_jobs_state_queue_priority_scheduled_at_id_index ON public.oban_jobs USING btree (state, queue, priority, scheduled_at, id);


--
-- Name: oban_jobs_unique_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX oban_jobs_unique_index ON public.oban_jobs USING btree (uniq_key) WHERE (uniq_key IS NOT NULL);


--
-- Name: order_customers_plain_name_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX order_customers_plain_name_index ON public.order_customers USING btree (plain_name);


--
-- Name: order_settings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX order_settings_organization_id_index ON public.order_settings USING btree (organization_id);


--
-- Name: orders_customer_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX orders_customer_id_index ON public.orders USING btree (customer_id);


--
-- Name: orders_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX orders_external_id_index ON public.orders USING btree (external_id);


--
-- Name: orders_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX orders_id_organization_id_index ON public.orders USING btree (id, organization_id);


--
-- Name: orders_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX orders_reference_at_index ON public.orders USING btree (reference_at);


--
-- Name: organization_settings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX organization_settings_organization_id_index ON public.organization_settings USING btree (organization_id);


--
-- Name: organization_settings_organization_id_key_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX organization_settings_organization_id_key_index ON public.organization_settings USING btree (organization_id, key);


--
-- Name: organizations_org_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX organizations_org_id_index ON public.organizations USING btree (org_id);


--
-- Name: packings_id_fulfillment_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX packings_id_fulfillment_id_organization_id_index ON public.packings USING btree (id, fulfillment_id, organization_id);


--
-- Name: pagarme_credentials_organization_id_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX pagarme_credentials_organization_id_location_id_index ON public.pagarme_credentials USING btree (organization_id, location_id);


--
-- Name: pagarme_hook_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX pagarme_hook_credentials_organization_id_index ON public.pagarme_hook_credentials USING btree (organization_id);


--
-- Name: pagarme_hook_credentials_shop_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX pagarme_hook_credentials_shop_id_index ON public.pagarme_hook_credentials USING btree (shop_id);


--
-- Name: payments_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX payments_id_organization_id_index ON public.payments USING btree (id, organization_id);


--
-- Name: payments_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX payments_reference_at_index ON public.payments USING btree (reference_at);


--
-- Name: payments_transaction_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX payments_transaction_id_index ON public.payments USING btree (transaction_id);


--
-- Name: product_sync_configurations_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_sync_configurations_organization_id_index ON public.product_sync_configurations USING btree (organization_id);


--
-- Name: product_sync_entries_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_sync_entries_external_id_index ON public.product_sync_entries USING btree (external_id);


--
-- Name: product_sync_entries_product_sync_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_sync_entries_product_sync_id_index ON public.product_sync_entries USING btree (product_sync_id);


--
-- Name: product_sync_entries_sku_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_sync_entries_sku_index ON public.product_sync_entries USING btree (sku);


--
-- Name: product_sync_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_sync_id_organization_id_index ON public.product_sync USING btree (id, organization_id);


--
-- Name: product_sync_mappings_organization_id_source_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_sync_mappings_organization_id_source_external_id_index ON public.product_sync_mappings USING btree (organization_id, source, external_id);


--
-- Name: product_sync_mappings_organization_id_source_product_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_sync_mappings_organization_id_source_product_id_index ON public.product_sync_mappings USING btree (organization_id, source, product_id);


--
-- Name: product_sync_organization_id_source_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_sync_organization_id_source_external_id_index ON public.product_sync USING btree (organization_id, source, external_id);


--
-- Name: product_taxes_org_id_ncm_uf_sku_partial_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_taxes_org_id_ncm_uf_sku_partial_idx ON public.product_taxes USING btree (organization_id, COALESCE(ncm, ''::character varying), COALESCE(uf, ''::character varying), COALESCE(sku, ''::character varying)) WHERE ((ncm IS NOT NULL) OR (sku IS NOT NULL) OR (uf IS NOT NULL));


--
-- Name: product_taxes_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_taxes_organization_id_index ON public.product_taxes USING btree (organization_id) WHERE ((ncm IS NULL) AND (uf IS NULL) AND (sku IS NULL));


--
-- Name: product_variant_mappings_organization_id_external_id_source_ind; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_variant_mappings_organization_id_external_id_source_ind ON public.product_variant_mappings USING btree (organization_id, external_id, source);


--
-- Name: product_variant_mappings_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variant_mappings_organization_id_index ON public.product_variant_mappings USING btree (organization_id);


--
-- Name: product_variant_mappings_organization_id_product_variant_id_ind; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variant_mappings_organization_id_product_variant_id_ind ON public.product_variant_mappings USING btree (organization_id, product_variant_id);


--
-- Name: product_variant_mappings_organization_id_source_product_variant; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_variant_mappings_organization_id_source_product_variant ON public.product_variant_mappings USING btree (organization_id, source, product_variant_id);


--
-- Name: product_variants_archived_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_archived_at_index ON public.product_variants USING btree (archived_at) WHERE (archived_at IS NULL);


--
-- Name: product_variants_bar_code_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_bar_code_index ON public.product_variants USING btree (bar_code);


--
-- Name: product_variants_bar_code_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_bar_code_trgm_idx ON public.product_variants USING gin (bar_code public.gin_trgm_ops);


--
-- Name: product_variants_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_variants_id_organization_id_index ON public.product_variants USING btree (id, organization_id);


--
-- Name: product_variants_name_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_name_trgm_idx ON public.product_variants USING gin (name public.gin_trgm_ops);


--
-- Name: product_variants_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_organization_id_index ON public.product_variants USING btree (organization_id);


--
-- Name: product_variants_product_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_product_id_index ON public.product_variants USING btree (product_id);


--
-- Name: product_variants_sku_archived_at_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_sku_archived_at_organization_id_index ON public.product_variants USING btree (sku, archived_at, organization_id) WHERE (archived_at IS NULL);


--
-- Name: product_variants_sync_metadata_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_variants_sync_metadata_index ON public.product_variants USING gin (sync_metadata);


--
-- Name: products_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX products_id_organization_id_index ON public.products USING btree (id, organization_id);


--
-- Name: products_name_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX products_name_trgm_idx ON public.products USING gin (name public.gin_trgm_ops);


--
-- Name: products_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX products_organization_id_index ON public.products USING btree (organization_id);


--
-- Name: reverse_fulfillment_line_items_organization_id_line_item_id_ind; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX reverse_fulfillment_line_items_organization_id_line_item_id_ind ON public.reverse_fulfillment_line_items USING btree (organization_id, line_item_id);


--
-- Name: reverse_fulfillment_line_items_organization_id_reverse_fulfillm; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX reverse_fulfillment_line_items_organization_id_reverse_fulfillm ON public.reverse_fulfillment_line_items USING btree (organization_id, reverse_fulfillment_id);


--
-- Name: reverse_fulfillments_organization_id_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX reverse_fulfillments_organization_id_id_index ON public.reverse_fulfillments USING btree (organization_id, id);


--
-- Name: reverse_fulfillments_organization_id_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX reverse_fulfillments_organization_id_status_index ON public.reverse_fulfillments USING btree (organization_id, status);


--
-- Name: shopify_apps_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX shopify_apps_organization_id_index ON public.shopify_apps USING btree (organization_id);


--
-- Name: shopify_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX shopify_credentials_organization_id_index ON public.shopify_credentials USING btree (organization_id);


--
-- Name: shopify_credentials_shop_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX shopify_credentials_shop_index ON public.shopify_credentials USING btree (shop);


--
-- Name: shopify_order_additional_infos_order_type_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX shopify_order_additional_infos_order_type_organization_id_index ON public.shopify_order_additional_infos USING btree (order_type, organization_id);


--
-- Name: shopify_storefront_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX shopify_storefront_credentials_organization_id_index ON public.shopify_storefront_credentials USING btree (organization_id);


--
-- Name: staff_roles_staff_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX staff_roles_staff_id_organization_id_index ON public.staff_roles USING btree (staff_id, organization_id);


--
-- Name: staffs_archived_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX staffs_archived_at_index ON public.staffs USING btree (archived_at) WHERE (archived_at IS NULL);


--
-- Name: staffs_external_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX staffs_external_id_organization_id_index ON public.staffs USING btree (external_id, organization_id) WHERE (archived_at IS NULL);


--
-- Name: staffs_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX staffs_id_organization_id_index ON public.staffs USING btree (id, organization_id);


--
-- Name: staffs_name_trgm_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX staffs_name_trgm_idx ON public.staffs USING gin (name public.gin_trgm_ops);


--
-- Name: subscriptions_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX subscriptions_id_organization_id_index ON public.subscriptions USING btree (id, organization_id);


--
-- Name: transaction_customers_transaction_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX transaction_customers_transaction_id_index ON public.transaction_customers USING btree (transaction_id);


--
-- Name: transactions_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX transactions_id_organization_id_index ON public.transactions USING btree (id, organization_id);


--
-- Name: transactions_order_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX transactions_order_id_index ON public.transactions USING btree (order_id) WHERE ((status)::text <> 'canceled'::text);


--
-- Name: transactions_order_id_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX transactions_order_id_status_index ON public.transactions USING btree (order_id, status);


--
-- Name: transactions_reference_at_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX transactions_reference_at_index ON public.transactions USING btree (reference_at);


--
-- Name: upload_entries_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX upload_entries_organization_id_index ON public.upload_entries USING btree (organization_id);


--
-- Name: upload_entries_s3_key_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX upload_entries_s3_key_index ON public.upload_entries USING btree (s3_key);


--
-- Name: upload_entries_status_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX upload_entries_status_index ON public.upload_entries USING btree (status);


--
-- Name: users_id_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_id_organization_id_index ON public.users USING btree (id, organization_id);


--
-- Name: users_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX users_organization_id_index ON public.users USING btree (organization_id);


--
-- Name: users_provider_external_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_provider_external_id_index ON public.users USING btree (provider, external_id);


--
-- Name: vinco_credential_organization_id_location_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX vinco_credential_organization_id_location_id_index ON public.vinco_credential USING btree (organization_id, location_id);


--
-- Name: vtex_credentials_account_name_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX vtex_credentials_account_name_index ON public.vtex_credentials USING btree (account_name);


--
-- Name: vtex_credentials_organization_id_index; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX vtex_credentials_organization_id_index ON public.vtex_credentials USING btree (organization_id);


--
-- Name: fiscal_invoices invoce_numbers_insert_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER invoce_numbers_insert_trigger BEFORE INSERT ON public.fiscal_invoices FOR EACH ROW EXECUTE FUNCTION public.update_sequence();


--
-- Name: fiscal_invoice_errors set_attempt_number; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER set_attempt_number BEFORE INSERT ON public.fiscal_invoice_errors FOR EACH ROW EXECUTE FUNCTION public.next_attempt_number();


--
-- Name: reverse_fulfillment_line_items update_line_items_return_summary_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_line_items_return_summary_trigger AFTER INSERT OR DELETE OR UPDATE ON public.reverse_fulfillment_line_items FOR EACH ROW EXECUTE FUNCTION public.update_line_items_return_summary();


--
-- Name: addon_mappings addon_mappings_addon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addon_mappings
    ADD CONSTRAINT addon_mappings_addon_id_fkey FOREIGN KEY (addon_id, organization_id) REFERENCES public.addons(id, organization_id);


--
-- Name: addon_mappings addon_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addon_mappings
    ADD CONSTRAINT addon_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: addons addons_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addons
    ADD CONSTRAINT addons_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: addresses addresses_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: addresses addresses_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: addresses addresses_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT addresses_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: api_tokens api_tokens_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.api_tokens
    ADD CONSTRAINT api_tokens_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: authorization_requests authorization_requests_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authorization_requests
    ADD CONSTRAINT authorization_requests_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: authorization_requests authorization_requests_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authorization_requests
    ADD CONSTRAINT authorization_requests_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: authorization_requests authorization_requests_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.authorization_requests
    ADD CONSTRAINT authorization_requests_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: cancelation_endpoints cancelation_endpoints_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cancelation_endpoints
    ADD CONSTRAINT cancelation_endpoints_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: card_tokens card_tokens_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_tokens
    ADD CONSTRAINT card_tokens_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);


--
-- Name: card_tokens card_tokens_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_tokens
    ADD CONSTRAINT card_tokens_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: card_tokens card_tokens_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_tokens
    ADD CONSTRAINT card_tokens_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES public.payments(id);


--
-- Name: cart_addons cart_addons_addon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_addons
    ADD CONSTRAINT cart_addons_addon_id_fkey FOREIGN KEY (addon_id, organization_id) REFERENCES public.addons(id, organization_id) ON DELETE CASCADE;


--
-- Name: cart_addons cart_addons_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_addons
    ADD CONSTRAINT cart_addons_cart_id_fkey FOREIGN KEY (cart_id, organization_id) REFERENCES public.carts(id, organization_id) ON DELETE CASCADE;


--
-- Name: cart_addons cart_addons_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_addons
    ADD CONSTRAINT cart_addons_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: cart_item_discounts cart_item_discounts_authorization_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_item_discounts
    ADD CONSTRAINT cart_item_discounts_authorization_request_id_fkey FOREIGN KEY (authorization_request_id, organization_id) REFERENCES public.authorization_requests(id, organization_id);


--
-- Name: cart_item_discounts cart_item_discounts_cart_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_item_discounts
    ADD CONSTRAINT cart_item_discounts_cart_item_id_fkey FOREIGN KEY (cart_item_id, organization_id) REFERENCES public.cart_items(id, organization_id);


--
-- Name: cart_item_discounts cart_item_discounts_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_item_discounts
    ADD CONSTRAINT cart_item_discounts_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: cart_items cart_items_delivery_group_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT cart_items_delivery_group_id_fkey FOREIGN KEY (delivery_group_id, organization_id) REFERENCES public.delivery_groups(id, organization_id);


--
-- Name: cart_items cart_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT cart_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: cart_items cart_items_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT cart_items_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id);


--
-- Name: carts carts_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: carts carts_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: carts carts_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: carts carts_shipping_address_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_shipping_address_id_fkey FOREIGN KEY (shipping_address_id, organization_id) REFERENCES public.addresses(id, organization_id) ON DELETE SET NULL (shipping_address_id);


--
-- Name: carts carts_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.carts
    ADD CONSTRAINT carts_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: cielo_credentials cielo_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.cielo_credentials
    ADD CONSTRAINT cielo_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: customer_endpoint_integrations customer_endpoint_integrations_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_endpoint_integrations
    ADD CONSTRAINT customer_endpoint_integrations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: customer_sync_entries customer_sync_entries_customer_sync_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_entries
    ADD CONSTRAINT customer_sync_entries_customer_sync_id_fkey FOREIGN KEY (customer_sync_id, organization_id) REFERENCES public.customer_sync(id, organization_id);


--
-- Name: customer_sync_entries customer_sync_entries_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_entries
    ADD CONSTRAINT customer_sync_entries_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: customer_sync_mappings customer_sync_mappings_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_mappings
    ADD CONSTRAINT customer_sync_mappings_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: customer_sync_mappings customer_sync_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync_mappings
    ADD CONSTRAINT customer_sync_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: customer_sync customer_sync_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_sync
    ADD CONSTRAINT customer_sync_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: customers customers_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: delivery_groups delivery_groups_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.delivery_groups
    ADD CONSTRAINT delivery_groups_cart_id_fkey FOREIGN KEY (cart_id, organization_id) REFERENCES public.carts(id, organization_id);


--
-- Name: delivery_groups delivery_groups_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.delivery_groups
    ADD CONSTRAINT delivery_groups_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: discounts discounts_authorization_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_authorization_request_id_fkey FOREIGN KEY (authorization_request_id, organization_id) REFERENCES public.authorization_requests(id, organization_id);


--
-- Name: discounts discounts_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_cart_id_fkey FOREIGN KEY (cart_id, organization_id) REFERENCES public.carts(id, organization_id);


--
-- Name: discounts discounts_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id);


--
-- Name: discounts discounts_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.discounts
    ADD CONSTRAINT discounts_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: docks docks_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.docks
    ADD CONSTRAINT docks_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: docks docks_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.docks
    ADD CONSTRAINT docks_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: erp_credentials erp_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.erp_credentials
    ADD CONSTRAINT erp_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: external_payment_references external_payment_references_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.external_payment_references
    ADD CONSTRAINT external_payment_references_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: external_payment_references external_payment_references_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.external_payment_references
    ADD CONSTRAINT external_payment_references_payment_id_fkey FOREIGN KEY (payment_id, organization_id) REFERENCES public.payments(id, organization_id);


--
-- Name: fiscal_invoice_errors fiscal_invoice_errors_fiscal_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoice_errors
    ADD CONSTRAINT fiscal_invoice_errors_fiscal_invoice_id_fkey FOREIGN KEY (fiscal_invoice_id, organization_id) REFERENCES public.fiscal_invoices(id, organization_id);


--
-- Name: fiscal_invoice_errors fiscal_invoice_errors_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoice_errors
    ADD CONSTRAINT fiscal_invoice_errors_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: fiscal_invoices fiscal_invoices_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.transaction_customers(id);


--
-- Name: fiscal_invoices fiscal_invoices_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id) REFERENCES public.fulfillments(id, organization_id);


--
-- Name: fiscal_invoices fiscal_invoices_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: fiscal_invoices fiscal_invoices_reverse_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_reverse_fulfillment_id_fkey FOREIGN KEY (reverse_fulfillment_id, organization_id) REFERENCES public.reverse_fulfillments(id, organization_id);


--
-- Name: fiscal_invoices fiscal_invoices_serie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_invoices
    ADD CONSTRAINT fiscal_invoices_serie_id_fkey FOREIGN KEY (serie_id, organization_id) REFERENCES public.invoice_series(id, organization_id);


--
-- Name: fiscal_settings fiscal_settings_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_settings
    ADD CONSTRAINT fiscal_settings_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id) ON DELETE CASCADE;


--
-- Name: fiscal_settings fiscal_settings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fiscal_settings
    ADD CONSTRAINT fiscal_settings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: fulfillment_items fulfillment_items_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items
    ADD CONSTRAINT fulfillment_items_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id, order_id) REFERENCES public.fulfillments(id, organization_id, order_id) ON DELETE CASCADE;


--
-- Name: fulfillment_items fulfillment_items_line_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items
    ADD CONSTRAINT fulfillment_items_line_item_id_fkey FOREIGN KEY (line_item_id, organization_id, order_id) REFERENCES public.line_items(id, organization_id, order_id) ON DELETE CASCADE;


--
-- Name: fulfillment_items fulfillment_items_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items
    ADD CONSTRAINT fulfillment_items_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id) ON DELETE CASCADE;


--
-- Name: fulfillment_items fulfillment_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillment_items
    ADD CONSTRAINT fulfillment_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: fulfillments fulfillments_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillments
    ADD CONSTRAINT fulfillments_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id) ON DELETE CASCADE;


--
-- Name: fulfillments fulfillments_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fulfillments
    ADD CONSTRAINT fulfillments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: gift_handler_configurations gift_handler_configurations_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_handler_configurations
    ADD CONSTRAINT gift_handler_configurations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: gift_handler_configurations gift_handler_configurations_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_handler_configurations
    ADD CONSTRAINT gift_handler_configurations_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id) ON DELETE CASCADE;


--
-- Name: gift_promo_credential gift_promo_credential_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_promo_credential
    ADD CONSTRAINT gift_promo_credential_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: gift_promo_credential gift_promo_credential_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gift_promo_credential
    ADD CONSTRAINT gift_promo_credential_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: iglu_credit_payments iglu_credit_payments_iglu_credit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credit_payments
    ADD CONSTRAINT iglu_credit_payments_iglu_credit_id_fkey FOREIGN KEY (iglu_credit_id, organization_id) REFERENCES public.iglu_credits(id, organization_id);


--
-- Name: iglu_credit_payments iglu_credit_payments_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credit_payments
    ADD CONSTRAINT iglu_credit_payments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: iglu_credit_payments iglu_credit_payments_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credit_payments
    ADD CONSTRAINT iglu_credit_payments_payment_id_fkey FOREIGN KEY (payment_id, organization_id) REFERENCES public.payments(id, organization_id);


--
-- Name: iglu_credits iglu_credits_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credits
    ADD CONSTRAINT iglu_credits_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id) ON DELETE SET NULL;


--
-- Name: iglu_credits iglu_credits_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credits
    ADD CONSTRAINT iglu_credits_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: iglu_credits iglu_credits_reverse_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.iglu_credits
    ADD CONSTRAINT iglu_credits_reverse_fulfillment_id_fkey FOREIGN KEY (reverse_fulfillment_id, organization_id) REFERENCES public.reverse_fulfillments(id, organization_id) ON DELETE RESTRICT;


--
-- Name: inventory_item_mappings inventory_item_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_item_mappings
    ADD CONSTRAINT inventory_item_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: inventory_item_mappings inventory_item_mappings_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_item_mappings
    ADD CONSTRAINT inventory_item_mappings_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id);


--
-- Name: inventory_items inventory_items_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id) ON DELETE CASCADE;


--
-- Name: inventory_items inventory_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: inventory_items inventory_items_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.inventory_items
    ADD CONSTRAINT inventory_items_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id) ON DELETE CASCADE;


--
-- Name: invoice_series invoice_series_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_series
    ADD CONSTRAINT invoice_series_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: invoice_series invoice_series_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoice_series
    ADD CONSTRAINT invoice_series_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: invoices_items invoices_items_fiscal_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_items
    ADD CONSTRAINT invoices_items_fiscal_invoice_id_fkey FOREIGN KEY (fiscal_invoice_id, organization_id) REFERENCES public.fiscal_invoices(id, organization_id);


--
-- Name: invoices_items invoices_items_line_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_items
    ADD CONSTRAINT invoices_items_line_item_id_fkey FOREIGN KEY (line_item_id, organization_id) REFERENCES public.line_items(id, organization_id);


--
-- Name: invoices_items invoices_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_items
    ADD CONSTRAINT invoices_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: invoices_payments invoices_payments_fiscal_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_payments
    ADD CONSTRAINT invoices_payments_fiscal_invoice_id_fkey FOREIGN KEY (fiscal_invoice_id, organization_id) REFERENCES public.fiscal_invoices(id, organization_id);


--
-- Name: invoices_payments invoices_payments_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_payments
    ADD CONSTRAINT invoices_payments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: invoices_payments invoices_payments_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices_payments
    ADD CONSTRAINT invoices_payments_payment_id_fkey FOREIGN KEY (payment_id, organization_id) REFERENCES public.payments(id, organization_id);


--
-- Name: line_item_discounts line_item_discounts_authorization_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_discounts
    ADD CONSTRAINT line_item_discounts_authorization_request_id_fkey FOREIGN KEY (authorization_request_id, organization_id) REFERENCES public.authorization_requests(id, organization_id);


--
-- Name: line_item_discounts line_item_discounts_line_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_discounts
    ADD CONSTRAINT line_item_discounts_line_item_id_fkey FOREIGN KEY (line_item_id, organization_id) REFERENCES public.line_items(id, organization_id);


--
-- Name: line_item_discounts line_item_discounts_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_item_discounts
    ADD CONSTRAINT line_item_discounts_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: line_items line_items_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id) REFERENCES public.fulfillments(id, organization_id) ON DELETE SET NULL (fulfillment_id);


--
-- Name: line_items line_items_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: line_items line_items_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id) ON DELETE CASCADE;


--
-- Name: line_items line_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: line_items line_items_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id);


--
-- Name: line_items line_items_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.line_items
    ADD CONSTRAINT line_items_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: location_mappings location_mappings_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_mappings
    ADD CONSTRAINT location_mappings_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: location_mappings location_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_mappings
    ADD CONSTRAINT location_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: location_settings location_settings_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_settings
    ADD CONSTRAINT location_settings_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id) MATCH FULL;


--
-- Name: location_sync_entries location_sync_entries_location_sync_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync_entries
    ADD CONSTRAINT location_sync_entries_location_sync_id_fkey FOREIGN KEY (location_sync_id, organization_id) REFERENCES public.location_sync(id, organization_id);


--
-- Name: location_sync_entries location_sync_entries_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync_entries
    ADD CONSTRAINT location_sync_entries_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: location_sync location_sync_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_sync
    ADD CONSTRAINT location_sync_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: location_taxes location_taxes_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_taxes
    ADD CONSTRAINT location_taxes_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: location_taxes location_taxes_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_taxes
    ADD CONSTRAINT location_taxes_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: location_users location_users_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_users
    ADD CONSTRAINT location_users_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: location_users location_users_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_users
    ADD CONSTRAINT location_users_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: location_users location_users_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.location_users
    ADD CONSTRAINT location_users_user_id_fkey FOREIGN KEY (user_id, organization_id) REFERENCES public.users(id, organization_id);


--
-- Name: locations locations_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: order_customers order_customers_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_customers
    ADD CONSTRAINT order_customers_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: order_customers order_customers_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_customers
    ADD CONSTRAINT order_customers_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id) ON DELETE CASCADE;


--
-- Name: order_customers order_customers_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_customers
    ADD CONSTRAINT order_customers_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: order_errors order_errors_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_errors
    ADD CONSTRAINT order_errors_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id);


--
-- Name: order_errors order_errors_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_errors
    ADD CONSTRAINT order_errors_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: order_settings order_settings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.order_settings
    ADD CONSTRAINT order_settings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: orders orders_cashier_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_cashier_id_fkey FOREIGN KEY (cashier_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: orders orders_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE SET NULL;


--
-- Name: orders orders_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: orders orders_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: orders orders_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: organization_settings organization_settings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organization_settings
    ADD CONSTRAINT organization_settings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: packing_items packing_items_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items
    ADD CONSTRAINT packing_items_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id) REFERENCES public.fulfillments(id, organization_id) ON DELETE CASCADE;


--
-- Name: packing_items packing_items_line_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items
    ADD CONSTRAINT packing_items_line_item_id_fkey FOREIGN KEY (line_item_id, fulfillment_id, organization_id) REFERENCES public.line_items(id, fulfillment_id, organization_id) ON DELETE CASCADE;


--
-- Name: packing_items packing_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items
    ADD CONSTRAINT packing_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: packing_items packing_items_packing_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packing_items
    ADD CONSTRAINT packing_items_packing_id_fkey FOREIGN KEY (packing_id, fulfillment_id, organization_id) REFERENCES public.packings(id, fulfillment_id, organization_id) ON DELETE CASCADE;


--
-- Name: packings packings_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: packings packings_dock_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_dock_id_fkey FOREIGN KEY (dock_id, organization_id) REFERENCES public.docks(id, organization_id);


--
-- Name: packings packings_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id) REFERENCES public.fulfillments(id, organization_id) ON DELETE CASCADE;


--
-- Name: packings packings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: packings packings_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.packings
    ADD CONSTRAINT packings_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: pagarme_credentials pagarme_credentials_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_credentials
    ADD CONSTRAINT pagarme_credentials_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.locations(id);


--
-- Name: pagarme_credentials pagarme_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_credentials
    ADD CONSTRAINT pagarme_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pagarme_hook_credentials pagarme_hook_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pagarme_hook_credentials
    ADD CONSTRAINT pagarme_hook_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: payments payments_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: payments payments_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_transaction_id_fkey FOREIGN KEY (transaction_id, organization_id) REFERENCES public.transactions(id, organization_id);


--
-- Name: product_sync_configurations product_sync_configurations_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_configurations
    ADD CONSTRAINT product_sync_configurations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: product_sync_entries product_sync_entries_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_entries
    ADD CONSTRAINT product_sync_entries_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: product_sync_entries product_sync_entries_product_sync_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_entries
    ADD CONSTRAINT product_sync_entries_product_sync_id_fkey FOREIGN KEY (product_sync_id, organization_id) REFERENCES public.product_sync(id, organization_id);


--
-- Name: product_sync_mappings product_sync_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_mappings
    ADD CONSTRAINT product_sync_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: product_sync_mappings product_sync_mappings_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync_mappings
    ADD CONSTRAINT product_sync_mappings_product_id_fkey FOREIGN KEY (product_id, organization_id) REFERENCES public.products(id, organization_id);


--
-- Name: product_sync product_sync_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_sync
    ADD CONSTRAINT product_sync_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: product_taxes product_taxes_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_taxes
    ADD CONSTRAINT product_taxes_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: product_variant_mappings product_variant_mappings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_mappings
    ADD CONSTRAINT product_variant_mappings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: product_variant_mappings product_variant_mappings_product_variant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variant_mappings
    ADD CONSTRAINT product_variant_mappings_product_variant_id_fkey FOREIGN KEY (product_variant_id, organization_id) REFERENCES public.product_variants(id, organization_id);


--
-- Name: product_variants product_variants_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variants
    ADD CONSTRAINT product_variants_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: product_variants product_variants_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_variants
    ADD CONSTRAINT product_variants_product_id_fkey FOREIGN KEY (product_id, organization_id) REFERENCES public.products(id, organization_id) ON DELETE CASCADE;


--
-- Name: products products_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: reverse_fulfillment_line_items reverse_fulfillment_line_items_line_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillment_line_items
    ADD CONSTRAINT reverse_fulfillment_line_items_line_item_id_fkey FOREIGN KEY (line_item_id, organization_id) REFERENCES public.line_items(id, organization_id) ON DELETE CASCADE;


--
-- Name: reverse_fulfillment_line_items reverse_fulfillment_line_items_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillment_line_items
    ADD CONSTRAINT reverse_fulfillment_line_items_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: reverse_fulfillment_line_items reverse_fulfillment_line_items_reverse_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillment_line_items
    ADD CONSTRAINT reverse_fulfillment_line_items_reverse_fulfillment_id_fkey FOREIGN KEY (reverse_fulfillment_id, organization_id) REFERENCES public.reverse_fulfillments(id, organization_id) ON DELETE CASCADE;


--
-- Name: reverse_fulfillments reverse_fulfillments_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillments
    ADD CONSTRAINT reverse_fulfillments_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: reverse_fulfillments reverse_fulfillments_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillments
    ADD CONSTRAINT reverse_fulfillments_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.locations(id);


--
-- Name: reverse_fulfillments reverse_fulfillments_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillments
    ADD CONSTRAINT reverse_fulfillments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: reverse_fulfillments reverse_fulfillments_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reverse_fulfillments
    ADD CONSTRAINT reverse_fulfillments_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id);


--
-- Name: shipping_settings shipping_settings_fulfillment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_settings
    ADD CONSTRAINT shipping_settings_fulfillment_id_fkey FOREIGN KEY (fulfillment_id, organization_id) REFERENCES public.fulfillments(id, organization_id) ON DELETE CASCADE;


--
-- Name: shipping_settings shipping_settings_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shipping_settings
    ADD CONSTRAINT shipping_settings_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: shopify_apps shopify_apps_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_apps
    ADD CONSTRAINT shopify_apps_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: shopify_credentials shopify_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_credentials
    ADD CONSTRAINT shopify_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: shopify_order_additional_infos shopify_order_additional_infos_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_order_additional_infos
    ADD CONSTRAINT shopify_order_additional_infos_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: shopify_storefront_credentials shopify_storefront_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shopify_storefront_credentials
    ADD CONSTRAINT shopify_storefront_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: staff_roles staff_roles_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staff_roles
    ADD CONSTRAINT staff_roles_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: staff_roles staff_roles_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staff_roles
    ADD CONSTRAINT staff_roles_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id) ON DELETE CASCADE;


--
-- Name: staffs_locations staffs_locations_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs_locations
    ADD CONSTRAINT staffs_locations_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: staffs_locations staffs_locations_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs_locations
    ADD CONSTRAINT staffs_locations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: staffs_locations staffs_locations_staff_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs_locations
    ADD CONSTRAINT staffs_locations_staff_id_fkey FOREIGN KEY (staff_id, organization_id) REFERENCES public.staffs(id, organization_id) ON DELETE CASCADE;


--
-- Name: staffs staffs_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.staffs
    ADD CONSTRAINT staffs_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: subscriptions subscriptions_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_customer_id_fkey FOREIGN KEY (customer_id, organization_id) REFERENCES public.customers(id, organization_id);


--
-- Name: subscriptions subscriptions_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.subscriptions
    ADD CONSTRAINT subscriptions_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: transaction_customers transaction_customers_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transaction_customers
    ADD CONSTRAINT transaction_customers_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: transaction_customers transaction_customers_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transaction_customers
    ADD CONSTRAINT transaction_customers_transaction_id_fkey FOREIGN KEY (transaction_id, organization_id) REFERENCES public.transactions(id, organization_id);


--
-- Name: transactions transactions_order_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_order_id_fkey FOREIGN KEY (order_id, organization_id) REFERENCES public.orders(id, organization_id);


--
-- Name: transactions transactions_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: upload_entries upload_entries_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.upload_entries
    ADD CONSTRAINT upload_entries_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- Name: users users_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: vinco_credential vinco_credential_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vinco_credential
    ADD CONSTRAINT vinco_credential_location_id_fkey FOREIGN KEY (location_id, organization_id) REFERENCES public.locations(id, organization_id);


--
-- Name: vinco_credential vinco_credential_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vinco_credential
    ADD CONSTRAINT vinco_credential_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: vtex_credentials vtex_credentials_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vtex_credentials
    ADD CONSTRAINT vtex_credentials_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- PostgreSQL database dump complete
--

INSERT INTO public."schema_migrations" (version) VALUES (20240127040110);
INSERT INTO public."schema_migrations" (version) VALUES (20240127040157);
INSERT INTO public."schema_migrations" (version) VALUES (20240128152414);
INSERT INTO public."schema_migrations" (version) VALUES (20240130170127);
INSERT INTO public."schema_migrations" (version) VALUES (20240131125850);
INSERT INTO public."schema_migrations" (version) VALUES (20240131201137);
INSERT INTO public."schema_migrations" (version) VALUES (20240215210532);
INSERT INTO public."schema_migrations" (version) VALUES (20240215210816);
INSERT INTO public."schema_migrations" (version) VALUES (20240215210830);
INSERT INTO public."schema_migrations" (version) VALUES (20240219133844);
INSERT INTO public."schema_migrations" (version) VALUES (20240229143254);
INSERT INTO public."schema_migrations" (version) VALUES (20240229143306);
INSERT INTO public."schema_migrations" (version) VALUES (20240229143322);
INSERT INTO public."schema_migrations" (version) VALUES (20240304234831);
INSERT INTO public."schema_migrations" (version) VALUES (20240307143136);
INSERT INTO public."schema_migrations" (version) VALUES (20240307143428);
INSERT INTO public."schema_migrations" (version) VALUES (20240307173727);
INSERT INTO public."schema_migrations" (version) VALUES (20240312122032);
INSERT INTO public."schema_migrations" (version) VALUES (20240319123515);
INSERT INTO public."schema_migrations" (version) VALUES (20240322130043);
INSERT INTO public."schema_migrations" (version) VALUES (20240325215617);
INSERT INTO public."schema_migrations" (version) VALUES (20240325221735);
INSERT INTO public."schema_migrations" (version) VALUES (20240325231706);
INSERT INTO public."schema_migrations" (version) VALUES (20240326215628);
INSERT INTO public."schema_migrations" (version) VALUES (20240401195454);
INSERT INTO public."schema_migrations" (version) VALUES (20240403165342);
INSERT INTO public."schema_migrations" (version) VALUES (20240403165402);
INSERT INTO public."schema_migrations" (version) VALUES (20240404032423);
INSERT INTO public."schema_migrations" (version) VALUES (20240404035104);
INSERT INTO public."schema_migrations" (version) VALUES (20240409131731);
INSERT INTO public."schema_migrations" (version) VALUES (20240409132006);
INSERT INTO public."schema_migrations" (version) VALUES (20240409203752);
INSERT INTO public."schema_migrations" (version) VALUES (20240410184543);
INSERT INTO public."schema_migrations" (version) VALUES (20240412110354);
INSERT INTO public."schema_migrations" (version) VALUES (20240414162846);
INSERT INTO public."schema_migrations" (version) VALUES (20240414202327);
INSERT INTO public."schema_migrations" (version) VALUES (20240414214644);
INSERT INTO public."schema_migrations" (version) VALUES (20240415124022);
INSERT INTO public."schema_migrations" (version) VALUES (20240415184535);
INSERT INTO public."schema_migrations" (version) VALUES (20240415184549);
INSERT INTO public."schema_migrations" (version) VALUES (20240415184616);
INSERT INTO public."schema_migrations" (version) VALUES (20240415184641);
INSERT INTO public."schema_migrations" (version) VALUES (20240417203201);
INSERT INTO public."schema_migrations" (version) VALUES (20240418144104);
INSERT INTO public."schema_migrations" (version) VALUES (20240418144114);
INSERT INTO public."schema_migrations" (version) VALUES (20240418144205);
INSERT INTO public."schema_migrations" (version) VALUES (20240418183748);
INSERT INTO public."schema_migrations" (version) VALUES (20240418191054);
INSERT INTO public."schema_migrations" (version) VALUES (20240418230637);
INSERT INTO public."schema_migrations" (version) VALUES (20240422130955);
INSERT INTO public."schema_migrations" (version) VALUES (20240422163841);
INSERT INTO public."schema_migrations" (version) VALUES (20240423142243);
INSERT INTO public."schema_migrations" (version) VALUES (20240423163556);
INSERT INTO public."schema_migrations" (version) VALUES (20240425182812);
INSERT INTO public."schema_migrations" (version) VALUES (20240426135547);
INSERT INTO public."schema_migrations" (version) VALUES (20240426135610);
INSERT INTO public."schema_migrations" (version) VALUES (20240503103943);
INSERT INTO public."schema_migrations" (version) VALUES (20240507195316);
INSERT INTO public."schema_migrations" (version) VALUES (20240510124157);
INSERT INTO public."schema_migrations" (version) VALUES (20240510130744);
INSERT INTO public."schema_migrations" (version) VALUES (20240514115638);
INSERT INTO public."schema_migrations" (version) VALUES (20240514120447);
INSERT INTO public."schema_migrations" (version) VALUES (20240515115830);
INSERT INTO public."schema_migrations" (version) VALUES (20240517231628);
INSERT INTO public."schema_migrations" (version) VALUES (20240522110106);
INSERT INTO public."schema_migrations" (version) VALUES (20240522110958);
INSERT INTO public."schema_migrations" (version) VALUES (20240522113919);
INSERT INTO public."schema_migrations" (version) VALUES (20240523005143);
INSERT INTO public."schema_migrations" (version) VALUES (20240523005320);
INSERT INTO public."schema_migrations" (version) VALUES (20240523005716);
INSERT INTO public."schema_migrations" (version) VALUES (20240523005728);
INSERT INTO public."schema_migrations" (version) VALUES (20240523021114);
INSERT INTO public."schema_migrations" (version) VALUES (20240524150951);
INSERT INTO public."schema_migrations" (version) VALUES (20240525183138);
INSERT INTO public."schema_migrations" (version) VALUES (20240527135722);
INSERT INTO public."schema_migrations" (version) VALUES (20240529153408);
INSERT INTO public."schema_migrations" (version) VALUES (20240529192535);
INSERT INTO public."schema_migrations" (version) VALUES (20240529194201);
INSERT INTO public."schema_migrations" (version) VALUES (20240529223322);
INSERT INTO public."schema_migrations" (version) VALUES (20240530003247);
INSERT INTO public."schema_migrations" (version) VALUES (20240530125003);
INSERT INTO public."schema_migrations" (version) VALUES (20240531185913);
INSERT INTO public."schema_migrations" (version) VALUES (20240601234932);
INSERT INTO public."schema_migrations" (version) VALUES (20240603111925);
INSERT INTO public."schema_migrations" (version) VALUES (20240603190641);
INSERT INTO public."schema_migrations" (version) VALUES (20240606001401);
INSERT INTO public."schema_migrations" (version) VALUES (20240609140702);
INSERT INTO public."schema_migrations" (version) VALUES (20240609160844);
INSERT INTO public."schema_migrations" (version) VALUES (20240611133321);
INSERT INTO public."schema_migrations" (version) VALUES (20240611134204);
INSERT INTO public."schema_migrations" (version) VALUES (20240612200951);
INSERT INTO public."schema_migrations" (version) VALUES (20240618112812);
INSERT INTO public."schema_migrations" (version) VALUES (20240618113307);
INSERT INTO public."schema_migrations" (version) VALUES (20240619015912);
INSERT INTO public."schema_migrations" (version) VALUES (20240619015926);
INSERT INTO public."schema_migrations" (version) VALUES (20240619174227);
INSERT INTO public."schema_migrations" (version) VALUES (20240619191217);
INSERT INTO public."schema_migrations" (version) VALUES (20240620192517);
INSERT INTO public."schema_migrations" (version) VALUES (20240621022806);
INSERT INTO public."schema_migrations" (version) VALUES (20240621205353);
INSERT INTO public."schema_migrations" (version) VALUES (20240621211117);
INSERT INTO public."schema_migrations" (version) VALUES (20240621212548);
INSERT INTO public."schema_migrations" (version) VALUES (20240621214700);
INSERT INTO public."schema_migrations" (version) VALUES (20240625041505);
INSERT INTO public."schema_migrations" (version) VALUES (20240625042818);
INSERT INTO public."schema_migrations" (version) VALUES (20240625043915);
INSERT INTO public."schema_migrations" (version) VALUES (20240625170452);
INSERT INTO public."schema_migrations" (version) VALUES (20240625203801);
INSERT INTO public."schema_migrations" (version) VALUES (20240626123516);
INSERT INTO public."schema_migrations" (version) VALUES (20240627230625);
INSERT INTO public."schema_migrations" (version) VALUES (20240628110936);
INSERT INTO public."schema_migrations" (version) VALUES (20240701212203);
INSERT INTO public."schema_migrations" (version) VALUES (20240712141422);
INSERT INTO public."schema_migrations" (version) VALUES (20240716180938);
INSERT INTO public."schema_migrations" (version) VALUES (20240717234205);
INSERT INTO public."schema_migrations" (version) VALUES (20240722172850);
INSERT INTO public."schema_migrations" (version) VALUES (20240723224612);
INSERT INTO public."schema_migrations" (version) VALUES (20240724181542);
INSERT INTO public."schema_migrations" (version) VALUES (20240724211830);
INSERT INTO public."schema_migrations" (version) VALUES (20240726223820);
INSERT INTO public."schema_migrations" (version) VALUES (20240731234926);
INSERT INTO public."schema_migrations" (version) VALUES (20240731235551);
INSERT INTO public."schema_migrations" (version) VALUES (20240801191638);
INSERT INTO public."schema_migrations" (version) VALUES (20240802145011);
INSERT INTO public."schema_migrations" (version) VALUES (20240802212044);
INSERT INTO public."schema_migrations" (version) VALUES (20240806125806);
INSERT INTO public."schema_migrations" (version) VALUES (20240806140641);
INSERT INTO public."schema_migrations" (version) VALUES (20240806140710);
INSERT INTO public."schema_migrations" (version) VALUES (20240807165742);
INSERT INTO public."schema_migrations" (version) VALUES (20240812223748);
INSERT INTO public."schema_migrations" (version) VALUES (20240813135801);
INSERT INTO public."schema_migrations" (version) VALUES (20240814121730);
INSERT INTO public."schema_migrations" (version) VALUES (20240814121906);
INSERT INTO public."schema_migrations" (version) VALUES (20240814200946);
INSERT INTO public."schema_migrations" (version) VALUES (20240819133419);
INSERT INTO public."schema_migrations" (version) VALUES (20240820021317);
INSERT INTO public."schema_migrations" (version) VALUES (20240820181845);
INSERT INTO public."schema_migrations" (version) VALUES (20240820182222);
INSERT INTO public."schema_migrations" (version) VALUES (20240820182308);
INSERT INTO public."schema_migrations" (version) VALUES (20240821044049);
INSERT INTO public."schema_migrations" (version) VALUES (20240822163019);
INSERT INTO public."schema_migrations" (version) VALUES (20240822164432);
INSERT INTO public."schema_migrations" (version) VALUES (20240822164528);
INSERT INTO public."schema_migrations" (version) VALUES (20240822164656);
INSERT INTO public."schema_migrations" (version) VALUES (20240826175342);
INSERT INTO public."schema_migrations" (version) VALUES (20240826175609);
INSERT INTO public."schema_migrations" (version) VALUES (20240827021608);
INSERT INTO public."schema_migrations" (version) VALUES (20240827165219);
INSERT INTO public."schema_migrations" (version) VALUES (20240905170459);
INSERT INTO public."schema_migrations" (version) VALUES (20240906170437);
INSERT INTO public."schema_migrations" (version) VALUES (20240906200908);
INSERT INTO public."schema_migrations" (version) VALUES (20240912110913);
INSERT INTO public."schema_migrations" (version) VALUES (20240916120549);
INSERT INTO public."schema_migrations" (version) VALUES (20240916153200);
INSERT INTO public."schema_migrations" (version) VALUES (20240919201627);
INSERT INTO public."schema_migrations" (version) VALUES (20240925012758);
INSERT INTO public."schema_migrations" (version) VALUES (20240930013352);
INSERT INTO public."schema_migrations" (version) VALUES (20240930020405);
INSERT INTO public."schema_migrations" (version) VALUES (20241002045915);
INSERT INTO public."schema_migrations" (version) VALUES (20241029132336);
INSERT INTO public."schema_migrations" (version) VALUES (20241029154051);
INSERT INTO public."schema_migrations" (version) VALUES (20241101015255);
INSERT INTO public."schema_migrations" (version) VALUES (20241104105006);
INSERT INTO public."schema_migrations" (version) VALUES (20241104113727);
INSERT INTO public."schema_migrations" (version) VALUES (20241104123506);
INSERT INTO public."schema_migrations" (version) VALUES (20241104135110);
INSERT INTO public."schema_migrations" (version) VALUES (20241104143112);
INSERT INTO public."schema_migrations" (version) VALUES (20241104155147);
INSERT INTO public."schema_migrations" (version) VALUES (20241104161603);
INSERT INTO public."schema_migrations" (version) VALUES (20241104171418);
INSERT INTO public."schema_migrations" (version) VALUES (20241108172250);
INSERT INTO public."schema_migrations" (version) VALUES (20241109002716);
INSERT INTO public."schema_migrations" (version) VALUES (20241111030558);
INSERT INTO public."schema_migrations" (version) VALUES (20241112090515);
INSERT INTO public."schema_migrations" (version) VALUES (20241112090946);
INSERT INTO public."schema_migrations" (version) VALUES (20241114034217);
INSERT INTO public."schema_migrations" (version) VALUES (20241119182203);
INSERT INTO public."schema_migrations" (version) VALUES (20241122150337);
INSERT INTO public."schema_migrations" (version) VALUES (20241202235615);
INSERT INTO public."schema_migrations" (version) VALUES (20241203190009);
INSERT INTO public."schema_migrations" (version) VALUES (20241206232241);
INSERT INTO public."schema_migrations" (version) VALUES (20241208190307);
INSERT INTO public."schema_migrations" (version) VALUES (20241210174221);
INSERT INTO public."schema_migrations" (version) VALUES (20241218151439);
INSERT INTO public."schema_migrations" (version) VALUES (20250103124721);
INSERT INTO public."schema_migrations" (version) VALUES (20250108142849);
INSERT INTO public."schema_migrations" (version) VALUES (20250108163759);
INSERT INTO public."schema_migrations" (version) VALUES (20250108165056);
INSERT INTO public."schema_migrations" (version) VALUES (20250108175342);
INSERT INTO public."schema_migrations" (version) VALUES (20250109151043);
INSERT INTO public."schema_migrations" (version) VALUES (20250109202526);
INSERT INTO public."schema_migrations" (version) VALUES (20250109210621);
INSERT INTO public."schema_migrations" (version) VALUES (20250110004956);
INSERT INTO public."schema_migrations" (version) VALUES (20250110213240);
INSERT INTO public."schema_migrations" (version) VALUES (20250113122010);
INSERT INTO public."schema_migrations" (version) VALUES (20250114001941);
INSERT INTO public."schema_migrations" (version) VALUES (20250114003104);
INSERT INTO public."schema_migrations" (version) VALUES (20250114003109);
INSERT INTO public."schema_migrations" (version) VALUES (20250114190603);
INSERT INTO public."schema_migrations" (version) VALUES (20250117010951);
INSERT INTO public."schema_migrations" (version) VALUES (20250117230342);
INSERT INTO public."schema_migrations" (version) VALUES (20250119020847);
INSERT INTO public."schema_migrations" (version) VALUES (20250120213059);
INSERT INTO public."schema_migrations" (version) VALUES (20250122021216);
INSERT INTO public."schema_migrations" (version) VALUES (20250122021227);
INSERT INTO public."schema_migrations" (version) VALUES (20250122192528);
INSERT INTO public."schema_migrations" (version) VALUES (20250122192538);
INSERT INTO public."schema_migrations" (version) VALUES (20250126180459);
INSERT INTO public."schema_migrations" (version) VALUES (20250130132523);
INSERT INTO public."schema_migrations" (version) VALUES (20250210112855);
INSERT INTO public."schema_migrations" (version) VALUES (20250210205827);
INSERT INTO public."schema_migrations" (version) VALUES (20250210210444);
INSERT INTO public."schema_migrations" (version) VALUES (20250210211308);
INSERT INTO public."schema_migrations" (version) VALUES (20250217194439);
INSERT INTO public."schema_migrations" (version) VALUES (20250219205632);
INSERT INTO public."schema_migrations" (version) VALUES (20250219210053);
INSERT INTO public."schema_migrations" (version) VALUES (20250219210305);
INSERT INTO public."schema_migrations" (version) VALUES (20250225131311);
INSERT INTO public."schema_migrations" (version) VALUES (20250227133334);
INSERT INTO public."schema_migrations" (version) VALUES (20250227133340);
INSERT INTO public."schema_migrations" (version) VALUES (20250227133345);
INSERT INTO public."schema_migrations" (version) VALUES (20250228012647);
INSERT INTO public."schema_migrations" (version) VALUES (20250228045522);
INSERT INTO public."schema_migrations" (version) VALUES (20250301030631);
INSERT INTO public."schema_migrations" (version) VALUES (20250318104220);
INSERT INTO public."schema_migrations" (version) VALUES (20250318104229);
INSERT INTO public."schema_migrations" (version) VALUES (20250318104935);
INSERT INTO public."schema_migrations" (version) VALUES (20250318104946);
INSERT INTO public."schema_migrations" (version) VALUES (20250328190717);
INSERT INTO public."schema_migrations" (version) VALUES (20250401014234);
INSERT INTO public."schema_migrations" (version) VALUES (20250401224559);
INSERT INTO public."schema_migrations" (version) VALUES (20250401224854);
INSERT INTO public."schema_migrations" (version) VALUES (20250402123211);
INSERT INTO public."schema_migrations" (version) VALUES (20250402123245);
INSERT INTO public."schema_migrations" (version) VALUES (20250422190848);
INSERT INTO public."schema_migrations" (version) VALUES (20250425140048);
INSERT INTO public."schema_migrations" (version) VALUES (20250513005302);
INSERT INTO public."schema_migrations" (version) VALUES (20250513005306);
INSERT INTO public."schema_migrations" (version) VALUES (20250519161420);
