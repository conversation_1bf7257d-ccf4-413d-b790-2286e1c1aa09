#!/bin/sh

if [ -n "$TAILSCALE_AUTHKEY" ]; then
    echo "Starting Tailscale"
    mkdir -p /tmp/tailscale
    # Start Tailscale
    nohup /app/tailscaled --tun=userspace-networking --socks5-server=localhost:1055 &
    HOSTNAME=$(cat /etc/hostname)
    nohup /app/tailscale up --authkey="${TAILSCALE_AUTHKEY}" --hostname="${HOSTNAME}" &
    echo "Started Tailscale"

    # Wait for Tailscale SOCKS5 proxy to be ready
    echo "Waiting for Tailscale SOCKS5 proxy to be ready..."
    until nc -z localhost 1055; do
        sleep 1
        echo "Waiting for Tailscale SOCKS5 proxy to be ready..."
    done
    echo "Tailscale SOCKS5 proxy is ready."
else
    echo "TAILSCALE_AUTHKEY not set. Skipping Tailscale startup."
fi

cd -P -- "$(dirname -- "$0")"
MIGRATION=true ./rms eval Rms.Release.migrate
PHX_SERVER=true exec ./rms start
