{"family": "iglu_rms_server", "executionRoleArn": "arn:aws:iam::628877775814:role/iglu_ecs_execution_role", "taskRoleArn": "arn:aws:iam::628877775814:role/iglu_ecs_execution_role", "networkMode": "awsvpc", "containerDefinitions": [{"name": "iglu_rms_server", "image": "628877775814.dkr.ecr.sa-east-1.amazonaws.com/iglu-rms:latest", "portMappings": [{"containerPort": 4000, "hostPort": 4000}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "awslogs-ecs", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "iglu-rms"}}}, {"name": "api-server-datadog-agent", "image": "datadog/agent:latest", "cpu": 256, "memory": 512, "mountPoints": [], "environment": [{"name": "ECS_FARGATE", "value": "true"}, {"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_PROCESS_AGENT_PROCESS_COLLECTION_ENABLED", "value": "true"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "awslogs-ecs", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "iglu-rms"}}}], "pidMode": "task", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "tags": [{"key": "Environment", "value": "prod"}]}