defmodule Rms.AccountsTest do
  alias Rms.Accounts.StaffRole
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Accounts

  describe "unsafe_get_user!/1" do
    test "returns user by id" do
      user = insert(:user)
      user_id = user.id
      assert %{id: ^user_id} = Accounts.unsafe_get_user!(user_id)
    end
  end

  describe "get_user_by_provider_and_external_id/2" do
    test "returns user when found" do
      user = insert(:user, provider: "auth0", external_id: "123456")
      assert {:ok, found_user} = Accounts.get_user_by_provider_and_external_id("auth0", "123456")
      assert found_user.id == user.id
    end

    test "returns error when not found" do
      assert {:error, :not_found} =
               Accounts.get_user_by_provider_and_external_id("auth0", "invalid")
    end
  end

  describe "create_user/1" do
    test "returns user" do
      assert {:ok, %Accounts.User{}} =
               Accounts.create_user(%{
                 provider: "ecto",
                 external_id: "anything",
                 org_id: "random_org_id"
               })
    end

    test "returns user without updating" do
      user = insert(:user)

      assert {:ok, new_user} =
               Accounts.create_user(%{
                 provider: user.provider,
                 external_id: user.external_id,
                 name: "new user name",
                 org_id: user.organization.org_id
               })

      assert user.id == new_user.id
      assert user.name == new_user.name
      assert user.external_id == new_user.external_id
      assert user.provider == new_user.provider
    end
  end

  describe "get_or_create_org!/1" do
    test "returns existing organization when org_id exists" do
      org = insert(:organization, org_id: "existing-org")

      result = Accounts.get_or_create_org!(%{org_id: "existing-org"})
      assert result.id == org.id
      assert result.org_id == "existing-org"
    end

    test "creates new organization when org_id doesn't exist" do
      result = Accounts.get_or_create_org!(%{org_id: "new-org", name: "New Org"})
      assert result.org_id == "new-org"
      assert result.name == "New Org"

      # Verify it was actually saved to the database
      assert Repo.get_by(Accounts.Organization, org_id: "new-org")
    end
  end

  describe "assign_locations/2" do
    test "assign locations to user" do
      user = insert(:user)
      location = insert(:location, organization: user.organization)

      assert {:ok, _location_user} =
               Accounts.assign_location(user, location.id)

      assert %{locations: [%{id: location_id}]} = Repo.preload(user, [:locations])
      assert location_id == location.id
    end
  end

  describe "validate_staffs_organization/2" do
    test "return ok for valid staffs location" do
      user = insert(:user)

      staff1 = insert(:staff, organization: user.organization)
      staff2 = insert(:staff, organization: user.organization)

      :ok = Accounts.validate_staffs_organization(user.organization.id, [staff1.id, staff2.id])
    end

    test "return error for invalid staffs location" do
      user = insert(:user)

      staff1 = insert(:staff, organization: user.organization)
      staff2 = insert(:staff, organization: user.organization)

      {:error, :invalid_staffs} =
        Accounts.validate_staffs_organization(user.organization.id, [staff1.id, staff2.id, -2])
    end
  end

  describe "create_api_token!/1" do
    test "creates a token for a user" do
      org = insert(:organization)
      token = Accounts.create_api_token!(org)
      assert token.organization_id == org.id
      assert token.token
      assert token.token_hash
    end
  end

  describe "create_api_token/1" do
    test "creates a token for a user" do
      org = insert(:organization)
      assert {:ok, token} = Accounts.create_api_token(org)
      assert token.organization_id == org.id
      assert token.token
      assert token.token_hash
    end
  end

  describe "delete_api_token!/1" do
    test "deletes an existing API token" do
      org = insert(:organization)
      token = Accounts.create_api_token!(org)

      assert {:ok, deleted_token} = Accounts.delete_api_token!(org.id)
      assert deleted_token.id == token.id

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_api_token!(token.token)
      end
    end

    test "raises an error when trying to delete a non-existent token" do
      assert_raise Ecto.NoResultsError, fn ->
        Accounts.delete_api_token!(999)
      end
    end
  end

  describe "get_api_token!/1" do
    test "returns the api token" do
      org = insert(:organization)
      token = Accounts.create_api_token!(org)
      assert result = Accounts.get_api_token!(token.token)
      assert result.token == token.token
    end

    test "does not create a new api token" do
      assert_raise Ecto.NoResultsError, fn -> Accounts.get_api_token!("any api token") end
    end
  end

  describe "get_api_token_by_organization_id!/1" do
    test "returns the API token for a given organization_id" do
      org = insert(:organization)

      # create_api_token! returns the ApiToken record, and its .token field should be the decrypted value
      created_token_record = Accounts.create_api_token!(org)

      retrieved_token_value = Accounts.get_api_token_by_organization_id!(org.id)
      assert retrieved_token_value == created_token_record.token
      assert is_binary(retrieved_token_value)
    end

    test "raises Ecto.NoResultsError if no API token is found for the organization_id (org does not exist)" do
      # An ID that is unlikely to exist
      non_existent_org_id = -999

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_api_token_by_organization_id!(non_existent_org_id)
      end
    end

    test "raises Ecto.NoResultsError if the organization exists but has no API token" do
      # Org exists, but no token created for it
      org_without_token = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_api_token_by_organization_id!(org_without_token.id)
      end
    end
  end

  describe "paginated_locations/2" do
    test "allow searching by name" do
      org = insert(:organization)
      insert(:location, name: "Main Office", organization: org)
      insert(:location, name: "Secondary Office", organization: org)

      page = Accounts.paginated_locations(org.id, name: "Main")

      assert [%{name: "Main Office"}] = page.entries
    end
  end

  describe "list_locations/2" do
    test "lists all non-archived locations for an organization" do
      org = insert(:organization)
      archived_location = insert(:location, organization: org, archived_at: DateTime.utc_now())
      non_archived_location_one = insert(:location, organization: org, archived_at: nil)
      non_archived_location_two = insert(:location, organization: org, archived_at: nil)

      assert locations = Accounts.list_locations(org.id)
      assert length(locations) == 2
      assert Enum.all?(locations, fn location -> location.archived_at == nil end)
      locations_id = Enum.map(locations, & &1.id)
      assert non_archived_location_one.id in locations_id
      assert non_archived_location_two.id in locations_id
      refute archived_location.id in locations_id
    end

    test "does not list other organization locations" do
      org = insert(:organization)
      other_org = insert(:organization)
      insert(:location, organization: org, archived_at: nil)
      other_org_location = insert(:location, organization: other_org, archived_at: nil)

      locations = Accounts.list_locations(org.id)
      locations_id = Enum.map(locations, & &1.id)
      refute other_org_location.id in locations_id
    end

    test "lists all locations for an organization including archived when parameter is true" do
      org = insert(:organization)
      archived_location = insert(:location, organization: org, archived_at: DateTime.utc_now())
      non_archived_location = insert(:location, organization: org, archived_at: nil)

      assert locations = Accounts.list_locations(org.id, allow_archived?: true)
      assert length(locations) == 2
      assert Enum.any?(locations, fn location -> location.archived_at != nil end)
      locations_id = Enum.map(locations, & &1.id)
      assert archived_location.id in locations_id
      assert non_archived_location.id in locations_id
    end
  end

  describe "get_location!/3" do
    test "returns only non-archived location" do
      org = insert(:organization)
      archived_location = insert(:location, organization: org, archived_at: DateTime.utc_now())
      non_archived_location = insert(:location, organization: org, archived_at: nil)

      assert location = Accounts.get_location!(org.id, non_archived_location.id)
      assert location.id == non_archived_location.id
      assert location.archived_at == nil

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_location!(org.id, archived_location.id)
      end
    end

    test "returns the location including archived if allow_archived? is true" do
      org = insert(:organization)
      now = DateTime.truncate(DateTime.utc_now(), :second)
      archived_location = insert(:location, organization: org, archived_at: now)
      non_archived_location = insert(:location, organization: org, archived_at: nil)

      assert location =
               Accounts.get_location!(org.id, archived_location.id, allow_archived?: true)

      assert location.id == archived_location.id
      assert location.archived_at

      assert location =
               Accounts.get_location!(org.id, non_archived_location.id, allow_archived?: true)

      assert location.id == non_archived_location.id
      refute location.archived_at
    end

    test "returns the location given an organization_id and location_id" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      assert returned_location = Accounts.get_location!(org.id, location.id)
      assert returned_location.id == location.id
    end

    test "raises Ecto.NoResultsError when location does not exist" do
      org = insert(:organization)
      non_existent_location_id = -1

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_location!(org.id, non_existent_location_id)
      end
    end

    test "raises Ecto.NoResultsError when location does not belong to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      location = insert(:location, organization: another_org)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_location!(org.id, location.id)
      end
    end
  end

  describe "create_location/2" do
    test "creates a location associated with an organization" do
      org = insert(:organization)
      location_attrs = string_params_for(:location, name: "Main Office", cnpj: "*********")

      assert {:ok, location} = Accounts.create_location(org.id, location_attrs)
      assert location.name == location_attrs["name"]
      assert location.organization_id == org.id

      assert_enqueued(
        worker: Rms.Workers.ImportLocationWorker,
        args: %{organization_id: location.organization_id, location_id: location.id}
      )
    end
  end

  describe "update_location/2" do
    test "updates a location but does not change archived_at" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      location = insert(:location, archived_at: nil)
      updated_attrs = %{name: "Updated Location Name", archived_at: now}

      assert {:ok, location} = Accounts.update_location(location, updated_attrs)
      assert location.name == updated_attrs.name
      assert location.archived_at == nil
    end

    test "does not update organization_id for a location" do
      location = insert(:location)
      original_organization_id = location.organization_id
      updated_attrs = %{organization_id: "new_organization_id"}

      assert {:ok, location} = Accounts.update_location(location, updated_attrs)
      assert location.organization_id == original_organization_id
    end

    test "allows updating external_id for a location" do
      location = insert(:location, external_id: "old_external_id")
      updated_attrs = %{external_id: "new_external_id"}

      assert {:ok, updated_location} = Accounts.update_location(location, updated_attrs)
      assert updated_location.external_id == "new_external_id"
    end

    test "updates location with location_tax" do
      location = insert(:location) |> Rms.Repo.preload(:location_tax)

      tax_attrs = %{
        ie: "*********",
        crt: "1",
        name: "Tax Name",
        current_tax_uf: "SP"
      }

      attrs = %{
        name: "Updated Location Name",
        location_tax: tax_attrs
      }

      assert {:ok, updated_location} = Accounts.update_location(location, attrs)
      assert updated_location.name == "Updated Location Name"
      assert updated_location.location_tax.ie == "*********"
      assert updated_location.location_tax.crt == "1"
      assert updated_location.location_tax.name == "Tax Name"
      assert updated_location.location_tax.location_id == updated_location.id
    end

    test "updates existing location_tax" do
      location = insert(:location)
      tax = insert(:location_tax, location: location, organization: location.organization)
      location = Rms.Repo.preload(location, :location_tax)

      tax_attrs = %{
        ie: "*********",
        crt: "2",
        name: "Updated Tax Name",
        current_tax_uf: "RJ",
        id: tax.id
      }

      attrs = %{
        name: "Updated Location Name",
        location_tax: tax_attrs
      }

      assert {:ok, updated_location} = Accounts.update_location(location, attrs)
      assert updated_location.location_tax.id == tax.id
      assert updated_location.location_tax.ie == "*********"
      assert updated_location.location_tax.crt == "2"
      assert updated_location.location_tax.name == "Updated Tax Name"
      assert updated_location.location_tax.current_tax_uf == "RJ"
      assert updated_location.location_tax.location_id == updated_location.id
    end

    test "passing nil as location_tax does not delete it" do
      location = insert(:location)
      insert(:location_tax, location: location, organization: location.organization)
      location = Rms.Repo.preload(location, :location_tax)

      attrs = %{
        name: "Updated Location Name",
        location_tax: nil
      }

      assert {:error, changeset} = Accounts.update_location(location, attrs)
      assert "is invalid" in errors_on(changeset).location_tax
    end

    test "not passing location_tax does not delete it" do
      location = insert(:location)
      insert(:location_tax, location: location, organization: location.organization)
      location = Rms.Repo.preload(location, :location_tax)

      attrs = %{
        name: "Updated Location Name"
      }

      assert {:ok, updated_location} = Accounts.update_location(location, attrs)
      assert updated_location.name == "Updated Location Name"
      updated_location = Rms.Repo.preload(updated_location, :location_tax)
      assert updated_location.location_tax
    end
  end

  describe "archive_location/1" do
    test "archives a location by setting archived_at" do
      location = insert(:location, archived_at: nil)

      assert {:ok, archived_location} = Accounts.archive_location(location)
      assert archived_location.archived_at
      assert DateTime.compare(DateTime.utc_now(), archived_location.archived_at) != :lt
    end
  end

  describe "delete_location/1" do
    test "deletes an archived location" do
      location = insert(:location, archived_at: DateTime.utc_now())
      assert {:ok, location} = Accounts.delete_location(location)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_location!(location.organization_id, location.id)
      end
    end

    test "does not delete a non-archived location" do
      location = insert(:location, archived_at: nil)
      assert {:error, changeset} = Accounts.delete_location(location)
      assert "can only delete archived locations" in errors_on(changeset).id
      assert Accounts.get_location!(location.organization_id, location.id)
    end
  end

  describe "list_staffs/2" do
    test "lists all non-archived staffs for an organization" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)

      s11 = insert(:staff, organization: org)
      insert(:staff_location, staff: s11, location: location, organization: org)
      insert(:staff_role, staff: s11, organization: org)

      s21 = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: s21, location: location, organization: org)
      insert(:staff_role, staff: s21, organization: org)

      location_2 = insert(:location, organization: org)
      s12 = insert(:staff, organization: org)
      insert(:staff_location, staff: s12, location: location_2, organization: org)
      insert(:staff_role, staff: s12, organization: org)

      s22 = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: s22, location: location_2, organization: org)
      insert(:staff_role, staff: s22, organization: org)

      assert staffs = Accounts.list_staffs(org.id)
      assert length(staffs) == 2
      assert Enum.all?(staffs, fn staff -> staff.archived_at == nil end)
    end

    test "does not list other organization locations" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      s1 = insert(:staff, organization: org)
      insert(:staff_location, staff: s1, location: location, organization: org)
      insert(:staff_role, staff: s1, organization: org)

      org_2 = insert(:organization)
      location_2 = insert(:location, organization: org_2)
      s2 = insert(:staff, organization: org_2)
      insert(:staff_location, staff: s2, location: location_2, organization: org_2)
      insert(:staff_role, staff: s2, organization: org_2)

      staffs = Accounts.list_staffs(org.id)
      staffs_id = Enum.map(staffs, & &1.id)
      assert s1.id in staffs_id
      refute s2.id in staffs_id
    end

    test "lists all staffs for an organization including archived when parameter is true" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)
      s11 = insert(:staff, organization: org)
      insert(:staff_location, staff: s11, location: location, organization: org)
      insert(:staff_role, staff: s11, organization: org)

      s12 = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: s12, location: location, organization: org)
      insert(:staff_role, staff: s12, organization: org)

      location_2 = insert(:location, organization: org)
      s21 = insert(:staff, organization: org)
      insert(:staff_location, staff: s21, location: location_2, organization: org)
      insert(:staff_role, staff: s21, organization: org)

      s22 = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: s22, location: location_2, organization: org)
      insert(:staff_role, staff: s22, organization: org)

      assert staffs = Accounts.list_staffs(org.id, allow_archived?: true)
      assert length(staffs) == 4
      assert Enum.any?(staffs, fn staff -> staff.archived_at != nil end)
    end

    test "filters by staff role" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      s1 = insert(:staff, organization: org)
      insert(:staff_location, staff: s1, location: location, organization: org)

      insert(:staff_role,
        staff: s1,
        organization: org,
        seller: true,
        stocker: true,
        cashier: false
      )

      s2 = insert(:staff, organization: org)
      insert(:staff_location, staff: s2, location: location, organization: org)

      insert(:staff_role,
        staff: s2,
        organization: org,
        seller: true,
        stocker: false,
        cashier: false
      )

      s3 = insert(:staff, organization: org)
      insert(:staff_location, staff: s3, location: location, organization: org)

      insert(:staff_role,
        staff: s3,
        organization: org,
        seller: false,
        stocker: true,
        cashier: false
      )

      s4 = insert(:staff, organization: org)
      insert(:staff_location, staff: s4, location: location, organization: org)

      insert(:staff_role,
        staff: s4,
        organization: org,
        seller: false,
        stocker: false,
        cashier: true
      )

      staffs = Accounts.list_staffs(org.id, query_params: [roles: {:seller, true}])
      cashier_staffs = Accounts.list_staffs(org.id, query_params: [roles: {:cashier, true}])
      staffs_id = Enum.map(staffs, & &1.id)
      cashier_staffs_id = Enum.map(cashier_staffs, & &1.id)
      assert s1.id in staffs_id
      assert s2.id in staffs_id
      refute s3.id in staffs_id
      refute s4.id in staffs_id
      assert s4.id in cashier_staffs_id
    end
  end

  describe "paginated_staff/2" do
    test "search by staff_name" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff1 = insert(:staff, organization: org, name: "Ana Paula")
      staff2 = insert(:staff, organization: org, name: "Ana Carolina")
      staff3 = insert(:staff, organization: org, name: "João")

      Enum.each([staff1, staff2, staff3], fn staff ->
        insert(:staff_location, staff: staff, location: location, organization: org)
        insert(:staff_role, staff: staff, organization: org)
      end)

      search_name = "Ana"
      page = Accounts.paginated_staff(org.id, staff_name: search_name)

      assert Enum.sort(Enum.map(page.entries, & &1.name)) ==
               Enum.sort(["Ana Paula", "Ana Carolina"])
    end

    test "retrieves staff in alphabetical order by name" do
      org = insert(:organization)

      insert(:staff, organization: org, name: "Bravo")
      insert(:staff, organization: org, name: "Alpha")
      insert(:staff, organization: org, name: "Charlie")

      page = Accounts.paginated_staff(org.id)

      staff_names = Enum.map(page.entries, fn staff -> staff.name end)

      assert staff_names == Enum.sort(staff_names)
    end
  end

  describe "get_staff!/3" do
    test "returns only non-archived staff" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)

      non_archived_staff = insert(:staff, organization: org)
      insert(:staff_location, staff: non_archived_staff, location: location, organization: org)

      archived_staff = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: archived_staff, location: location, organization: org)

      assert staff = Accounts.get_staff!(org.id, non_archived_staff.id)
      assert staff.id == non_archived_staff.id
      assert staff.archived_at == nil

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_staff!(org.id, archived_staff.id)
      end
    end

    test "returns the staff including archived if allow_archived? is true" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)

      non_archived_staff = insert(:staff, organization: org)
      insert(:staff_location, staff: non_archived_staff, location: location, organization: org)

      archived_staff = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: archived_staff, location: location, organization: org)

      assert staff =
               Accounts.get_staff!(org.id, non_archived_staff.id, allow_archived?: true)

      assert staff.id == non_archived_staff.id
      assert staff.archived_at == nil

      assert staff = Accounts.get_staff!(org.id, archived_staff.id, allow_archived?: true)
      assert staff.id == archived_staff.id
      assert staff.archived_at == now
    end

    test "raises Ecto.NoResultsError when staff does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_staff!(org.id, 01)
      end
    end

    test "raises Ecto.NoResultsError when staff does not belong to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      location = insert(:location, organization: another_org)

      non_archived_staff = insert(:staff, organization: another_org)

      insert(:staff_location,
        staff: non_archived_staff,
        location: location,
        organization: another_org
      )

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_staff!(org.id, non_archived_staff.id)
      end
    end
  end

  describe "create_staff/2" do
    test "creates a staff associated with an location" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      staff_attrs = %{
        "name" => "Main Office",
        "email" => "<EMAIL>",
        "external_id" => "1",
        "staff_locations" => [
          %{
            "location_id" => location.id
          }
        ],
        "staff_role" => %{"seller" => true, "stocker" => false, "cashier" => false}
      }

      location_id = location.id
      assert {:ok, staff} = Accounts.create_staff(org.id, staff_attrs)
      assert staff.name == staff_attrs["name"]

      assert [
               %Rms.Accounts.StaffLocation{
                 location_id: ^location_id
               }
             ] = staff.staff_locations
    end

    test "creates a staff associated with a role" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      staff_attrs = %{
        "name" => "Main Office",
        "email" => "<EMAIL>",
        "external_id" => "1",
        "staff_locations" => [
          %{
            "location_id" => location.id
          }
        ],
        "staff_role" => %{"seller" => true, "stocker" => false, "cashier" => false}
      }

      assert {:ok, staff} = Accounts.create_staff(org.id, staff_attrs)
      assert staff.name == staff_attrs["name"]

      assert %StaffRole{
               seller: true,
               stocker: false
             } = staff.staff_role
    end

    test "does not create staff when location belongs to another org" do
      org = insert(:organization)

      org2 = insert(:organization)
      location2 = insert(:location, organization: org2)

      staff_attrs = %{
        "name" => "Main Office",
        "email" => "<EMAIL>",
        "external_id" => "1",
        "staff_locations" => [
          %{
            "location_id" => location2.id
          }
        ],
        "staff_role" => %{"seller" => true, "stocker" => false, "cashier" => false}
      }

      assert {:error, changeset} = Accounts.create_staff(org.id, staff_attrs)

      [
        %Ecto.Changeset{
          errors: [
            location_id:
              {"does not exist",
               [
                 constraint: :foreign,
                 constraint_name: "staffs_locations_location_id_fkey"
               ]}
          ]
        }
      ] = changeset.changes.staff_locations
    end

    test "does not create a staff when location does not exist" do
      org = insert(:organization)

      staff_attrs = %{
        "name" => "Main Office",
        "email" => "<EMAIL>",
        "external_id" => "1",
        "staff_locations" => [
          %{
            "location_id" => "1"
          }
        ],
        "staff_role" => %{"seller" => true, "stocker" => false, "cashier" => false}
      }

      assert {:error, changeset} = Accounts.create_staff(org.id, staff_attrs)
      [staff_location_changeset] = changeset.changes.staff_locations
      assert "does not exist" in errors_on(staff_location_changeset).location_id
    end
  end

  describe "update_staff/2" do
    test "does not update external_id for a staff" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      original_external_id = staff.external_id
      updated_attrs = %{extarnel_id: "new_extarnel_id"}

      assert {:ok, staff} = Accounts.update_staff(staff, updated_attrs)
      assert staff.external_id == original_external_id
    end

    test "update locations for a staff" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      new_location = insert(:location, organization: org)

      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      original_external_id = staff.external_id
      new_location_id = new_location.id

      updated_attrs = %{
        extarnel_id: "new_extarnel_id",
        staff_locations: [
          %{
            location_id: new_location.id
          }
        ]
      }

      assert {:ok, staff} = Accounts.update_staff(staff, updated_attrs)
      assert staff.external_id == original_external_id
      assert [%Rms.Accounts.StaffLocation{location_id: ^new_location_id}] = staff.staff_locations
    end

    test "does not update locations for a staff when it is from another org" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      org2 = insert(:organization)
      new_location = insert(:location, organization: org2)

      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      updated_attrs = %{
        extarnel_id: "new_extarnel_id",
        staff_locations: [
          %{
            location_id: new_location.id
          }
        ]
      }

      assert {:error, _changeset} = Accounts.update_staff(staff, updated_attrs)
    end

    test "updates a staff but does not change archived_at" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)

      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      updated_attrs = %{name: "new name", archived_at: now}

      assert {:ok, staff} = Accounts.update_staff(staff, updated_attrs)
      assert staff.name == staff.name
      assert staff.archived_at == nil
    end
  end

  describe "archive_staff/1" do
    test "archives a staff by setting archived_at" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      assert {:ok, archived_staff} = Accounts.archive_location(staff)
      assert archived_staff.archived_at
      assert DateTime.compare(DateTime.utc_now(), archived_staff.archived_at) != :lt
    end
  end

  describe "delete_staff/1" do
    test "deletes an archived staff" do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org, archived_at: now)
      insert(:staff_location, staff: staff, location: location, organization: org)

      assert {:ok, staff} = Accounts.delete_staff(staff)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_staff!(org.id, staff.id)
      end

      Accounts.get_location!(org.id, location.id)
    end

    test "does not delete a non-archived staff" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)
      insert(:staff_location, staff: staff, location: location, organization: org)

      assert {:error, changeset} = Accounts.delete_staff(staff)
      assert "can only delete archived locations" in errors_on(changeset).id
      assert Accounts.get_staff!(org.id, staff.id)
    end
  end
end
