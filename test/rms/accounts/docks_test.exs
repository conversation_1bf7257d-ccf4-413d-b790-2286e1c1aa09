defmodule Rms.DocksTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Accounts

  describe "paginated_docks/2" do
    test "allow searching by name" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:dock, name: "Main Dock", organization: org, location: location)
      insert(:dock, name: "Secondary Dock", organization: org, location: location)

      page = Rms.Accounts.paginated_docks(org.id, name: "Main")

      assert [%{name: "Main Dock"}] = page.entries
    end
  end

  describe "get_dock!/3" do
    test "returns the dock given an organization_id and dock_id" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      dock = insert(:dock, organization: org, location: location)

      assert returned_dock = Accounts.get_dock!(org.id, dock.id)
      assert returned_dock.id == dock.id
    end

    test "raises Ecto.NoResultsError when dock does not exist" do
      org = insert(:organization)

      non_existent_dock_id = -1

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_dock!(org.id, non_existent_dock_id)
      end
    end

    test "raises Ecto.NoResultsError when dock does not belong to organization" do
      org = insert(:organization)

      another_org = insert(:organization)
      another_org_location = insert(:location, organization: another_org)

      dock = insert(:dock, organization: another_org, location: another_org_location)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_dock!(org.id, dock.id)
      end
    end
  end

  describe "create_dock/2" do
    test "creates a dock associated with an organization" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      dock_attrs =
        string_params_for(:dock, name: "Main Dock", external_id: "12", location_id: location.id)

      assert {:ok, dock} = Accounts.create_dock(org.id, dock_attrs)
      assert dock.name == dock_attrs["name"]
      assert dock.organization_id == org.id
    end

    test "return an error when trying to duplate one dock to the same location" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      attrs = %{
        "name" => "Main Dock",
        "external_id" => "1",
        "location_id" => location.id,
        "organization_id" => org.id
      }

      assert {:ok, _} = Accounts.create_dock(org.id, attrs)
      assert {:error, _} = Accounts.create_dock(org.id, attrs)
    end
  end

  describe "delete_dock/1" do
    test "deletes dock" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      dock = insert(:dock, organization: org, location: location)
      assert {:ok, dock} = Accounts.delete_dock(dock)

      assert_raise Ecto.NoResultsError, fn ->
        Accounts.get_dock!(dock.organization_id, dock.id)
      end
    end
  end
end
