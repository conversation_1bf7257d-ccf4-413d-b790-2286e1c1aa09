defmodule Rms.Addresses.AddressTest do
  use Rms.DataCase

  alias Rms.Addresses.Address

  describe "changeset/2" do
    test "validates required fields" do
      attrs = %{}
      changeset = Address.changeset(%Address{}, attrs)

      assert "can't be blank" in errors_on(changeset).receiver_name
      assert "can't be blank" in errors_on(changeset).city_name
      assert "can't be blank" in errors_on(changeset).state
      assert "can't be blank" in errors_on(changeset).country_name
      assert "can't be blank" in errors_on(changeset).street
      assert "can't be blank" in errors_on(changeset).zip
    end

    test "validates zip format" do
      attrs = %{zip: "123"}
      changeset = Address.changeset(%Address{}, attrs)
      assert "must have 8 digits" in errors_on(changeset).zip

      attrs = %{zip: nil}
      changeset = Address.changeset(%Address{}, attrs)
      assert "can't be blank" in errors_on(changeset).zip

      attrs = %{zip: "12345678"}
      changeset = Address.changeset(%Address{}, attrs)
      refute changeset.errors[:zip]
    end

    test "creates a valid changeset" do
      attrs = %{
        receiver_name: "John <PERSON>",
        city_name: "New York",
        state: "NY",
        country_name: "USA",
        street: "Broadway",
        zip: "12345678"
      }

      changeset = Address.changeset(%Address{}, attrs)
      assert changeset.valid?
    end
  end
end
