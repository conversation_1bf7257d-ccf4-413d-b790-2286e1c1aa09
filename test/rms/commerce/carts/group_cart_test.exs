defmodule Rms.Commerce.Carts.GroupCartTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Carts.GroupCart
  import Rms.Factory

  describe "execute/3" do
    test "return a grouped cart and keep total values unchanged" do
      organization = insert(:organization)
      loc = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)

      pv1 =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      pv2 =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          location: loc,
          total_price: Decimal.new("100.00"),
          total_items_selling_price: Decimal.new("90.00"),
          total_items_list_price: Decimal.new("110.00"),
          total_manual_discount: Decimal.new("10.00"),
          total_delivery_price: Decimal.new("5.00")
        )

      delivery_group =
        insert(:delivery_group,
          organization: organization,
          cart: cart
        )

      cart_item1 =
        insert(:cart_item,
          product_variant: pv1,
          quantity: 2,
          organization: organization,
          delivery_group: delivery_group
        )

      cart_item2 =
        insert(:cart_item,
          product_variant: pv2,
          quantity: 2,
          organization: organization,
          delivery_group: delivery_group
        )

      group_mapping = [
        %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 1},
        %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 1},
        %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 2},
        %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 2}
      ]

      original_total_price = cart.total_price
      original_total_items_selling_price = cart.total_items_selling_price
      original_total_items_list_price = cart.total_items_list_price
      original_total_manual_discount = cart.total_manual_discount
      original_total_delivery_price = cart.total_delivery_price

      new_cart = GroupCart.execute(organization.id, cart.id, group_mapping)

      assert new_cart[:total_price] == original_total_price
      assert new_cart[:total_items_selling_price] == original_total_items_selling_price
      assert new_cart[:total_items_list_price] == original_total_items_list_price
      assert new_cart[:total_manual_discount] == original_total_manual_discount
      assert new_cart[:total_delivery_price] == original_total_delivery_price

      assert new_cart[:customer_id] == customer.id

      assert length(new_cart[:delivery_groups]) == 1

      group_1_items =
        new_cart[:delivery_groups]
        |> Enum.at(0)
        |> Map.get(:cart_items)
        |> Enum.filter(fn item -> item[:group_index] == "1" end)

      assert length(group_1_items) == 2

      group_2_items =
        new_cart[:delivery_groups]
        |> Enum.at(0)
        |> Map.get(:cart_items)
        |> Enum.filter(fn item -> item[:group_index] == "2" end)

      assert length(group_2_items) == 2

      Enum.each(group_1_items, fn item ->
        assert item[:quantity] == 1
      end)

      Enum.each(group_2_items, fn item ->
        assert item[:quantity] == 1
      end)
    end

    test "execute/3 handles multiple delivery groups" do
      organization = insert(:organization)
      loc = insert(:location, organization: organization)
      cart = insert(:cart, organization: organization, location: loc)
      delivery_group1 = insert(:delivery_group, organization: organization, cart: cart)
      delivery_group2 = insert(:delivery_group, organization: organization, cart: cart)

      pv1 =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      cart_item1 =
        insert(:cart_item,
          organization: organization,
          product_variant: pv1,
          delivery_group: delivery_group1
        )

      cart_item2 =
        insert(:cart_item,
          organization: organization,
          product_variant: pv1,
          delivery_group: delivery_group2
        )

      group_mapping = [
        %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 1},
        %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 1}
      ]

      new_cart = GroupCart.execute(organization.id, cart.id, group_mapping)

      assert length(new_cart[:delivery_groups]) == 2
    end

    test "execute/3 return a grouped cart and keep total values unchanged, validating cart items prices" do
      organization = insert(:organization)
      loc = insert(:location, organization: organization)

      pv1 =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      pv2 =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      cart = insert(:cart, organization: organization, location: loc)
      delivery_group = insert(:delivery_group, organization: organization, cart: cart)

      cart_item1 =
        insert(:cart_item,
          product_variant: pv1,
          quantity: 2,
          organization: organization,
          delivery_group: delivery_group
        )

      cart_item2 =
        insert(:cart_item,
          product_variant: pv2,
          quantity: 2,
          organization: organization,
          delivery_group: delivery_group
        )

      group_mapping = [
        %{"id" => cart_item1.id, "quantity" => 2, "group_index" => 1},
        %{"id" => cart_item2.id, "quantity" => 2, "group_index" => 1},
        %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 2},
        %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 2}
      ]

      new_cart = GroupCart.execute(organization.id, cart.id, group_mapping)

      # Assert total values remain unchanged
      assert new_cart[:total_price] == Decimal.new("0")
      assert new_cart[:total_delivery_price] == Decimal.new("0")
      assert new_cart[:total_items_selling_price] == Decimal.new("0")
      assert new_cart[:total_items_list_price] == Decimal.new("0")
      assert new_cart[:total_manual_discount] == Decimal.new("0")

      # Validate cart items prices
      Enum.each(new_cart[:delivery_groups], fn group ->
        Enum.each(group[:cart_items], fn
          %{group_index: "1"} = item ->
            assert item[:total_price] == Decimal.new("20")
            assert item[:selling_price] == Decimal.new("10")
            assert item[:list_price] == Decimal.new("10")

          %{group_index: "2"} = item ->
            assert item[:total_price] == Decimal.new("10")
            assert item[:selling_price] == Decimal.new("10")
            assert item[:list_price] == Decimal.new("10")
        end)
      end)
    end
  end
end
