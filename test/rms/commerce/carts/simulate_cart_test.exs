defmodule Rms.Commerce.Carts.SimulateCartTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Carts.SimulateCart
  import Rms.Factory

  use ExUnit.Case

  import Mox

  setup :verify_on_exit!

  ## =================================================

  alias Rms.Integrations.VTEX

  setup do
    organization = insert(:organization)

    product_variants =
      insert_list(5, :product_variant,
        organization: organization,
        product: build(:product, organization: organization)
      )

    {:ok, org: organization, product_variants: product_variants}
  end

  @vtex_cart_simulation %{
    "items" => [
      %{
        "id" => "2",
        "requestIndex" => 0,
        "quantity" => 40,
        "seller" => "1",
        "sellerChain" => [
          "1"
        ],
        "tax" => 0,
        "priceValidUntil" => "2025-05-31T19:15:40Z",
        "price" => 1500,
        "listPrice" => 9500,
        "rewardValue" => 0,
        "sellingPrice" => 1500,
        "offerings" => [],
        "priceTags" => [],
        "measurementUnit" => "un",
        "unitMultiplier" => 1.0000,
        "parentItemIndex" => nil,
        "parentAssemblyBinding" => nil,
        "availability" => "available",
        "priceDefinition" => %{
          "calculatedSellingPrice" => 1500,
          "total" => 60_000,
          "sellingPrices" => [
            %{
              "value" => 1500,
              "quantity" => 40
            }
          ],
          "reason" => nil
        },
        "priceTable" => nil
      },
      %{
        "id" => "3",
        "requestIndex" => 1,
        "quantity" => 11,
        "seller" => "1",
        "sellerChain" => [
          "1"
        ],
        "tax" => 0,
        "priceValidUntil" => "2025-05-31T19:15:40Z",
        "price" => 22_500,
        "listPrice" => 42_500,
        "rewardValue" => 0,
        "sellingPrice" => 22_500,
        "offerings" => [],
        "priceTags" => [],
        "measurementUnit" => "un",
        "unitMultiplier" => 1.0000,
        "parentItemIndex" => nil,
        "parentAssemblyBinding" => nil,
        "availability" => "available",
        "catalogProvider" =>
          "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/3",
        "priceDefinition" => %{
          "calculatedSellingPrice" => 22_500,
          "total" => 247_500,
          "sellingPrices" => [
            %{
              "value" => 22_500,
              "quantity" => 11
            }
          ],
          "reason" => nil
        },
        "priceTable" => nil
      }
    ],
    "postalCode" => "01402-000",
    "country" => "BRA",
    "logisticsInfo" => [
      %{
        "itemIndex" => 0,
        "addressId" => nil,
        "selectedSla" => nil,
        "selectedDeliveryChannel" => nil,
        "quantity" => 40,
        "shipsTo" => [
          "BRA"
        ],
        "slas" => [
          %{
            "id" => "Normal",
            "deliveryChannel" => "delivery",
            "name" => "Normal",
            "deliveryIds" => [
              %{
                "courierId" => "1",
                "warehouseId" => "16f4675",
                "dockId" => "16c7f6f",
                "courierName" => "Transportadora",
                "quantity" => 30,
                "kitItemDetails" => []
              },
              %{
                "courierId" => "1",
                "warehouseId" => "1_1",
                "dockId" => "1",
                "courierName" => "Transportadora",
                "quantity" => 10,
                "kitItemDetails" => []
              }
            ],
            "shippingEstimate" => "3bd",
            "shippingEstimateDate" => nil,
            "lockTTL" => nil,
            "availableDeliveryWindows" => [],
            "deliveryWindow" => nil,
            "price" => 3298,
            "listPrice" => 3298,
            "tax" => 0,
            "pickupStoreInfo" => %{
              "isPickupStore" => false,
              "friendlyName" => nil,
              "address" => nil,
              "additionalInfo" => nil,
              "dockId" => nil
            },
            "pickupPointId" => nil,
            "pickupDistance" => 0.0,
            "polygonName" => "",
            "transitTime" => "3bd"
          },
          %{
            "id" => "Retirada em Loja (1)",
            "deliveryChannel" => "pickup-in-point",
            "name" => "Retirada em Loja (1)",
            "deliveryIds" => [
              %{
                "courierId" => "2",
                "warehouseId" => "1e10a41",
                "dockId" => "1dd5911",
                "courierName" => "Retirada em Loja",
                "quantity" => 30,
                "kitItemDetails" => []
              },
              %{
                "courierId" => "2",
                "warehouseId" => "1c3cbfe",
                "dockId" => "18c1bdc",
                "courierName" => "Retirada em Loja",
                "quantity" => 10,
                "kitItemDetails" => []
              }
            ],
            "shippingEstimate" => "0bd",
            "shippingEstimateDate" => nil,
            "lockTTL" => nil,
            "availableDeliveryWindows" => [],
            "deliveryWindow" => nil,
            "price" => 0,
            "listPrice" => 0,
            "tax" => 0,
            "pickupStoreInfo" => %{
              "isPickupStore" => true,
              "friendlyName" => "Baw Birth Store",
              "address" => %{
                "addressType" => "pickup",
                "receiverName" => nil,
                "addressId" => "1",
                "isDisposable" => true,
                "postalCode" => "01131-000",
                "city" => "São Paulo",
                "state" => "SP",
                "country" => "BRA",
                "street" => "Rua dos Italianos",
                "number" => "998",
                "neighborhood" => "Bom Retiro",
                "complement" => "",
                "reference" => nil,
                "geoCoordinates" => [
                  -46.64538,
                  -23.52124
                ]
              },
              "additionalInfo" => "",
              "dockId" => "1dd5911"
            },
            "pickupPointId" => "1_1",
            "pickupDistance" => 6.259303569793701,
            "polygonName" => "",
            "transitTime" => "0bd"
          }
        ],
        "deliveryChannels" => [
          %{
            "id" => "pickup-in-point"
          },
          %{
            "id" => "delivery"
          }
        ]
      },
      %{
        "itemIndex" => 1,
        "addressId" => nil,
        "selectedSla" => nil,
        "selectedDeliveryChannel" => nil,
        "quantity" => 11,
        "shipsTo" => [
          "BRA"
        ],
        "slas" => [
          %{
            "id" => "Normal",
            "deliveryChannel" => "delivery",
            "name" => "Normal",
            "deliveryIds" => [
              %{
                "courierId" => "1",
                "warehouseId" => "16f4675",
                "dockId" => "16c7f6f",
                "courierName" => "Transportadora",
                "quantity" => 49,
                "kitItemDetails" => []
              }
            ],
            "shippingEstimate" => "3bd",
            "shippingEstimateDate" => nil,
            "lockTTL" => nil,
            "availableDeliveryWindows" => [],
            "deliveryWindow" => nil,
            "price" => 1482,
            "listPrice" => 1482,
            "tax" => 0,
            "pickupStoreInfo" => %{
              "isPickupStore" => false,
              "friendlyName" => nil,
              "address" => nil,
              "additionalInfo" => nil,
              "dockId" => nil
            },
            "pickupPointId" => nil,
            "pickupDistance" => 0.0,
            "polygonName" => "",
            "transitTime" => "3bd"
          },
          %{
            "id" => "Retirada em Loja (1)",
            "deliveryChannel" => "pickup-in-point",
            "name" => "Retirada em Loja (1)",
            "deliveryIds" => [
              %{
                "courierId" => "2",
                "warehouseId" => "1c3cbfe",
                "dockId" => "18c1bdc",
                "courierName" => "Retirada em Loja",
                "quantity" => 10,
                "kitItemDetails" => []
              },
              %{
                "courierId" => "2",
                "warehouseId" => "1e10a41",
                "dockId" => "1dd5911",
                "courierName" => "Retirada em Loja",
                "quantity" => 1,
                "kitItemDetails" => []
              }
            ],
            "shippingEstimate" => "0bd",
            "shippingEstimateDate" => nil,
            "lockTTL" => nil,
            "availableDeliveryWindows" => [],
            "deliveryWindow" => nil,
            "price" => 0,
            "listPrice" => 0,
            "tax" => 0,
            "pickupStoreInfo" => %{
              "isPickupStore" => true,
              "friendlyName" => "Baw Birth Store",
              "address" => %{
                "addressType" => "pickup",
                "receiverName" => nil,
                "addressId" => "1",
                "isDisposable" => true,
                "postalCode" => "01131-000",
                "city" => "São Paulo",
                "state" => "SP",
                "country" => "BRA",
                "street" => "Rua dos Italianos",
                "number" => "998",
                "neighborhood" => "Bom Retiro",
                "complement" => "",
                "reference" => nil,
                "geoCoordinates" => [
                  -46.64538,
                  -23.52124
                ]
              },
              "additionalInfo" => "",
              "dockId" => "1dd5911"
            },
            "pickupPointId" => "1_1",
            "pickupDistance" => 6.259303569793701,
            "polygonName" => "",
            "transitTime" => "0bd"
          }
        ],
        "deliveryChannels" => [
          %{
            "id" => "pickup-in-point"
          },
          %{
            "id" => "delivery"
          }
        ]
      }
    ],
    "messages" => [
      %{
        "code" => "itemQuantityNotAvailable",
        "text" =>
          "A quantidade desejada para o item .SHORTS KNIT BAW X RICK AND MORTY SHORTS KNIT BAW X RICK AND MORTY PRETO G não está disponível",
        "status" => "warning",
        "fields" => %{
          "ean" => "7909944338336"
        }
      },
      %{
        "code" => "itemQuantityNotAvailable",
        "text" =>
          "A quantidade desejada para o item .SHORTS KNIT BAW X RICK AND MORTY SHORTS KNIT BAW X RICK AND MORTY PRETO M não está disponível",
        "status" => "warning",
        "fields" => %{
          "ean" => "7909944338367"
        }
      }
    ],
    "pickupPoints" => [
      %{
        "friendlyName" => "Baw Birth Store",
        "address" => %{
          "addressType" => "pickup",
          "receiverName" => nil,
          "addressId" => "1",
          "isDisposable" => true,
          "postalCode" => "01131-000",
          "city" => "São Paulo",
          "state" => "SP",
          "country" => "BRA",
          "street" => "Rua dos Italianos",
          "number" => "998",
          "neighborhood" => "Bom Retiro",
          "complement" => "",
          "reference" => nil,
          "geoCoordinates" => [
            -46.64538,
            -23.52124
          ]
        },
        "additionalInfo" => "",
        "id" => "1_1",
        "businessHours" => []
      }
    ]
  }

  describe "fetch_delivery_options/2" do
    test "return fulfillment options for vtex", %{org: org, product_variants: pvs} do
      [p1, p2 | _] = pvs

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      insert(:vtex_credential, organization: org)

      # Set ecommerce setting
      Rms.Settings.upsert_organization_setting(org.id, "ecommerce", "vtex")

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        {:ok, @vtex_cart_simulation}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 49
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004"
      }

      assert {:ok, fulfillment_options} =
               SimulateCart.fetch_delivery_options(org.id, cart_info)

      assert fulfillment_options == %{
               "items" => [
                 %{
                   "item_index" => 0,
                   "product_variant_id" => p1.id,
                   "delivery_options" => [
                     %{
                       "id" => "Retirada em Loja (1)",
                       "name" => "Retirada em Loja (1)",
                       "delivery_type" => "pickup-in-point",
                       "price" => 0,
                       "quantity" => 40,
                       "delivery_time" => "0bd",
                       "metadata" => %{
                         "id" => "Retirada em Loja (1)",
                         "deliveryChannel" => "pickup-in-point",
                         "name" => "Retirada em Loja (1)",
                         "deliveryIds" => [
                           %{
                             "courierId" => "2",
                             "warehouseId" => "1e10a41",
                             "dockId" => "1dd5911",
                             "courierName" => "Retirada em Loja",
                             "quantity" => 30,
                             "kitItemDetails" => []
                           },
                           %{
                             "courierId" => "2",
                             "warehouseId" => "1c3cbfe",
                             "dockId" => "18c1bdc",
                             "courierName" => "Retirada em Loja",
                             "quantity" => 10,
                             "kitItemDetails" => []
                           }
                         ],
                         "shippingEstimate" => "0bd",
                         "shippingEstimateDate" => nil,
                         "lockTTL" => nil,
                         "availableDeliveryWindows" => [],
                         "deliveryWindow" => nil,
                         "price" => 0,
                         "listPrice" => 0,
                         "tax" => 0,
                         "pickupStoreInfo" => %{
                           "isPickupStore" => true,
                           "friendlyName" => "Baw Birth Store",
                           "address" => %{
                             "addressType" => "pickup",
                             "receiverName" => nil,
                             "addressId" => "1",
                             "isDisposable" => true,
                             "postalCode" => "01131-000",
                             "city" => "São Paulo",
                             "state" => "SP",
                             "country" => "BRA",
                             "street" => "Rua dos Italianos",
                             "number" => "998",
                             "neighborhood" => "Bom Retiro",
                             "complement" => "",
                             "reference" => nil,
                             "geoCoordinates" => [
                               -46.64538,
                               -23.52124
                             ]
                           },
                           "additionalInfo" => "",
                           "dockId" => "1dd5911"
                         },
                         "pickupPointId" => "1_1",
                         "pickupDistance" => 6.259303569793701,
                         "polygonName" => "",
                         "transitTime" => "0bd"
                       }
                     },
                     %{
                       "id" => "Normal",
                       "name" => "Normal",
                       "delivery_type" => "delivery",
                       "price" => 3298,
                       "quantity" => 40,
                       "delivery_time" => "3bd",
                       "metadata" => %{
                         "id" => "Normal",
                         "deliveryChannel" => "delivery",
                         "name" => "Normal",
                         "deliveryIds" => [
                           %{
                             "courierId" => "1",
                             "warehouseId" => "16f4675",
                             "dockId" => "16c7f6f",
                             "courierName" => "Transportadora",
                             "quantity" => 30,
                             "kitItemDetails" => []
                           },
                           %{
                             "courierId" => "1",
                             "warehouseId" => "1_1",
                             "dockId" => "1",
                             "courierName" => "Transportadora",
                             "quantity" => 10,
                             "kitItemDetails" => []
                           }
                         ],
                         "shippingEstimate" => "3bd",
                         "shippingEstimateDate" => nil,
                         "lockTTL" => nil,
                         "availableDeliveryWindows" => [],
                         "deliveryWindow" => nil,
                         "price" => 3298,
                         "listPrice" => 3298,
                         "tax" => 0,
                         "pickupStoreInfo" => %{
                           "isPickupStore" => false,
                           "friendlyName" => nil,
                           "address" => nil,
                           "additionalInfo" => nil,
                           "dockId" => nil
                         },
                         "pickupPointId" => nil,
                         "pickupDistance" => 0.0,
                         "polygonName" => "",
                         "transitTime" => "3bd"
                       }
                     }
                   ]
                 },
                 %{
                   "item_index" => 1,
                   "product_variant_id" => p2.id,
                   "delivery_options" => [
                     %{
                       "id" => "Normal",
                       "name" => "Normal",
                       "delivery_type" => "delivery",
                       "price" => 1482,
                       "quantity" => 49,
                       "delivery_time" => "3bd",
                       "metadata" => %{
                         "id" => "Normal",
                         "deliveryChannel" => "delivery",
                         "name" => "Normal",
                         "deliveryIds" => [
                           %{
                             "courierId" => "1",
                             "warehouseId" => "16f4675",
                             "dockId" => "16c7f6f",
                             "courierName" => "Transportadora",
                             "quantity" => 49,
                             "kitItemDetails" => []
                           }
                         ],
                         "shippingEstimate" => "3bd",
                         "shippingEstimateDate" => nil,
                         "lockTTL" => nil,
                         "availableDeliveryWindows" => [],
                         "deliveryWindow" => nil,
                         "price" => 1482,
                         "listPrice" => 1482,
                         "tax" => 0,
                         "pickupStoreInfo" => %{
                           "isPickupStore" => false,
                           "friendlyName" => nil,
                           "address" => nil,
                           "additionalInfo" => nil,
                           "dockId" => nil
                         },
                         "pickupPointId" => nil,
                         "pickupDistance" => 0.0,
                         "polygonName" => "",
                         "transitTime" => "3bd"
                       }
                     }
                   ]
                 }
               ],
               "pickup_points" => [
                 %{
                   "address" => %{
                     "address_id" => "1",
                     "city" => "São Paulo",
                     "complement" => "",
                     "country" => "BRA",
                     "geo_coordinates" => [-46.64538, -23.52124],
                     "neighborhood" => "Bom Retiro",
                     "number" => "998",
                     "postal_code" => "01131-000",
                     "receiver_name" => nil,
                     "reference" => nil,
                     "state" => "SP",
                     "street" => "Rua dos Italianos"
                   },
                   "name" => "Baw Birth Store",
                   "pickup_distance" => 6.259303569793701
                 }
               ]
             }
    end

    test "returns empty delivery options if product variant mapping has sold_out as true", %{
      org: org,
      product_variants: pvs
    } do
      [p1, p2 | _] = pvs

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p1,
        external_id: "2",
        sold_out: true
      )

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      insert(:vtex_credential, organization: org)

      # Set ecommerce setting
      Rms.Settings.upsert_organization_setting(org.id, "ecommerce", "vtex")

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        {:ok, @vtex_cart_simulation}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 11
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004"
      }

      assert {:ok,
              %{
                "items" => [
                  %{"delivery_options" => first_options},
                  %{"delivery_options" => second_options}
                ]
              }} =
               SimulateCart.fetch_delivery_options(org.id, cart_info)

      assert first_options == []
      assert length(second_options) > 0
    end

    test "returns empty response when enabled_fulfillments setting has delivery and local_pickup as false (organization setting)",
         %{
           org: org,
           product_variants: pvs
         } do
      # Set enabled_fulfillments setting with both delivery and local_pickup as false on organization level
      Rms.Settings.upsert_organization_setting(org.id, "enabled_fulfillments", %{
        "delivery" => false,
        "local_pickup" => false
      })

      [p1, p2 | _] = pvs

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 11
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004"
      }

      assert {:ok, response} = SimulateCart.fetch_delivery_options(org.id, cart_info)
      assert response == %{"items" => [], "pickup_points" => []}
    end

    test "returns empty response when enabled_fulfillments setting has delivery and local_pickup as false (location setting)",
         %{
           org: org,
           product_variants: pvs
         } do
      # Create location
      location = insert(:location, organization: org)

      # Set enabled_fulfillments setting with both delivery and local_pickup as false on location level
      Rms.Settings.create_location_setting(
        location.id,
        org.id,
        "enabled_fulfillments",
        %{
          "delivery" => false,
          "local_pickup" => false
        }
      )

      [p1, p2 | _] = pvs

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 11
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004",
        "location_id" => location.id
      }

      assert {:ok, response} = SimulateCart.fetch_delivery_options(org.id, cart_info)
      assert response == %{"items" => [], "pickup_points" => []}
    end

    test "prioritizes organization setting over location setting for enabled_fulfillments", %{
      org: org,
      product_variants: pvs
    } do
      # Create location with ecommerce credentials
      location = insert(:location, organization: org)
      insert(:vtex_credential, organization: org)
      Rms.Settings.upsert_organization_setting(org.id, "ecommerce", "vtex")

      # Set enabled_fulfillments at location level to allow delivery
      Rms.Settings.create_location_setting(
        location.id,
        org.id,
        "enabled_fulfillments",
        %{
          "delivery" => true,
          "local_pickup" => true
        }
      )

      # Set enabled_fulfillments at organization level to disable all
      Rms.Settings.upsert_organization_setting(org.id, "enabled_fulfillments", %{
        "delivery" => false,
        "local_pickup" => false
      })

      [p1, p2 | _] = pvs

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 11
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004",
        "location_id" => location.id
      }

      # Organization setting should take precedence, returning empty response
      assert {:ok, response} = SimulateCart.fetch_delivery_options(org.id, cart_info)
      assert response == %{"items" => [], "pickup_points" => []}
    end
  end

  describe "simulate_cart/3" do
    test "passes priceTables when searching for customer", %{org: org, product_variants: pvs} do
      customer = insert(:customer, organization: org)
      [p1, _ | _] = pvs

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p1
      )

      insert(:vtex_credential, organization: org)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :search_customer, fn _, _ ->
        {:ok, [%{"priceTables" => "sample_price_table"}]}
      end)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, payload ->
        assert payload["priceTables"] == ["sample_price_table"]
        {:ok, @vtex_cart_simulation}
      end)

      cart_info = %{
        # assuming customer ID for testing
        "customer_id" => customer.id,
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004"
      }

      assert {:ok, _fulfillment_options} =
               SimulateCart.simulate_cart(org.id, cart_info, "vtex")
    end

    test "calculates total_items_list_price using listPrice", %{org: org, product_variants: pvs} do
      [p1, p2 | _] = pvs

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "vtex",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      insert(:vtex_credential, organization: org)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        {:ok, @vtex_cart_simulation}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 40
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 11
          }
        ],
        "country" => "BRA",
        "zip_code" => "04088004"
      }

      assert {:ok, result} = SimulateCart.simulate_cart(org.id, cart_info, "vtex")
      assert result["total_items_list_price"] == Decimal.new("8475.00")
      assert result["total_items_selling_price"] == Decimal.new("3075.00")
      assert result["total_price"] == Decimal.new("3075.00")
    end
  end
end
