defmodule Rms.Commerce.Carts.SimulatorTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Carts.Simulator
  import Rms.Factory

  use ExUnit.Case
  import Mox

  setup :verify_on_exit!

  describe "run/2" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      # Insert Shopify credentials for V1 tests
      insert(:shopify_credential, organization: organization)

      {:ok, org: organization, location: location, product_variants: product_variants}
    end

    test "uses V1 implementation when feature flag is disabled", %{
      org: org,
      location: location,
      product_variants: pvs
    } do
      [p1, p2 | _] = pvs

      # Mock feature flag to return false (use V1)
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      # Mock Shopify client and simulate_cart call
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      # The V1 implementation might not call simulate_cart if there are no external mappings
      # So we use stub instead of expect to allow 0 or more calls
      stub(Rms.Integrations.Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         %{
           "cart" => %{
             "lines" => %{"edges" => []},
             "discountCodes" => [],
             "discountAllocations" => []
           }
         }}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery"
          }
        ],
        "customer_id" => 123,
        "location_id" => location.id
      }

      # This should call the V1 implementation (Rms.Integrations.Shopify.Carts.Simulate.execute)
      result = Simulator.run(org.id, cart_info)

      # The result structure should match V1 format
      assert {:ok, response} = result
      assert is_map(response)
    end

    test "uses V2 implementation when feature flag is enabled", %{
      org: org,
      location: location,
      product_variants: pvs
    } do
      [p1, p2 | _] = pvs

      # Mock feature flag to return true (use V2)
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> true end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery"
          }
        ],
        "customer_id" => 123,
        "location_id" => location.id
      }

      # This should call the V2 implementation
      # Since V2 is not fully implemented yet, it might fail and fallback to V1
      result = Simulator.run(org.id, cart_info)

      # Should return a result (either V2 success or V1 fallback)
      assert {:ok, response} = result
      assert is_map(response)
    end

    test "forces V2 implementation regardless of feature flag", %{
      org: org,
      location: location,
      product_variants: pvs
    } do
      [p1 | _] = pvs

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ],
        "customer_id" => 123,
        "location_id" => location.id
      }

      # This should force V2 implementation regardless of feature flag
      result = Simulator.run_v2(org.id, cart_info)

      # Should return a result or error from V2
      case result do
        {:ok, response} ->
          assert is_map(response)

        {:error, reason} ->
          # V2 might fail due to missing implementations, which is expected
          assert is_atom(reason) or is_tuple(reason)
      end
    end

    test "handles empty cart gracefully", %{org: org, location: location} do
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      # Mock Shopify client for empty cart
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      # Use stub for simulate_cart since it might not be called for empty carts
      stub(Rms.Integrations.Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         %{
           "cart" => %{
             "lines" => %{"edges" => []},
             "discountCodes" => [],
             "discountAllocations" => []
           }
         }}
      end)

      cart_info = %{
        "items" => [],
        "customer_id" => 123,
        "location_id" => location.id
      }

      result = Simulator.run(org.id, cart_info)

      assert {:ok, response} = result
      assert is_map(response)
    end

    test "handles invalid organization_id", %{product_variants: pvs} do
      [p1 | _] = pvs

      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ]
      }

      # Using invalid organization_id should handle gracefully
      assert_raise Ecto.NoResultsError, fn ->
        Simulator.run(999_999, cart_info)
      end
    end
  end

  describe "run/2 calling v2" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: organization,
          product: build(:product, organization: organization),
          price: Decimal.new("10.00"),
          list_price: Decimal.new("12.00")
        )

      # Insert Shopify credentials for V2 tests
      insert(:shopify_credential, organization: organization)

      # Create external mappings for product variants so they can be processed by Shopify
      Enum.each(product_variants, fn pv ->
        insert(:product_variant_mapping,
          organization: organization,
          product_variant: pv,
          external_id: "gid://shopify/ProductVariant/#{pv.id}",
          source: "shopify"
        )
      end)

      # Create a customer for the tests with a valid address
      customer = insert(:customer, organization: organization)

      insert(:address,
        customer: customer,
        country_name: "Brasil",
        state: "SP",
        city_name: "São Paulo",
        zip: "01000-000"
      )

      {:ok,
       org: organization,
       location: location,
       product_variants: product_variants,
       customer: customer}
    end

    test "returns the same result as v1 under the same circumstances", %{
      org: org,
      location: location,
      product_variants: pvs,
      customer: customer
    } do
      [p1, p2 | _] = pvs

      # Mock Shopify client for both V1 and V2
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, 2, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      # Mock the simulate_cart response to return consistent data in the format V1 expects
      cart_response = %{
        "data" => %{
          "cartCreate" => %{
            "userErrors" => [],
            "cart" => %{
              "lines" => %{
                "edges" => [
                  %{
                    "node" => %{
                      "quantity" => 2,
                      "cost" => %{
                        "totalAmount" => %{"amount" => "20.00"},
                        "amountPerQuantity" => %{"amount" => "10.00"}
                      },
                      "discountAllocations" => [],
                      "merchandise" => %{"id" => "gid://shopify/ProductVariant/#{p1.id}"}
                    }
                  },
                  %{
                    "node" => %{
                      "quantity" => 1,
                      "cost" => %{
                        "totalAmount" => %{"amount" => "10.00"},
                        "amountPerQuantity" => %{"amount" => "10.00"}
                      },
                      "discountAllocations" => [],
                      "merchandise" => %{"id" => "gid://shopify/ProductVariant/#{p2.id}"}
                    }
                  }
                ]
              },
              "discountCodes" => [],
              "discountAllocations" => []
            }
          }
        }
      }

      delivery_response = %{
        "data" => %{
          "cartDeliveryGroupsUpdate" => %{
            "userErrors" => [],
            "cart" => %{
              "deliveryGroups" => %{
                "edges" => []
              }
            }
          }
        }
      }

      # Format as multipart response like V1 expects (exactly 5 parts)
      multipart_response =
        "part1\r\n\r\n" <>
          Jason.encode!(cart_response) <>
          "\r\n\r\npart3\r\n\r\n" <>
          Jason.encode!(delivery_response) <>
          "\r\n\r\npart5"

      expect(Rms.Integrations.Shopify.Mock, :simulate_cart, 2, fn _, _ ->
        {:ok, multipart_response}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ],
        "customer_id" => customer.id,
        "location_id" => location.id,
        "staff_id" => 456,
        "country" => "BRA",
        "postal_code" => "01000000",
        "discounts" => []
      }

      # Get V1 result
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)
      {:ok, v1_result} = Simulator.run(org.id, cart_info)

      # Get V2 result
      {:ok, v2_result} = Simulator.run_v2(org.id, cart_info)

      # Compare key fields that should be identical
      assert v1_result["customer_id"] == v2_result["customer_id"]
      assert v1_result["location_id"] == v2_result["location_id"]
      assert v1_result["ecommerce"] == v2_result["ecommerce"]
      assert v1_result["staff_id"] == v2_result["staff_id"]

      # Compare delivery groups structure
      assert length(v1_result["delivery_groups"]) == length(v2_result["delivery_groups"])

      # Both should have the same number of cart items
      v1_items = v1_result["delivery_groups"] |> Enum.flat_map(& &1["cart_items"])
      v2_items = v2_result["delivery_groups"] |> Enum.flat_map(& &1["cart_items"])
      assert length(v1_items) == length(v2_items)

      # Compare totals (should be very close, allowing for rounding differences)
      assert_decimal_close(
        v1_result["total_items_list_price"],
        v2_result["total_items_list_price"]
      )

      assert_decimal_close(
        v1_result["total_items_selling_price"],
        v2_result["total_items_selling_price"]
      )

      assert_decimal_close(v1_result["total_price"], v2_result["total_price"])
    end

    test "handles empty cart the same way as v1", %{org: org, location: location} do
      # Mock Shopify client for V1 only (V2 won't call Shopify for empty carts)
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, 1, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      stub(Rms.Integrations.Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         %{
           "cart" => %{
             "lines" => %{"edges" => []},
             "discountCodes" => [],
             "discountAllocations" => []
           }
         }}
      end)

      cart_info = %{
        "items" => [],
        "customer_id" => 123,
        "location_id" => location.id,
        "country" => "BRA",
        "postal_code" => "01000000"
      }

      # Get V1 result
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)
      {:ok, v1_result} = Simulator.run(org.id, cart_info)

      # Get V2 result
      {:ok, v2_result} = Simulator.run_v2(org.id, cart_info)

      # Both should handle empty carts the same way
      assert v1_result["customer_id"] == v2_result["customer_id"]
      assert v1_result["location_id"] == v2_result["location_id"]
      assert v1_result["delivery_groups"] == v2_result["delivery_groups"]
      assert v1_result["total_price"] == v2_result["total_price"]
    end

    defp assert_decimal_close(v1_value, v2_value) do
      # Convert to Decimal if they're strings
      d1 = if is_binary(v1_value), do: Decimal.new(v1_value), else: v1_value
      d2 = if is_binary(v2_value), do: Decimal.new(v2_value), else: v2_value

      # Allow for small rounding differences (within 0.01)
      diff = Decimal.abs(Decimal.sub(d1, d2))

      assert Decimal.compare(diff, Decimal.new("0.01")) in [:lt, :eq],
             "Values #{d1} and #{d2} differ by more than 0.01"
    end
  end

  describe "Cart Simulator Core Data Structures" do
    alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount, Totals}

    test "Cart struct enforces required keys" do
      # Should work with required keys
      cart = %Cart{
        organization_id: 1,
        items: []
      }

      assert cart.organization_id == 1
      assert cart.items == []

      # Should raise error without required keys
      assert_raise ArgumentError, fn ->
        struct!(Cart, %{})
      end
    end

    test "Item struct enforces required keys" do
      # Should work with required keys
      item = %Item{
        variant_id: 123,
        quantity: 2
      }

      assert item.variant_id == 123
      assert item.quantity == 2

      # Should raise error without required keys
      assert_raise ArgumentError, fn ->
        struct!(Item, %{variant_id: 123})
      end
    end

    test "Simulation struct enforces required keys" do
      # Should work with required keys
      simulation = %Simulation{
        strategy: :in_store,
        items: []
      }

      assert simulation.strategy == :in_store
      assert simulation.items == []
    end

    test "Discount struct enforces required keys" do
      # Should work with required keys
      discount = %Discount{
        code: "SAVE10",
        value: Decimal.new("10.00")
      }

      assert discount.code == "SAVE10"
      assert Decimal.equal?(discount.value, Decimal.new("10.00"))
    end

    test "Totals struct has all optional fields" do
      # Should work with no fields
      totals = %Totals{}
      assert is_nil(totals.final_price)

      # Should work with some fields
      totals = %Totals{
        final_price: Decimal.new("29.99"),
        items_list_price: Decimal.new("39.99")
      }

      assert Decimal.equal?(totals.final_price, Decimal.new("29.99"))
      assert Decimal.equal?(totals.items_list_price, Decimal.new("39.99"))
    end
  end
end
