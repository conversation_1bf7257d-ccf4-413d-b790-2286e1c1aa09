defmodule Rms.Commerce.Carts.SimulatorTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Carts.Simulator
  import Rms.Factory

  use ExUnit.Case
  import Mox

  setup :verify_on_exit!

  describe "Cart Simulator V2" do
    setup do
      organization = insert(:organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      # Insert Shopify credentials for V1 tests
      insert(:shopify_credential, organization: organization)

      {:ok, org: organization, product_variants: product_variants}
    end

    test "uses V1 implementation when feature flag is disabled", %{org: org, product_variants: pvs} do
      [p1, p2 | _] = pvs

      # Mock feature flag to return false (use V1)
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      # Mock Shopify client and simulate_cart call
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      expect(Rms.Integrations.Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok, %{
          "cart" => %{
            "lines" => %{"edges" => []},
            "discountCodes" => [],
            "discountAllocations" => []
          }
        }}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery"
          }
        ],
        "customer_id" => 123,
        "location_id" => 456
      }

      # This should call the V1 implementation (Rms.Integrations.Shopify.Carts.Simulate.execute)
      result = Simulator.run(org.id, cart_info)

      # The result structure should match V1 format
      assert {:ok, response} = result
      assert is_map(response)
    end

    test "uses V2 implementation when feature flag is enabled", %{org: org, product_variants: pvs} do
      [p1, p2 | _] = pvs

      # Mock feature flag to return true (use V2)
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> true end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery"
          }
        ],
        "customer_id" => 123,
        "location_id" => 456
      }

      # This should call the V2 implementation
      # Since V2 is not fully implemented yet, it might fail and fallback to V1
      result = Simulator.run(org.id, cart_info)

      # Should return a result (either V2 success or V1 fallback)
      assert {:ok, response} = result
      assert is_map(response)
    end

    test "forces V2 implementation regardless of feature flag", %{org: org, product_variants: pvs} do
      [p1 | _] = pvs

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ],
        "customer_id" => 123,
        "location_id" => 456
      }

      # This should force V2 implementation regardless of feature flag
      result = Simulator.run_v2(org.id, cart_info)

      # Should return a result or error from V2
      case result do
        {:ok, response} ->
          assert is_map(response)
        {:error, reason} ->
          # V2 might fail due to missing implementations, which is expected
          assert is_atom(reason) or is_tuple(reason)
      end
    end

    test "handles empty cart gracefully", %{org: org} do
      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      # Mock Shopify client for empty cart
      expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        %Tesla.Client{adapter: Tesla.Adapter.Mock}
      end)

      expect(Rms.Integrations.Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok, %{
          "cart" => %{
            "lines" => %{"edges" => []},
            "discountCodes" => [],
            "discountAllocations" => []
          }
        }}
      end)

      cart_info = %{
        "items" => [],
        "customer_id" => 123,
        "location_id" => 456
      }

      result = Simulator.run(org.id, cart_info)

      assert {:ok, response} = result
      assert is_map(response)
    end

    test "handles invalid organization_id", %{product_variants: pvs} do
      [p1 | _] = pvs

      expect(Rms.Clients.FeatureFlag.Mock, :should_use_new_simulator?, fn _ -> false end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ]
      }

      # Using invalid organization_id should handle gracefully
      assert_raise Ecto.NoResultsError, fn ->
        Simulator.run(999_999, cart_info)
      end
    end
  end

  describe "Cart Simulator Core Data Structures" do
    alias Rms.Commerce.Carts.Simulator.Core.{Cart, Item, Simulation, Discount, Totals}

    test "Cart struct enforces required keys" do
      # Should work with required keys
      cart = %Cart{
        organization_id: 1,
        items: []
      }
      assert cart.organization_id == 1
      assert cart.items == []

      # Should raise error without required keys
      assert_raise ArgumentError, fn ->
        struct!(Cart, %{})
      end
    end

    test "Item struct enforces required keys" do
      # Should work with required keys
      item = %Item{
        variant_id: 123,
        quantity: 2
      }
      assert item.variant_id == 123
      assert item.quantity == 2

      # Should raise error without required keys
      assert_raise ArgumentError, fn ->
        struct!(Item, %{variant_id: 123})
      end
    end

    test "Simulation struct enforces required keys" do
      # Should work with required keys
      simulation = %Simulation{
        strategy: :in_store,
        items: []
      }
      assert simulation.strategy == :in_store
      assert simulation.items == []
    end

    test "Discount struct enforces required keys" do
      # Should work with required keys
      discount = %Discount{
        code: "SAVE10",
        value: Decimal.new("10.00")
      }
      assert discount.code == "SAVE10"
      assert Decimal.equal?(discount.value, Decimal.new("10.00"))
    end

    test "Totals struct has all optional fields" do
      # Should work with no fields
      totals = %Totals{}
      assert is_nil(totals.final_price)

      # Should work with some fields
      totals = %Totals{
        final_price: Decimal.new("29.99"),
        items_list_price: Decimal.new("39.99")
      }
      assert Decimal.equal?(totals.final_price, Decimal.new("29.99"))
      assert Decimal.equal?(totals.items_list_price, Decimal.new("39.99"))
    end
  end
end
