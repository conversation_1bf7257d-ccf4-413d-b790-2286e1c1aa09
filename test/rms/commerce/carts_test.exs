defmodule Rms.Commerce.CartsTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Carts
  alias Rms.Commerce.Carts.Cart
  import Rms.Factory

  use ExUnit.Case

  describe "create_cart/2" do
    test "creates an empty cart" do
      organization = insert(:organization)

      attrs = %{
        id: "0190553d-81d2-7784-ad53-9d8a14a36f1e",
        organization_id: organization.id,
        total_price: 100.00,
        total_items_list_price: 100.00,
        total_items_selling_price: 100.00,
        total_manual_discount: 0.00,
        ecommerce: "vtex"
      }

      {:ok, cart} = Carts.create_cart(organization.id, attrs)

      assert %Cart{} = cart
    end

    test "doesnt allow a cart with empty delivery groups" do
      organization = insert(:organization)

      attrs = %{
        id: "0190553d-81d2-7784-ad53-9d8a14a36f1e",
        organization_id: organization.id,
        total_price: 100.00,
        total_items_list_price: 100.00,
        total_items_selling_price: 100.00,
        total_manual_discount: 0.00,
        ecommerce: "vtex",
        delivery_groups: [
          %{
            id: "5fe55003-d2bd-4444-9418-8171a157fae8",
            delivery_time: 3,
            fulfillment_type: "delivery"
          }
        ]
      }

      {:error, messages} = Carts.create_cart(organization.id, attrs)
      assert %{delivery_groups: [%{cart_items: ["can't be blank"]}]} = errors_on(messages)
    end

    test "creates a cart with multiple items" do
      organization = insert(:organization)
      product = insert(:product, organization: organization)
      product_variant = insert(:product_variant, product: product, organization: organization)
      product_variant2 = insert(:product_variant, product: product, organization: organization)
      staff = insert(:staff, organization: organization)
      customer = insert(:customer, organization: organization)
      location = insert(:location, organization: organization)
      address = insert(:address, organization: organization)

      attrs = %{
        id: "0190553d-81d2-7784-ad53-9d8a14a36f1e",
        organization_id: organization.id,
        staff_id: staff.id,
        customer_id: customer.id,
        location_id: location.id,
        shipping_address_id: address.id,
        total_price: 550.00,
        total_items_list_price: 700.00,
        total_items_selling_price: 550.00,
        total_manual_discount: 0.00,
        metadata: %{
          aaaa: "bbbb"
        },
        ecommerce: "vtex",
        messages: [
          %{
            type: "error",
            code: "aaaa",
            info: %{
              asdasd: "bbbbb"
            }
          }
        ],
        discounts: [
          %{
            type: "fixed",
            value: "10.00",
            description: "10% off"
          }
        ],
        delivery_groups: [
          %{
            id: "5fe55003-d2bd-4444-9418-8171a157fae8",
            delivery_time: 3,
            fulfillment_type: "delivery",
            pickup_point: %{
              "address" => "aaaa"
            },
            metadata: %{
              bbbb: "cccc"
            },
            cart_items: [
              %{
                quantity: 1,
                list_price: 100.00,
                selling_price: 100.00,
                total_price: 100.00,
                product_variant_id: product_variant.id,
                discounts: [
                  %{
                    type: "fixed",
                    value: "10.00",
                    description: "10% off"
                  }
                ],
                metadata: %{
                  asbdas: "asdasd"
                }
              },
              %{
                quantity: 3,
                list_price: 200.00,
                selling_price: 150.00,
                total_price: 450.00,
                discounts: [
                  %{
                    type: "fixed",
                    value: "10.00",
                    description: "10% off"
                  }
                ],
                product_variant_id: product_variant2.id
              }
            ]
          }
        ]
      }

      {:ok, cart} = Carts.create_cart(organization.id, attrs)
      assert %Cart{} = cart
      assert cart.customer_id == customer.id
      assert cart.staff_id == staff.id
      assert cart.location_id == location.id
      assert cart.shipping_address_id == address.id

      Enum.each(cart.delivery_groups, fn delivery_group ->
        Enum.each(delivery_group.cart_items, fn cart_item ->
          assert [
                   %{
                     type: "fixed",
                     value: "10.00",
                     description: "10% off"
                   }
                 ] = Rms.Repo.preload(cart_item, [:discounts]).discounts
        end)
      end)

      assert [
               %{
                 type: "fixed",
                 value: "10.00",
                 description: "10% off"
               }
             ] = cart.discounts
    end

    test "creates a cart with a shipping_address, nilify on delete" do
      organization = insert(:organization)

      shipping_address = insert(:address, organization: organization)

      attrs = %{
        id: "0190553d-81d2-7784-ad53-9d8a14a36f1e",
        organization_id: organization.id,
        total_price: 100.00,
        total_items_list_price: 100.00,
        total_items_selling_price: 100.00,
        total_manual_discount: 0.00,
        ecommerce: "vtex",
        shipping_address_id: shipping_address.id
      }

      {:ok, cart} = Carts.create_cart(organization.id, attrs)
      assert cart.shipping_address_id == shipping_address.id
      Rms.Addresses.delete_address!(shipping_address)

      cart = Carts.get_cart!(organization.id, cart.id)
      assert cart.shipping_address_id == nil
    end

    test "creates a cart with addons" do
      organization = insert(:organization)
      product = insert(:product, organization: organization)
      product_variant = insert(:product_variant, product: product, organization: organization)
      addon = insert(:addon, organization: organization)

      attrs = %{
        id: "0190553d-81d2-7784-ad53-9d8a14a36f1e",
        organization_id: organization.id,
        total_price: 150.00,
        total_items_list_price: 150.00,
        total_items_selling_price: 150.00,
        total_manual_discount: 0.00,
        ecommerce: "vtex",
        cart_addons: [
          %{
            quantity: 1,
            name: addon.name,
            type: addon.type,
            price: 50.00,
            list_price: 50.00,
            image_url: "url",
            addon_id: addon.id
          }
        ],
        delivery_groups: [
          %{
            id: "5fe55003-d2bd-4444-9418-8171a157fae8",
            delivery_time: 0,
            fulfillment_type: "in-store",
            cart_items: [
              %{
                quantity: 1,
                list_price: 100.00,
                selling_price: 100.00,
                total_price: 100.00,
                product_variant_id: product_variant.id
              }
            ]
          }
        ]
      }

      assert {:ok, cart} = Carts.create_cart(organization.id, attrs)
      cart = Repo.preload(cart, [:cart_addons])
      assert [cart_addon] = cart.cart_addons
      assert cart_addon.addon_id == addon.id
      assert cart_addon.price == Decimal.new("50.0")
    end
  end

  describe "update_cart/2" do
    test "updates a cart when valid attributes are passed" do
      organization = insert(:organization)
      cart = insert(:cart, organization: organization)
      staff = insert(:staff, organization: organization)
      location = insert(:location, organization: organization)

      attrs = %{
        saved: true,
        state_of_sale: "cashier",
        staff_id: staff.id,
        location_id: location.id
      }

      {:ok, %Cart{} = updated_cart} = Carts.update_cart(cart, attrs)
      assert updated_cart.saved == true
      assert updated_cart.state_of_sale == "cashier"
      assert updated_cart.staff_id == staff.id
      assert updated_cart.location_id == location.id
    end
  end

  describe "paginated_carts/2" do
    setup do
      organization = insert(:organization)
      # Insert multiple carts for testing
      _carts = insert_list(20, :cart, organization: organization)

      {:ok, organization: organization}
    end

    test "paging works correctly", %{organization: organization} do
      opts = [limit: 10]
      results = Carts.paginated_carts(organization.id, opts)
      assert length(results.entries) == 10

      opts = [limit: 5, after: results.metadata.after]
      next_page_results = Carts.paginated_carts(organization.id, opts)
      assert length(next_page_results.entries) == 5

      result_entries_id = Enum.map(results.entries, & &1.id)
      next_page_results_entries_id = Enum.map(next_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(result_entries_id),
               MapSet.new(next_page_results_entries_id)
             )

      opts = [limit: 5, before: next_page_results.metadata.before]
      previous_page_results = Carts.paginated_carts(organization.id, opts)
      assert length(previous_page_results.entries) == 5

      previous_page_results_entries_id = Enum.map(previous_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(previous_page_results_entries_id),
               MapSet.new(next_page_results_entries_id)
             )
    end

    test "search works correctly", %{organization: organization} do
      insert(:cart, saved: true, organization: organization)
      insert(:cart, saved: true, organization: organization)

      search_opts = [saved: true, limit: 5]
      search_results = Carts.paginated_carts(organization.id, search_opts)

      assert length(search_results.entries) == 2

      assert Enum.all?(search_results.entries, &(&1.saved == true))
    end

    test "search does not duplicate results", %{organization: organization} do
      search_opts = [limit: 20]

      search_results = Carts.paginated_carts(organization.id, search_opts)
      product_ids = Enum.map(search_results.entries, fn c -> c.id end)
      assert product_ids == Enum.uniq(product_ids)
    end
  end
end
