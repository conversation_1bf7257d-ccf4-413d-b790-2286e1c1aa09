defmodule Rms.Commerce.Discounts.AuthorizationRequestTest do
  use Rms.DataCase

  alias Rms.Commerce.Discounts.AuthorizationRequest

  describe "changeset/2" do
    test "validates required fields and valid statuses" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      valid_attrs = %{
        "code" => "valid_code",
        "status" => "pending",
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "discounts" => []
      }

      changeset =
        AuthorizationRequest.changeset(
          %AuthorizationRequest{organization_id: org.id},
          valid_attrs
        )

      assert changeset.valid?

      Enum.each(~w(accepted pending rejected canceled), fn status ->
        changeset =
          AuthorizationRequest.changeset(
            %AuthorizationRequest{organization_id: org.id},
            Map.put(valid_attrs, "status", status)
          )

        assert changeset.valid?
      end)
    end

    test "invalid when required fields are missing" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      changeset =
        AuthorizationRequest.changeset(%AuthorizationRequest{organization_id: org.id}, %{
          "location_id" => loc.id,
          "staff_id" => staff.id
        })

      refute changeset.valid?

      assert [code: {"can't be blank", [validation: :required]}] = changeset.errors
    end

    test "invalid when status is not in allowed values" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      invalid_attrs = %{
        "code" => "valid_code",
        "status" => "invalid_status",
        "location_id" => loc.id,
        "staff_id" => staff.id
      }

      changeset =
        AuthorizationRequest.changeset(
          %AuthorizationRequest{organization_id: org.id},
          invalid_attrs
        )

      refute changeset.valid?

      assert {:status,
              {"is invalid",
               [validation: :inclusion, enum: ~w(accepted pending rejected canceled)]}} in changeset.errors
    end

    test "casts discounts association" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      discount_attrs = %{
        "type" => "percentage",
        "value" => "0.1"
      }

      attrs = %{
        "code" => "valid_code",
        "status" => "pending",
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "discounts" => [discount_attrs]
      }

      changeset =
        AuthorizationRequest.changeset(%AuthorizationRequest{organization_id: org.id}, attrs)

      assert changeset.valid?

      [discount_changeset] = get_change(changeset, :discounts)
      assert discount_changeset.valid?
      assert discount_changeset.changes.type == "percentage"
    end
  end

  describe "update_changeset/2" do
    test "validates status" do
      org = insert(:organization)

      valid_attrs = %{
        "status" => "pending"
      }

      changeset =
        AuthorizationRequest.update_changeset(
          %AuthorizationRequest{organization_id: org.id},
          valid_attrs
        )

      assert changeset.valid?

      Enum.each(~w(accepted pending rejected canceled), fn status ->
        changeset =
          AuthorizationRequest.update_changeset(
            %AuthorizationRequest{organization_id: org.id},
            %{"status" => status}
          )

        assert changeset.valid?
      end)
    end

    test "invalid when status is not in allowed values" do
      org = insert(:organization)

      invalid_attrs = %{
        "status" => "invalid_status"
      }

      changeset =
        AuthorizationRequest.update_changeset(
          %AuthorizationRequest{organization_id: org.id},
          invalid_attrs
        )

      refute changeset.valid?

      assert {:status,
              {"is invalid",
               [validation: :inclusion, enum: ~w(accepted pending rejected canceled)]}} in changeset.errors
    end
  end
end
