defmodule Rms.Commerce.Discounts.DiscountTest do
  use Rms.DataCase

  alias Rms.Commerce.Discounts.Discount

  describe "changeset/2" do
    test "when type is coupon" do
      org = insert(:organization)

      attrs = %{
        "type" => "coupon",
        "value" => "1234"
      }

      changeset = Discount.changeset(%Discount{organization_id: org.id}, attrs)
      assert changeset.valid?
    end

    test "when type is coupon and code is not set" do
      org = insert(:organization)

      attrs = %{
        "type" => "coupon"
      }

      changeset = Discount.changeset(%Discount{organization_id: org.id}, attrs)
      refute changeset.valid?
    end

    test "when type is percentage" do
      org = insert(:organization)

      attrs = %{
        "type" => "percentage",
        "value" => "0.1"
      }

      changeset = Discount.changeset(%Discount{organization_id: org.id}, attrs)
      assert changeset.valid?
    end

    test "when type is fixed" do
      org = insert(:organization)

      attrs = %{
        "type" => "percentage",
        "value" => "10.0"
      }

      changeset = Discount.changeset(%Discount{organization_id: org.id}, attrs)
      assert changeset.valid?
    end
  end
end
