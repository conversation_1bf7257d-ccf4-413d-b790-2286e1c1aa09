defmodule Rms.Commerce.DiscountsTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Discounts
  alias Rms.Commerce.Discounts.AuthorizationRequest
  alias Rms.Commerce.Discounts.Discount
  import Rms.Factory

  use ExUnit.Case

  describe "validate_token/2" do
    setup do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      authorization_request =
        %AuthorizationRequest{
          organization_id: org.id,
          location_id: location.id,
          staff_id: staff.id,
          status: "pending",
          code: "123456"
        }
        |> Rms.Repo.insert!()

      {:ok, authorization_request: authorization_request}
    end

    test "valid code for a pending authorization request", %{
      authorization_request: authorization_request
    } do
      assert {:ok, "valid code"} = Discounts.validate_token(authorization_request, "123456")
    end

    test "invalid code format", %{authorization_request: authorization_request} do
      assert {:unauthorized, "invalid code"} =
               Discounts.validate_token(authorization_request, "abc123")
    end

    test "wrong code", %{authorization_request: authorization_request} do
      assert {:unauthorized, "wrong code"} =
               Discounts.validate_token(authorization_request, "654321")
    end

    test "already approved authorization request", %{authorization_request: authorization_request} do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "accepted"})
        |> Rms.Repo.update!()

      assert {:error, "request already approved"} =
               Discounts.validate_token(authorization_request, "123456")
    end

    test "already canceled authorization request", %{authorization_request: authorization_request} do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "canceled"})
        |> Rms.Repo.update!()

      assert {:error, "request already canceled"} =
               Discounts.validate_token(authorization_request, "123456")
    end

    test "already denied authorization request", %{authorization_request: authorization_request} do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "rejected"})
        |> Rms.Repo.update!()

      assert {:error, "request already denied"} =
               Discounts.validate_token(authorization_request, "123456")
    end
  end

  describe "get_authorization_request!/2" do
    test "gets authorization request by ID" do
      org = insert(:organization)
      staff = insert(:staff, organization: org)
      loc = insert(:location, organization: org)

      authorization_request =
        insert(:authorization_request, organization: org, staff: staff, location: loc)

      result =
        Discounts.get_authorization_request!(
          authorization_request.organization_id,
          authorization_request.id
        )

      assert result.id == authorization_request.id
    end

    test "raises error when authorization request not found" do
      assert_raise Ecto.NoResultsError, fn ->
        Discounts.get_authorization_request!(-1, -1)
      end
    end
  end

  describe "update_authorization_request_status!/2" do
    setup do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      authorization_request =
        %AuthorizationRequest{
          organization_id: org.id,
          location_id: location.id,
          staff_id: staff.id,
          status: "pending",
          code: "123456"
        }
        |> Rms.Repo.insert!()

      {:ok, authorization_request: authorization_request}
    end

    test "successfully updates status", %{authorization_request: authorization_request} do
      Discounts.update_authorization_request_status!(authorization_request, "accepted")
      updated_request = Rms.Repo.get!(AuthorizationRequest, authorization_request.id)
      assert updated_request.status == "accepted"
    end

    test "fails with invalid status", %{authorization_request: authorization_request} do
      assert_raise Ecto.InvalidChangesetError, fn ->
        Discounts.update_authorization_request_status!(authorization_request, "invalid_status")
      end
    end
  end

  describe "validate_required_attrs/2" do
    test "validates presence of code for coupon type" do
      org = insert(:organization)

      changeset =
        Discount.changeset(%Discount{organization_id: org.id}, %{
          "type" => "coupon"
        })

      refute changeset.valid?
      assert %{value: ["can't be blank"]} == errors_on(changeset)
    end

    test "validates presence of value for percentage type" do
      org = insert(:organization)

      changeset =
        Discount.changeset(%Discount{organization_id: org.id}, %{
          "type" => "percentage"
        })

      refute changeset.valid?
      assert %{value: ["can't be blank"]} == errors_on(changeset)
    end
  end

  describe "Discount operations" do
    test "successfully creates a discount with code" do
      org = insert(:organization)
      discount_attrs = %{"type" => "coupon", "value" => "123456"}
      changeset = Discount.changeset(%Discount{organization_id: org.id}, discount_attrs)
      assert changeset.valid?

      {:ok, discount} = Rms.Repo.insert(changeset)
      assert discount.type == "coupon"
      assert discount.value == "123456"
      assert discount.organization_id == org.id
    end

    test "successfully creates a discount with value" do
      org = insert(:organization)

      discount_attrs = %{
        "type" => "percentage",
        "value" => "0.1",
        "organization_id" => org.id
      }

      changeset = Discount.changeset(%Discount{organization_id: org.id}, discount_attrs)
      assert changeset.valid?

      {:ok, discount} = Rms.Repo.insert(changeset)
      assert discount.type == "percentage"
      assert discount.value == "0.1"
      assert discount.organization_id == org.id
    end

    test "fails to create a discount without required attributes" do
      changeset = Discount.changeset(%Discount{}, %{"type" => "percentage"})
      refute changeset.valid?
      assert %{value: ["can't be blank"]} == errors_on(changeset)
    end
  end
end
