defmodule Rms.Commerce.Discounts.Engine.GiftHandlerTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Commerce.Discounts.Engine.GiftHandler

  describe "execute/2" do
    test "does not insert new itens in cart simulation" do
      org = insert(:organization)

      [p1, p2] =
        insert_list(2, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("202.25"),
        "total_manual_discount" => Decimal.new("10.65"),
        "total_price" => Decimal.new("222.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("0.00")
      }

      new_cart = GiftHandler.execute(org.id, cart)

      assert cart == new_cart
    end

    test "insert new itens in cart simulation" do
      org = insert(:organization)

      [p1, p2, p3, p4, p5] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      attrs = %{
        organization_id: org.id,
        product_variant_id: p1.id,
        gifts: [
          %{
            product_variant_id: p3.id,
            quantity: 1,
            cost: Decimal.new("1")
          },
          %{
            product_variant_id: p5.id,
            quantity: 2,
            cost: Decimal.new("0.5")
          }
        ]
      }

      Rms.Commerce.Products.create_gift_handler_configuration(org.id, attrs)

      attrs = %{
        organization_id: org.id,
        product_variant_id: p2.id,
        gifts: [
          %{
            product_variant_id: p4.id,
            quantity: 2,
            cost: 1
          }
        ]
      }

      Rms.Commerce.Products.create_gift_handler_configuration(org.id, attrs)

      cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("222.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65")
      }

      expected_new_cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => p4.price,
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("1"),
                "product_variant_id" => p4.id,
                "quantity" => 4,
                "request_index" => 0,
                "selling_price" => Decimal.new("1"),
                "total_price" => Decimal.new("4"),
                "is_gift" => true
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("4.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("4.45"),
                "total_price" => Decimal.new("8.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => p5.price,
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("0.5"),
                "product_variant_id" => p5.id,
                "quantity" => 4,
                "request_index" => 0,
                "selling_price" => Decimal.new("0.5"),
                "total_price" => Decimal.new("2.0"),
                "is_gift" => true
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => p3.price,
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("1.0"),
                "product_variant_id" => p3.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("1.0"),
                "total_price" => Decimal.new("2.0"),
                "is_gift" => true
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("98.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("98.0"),
                "total_price" => Decimal.new("196.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_items_list_price" => Decimal.new("312.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("222.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65")
      }

      new_cart = GiftHandler.execute(org.id, cart)

      assert expected_new_cart["total_delivery_price"] == new_cart["total_delivery_price"]
      assert expected_new_cart["total_items_list_price"] == new_cart["total_items_list_price"]

      assert expected_new_cart["total_items_selling_price"] ==
               new_cart["total_items_selling_price"]

      assert expected_new_cart["total_manual_discount"] == new_cart["total_manual_discount"]
      assert expected_new_cart["total_price"] == new_cart["total_price"]

      assert expected_new_cart["total_ecommerce_discounts"] ==
               new_cart["total_ecommerce_discounts"]
    end
  end
end
