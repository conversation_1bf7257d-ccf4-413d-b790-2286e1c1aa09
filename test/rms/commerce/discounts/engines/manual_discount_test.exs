defmodule Rms.Commerce.Discounts.Engine.ManualDiscountTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Commerce.Discounts.Engine.ManualDiscount

  describe "execute/2" do
    test "apply discounts on itens" do
      org = insert(:organization)

      [p1, p2] =
        insert_list(2, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil,
            "discounts" => [
              %{
                "type" => "fixed",
                "value" => "3",
                "description" => nil
              }
            ]
          },
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil,
            "discounts" => [
              %{
                "type" => "percentage",
                "value" => "50",
                "description" => nil
              }
            ]
          }
        ],
        "postal_code" => "04088004",
        "discounts" => [],
        "country" => "BRA"
      }

      cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("222.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65")
      }

      expected_new_cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "discounts" => [
                  %{"description" => nil, "type" => "fixed", "value" => "3"}
                ],
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("9.90"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("4.95"),
                "total_manual_discounts" => Decimal.new("3.0"),
                "total_price" => Decimal.new("9.90")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "discounts" => [
                  %{"description" => nil, "type" => "percentage", "value" => "50"}
                ],
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.00"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("50.00"),
                "total_manual_discounts" => Decimal.new("100.00"),
                "total_price" => Decimal.new("100.00")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "discounts" => [
          %{"description" => "", "type" => "coupon", "value" => "TEST123"}
        ],
        "ecommerce" => "shopify",
        "location_id" => nil,
        "messages" => [],
        "shipping_address_id" => nil,
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_ecommerce_discounts" => Decimal.new("10.65"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_manual_discount" => Decimal.new("103.00"),
        "total_items_selling_price" => Decimal.new("109.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("119.25")
      }

      assert expected_new_cart ==
               ManualDiscount.execute(org.id, cart_info, cart)
    end

    test "does not apply discounts on itens when does not find them" do
      org = insert(:organization)

      [p1, p2] =
        insert_list(2, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil,
            "discounts" => [
              %{
                "type" => "fixed",
                "value" => "3",
                "description" => nil
              }
            ]
          },
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil,
            "discounts" => [
              %{
                "type" => "percentage",
                "value" => "50",
                "description" => nil
              }
            ]
          }
        ],
        "postal_code" => "04088004",
        "discounts" => [],
        "country" => "BRA"
      }

      cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("222.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65")
      }

      expected_new_cart = %{
        "customer_id" => nil,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "discounts" => [
                  %{"description" => nil, "type" => "percentage", "value" => "50"}
                ],
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "estimatedCost" => %{"amount" => "20.0"},
                  "handle" => "a1130253b6d3329db24887f2a16ff61a",
                  "title" => "Standard"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.00"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("50.00"),
                "total_manual_discounts" => Decimal.new("100.00"),
                "total_price" => Decimal.new("100.00")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "discounts" => [
          %{"description" => "", "type" => "coupon", "value" => "TEST123"}
        ],
        "ecommerce" => "shopify",
        "location_id" => nil,
        "messages" => [],
        "shipping_address_id" => nil,
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("20.00"),
        "total_ecommerce_discounts" => Decimal.new("10.65"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_manual_discount" => Decimal.new("100.00"),
        "total_items_selling_price" => Decimal.new("112.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("122.25")
      }

      assert expected_new_cart ==
               ManualDiscount.execute(org.id, cart_info, cart)
    end
  end
end
