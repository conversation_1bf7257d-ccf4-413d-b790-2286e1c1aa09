defmodule Rms.Commerce.Fulfillments.BuildFulfillmentsTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Commerce.Fulfillments.BuildFulfillments

  import Rms.Factory

  describe "shopify build_fulfillments/2" do
    test "correctly builds a fulfillment for each delivery method" do
      org = insert(:organization)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      order_attrs = %{
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "in-store",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: nil
          }
        ]
      }

      assert {:ok, order} = BuildFulfillments.execute(order_attrs, org.id)
      assert length(order.fulfillments) == 3

      Enum.each(order.fulfillments, fn fulfillment ->
        item = Enum.at(fulfillment.line_items, 0)
        assert length(fulfillment.line_items) == 1
        assert fulfillment.ecommerce == "shopify"
        assert fulfillment.shipping_method == item.shipping_method
        assert fulfillment.shipping_settings.settings == item.shipping_settings
      end)
    end

    test "correctly builds a fulfillment for each delivery method and delivery settings" do
      org = insert(:organization)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      order_attrs = %{
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "in-store",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "deliveryMethodType" => "PICK_UP",
              "estimatedCost" => %{
                "amount" => "100.1"
              },
              "handle" => "fd2120274e392769c872754fb2eac8f2",
              "title" => "Carro"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "deliveryMethodType" => "PICK_UP",
              "estimatedCost" => %{
                "amount" => "100.1"
              },
              "handle" => "fd2120274e392769c872754fb2eac8f2",
              "title" => "Carro"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "fd2120274e392769c872754fb2eac8g2",
              "title" => "Carro"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "fd2120274e392769c872754fb2eac8f2",
              "title" => "Carro"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "fd2120274e392769c872754fb2eac8g2",
              "title" => "Carro"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "fd2120274e392769c872754fb2eac8a2",
              "title" => "Carro"
            }
          }
        ]
      }

      assert {:ok, order} = BuildFulfillments.execute(order_attrs, org.id)
      assert length(order.fulfillments) == 6

      Enum.each(order.fulfillments, fn fulfillment ->
        item = Enum.at(fulfillment.line_items, 0)
        assert fulfillment.ecommerce == "shopify"
        assert fulfillment.shipping_method == item.shipping_method
        assert fulfillment.shipping_settings.settings == item.shipping_settings
      end)
    end

    test "returns a error when there is a invalid shipping method" do
      org = insert(:organization)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      order_attrs = %{
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "in-store",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "motoboy",
            shipping_settings: nil
          }
        ]
      }

      assert {:error, "shipping method not suported in fullfilments creation"} =
               BuildFulfillments.execute(order_attrs, org.id)
    end
  end

  describe "vtex build_fulfillments/2" do
    test "correctly build a fulfillment for each delivery method" do
      org = insert(:organization)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      order_attrs = %{
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "in-store",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (2)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (2)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Express",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Express",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          }
        ]
      }

      assert {:ok, order} = BuildFulfillments.execute(order_attrs, org.id)
      assert length(order.fulfillments) == 5

      Enum.each(order.fulfillments, fn fulfillment ->
        assert Enum.all?(fulfillment.line_items, fn line_item ->
                 line_item.shipping_method == fulfillment.shipping_method
               end)
      end)
    end

    test "returns a error when there is a invalid shipping method" do
      org = insert(:organization)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      order_attrs = %{
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "in-store",
            shipping_settings: nil
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (2)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "local-pickup",
            shipping_settings: %{
              "id" => "Retirada em Loja (2)",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "delivery",
            shipping_settings: %{
              "id" => "Express",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          },
          %{
            product_variant_id: pv.id,
            quantity: 1,
            shipping_method: "motoboy",
            shipping_settings: %{
              "id" => "Express",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 2250,
              "listPrice" => 3298,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            }
          }
        ]
      }

      assert {:error, "shipping method not suported in fullfilments creation"} =
               BuildFulfillments.execute(order_attrs, org.id)
    end
  end
end
