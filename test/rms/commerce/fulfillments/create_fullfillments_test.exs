defmodule Rms.Commerce.Fulfillments.CreateFulfillmentsTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Commerce.Fulfillments.CreateFulfillments

  import Rms.Factory

  setup do
    Mox.stub_with(Rms.Clients.FeatureFlag.Mock, FeatureFlagStub)
    :ok
  end

  describe "no ecommerce create_fulfillments/2" do
    test "returns a error when there is no ecommerce config" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order = insert(:order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      f1 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "in-store"
        )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      f2 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "local-pickup"
        )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      f3 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      f4 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "motoboy"
        )

      insert(:line_item,
        fulfillment: f4,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "motoboy"
      )

      assert {:error,
              %{
                reason: "No organization ecommerce found",
                stacktrace: "Elixir.Rms.Commerce.Fulfillments.CreateFulfillments"
              }} = CreateFulfillments.execute(order.id, org.id)

      assert [] = all_enqueued(worker: Rms.Workers.CreateEcommerceOrderWorker)
    end
  end

  describe "shopify create_fulfillments/2" do
    alias Rms.Integrations.Shopify

    test "correctly create a fulfillment for each delivery method" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order = insert(:order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      f1 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "in-store"
        )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      f2 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "local-pickup"
        )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      f3 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      assert {:ok, _} = CreateFulfillments.execute(order.id, org.id)
      fulfillments = Rms.Commerce.Fulfillments.list_fulfillments(org.id)
      assert length(fulfillments) == 3

      Enum.each(fulfillments, fn fulfillment ->
        args = %{"id" => fulfillment.id, "organization_id" => fulfillment.organization_id}

        if fulfillment.shipping_method in ["in-store", "delivery"] do
          assert_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          assert_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        else
          refute_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          refute_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        end
      end)
    end

    test "correctly create a fulfillment for each delivery method and delivery settings" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order = insert(:order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      f1 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "in-store"
        )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        shipping_settings: nil
      )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        shipping_settings: nil
      )

      f2 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "local-pickup"
        )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup",
        shipping_settings: %{
          "deliveryMethodType" => "PICK_UP",
          "estimatedCost" => %{
            "amount" => "100.1"
          },
          "handle" => "fd2120274e392769c872754fb2eac8f2",
          "title" => "Carro"
        }
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup",
        shipping_settings: %{
          "deliveryMethodType" => "PICK_UP",
          "estimatedCost" => %{
            "amount" => "100.0"
          },
          "handle" => "fd2120274e392769c872754fb2eac8f2",
          "title" => "Carro"
        }
      )

      f3 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: %{
          "deliveryMethodType" => "SHIPPING",
          "estimatedCost" => %{
            "amount" => "100.0"
          },
          "handle" => "fd2120274e392769c872754fb2eac8g2",
          "title" => "Carro"
        }
      )

      f4 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f4,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: %{
          "deliveryMethodType" => "SHIPPING",
          "estimatedCost" => %{
            "amount" => "100.0"
          },
          "handle" => "fd2120274e392769c872754fb2eac8f2",
          "title" => "Carro"
        }
      )

      f5 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f5,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: %{
          "deliveryMethodType" => "SHIPPING",
          "estimatedCost" => %{
            "amount" => "100.0"
          },
          "handle" => "fd2120274e392769c872754fb2eac8g2",
          "title" => "Carro"
        }
      )

      f6 =
        insert(:fulfillment,
          order: order,
          organization: org,
          shipping_method: "delivery"
        )

      insert(:line_item,
        fulfillment: f6,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: %{
          "deliveryMethodType" => "SHIPPING",
          "estimatedCost" => %{
            "amount" => "100.0"
          },
          "handle" => "fd2120274e392769c872754fb2eac8a2",
          "title" => "Carro"
        }
      )

      assert {:ok, _} = CreateFulfillments.execute(order.id, org.id)
      fulfillments = Rms.Commerce.Fulfillments.list_fulfillments(org.id)
      assert length(fulfillments) == 6

      Enum.each(fulfillments, fn fulfillment ->
        args = %{"id" => fulfillment.id, "organization_id" => fulfillment.organization_id}

        if fulfillment.shipping_method in ["in-store", "delivery"] do
          assert_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          assert_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        else
          refute_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          refute_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        end
      end)
    end
  end

  test "dont send order to shopify" do
    org = insert(:organization)
    loc = insert(:location, organization: org)

    order = insert(:order, organization: org)

    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    env_params = %{data: "shopify"}

    insert(:organization_setting,
      key: "ecommerce",
      value: env_params,
      organization: org
    )

    insert(:organization_setting,
      key: "send_order_to_shopify",
      value: %{data: false},
      organization: org
    )

    f1 =
      insert(:fulfillment,
        order: order,
        organization: org,
        shipping_method: "in-store"
      )

    insert(:line_item,
      fulfillment: f1,
      organization: org,
      product_variant: pv,
      location: loc,
      shipping_method: "in-store",
      shipping_settings: nil
    )

    insert(:line_item,
      fulfillment: f1,
      organization: org,
      product_variant: pv,
      location: loc,
      shipping_method: "in-store",
      shipping_settings: nil
    )

    # Valida que a função retorna erro, pois não deve enviar para o Shopify
    assert {:ok, _} = CreateFulfillments.execute(order.id, org.id)

    args = %{"id" => f1.id, "organization_id" => f1.organization_id}

    refute_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
    refute_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
  end
end
