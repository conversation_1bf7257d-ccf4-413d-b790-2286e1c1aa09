defmodule Rms.Commerce.Fulfillments.CreditGeneratorTest do
  use Rms.DataCase, async: true

  import Rms.Factory

  alias Rms.Repo
  alias Rms.Commerce.Fulfillments.CreditGenerator

  describe "generate_credit/2" do
    setup do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      location = insert(:location, organization: organization)

      # Create line items
      product = insert(:product, organization: organization)
      product_variant = insert(:product_variant, product: product, organization: organization)

      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      # Create reverse fulfillment
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      {:ok,
       %{
         organization: organization,
         customer: customer,
         reverse_fulfillment: reverse_fulfillment,
         line_item: line_item,
         location: location
       }}
    end

    test "generates credit for a reverse fulfillment", %{
      organization: organization,
      customer: customer,
      reverse_fulfillment: fulfillment,
      line_item: line_item
    } do
      assert fulfillment.status == "pending"
      {:ok, [credit, _]} = CreditGenerator.generate_credit(fulfillment, customer.id)

      # Test credit amount calculated correctly
      assert credit.amount == Decimal.mult(line_item.price, Decimal.new("2"))
      assert credit.customer_id == customer.id
      assert credit.organization_id == organization.id
      assert credit.reason == "return"
      assert credit.reverse_fulfillment_id == fulfillment.id

      # Test metadata
      assert credit.metadata.reference.type == "reverse_fulfillment"
      assert credit.metadata.reference.id == fulfillment.id

      [item_metadata] = credit.metadata.items
      assert item_metadata.line_item_id == line_item.id
      assert item_metadata.returned_quantity == 2
      assert item_metadata.price == line_item.price
      assert item_metadata.reason == "defective"

      # Test reverse_fulfillment association
      credit = Repo.preload(credit, :reverse_fulfillment)
      assert credit.reverse_fulfillment.id == fulfillment.id

      # Test status update
      fulfillment = Repo.reload!(fulfillment)
      assert fulfillment.status == "processing"
    end

    test "enforces uniqueness of credits per reverse fulfillment", %{
      customer: customer,
      reverse_fulfillment: fulfillment
    } do
      {:ok, _credit} = CreditGenerator.generate_credit(fulfillment, customer.id)

      fulfillment = Repo.reload!(fulfillment)
      assert {:error, changeset} = CreditGenerator.generate_credit(fulfillment, customer.id)
      assert "has already been taken" in errors_on(changeset).reverse_fulfillment_id
    end

    test "does not enforce uniqueness for cancelled credits", %{
      customer: customer,
      reverse_fulfillment: fulfillment
    } do
      {:ok, [credit, _]} = CreditGenerator.generate_credit(fulfillment, customer.id)

      # Cancel the credit
      Repo.update!(Ecto.Changeset.change(credit, status: "cancelled"))

      # Should be able to generate a new credit
      fulfillment = Repo.reload!(fulfillment)
      assert {:ok, [new_credit, _]} = CreditGenerator.generate_credit(fulfillment, customer.id)
      assert new_credit.reverse_fulfillment_id == fulfillment.id
    end

    test "calculates credit total for multiple items", %{
      organization: organization,
      customer: customer,
      location: location
    } do
      product = insert(:product, organization: organization)
      pv1 = insert(:product_variant, product: product, organization: organization)
      pv2 = insert(:product_variant, product: product, organization: organization)

      line_item1 =
        insert(:line_item,
          organization: organization,
          product_variant: pv1,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      line_item2 =
        insert(:line_item,
          organization: organization,
          product_variant: pv2,
          price: Decimal.new("20.00"),
          quantity: 1,
          location: location
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item1,
              returned_quantity: 2,
              reason: "defective"
            ),
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item2,
              returned_quantity: 1,
              reason: "defective"
            )
          ]
        )

      {:ok, [credit, _]} = CreditGenerator.generate_credit(fulfillment, customer.id)

      # 2 x $10 + 1 x $20 = $40 total credit
      assert Decimal.eq?(credit.amount, Decimal.new("40.00"))
    end

    test "formats items correctly", %{
      line_item: line_item,
      reverse_fulfillment: fulfillment
    } do
      [formatted_item] = CreditGenerator.format_items(fulfillment.line_items)

      assert formatted_item.line_item_id == line_item.id
      assert formatted_item.returned_quantity == 2
      assert formatted_item.reason == "defective"
      assert formatted_item.price == line_item.price
    end
  end
end
