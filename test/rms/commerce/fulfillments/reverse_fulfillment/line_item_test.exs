defmodule Rms.Commerce.Fulfillments.ReverseFulfillment.LineItemTest do
  use Rms.DataCase, async: true

  alias Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem

  describe "changeset/2" do
    setup do
      {:ok, organization_id: 1, line_item_id: 2}
    end

    test "validates required fields" do
      changeset = LineItem.changeset(%LineItem{}, %{})

      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).returned_quantity
      assert "can't be blank" in errors_on(changeset).organization_id
      assert "can't be blank" in errors_on(changeset).line_item_id
    end

    test "validates returned_quantity must be greater than 0", %{
      organization_id: organization_id,
      line_item_id: line_item_id
    } do
      attrs = %{
        returned_quantity: 0,
        organization_id: organization_id,
        line_item_id: line_item_id
      }

      changeset = LineItem.changeset(%LineItem{}, attrs)

      refute changeset.valid?
      assert "must be greater than 0" in errors_on(changeset).returned_quantity
    end

    test "creates valid changeset with all fields", %{
      organization_id: organization_id,
      line_item_id: line_item_id
    } do
      attrs = %{
        returned_quantity: 2,
        reason: "defective",
        metadata: %{note: "customer complaint"},
        reverse_fulfillment_id: Ecto.UUID.generate(),
        organization_id: organization_id + 1,
        line_item_id: line_item_id
      }

      changeset = LineItem.changeset(%LineItem{organization_id: organization_id}, attrs)

      assert changeset.valid?
      assert get_change(changeset, :returned_quantity) == 2
      assert get_change(changeset, :reason) == "defective"
      assert get_change(changeset, :metadata) == %{note: "customer complaint"}
      refute get_change(changeset, :organization_id)
      assert get_change(changeset, :line_item_id) == line_item_id
    end
  end

  describe "cancel_changeset/1" do
    setup do
      organization_id = 1

      line_item = %LineItem{
        organization_id: organization_id,
        returned_quantity: 5,
        metadata: %{"some" => "data"}
      }

      {:ok, line_item: line_item}
    end

    test "sets returned_quantity to 0 and stores original in metadata", %{line_item: line_item} do
      changeset = LineItem.cancel_changeset(line_item)

      assert changeset.valid?
      assert get_change(changeset, :returned_quantity) == 0
      assert get_in(get_change(changeset, :metadata), [:original_returned_quantity]) == 5
      assert get_in(get_change(changeset, :metadata), ["some"]) == "data"
      assert %DateTime{} = get_in(get_change(changeset, :metadata), [:cancelled_at])
    end

    test "handles nil metadata", %{line_item: line_item} do
      line_item = %{line_item | metadata: nil}
      changeset = LineItem.cancel_changeset(line_item)

      assert changeset.valid?
      assert get_change(changeset, :returned_quantity) == 0
      assert get_in(get_change(changeset, :metadata), [:original_returned_quantity]) == 5
      assert %DateTime{} = get_in(get_change(changeset, :metadata), [:cancelled_at])
    end
  end
end
