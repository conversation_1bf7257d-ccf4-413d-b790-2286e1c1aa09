defmodule Rms.Commerce.Fulfillments.ReverseFulfillmentTest do
  use Rms.DataCase

  alias Rms.Commerce.Fulfillments.ReverseFulfillment

  describe "cancel_changeset/2" do
    test "allows cancellation from pending status" do
      reverse_fulfillment = %ReverseFulfillment{
        status: "pending",
        iglu_credits: [],
        line_items: []
      }

      changeset = ReverseFulfillment.cancel_changeset(reverse_fulfillment)

      assert changeset.valid?
      assert get_change(changeset, :status) == "cancelled"
    end

    test "allows cancellation from processing status" do
      reverse_fulfillment = %ReverseFulfillment{
        status: "processing",
        iglu_credits: [],
        line_items: []
      }

      changeset = ReverseFulfillment.cancel_changeset(reverse_fulfillment)

      assert changeset.valid?
      assert get_change(changeset, :status) == "cancelled"
    end

    test "does not allow cancellation from completed status" do
      reverse_fulfillment = %ReverseFulfillment{
        status: "completed",
        iglu_credits: [],
        line_items: []
      }

      changeset = ReverseFulfillment.cancel_changeset(reverse_fulfillment)

      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).status
    end

    test "ignores provided status and always sets cancelled" do
      reverse_fulfillment = %ReverseFulfillment{
        status: "pending",
        iglu_credits: [],
        line_items: []
      }

      changeset = ReverseFulfillment.cancel_changeset(reverse_fulfillment, %{status: "completed"})

      assert changeset.valid?
      assert get_change(changeset, :status) == "cancelled"
    end
  end

  describe "optimistic locking" do
    test "increments version number on successful update" do
      organization = insert(:organization)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "pending",
          version: 1
        )

      changeset =
        ReverseFulfillment.update_changeset(reverse_fulfillment, %{status: "processing"})

      assert {:ok, updated} = Rms.Repo.update(changeset)
      assert updated.version == 2
    end

    test "prevents concurrent updates with stale version" do
      organization = insert(:organization)
      staff = insert(:staff, organization: organization)

      initial_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "pending",
          version: 1,
          staff: staff
        )

      # First update succeeds
      {:ok, updated} =
        ReverseFulfillment.update_changeset(initial_fulfillment, %{status: "processing"})
        |> Rms.Repo.update()

      assert updated.version == 2

      # Second update with stale version fails
      changeset = ReverseFulfillment.update_changeset(initial_fulfillment, %{status: "cancelled"})

      assert_raise Ecto.StaleEntryError, fn ->
        Rms.Repo.update(changeset)
      end
    end
  end
end
