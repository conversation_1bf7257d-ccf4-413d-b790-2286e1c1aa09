defmodule Rms.Commerce.FulfillmentsTest do
  alias Rms.Commerce.Fulfillments
  alias Rms.Commerce.Fulfillments.ReverseFulfillment

  use Rms.DataCase
  use Rms.EventsCase

  import Rms.Factory

  use ExUnit.Case

  describe "list_fulfillments/3" do
    test "list all fulfillments from a organization" do
      org1 = insert(:organization)
      loc1 = insert(:location, organization: org1)

      order1 = insert(:order, organization: org1)
      product1 = insert(:product, organization: org1)
      pv1 = insert(:product_variant, product: product1, organization: org1)

      insert_list(5, :fulfillment,
        organization: org1,
        order: order1,
        shipping_method: "in-store",
        shipping_settings: %{
          organization: org1,
          price: 10.01,
          time_period: "10",
          ecommerce: "vinco",
          settings: %{"map" => "map"}
        },
        line_items:
          insert_list(
            2,
            :line_item,
            organization: org1,
            product_variant: pv1,
            location: loc1
          )
      )

      org2 = insert(:organization)
      loc2 = insert(:location, organization: org2)

      order2 = insert(:order, organization: org2)
      product2 = insert(:product, organization: org2)
      pv2 = insert(:product_variant, product: product2, organization: org2)

      insert_list(3, :fulfillment,
        organization: org2,
        order: order2,
        shipping_method: "in-store",
        shipping_settings: %{
          organization: org2,
          price: 10.01,
          time_period: "10",
          ecommerce: "vinco",
          settings: %{"map" => "map"}
        },
        line_items:
          insert_list(
            2,
            :line_item,
            organization: org2,
            product_variant: pv2,
            location: loc2
          )
      )

      fulfillments = Fulfillments.list_fulfillments(org1.id)
      assert length(fulfillments) == 5
    end

    test "list all fulfillments from a organization using filters" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order1 = insert(:order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:fulfillment,
        organization: org,
        order: order1,
        shipping_method: "in-store",
        shipping_settings: %{
          organization: org,
          price: 10.01,
          time_period: "10",
          ecommerce: "vinco",
          settings: %{"map" => "map"}
        },
        line_items:
          insert_list(
            2,
            :line_item,
            organization: org,
            product_variant: pv,
            location: loc
          )
      )

      order2 = insert(:order, organization: org)

      insert(:fulfillment,
        organization: org,
        order: order2,
        shipping_method: "in-store",
        shipping_settings: %{
          organization: org,
          price: 10.01,
          time_period: "10",
          ecommerce: "vinco",
          settings: %{"map" => "map"}
        },
        line_items:
          insert_list(
            2,
            :line_item,
            organization: org,
            product_variant: pv,
            location: loc
          )
      )

      fulfillments = Fulfillments.list_fulfillments(org.id, query_params: [order_id: order1.id])
      assert length(fulfillments) == 1

      assert Enum.all?(fulfillments, fn fulfillment ->
               fulfillment.order_id == order1.id
             end)
    end
  end

  describe "get_fulfillment!/2" do
    test "returns a fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order1 = insert(:order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order1,
          shipping_method: "in-store",
          shipping_settings: %{
            organization: org,
            price: 10.01,
            time_period: "10",
            ecommerce: "vinco",
            settings: %{"map" => "map"}
          },
          line_items:
            insert_list(
              2,
              :line_item,
              organization: org,
              product_variant: pv,
              location: loc
            )
        )

      assert f = Fulfillments.get_fulfillment!(org.id, fulfillment.id)

      assert f.id == fulfillment.id
      assert f.shipping_method == fulfillment.shipping_method
    end

    test "raises Ecto.NoResultsError when fulfillment does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Fulfillments.get_fulfillment!(org.id, 1)
      end
    end
  end

  describe "delete_fulfillment/1" do
    test "deletes an archived location" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order1 = insert(:order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order1,
          shipping_method: "in-store",
          shipping_settings: %{
            organization: org,
            price: 10.01,
            time_period: "10",
            ecommerce: "vinco",
            settings: %{"map" => "map"}
          },
          line_items:
            insert_list(
              2,
              :line_item,
              organization: org,
              product_variant: pv,
              location: loc
            )
        )

      assert {:ok, fulfillment} = Fulfillments.delete_fulfillment(fulfillment)

      assert_raise Ecto.NoResultsError, fn ->
        Fulfillments.get_fulfillment!(org.id, fulfillment.id)
      end
    end
  end

  describe "update_fulfillment/2" do
    test "Only update external reference field" do
      org = insert(:organization)
      another_org = insert(:organization)
      loc = insert(:location, organization: org)

      another_order = insert(:order, organization: org)

      order1 = insert(:order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order1,
          shipping_method: "in-store",
          shipping_settings: %{
            organization: org,
            price: 10.01,
            time_period: "10",
            ecommerce: "vinco",
            settings: %{"map" => "map"}
          },
          line_items:
            insert_list(
              2,
              :line_item,
              organization: org,
              product_variant: pv,
              location: loc
            )
        )

      update_attrs = %{
        external_reference: "oi",
        status: "done",
        shipping_method: "local-pickup",
        organization_id: another_org.id,
        order_id: another_order.id
      }

      assert {:ok, updated_fulfillment} =
               Fulfillments.update_fulfillment(fulfillment, update_attrs)

      assert updated_fulfillment.status == update_attrs.status
      assert updated_fulfillment.external_reference == update_attrs.external_reference
      refute updated_fulfillment.shipping_method == update_attrs.shipping_method
      refute updated_fulfillment.order_id == update_attrs.order_id
      refute updated_fulfillment.organization_id == update_attrs.organization_id
    end
  end

  describe "update_reverse_fulfillment_status/2" do
    test "updates the status of a reverse fulfillment" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "pending",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      assert {:ok, updated_fulfillment} =
               Fulfillments.update_reverse_fulfillment_status(reverse_fulfillment, "processing")

      assert updated_fulfillment.status == "processing"
    end

    test "returns error when updating to invalid status" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "pending",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      assert {:error, changeset} =
               Fulfillments.update_reverse_fulfillment_status(
                 reverse_fulfillment,
                 "invalid_status"
               )

      assert "is invalid" in errors_on(changeset).status
    end

    test "respects allowed state transitions" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "pending",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      # Should be able to go from pending to processing
      assert {:ok, processing} =
               Fulfillments.update_reverse_fulfillment_status(reverse_fulfillment, "processing")

      # Should be able to go from processing to completed
      assert {:ok, completed} =
               Fulfillments.update_reverse_fulfillment_status(processing, "completed")

      # Should not be able to go from completed back to processing
      assert {:error, changeset} =
               Fulfillments.update_reverse_fulfillment_status(completed, "processing")

      assert "is invalid" in errors_on(changeset).status
    end
  end

  describe "complete_reverse_fulfillment/1" do
    test "successfully completes a reverse fulfillment and emits event" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "processing",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      assert {:ok, completed_fulfillment} =
               Fulfillments.complete_reverse_fulfillment(reverse_fulfillment)

      assert completed_fulfillment.status == "completed"

      # Verify event was emitted
      assert_emit(
        "reverse_fulfillment.completed",
        ReverseFulfillment.reverse_fulfillment_payload(completed_fulfillment)
      )
    end

    test "returns error when trying to complete cancelled fulfillment" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "cancelled",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 2,
              reason: "defective"
            )
          ]
        )

      assert {:error, changeset} = Fulfillments.complete_reverse_fulfillment(reverse_fulfillment)
      assert "is invalid" in errors_on(changeset).status
    end
  end

  describe "cancel_reverse_fulfillment/1" do
    test "successfully cancels a reverse fulfillment" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      product = insert(:product, organization: org)
      variant = insert(:product_variant, product: product, organization: org)

      # Create line item with specific returned quantity
      line_item =
        insert(:line_item,
          organization: org,
          product_variant: variant,
          quantity: 10,
          location: build(:location, organization: org)
        )

      reverse_line_item =
        build(:reverse_fulfillment_line_item,
          organization: org,
          line_item: line_item,
          returned_quantity: 5,
          metadata: %{"original_data" => "value"}
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          line_items: [reverse_line_item]
        )

      # Create associated credit
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: customer,
          reverse_fulfillment: fulfillment,
          status: "available"
        )

      assert {:ok, cancelled_fulfillment} = Fulfillments.cancel_reverse_fulfillment(fulfillment)
      assert cancelled_fulfillment.status == "cancelled"

      # Verify associated credit was cancelled
      credit = Rms.Repo.reload!(credit)
      assert credit.status == "cancelled"

      # Verify line item was properly cancelled
      [cancelled_line_item] = cancelled_fulfillment.line_items
      assert cancelled_line_item.returned_quantity == 0
      assert cancelled_line_item.metadata[:original_returned_quantity] == 5
      assert cancelled_line_item.metadata["original_data"] == "value"
      assert %DateTime{} = cancelled_line_item.metadata[:cancelled_at]
    end

    test "successfully cancels a reverse fulfillment with associated payment" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      product = insert(:product, organization: org)
      variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: variant,
          location: build(:location, organization: org)
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item
            )
          ]
        )

      # Create associated credit that has been used in a payment
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: customer,
          reverse_fulfillment: fulfillment,
          status: "used"
        )

      # Create a payment using the credit
      payment =
        insert(:payment,
          organization: org,
          status: "settled",
          method: "return_credit"
        )

      insert(:iglu_credit_payment, organization: org, payment: payment, iglu_credit: credit)

      assert {:ok, cancelled_fulfillment} = Fulfillments.cancel_reverse_fulfillment(fulfillment)
      assert cancelled_fulfillment.status == "cancelled"

      # Verify associated credit remains used since payment exists
      credit = Rms.Repo.reload!(credit)
      assert credit.status == "cancelled"

      payment = Rms.Repo.reload!(payment)
      assert payment.status == "canceled"

      # Verify event was emitted
      assert_emit(
        "reverse_fulfillment.cancelled",
        ReverseFulfillment.reverse_fulfillment_payload(cancelled_fulfillment)
      )
    end

    test "returns error when payment cancellation fails" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      product = insert(:product, organization: org)
      variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: variant,
          location: build(:location, organization: org)
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item
            )
          ]
        )

      # Create associated credit that has been used in a payment
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: customer,
          reverse_fulfillment: fulfillment,
          status: "used"
        )

      # Create a payment that will fail to cancel
      payment =
        insert(:payment,
          organization: org,
          status: "settled",
          method: "return_credit",
          transaction: build(:transaction, organization: org, status: "done")
        )

      insert(:iglu_credit_payment, organization: org, payment: payment, iglu_credit: credit)

      assert {:error, :payment_cancellation_failed} =
               Fulfillments.cancel_reverse_fulfillment(fulfillment)

      # Verify nothing was cancelled
      credit = Rms.Repo.reload!(credit)
      assert credit.status == "used"

      payment = Rms.Repo.reload!(payment)
      assert payment.status == "settled"
    end

    test "returns error when trying to cancel completed fulfillment" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: variant,
          location: build(:location, organization: org)
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "completed",
          location: build(:location, organization: org),
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item
            )
          ]
        )

      assert {:error, changeset} = Fulfillments.cancel_reverse_fulfillment(fulfillment)
      assert "is invalid" in errors_on(changeset).status
    end
  end

  describe "create_reverse_fulfillment/2" do
    test "successfully creates a reverse fulfillment" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      staff = insert(:staff, organization: org)
      customer = insert(:customer, organization: org)

      line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 2,
          "reason" => "defective"
        }
      ]

      assert {:ok, reverse_fulfillment} =
               Fulfillments.create_reverse_fulfillment(
                 org.id,
                 line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert reverse_fulfillment.organization_id == org.id
      assert length(reverse_fulfillment.line_items) == 1
      assert hd(reverse_fulfillment.line_items).returned_quantity == 2
      assert hd(reverse_fulfillment.line_items).reason == "defective"
      assert hd(reverse_fulfillment.line_items).line_item_id == line_item.id

      # Verify event was emitted
      assert_emit(
        "reverse_fulfillment.created",
        ReverseFulfillment.reverse_fulfillment_payload(reverse_fulfillment)
      )
    end

    test "fails to create a reverse fulfillment with invalid data" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)
      customer = insert(:customer, organization: org)

      line_items = [
        %{
          "line_item_id" => 123_456,
          "returned_quantity" => 2,
          "reason" => "defective"
        }
      ]

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 org.id,
                 line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert [line_item_error] = errors_on(changeset).line_items
      assert "does not exist" in line_item_error.line_item
    end

    test "successfully creates a reverse fulfillment with nil staff_id" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 1,
          "reason" => "accidental purchase"
        }
      ]

      assert {:ok, reverse_fulfillment} =
               Fulfillments.create_reverse_fulfillment(
                 org.id,
                 line_items,
                 location.id,
                 # staff_id
                 nil,
                 customer.id
               )

      assert reverse_fulfillment.organization_id == org.id
      assert reverse_fulfillment.location_id == location.id
      assert reverse_fulfillment.customer_id == customer.id
      assert reverse_fulfillment.staff_id == nil
      assert length(reverse_fulfillment.line_items) == 1
      assert hd(reverse_fulfillment.line_items).returned_quantity == 1
      assert hd(reverse_fulfillment.line_items).reason == "accidental purchase"
      assert hd(reverse_fulfillment.line_items).line_item_id == line_item.id

      assert_emit(
        "reverse_fulfillment.created",
        ReverseFulfillment.reverse_fulfillment_payload(reverse_fulfillment)
      )
    end

    test "fails when returned quantity is greater than original quantity" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)
      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)
      customer = insert(:customer, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 2,
          location: location
        )

      line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 3,
          "reason" => "defective"
        }
      ]

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 org.id,
                 line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert [line_item_error] = errors_on(changeset).line_items

      assert "must be less than or equal to available quantity" in line_item_error.returned_quantity
    end
  end

  describe "get_reverse_fulfillment_line_items_with_skus/2" do
    test "returns line items with SKUs for a reverse fulfillment" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      # Create products with specific SKUs
      product1 = insert(:product, organization: org)
      product2 = insert(:product, organization: org)

      product_variant1 = insert(:product_variant, product: product1, organization: org)
      product_variant2 = insert(:product_variant, product: product2, organization: org)

      # Create line items with specific SKUs
      line_item1 =
        insert(:line_item,
          organization: org,
          product_variant: product_variant1,
          price: Decimal.new("10.00"),
          quantity: 5,
          location: location,
          sku: "SKU123"
        )

      line_item2 =
        insert(:line_item,
          organization: org,
          product_variant: product_variant2,
          price: Decimal.new("15.00"),
          quantity: 3,
          location: location,
          sku: "SKU456"
        )

      # Create a reverse fulfillment with multiple line items
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "pending",
          location: location,
          staff: staff,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item1,
              returned_quantity: 2,
              reason: "defective",
              return_to_inventory: true
            ),
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item2,
              returned_quantity: 1,
              reason: "customer_changed_mind",
              return_to_inventory: false
            )
          ]
        )

      # Get line items with SKUs
      line_items_with_skus =
        Fulfillments.get_reverse_fulfillment_line_items_with_skus(
          org.id,
          reverse_fulfillment.id
        )

      # Verify results
      assert length(line_items_with_skus) == 2

      # Find items by SKU for verification
      sku123_item = Enum.find(line_items_with_skus, &(&1.sku == "SKU123"))
      sku456_item = Enum.find(line_items_with_skus, &(&1.sku == "SKU456"))

      # Verify first line item
      assert sku123_item != nil
      assert sku123_item.returned_quantity == 2
      assert sku123_item.line_item_id == line_item1.id
      assert sku123_item.return_to_inventory == true

      # Verify second line item
      assert sku456_item != nil
      assert sku456_item.returned_quantity == 1
      assert sku456_item.line_item_id == line_item2.id
      assert sku456_item.return_to_inventory == false
    end

    test "returns empty list when no line items match criteria" do
      org = insert(:organization)
      other_org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      # Create a product and line item
      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          organization: org,
          product_variant: product_variant,
          location: location,
          sku: "SKU123"
        )

      # Create a reverse fulfillment
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          location: location,
          staff: staff,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              # Zero quantity should be filtered out
              returned_quantity: 0,
              reason: "defective"
            )
          ]
        )

      # Test with wrong organization ID
      assert Fulfillments.get_reverse_fulfillment_line_items_with_skus(
               other_org.id,
               reverse_fulfillment.id
             ) == []

      # Test with correct organization but zero quantities
      assert Fulfillments.get_reverse_fulfillment_line_items_with_skus(
               org.id,
               reverse_fulfillment.id
             ) == []
    end

    test "only returns line items with positive returned quantities" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      staff = insert(:staff, organization: org)

      # Create products
      product1 = insert(:product, organization: org)
      product2 = insert(:product, organization: org)

      product_variant1 = insert(:product_variant, product: product1, organization: org)
      product_variant2 = insert(:product_variant, product: product2, organization: org)

      # Create line items
      line_item1 =
        insert(:line_item,
          organization: org,
          product_variant: product_variant1,
          location: location,
          sku: "SKU123",
          quantity: 2
        )

      line_item2 =
        insert(:line_item,
          organization: org,
          product_variant: product_variant2,
          location: location,
          sku: "SKU456"
        )

      # Create a reverse fulfillment with one positive and one zero quantity
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          location: location,
          staff: staff,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item1,
              returned_quantity: 2,
              reason: "defective"
            ),
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item2,
              # Should be filtered out
              returned_quantity: 0,
              reason: "customer_changed_mind"
            )
          ]
        )

      # Get line items with SKUs
      line_items_with_skus =
        Fulfillments.get_reverse_fulfillment_line_items_with_skus(
          org.id,
          reverse_fulfillment.id
        )

      # Verify only one item is returned
      assert length(line_items_with_skus) == 1
      assert hd(line_items_with_skus).sku == "SKU123"
      assert hd(line_items_with_skus).returned_quantity == 2
    end
  end
end
