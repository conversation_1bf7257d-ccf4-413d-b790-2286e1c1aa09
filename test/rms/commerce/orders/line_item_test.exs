defmodule Rms.Commerce.Orders.LineItemTest do
  use Rms.DataCase

  alias Rms.Commerce.Orders.LineItem

  describe "changeset/2" do
    test "with required fields" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "UPS",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: false)
      assert changeset.valid?
    end

    test "with required fields and shipping settings" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "UPS",
        shipping_settings: %{id: 1, name: "NORMAL"},
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: false)
      assert changeset.valid?
    end

    test "returns error on missing required fields" do
      attrs = %{
        price: nil,
        quantity: nil,
        shipping_method: nil,
        product_variant_id: nil,
        staff_id: nil,
        location_id: nil
      }

      changeset = LineItem.changeset(%LineItem{}, attrs)
      refute changeset.valid?
      errors = errors_on(changeset)
      assert "can't be blank" in errors.price
      assert "can't be blank" in errors.quantity
      assert "can't be blank" in errors.shipping_method
      assert "can't be blank" in errors.location_id
    end

    test "requires price_table opt to be passed" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "UPS",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: true)
      refute changeset.valid?
      assert "is not in price table" in errors_on(changeset).product_variant_id
    end

    test "validates if price matches price table" do
      attrs = %{
        price: Decimal.new("20.00"),
        quantity: 2,
        shipping_method: "UPS",
        product_variant_id: 2,
        staff_id: 5,
        location_id: 3
      }

      changeset =
        LineItem.changeset(%LineItem{}, attrs, price_table: %{2 => Decimal.new("20.00")})

      assert changeset.valid?
    end

    test "returns an error if price does not match price table" do
      attrs = %{
        price: Decimal.new("10.00"),
        quantity: 2,
        shipping_method: "UPS",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset =
        LineItem.changeset(%LineItem{}, attrs,
          validate_price: true,
          price_table: %{1 => %{price: Decimal.new("5.00"), list_price: Decimal.new("5.00")}}
        )

      refute changeset.valid?
      assert "does not match product variant price" in errors_on(changeset).price
    end

    test "does not require price to match product variant price when validate_price is false" do
      attrs = %{
        price: Decimal.new("10.00"),
        quantity: 2,
        shipping_method: "UPS",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset =
        LineItem.changeset(%LineItem{}, attrs,
          price_table: %{1 => %{price: Decimal.new("5.00"), list_price: Decimal.new("5.00")}},
          validate_price: false
        )

      assert changeset.valid?
    end

    test "return erro for delivery method when shipping settings is null" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "delivery",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: false)
      errors = errors_on(changeset)
      assert "can't be blank for delivery shipping_method" in errors.shipping_settings
      refute changeset.valid?
    end

    test "return erro for local-pickup method when shipping settings is null" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "local-pickup",
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: false)
      errors = errors_on(changeset)
      assert "can't be blank for local-pickup shipping_method" in errors.shipping_settings
      refute changeset.valid?
    end

    test "with shipping settings for delivery" do
      attrs = %{
        price: 10.00,
        quantity: 2,
        shipping_method: "delivery",
        shipping_settings: %{id: 1, name: "NORMAL"},
        product_variant_id: 1,
        staff_id: 5,
        location_id: 3
      }

      changeset = LineItem.changeset(%LineItem{}, attrs, validate_price: false)
      assert changeset.valid?
    end
  end
end
