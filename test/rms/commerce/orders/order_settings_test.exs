defmodule Rms.Commerce.Orders.OrderSettingsTest do
  use Rms.DataCase

  alias Rms.Commerce.Orders.OrderSettings

  describe "changeset/2" do
    test "validates at least one of prefix or suffix is present" do
      changeset = OrderSettings.changeset(%OrderSettings{}, %{prefix: "", suffix: ""})
      assert "at least one of prefix or suffix must be set" in errors_on(changeset).prefix

      changeset = OrderSettings.changeset(%OrderSettings{}, %{prefix: "PRE-"})
      assert changeset.valid?

      changeset = OrderSettings.changeset(%OrderSettings{}, %{suffix: "-SUF"})
      assert changeset.valid?
    end

    test "validates maximum length of prefix and suffix" do
      changeset =
        OrderSettings.changeset(%OrderSettings{}, %{prefix: "TOOLONG", suffix: "ALSOTO<PERSON>ONG"})

      errors = errors_on(changeset)
      assert "should be at most 5 character(s)" in errors[:prefix]
      assert "should be at most 5 character(s)" in errors[:suffix]
    end
  end
end
