defmodule Rms.Commerce.Orders.OrderTest do
  use Rms.DataCase

  alias Rms.Commerce.Orders.Order
  alias Rms.Repo

  describe "event_payload/1" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)
      cashier_staff = insert(:staff, organization: organization)

      insert(:staff_location,
        staff: cashier_staff,
        location: location,
        organization: organization
      )

      insert(:staff_role,
        staff: cashier_staff,
        organization: organization,
        seller: false,
        stocker: false,
        cashier: true
      )

      order =
        insert(:order,
          organization: organization,
          location: location,
          customer: customer,
          staff: staff,
          cashier: cashier_staff,
          total_discount: Decimal.new("10.00"),
          total_price: Decimal.new("90.00"),
          total_items_selling_price: Decimal.new("100.00")
        )

      {:ok,
       %{
         order: order,
         organization: organization,
         location: location,
         customer: customer,
         staff: staff
       }}
    end

    test "returns the correct payload for an order without associations", %{order: order} do
      payload = Order.event_payload(order)

      assert payload.id == order.id
      assert payload.delivery_price == order.total_delivery_price
      assert payload.discounts_total == order.total_discount

      assert payload.total_amount ==
               Decimal.sub(
                 order.total_items_selling_price || Decimal.new("0"),
                 order.total_discount || Decimal.new("0")
               )

      assert payload.status == order.status
      assert payload.inserted_at == order.inserted_at
      assert payload.external_id == order.external_id
      assert payload.organization_id == order.organization_id
      assert payload.staff != nil
      assert payload.cashier != nil
      assert payload.customer != nil
      assert payload.line_items == []
      assert payload.location_id == order.location_id
      assert payload.location != nil
      assert payload.transaction == nil
      assert payload.fulfillments == []
    end

    test "returns the correct payload for an order with all associations", %{order: order} do
      product_variant =
        insert(:product_variant,
          organization: order.organization,
          product: build(:product, organization: order.organization)
        )

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: order.organization,
          external_reference: "external_reference"
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          location: order.location,
          product_variant: product_variant,
          organization: order.organization
        )

      transaction = insert(:transaction, order: order, organization: order.organization)
      _payment = insert(:payment, transaction: transaction, organization: order.organization)

      _customer_transaction =
        insert(:transaction_customer,
          transaction: transaction,
          organization: order.organization
        )

      insert(:discount,
        organization: order.organization,
        order: order,
        type: "fixed",
        value: "10.00",
        description: "aaaa"
      )

      insert(:discount,
        organization: order.organization,
        order: order,
        type: "percentage",
        value: "10.00",
        description: "bbbb"
      )

      updated_order = Repo.preload(order, [:transaction, fulfillments: [:line_items]])

      payload = Order.event_payload(updated_order)

      assert payload.id == updated_order.id
      assert payload.delivery_price == updated_order.total_delivery_price
      assert payload.discounts_total == updated_order.total_discount

      assert payload.total_amount ==
               Decimal.sub(
                 updated_order.total_items_selling_price || Decimal.new("0"),
                 updated_order.total_discount || Decimal.new("0")
               )

      assert payload.status == updated_order.status
      assert payload.inserted_at == updated_order.inserted_at
      assert payload.external_id == updated_order.external_id
      assert payload.organization_id == updated_order.organization_id
      assert payload.staff != nil
      assert payload.customer != nil
      assert length(payload.line_items) == 1
      assert payload.location_id == updated_order.location_id
      assert payload.location != nil
      assert payload.transaction != nil
      assert length(payload.fulfillments) == 1
      assert payload.fulfillments |> Enum.all?(&(not is_nil(&1.external_reference)))
      assert length(payload.discounts) == 2
    end

    test "handles nil fields correctly", %{order: order} do
      order = %Order{
        order
        | total_price: nil,
          total_discount: nil,
          total_items_selling_price: nil,
          total_delivery_price: nil,
          transaction: nil,
          fulfillments: []
      }

      payload = Order.event_payload(order)
      zero = Decimal.new("0")

      assert payload.total_amount == zero
      assert payload.discounts_total == zero
      refute payload.delivery_price
    end
  end

  describe "changeset/2" do
    test "calculates total_discount correctly" do
      attrs = %{
        "fulfillments" => [
          %{
            "line_items" => [
              %{
                "price" => "35.49",
                "quantity" => 3,
                "list_price" => "149.00",
                "manual_discount" => "340.53"
              },
              %{
                "price" => "85.56",
                "quantity" => 1,
                "list_price" => "449.00",
                "manual_discount" => "273.64"
              },
              %{
                "price" => "430.97",
                "quantity" => 1,
                "list_price" => "649.00",
                "manual_discount" => "88.23"
              },
              %{
                "price" => "299.00",
                "quantity" => 1,
                "list_price" => "299.00",
                "manual_discount" => "0.00"
              }
            ]
          }
        ]
      }

      changeset = Order.changeset(%Order{}, attrs, price_table: %{}, allowed_location_ids: :all)

      assert Decimal.eq?(
               Decimal.new("922.0"),
               Ecto.Changeset.get_change(changeset, :total_discount)
             )
    end

    test "requires customer_id if there's a subscription addon" do
      attrs = %{
        "location_id" => 1,
        "addons" => [
          %{
            "addon_id" => 1,
            "name" => "subscription",
            "price" => "99.99",
            "list_price" => "99.99",
            "url" => "url",
            "type" => "subscription",
            "metadata" => %{}
          }
        ]
      }

      changeset = Order.changeset(%Order{}, attrs, price_table: %{}, allowed_location_ids: :all)

      refute changeset.valid?
      assert "is required when adding a subscription addon" in errors_on(changeset).customer_id

      # With customer_id
      attrs = Map.put(attrs, "customer_id", "123")
      changeset = Order.changeset(%Order{}, attrs, price_table: %{}, allowed_location_ids: :all)

      assert changeset.valid?
    end
  end
end
