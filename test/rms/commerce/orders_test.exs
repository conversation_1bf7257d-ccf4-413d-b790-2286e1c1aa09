defmodule Rms.Commerce.OrdersTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Commerce.Orders
  alias Rms.Commerce.Orders.Order
  alias Rms.Finance

  import Rms.Factory
  import Mox

  setup :verify_on_exit!

  describe "update_order_statuses/2" do
    test "updates order statuses correctly based on line items" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      line_item =
        insert(:line_item,
          location: location,
          product_variant: product_variant,
          fulfillment: fulfillment,
          organization: org,
          quantity: 2
        )

      # Create a completed reverse fulfillment with 2 items returned
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "completed",
          staff: build(:staff, organization: org)
        )

      insert(:reverse_fulfillment_line_item,
        organization: org,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 2
      )

      affected_orders = [order.id]

      Orders.update_order_statuses(affected_orders, org.id)

      updated_order = Orders.get_order!(org.id, order.id)

      assert updated_order.status == "returned"
    end
  end

  describe "paginated_orders/2" do
    defp create_order_with_payment(attrs) do
      order = insert(:order, attrs)
      transaction = insert(:transaction, order: order, organization: order.organization)
      insert(:payment, transaction: transaction, organization: order.organization)
      order
    end

    setup do
      organization = insert(:organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      location = insert(:location, organization: organization)

      # Create 5 iglu orders and 5 shopify orders for testing pagination/filtering
      iglu_orders =
        for _ <- 1..5, do: create_order_with_payment(organization: organization, source: "iglu")

      shopify_orders =
        for _ <- 1..5,
            do: create_order_with_payment(organization: organization, source: "shopify")

      all_orders = iglu_orders ++ shopify_orders

      # Add fulfillments/line_items to the LAST order for the preload test
      last_order = List.last(all_orders)

      fulfillment =
        insert(:fulfillment,
          order: last_order,
          organization: organization,
          shipping_method: "in-store"
        )

      insert(:line_item,
        organization: organization,
        product_variant: product_variant,
        fulfillment: fulfillment,
        location: location,
        price: 10.00,
        quantity: 1,
        shipping_method: "in-store"
      )

      {:ok, organization: organization, iglu_orders: iglu_orders, shopify_orders: shopify_orders}
    end

    test "returns a paginated list of orders (mixed sources, no filter applied)", %{
      organization: organization
    } do
      # Note: This test assumes default behavior doesn't filter by source if not provided
      # If the requirement changes to always default, this test needs modification or removal.
      opts = [limit: 5]
      results = Orders.paginated_orders(organization.id, opts)

      assert length(results.entries) == 5
      assert results.metadata.after
    end

    test "can filter by source=iglu", %{organization: organization, iglu_orders: iglu_orders} do
      opts = [source: "iglu", limit: 10]
      results = Orders.paginated_orders(organization.id, opts)

      assert length(results.entries) == 5
      returned_ids = Enum.map(results.entries, & &1.id)
      expected_ids = Enum.map(iglu_orders, & &1.id)
      assert Enum.sort(returned_ids) == Enum.sort(expected_ids)
      assert Enum.all?(results.entries, &(&1.source == "iglu"))
    end

    test "can filter by source=shopify", %{
      organization: organization,
      shopify_orders: shopify_orders
    } do
      opts = [source: "shopify", limit: 10]
      results = Orders.paginated_orders(organization.id, opts)

      assert length(results.entries) == 5
      returned_ids = Enum.map(results.entries, & &1.id)
      expected_ids = Enum.map(shopify_orders, & &1.id)
      assert Enum.sort(returned_ids) == Enum.sort(expected_ids)
      assert Enum.all?(results.entries, &(&1.source == "shopify"))
    end

    test "combines source filter with other filters (e.g., status)", %{organization: organization} do
      # Create specific orders for this test
      iglu_paid =
        create_order_with_payment(organization: organization, source: "iglu", status: "paid")

      _iglu_open =
        create_order_with_payment(organization: organization, source: "iglu", status: "open")

      _shopify_paid =
        create_order_with_payment(organization: organization, source: "shopify", status: "paid")

      opts = [source: "iglu", status: "paid"]
      results = Orders.paginated_orders(organization.id, opts)

      assert length(results.entries) == 1
      assert hd(results.entries).id == iglu_paid.id
      assert hd(results.entries).source == "iglu"
      assert hd(results.entries).status == "paid"
    end

    test "can search by partial customer name (with source filter)", %{organization: organization} do
      customer_name = "JôHn Doe"
      customer = insert(:customer, name: customer_name, organization: organization)

      # Order that should match
      iglu_order = create_order_with_payment(organization: organization, source: "iglu")

      insert(:order_customer,
        order: iglu_order,
        organization: organization,
        name: customer_name,
        customer: customer
      )

      # Order with same customer but wrong source
      shopify_order = create_order_with_payment(organization: organization, source: "shopify")

      insert(:order_customer,
        order: shopify_order,
        organization: organization,
        name: customer_name,
        customer: customer
      )

      opts = [search: "John", source: "iglu"]
      result = Orders.paginated_orders(organization.id, opts)

      assert length(result.entries) == 1
      assert hd(result.entries).id == iglu_order.id
    end

    test "can filter by status", %{organization: organization} do
      create_order_with_payment(organization: organization, status: "closed", source: "iglu")

      opts = [status: "closed", source: "iglu"]
      result = Orders.paginated_orders(organization.id, opts)

      assert [%{status: "closed"}] = result.entries
    end

    test "paginates using the after cursor (with source filter)", %{
      organization: organization,
      iglu_orders: iglu_orders
    } do
      opts_page1 = [limit: 3, source: "iglu"]
      first_page_results = Orders.paginated_orders(organization.id, opts_page1)

      opts_page2 = [limit: 3, source: "iglu", after: first_page_results.metadata.after]
      second_page_results = Orders.paginated_orders(organization.id, opts_page2)

      assert length(first_page_results.entries) == 3
      # Total 5 iglu orders created in setup
      assert length(second_page_results.entries) == 2

      first_page_ids_set = MapSet.new(Enum.map(first_page_results.entries, & &1.id))
      second_page_ids_set = MapSet.new(Enum.map(second_page_results.entries, & &1.id))
      all_iglu_ids_set = MapSet.new(Enum.map(iglu_orders, & &1.id))

      assert MapSet.disjoint?(first_page_ids_set, second_page_ids_set)
      assert MapSet.subset?(first_page_ids_set, all_iglu_ids_set)
      assert MapSet.subset?(second_page_ids_set, all_iglu_ids_set)
    end

    test "can filter by location_id", %{organization: organization} do
      location1 = insert(:location, organization: organization)
      location2 = insert(:location, organization: organization)

      order1 = insert(:order, location_id: location1.id, organization: organization)
      order2 = insert(:order, location_id: location2.id, organization: organization)

      transaction1 =
        insert(:transaction, order: order1, status: "open", organization: organization)

      transaction2 =
        insert(:transaction, order: order2, status: "open", organization: organization)

      insert(:payment, transaction: transaction1, organization: organization)
      insert(:payment, transaction: transaction2, organization: organization)

      opts = [location_id: location1.id]
      result = Orders.paginated_orders(organization.id, opts)
      location_id = location1.id
      assert [%{location_id: ^location_id}] = result.entries
    end

    test "can filter by staff_id", %{organization: organization} do
      staff1 = insert(:staff, organization: organization)
      staff2 = insert(:staff, organization: organization)

      order1 = insert(:order, staff_id: staff1.id, organization: organization)
      order2 = insert(:order, staff_id: staff2.id, organization: organization)

      transaction1 =
        insert(:transaction, order: order1, status: "open", organization: organization)

      transaction2 =
        insert(:transaction, order: order2, status: "open", organization: organization)

      insert(:payment, transaction: transaction1, organization: organization)
      insert(:payment, transaction: transaction2, organization: organization)

      opts = [staff_id: staff1.id]
      result = Orders.paginated_orders(organization.id, opts)
      staff_id = staff1.id
      assert [%{staff_id: ^staff_id}] = result.entries
    end

    test "can filter by payment_method", %{organization: organization} do
      order = insert(:order, organization: organization)

      transaction = insert(:transaction, order: order, status: "open", organization: organization)

      insert(:payment,
        method: "credit_card",
        organization: organization,
        transaction: transaction
      )

      insert(:payment,
        method: "cash",
        organization: organization,
        transaction: transaction
      )

      opts = [payment_method: "cash"]
      result = Orders.paginated_orders(organization.id, opts)

      order_id = order.id
      assert [%{id: ^order_id}] = result.entries
    end

    test "can filter by inserted_at following iso8601", %{organization: organization} do
      date_iso8601 = "2023-01-01T07:00:00Z"
      date = NaiveDateTime.from_iso8601!(date_iso8601)

      order = insert(:order, inserted_at: date, organization: organization)
      transaction = insert(:transaction, order: order, status: "open", organization: organization)
      insert(:payment, transaction: transaction, organization: organization)

      opts = [inserted_at: date_iso8601]
      result = Orders.paginated_orders(organization.id, opts)

      assert [%{inserted_at: inserted_at}] = result.entries
      assert Date.compare(NaiveDateTime.to_date(inserted_at), ~D[2023-01-01]) == :eq
    end

    test "paginates using the after cursor", %{organization: organization} do
      first_page_opts = [limit: 5]
      first_page_results = Orders.paginated_orders(organization.id, first_page_opts)

      second_page_opts = [limit: 5, after: first_page_results.metadata.after]
      second_page_results = Orders.paginated_orders(organization.id, second_page_opts)

      first_page_ids_set =
        first_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      second_page_ids_set =
        second_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      assert MapSet.disjoint?(second_page_ids_set, first_page_ids_set)
    end

    test "preloads associations if specified", %{organization: organization} do
      opts = [preloads: [fulfillments: :line_items], limit: 1]
      results = Orders.paginated_orders(organization.id, opts)
      order = hd(results.entries)

      assert Map.has_key?(order, :fulfillments)
      assert Map.has_key?(hd(order.fulfillments), :line_items)
      assert length(order.fulfillments) > 0
      assert length(order.fulfillments |> Enum.flat_map(& &1.line_items)) > 0
    end

    test "can search by order id", %{organization: organization} do
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, status: "open", organization: organization)
      insert(:payment, transaction: transaction, organization: organization)

      opts = [search: Integer.to_string(order.id)]
      result = Orders.paginated_orders(organization.id, opts)

      assert result.entries |> length() == 1
      assert hd(result.entries).id == order.id
    end

    test "can search by customer document", %{organization: organization} do
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, status: "open", organization: organization)
      insert(:payment, transaction: transaction, organization: organization)

      document = "12345678900"

      customer =
        insert(:customer,
          document: document,
          organization: organization
        )

      insert(:order_customer,
        order: order,
        organization: organization,
        document: document,
        customer: customer
      )

      # Search for the document
      opts = [search: document]
      result = Orders.paginated_orders(organization.id, opts)

      assert result.entries |> length() == 1
      assert hd(result.entries).id == order.id
    end

    test "can search by customer email", %{organization: organization} do
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, status: "open", organization: organization)
      insert(:payment, transaction: transaction, organization: organization)

      email = "<EMAIL>"

      customer =
        insert(:customer, email: email, organization: organization)

      insert(:order_customer,
        order: order,
        organization: organization,
        email: email,
        customer: customer
      )

      opts = [search: email]

      result = Orders.paginated_orders(organization.id, opts)

      assert result.entries |> length() == 1
      assert hd(result.entries).id == order.id
    end

    test "returns empty when search matches no orders", %{organization: organization} do
      opts = [search: "nonexistent"]
      result = Orders.paginated_orders(organization.id, opts)

      assert result.entries |> length() == 0
    end

    test "can filter by reference_at date (matches any time within the day)", %{
      organization: organization
    } do
      # 1. Define reference dates/times
      today_start = DateTime.utc_now() |> NaiveDateTime.beginning_of_day()
      # Add 12 hours
      today_midday = NaiveDateTime.add(today_start, 12 * 3600, :second)
      # Previous day
      yesterday = NaiveDateTime.add(today_start, -12 * 3600, :second)
      # Next day
      tomorrow = NaiveDateTime.add(today_start, 36 * 3600, :second)

      # 2. Create orders with specific reference_at datetimes
      order_today_start =
        create_order_with_payment(organization: organization, reference_at: today_start)

      order_today_midday =
        create_order_with_payment(organization: organization, reference_at: today_midday)

      _order_yesterday =
        create_order_with_payment(organization: organization, reference_at: yesterday)

      _order_tomorrow =
        create_order_with_payment(organization: organization, reference_at: tomorrow)

      # 3. Filter using DateTime (start of day)
      opts_ndt = [reference_at: today_start]
      result_ndt = Orders.paginated_orders(organization.id, opts_ndt)

      # Assertions for DateTime filter
      # Should match both orders from today
      assert length(result_ndt.entries) == 2
      result_ids_ndt = Enum.map(result_ndt.entries, & &1.id) |> Enum.sort()
      expected_ids = [order_today_start.id, order_today_midday.id] |> Enum.sort()
      assert result_ids_ndt == expected_ids

      # 4. Filter using ISO8601 String (midday) - should still match both from today
      today_midday_str =
        today_midday
        |> DateTime.from_naive!("Etc/UTC")
        |> DateTime.to_iso8601()

      opts_str = [reference_at: today_midday_str]
      result_str = Orders.paginated_orders(organization.id, opts_str)

      # Assertions for String filter
      # Should still match both orders from today
      assert length(result_str.entries) == 2
      result_ids_str = Enum.map(result_str.entries, & &1.id) |> Enum.sort()
      assert result_ids_str == expected_ids
    end
  end

  describe "mark_order_as_paid/1" do
    test "updates the order status to paid" do
      order = insert(:order, status: "open")
      assert {:ok, _} = Orders.mark_order_as_paid(order)

      updated_order = Rms.Repo.get!(Order, order.id)
      assert updated_order.status == "paid"
    end

    test "emit order.paid event" do
      order = insert(:order, status: "open")
      assert {:ok, %{update_order: order}} = Orders.mark_order_as_paid(order)
      assert_emit("order.paid", Order.event_payload(order))
    end
  end

  describe "get_order!/2" do
    test "returns the order" do
      order = insert(:order)
      result = Orders.get_order!(order.organization_id, order.id)
      assert result.id == order.id
    end

    test "returns the order with preloaded associations" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, status: "open", organization: org)

      result = Orders.get_order!(order.organization_id, order.id, [:transaction])
      assert result.transaction.id == transaction.id
    end
  end

  describe "create_order/2" do
    test "successfully creates an order with line item price 0" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "0.0",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            price: "0.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("0.0"))
      assert_emit("order.created", Order.event_payload(order))

      assert [line_item] = order.fulfillments |> Enum.flat_map(& &1.line_items)
      assert Decimal.eq?(line_item.price, Decimal.new("0.0"))
    end

    test "successfully creates an order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        line_items: [
          %{
            shipping_method: "in-store",
            quantity: 1,
            price: "10.0",
            product_variant_id: pv.id,
            shipping_settings: nil,
            discounts: [
              %{
                type: "fixed",
                value: "10.00",
                description: "10% off"
              }
            ],
            location_id: loc.id
          }
        ],
        location_id: loc.id
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      order = Rms.Repo.preload(order, [:order_customer])
      assert order.status == "open"
      assert order.organization_id == org.id
      refute order.order_customer

      Enum.each(order.fulfillments, fn fulfillment ->
        Enum.each(fulfillment.line_items, fn line_item ->
          assert [
                   %{
                     type: "fixed",
                     value: "10.00",
                     description: "10% off"
                   }
                 ] = Rms.Repo.preload(line_item, [:discounts]).discounts
        end)
      end)

      assert_emit("order.created", Order.event_payload(order))
    end

    test "successfully creates an order and a order customer" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      customer = insert(:customer, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        line_items: [],
        location_id: loc.id,
        customer_id: customer.id
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      order = Rms.Repo.preload(order, [:order_customer])
      assert order.status == "open"
      assert order.organization_id == org.id
      assert order.order_customer
      assert_emit("order.created", Order.event_payload(order))
    end

    test "calculates total price with addons correctly" do
      zero = Decimal.new("0")
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.00",
        location_id: loc.id,
        customer_id: customer.id,
        addons: [
          %{
            addon_id: 1,
            name: "Prime",
            price: "9.00",
            list_price: "10.00",
            type: "subscription",
            metadata: %{"sku_id" => "PRIME"}
          }
        ],
        line_items: [
          %{
            product_variant_id:
              insert(:product_variant,
                organization: org,
                product: build(:product, organization: org)
              ).id,
            location_id: loc.id,
            quantity: 1,
            price: "11.0",
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)

      assert Decimal.eq?(order.total_discount_with_addons, Decimal.new("1.00"))
      assert Decimal.eq?(order.total_price_with_addons, Decimal.new("20.00"))

      assert Enum.reduce(order.addons, zero, fn addon, acc -> acc |> Decimal.add(addon.price) end) ==
               Decimal.new("9.00")
    end

    test "successfully creates an order with delivery price included in total_price" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        total_delivery_price: "5.0",
        line_items: [
          %{
            product_variant_id:
              insert(:product_variant,
                organization: org,
                product: build(:product, organization: org)
              ).id,
            location_id: loc.id,
            quantity: 1,
            price: "10.0",
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("15.0"))
      assert order.total_delivery_price == Decimal.new("5.0")
      assert_emit("order.created", Order.event_payload(order))
    end

    test "successfully creates an order with addresses" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        line_items: [],
        shipping_address: %{
          receiver_name: "Chuck Norris",
          city_name: "São Paulo",
          state: "SP",
          country_name: "Brazil",
          neighborhood: "Jardim Paulista",
          street: "Av. Paulista",
          street_type: "Avenue",
          number: "1000",
          zip: "01310100",
          complement: "Apt 1001"
        }
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert order.status == "open"
      assert order.organization_id == org.id
      assert order.shipping_address.neighborhood == attrs.shipping_address.neighborhood
      assert_emit("order.created", Order.event_payload(order))
    end

    test "creates order and attributes it to staff_id" do
      org = insert(:organization)
      staff = insert(:staff, organization: org)
      loc = insert(:location, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        staff_id: staff.id,
        line_items: []
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert order.staff_id == staff.id
      assert_emit("order.created", Order.event_payload(order))
    end

    test "successfully creates an order with line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "20.0",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            location_id: loc.id,
            quantity: 2,
            price: "10.0",
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)

      assert [fulfillment] = order.fulfillments
      assert [line_item] = fulfillment.line_items
      assert line_item.quantity == 2
      assert Decimal.eq?(line_item.price, Decimal.new("10.0"))
      assert_emit("order.created", Order.event_payload(order))

      # automatically filled
      assert line_item.reference_at
      assert order.reference_at
      assert fulfillment.reference_at
    end

    test "successfully creates an order with explicit reference_at" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)
      reference_time = DateTime.utc_now() |> DateTime.truncate(:second)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        reference_at: reference_time,
        line_items: [
          %{
            product_variant_id: pv.id,
            location_id: loc.id,
            quantity: 1,
            price: "10.0",
            shipping_method: "in-store",
            reference_at: reference_time
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert order.reference_at == reference_time

      assert [fulfillment] = order.fulfillments
      assert fulfillment.reference_at == reference_time

      assert [line_item] = fulfillment.line_items
      assert line_item.reference_at == reference_time

      assert_emit("order.created", Order.event_payload(order))
    end

    test "successfully creates an order with line items and shipping_settings" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "20.0",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            location_id: loc.id,
            quantity: 2,
            price: "10.0",
            shipping_method: "in-store",
            shipping_settings: %{id: 1, name: "NORMAL"}
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert [line_item] = order.fulfillments |> Enum.flat_map(& &1.line_items)
      assert line_item.quantity == 2
      assert line_item.shipping_settings == %{id: 1, name: "NORMAL"}
      assert Decimal.eq?(line_item.price, Decimal.new("10.0"))
      assert_emit("order.created", Order.event_payload(order))
    end

    test "updates the total_price to match the line items total" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 15.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # This is intentionally incorrect to test the adjustment
        total_price: "5.0",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "15.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("30.0"))
      assert_emit("order.created", Order.event_payload(order))
    end

    test "updates the prices to match the discounts" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 15.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # This is intentionally incorrect to test the adjustment
        total_price: "90.00",
        total_manual_discount: "5.00",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "15.0",
            location_id: loc.id,
            shipping_method: "in-store"
          },
          %{
            product_variant_id: pv.id,
            quantity: 3,
            price: "20.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("85.01"))
      assert_emit("order.created", Order.event_payload(order))

      assert [first_line_item, second_line_item] =
               order.fulfillments |> Enum.flat_map(& &1.line_items)

      assert first_line_item.price == Decimal.new("14.17")
      assert second_line_item.price == Decimal.new("18.89")
    end

    test "updates the prices to maximize discount" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 15.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "100.0",
        total_manual_discount: "1.01",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 4,
            price: "15.0",
            location_id: loc.id,
            shipping_method: "in-store"
          },
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "20.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("99.00"))
      assert_emit("order.created", Order.event_payload(order))

      assert [first_line_item, second_line_item] =
               order.fulfillments |> Enum.flat_map(& &1.line_items)

      assert first_line_item.price == Decimal.new("14.85")
      assert first_line_item.manual_discount == Decimal.new("0.60")
      assert second_line_item.price == Decimal.new("19.80")
      assert second_line_item.manual_discount == Decimal.new("0.40")
    end

    test "greedly fixes the prices of items impossible do evenly weight" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 15.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # This is intentionally incorrect to test the adjustment
        total_price: "100.0",
        total_manual_discount: "1.01",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            price: "60.0",
            location_id: loc.id,
            shipping_method: "in-store"
          },
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "20.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("98.99"))
      assert_emit("order.created", Order.event_payload(order))

      assert [first_line_item, second_line_item] =
               order.fulfillments |> Enum.flat_map(& &1.line_items)

      assert first_line_item.price == Decimal.new("59.39")
      assert first_line_item.manual_discount == Decimal.new("0.61")
      assert second_line_item.price == Decimal.new("19.80")
      assert second_line_item.manual_discount == Decimal.new("0.40")
    end

    test "fixes the discounts of an order with a single item" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 15.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # This is intentionally incorrect to test the adjustment
        total_price: "100.0",
        total_manual_discount: "1.01",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 1,
            price: "100.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("98.99"))
      assert_emit("order.created", Order.event_payload(order))

      assert [first_line_item] = order.fulfillments |> Enum.flat_map(& &1.line_items)

      assert first_line_item.price == Decimal.new("98.99")
      assert first_line_item.manual_discount == Decimal.new("1.01")
    end

    test "distributes percentage discount correctly across line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, organization: org, product: product, list_price: 15.0)
      pv2 = insert(:product_variant, organization: org, product: product, list_price: 20.0)

      # Testing the exact scenario: 30% discount on 90.00 = 27.00 discount
      # Line items: 2x15.0=30.00 and 3x20.0=60.00, total=90.00
      attrs = %{
        organization_id: org.id,
        status: "open",
        # 90.00 - 27.00
        total_price: "63.00",
        # 30% of 90.00
        total_manual_discount: "27.00",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv1.id,
            quantity: 2,
            price: "15.0",
            location_id: loc.id,
            shipping_method: "in-store"
          },
          %{
            product_variant_id: pv2.id,
            quantity: 3,
            price: "20.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)

      # Total should be 63.00 after discount
      assert Decimal.eq?(order.total_price, Decimal.new("63.00"))

      assert [first_line_item, second_line_item] =
               order.fulfillments |> Enum.flat_map(& &1.line_items)

      # First item: 30.00 * 0.7 = 21.00 total (10.50 per unit)
      # Discount on first item: 30.00 * 0.3 = 9.00
      # 15.00 - 4.50 per unit
      expected_first_price = Decimal.new("10.50")
      # 4.50 * 2
      expected_first_discount = Decimal.new("9.00")

      # Second item: 60.00 * 0.7 = 42.00 total (14.00 per unit)
      # Discount on second item: 60.00 * 0.3 = 18.00
      # 20.00 - 6.00 per unit
      expected_second_price = Decimal.new("14.00")
      # 6.00 * 3
      expected_second_discount = Decimal.new("18.00")

      assert Decimal.eq?(first_line_item.price, expected_first_price)
      assert Decimal.eq?(first_line_item.manual_discount, expected_first_discount)
      assert Decimal.eq?(second_line_item.price, expected_second_price)
      assert Decimal.eq?(second_line_item.manual_discount, expected_second_discount)

      # Verify total calculations
      total_after_discount =
        Decimal.mult(first_line_item.price, Decimal.new(first_line_item.quantity))
        |> Decimal.add(
          Decimal.mult(second_line_item.price, Decimal.new(second_line_item.quantity))
        )

      assert Decimal.eq?(total_after_discount, Decimal.new("63.00"))
    end

    test "handles ecommerce discounts correctly" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 20.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # 100.00 - 20.00 - 10.00
        total_price: "70.00",
        total_manual_discount: "10.00",
        total_ecommerce_discounts: "20.00",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 5,
            price: "20.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("70.00"))

      assert [line_item] = order.fulfillments |> Enum.flat_map(& &1.line_items)

      # Total discount is 30.00 (manual + ecommerce), so 30% of 100.00
      # Price per unit should be 14.00 (20.00 - 6.00)
      assert Decimal.eq?(line_item.price, Decimal.new("14.00"))
      assert Decimal.eq?(line_item.manual_discount, Decimal.new("30.00"))
    end

    test "distributes discount evenly when possible" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 25.0)

      attrs = %{
        organization_id: org.id,
        status: "open",
        # 100.00 - 20.00
        total_price: "80.00",
        # 20% discount
        total_manual_discount: "20.00",
        location_id: loc.id,
        line_items: [
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "25.0",
            location_id: loc.id,
            shipping_method: "in-store"
          },
          %{
            product_variant_id: pv.id,
            quantity: 2,
            price: "25.0",
            location_id: loc.id,
            shipping_method: "in-store"
          }
        ]
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert Decimal.eq?(order.total_price, Decimal.new("80.00"))

      assert [first_line_item, second_line_item] =
               order.fulfillments |> Enum.flat_map(& &1.line_items)

      # Both items should get equal 20% discount
      # 25.00 - 5.00
      expected_price = Decimal.new("20.00")
      # 5.00 * 2
      expected_discount = Decimal.new("10.00")

      assert Decimal.eq?(first_line_item.price, expected_price)
      assert Decimal.eq?(first_line_item.manual_discount, expected_discount)
      assert Decimal.eq?(second_line_item.price, expected_price)
      assert Decimal.eq?(second_line_item.manual_discount, expected_discount)
    end

    test "returns an error if line item price does not match" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          list_price: 15.0,
          price: 15.0,
          product: product
        )

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        line_items: [
          %{product_variant_id: pv.id, quantity: 1, price: "5.0", shipping_method: "in-store"}
        ],
        location_id: loc.id
      }

      assert {:error, changeset} =
               Orders.create_order(org.id, attrs,
                 allowed_location_ids: :all,
                 validate_price: true
               )

      assert [line_item_errors] = hd(errors_on(changeset).fulfillments).line_items
      assert "does not match product variant price" in line_item_errors.price
    end

    test "creates order with no name when order_settings exists" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:order_settings, organization: org, prefix: "ORD-", suffix: "-2024")

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        line_items: []
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert order.name
      assert String.starts_with?(order.name, "ORD-")
      assert String.ends_with?(order.name, "-2024")
    end

    test "creates order with name does not increment current_order_number" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      settings =
        insert(:order_settings,
          organization: org,
          prefix: "ORD-",
          suffix: "-2024",
          current_order_number: 5
        )

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        location_id: loc.id,
        line_items: [],
        name: "CUSTOM-ORDER-1"
      }

      assert {:ok, order} = Orders.create_order(org.id, attrs, allowed_location_ids: :all)
      assert order.name == "CUSTOM-ORDER-1"

      updated_settings = Rms.Repo.get!(Rms.Commerce.Orders.OrderSettings, settings.id)
      assert updated_settings.current_order_number == 5
    end

    test "successfully creates an order with transaction customer data" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product, list_price: 10.0)

      transaction_customer = %{
        name: "John Doe",
        document_type: "cpf",
        document: "05774088529",
        email: "<EMAIL>",
        phone_number: "+5511999999999",
        address: %{
          street: "Main Street",
          number: "123",
          complement: "Apt 4B",
          neighborhood: "Downtown",
          city_name: "São Paulo",
          city_code: "3550308",
          state: "SP",
          country_name: "Brazil",
          zip: "01234567"
        }
      }

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "10.0",
        line_items: [
          %{
            shipping_method: "in-store",
            quantity: 1,
            price: "10.0",
            product_variant_id: pv.id,
            shipping_settings: nil,
            location_id: location.id
          }
        ],
        location_id: location.id
      }

      assert {:ok, order} =
               Orders.create_order(org.id, attrs,
                 allowed_location_ids: :all,
                 transaction_customer: transaction_customer,
                 create_transaction: true
               )

      # Verify order was created
      assert order.status == "open"
      assert order.organization_id == org.id

      # Verify transaction was created with customer
      order = Repo.preload(order, transaction: [:customer])
      assert order.transaction
      assert order.transaction.customer
      assert order.transaction.customer.name == "John Doe"
      assert order.transaction.customer.document_type == "cpf"
      assert order.transaction.customer.email == "<EMAIL>"

      assert_emit("order.created", Order.event_payload(order))
    end
  end

  describe "update_order!/2" do
    test "updates the specified fields of an order" do
      order = insert(:order)
      updates = %{status: "paid"}

      updated_order = Orders.update_order!(order, updates)

      assert updated_order.status == "paid"
    end

    test "raises an error when the order does not exist" do
      assert_raise Ecto.InvalidChangesetError, fn ->
        Orders.update_order!(insert(:order), %{status: "invalid status"})
      end
    end
  end

  describe "update_order_name/2" do
    test "updates the order name" do
      order = insert(:order, name: "Original Name")
      {:ok, updated_order} = Orders.update_order_name(order, "New Name")
      assert updated_order.name == "New Name"
    end

    test "returns an error for invalid names" do
      order = insert(:order, name: "Original Name")
      {:error, changeset} = Orders.update_order_name(order, "")
      assert "can't be blank" in errors_on(changeset).name
    end
  end

  describe "create_new/2" do
    test "returns existing order" do
      existing_order = insert(:order, external_id: "gid://shopify/Order/any-random-id")

      assert {:ok, result} =
               Orders.create_new(existing_order.organization_id, existing_order.external_id)

      assert result.id == existing_order.id
    end

    test "create a new order based on shopify order" do
      org = insert(:organization)

      id = "gid://shopify/Order/custom_id"
      replace_id = %{"data" => %{"order" => %{"id" => id}}}
      Rms.ShopifyMock.order_mock(replace_id)

      insert(:shopify_credential, organization: org)

      assert {:ok, result} = Orders.create_new(org.id, id)
      assert result.external_id == id
    end
  end

  describe "upsert_order_settings/1" do
    test "creates new order settings" do
      org = insert(:organization)
      attrs = %{organization_id: org.id, prefix: "ORD-", suffix: "-2024"}

      assert {:ok, settings} = Orders.upsert_order_settings(org.id, attrs)
      assert settings.organization_id == org.id
      assert settings.prefix == "ORD-"
      assert settings.suffix == "-2024"
      assert settings.current_order_number == 0
    end

    test "updates existing order settings" do
      org = insert(:organization)

      existing_settings =
        insert(:order_settings, organization: org, prefix: "OLD-", suffix: "-OLD")

      attrs = %{organization_id: org.id, prefix: "NEW-", suffix: "-NEW"}
      assert {:ok, updated_settings} = Orders.upsert_order_settings(org.id, attrs)

      assert updated_settings.id == existing_settings.id
      assert updated_settings.prefix == "NEW-"
      assert updated_settings.suffix == "-NEW"
    end
  end

  describe "generate_order_name/1" do
    test "generates a new order name" do
      org = insert(:organization)

      insert(:order_settings,
        organization: org,
        prefix: "ORD-",
        suffix: "-2024"
      )

      assert {:ok, name} = Orders.generate_order_name(org.id)
      assert name == "ORD-1-2024"

      assert {:ok, next_name} = Orders.generate_order_name(org.id)
      assert next_name == "ORD-2-2024"
    end

    test "handles concurrent requests" do
      org = insert(:organization)

      insert(:order_settings,
        organization: org,
        prefix: "ORD-",
        suffix: "-2024"
      )

      tasks =
        Enum.map(1..10, fn _ ->
          Task.async(fn -> Orders.generate_order_name(org.id) end)
        end)

      results = Task.await_many(tasks)
      names = Enum.map(results, fn {:ok, name} -> name end)
      sorted_names = Enum.sort(names)

      assert sorted_names == [
               "ORD-1-2024",
               "ORD-10-2024",
               "ORD-2-2024",
               "ORD-3-2024",
               "ORD-4-2024",
               "ORD-5-2024",
               "ORD-6-2024",
               "ORD-7-2024",
               "ORD-8-2024",
               "ORD-9-2024"
             ]
    end
  end

  describe "cancel_order/2" do
    test "cancels an order" do
      order = insert(:order, status: "open")
      attrs = %{external_id: "EXT_123"}

      assert {:ok, updated_order} = Orders.cancel_order(order, attrs)
      assert updated_order.status == "canceled"
      assert updated_order.external_id == "EXT_123"
    end

    test "emits order.canceled event" do
      order = insert(:order, status: "open")
      attrs = %{external_id: "EXT_123"}

      assert {:ok, updated_order} = Orders.cancel_order(order, attrs)
      assert_emit("order.canceled", Order.event_payload(updated_order))
    end
  end

  describe "get_line_items_with_current_quantity/1" do
    test "returns line items with correct current quantity" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      line_item =
        insert(:line_item,
          location: location,
          product_variant: product_variant,
          fulfillment: fulfillment,
          organization: org,
          quantity: 5
        )

      # Create a completed reverse fulfillment with 2 items returned
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "completed"
        )

      insert(:reverse_fulfillment_line_item,
        organization: org,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 2
      )

      # Create a processing reverse fulfillment with 1 item being returned
      processing_reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "processing"
        )

      insert(:reverse_fulfillment_line_item,
        organization: org,
        reverse_fulfillment: processing_reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 1
      )

      # Create a pending reverse fulfillment that should not affect the count
      pending_reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "pending"
        )

      insert(:reverse_fulfillment_line_item,
        organization: org,
        reverse_fulfillment: pending_reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 1
      )

      result = Orders.get_line_items_with_current_quantity(order.id)

      assert length(result) == 1
      [item] = result
      assert item.line_item.id == line_item.id
      # Original quantity (5) - completed returns (2) - processing returns (1) = 2
      assert item.current_quantity == 2
    end

    test "returns original quantity when no returns exist" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      line_item =
        insert(:line_item,
          location: location,
          product_variant: product_variant,
          fulfillment: fulfillment,
          organization: org,
          quantity: 5
        )

      result = Orders.get_line_items_with_current_quantity(order.id)

      assert length(result) == 1
      [item] = result
      assert item.line_item.id == line_item.id
      assert item.current_quantity == 5
    end

    test "handles multiple line items correctly" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product_variant1 =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      product_variant2 =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      line_item1 =
        insert(:line_item,
          location: location,
          product_variant: product_variant1,
          fulfillment: fulfillment,
          organization: org,
          quantity: 3
        )

      line_item2 =
        insert(:line_item,
          location: location,
          product_variant: product_variant2,
          fulfillment: fulfillment,
          organization: org,
          quantity: 4
        )

      # Create returns only for the first line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          status: "completed"
        )

      insert(:reverse_fulfillment_line_item,
        organization: org,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item1,
        returned_quantity: 1
      )

      result = Orders.get_line_items_with_current_quantity(order.id)

      assert length(result) == 2

      item1 = Enum.find(result, &(&1.line_item.id == line_item1.id))
      item2 = Enum.find(result, &(&1.line_item.id == line_item2.id))

      # Original 3 - 1 returned
      assert item1.current_quantity == 2
      # Original 4, no returns
      assert item2.current_quantity == 4
    end
  end

  describe "create_order_with_credit/4" do
    setup do
      org = insert(:organization)
      location = insert(:location, organization: org)

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          list_price: 100.00,
          price: 100.00
        )

      base_attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "100.00",
        location_id: location.id,
        line_items: [
          params_for(:line_item, %{
            product_variant_id: product_variant.id,
            location: location,
            quantity: 1,
            price: "100.00",
            shipping_method: "in-store"
          })
        ]
      }

      {:ok, org: org, location: location, base_attrs: base_attrs}
    end

    test "successfully creates order using credit for full payment", %{
      org: org,
      base_attrs: attrs
    } do
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "100.00",
          status: "available",
          reason: "return",
          metadata: %{reference: "REF123"},
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      assert {:ok, order} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      # Verify order was created
      assert order.total_price == Decimal.new("100.00")

      # Verify credit was marked as used
      updated_credit = Finance.get_iglu_credit!(org.id, credit.id)
      assert updated_credit.status == "used"

      # Verify payment was created correctly
      order = Repo.preload(order, transaction: :payments)
      assert [payment] = order.transaction.payments
      assert payment.method == "return_credit"
      assert payment.amount == Decimal.new("100.00")
      assert payment.status == "settled"
      assert payment.metadata["credit_id"] == credit.id
      assert payment.metadata["reason"] == "return"
      assert payment.metadata["reference"] == "REF123"
    end

    test "successfully creates order using credit for partial payment", %{
      org: org,
      base_attrs: attrs
    } do
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "50.00",
          status: "available",
          reason: "return",
          metadata: %{reference: "REF123"},
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      assert {:ok, order} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      # Verify order was created
      assert order.total_price == Decimal.new("100.00")
      # Verify order status reflects partial payment
      assert order.status == "open"

      # Verify credit was marked as used and amount tracking is correct
      updated_credit = Finance.get_iglu_credit!(org.id, credit.id)
      assert updated_credit.status == "used"
      assert updated_credit.used_amount == Decimal.new("50.00")
      assert updated_credit.amount == Decimal.new("50.00")

      # Verify payment was created correctly
      order = Repo.preload(order, transaction: :payments)
      assert [payment] = order.transaction.payments
      assert payment.method == "return_credit"
      assert payment.amount == Decimal.new("50.00")
      assert payment.status == "settled"
      # Verify payment metadata
      assert payment.metadata["credit_id"] == credit.id
      assert payment.metadata["reference"] == "REF123"
    end

    test "fails when credit is expired", %{org: org, base_attrs: attrs} do
      past_date = DateTime.utc_now() |> DateTime.add(-1, :day)

      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "100.00",
          status: "available",
          expires_at: past_date,
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      assert {:error, changeset} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      assert "already expired" in errors_on(changeset).iglu_credit

      # Verify no order was created
      assert Repo.aggregate(Order, :count) == 0
    end

    test "fails with invalid order attributes", %{org: org} do
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "100.00",
          status: "available",
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      # Invalid attrs - missing required location_id
      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: "100.00",
        line_items: []
      }

      assert {:error, %Ecto.Changeset{}} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      # Verify credit was not marked as used
      updated_credit = Finance.get_iglu_credit!(org.id, credit.id)
      assert updated_credit.status == "available"
    end

    test "preserves credit metadata in payment", %{org: org, base_attrs: attrs} do
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "100.00",
          status: "available",
          reason: "product_return",
          metadata: %{
            reference: "RMA123",
            return_reason: "defective"
          },
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      assert {:ok, order} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      # Verify payment metadata
      order = Repo.preload(order, transaction: :payments)
      assert [payment] = order.transaction.payments
      assert payment.metadata["credit_id"] == credit.id
      assert payment.metadata["reason"] == "product_return"
      assert payment.metadata["reference"] == "RMA123"
    end

    test "successfully creates order using partial amount from credit", %{
      org: org,
      base_attrs: attrs
    } do
      credit =
        insert(:iglu_credit,
          organization: org,
          customer: nil,
          amount: "100.00",
          status: "used",
          reason: "return",
          metadata: %{reference: "REF123"},
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      # Modify base attrs to have a smaller total price
      attrs = Map.put(attrs, :total_price, "50.00")
      attrs = put_in(attrs, [:line_items, Access.at(0), :price], "50.00")

      assert {:ok, order} =
               Orders.create_order_with_credit(org.id, attrs, credit, allowed_location_ids: :all)

      # Verify order was created with correct price
      assert order.total_price == Decimal.new("50.00")

      # Verify credit was partially used and still has remaining balance
      updated_credit = Finance.get_iglu_credit!(org.id, credit.id)
      assert updated_credit.status == "used"
      assert updated_credit.amount == Decimal.new("100.00")
      assert updated_credit.used_amount == Decimal.new("50.00")

      # Verify payment was created correctly
      order = Repo.preload(order, transaction: :payments)
      assert [payment] = order.transaction.payments
      assert payment.method == "return_credit"
      assert payment.amount == Decimal.new("50.00")
      assert payment.status == "settled"
      assert payment.metadata["credit_id"] == credit.id
      assert payment.metadata["reason"] == "return"
      assert payment.metadata["reference"] == "REF123"
    end

    test "successfully creates order with transaction customer data", %{
      org: org,
      base_attrs: attrs
    } do
      customer = insert(:customer, organization: org)

      credit =
        insert(:iglu_credit,
          organization: org,
          customer: customer,
          amount: "75.00",
          status: "available",
          reason: "return",
          metadata: %{reference: %{type: "reverse_fulfillment"}},
          reverse_fulfillment: build(:reverse_fulfillment, organization: org)
        )

      transaction_customer = %{
        name: "John Doe",
        document_type: "cpf",
        document: "05774088529",
        email: "<EMAIL>",
        phone_number: "+5511999999999",
        address: %{
          street: "Main Street",
          number: "123",
          complement: "Apt 4B",
          neighborhood: "Downtown",
          city_name: "São Paulo",
          city_code: "3550308",
          state: "SP",
          country_name: "Brazil",
          zip: "01234567"
        }
      }

      assert {:ok, order} =
               Orders.create_order_with_credit(
                 org.id,
                 attrs,
                 credit,
                 allowed_location_ids: :all,
                 transaction_customer: transaction_customer
               )

      # Verify order was created
      assert order.total_price == Decimal.new("100.00")

      # Verify transaction was created with customer
      order = Repo.preload(order, transaction: [:customer, :payments])
      assert order.transaction.customer
      assert order.transaction.customer.name == "John Doe"
      assert order.transaction.customer.document_type == "cpf"
      assert order.transaction.customer.email == "<EMAIL>"

      # Verify payment was created correctly
      assert [payment] = order.transaction.payments
      assert payment.method == "return_credit"
      assert payment.amount == Decimal.new("75.00")
      assert payment.status == "settled"

      # Verify credit was marked as used
      updated_credit = Finance.get_iglu_credit!(org.id, credit.id)
      assert updated_credit.status == "used"
    end
  end

  describe "get_order_by_df_key!/2" do
    test "returns order when df_key exists" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      fulfillment = insert(:fulfillment, order: order, organization: organization)
      invoice_series = insert(:invoice_serie, invoice_env: "prod", organization: organization)

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: invoice_series
        )

      found_order = Orders.get_order_by_df_key!(organization.id, "123ABC")
      assert found_order.id == order.id
    end

    test "returns order with prod environment when same df_key exists in dev and prod" do
      organization = insert(:organization)

      # Create order for dev environment
      dev_order = insert(:order, organization: organization)
      dev_fulfillment = insert(:fulfillment, order: dev_order, organization: organization)
      dev_invoice_series = insert(:invoice_serie, invoice_env: "dev", organization: organization)

      _dev_fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: dev_fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: dev_invoice_series
        )

      # Create order for prod environment
      prod_order = insert(:order, organization: organization)
      prod_fulfillment = insert(:fulfillment, order: prod_order, organization: organization)

      prod_invoice_series =
        insert(:invoice_serie, invoice_env: "prod", organization: organization)

      _prod_fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: prod_fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: prod_invoice_series
        )

      # Should return the prod order, not the dev one
      found_order = Orders.get_order_by_df_key!(organization.id, "123ABC")
      assert found_order.id == prod_order.id
      refute found_order.id == dev_order.id
    end

    test "filters out orders with dev invoice environment" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      fulfillment = insert(:fulfillment, order: order, organization: organization)
      invoice_series = insert(:invoice_serie, invoice_env: "dev", organization: organization)

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: invoice_series
        )

      assert_raise Ecto.NoResultsError, fn ->
        Orders.get_order_by_df_key!(organization.id, "123ABC")
      end
    end

    test "raises error when df_key does not exist" do
      organization = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Orders.get_order_by_df_key!(organization.id, "nonexistent")
      end
    end

    test "returns order only from specified organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)

      order = insert(:order, organization: org1)
      fulfillment = insert(:fulfillment, order: order, organization: org1)

      _fiscal_invoice =
        insert(:fiscal_invoice, fulfillment: fulfillment, df_key: "123ABC", organization: org1)

      assert_raise Ecto.NoResultsError, fn ->
        Orders.get_order_by_df_key!(org2.id, "123ABC")
      end
    end

    test "returns order with prod environment when same df_key exists in dev and prod and dev is first" do
      organization = insert(:organization)

      # Create order for dev environment
      dev_order = insert(:order, organization: organization)
      dev_fulfillment = insert(:fulfillment, order: dev_order, organization: organization)
      dev_invoice_series = insert(:invoice_serie, invoice_env: "dev", organization: organization)

      _dev_fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: dev_fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: dev_invoice_series
        )

      # Create order for prod environment
      prod_order = insert(:order, organization: organization)
      prod_fulfillment = insert(:fulfillment, order: prod_order, organization: organization)

      prod_invoice_series =
        insert(:invoice_serie, invoice_env: "prod", organization: organization)

      _prod_fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: prod_fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: prod_invoice_series
        )

      # Should return the prod order, not the dev one
      found_order = Orders.get_order_by_df_key!(organization.id, "123ABC")
      assert found_order.id == prod_order.id
      refute found_order.id == dev_order.id
    end
  end

  describe "get_external_orders/3" do
    setup do
      organization = insert(:organization)

      insert(:shopify_credential, organization: organization)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: organization
      )

      {:ok, org: organization}
    end

    test "fetches orders from Shopify and includes internal order ID if matched", %{
      org: organization
    } do
      # 1. Internal order matching via external_id
      internal_order_1 =
        insert(:order,
          organization: organization,
          external_id: "gid://shopify/Order/1",
          source: "shopify"
        )

      # 2. Internal order matching via fulfillment external_reference
      internal_order_2 =
        insert(:order, organization: organization, external_id: nil, source: "shopify")

      _fulfillment_2 =
        insert(:fulfillment,
          order: internal_order_2,
          organization: organization,
          external_reference: "gid://shopify/Order/2"
        )

      # 3. Internal order with no match
      _internal_order_3 =
        insert(:order,
          organization: organization,
          external_id: "gid://shopify/Order/unrelated",
          source: "shopify"
        )

      # Mock Shopify response
      mock_shopify_orders = [
        %{"id" => "gid://shopify/Order/1", "name" => "Shopify Order 1"},
        %{"id" => "gid://shopify/Order/2", "name" => "Shopify Order 2"},
        # No internal match
        %{"id" => "gid://shopify/Order/3", "name" => "Shopify Order 3"}
      ]

      mock_shopify_response = {:ok, %{orders: mock_shopify_orders, last_cursor: "cursor123"}}

      # Mock the Shopify client call
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :find_orders, fn _client, query_params ->
        assert query_params[:query] == "fulfillment_status:fulfilled"
        # Default limit
        assert query_params[:first] == 10
        mock_shopify_response
      end)

      result = Orders.get_external_orders(organization.id, [])

      # Assertions
      assert {:ok, %{orders: orders, metadata: metadata}} = result
      assert length(orders) == 3

      order1_data = Enum.find(orders, &(&1["id"] == "gid://shopify/Order/1"))
      order2_data = Enum.find(orders, &(&1["id"] == "gid://shopify/Order/2"))
      order3_data = Enum.find(orders, &(&1["id"] == "gid://shopify/Order/3"))

      assert order1_data["internal_order_id"] == internal_order_1.id
      assert order2_data["internal_order_id"] == internal_order_2.id
      assert order3_data["internal_order_id"] == nil

      assert metadata == %{
               after: nil,
               before: nil,
               first: 10,
               count: 3,
               last_cursor: "cursor123"
             }
    end

    test "handles pagination and filters correctly", %{org: organization} do
      insert(:order, organization: organization, external_id: "gid://shopify/Order/1")
      insert(:order, organization: organization, external_id: nil)

      mock_shopify_orders = [%{"id" => "gid://shopify/Order/4", "name" => "Shopify Order 4"}]
      mock_shopify_response = {:ok, %{orders: mock_shopify_orders, last_cursor: "cursor456"}}

      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :find_orders, fn _client, query_params ->
        assert query_params[:query] ==
                 "fulfillment_status:fulfilled AND created_at:>2024-01-01 AND #Order4"

        assert query_params[:first] == 5
        assert query_params[:after] == "cursorABC"
        assert query_params[:before] == "cursorXYZ"
        mock_shopify_response
      end)

      opts = [
        limit: 5,
        after: "cursorABC",
        before: "cursorXYZ",
        from: "2024-01-01",
        search: "#Order4"
      ]

      result = Orders.get_external_orders(organization.id, opts)

      # assert {:ok, data} = result
      assert {:ok, %{orders: orders, metadata: metadata}} = result
      assert length(orders) == 1
      order4_data = hd(orders)
      assert order4_data["id"] == "gid://shopify/Order/4"
      # No internal match created
      assert order4_data["internal_order_id"] == nil

      assert metadata == %{
               after: "cursorABC",
               before: "cursorXYZ",
               first: 5,
               count: 1,
               last_cursor: "cursor456"
             }
    end

    test "returns error if Shopify credential is not found" do
      organization = insert(:organization)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: organization
      )

      assert_raise Ecto.NoResultsError, fn ->
        Orders.get_external_orders(organization.id, [])
      end
    end
  end

  describe "get_order_by_external_identifier/2" do
    setup do
      org = insert(:organization)
      {:ok, org: org}
    end

    test "returns order matching external_id", %{org: org} do
      order = insert(:order, organization: org, external_id: "direct-match-123")
      # Noise
      insert(:order, organization: org, external_id: "other-id")

      found_order = Orders.get_order_by_external_identifier(org.id, "direct-match-123")
      assert found_order
      assert found_order.id == order.id
      assert found_order.external_id == "direct-match-123"
    end

    test "returns order matching fulfillment external_reference", %{org: org} do
      order1 = insert(:order, organization: org, external_id: "no-match")

      _fulfillment =
        insert(:fulfillment,
          order: order1,
          organization: org,
          external_reference: "fulfillment-match-456"
        )

      # Noise
      order2 = insert(:order, organization: org)
      insert(:fulfillment, order: order2, organization: org, external_reference: "other-ref")

      found_order = Orders.get_order_by_external_identifier(org.id, "fulfillment-match-456")
      assert found_order
      assert found_order.id == order1.id
      # Ensure we didn't just match on order1's external_id
      refute found_order.external_id == "fulfillment-match-456"
    end

    test "prioritizes direct match over fulfillment match", %{org: org} do
      order_direct = insert(:order, organization: org, external_id: "match-789")
      order_fulfillment = insert(:order, organization: org)

      insert(:fulfillment,
        order: order_fulfillment,
        organization: org,
        external_reference: "match-789"
      )

      found_order = Orders.get_order_by_external_identifier(org.id, "match-789")
      assert found_order
      # Should be the order with the direct external_id match
      assert found_order.id == order_direct.id
      assert found_order.external_id == "match-789"
    end

    test "returns nil if no match found", %{org: org} do
      insert(:order, organization: org, external_id: "some-id")
      order2 = insert(:order, organization: org)
      insert(:fulfillment, order: order2, organization: org, external_reference: "some-ref")

      found_order = Orders.get_order_by_external_identifier(org.id, "no-match-at-all")
      assert is_nil(found_order)
    end

    test "returns nil if identifier matches but for different organization", %{org: org} do
      org2 = insert(:organization)
      insert(:order, organization: org2, external_id: "match-but-wrong-org")

      found_order = Orders.get_order_by_external_identifier(org.id, "match-but-wrong-org")
      assert is_nil(found_order)
    end
  end
end
