defmodule Rms.Commerce.PackingsTest do
  use Rms.DataCase
  use Rms.EventsCase

  import Rms.Factory

  alias Rms.Commerce.Packings

  use ExUnit.Case

  setup do
    org = insert(:organization)
    location = insert(:location, organization: org)
    dock = insert(:dock, organization: org, location: location)
    order = insert(:order, organization: org)
    fulfillment = insert(:fulfillment, organization: org, order: order)
    product = insert(:product, organization: org)
    pv = insert(:product_variant, organization: org, product: product)

    line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        location: location,
        product_variant: pv
      )

    {:ok,
     organization: org,
     location: location,
     dock: dock,
     order: order,
     fulfillment: fulfillment,
     line_item: line_item,
     product_variant: pv}
  end

  describe "get_packing!/3" do
    test "returns inserted packing", %{organization: org, dock: dock, fulfillment: fulfillment} do
      p1 =
        insert(:packing,
          organization: org,
          dock: dock,
          fulfillment: fulfillment,
          shipping_method: "delivery"
        )

      packing = Packings.get_packing!(org.id, p1.id)
      assert packing.id == p1.id
    end
  end

  describe "create_packing/2" do
    test "successfully creates a packing", %{
      organization: org,
      dock: dock,
      fulfillment: fulfillment
    } do
      attrs =
        params_for(:packing,
          dock_id: dock.id,
          shipping_method: "delivery"
        )

      assert {:ok, packing} = Packings.create_packing(org.id, fulfillment.id, attrs)
      assert packing.dock_id == dock.id
      assert packing.fulfillment_id == fulfillment.id
      assert packing.status == "pending"
      assert packing.shipping_method == "delivery"
    end

    test "successfully creates a full packing", %{
      organization: org,
      dock: dock,
      fulfillment: fulfillment
    } do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)

      attrs =
        params_for(:packing,
          dock_id: dock.id,
          shipping_method: "delivery",
          customer_id: customer.id,
          staff_id: staff.id,
          courier_name: "Fedex"
        )

      assert {:ok, packing} = Packings.create_packing(org.id, fulfillment.id, attrs)
      assert packing.dock_id == dock.id
      assert packing.fulfillment_id == fulfillment.id
      assert packing.status == "pending"
      assert packing.shipping_method == "delivery"
      assert packing.shipping_method == "delivery"
      assert packing.customer_id == customer.id
      assert packing.staff_id == staff.id
      assert packing.courier_name == "Fedex"
    end

    test "returns an error when creating a packing with invalid data" do
      attrs = %{name: nil, ncm: nil}
      assert {:error, _} = Packings.create_packing(1, 1, attrs)
    end

    test "creates associated packing_items", %{
      organization: org,
      dock: dock,
      fulfillment: fulfillment,
      line_item: line_item
    } do
      attrs =
        %{
          dock_id: dock.id,
          shipping_method: "delivery",
          packing_items: [
            %{
              quantity: 1,
              line_item_id: line_item.id
            },
            %{
              quantity: 2,
              line_item_id: line_item.id
            }
          ],
          total_price: Decimal.new("100.00")
        }

      assert {:ok, packing} =
               Packings.create_packing(org.id, fulfillment.id, attrs)

      packing = Rms.Repo.preload(packing, packing_items: [line_item: [:fulfillment]])

      assert packing.dock_id == dock.id
      assert packing.fulfillment_id == fulfillment.id
      assert packing.status == "pending"
      assert packing.shipping_method == "delivery"
      assert packing.total_price == Decimal.new("100.00")
      assert packing_items = packing.packing_items
      assert Enum.count(packing_items) == 2
      assert Enum.all?(packing_items, fn pi -> pi.line_item_id == line_item.id end)
    end

    test "doesnt allow creating packing_items with other fulfillment", %{
      organization: org,
      location: loc,
      dock: dock,
      order: order,
      fulfillment: fulfillment,
      line_item: line_item,
      product_variant: pv
    } do
      fulfillment_2 = insert(:fulfillment, organization: org, order: order)

      line_item_2 =
        insert(:line_item,
          organization: org,
          fulfillment: fulfillment_2,
          product_variant: pv,
          location: loc
        )

      attrs =
        %{
          dock_id: dock.id,
          shipping_method: "delivery",
          packing_items: [
            %{
              quantity: 1,
              line_item_id: line_item_2.id
            },
            %{
              quantity: 2,
              line_item_id: line_item.id
            }
          ],
          total_price: Decimal.new("100.00")
        }

      assert {:error, changeset} =
               Packings.create_packing(org.id, fulfillment.id, attrs)

      assert [%{line_item: ["does not exist"]} | _] =
               errors_on(changeset).packing_items
    end
  end

  describe "paginated_packings/2" do
    test "returns a paginated list of orders", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert_list(20, :packing,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      opts = [limit: 5]
      results = Packings.paginated_packings(organization.id, opts)

      assert length(results.entries) == 5
      assert results.metadata.after
      assert Map.has_key?(results.metadata, :before)
    end

    test "can filter by status", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert(:packing,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery",
        status: "separated"
      )

      insert(:packing,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery",
        status: "pending"
      )

      opts = [status: "separated"]
      result = Packings.paginated_packings(organization.id, opts)

      assert [%{status: "separated"}] = result.entries
    end

    test "can filter by location_id", %{organization: organization, fulfillment: fulfillment} do
      location1 = insert(:location, organization: organization)
      location2 = insert(:location, organization: organization)

      dock1 = insert(:dock, organization: organization, location: location1)
      dock2 = insert(:dock, organization: organization, location: location2)

      insert(:packing,
        organization: organization,
        dock: dock1,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      insert(:packing,
        organization: organization,
        dock: dock1,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      insert(:packing,
        organization: organization,
        dock: dock1,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      insert(:packing,
        organization: organization,
        dock: dock2,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      insert(:packing,
        organization: organization,
        dock: dock2,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      opts = [location_id: location1.id, preloads: :dock]
      result = Packings.paginated_packings(organization.id, opts)
      assert length(result.entries) == 3
      assert Enum.all?(result.entries, fn entry -> entry.dock.location_id == location1.id end)
    end

    test "can filter by inserted_at", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment
    } do
      date = ~N[2023-01-01 07:00:00]

      insert(:packing,
        inserted_at: date,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment
      )

      opts = [inserted_at: date]
      result = Packings.paginated_packings(organization.id, opts)

      assert [%{inserted_at: inserted_at}] = result.entries
      assert Date.compare(NaiveDateTime.to_date(inserted_at), ~D[2023-01-01]) == :eq
    end

    test "can filter by inserted_at following iso8601", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment
    } do
      date_iso8601 = "2023-01-01T07:00:00Z"
      date = NaiveDateTime.from_iso8601!(date_iso8601)

      insert(:packing,
        inserted_at: date,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment
      )

      opts = [inserted_at: date_iso8601]
      result = Packings.paginated_packings(organization.id, opts)

      assert [%{inserted_at: inserted_at}] = result.entries
      assert Date.compare(NaiveDateTime.to_date(inserted_at), ~D[2023-01-01]) == :eq
    end

    test "paginates using the after cursor", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert_list(20, :packing,
        organization: organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      first_page_opts = [limit: 5]
      first_page_results = Packings.paginated_packings(organization.id, first_page_opts)

      second_page_opts = [limit: 5, after: first_page_results.metadata.after]
      second_page_results = Packings.paginated_packings(organization.id, second_page_opts)

      first_page_ids_set =
        first_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      second_page_ids_set =
        second_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      assert MapSet.disjoint?(second_page_ids_set, first_page_ids_set)
    end

    test "preloads associations if specified", %{
      organization: organization,
      dock: dock,
      fulfillment: fulfillment,
      line_item: line_item
    } do
      packing =
        insert(:packing,
          organization: organization,
          dock: dock,
          fulfillment: fulfillment,
          shipping_method: "delivery",
          status: "separated"
        )

      insert_list(5, :packing_item,
        organization: organization,
        packing: packing,
        line_item: line_item,
        fulfillment: fulfillment
      )

      opts = [preloads: [dock: :location, packing_items: :line_item], limit: 1]
      results = Packings.paginated_packings(organization.id, opts)
      packing = hd(results.entries)

      assert Enum.all?(results.entries, fn packing -> Map.has_key?(packing, :dock) end)
      assert length(packing.packing_items) == 5
      assert length(packing.packing_items |> Enum.map(& &1.line_item)) > 0
    end
  end
end
