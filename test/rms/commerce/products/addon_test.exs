defmodule Rms.Commerce.Products.AddonTest do
  use Rms.DataCase

  alias Rms.Commerce.Products.Addon

  describe "changeset/2" do
    test "validates required fields for new addon" do
      attrs = %{}
      changeset = Addon.changeset(%Addon{}, attrs)
      assert {:error, changeset} = Ecto.Changeset.apply_action(changeset, :insert)
      assert %{name: ["can't be blank"], list_price: ["can't be blank"]} = errors_on(changeset)
    end

    test "does not require fields for existing addon" do
      addon = insert(:addon)
      changeset = Addon.changeset(addon, %{})
      assert {:ok, _updated_addon} = Ecto.Changeset.apply_action(changeset, :update)
    end

    test "sets price equal to list_price when price is nil" do
      attrs = %{name: "Test Addon", list_price: Decimal.new("10.00")}
      changeset = Addon.changeset(%Addon{}, attrs)
      assert {:ok, addon} = Ecto.Changeset.apply_action(changeset, :insert)
      assert Decimal.eq?(addon.price, Decimal.new("10.00"))
    end

    test "keeps existing price when price is provided" do
      attrs = %{name: "Test Addon", list_price: Decimal.new("10.00"), price: Decimal.new("8.00")}
      changeset = Addon.changeset(%Addon{}, attrs)
      assert {:ok, addon} = Ecto.Changeset.apply_action(changeset, :insert)
      assert Decimal.eq?(addon.price, Decimal.new("8.00"))
    end
  end

  describe "archive_changeset/2" do
    test "sets archived_at to current time" do
      addon = insert(:addon)
      now = DateTime.utc_now()
      changeset = Addon.archive_changeset(addon, now)
      assert {:ok, archived_addon} = Ecto.Changeset.apply_action(changeset, :update)
      assert archived_addon.archived_at == DateTime.truncate(now, :second)
    end
  end
end
