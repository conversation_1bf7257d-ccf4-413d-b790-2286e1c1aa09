defmodule Rms.Commerce.Products.ProductTest do
  use ExUnit.Case, async: true

  import Rms.DataCase

  alias Rms.Commerce.Products.Product

  describe "changeset/2" do
    test "validates required fileds" do
      product = %Product{organization_id: 1}
      attrs = %{}

      assert changeset = Product.changeset(product, attrs)
      refute changeset.valid?
      refute errors_on(changeset)[:organization_id]
      assert "can't be blank" in errors_on(changeset).name
    end

    test "cast associated product_variants" do
      product = %Product{organization_id: 1}

      attrs = %{
        name: "Product Name",
        ncm: "12345678",
        product_variants: [
          %{
            name: "Product Variant Name",
            sku: "12345678",
            bar_code: "12345678",
            image_urls: ["http://example.com/image.jpg"],
            variation_type: %{"size" => "M"}
          }
        ]
      }

      assert changeset = Product.changeset(product, attrs)

      assert [product_variant_changeset] =
               Ecto.Changeset.fetch_change!(changeset, :product_variants)

      assert 1 = Ecto.Changeset.fetch_field!(product_variant_changeset, :organization_id)
    end
  end
end
