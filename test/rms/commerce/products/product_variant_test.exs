defmodule Rms.Commerce.Products.ProductVariantTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Commerce.Products.ProductVariant

  setup do
    org = insert(:organization)

    product_variant =
      insert(:product_variant, organization: org, product: build(:product, organization: org))

    attrs = %{
      name: "Variant 1",
      list_price: "10.0",
      sku: "SKU123",
      bar_code: "123456789012",
      image_urls: ["http://example.com/image1.jpg"],
      variation_types: [%{"key" => "color", "value" => "red"}]
    }

    {:ok, product_variant: product_variant, organization: org, default_attrs: attrs}
  end

  describe "changeset/2" do
    test "cast embedded schema variation_types", %{
      product_variant: product_variant,
      default_attrs: attrs
    } do
      changeset =
        ProductVariant.changeset(
          product_variant,
          attrs
        )

      assert changeset.valid?
      assert [variation_type] = changeset.changes.variation_types
      assert %{key: "color", value: "red"} = variation_type.changes
    end

    test "sets price to list_price when price is null", %{
      product_variant: product_variant,
      default_attrs: attrs
    } do
      attrs = Map.put(attrs, :price, nil)
      changeset = ProductVariant.changeset(product_variant, attrs)

      assert {:ok, result} = Ecto.Changeset.apply_action(changeset, :insert)
      assert result.price == Decimal.new(attrs.list_price)
    end

    test "generates sync_metadata when not provided", %{default_attrs: attrs, organization: org} do
      product_variant = %ProductVariant{
        organization_id: org.id,
        product_id: insert(:product, organization: org).id
      }

      changeset = ProductVariant.changeset(product_variant, attrs)
      assert {:ok, result} = Repo.insert(changeset)

      assert %{"field_sources" => field_sources} = result.sync_metadata
      assert Map.has_key?(field_sources, "name")
      assert Map.has_key?(field_sources, "list_price")
      assert Map.has_key?(field_sources, "sku")

      assert %{"source" => "internal"} = field_sources["name"]
      assert {:ok, _, _} = DateTime.from_iso8601(field_sources["name"]["updated_at"])
    end

    test "merges with provided sync_metadata", %{default_attrs: attrs, organization: org} do
      sync_metadata = %{
        "messages" => ["Synced with VTEX"],
        "field_sources" => %{
          "price" => %{
            "source" => "vtex",
            "updated_at" => "2024-01-01T00:00:00Z"
          }
        }
      }

      attrs = Map.put(attrs, :sync_metadata, sync_metadata)
      attrs = Map.put(attrs, :name, "New Name")

      product_variant = %ProductVariant{
        organization_id: org.id,
        product_id: insert(:product, organization: org).id
      }

      changeset = ProductVariant.changeset(product_variant, attrs, "internal")

      assert {:ok, result} = Repo.insert(changeset)

      field_sources = result.sync_metadata["field_sources"]

      # Messages should be preserved
      assert ["Synced with VTEX"] = result.sync_metadata["messages"]

      # New field should have internal source
      assert %{
               "source" => "internal"
             } = field_sources["name"]
    end

    test "tracks field changes in sync_metadata with default source", %{
      default_attrs: attrs,
      organization: org
    } do
      product_variant = %ProductVariant{
        organization_id: org.id,
        product_id: insert(:product, organization: org).id
      }

      changeset = ProductVariant.changeset(product_variant, attrs)
      assert {:ok, result} = Repo.insert(changeset)

      assert %{
               "field_sources" => field_sources
             } = result.sync_metadata

      assert %{"source" => "internal"} = field_sources["name"]
      assert %{"source" => "internal"} = field_sources["sku"]
      assert %{"source" => "internal"} = field_sources["list_price"]

      # Verify timestamps are in ISO8601 format
      assert {:ok, _, _} = DateTime.from_iso8601(field_sources["name"]["updated_at"])
    end

    test "tracks field changes in sync_metadata with custom source", %{
      organization: org,
      default_attrs: attrs
    } do
      product_variant = %ProductVariant{
        organization_id: org.id,
        product_id: insert(:product, organization: org).id
      }

      changeset =
        ProductVariant.changeset(product_variant, attrs, "shopify")

      assert {:ok, result} = Repo.insert(changeset)

      assert %{
               "field_sources" => field_sources
             } = result.sync_metadata

      assert %{"source" => "shopify"} = field_sources["name"]
      assert %{"source" => "shopify"} = field_sources["sku"]
      assert %{"source" => "shopify"} = field_sources["list_price"]
    end

    test "preserves existing sync_metadata for unchanged fields", %{organization: org} do
      existing_metadata = %{
        "field_sources" => %{
          "list_price" => %{
            "source" => "shopify",
            "updated_at" => "2024-01-01T00:00:00Z"
          }
        }
      }

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          sync_metadata: existing_metadata,
          name: "Variant 1",
          list_price: "10.0",
          price: "10.0",
          sku: "SKU123",
          bar_code: "123456789012",
          image_urls: ["http://example.com/image1.jpg"],
          variation_types: [%{key: "color", value: "red"}]
        )

      # Update only the price
      changeset =
        ProductVariant.changeset(product_variant, %{price: "15.0"}, "linx_pos")

      assert {:ok, result} = Repo.update(changeset)

      field_sources = result.sync_metadata["field_sources"]

      # Original list_price metadata should be preserved
      assert %{
               "source" => "shopify",
               "updated_at" => "2024-01-01T00:00:00Z"
             } = field_sources["list_price"]

      # New price metadata should be added
      assert %{
               "source" => "linx_pos"
             } = field_sources["price"]
    end

    test "does not update sync_metadata when no fields change", %{organization: org} do
      existing_metadata = %{
        "field_sources" => %{
          "price" => %{
            "source" => "shopify",
            "updated_at" => "2024-01-01T00:00:00Z"
          }
        }
      }

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          sync_metadata: existing_metadata,
          name: "Variant 1",
          list_price: "10.0",
          price: "10.0",
          sku: "SKU123",
          bar_code: "123456789012",
          image_urls: ["http://example.com/image1.jpg"],
          variation_types: [%{key: "color", value: "red"}]
        )

      changeset = ProductVariant.changeset(product_variant, %{}, "linx_pos")

      assert {:ok, result} = Repo.update(changeset)

      # Done like this because `name` alwyas changes
      assert result.sync_metadata["field_sources"]["price"] ==
               existing_metadata["field_sources"]["price"]
    end

    test "respects product sync configuration from same source", %{organization: org} do
      insert(:product_sync_configuration,
        organization: org,
        field_priorities: %{
          "name" => ["shopify", "linx_pos"],
          "price" => ["linx_pos", "shopify"]
        },
        default_priority: ["shopify", "linx_pos"]
      )

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          name: "Initial Variant",
          price: Decimal.new("10.00"),
          sync_metadata: %{
            "field_sources" => %{
              "name" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"},
              "price" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      # Update from same source should work
      changeset =
        ProductVariant.changeset(
          product_variant,
          %{name: "Updated from Shopify", price: "20.00"},
          "shopify"
        )

      assert {:ok, updated_variant} = Repo.update(changeset)

      assert updated_variant.name == "Updated from Shopify"
      assert Decimal.equal?(updated_variant.price, Decimal.new("20.00"))
    end

    test "respects product sync configuration priorities between different sources", %{
      organization: org
    } do
      insert(:product_sync_configuration,
        organization: org,
        field_priorities: %{
          "name" => ["shopify", "linx_pos"],
          "price" => ["linx_pos", "shopify"]
        },
        default_priority: ["shopify", "linx_pos"]
      )

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          name: "Initial Variant",
          price: Decimal.new("10.00"),
          sync_metadata: %{
            "field_sources" => %{
              "name" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"},
              "price" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      # Update from lower priority source should not override
      changeset =
        ProductVariant.changeset(
          product_variant,
          %{name: "Updated from Linx", price: "20.00"},
          "linx_pos"
        )

      assert {:ok, updated_variant} = Repo.update(changeset)

      # Name should not change (shopify has higher priority for name)
      assert updated_variant.name == "Initial Variant"
      # Price should change (linx_pos has higher priority for price)
      assert Decimal.equal?(updated_variant.price, Decimal.new("20.00"))
    end

    test "uses default priority when field is not in configuration", %{organization: org} do
      insert(:product_sync_configuration,
        organization: org,
        field_priorities: %{
          "name" => ["shopify", "linx_pos"]
        },
        default_priority: ["linx_pos", "shopify"]
      )

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org),
          name: "Initial Variant",
          price: Decimal.new("10.00"),
          bar_code: "123",
          sync_metadata: %{
            "field_sources" => %{
              "bar_code" => %{"source" => "linx_pos", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      # Update using default priority
      changeset =
        ProductVariant.changeset(product_variant, %{bar_code: "456"}, "shopify")

      assert {:ok, updated_variant} = Repo.update(changeset)

      # bar_code should not change (linx_pos has higher priority in default)
      assert updated_variant.bar_code == "123"
    end
  end
end
