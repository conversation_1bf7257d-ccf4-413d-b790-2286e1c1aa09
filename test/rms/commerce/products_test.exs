defmodule Rms.Commerce.ProductsTest do
  use Rms.DataCase
  use Rms.EventsCase

  import Rms.Factory

  alias Rms.Commerce.Products
  alias Rms.Commerce.Products.Product
  alias Rms.Commerce.Products.InventoryItem

  use ExUnit.Case

  describe "list_products/1" do
    test "returns all products for a given organization" do
      org = insert(:organization)

      insert(:product, organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      insert(:product, organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      products = Products.list_products(org.id)

      assert length(products) == 2
      assert Enum.all?(products, fn product -> product.organization_id == org.id end)
    end
  end

  describe "get_product!/2" do
    test "retrieves a product by ID and organization ID" do
      product = insert(:product)
      retrieved_product = Products.get_product!(product.organization_id, product.id)
      assert retrieved_product.id == product.id
      assert retrieved_product.organization_id == product.organization_id
    end

    test "raises if the product does not exist" do
      assert_raise Ecto.NoResultsError, fn ->
        Products.get_product!(1, -1)
      end
    end

    test "raises if the product does not belong to the organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)
      product = insert(:product, organization: org1)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_product!(org2.id, product.id)
      end
    end
  end

  describe "get_product_by_barcode!/2" do
    test "returns the no archived product when multiple products have the same barcode" do
      org = insert(:organization)

      # Create first product (older)
      first_product = insert(:product, organization: org, inserted_at: ~N[2024-01-01 00:00:00])

      insert(:product_variant,
        product: first_product,
        organization: org,
        bar_code: "SAME123"
      )

      # Create second product (newer)
      second_product =
        insert(:product,
          organization: org,
          inserted_at: ~N[2024-01-02 00:00:00]
        )

      insert(:product_variant,
        product: second_product,
        organization: org,
        bar_code: "SAME123",
        archived_at: ~N[2024-01-01 00:00:00]
      )

      # Should return the second (newer) product
      result = Products.get_product_by_barcode!(org.id, "SAME123")
      assert result.id == first_product.id
    end

    test "returns the newest product when multiple products have the same barcode" do
      org = insert(:organization)

      # Create first product (older)
      first_product = insert(:product, organization: org)

      insert(:product_variant,
        product: first_product,
        organization: org,
        bar_code: "SAME123",
        inserted_at: ~N[2024-01-01 00:00:00]
      )

      # Create second product (newer)
      second_product = insert(:product, organization: org)

      insert(:product_variant,
        product: second_product,
        organization: org,
        bar_code: "SAME123",
        inserted_at: ~N[2024-01-02 00:00:00]
      )

      # Should return the second (newer) product
      result = Products.get_product_by_barcode!(org.id, "SAME123")
      assert result.id == second_product.id
    end

    test "retrieves a product and its variants by barcode" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      insert(:product_variant, organization: org, product: product, bar_code: "123456789")
      insert(:product_variant, organization: org, product: product)

      retrieved_product = Products.get_product_by_barcode!(org.id, "123456789")

      assert retrieved_product.id == product.id
      assert length(retrieved_product.product_variants) == 2

      assert Enum.all?(retrieved_product.product_variants, fn variant ->
               variant.product_id == product.id
             end)
    end

    test "raises if no product variant with given barcode is found" do
      assert_raise Ecto.NoResultsError, fn ->
        Products.get_product_by_barcode!(1, "nonexistent_barcode")
      end
    end

    test "raises if no product variant with given barcode is found within organization" do
      org = insert(:organization)
      other_org = insert(:organization)
      product = insert(:product, organization: org)
      insert(:product_variant, organization: org, product: product, bar_code: "123456789")

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_product_by_barcode!(other_org.id, "123456789")
      end
    end
  end

  describe "create_product/2" do
    test "successfully creates a product" do
      attrs = %{name: "New Product", ncm: "some ncm"}
      org = insert(:organization)
      assert {:ok, product} = Products.create_product(org.id, attrs)
      assert product.name == "New Product"
      assert product.ncm == "some ncm"
    end

    test "returns an error when creating a product with invalid data" do
      attrs = %{name: nil, ncm: nil}
      assert {:error, _} = Products.create_product(1, attrs)
    end

    test "creates associated product variants" do
      attrs =
        string_params_for(:product,
          name: "Meia",
          product_variants: [
            string_params_for(:product_variant,
              name: nil,
              variation_types: [%{key: "cor", value: "Marrom"}]
            )
          ]
        )

      organization = insert(:organization)
      organization_id = organization.id

      assert {:ok, product} =
               Products.create_product(organization_id, attrs)

      assert [product_variant] = product.product_variants
      product_variant = Rms.Repo.reload(product_variant, force: true)
      assert product_variant.product_id == product.id
      assert product_variant.name == "Meia Marrom"
    end
  end

  describe "update_product/2" do
    test "updates the specified fields of a product" do
      product = insert(:product, name: "Product Name", ncm: "some ncm")
      update_attrs = %{name: "Updated Product Name", ncm: "updated ncm"}

      assert {:ok, updated_product} = Products.update_product(product, update_attrs)
      assert updated_product.name == "Updated Product Name"
      assert updated_product.ncm == "updated ncm"
    end

    test "does not update organization_id" do
      org1 = insert(:organization)
      org2 = insert(:organization)
      product = insert(:product, organization: org1)
      update_attrs = %{organization_id: org2.id}

      assert {:ok, _} = Products.update_product(product, update_attrs)

      updated_product = Rms.Repo.get!(Product, product.id)
      assert updated_product.organization_id == org1.id
    end

    test "update associated product_variants" do
      org = insert(:organization)
      product = insert(:product, organization: org)

      variant =
        insert(:product_variant,
          product: product,
          organization: org,
          list_price: Decimal.new("10.0")
        )

      update_attrs = %{
        product_variants: [
          %{id: variant.id, list_price: Decimal.new("15.0")}
        ]
      }

      product = Rms.Repo.preload(product, [:product_variants])
      assert {:ok, _updated_product} = Products.update_product(product, update_attrs)

      updated_variant = Rms.Repo.reload!(variant, force: true)
      assert Decimal.eq?(updated_variant.list_price, Decimal.new("15.0"))
    end

    test "does not allow deleting associated product_variants by setting product_variants to empty list on update" do
      org = insert(:organization)
      product = insert(:product, organization: org)

      insert(:product_variant,
        product: product,
        organization: org,
        list_price: Decimal.new("10.0")
      )

      product = Rms.Repo.preload(product, [:product_variants])

      assert {:error, changeset} = Products.update_product(product, %{product_variants: []})
      assert "is invalid" in errors_on(changeset).product_variants
    end

    test "does not return an error if there's no product_variants in update args" do
      org = insert(:organization)
      product = insert(:product, organization: org)

      insert(:product_variant,
        product: product,
        organization: org,
        list_price: Decimal.new("10.0")
      )

      product = Rms.Repo.preload(product, [:product_variants])

      assert {:ok, _product} = Products.update_product(product, %{name: "New Name"})
    end
  end

  describe "get_product_variants!/2" do
    test "retrieves all variants for a given product and organization" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      variant1 = insert(:product_variant, product: product, organization: org)
      variant2 = insert(:product_variant, product: product, organization: org)

      variants =
        Products.get_product_variants!(org.id, [variant1.id, variant2.id])

      assert Kernel.map_size(variants) == 2
      assert Enum.any?(variants, fn {k, v} -> k == v.id and v.id == variant1.id end)
      assert Enum.any?(variants, fn {k, v} -> k == v.id and v.id == variant2.id end)
    end
  end

  describe "delete_product/1" do
    test "deletes a product" do
      product = insert(:product)
      assert {:ok, _} = Products.delete_product(product)

      refute Rms.Repo.get(Product, product.id)
    end
  end

  describe "paginated_products/2" do
    setup do
      organization = insert(:organization)
      # Insert multiple products with variants for testing
      products = insert_list(20, :product, organization: organization)

      Enum.each(products, fn product ->
        insert_pair(:product_variant, product: product, organization: product.organization)
      end)

      {:ok, organization_id: organization.id}
    end

    test "paging works correctly", %{organization_id: organization_id} do
      opts = [limit: 10]
      results = Products.paginated_products(organization_id, opts)
      assert length(results.entries) == 10

      opts = [limit: 5, after: results.metadata.after]
      next_page_results = Products.paginated_products(organization_id, opts)
      assert length(next_page_results.entries) == 5

      result_entries_id = Enum.map(results.entries, & &1.id)
      next_page_results_entries_id = Enum.map(next_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(result_entries_id),
               MapSet.new(next_page_results_entries_id)
             )

      opts = [limit: 5, before: next_page_results.metadata.before]
      previous_page_results = Products.paginated_products(organization_id, opts)
      assert length(previous_page_results.entries) == 5

      previous_page_results_entries_id = Enum.map(previous_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(previous_page_results_entries_id),
               MapSet.new(next_page_results_entries_id)
             )
    end

    test "search works correctly", %{organization_id: organization_id} do
      search_opts = [search: "Product", limit: 5]
      search_results = Products.paginated_products(organization_id, search_opts)
      assert length(search_results.entries) == 5

      assert Enum.all?(search_results.entries, &(&1.name =~ "Product"))
    end

    test "search gets partial matches" do
      org = insert(:organization)

      insert(:product, name: "Shirt", organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      insert(:product, name: "Special Shirt ", organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      insert(:product, name: "Very Special Shirt", organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      insert(:product, name: "Extremely Very Special Shirt", organization: org)
      |> then(&insert(:product_variant, product: &1, organization: org))

      search_opts = [search: "vert", limit: 20]
      search_results = Products.paginated_products(org.id, search_opts)

      assert length(search_results.entries) == 2
      assert Enum.all?(search_results.entries, &(&1.name =~ ~r/.*very.*/i))
    end

    test "search gets variants" do
      org = insert(:organization)
      product = insert(:product, name: "Shirt", organization: org)
      insert(:product_variant, name: "Grey shirt", product: product, organization: org)
      insert(:product_variant, name: "Dark grey shirt", product: product, organization: org)

      search_opts = [search: "gray", limit: 20]
      search_results = Products.paginated_products(org.id, search_opts)

      assert search_results.entries == []
    end

    test "search by sku works" do
      org = insert(:organization)

      product = insert(:product, name: "Generic Product", organization: org)

      insert(:product_variant, sku: "ABC123", product: product, organization: org)
      insert(:product_variant, sku: "DEF456", product: product, organization: org)

      search_opts = [search: "ABC123", limit: 20]
      search_results = Products.paginated_products(org.id, search_opts)

      assert length(search_results.entries) == 1
      assert hd(search_results.entries).id == product.id
    end

    test "search does not duplicate results", %{organization_id: organization_id} do
      search_opts = [search: "Product", limit: 20]

      search_results = Products.paginated_products(organization_id, search_opts)
      product_ids = Enum.map(search_results.entries, fn p -> p.id end)
      assert product_ids == Enum.uniq(product_ids)
    end
  end

  describe "create_inventory_item/2" do
    test "create a new inventory item" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      attrs = %{
        quantity: 10,
        location_id: loc.id,
        product_variant_id: pv.id
      }

      assert {:ok, _} = Products.create_inventory_item(org.id, attrs)
    end

    test "creating two conflicting inventory items replaces updated_at and quantity" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      before =
        DateTime.utc_now()
        |> DateTime.add(-5, :second)

      inventory_item =
        insert(:inventory_item,
          organization: org,
          location: loc,
          product_variant: pv,
          quantity: 10,
          updated_at: before,
          inserted_at: before
        )

      attrs = %{
        quantity: 9,
        location_id: loc.id,
        product_variant_id: pv.id
      }

      assert {:ok, new_inventory_item} =
               Products.create_inventory_item(org.id, attrs)

      assert inventory_item.quantity == 10
      assert new_inventory_item.quantity == 9

      assert NaiveDateTime.after?(new_inventory_item.updated_at, inventory_item.updated_at)
    end

    test "does not create when product does not belongs to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      attrs = %{
        quantity: 10,
        location_id: loc.id,
        product_variant_id: pv.id
      }

      assert {:error, _} = Products.create_inventory_item(another_org.id, attrs)
    end
  end

  describe "list_inventory_item/3" do
    test "list all inventory item from a organization" do
      org1 = insert(:organization)
      loc1 = insert(:location, organization: org1)

      product1 = insert(:product, organization: org1)

      pv1 = insert(:product_variant, product: product1, organization: org1)
      _ii1 = insert(:inventory_item, organization: org1, location: loc1, product_variant: pv1)

      pv2 = insert(:product_variant, product: product1, organization: org1)
      _ii2 = insert(:inventory_item, organization: org1, location: loc1, product_variant: pv2)

      org2 = insert(:organization)
      loc2 = insert(:location, organization: org2)

      product2 = insert(:product, organization: org2)

      pv1 = insert(:product_variant, product: product2, organization: org2)
      _ii2 = insert(:inventory_item, organization: org2, location: loc2, product_variant: pv1)

      pv2 = insert(:product_variant, product: product2, organization: org2)
      _ii2 = insert(:inventory_item, organization: org2, location: loc2, product_variant: pv2)

      inventory_items = Products.list_inventory_items(org1.id)
      assert length(inventory_items) == 2

      assert Enum.all?(inventory_items, fn i ->
               i.organization_id == org1.id
             end)
    end

    test "list all serie from a organization using some filters" do
      org1 = insert(:organization)
      loc1 = insert(:location, organization: org1)
      loc2 = insert(:location, organization: org1)

      product1 = insert(:product, organization: org1)

      pv1 = insert(:product_variant, product: product1, organization: org1)
      _ii11 = insert(:inventory_item, organization: org1, location: loc1, product_variant: pv1)
      ii12 = insert(:inventory_item, organization: org1, location: loc2, product_variant: pv1)

      pv2 = insert(:product_variant, product: product1, organization: org1)
      _ii21 = insert(:inventory_item, organization: org1, location: loc1, product_variant: pv2)
      _ii22 = insert(:inventory_item, organization: org1, location: loc2, product_variant: pv2)

      [inventory_item] =
        Products.list_inventory_items(org1.id,
          query_params: [location_id: loc2.id, product_variant_id: pv1.id]
        )

      assert inventory_item.id == ii12.id
    end
  end

  describe "get_inventory_item!/2" do
    test "returns a inventory item" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      ii = insert(:inventory_item, organization: org, location: loc, product_variant: pv)

      assert inventory_item = Products.get_inventory_item!(org.id, ii.id)

      assert ii.id == inventory_item.id
    end

    test "raises Ecto.NoResultsError when inventory item does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_inventory_item!(org.id, 1)
      end
    end
  end

  describe "get_inventory_item_by_sku!/3" do
    test "retrieves an inventory item by SKU" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org, sku: "SKU123")

      inventory_item =
        insert(:inventory_item, organization: org, location: loc, product_variant: pv)

      retrieved_inventory_item = Products.get_inventory_item_by_sku!(org.id, "SKU123")

      assert retrieved_inventory_item.id == inventory_item.id
      assert retrieved_inventory_item.organization_id == org.id
      assert retrieved_inventory_item.product_variant_id == pv.id
    end

    test "raises Ecto.NoResultsError if no inventory item with given SKU is found" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_inventory_item_by_sku!(org.id, "nonexistent_sku")
      end
    end

    test "raises Ecto.NoResultsError if no inventory item with given SKU is found within organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)
      loc = insert(:location, organization: org1)

      product = insert(:product, organization: org1)
      pv = insert(:product_variant, product: product, organization: org1, sku: "SKU123")

      insert(:inventory_item, organization: org1, location: loc, product_variant: pv)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_inventory_item_by_sku!(org2.id, "SKU123")
      end
    end
  end

  describe "delete_inventory_item/1" do
    test "deletes an inventory item" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      product = insert(:product, organization: org)

      pv = insert(:product_variant, product: product, organization: org)
      ii = insert(:inventory_item, organization: org, location: loc, product_variant: pv)

      assert {:ok, inventory} = Products.delete_inventory_item(ii)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_inventory_item!(org.id, inventory.id)
      end
    end
  end

  describe "update_inventory_item/2" do
    test "only update quantity field" do
      org = insert(:organization)
      another_org = insert(:organization)
      loc = insert(:location, organization: org)
      another_loc = insert(:location, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)
      another_pv = insert(:product_variant, product: product, organization: org)

      ii =
        insert(:inventory_item,
          organization: org,
          location: loc,
          product_variant: pv,
          quantity: 10
        )

      update_attrs = %{
        quantity: 20,
        organization_id: another_org.id,
        location_id: another_loc.id,
        product_variant_id: another_pv.id
      }

      assert {:ok, updated_inventory} = Products.update_inventory_item(ii, update_attrs)
      assert updated_inventory.quantity == update_attrs.quantity
      refute updated_inventory.organization_id == update_attrs.organization_id
      refute updated_inventory.location_id == update_attrs.location_id
      refute updated_inventory.product_variant_id == update_attrs.product_variant_id
    end
  end

  describe "create_inventory_item_by_sku/3" do
    test "creates inventory item when SKU exists" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org, sku: "TEST123")

      attrs = %{
        "location_id" => loc.id,
        "quantity" => 30
      }

      assert {:ok, inventory_item} =
               Products.create_inventory_item_by_sku(org.id, "TEST123", attrs)

      assert inventory_item.quantity == 30
      assert inventory_item.location_id == loc.id
      assert inventory_item.product_variant_id == pv.id
      assert inventory_item.organization_id == org.id
    end

    test "returns error when SKU doesn't exist" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        "location_id" => loc.id,
        "quantity" => 30
      }

      assert_raise Ecto.NoResultsError, fn ->
        Products.create_inventory_item_by_sku(org.id, "NONEXISTENT", attrs)
      end
    end
  end

  describe "create_inventory_items_by_sku/2" do
    test "creates multiple inventory items successfully" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: product, organization: org, sku: "SKU1")
      pv2 = insert(:product_variant, product: product, organization: org, sku: "SKU2")

      items = %{
        "SKU1" => %{"location_id" => loc.id, "quantity" => 10},
        "SKU2" => %{"location_id" => loc.id, "quantity" => 20}
      }

      assert {2, [item1, item2]} = Products.create_inventory_items_by_sku(org.id, items)

      assert item1.product_variant_id == pv1.id
      assert item1.quantity == 10

      assert item2.product_variant_id == pv2.id
      assert item2.quantity == 20
    end

    test "partially succeed create inventory items by sku" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)

      insert(:product_variant, product: product, organization: org, sku: "SKU1")

      items = %{
        "SKU1" => %{"location_id" => loc.id, "quantity" => 10},
        "SKU2" => %{"location_id" => loc.id, "quantity" => 20}
      }

      assert {1, _} = Products.create_inventory_items_by_sku(org.id, items)

      # Verify no inventory items were created
      assert Repo.aggregate(InventoryItem, :count) == 1
    end
  end

  describe "update_inventory_items_by_sku/2" do
    test "updates multiple inventory items successfully" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: product, organization: org, sku: "SKU1")
      pv2 = insert(:product_variant, product: product, organization: org, sku: "SKU2")

      insert(:inventory_item,
        organization: org,
        location: loc,
        product_variant: pv1,
        quantity: 10
      )

      insert(:inventory_item,
        organization: org,
        location: loc,
        product_variant: pv2,
        quantity: 20
      )

      items = %{
        "SKU1" => %{"quantity" => 15},
        "SKU2" => %{"quantity" => 25}
      }

      assert {:ok, results} = Products.update_inventory_items_by_sku(org.id, items)

      assert updated_item1 = results[{:inventory, "SKU1"}]
      assert updated_item2 = results[{:inventory, "SKU2"}]

      assert updated_item1.quantity == 15
      assert updated_item2.quantity == 25
    end

    test "rolls back transaction if any update fails" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: product, organization: org, sku: "SKU1")
      pv2 = insert(:product_variant, product: product, organization: org, sku: "SKU2")

      ii1 =
        insert(:inventory_item,
          organization: org,
          location: loc,
          product_variant: pv1,
          quantity: 10
        )

      _ii2 =
        insert(:inventory_item,
          organization: org,
          location: loc,
          product_variant: pv2,
          quantity: 20
        )

      items = %{
        "SKU1" => %{"quantity" => 15},
        # This should fail validation
        "SKU2" => %{"quantity" => nil}
      }

      assert {:error, {:inventory, "SKU2"}, _changeset, _changes_so_far} =
               Products.update_inventory_items_by_sku(org.id, items)

      # Verify no updates were made
      reloaded_ii1 = Repo.reload!(ii1)
      assert reloaded_ii1.quantity == 10
    end
  end

  describe "list_addons/1" do
    test "returns all addons for a given organization" do
      org = insert(:organization)
      another_org = insert(:organization)

      insert(:addon, organization: org)
      insert(:addon, organization: org)
      insert(:addon, organization: org)

      insert(:addon, organization: another_org)
      insert(:addon, organization: another_org)
      insert(:addon, organization: another_org)

      addons = Products.list_addons(org.id)

      assert length(addons) == 3
      assert Enum.all?(addons, fn addon -> addon.organization_id == org.id end)
    end
  end

  describe "paginated_addons/2" do
    test "paging works correctly" do
      organization = insert(:organization)
      organization_id = organization.id
      insert_list(20, :addon, organization: organization)

      opts = [limit: 10]
      results = Products.paginated_addons(organization_id, opts)
      assert length(results.entries) == 10

      opts = [limit: 5, after: results.metadata.after]
      next_page_results = Products.paginated_addons(organization_id, opts)
      assert length(next_page_results.entries) == 5

      result_entries_id = Enum.map(results.entries, & &1.id)
      next_page_results_entries_id = Enum.map(next_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(result_entries_id),
               MapSet.new(next_page_results_entries_id)
             )

      opts = [limit: 5, before: next_page_results.metadata.before]
      previous_page_results = Products.paginated_addons(organization_id, opts)
      assert length(previous_page_results.entries) == 5

      previous_page_results_entries_id = Enum.map(previous_page_results.entries, & &1.id)

      assert MapSet.disjoint?(
               MapSet.new(previous_page_results_entries_id),
               MapSet.new(next_page_results_entries_id)
             )
    end

    test "search works correctly" do
      organization = insert(:organization)
      insert_list(20, :addon, organization: organization, type: "medical appointment")
      insert_list(20, :addon, organization: organization, type: "subscription")

      search_opts = [type: "subscription", limit: 5]
      search_results = Products.paginated_addons(organization.id, search_opts)
      assert length(search_results.entries) == 5

      assert Enum.all?(search_results.entries, &(&1.type == "subscription"))
    end
  end

  describe "get_addon!/2" do
    test "retrieves a addon by ID and organization ID" do
      org = insert(:organization)
      addon = insert(:addon, organization: org)

      retrieved_addon = Products.get_addon!(addon.organization_id, addon.id)
      assert retrieved_addon.id == addon.id
      assert retrieved_addon.organization_id == addon.organization_id
    end

    test "raises if the addon does not exist" do
      assert_raise Ecto.NoResultsError, fn ->
        Products.get_addon!(1, -1)
      end
    end

    test "raises if the product does not belong to the organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      addon = insert(:addon, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        Products.get_product!(another_org.id, addon.id)
      end
    end
  end

  describe "delete_addon/1" do
    test "deletes a addon" do
      addon = insert(:addon)
      assert {:ok, _} = Products.delete_addon(addon)

      refute Rms.Repo.get(Rms.Commerce.Products.Addon, addon.id)
    end
  end

  describe "create_addon/2" do
    test "create a new addon" do
      org = insert(:organization)

      attrs = %{
        name: "Addon 1",
        list_price: 540.045,
        price: 540.045,
        type: "subscription",
        description: "subscription test",
        image_url: "subscription",
        metadata: %{
          "name" => "vtex.subscription.prime",
          "content" => %{
            "vtex.subscription.key.frequency" => "6 month"
          }
        }
      }

      assert {:ok, addon} = Products.create_addon(org.id, attrs)

      assert addon.name == attrs.name
      assert addon.list_price == Decimal.new("#{attrs.list_price}")
      assert addon.type == attrs.type
      assert addon.description == attrs.description
      assert addon.image_url == attrs.image_url
      assert addon.metadata == attrs.metadata
    end
  end

  describe "create_or_update_gift_handler_configuration/2" do
    test "inserts a new gift handler configuration when it doesn't exist" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      attrs = %{
        product_variant_id: pv.id,
        gifts: %{
          "1" => %{
            product_variant_id: gift_pv.id,
            quantity: 5,
            cost: 100
          }
        }
      }

      {:ok, config} =
        Products.create_gift_handler_configuration(org.id, attrs)

      assert config.organization_id == org.id
      assert config.product_variant_id == pv.id
      [gift] = config.gifts
      assert gift.quantity == 5
      assert gift.cost == Decimal.new("100")
      assert gift.product_variant_id == gift_pv.id
    end

    test "updates an existing gift handler configuration if it exists" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      pv_2 = insert(:product_variant, organization: org, product: product)
      gift_pv_2 = insert(:product_variant, organization: org, product: product)

      attrs = %{
        product_variant_id: pv.id,
        gifts: %{
          "1" => %{
            product_variant_id: gift_pv.id,
            quantity: 5,
            cost: 100
          }
        }
      }

      {:ok, config} =
        Products.create_gift_handler_configuration(org.id, attrs)

      assert config.organization_id == org.id
      assert config.product_variant_id == pv.id
      [gift] = config.gifts
      assert gift.quantity == 5
      assert gift.cost == Decimal.new("100")
      assert gift.product_variant_id == gift_pv.id

      updated_attrs = %{
        product_variant_id: pv_2.id,
        gifts: %{
          "1" => %{
            product_variant_id: gift_pv_2.id,
            quantity: 10,
            cost: 200
          }
        }
      }

      {:ok, updated_config} =
        Products.create_gift_handler_configuration(org.id, updated_attrs)

      assert updated_config.organization_id == org.id
      assert updated_config.product_variant_id == pv_2.id
      [gift] = updated_config.gifts
      assert gift.quantity == 10
      assert gift.cost == Decimal.new("200")
      assert gift.product_variant_id == gift_pv_2.id
    end
  end

  describe "import_gift_handler_configurations/2" do
    test "create a gift_handler_configurations for each bundle" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      pv_2 = insert(:product_variant, organization: org, product: product)
      gift_pv_2 = insert(:product_variant, organization: org, product: product)

      attrs = [
        %{
          "Bundle" => [
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => gift_pv.sku
            }
          ],
          "Sku" => pv.sku
        },
        %{
          "Bundle" => [
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => gift_pv_2.sku
            }
          ],
          "Sku" => pv_2.sku
        }
      ]

      assert {:ok, _} = Products.import_gift_handler_configurations(org.id, attrs)
    end

    test "does not create a gift_handler_configurations when a sku does not exist" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      pv_2 = insert(:product_variant, organization: org, product: product)

      attrs = [
        %{
          "Bundle" => [
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => gift_pv.sku
            }
          ],
          "Sku" => pv.sku
        },
        %{
          "Bundle" => [
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => "gift_pv_2.sku"
            }
          ],
          "Sku" => pv_2.sku
        }
      ]

      assert {
               :error,
               _,
               "Sku gift_pv_2.sku Not Found",
               _
             } = Products.import_gift_handler_configurations(org.id, attrs)
    end

    test "does not create a gift_handler_configurations when product is archived" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      gift_pv_2 =
        insert(:product_variant,
          organization: org,
          archived_at: DateTime.utc_now(),
          product: product
        )

      attrs = [
        %{
          "Bundle" => [
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => gift_pv.sku
            },
            %{
              "Cost" => 0.1,
              "Discount" => 0.1,
              "Quantity" => 1,
              "Sku" => gift_pv_2.sku
            }
          ],
          "Sku" => pv.sku
        }
      ]

      assert {
               :error,
               _,
               _,
               _
             } = Products.import_gift_handler_configurations(org.id, attrs)
    end
  end
end
