defmodule Rms.Commerce.ReverseFulfillmentTest do
  use Rms.DataCase, async: true

  import Rms.Factory

  alias Rms.Commerce.{Orders, Fulfillments}
  alias Rms.Commerce.Fulfillments.ReverseFulfillment

  describe "reverse_fulfillment/2" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: product
        )

      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)

      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      order =
        Rms.Repo.preload(order, fulfillments: [:line_items])

      fulfillment = List.first(order.fulfillments)

      line_item =
        Enum.find(fulfillment.line_items, &(&1.product_variant_id == product_variant.id))

      {:ok,
       fulfillment: fulfillment,
       line_item: line_item,
       location: location,
       organization: organization,
       customer: customer}
    end

    test "creates reverse fulfillment successfully", %{
      line_item: line_item,
      organization: organization,
      location: location,
      customer: customer
    } do
      staff = insert(:staff, organization: organization)

      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 2,
          "reason" => "defective",
          "return_to_inventory" => true
        }
      ]

      assert {:ok, reverse_fulfillment} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert %ReverseFulfillment{} = reverse_fulfillment
      assert reverse_fulfillment.organization_id == organization.id
      assert reverse_fulfillment.status == "pending"
      assert [line_item_return] = reverse_fulfillment.line_items
      assert line_item_return.line_item_id == line_item.id
      assert line_item_return.returned_quantity == 2
      assert line_item_return.organization_id == organization.id
      assert line_item_return.reason == "defective"
      assert line_item_return.return_to_inventory == true
    end

    test "fails when trying to return more items than available", %{
      line_item: line_item,
      organization: organization,
      location: location,
      customer: customer
    } do
      staff = insert(:staff, organization: organization)

      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 6,
          "reason" => "defective"
        }
      ]

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert [line_item_changeset] = changeset.changes.line_items

      assert "must be less than or equal to available quantity" in errors_on(line_item_changeset).returned_quantity
    end

    test "fails when trying to return zero items", %{
      line_item: line_item,
      organization: organization,
      location: location,
      customer: customer
    } do
      staff = insert(:staff, organization: organization)

      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 0,
          "reason" => "defective"
        }
      ]

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert [line_item_changeset] = changeset.changes.line_items
      assert "must be greater than 0" in errors_on(line_item_changeset).returned_quantity
    end

    test "fails when trying to return negative items", %{
      line_item: line_item,
      organization: organization,
      location: location,
      customer: customer
    } do
      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => -1,
          "reason" => "defective"
        }
      ]

      staff = insert(:staff, organization: organization)

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert [line_item_changeset] = changeset.changes.line_items
      assert "must be greater than 0" in errors_on(line_item_changeset).returned_quantity
    end

    test "fails when line items not found", %{
      organization: organization,
      location: location,
      customer: customer
    } do
      staff = insert(:staff, organization: organization)

      returned_line_items = [
        %{
          "line_item_id" => 0,
          "returned_quantity" => 1,
          "reason" => "defective"
        }
      ]

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert "does not exist" in hd(errors_on(changeset).line_items).line_item
    end

    test "creates reverse fulfillment with default return_to_inventory value", %{
      line_item: line_item,
      organization: organization,
      location: location,
      customer: customer
    } do
      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 2,
          "reason" => "defective"
          # Not specifying return_to_inventory should use default false
        }
      ]

      staff = insert(:staff, organization: organization)

      assert {:ok, reverse_fulfillment} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 returned_line_items,
                 location.id,
                 staff.id,
                 customer.id
               )

      assert %ReverseFulfillment{} = reverse_fulfillment
      assert [line_item_return] = reverse_fulfillment.line_items
      assert line_item_return.return_to_inventory == false
    end

    test "fails when no line items provided", %{
      organization: organization,
      location: location,
      customer: customer
    } do
      staff = insert(:staff, organization: organization)

      assert {:error, changeset} =
               Fulfillments.create_reverse_fulfillment(
                 organization.id,
                 [],
                 location.id,
                 staff.id,
                 customer.id
               )

      assert "can't be blank" in errors_on(changeset).line_items
    end
  end

  describe "get_order_from_reverse_fulfillment/1" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: product
        )

      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)

      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      # Create transaction first
      transaction = insert(:transaction, order: order, organization: organization)

      # Then create payment with the transaction
      payment =
        insert(:payment, %{
          transaction: transaction,
          organization: organization,
          method: "iglu_credit"
        })

      # Create reverse fulfillment with a line item
      order = Rms.Repo.preload(order, fulfillments: [line_items: [:product_variant]])
      fulfillment = List.first(order.fulfillments)
      line_item = List.first(fulfillment.line_items)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            %{
              organization_id: organization.id,
              line_item_id: line_item.id,
              returned_quantity: 1,
              reason: "defective"
            }
          ]
        )

      # Then create iglu_credit with the reverse_fulfillment
      iglu_credit =
        insert(:iglu_credit, %{
          organization: organization,
          reverse_fulfillment_id: reverse_fulfillment.id,
          reason: "return",
          amount: Decimal.new("100.00")
        })

      # Finally create iglu_credit_payment linking everything
      _iglu_credit_payment =
        insert(:iglu_credit_payment, %{
          payment: payment,
          iglu_credit: iglu_credit,
          organization: organization
        })

      {:ok, order: order, reverse_fulfillment: reverse_fulfillment, organization: organization}
    end

    test "returns the original order associated with a reverse fulfillment", %{
      order: order,
      reverse_fulfillment: reverse_fulfillment
    } do
      [retrieved_order] =
        Rms.Commerce.Orders.OrderData.get_order_from_reverse_fulfillment(reverse_fulfillment.id)

      assert retrieved_order.id == order.id
    end
  end

  describe "get_original_order_from_reverse_fulfillment/1" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: product
        )

      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)

      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      # Create transaction first
      transaction = insert(:transaction, order: order, organization: organization)

      # Then create payment with the transaction
      payment =
        insert(:payment, %{
          transaction: transaction,
          organization: organization,
          method: "iglu_credit"
        })

      # Create reverse fulfillment with a line item
      order = Rms.Repo.preload(order, fulfillments: [line_items: [:product_variant]])
      fulfillment = List.first(order.fulfillments)
      line_item = List.first(fulfillment.line_items)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            %{
              organization_id: organization.id,
              line_item_id: line_item.id,
              returned_quantity: 1,
              reason: "defective"
            }
          ]
        )

      # Then create iglu_credit with the reverse_fulfillment
      iglu_credit =
        insert(:iglu_credit, %{
          organization: organization,
          reverse_fulfillment_id: reverse_fulfillment.id,
          reason: "return",
          amount: Decimal.new("100.00")
        })

      # Finally create iglu_credit_payment linking everything
      _iglu_credit_payment =
        insert(:iglu_credit_payment, %{
          payment: payment,
          iglu_credit: iglu_credit,
          organization: organization
        })

      {:ok, order: order, reverse_fulfillment: reverse_fulfillment, organization: organization}
    end

    test "returns the orders associated with the given reverse fulfillment ID", %{
      order: order,
      reverse_fulfillment: reverse_fulfillment
    } do
      # Query for the orders using the reverse fulfillment ID
      [found_order] =
        Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
          reverse_fulfillment.id
        )

      # Assert we got back the correct order
      assert found_order.id == order.id
    end

    test "returns empty list when reverse fulfillment ID doesn't exist" do
      result =
        Rms.Commerce.Orders.OrderData.get_original_order_from_reverse_fulfillment(
          Ecto.UUID.generate()
        )

      assert result == []
    end
  end
end
