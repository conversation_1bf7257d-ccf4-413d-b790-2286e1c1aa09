defmodule Rms.Commerce.Workflows.ConfirmOrderCancelWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Tesla.Mock

  import Mox

  alias Rms.Commerce.Orders
  alias Rms.Commerce.Workflows.ConfirmOrderCancelWorker

  setup :verify_on_exit!

  describe "process/1" do
    setup do
      organization = insert(:organization)
      order = insert(:order, organization: organization, status: "canceling")

      {:ok, organization: organization, order: order}
    end

    test "updates order status to canceled", %{organization: organization, order: order} do
      args = %{"order_id" => order.id, "organization_id" => organization.id}
      assert {:ok, updated_order} = perform_job(ConfirmOrderCancelWorker, args)

      assert updated_order.status == "canceled"
    end

    test "returns error when order is not found" do
      args = %{"order_id" => 1, "organization_id" => 1}

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(ConfirmOrderCancelWorker, args)
      end
    end

    test "updates order status to canceled if cancelation endpoint return success", %{
      organization: organization,
      order: order
    } do
      insert(:cancelation_endpoint, organization: organization, active: true)

      mock(fn _env -> json(%{message: "order canceled with success"}) end)

      args = %{"order_id" => order.id, "organization_id" => organization.id}
      assert {:ok, updated_order} = perform_job(ConfirmOrderCancelWorker, args)

      assert updated_order.status == "canceled"
    end

    test "returns error when cancelation endpoint returns error", %{
      organization: organization,
      order: order
    } do
      insert(:cancelation_endpoint, organization: organization, active: true)

      mock(fn _env -> json(%{message: "order cancelation failed"}, status: 400) end)

      args = %{"order_id" => order.id, "organization_id" => organization.id}
      assert {:error, reason} = perform_job(ConfirmOrderCancelWorker, args)
      assert reason =~ "order cancelation failed"

      updated_order = Orders.get_order!(organization.id, order.id)
      assert updated_order.status == "canceling"
    end

    test "confirms cancelation if order transaction is not done, even with cancelation endpoint",
         %{
           organization: organization,
           order: order
         } do
      insert(:cancelation_endpoint, organization: organization, active: true)
      insert(:transaction, order: order, organization: organization, status: "open")

      args = %{"order_id" => order.id, "organization_id" => organization.id}
      assert {:ok, updated_order} = perform_job(ConfirmOrderCancelWorker, args)

      assert updated_order.status == "canceled"
    end
  end

  describe "process/1 with vinco url" do
    setup do
      Mox.stub_with(VincoClientMock, VincoClientStub)

      organization = insert(:organization)
      order = insert(:order, organization: organization, status: "canceling")

      {:ok, organization: organization, order: order}
    end

    test "updates order status to canceled if cancelation endpoint return success" do
      expect(VincoClientMock, :cancel_fiscal_invoice, 1, fn _client, _, _ ->
        {:ok,
         %{
           "IdEvento" => 115,
           "CodStatus" => 1,
           "Motivo" => "Evento registrado e vinculado a NF-e",
           "ChaveEvento" => 1111,
           "ProtocoloEvento" => 12_344,
           "XmlEvento" => "_xml"
         }}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, status: "done", organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        status: "authorized",
        serie_id: serie.id,
        fulfillment_id: fulfillment.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, _fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:cancelation_endpoint,
        organization: org,
        endpoint: "https://vinco.com",
        active: true
      )

      args = %{"order_id" => order.id, "organization_id" => org.id}
      assert {:ok, updated_order} = perform_job(ConfirmOrderCancelWorker, args)

      assert updated_order.status == "canceled"
    end

    test "returns error when cancelation endpoint returns error" do
      Mox.expect(VincoClientMock, :cancel_fiscal_invoice, 1, fn _client, _, _ ->
        {:ok,
         %{
           IdEvento: 0,
           CodStatus: -215,
           Motivo: "Comprimento da chave diferente de 44. [43]."
         }}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org, status: "canceling")
      transaction = insert(:transaction, order: order, status: "done", organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        status: "authorized",
        fulfillment_id: fulfillment.id,
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, _fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:cancelation_endpoint,
        organization: org,
        endpoint: "https://vinco.com",
        active: true
      )

      args = %{"order_id" => order.id, "organization_id" => org.id}
      assert {:error, _reason} = perform_job(ConfirmOrderCancelWorker, args)

      updated_order = Orders.get_order!(org.id, order.id)
      assert updated_order.status == "canceling"
    end

    test "confirms cancelation if order transaction is not done, even with cancelation endpoint",
         %{
           organization: organization,
           order: order
         } do
      insert(:cancelation_endpoint, organization: organization, active: true)
      insert(:transaction, order: order, organization: organization, status: "open")

      args = %{"order_id" => order.id, "organization_id" => organization.id}
      assert {:ok, updated_order} = perform_job(ConfirmOrderCancelWorker, args)

      assert updated_order.status == "canceled"
    end
  end
end
