defmodule Rms.Commerce.Workflows.OrderCancellationWorkflowTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Commerce.Orders
  alias Rms.Commerce.Workflows.OrderCancellationWorkflow
  alias Rms.Commerce.Workflows.CancelFulfillmentWorker
  alias Rms.Commerce.Workflows.ConfirmOrderCancelWorker

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)

    order = insert(:order, organization: organization, status: "paid")

    fulfillments =
      insert_pair(:fulfillment,
        order: order,
        organization: organization,
        ecommerce: "vtex",
        external_reference: fn -> Ecto.UUID.autogenerate() end
      )

    insert(:vtex_credential, organization: organization)

    {:ok, organization: organization, order: order, fulfillments: fulfillments}
  end

  describe "build_workflow/2" do
    test "creates a workflow with correct steps", %{order: order, fulfillments: fulfillments} do
      reason = "Test cancellation reason"

      order
      |> OrderCancellationWorkflow.build_workflow(reason: reason)
      |> Oban.insert_all()

      assert_enqueued(
        worker: OrderCancellationWorkflow,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(0) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(1) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: ConfirmOrderCancelWorker,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )
    end

    test "creates a workflow with correct steps and igluPOS integration", %{
      order: order,
      fulfillments: fulfillments
    } do
      reason = "Test cancellation reason"

      insert(:organization_setting,
        key: "iglu_pos_cancel",
        value: %{data: true},
        organization: order.organization
      )

      order
      |> OrderCancellationWorkflow.build_workflow(reason: reason)
      |> Oban.insert_all()

      assert_enqueued(
        worker: OrderCancellationWorkflow,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(0) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(1) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )

      assert_enqueued(
        worker: ConfirmOrderCancelWorker,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => reason
        }
      )
    end

    test "creates a workflow with default reason when not provided", %{
      order: order,
      fulfillments: fulfillments
    } do
      order
      |> OrderCancellationWorkflow.build_workflow()
      |> Oban.insert_all()

      assert_enqueued(
        worker: OrderCancellationWorkflow,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => "canceled by iglu"
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(0) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => "canceled by iglu"
        }
      )

      assert_enqueued(
        worker: CancelFulfillmentWorker,
        args: %{
          "fulfillment_id" => fulfillments |> Enum.at(1) |> Map.get(:id),
          "organization_id" => order.organization_id,
          "reason" => "canceled by iglu"
        }
      )

      assert_enqueued(
        worker: ConfirmOrderCancelWorker,
        args: %{
          "order_id" => order.id,
          "organization_id" => order.organization_id,
          "reason" => "canceled by iglu"
        }
      )
    end
  end

  describe "process/1" do
    test "updates order status to canceling", %{order: order} do
      args = %{"order_id" => order.id, "organization_id" => order.organization_id}
      assert :ok = perform_job(OrderCancellationWorkflow, args)

      updated_order = Orders.get_order!(order.organization_id, order.id)
      assert updated_order.status == "canceling"
    end
  end

  describe "full workflow" do
    test "cancels order and all fulfillments", %{order: order, fulfillments: fulfillments} do
      expect(Rms.Integrations.VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(Rms.Integrations.VTEX.Mock, :cancel_ecommerce_order, 2, fn :mock_client, _, _ ->
        {:ok, %{}}
      end)

      order
      |> OrderCancellationWorkflow.build_workflow()
      |> run_workflow()

      updated_order = Rms.Repo.reload!(order)
      assert updated_order.status == "canceled"

      for fulfillment <- Rms.Repo.reload!(fulfillments) do
        assert fulfillment.status == "canceled"
      end
    end
  end
end
