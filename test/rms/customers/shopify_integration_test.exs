defmodule Rms.Customers.ShopifyIntegrationTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Customers

  describe "maybe_update_customer_and_addresses/4" do
    test "updates customer when incoming timestamp is newer" do
      customer = insert(:customer, updated_at: ~N[2025-01-01 00:00:00])

      attrs = %{
        name: "Updated Name",
        updated_at: "2025-01-02T00:00:00Z"
      }

      assert {:ok, updated_customer} =
               Customers.maybe_update_customer_and_addresses(customer, attrs, [[], []], [])

      assert updated_customer.name == "Updated Name"
    end

    test "does not update customer when incoming timestamp is older" do
      customer = insert(:customer, updated_at: ~N[2025-01-02 00:00:00], name: "Original Name")

      attrs = %{
        name: "Updated Name",
        updated_at: "2025-01-01T00:00:00Z"
      }

      assert {:ok, unchanged_customer} =
               Customers.maybe_update_customer_and_addresses(customer, attrs, [[], []], [])

      assert unchanged_customer.name == "Original Name"
    end

    test "always updates customer when no timestamp is provided" do
      customer = insert(:customer, updated_at: ~N[2025-01-01 00:00:00], name: "Original Name")

      attrs = %{
        name: "Updated Name"
      }

      assert {:ok, updated_customer} =
               Customers.maybe_update_customer_and_addresses(customer, attrs, [[], []], [])

      assert updated_customer.name == "Updated Name"
    end

    test "deletes addresses that don't match any in the incoming data" do
      customer = insert(:customer)
      address_to_delete = insert(:address, customer: customer, city_name: "Old City")

      attrs = %{name: "Updated Name"}
      addresses_to_delete = [address_to_delete]
      addresses_to_create = []

      assert {:ok, updated_customer} =
               Customers.maybe_update_customer_and_addresses(
                 customer,
                 attrs,
                 [addresses_to_delete, addresses_to_create],
                 []
               )

      assert updated_customer.addresses == []
    end

    test "creates new addresses that don't exist in the customer record" do
      customer = insert(:customer)

      attrs = %{name: "Updated Name"}

      new_address_attrs = %{
        receiver_name: "Test Receiver",
        city_name: "New City",
        street: "New Street",
        number: "123",
        complement: "Apt 4",
        zip: "12345678",
        state: "SP",
        country_name: "Test Country"
      }

      assert {:ok, updated_customer} =
               Customers.maybe_update_customer_and_addresses(
                 customer,
                 attrs,
                 [[], [new_address_attrs]],
                 []
               )

      assert length(updated_customer.addresses) == 1
      [created_address] = updated_customer.addresses
      assert created_address.city_name == "New City"
      assert created_address.street == "New Street"
    end

    test "performs both deletions and creations in a single transaction" do
      customer = insert(:customer)
      address_to_delete = insert(:address, customer: customer, city_name: "Old City")

      attrs = %{name: "Updated Name"}

      new_address_attrs = %{
        receiver_name: "Test Receiver",
        city_name: "New City",
        street: "New Street",
        number: "123",
        complement: "Apt 4",
        zip: "12345678",
        state: "SP",
        country_name: "Test Country"
      }

      assert {:ok, updated_customer} =
               Customers.maybe_update_customer_and_addresses(
                 customer,
                 attrs,
                 [[address_to_delete], [new_address_attrs]],
                 []
               )

      assert length(updated_customer.addresses) == 1
      [created_address] = updated_customer.addresses
      assert created_address.city_name == "New City"
    end
  end
end
