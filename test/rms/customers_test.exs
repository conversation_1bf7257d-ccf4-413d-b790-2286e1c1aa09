defmodule Rms.CustomersTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Customers
  alias Rms.Customers.Customer

  describe "customers" do
    @invalid_attrs %{
      name: nil,
      document_type: nil,
      document: nil,
      document_hash: nil,
      email: nil,
      email_hash: nil,
      primary_phone_number: nil,
      primary_phone_number_hash: nil
    }

    test "list_customers/0 returns all customers" do
      customer = insert(:customer)
      assert [%{id: customer_id}] = Customers.list_customers(customer.organization_id)
      assert customer_id == customer.id
    end

    test "list_customer/2 can receive a search keyword argument to search by name, email, document and primary_phone_number" do
      org = insert(:organization)

      insert(:customer,
        name: "<PERSON>",
        email: "<EMAIL>",
        document: "1234",
        primary_phone_number: "555-1234",
        email_hash: "<EMAIL>",
        document_hash: "1234",
        primary_phone_number_hash: "555-1234",
        organization: org
      )

      insert(:customer,
        name: "<PERSON>",
        email: "<EMAIL>",
        document: "5678",
        primary_phone_number: "555-5678",
        email_hash: "<EMAIL>",
        document_hash: "5678",
        primary_phone_number_hash: "555-5678",
        organization: org
      )

      assert [%{name: "John Doe"}] = Customers.list_customers(org.id, name: "John Doe")

      assert [%{email: "<EMAIL>"}] =
               Customers.list_customers(org.id, email: "<EMAIL>")

      assert [%{document: "1234"}] =
               Customers.list_customers(org.id, document: "1234", document_type: "cpf")

      assert [%{primary_phone_number: "555-5678"}] =
               Customers.list_customers(org.id, primary_phone_number: "555-5678")
    end

    test "list_customers/2 search by name is case insensitive" do
      org = insert(:organization)

      insert(:customer,
        name: "Case Insensitive Name",
        email: "<EMAIL>",
        document: "4321",
        primary_phone_number: "555-4321",
        organization: org
      )

      assert [%{name: "Case Insensitive Name"}] =
               Customers.list_customers(org.id, search: "case insensitive name")
    end

    test "list_customers/2 allows partial search on name" do
      org = insert(:organization)

      insert(:customer,
        name: "Partial Search Test",
        email: "<EMAIL>",
        document: "1122",
        primary_phone_number: "555-1122",
        organization: org
      )

      insert(:customer,
        name: "Another Test",
        email: "<EMAIL>",
        document: "3344",
        primary_phone_number: "555-3344",
        organization: org
      )

      assert [%{name: "Partial Search Test"}] =
               Customers.list_customers(org.id, name: "Partial")
    end

    test "get_customer!/1 returns the customer with given id" do
      customer = insert(:customer)
      assert Customers.get_customer!(customer.organization_id, customer.id).id == customer.id
    end

    test "create_customer/2 with valid data creates a customer" do
      org = insert(:organization)

      valid_attrs = %{
        name: "some name",
        document_type: "SOME DOCUMENT_TYPE",
        document: "some document",
        document_hash: "some document_hash",
        email: "some email",
        email_hash: "some email_hash",
        primary_phone_number: "some primary_phone_number",
        primary_phone_number_hash: "some primary_phone_number_hash",
        birthdate: "2024-11-08"
      }

      assert {:ok, %Customer{} = customer} = Customers.create_customer(org.id, valid_attrs)
      assert customer.name == "some name"
      assert customer.document_type == "SOME DOCUMENT_TYPE"
      assert customer.document == "some document"
      assert customer.document_hash == "some document"
      assert customer.email == "some email"
      assert customer.email_hash == "some email"
      assert customer.primary_phone_number == "some primary_phone_number"
      assert customer.primary_phone_number_hash == "some primary_phone_number"
      assert customer.birthdate == ~D[2024-11-08]
      assert customer.email_plain == "some email"
      assert customer.primary_phone_number_plain == "some primary_phone_number"
      assert customer.document_plain == "some document"
    end

    test "create_customer/2 allows duplicate customers with the same attributes" do
      org = insert(:organization)

      valid_attrs = %{
        name: "duplicate name",
        document_type: "duplicate document_type",
        document: "duplicate document",
        document_hash: "duplicate document_hash",
        email: "duplicate email",
        email_hash: "duplicate email_hash",
        name_hash: "duplicate name_hash",
        primary_phone_number: "duplicate primary_phone_number",
        primary_phone_number_hash: "duplicate primary_phone_number_hash"
      }

      {:ok, %Customer{} = customer1} = Customers.create_customer(org.id, valid_attrs)
      {:ok, %Customer{} = customer2} = Customers.create_customer(org.id, valid_attrs)

      refute customer1.id == customer2.id
    end

    test "create_customer/2 with invalid data returns error changeset" do
      org = insert(:organization)
      assert {:error, %Ecto.Changeset{}} = Customers.create_customer(org.id, @invalid_attrs)
    end

    test "create_customer/2 emits customer.created event" do
      org = insert(:organization)

      valid_attrs = string_params_for(:customer)

      assert {:ok, %Customer{} = customer} = Customers.create_customer(org.id, valid_attrs)
      assert_emit("customer.created", Customer.event_payload(customer))
    end

    test "update_customer/2 with valid data updates the customer" do
      customer = insert(:customer)

      update_attrs = %{
        name: "some updated name",
        document_type: "SOME UPDATED DOCUMENT_TYPE",
        document: "some updated document",
        document_hash: "some updated document_hash",
        email: "some updated email",
        email_hash: "some updated email_hash",
        primary_phone_number: "some updated primary_phone_number",
        primary_phone_number_hash: "some updated primary_phone_number_hash"
      }

      assert {:ok, %Customer{} = customer} = Customers.update_customer(customer, update_attrs)
      assert customer.name == "some updated name"
      assert customer.document_type == "SOME UPDATED DOCUMENT_TYPE"
      assert customer.document == "some updated document"
      assert customer.document_hash == "some updated document"
      assert customer.email == "some updated email"
      assert customer.email_hash == "some updated email"
      assert customer.primary_phone_number == "some updated primary_phone_number"
      assert customer.primary_phone_number_hash == "some updated primary_phone_number"
    end

    test "update_customer/2 with invalid data returns error changeset" do
      customer = insert(:customer)
      assert {:error, %Ecto.Changeset{}} = Customers.update_customer(customer, @invalid_attrs)
      updated_customer = Customers.get_customer!(customer.organization_id, customer.id)

      assert updated_customer.name == customer.name
      assert updated_customer.document_type == customer.document_type
      assert updated_customer.document == customer.document
      assert updated_customer.email == customer.email
      assert updated_customer.primary_phone_number == customer.primary_phone_number
    end

    test "delete_customer/1 deletes the customer" do
      customer = insert(:customer)
      assert {:ok, %Customer{}} = Customers.delete_customer(customer)

      assert_raise Ecto.NoResultsError, fn ->
        Customers.get_customer!(customer.organization_id, customer.id)
      end
    end

    test "change_customer/1 returns a customer changeset" do
      customer = insert(:customer)
      assert %Ecto.Changeset{} = Customers.change_customer(customer)
    end

    test "paginated_customers/2 ensures customers appear only once, regardless of addresses" do
      org = insert(:organization)

      customer = insert(:customer, organization: org)
      insert(:address, customer: customer)
      insert(:address, customer: customer)
      insert(:address, customer: customer)

      result = Customers.paginated_customers(org.id, limit: 10)

      assert length(result.entries) == 1
      assert hd(result.entries).id == customer.id
    end

    test "paginated_customers/2 returns paginated customers" do
      org = insert(:organization)

      insert_list(25, :customer,
        organization: org,
        inserted_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC")
      )

      result = Customers.paginated_customers(org.id, limit: 10, count: true)
      assert length(result.entries) == 10
      assert result.metadata.after != nil
      assert result.metadata.before == nil
      assert result.metadata.limit == 10
      assert result.metadata.total_count == 25

      result =
        Customers.paginated_customers(org.id,
          limit: 10,
          after: result.metadata.after,
          count: true
        )

      assert length(result.entries) == 10
      assert result.metadata.after != nil
      assert result.metadata.before != nil
      assert result.metadata.limit == 10
      assert result.metadata.total_count == 25

      result =
        Customers.paginated_customers(org.id,
          limit: 10,
          after: result.metadata.after,
          count: true
        )

      assert length(result.entries) == 5
      assert result.metadata.after == nil
      assert result.metadata.before != nil
      assert result.metadata.limit == 10
      assert result.metadata.total_count == 25
    end

    test "paginated_customers/2 filters customers by document" do
      org = insert(:organization)

      insert(:customer, document: "12345678901", document_type: "CPF", organization: org)
      insert(:customer, document: "98765432109", document_type: "CPF", organization: org)

      result =
        Customers.paginated_customers(org.id, document: "12345678901", document_type: "CPF")

      assert length(result.entries) == 1
      assert hd(result.entries).document == "12345678901"
    end

    test "paginated_customers/2 filters customers by email" do
      org = insert(:organization)

      insert(:customer, email: "<EMAIL>", organization: org)
      insert(:customer, email: "<EMAIL>", organization: org)

      result = Customers.paginated_customers(org.id, email: "<EMAIL>")
      assert length(result.entries) == 1
      assert hd(result.entries).email == "<EMAIL>"
    end

    test "paginated_customers/2 filters customers by primary_phone_number" do
      org = insert(:organization)

      insert(:customer, primary_phone_number: "5511999999999", organization: org)
      insert(:customer, primary_phone_number: "5511888888888", organization: org)

      result = Customers.paginated_customers(org.id, primary_phone_number: "5511999999999")
      assert length(result.entries) == 1
      assert hd(result.entries).primary_phone_number == "5511999999999"
    end

    test "paginated_customers/2 returns customers in descending order by updated_at and id" do
      org = insert(:organization)

      insert(:customer,
        organization: org,
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        id: 1
      )

      insert(:customer,
        organization: org,
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        id: 2
      )

      insert(:customer,
        organization: org,
        updated_at: DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC"),
        id: 3
      )

      result = Customers.paginated_customers(org.id, limit: 3)

      assert length(result.entries) == 3
      assert [first, second, third] = result.entries

      assert first.updated_at == DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC")
      assert first.id == 3

      assert second.updated_at == DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC")
      assert second.id == 2

      assert third.updated_at == DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC")
      assert third.id == 1
    end

    test "orders by updated_at in descending order when no search parameter is provided" do
      org = insert(:organization)

      # Insert customers with different updated_at timestamps
      insert(:customer,
        name: "John Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Jane Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Alice Smith",
        updated_at: DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC"),
        organization: org
      )

      # Fetch paginated results without search
      result = Customers.paginated_customers(org.id, limit: 3)

      # Verify the order
      assert length(result.entries) == 3
      assert [first, second, third] = result.entries

      assert first.updated_at == DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC")
      assert second.updated_at == DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC")
      assert third.updated_at == DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC")
    end

    test "orders by name in ascending order when search parameter is provided" do
      org = insert(:organization)

      insert(:customer,
        name: "John Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Jane Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Alice Smith",
        updated_at: DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC"),
        organization: org
      )

      result = Customers.paginated_customers(org.id, search: "Doe", limit: 3)

      assert length(result.entries) == 2
      assert [first, second] = result.entries

      assert first.name == "Jane Doe"
      assert second.name == "John Doe"
    end

    test "returns no results for non-matching search" do
      org = insert(:organization)

      insert(:customer,
        name: "John Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Jane Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        organization: org
      )

      result = Customers.paginated_customers(org.id, search: "Alice", limit: 3)

      assert Enum.empty?(result.entries)
    end

    test "returns customers matching partial name search in alphabetical order" do
      org = insert(:organization)

      insert(:customer,
        name: "John Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Jane Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Alice Smith",
        updated_at: DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC"),
        organization: org
      )

      result = Customers.paginated_customers(org.id, search: "Doe", limit: 3)

      assert length(result.entries) == 2
      assert [first, second] = result.entries

      assert first.name == "Jane Doe"
      assert second.name == "John Doe"
    end

    test "returns customers matching case-insensitive name search in alphabetical order" do
      org = insert(:organization)

      insert(:customer,
        name: "John Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Jane Doe",
        updated_at: DateTime.from_naive!(~N[2023-01-02T00:00:00], "Etc/UTC"),
        organization: org
      )

      insert(:customer,
        name: "Alice Smith",
        updated_at: DateTime.from_naive!(~N[2023-01-03T00:00:00], "Etc/UTC"),
        organization: org
      )

      result = Customers.paginated_customers(org.id, search: "jane", limit: 3)

      assert length(result.entries) == 1
      assert hd(result.entries).name == "Jane Doe"
    end

    test "returns customers matching partial name search" do
      org = insert(:organization)
      customer = insert(:customer, name: "Matheus Cunha", organization: org)

      result = Customers.paginated_customers(org.id, search: "Mat")

      assert length(result.entries) == 1
      assert hd(result.entries).id == customer.id
    end

    test "returns customers matching exact name search" do
      org = insert(:organization)
      customer = insert(:customer, name: "Matheus Cunha", organization: org)

      result = Customers.paginated_customers(org.id, search: "Matheus Cunha")

      assert length(result.entries) == 1
      assert hd(result.entries).id == customer.id
    end

    test "returns no customers for non-matching search" do
      org = insert(:organization)
      insert(:customer, name: "Matheus Cunha", organization: org)

      result = Customers.paginated_customers(org.id, search: "John")

      assert Enum.empty?(result.entries)
    end

    test "returns customers matching case-insensitive name search" do
      org = insert(:organization)
      customer = insert(:customer, name: "Matheus Cunha", organization: org)

      result = Customers.paginated_customers(org.id, search: "matheus")

      assert length(result.entries) == 1
      assert hd(result.entries).id == customer.id
    end

    test "returns customers matching partial name in the middle" do
      org = insert(:organization)
      customer = insert(:customer, name: "Matheus Cunha", organization: org)

      result = Customers.paginated_customers(org.id, search: "theus")

      assert length(result.entries) == 1
      assert hd(result.entries).id == customer.id
    end
  end

  describe "update_customer_address!/2" do
    test "updates customer addresses" do
      customer = insert(:customer)
      old_address = insert(:address, customer: customer)
      new_address_attrs = params_for(:address)
      customer = Repo.preload(customer, [:addresses])

      assert {:ok, %Customer{} = updated_customer} =
               Customers.update_customer_address!(customer, [new_address_attrs])

      assert length(updated_customer.addresses) == 1
      [updated_address] = updated_customer.addresses

      refute updated_address.id == old_address.id
      assert updated_address.street == new_address_attrs.street
      assert updated_address.city_name == new_address_attrs.city_name
      assert updated_address.state == new_address_attrs.state
      assert updated_address.zip == new_address_attrs.zip
    end

    test "keeps old addresses when sending both old and new" do
      customer = insert(:customer)
      old_address = insert(:address, customer: customer)
      new_address_attrs = params_for(:address)

      customer = Repo.preload(customer, [:addresses])

      assert {:ok, %Customer{} = updated_customer} =
               Customers.update_customer_address!(customer, [
                 Map.from_struct(old_address),
                 new_address_attrs
               ])

      assert length(updated_customer.addresses) == 2
      assert Enum.any?(updated_customer.addresses, fn addr -> addr.id == old_address.id end)

      assert Enum.any?(updated_customer.addresses, fn addr ->
               addr.street == new_address_attrs.street
             end)
    end

    test "emits event for address created when new address is added" do
      customer = insert(:customer)
      customer = Repo.preload(customer, [:addresses])
      new_address_attrs = params_for(:address)

      assert {:ok, %Customer{} = updated_customer} =
               Customers.update_customer_address!(customer, [new_address_attrs])

      assert [new_address] = updated_customer.addresses

      assert_emit("address.created", %{
        id: nil,
        customer_id: new_address.customer_id,
        organization_id: new_address.organization_id,
        city_code: new_address.city_code,
        city_name: new_address.city_name,
        complement: new_address.complement,
        country_name: new_address.country_name,
        neighborhood: new_address.neighborhood,
        number: new_address.number,
        receiver_name: new_address.receiver_name,
        state: new_address.state,
        street: new_address.street,
        street_type: new_address.street_type,
        zip: new_address.zip
      })
    end

    test "emits event for address updated when old address is updated" do
      customer = insert(:customer)

      old_address =
        insert(:address, customer: customer)
        |> Map.from_struct()

      customer = Repo.preload(customer, [:addresses])
      updated_address_attrs = %{old_address | street: "Updated Street"}

      assert {:ok, %Customer{} = updated_customer} =
               Customers.update_customer_address!(customer, [updated_address_attrs])

      assert [updated_address] = updated_customer.addresses

      assert_emit("address.updated", %{
        id: updated_address.id,
        customer_id: updated_address.customer_id,
        organization_id: updated_address.organization_id,
        state: updated_address.state,
        zip: updated_address.zip,
        city_name: updated_address.city_name,
        street: updated_address.street,
        city_code: updated_address.city_code,
        complement: updated_address.complement,
        country_name: updated_address.country_name,
        neighborhood: updated_address.neighborhood,
        number: updated_address.number,
        receiver_name: updated_address.receiver_name,
        street_type: updated_address.street_type
      })
    end

    test "does not emit address.updated if old address is not updated" do
      customer = insert(:customer)
      old_address = insert(:address, customer: customer)
      customer = Repo.preload(customer, [:addresses])
      unchanged_address_attrs = Map.from_struct(old_address)

      assert {:ok, %Customer{}} =
               Customers.update_customer_address!(customer, [unchanged_address_attrs])

      refute_emit("address.updated")
    end

    test "with invalid data returns error changeset" do
      customer = insert(:customer)
      customer = Repo.preload(customer, [:addresses])
      invalid_address_attrs = %{zip: "invalid"}

      assert {:error, %Ecto.Changeset{}} =
               Customers.update_customer_address!(customer, [invalid_address_attrs])

      updated_customer = Customers.get_customer!(customer.organization_id, customer.id)
      assert updated_customer.addresses == customer.addresses
    end
  end

  describe "paginated_customers/2 by plain fields" do
    test "should find customer by `document_plain`" do
      org = insert(:organization)

      customer =
        insert(:customer,
          name: "Walter White",
          email: "<EMAIL>",
          document_plain: "12345678900",
          organization: org
        )

      result = Customers.paginated_customers(org.id, search: "456")
      assert length(result.entries) == 1

      assert customer.id == List.first(result.entries).id
    end

    test "should find customer by `email_plain`" do
      org = insert(:organization)

      customer =
        insert(:customer,
          name: "Walter White",
          email_plain: "<EMAIL>",
          organization: org
        )

      result = Customers.paginated_customers(org.id, search: "bad@")
      assert length(result.entries) == 1

      assert customer.id == List.first(result.entries).id
    end

    test "should find customer by `primary_phone_number_plain`" do
      org = insert(:organization)

      customer =
        insert(:customer,
          name: "Walter White",
          primary_phone_number_plain: "+5511949125467",
          organization: org
        )

      result = Customers.paginated_customers(org.id, search: "125")
      assert length(result.entries) == 1

      assert customer.id == List.first(result.entries).id
    end

    test "should find customers by all plain fields" do
      org = insert(:organization)

      insert(:customer,
        name: "Walter White",
        primary_phone_number_plain: "+5511949125467",
        organization: org
      )

      insert(:customer,
        name: "Walter White",
        email_plain: "<EMAIL>",
        organization: org
      )

      insert(:customer,
        name: "Walter White",
        document_plain: "987125222222",
        organization: org
      )

      result = Customers.paginated_customers(org.id, search: "125")
      assert length(result.entries) == 3
    end
  end
end
