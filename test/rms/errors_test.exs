defmodule Rms.ErrorsTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Errors

  describe "create invoice_error/2" do
    test "create a new invoice invoice error" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      attrs = %{
        reason: "error text",
        stacktrace: "#{__MODULE__}",
        fiscal_invoice_id: fi.id
      }

      assert {:ok, _error} = Errors.create_fiscal_invoice_error(fi, attrs)
    end
  end

  describe "list_invoice_error/3" do
    test "list all invoice errors from a organization" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)
      another_serie = insert(:invoice_serie, location: another_loc, organization: another_org)
      another_fi = insert(:fiscal_invoice, serie: another_serie, organization: another_org)

      insert(:fiscal_invoice_error, fiscal_invoice: another_fi, organization: another_org)
      insert(:fiscal_invoice_error, fiscal_invoice: another_fi, organization: another_org)
      insert(:fiscal_invoice_error, fiscal_invoice: another_fi, organization: another_org)

      fi_errors = Errors.list_fiscal_invoice_error(org.id)
      assert length(fi_errors) == 3

      assert Enum.all?(fi_errors, fn fi_error ->
               fi_error.organization_id == fi.organization_id
             end)
    end

    test "list all invoice errors from a organization using some filters" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      fi_errors =
        Errors.list_fiscal_invoice_error(org.id, query_params: [ficsal_invoice_id: fi.id])

      assert length(fi_errors) == 2
      assert Enum.all?(fi_errors, fn fi_error -> fi_error.fiscal_invoice_id == fi.id end)
    end
  end

  describe "get_invoice_error!/2" do
    test "returns a invoice error" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      fie = insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)
      insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      assert ierror = Errors.get_fiscal_invoice_error!(org.id, fie.id)
      assert ierror.id == fie.id
      assert ierror.reason == fie.reason
      assert ierror.stacktrace == fie.stacktrace
    end

    test "raises Ecto.NoResultsError when invoice error does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Errors.get_fiscal_invoice_error!(org.id, 1)
      end
    end

    test "raises Ecto.NoResultsError when invoice error does not belong to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      fie = insert(:fiscal_invoice_error, fiscal_invoice: fi, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        Errors.get_fiscal_invoice_error!(another_org.id, fie.id)
      end
    end
  end

  describe "create order_error/2" do
    test "create a new invoice invoice error" do
      org = insert(:organization)
      order = insert(:order, organization: org)

      attrs = %{
        error: "error text",
        stacktrace: "#{__MODULE__}",
        order_id: order.id
      }

      assert {:ok, _error} = Errors.create_order_error(org.id, attrs)
    end

    test "does not create when order does not belongs to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      order = insert(:order, organization: org)

      attrs = %{
        reason: "error text",
        stacktrace: "#{__MODULE__}",
        order_id: order.id
      }

      assert {:error, _error} = Errors.create_order_error(another_org.id, attrs)
    end
  end

  describe "list_order_error/3" do
    test "list all order errors from a organization" do
      org = insert(:organization)
      order = insert(:order, organization: org)

      insert(:order_error, order: order, organization: org)
      insert(:order_error, order: order, organization: org)
      insert(:order_error, order: order, organization: org)

      another_org = insert(:organization)
      another_order = insert(:order, organization: another_org)

      insert(:order_error, order: another_order, organization: another_org)
      insert(:order_error, order: another_order, organization: another_org)
      insert(:order_error, order: another_order, organization: another_org)

      order_errors = Errors.list_order_error(org.id)
      assert length(order_errors) == 3
      assert Enum.all?(order_errors, fn order_error -> order_error.organization_id == org.id end)
    end

    test "list all invoice errors from a organization using some filters" do
      org = insert(:organization)
      order = insert(:order, organization: org)

      insert(:order_error, order: order, organization: org)
      insert(:order_error, order: order, organization: org)

      order = insert(:order, organization: org)

      insert(:order_error, order: order, organization: org)
      insert(:order_error, order: order, organization: org)

      order = insert(:order, organization: org)

      insert(:order_error, order: order, organization: org)
      insert(:order_error, order: order, organization: org)

      order_errors = Errors.list_order_error(org.id, query_params: [order_id: order.id])
      assert length(order_errors) == 2
      assert Enum.all?(order_errors, fn order_error -> order_error.order_id == order.id end)
    end
  end

  describe "get_order_error!/2" do
    test "returns a invoice error" do
      org = insert(:organization)
      order = insert(:order, organization: org)

      oe = insert(:order_error, order: order, organization: org)

      assert error = Errors.get_order_error!(org.id, oe.id)
      assert error.id == oe.id
      assert error.error == oe.error
      assert error.stacktrace == oe.stacktrace
    end

    test "raises Ecto.NoResultsError when invoice error does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Errors.get_order_error!(org.id, 1)
      end
    end

    test "raises Ecto.NoResultsError when invoice error does not belong to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      order = insert(:order, organization: org)

      oe = insert(:order_error, order: order, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        Errors.get_order_error!(another_org.id, oe.id)
      end
    end
  end
end
