defmodule Rms.Events.Handlers.AmaroNcmUpdateTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  alias Rms.Events.Handlers.AmaroNcmUpdate
  alias Rms.Integrations.Erp.Amaro.Mock
  alias Rms.Commerce.Products.ProductVariant

  setup :verify_on_exit!

  describe "process/1" do
    setup do
      organization = insert(:organization)

      insert(:organization_setting,
        organization: organization,
        key: "erp_integration",
        value: %{"data" => "amaro"}
      )

      product = insert(:product, organization: organization, ncm: nil)

      product_variant =
        insert(:product_variant, product: product, organization: organization, sku: "SKU123")

      %{
        organization: organization,
        product: product,
        product_variant: product_variant
      }
    end

    test "updates product NCM from AMARO API when product has no NCM code", %{
      organization: organization,
      product: product,
      product_variant: product_variant
    } do
      args = %{
        "resource" => ProductVariant.event_payload(product_variant)
      }

      expect(Mock, :client, fn _organization_id -> %Tesla.Client{} end)

      expect(Mock, :get_product_taxes, fn _client, sku ->
        assert sku == product_variant.sku
        {:ok, %{"ncm" => "12345678"}}
      end)

      assert :ok = perform_job(AmaroNcmUpdate, args)

      updated_product = Rms.Commerce.Products.get_product!(organization.id, product.id)
      assert updated_product.ncm == "12345678"
    end

    test "discards job when product already has NCM code", %{
      organization: _organization,
      product: product,
      product_variant: product_variant
    } do
      # Update product to have an NCM code
      {:ok, _product_with_ncm} = Rms.Commerce.Products.update_product(product, %{ncm: "87654321"})

      args = %{
        "resource" => ProductVariant.event_payload(product_variant)
      }

      assert {:ok, "product already has an ncm code"} = perform_job(AmaroNcmUpdate, args)
    end

    test "discards job when erp integration is not amaro", %{
      organization: organization,
      product: _product,
      product_variant: product_variant
    } do
      # Update the ERP integration to something other than "amaro"
      Rms.Settings.update_organization_setting(
        organization.id,
        "erp_integration",
        "other_erp"
      )

      args = %{
        "resource" => ProductVariant.event_payload(product_variant)
      }

      assert {:ok, "not amaro integration"} = perform_job(AmaroNcmUpdate, args)
    end

    test "returns error when API request fails", %{
      organization: _organization,
      product: _product,
      product_variant: product_variant
    } do
      args = %{
        "resource" => ProductVariant.event_payload(product_variant)
      }

      expect(Mock, :client, fn _organization_id -> %Tesla.Client{} end)

      expect(Mock, :get_product_taxes, fn _client, _sku ->
        {:error, "API request failed"}
      end)

      assert {:error, "API request failed"} = perform_job(AmaroNcmUpdate, args)
    end
  end
end
