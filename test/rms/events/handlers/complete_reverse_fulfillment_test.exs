defmodule Rms.Events.Handlers.CompleteReverseFulfillmentTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Events.Handlers.CompleteReverseFulfillment

  describe "process/1" do
    test "completes processing reverse fulfillments associated with return credit payments" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      # Create order and transaction
      order = insert(:order, organization: organization, customer: customer)
      transaction = insert(:transaction, order: order, organization: organization)

      # Create a processing reverse fulfillment
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "processing"
        )

      # Create iglu credit linked to the reverse fulfillment
      iglu_credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          reverse_fulfillment: reverse_fulfillment,
          status: "available"
        )

      # Create return credit payment
      payment =
        insert(:payment,
          organization: organization,
          transaction: transaction,
          method: "return_credit",
          status: "settled"
        )

      # Link payment to iglu credit
      insert(:iglu_credit_payment,
        organization: organization,
        payment: payment,
        iglu_credit: iglu_credit
      )

      # Process the order.paid event using Oban.Pro.Testing
      assert {:ok, :ok} =
               perform_job(CompleteReverseFulfillment, %{
                 "event_name" => "order.paid",
                 "resource" => %{
                   "transaction" => %{"id" => transaction.id},
                   "organization_id" => organization.id
                 }
               })

      # Assert the reverse fulfillment was completed
      updated_rf = Repo.reload(reverse_fulfillment)
      assert updated_rf.status == "completed"
    end

    test "does not complete reverse fulfillments for non-return credit payments" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      order = insert(:order, organization: organization, customer: customer)
      transaction = insert(:transaction, order: order, organization: organization)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "processing"
        )

      iglu_credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          reverse_fulfillment: reverse_fulfillment,
          status: "available"
        )

      # Create credit card payment instead of return credit
      payment =
        insert(:payment,
          organization: organization,
          transaction: transaction,
          method: "credit_card",
          status: "settled"
        )

      insert(:iglu_credit_payment,
        organization: organization,
        payment: payment,
        iglu_credit: iglu_credit
      )

      assert {:ok, :ok} =
               perform_job(CompleteReverseFulfillment, %{
                 "event_name" => "order.paid",
                 "resource" => %{
                   "transaction" => %{"id" => transaction.id},
                   "organization_id" => organization.id
                 }
               })

      updated_rf = Repo.reload(reverse_fulfillment)
      assert updated_rf.status == "processing"
    end

    test "ignores non-processing reverse fulfillments" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      order = insert(:order, organization: organization, customer: customer)
      transaction = insert(:transaction, order: order, organization: organization)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          # Not processing
          status: "pending"
        )

      iglu_credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          reverse_fulfillment: reverse_fulfillment,
          status: "available"
        )

      payment =
        insert(:payment,
          organization: organization,
          transaction: transaction,
          method: "return_credit",
          status: "settled"
        )

      insert(:iglu_credit_payment,
        organization: organization,
        payment: payment,
        iglu_credit: iglu_credit
      )

      assert {:ok, :ok} =
               perform_job(CompleteReverseFulfillment, %{
                 "event_name" => "order.paid",
                 "resource" => %{
                   "transaction" => %{"id" => transaction.id},
                   "organization_id" => organization.id
                 }
               })

      updated_rf = Repo.reload(reverse_fulfillment)
      assert updated_rf.status == "pending"
    end

    test "updates order status to returned when all items are returned" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      order = insert(:order, organization: organization, customer: customer)
      fulfillment = insert(:fulfillment, order: order, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      line_item =
        insert(:line_item,
          location: build(:location, organization: organization),
          fulfillment: fulfillment,
          product_variant: product_variant,
          organization: organization,
          quantity: 2
        )

      transaction = insert(:transaction, order: order, organization: organization)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "processing"
        )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 2
      )

      iglu_credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          reverse_fulfillment: reverse_fulfillment,
          status: "available"
        )

      payment =
        insert(:payment,
          organization: organization,
          transaction: transaction,
          method: "return_credit",
          status: "settled"
        )

      insert(:iglu_credit_payment,
        organization: organization,
        payment: payment,
        iglu_credit: iglu_credit
      )

      assert {:ok, :ok} =
               perform_job(CompleteReverseFulfillment, %{
                 "event_name" => "order.paid",
                 "resource" => %{
                   "transaction" => %{"id" => transaction.id},
                   "organization_id" => organization.id
                 }
               })

      updated_order = Repo.reload(order)
      assert updated_order.status == "returned"
    end

    test "updates order status to partially_returned when some items are returned" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      order = insert(:order, organization: organization, customer: customer)
      fulfillment = insert(:fulfillment, order: order, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      line_item =
        insert(:line_item,
          location: build(:location, organization: organization),
          product_variant: product_variant,
          fulfillment: fulfillment,
          organization: organization,
          quantity: 2
        )

      transaction = insert(:transaction, order: order, organization: organization)

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          status: "processing"
        )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 1
      )

      iglu_credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          reverse_fulfillment: reverse_fulfillment,
          status: "available"
        )

      payment =
        insert(:payment,
          organization: organization,
          transaction: transaction,
          method: "return_credit",
          status: "settled"
        )

      insert(:iglu_credit_payment,
        organization: organization,
        payment: payment,
        iglu_credit: iglu_credit
      )

      assert {:ok, :ok} =
               perform_job(CompleteReverseFulfillment, %{
                 "event_name" => "order.paid",
                 "resource" => %{
                   "transaction" => %{"id" => transaction.id},
                   "organization_id" => organization.id
                 }
               })

      updated_order = Repo.reload(order)
      assert updated_order.status == "partially_returned"
    end
  end
end
