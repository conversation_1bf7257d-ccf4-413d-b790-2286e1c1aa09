defmodule Rms.Events.Handlers.CreateFulfillmentOrderTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  setup do
    Mox.stub_with(Rms.Clients.FeatureFlag.Mock, FeatureFlagStub)
    :ok
  end

  describe "no ecommerce perform/1" do
    test "returns a error when there is no ecommerce config" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order = insert(:order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      f1 = insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")
      f2 = insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      assert {:error,
              %{
                reason: "No organization ecommerce found",
                stacktrace: "Elixir.Rms.Commerce.Fulfillments.CreateFulfillments"
              }} =
               perform_job(Rms.Events.Handlers.CreateFulfillmentOrder, %{
                 "resource" => %{
                   "id" => order.id,
                   "organization_id" => order.organization_id
                 }
               })

      assert [] = all_enqueued(worker: Rms.Integrations.Shopify.CreateDraftOrderWorker)
    end
  end

  describe "shopify perform/1" do
    alias Rms.Integrations.Shopify

    test "correctly create a fulfillment for each delivery method" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order = insert(:order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      env_params = %{data: "shopify"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      f1 = insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")
      f2 = insert(:fulfillment, order: order, organization: org, shipping_method: "local-pickup")
      f3 = insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      insert(:line_item,
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store"
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      insert(:line_item,
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup"
      )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      insert(:line_item,
        fulfillment: f3,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "delivery"
      )

      {:ok, _invoices} =
        perform_job(Rms.Events.Handlers.CreateFulfillmentOrder, %{
          "resource" => %{
            "id" => order.id,
            "organization_id" => order.organization_id
          }
        })

      fulfillments = Rms.Commerce.Fulfillments.list_fulfillments(order.organization_id)
      assert length(fulfillments) == 3

      Enum.each(fulfillments, fn fulfillment ->
        args = %{"id" => fulfillment.id, "organization_id" => fulfillment.organization_id}

        if fulfillment.shipping_method in ["in-store", "delivery"] do
          assert_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          assert_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        else
          refute_enqueued(worker: Shopify.CreateCustomerWorker, args: args)
          refute_enqueued(worker: Shopify.CreateDraftOrderWorker, args: args)
        end
      end)
    end
  end
end
