defmodule Rms.Events.Handlers.EmitInvoiceOrderTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Events.Handlers.EmitInvoiceOrder

  describe "perform/1" do
    test "create a nfc invoice for the 2 line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      nfc_serie =
        insert(:invoice_serie,
          invoice_env: "test",
          invoice_type: "nfc",
          location: loc,
          organization: org
        )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org, status: "done")
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      line_item1 =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} =
        perform_job(EmitInvoiceOrder, %{
          "resource" => %{
            "id" => order.id
          }
        })

      invoice =
        invoices["fulfillment:#{fulfillment.id}-shipping_method:#{fulfillment.shipping_method}"]

      assert invoice.operation_type == "sale"
      assert invoice.serie_id == nfc_serie.id

      line_item_id = line_item.id
      line_item_id1 = line_item1.id

      assert [%{line_item_id: ^line_item_id}, %{line_item_id: ^line_item_id1}] =
               invoice.invoice_items
    end

    test "create a nf invoice for the 2 line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      nf_serie =
        insert(:invoice_serie,
          invoice_env: "test",
          invoice_type: "nfc",
          location: loc,
          organization: org
        )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org, status: "done")
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer,
        transaction: transaction,
        organization: org,
        document_type: "cnpj"
      )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} =
        perform_job(EmitInvoiceOrder, %{
          "resource" => %{
            "id" => order.id
          }
        })

      invoice =
        invoices["fulfillment:#{fulfillment.id}-shipping_method:#{fulfillment.shipping_method}"]

      assert invoice.operation_type == "sale"
      assert invoice.serie_id == nf_serie.id
    end

    test "does not create for delivery shipping methods" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      loc2 = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc2,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc2,
        organization: org
      )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org, status: "done")
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      insert(:line_item,
        fulfillment: fulfillment,
        shipping_method: "local pickup",
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "delivery",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc2
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} =
        perform_job(EmitInvoiceOrder, %{
          "resource" => %{
            "id" => order.id
          }
        })

      Enum.each(invoices, fn {_, invoice} ->
        assert invoice == "sent to e-commerce"
      end)
    end

    test "does not create for transaction status != done" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:error, %{reason: "Can not find a transaction", stacktrace: _}} =
        perform_job(EmitInvoiceOrder, %{
          "resource" => %{
            "id" => order.id
          }
        })
    end

    test "does not create when there is not a valid serie" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        status: "inactive",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:error, %{reason: "Can not find a transaction", stacktrace: _}} =
        perform_job(EmitInvoiceOrder, %{
          "resource" => %{
            "id" => order.id
          }
        })
    end
  end
end
