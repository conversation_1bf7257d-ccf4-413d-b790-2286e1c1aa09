defmodule Rms.Events.Handlers.ImportSalesInvoiceTest do
  use Rms.DataCase, async: true

  import Mox
  import Rms.Factory

  alias Rms.Events.Handlers.ImportSalesInvoice

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock
  alias Rms.Integrator.Mock, as: IntegratorMock

  alias Rms.Repo

  setup :verify_on_exit!

  describe "process/1" do
    setup :create_shopify_order_with_fulfillment

    test "when the order exists and import is successful, it creates the fiscal invoice",
         %{order: order, fulfillment: fulfillment} do
      resource = %{
        "id" => order.id,
        "organization_id" => order.organization_id
      }

      job_args = %{"resource" => resource}

      mock_order_response = %{
        "data" => %{
          "order" => %{
            "name" => "1001",
            "customAttributes" => [
              %{"key" => "nfce-key", "value" => "35240111111111000111550010000987651234567898"}
            ]
          }
        }
      }

      order_external_id = order.external_id

      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:get_order!, fn :mock_client, ^order_external_id, _, _ ->
        {:ok, mock_order_response}
      end)

      expect(VincoClientMock, :get_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:ok,
         %{
           "ChaveDFe" => "35240111111111000111550010000987651234567898",
           "XmlDFe" => """
           <?xml version="1.0" encoding="UTF-8"?>
           <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
             <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
             <infNFe versao="4.00" Id="NFe352401...">
               <ide>
                 <cUF>35</cUF>
                 <cNF>12345678</cNF>
                 <natOp>VENDA DE MERCADORIA</natOp>
                 <mod>55</mod>
                 <serie>1</serie>
                 <nNF>98765</nNF>
                 <dhEmi>2024-01-15T10:00:00-03:00</dhEmi>
                 <tpNF>1</tpNF>
                 <idDest>1</idDest>
                 <cMunFG>3550308</cMunFG>
                 <tpImp>1</tpImp>
                 <tpEmis>1</tpEmis>
                 <cDV>8</cDV>
                 <tpAmb>1</tpAmb>
                 <finNFe>1</finNFe>
                 <indFinal>1</indFinal>
                 <indPres>1</indPres>
                 <procEmi>0</procEmi>
                 <verProc>App v1.0</verProc>
               </ide>
               <!-- Other tags like emit, dest, det, total, etc. -->
             </infNFe>
           <infNFeSupl>
           <qrCode>
           <![CDATA[ https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240943253315000234652610000000011248668588|2|2|1|bc552bf1f84ae73f39ae2901e020fc9e7b2186a5 ]]>
           </qrCode>
           <urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave>
           </infNFeSupl>
           </NFe>
           <protNFe versao="4.00">
               <infProt>
                   <tpAmb>1</tpAmb>
                   <verAplic>SP_NFE_PL_009_V4</verAplic>
                   <chNFe>35240111111111000111550010000987651234567898</chNFe>
                   <dhRecbto>2024-01-15T10:01:00-03:00</dhRecbto>
                   <nProt>135240000000001</nProt>
                   <digVal>...</digVal>
                   <cStat>100</cStat>
                   <xMotivo>Autorizado o uso da NF-e</xMotivo>
               </infProt>
           </protNFe>
           </nfeProc>
           """
         }}
      end)

      assert {:ok, _} = ImportSalesInvoice.process(%Oban.Job{args: job_args})

      updated_fulfillment = Repo.preload(fulfillment, :fiscal_invoices, force: true)
      assert [fiscal_invoice] = updated_fulfillment.fiscal_invoices
      assert fiscal_invoice.df_key == "35240111111111000111550010000987651234567898"
    end

    test "when the fulfillment already has a sales fiscal invoice, it returns :no_fulfillments_without_fiscal_invoice",
         %{order: order, fulfillment: fulfillment} do
      # Insert a sales fiscal invoice for the existing fulfillment
      insert(:fiscal_invoice,
        fulfillment: fulfillment,
        organization: fulfillment.organization,
        operation_type: "sale"
      )

      resource = %{
        "id" => order.id,
        "organization_id" => order.organization_id
      }

      job_args = %{"resource" => resource}

      # Ensure Shopify and Vinco are not called
      expect(ShopifyMock, :client, 0, fn _, _ -> :mock_client end)

      expect(VincoClientMock, :get_fiscal_invoice, 0, fn _, _, _, _, _ ->
        raise "should not be called"
      end)

      assert {:ok, :no_fulfillments_without_fiscal_invoice} ==
               ImportSalesInvoice.process(%Oban.Job{args: job_args})
    end

    test "when the order exists but no invoice is found externally, it returns an error",
         %{order: order, fulfillment: fulfillment} do
      resource = %{
        "id" => order.id,
        "organization_id" => order.organization_id
      }

      job_args = %{"resource" => resource}

      mock_order_response_no_key = %{
        "data" => %{
          "order" => %{
            "name" => "1001",
            "customAttributes" => []
          }
        }
      }

      order_external_id = order.external_id

      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:get_order!, fn :mock_client, ^order_external_id, _, _ ->
        {:ok, mock_order_response_no_key}
      end)

      expect(VincoClientMock, :get_fiscal_invoice, 0, fn _, _, _, _, _ -> {:error, :not_found} end)

      expect(IntegratorMock, :fetch_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:error, :not_found}
      end)

      # Call the actual worker process function
      assert {:error, :invoice_not_found} =
               ImportSalesInvoice.process(%Oban.Job{args: job_args})

      # Verify no fiscal invoice was created
      updated_fulfillment = Repo.preload(fulfillment, :fiscal_invoices, force: true)
      assert updated_fulfillment.fiscal_invoices == []
    end

    defp create_shopify_order_with_fulfillment(context) do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      insert(:shopify_credential, organization: organization)
      insert(:vinco_credential, organization: organization, location: location)
      insert(:api_token, organization: organization)

      order =
        insert(:order,
          organization: organization,
          location: location,
          source: "shopify",
          external_id: "gid://shopify/Order/#{Ecto.UUID.generate()}"
        )

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: organization,
          external_reference: "gid://shopify/Fulfillment/#{Ecto.UUID.generate()}"
        )

      Map.merge(context, %{
        order: order,
        organization: organization,
        location: location,
        fulfillment: fulfillment
      })
    end
  end
end
