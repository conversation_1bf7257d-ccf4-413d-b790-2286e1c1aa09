defmodule Rms.Events.Handlers.RefundPaymentTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  setup :verify_on_exit!

  describe "perform/1" do
    test "refund transaction" do
      org = insert(:organization)
      order = insert(:order, organization: org, location: build(:location, organization: org))
      credential = insert(:gift_promo_credential, organization: org, location: order.location)
      transaction = insert(:transaction, order: order, organization: credential.organization)

      payment =
        insert(:payment,
          method: "gift_card",
          organization: credential.organization,
          transaction: transaction,
          metadata: %{
            "provider" => "gift_promo",
            "card_number" => "**********",
            "card_pin" => "1234",
            "nsu_host" => "nsu_host",
            "authorization" => "authorization"
          }
        )

      payment_id = payment.id

      resource = Rms.Finance.Payment.event_payload(payment)

      expect(Rms.Integrations.GiftPromo.Mock, :client, fn _ ->
        :mock_client
      end)

      expect(Rms.Integrations.GiftPromo.Mock, :refund_transaction, fn :mock_client, params ->
        assert %{
                 access_key: "gcod#188484*aKsu1",
                 authorization: "authorization",
                 capture_type: "1",
                 card_number: "**********",
                 card_pin: "1234",
                 nsu: ^payment_id,
                 nsu_host: "nsu_host",
                 provider: "2",
                 store_id: "188484"
               } = params

        {:ok, %{}}
      end)

      assert {:ok, _} = perform_job(Rms.Events.Handlers.RefundPayment, %{"resource" => resource})
    end

    test "refund transaction for shopify provider" do
      org = insert(:organization)
      order = insert(:order, organization: org, location: build(:location, organization: org))
      _credential = insert(:shopify_credential, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          status: "settled",
          method: "gift_card",
          amount: "10.0",
          metadata: %{
            provider: "shopify",
            card_number: "123456"
          },
          organization: org,
          transaction: transaction
        )

      expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"
      credit_transaction_id = "gid://shopify/GiftCardCreditTransaction/456"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      expect(Rms.Integrations.Shopify.Mock, :credit_gift_card, fn :mock_client,
                                                                  ^gift_card_id,
                                                                  credit_input ->
        assert credit_input == %{
                 creditAmount: %{
                   amount: Decimal.to_string(payment.amount),
                   currencyCode: "BRL"
                 },
                 note: "refund_iglu_payment_id:#{payment.id}"
               }

        {:ok,
         %{
           "giftCardCreditTransaction" => %{
             "id" => credit_transaction_id,
             "amount" => %{
               "amount" => Decimal.to_string(payment.amount),
               "currencyCode" => "BRL"
             }
           }
         }}
      end)

      resource = Rms.Finance.Payment.event_payload(payment)

      Oban.Testing.with_testing_mode(:inline, fn ->
        perform_job(Rms.Events.Handlers.RefundPayment, %{"resource" => resource})
      end)
    end
  end
end
