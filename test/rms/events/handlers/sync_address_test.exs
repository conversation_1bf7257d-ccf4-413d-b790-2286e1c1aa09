defmodule Rms.Events.Handlers.SyncAddressTest do
  use Rms.DataCase, async: true
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  alias Rms.Events.Handlers.SyncAddress
  alias Rms.Integrations.CustomerSyncMapping

  # Mock the VTEX client
  @vtex_client Rms.Integrations.VTEX.Mock

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    customer = insert(:customer, organization: organization)
    address = insert(:address, customer: customer, organization: organization)
    vtex_credential = insert(:vtex_credential, organization: organization)

    insert(:customer_sync_mapping,
      organization: organization,
      customer: customer,
      source: "vtex",
      external_id: "003f4a80-232d-11ef-8452-127906b53e0f"
    )

    %{
      organization: organization,
      customer: customer,
      address: address,
      vtex_credential: vtex_credential
    }
  end

  describe "process/1" do
    test "successfully creates an address in VTEX", %{address: address} do
      expect(@vtex_client, :client, fn _vtex_credential, _env -> :vtex_client end)

      expect(@vtex_client, :create_document, fn :vtex_client, "AD", params ->
        assert params.userId == "003f4a80-232d-11ef-8452-127906b53e0f"
        assert params.addressName == "IGLU-#{address.id}"
        {:ok, %{"id" => "3770817b-232d-11ef-8452-0affdeec9309"}}
      end)

      args = %{
        "event_name" => "address.created",
        "resource" => Rms.Addresses.Address.event_payload(address)
      }

      assert {:ok, _} = perform_job(SyncAddress, args)
    end

    test "successfully updates an address in VTEX", %{address: address} do
      expect(@vtex_client, :client, fn _vtex_credential, _env -> :vtex_client end)

      expect(@vtex_client, :search_customer_address, fn :vtex_client,
                                                        "003f4a80-232d-11ef-8452-127906b53e0f",
                                                        _ ->
        {:ok,
         [
           %{
             "addressName" => "24wrxnz3wbg",
             "userId" => "003f4a80-232d-11ef-8452-127906b53e0f",
             "id" => "3770817b-232d-11ef-8452-0affdeec9309",
             "accountId" => "120d3067-4e78-4fe9-a6a2-1f120dac67fd",
             "accountName" => "boboqa",
             "dataEntityId" => "AD",
             "postalCode" => address.zip,
             "street" => address.street,
             "number" => address.number
           }
         ]}
      end)

      expect(@vtex_client, :update_document, fn :vtex_client,
                                                "AD",
                                                "3770817b-232d-11ef-8452-0affdeec9309",
                                                params ->
        assert params.userId == "003f4a80-232d-11ef-8452-127906b53e0f"
        assert params.addressName == "IGLU-#{address.id}"
        {:ok, %{"id" => "3770817b-232d-11ef-8452-0affdeec9309"}}
      end)

      args = %{
        "event_name" => "address.updated",
        "resource" => Rms.Addresses.Address.event_payload(address)
      }

      assert {:ok, _} = perform_job(SyncAddress, args)
    end

    test "returns error when VTEX customer mapping is not found", %{address: address} do
      Repo.delete_all(CustomerSyncMapping)

      args = %{
        "event_name" => "address.created",
        "resource" => Rms.Addresses.Address.event_payload(address)
      }

      assert {:discard, :vtex_customer_not_found} = perform_job(SyncAddress, args)
    end

    test "returns error when VTEX address is not found during update", %{address: address} do
      expect(@vtex_client, :client, fn _vtex_credential, _env -> :vtex_client end)

      expect(@vtex_client, :search_customer_address, fn :vtex_client,
                                                        "003f4a80-232d-11ef-8452-127906b53e0f",
                                                        _ ->
        {:ok, []}
      end)

      args = %{
        "event_name" => "address.updated",
        "resource" => Rms.Addresses.Address.event_payload(address)
      }

      assert {:error, :vtex_address_not_found} = perform_job(SyncAddress, args)
    end
  end
end
