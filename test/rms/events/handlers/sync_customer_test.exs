defmodule Rms.Events.Handlers.SyncCustomerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  alias Rms.Events.Handlers.SyncCustomer
  alias Rms.Integrations.VTEX.Customers.CreateCustomerWorker
  alias Rms.Integrations.VTEX.Customers.CreateAddressWorker

  setup :verify_on_exit!

  describe "process/1" do
    setup do
      organization = insert(:organization)
      insert(:vtex_credential, organization: organization)

      insert(:organization_setting,
        organization: organization,
        key: "customer_sync_service",
        value: %{"data" => "vtex"}
      )

      %{organization: organization}
    end

    test "enqueues VTEX customer creation workflow", %{organization: organization} do
      customer = insert(:customer, organization: organization)

      args = %{
        "event_name" => "customer.created",
        "resource" => %{
          "id" => customer.id,
          "organization_id" => organization.id,
          "name" => customer.name,
          "email" => customer.email,
          "document" => customer.document,
          "document_type" => customer.document_type,
          "primary_phone_number" => customer.primary_phone_number,
          "addresses" => []
        }
      }

      assert :ok = perform_job(SyncCustomer, args)

      assert_enqueued(
        worker: <PERSON><PERSON><PERSON>ust<PERSON>Worker,
        args: %{"customer" => args["resource"], "organization_id" => organization.id}
      )

      assert_enqueued(
        worker: CreateAddressWorker,
        args: %{"customer" => args["resource"], "organization_id" => organization.id}
      )
    end

    test "does not enqueue workflow for unsupported service", %{organization: organization} do
      Rms.Settings.update_organization_setting(
        organization.id,
        "customer_sync_service",
        "unsupported"
      )

      customer = insert(:customer, organization: organization)

      args = %{
        "event_name" => "customer.created",
        "resource" => %{"id" => customer.id, "organization_id" => organization.id}
      }

      assert {:discard, "invalid service \"unsupported\""} = perform_job(SyncCustomer, args)

      refute_enqueued(worker: CreateWorker)
      refute_enqueued(worker: CreateAddressWorker)
    end

    test "runs the entire workflow successfully", %{organization: organization} do
      customer = insert(:customer, organization: organization)
      address = insert(:address, customer: customer, organization: organization)

      args = %{
        "event_name" => "customer.created",
        "resource" => %{
          "id" => customer.id,
          "organization_id" => organization.id,
          "name" => customer.name,
          "email" => customer.email,
          "document" => customer.document,
          "document_type" => customer.document_type,
          "primary_phone_number" => customer.primary_phone_number,
          "birthdate" => customer.birthdate,
          "addresses" => [
            %{
              "id" => address.id,
              "receiver_name" => address.receiver_name,
              "city_name" => address.city_name,
              "state" => address.state,
              "country_name" => address.country_name,
              "neighborhood" => address.neighborhood,
              "street" => address.street,
              "street_type" => address.street_type,
              "number" => address.number,
              "zip" => address.zip,
              "complement" => address.complement
            }
          ]
        }
      }

      expect(Rms.Integrations.VTEX.Mock, :client, 2, fn _, _ -> Tesla.client([]) end)

      expect(Rms.Integrations.VTEX.Mock, :create_document, 2, fn _, _, _ ->
        {:ok, %{"DocumentId" => "123", "Href" => "http://example.com", "Id" => "CL-123"}}
      end)

      workflow = SyncCustomer.vtex_workflow(args["resource"])

      assert %{completed: 3} = run_workflow(workflow)
    end

    test "runs the entire workflow successfully with nil birthdate", %{organization: organization} do
      customer = insert(:customer, organization: organization)
      address = insert(:address, customer: customer, organization: organization)

      args = %{
        "event_name" => "customer.created",
        "resource" => %{
          "id" => customer.id,
          "organization_id" => organization.id,
          "name" => customer.name,
          "email" => customer.email,
          "document" => customer.document,
          "document_type" => customer.document_type,
          "primary_phone_number" => customer.primary_phone_number,
          "birthdate" => nil,
          "addresses" => [
            %{
              "id" => address.id,
              "receiver_name" => address.receiver_name,
              "city_name" => address.city_name,
              "state" => address.state,
              "country_name" => address.country_name,
              "neighborhood" => address.neighborhood,
              "street" => address.street,
              "street_type" => address.street_type,
              "number" => address.number,
              "zip" => address.zip,
              "complement" => address.complement
            }
          ]
        }
      }

      expect(Rms.Integrations.VTEX.Mock, :client, 2, fn _, _ -> Tesla.client([]) end)

      expect(Rms.Integrations.VTEX.Mock, :create_document, 2, fn _, _, _ ->
        {:ok, %{"DocumentId" => "123", "Href" => "http://example.com", "Id" => "CL-123"}}
      end)

      workflow = SyncCustomer.vtex_workflow(args["resource"])

      assert %{completed: 3} = run_workflow(workflow)
    end
  end
end
