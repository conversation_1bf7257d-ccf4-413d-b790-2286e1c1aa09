defmodule Rms.Events.Handlers.SyncOrderTest do
  use Rms.DataCase
  use Rms.EventsCase

  import Mox

  alias Rms.Events.Handlers.SyncOrder
  alias Rms.ShopifyMock
  alias Rms.Commerce.Orders.Order

  setup do
    org = insert(:organization)
    insert(:shopify_credential, organization: org)

    {:ok, org: org}
  end

  test "mark order as paid on Shopify if external_id is not null", %{org: org} do
    order = insert(:order, organization: org, external_id: "gid://shopify/Order/any_order_id")

    ShopifyMock.mark_as_paid_mock()

    assert {:discard, "no ecommerce found for #{org.id}"} ==
             perform_job(SyncOrder, %{
               event_name: "order.paid",
               resource: Rms.Commerce.Orders.Order.event_payload(order)
             })
  end

  describe "vtex order" do
    test "enqueues place order and payment notification workers", %{org: org} do
      {:ok, _} = Rms.Settings.create_organization_setting(org.id, "ecommerce", "vtex")
      order = insert(:order, organization: org)
      insert(:fulfillment, order: order, ecommerce: "vtex", organization: org)

      assert :ok =
               perform_job(SyncOrder, %{
                 event_name: "order.paid",
                 resource: Rms.Commerce.Orders.Order.event_payload(order)
               })

      assert_enqueued(
        worker: Rms.Integrations.VTEX.PlaceOrderWorker,
        args: %{order_id: order.id, organization_id: org.id},
        prefix: "public"
      )

      assert_enqueued(
        worker: Rms.Integrations.VTEX.PaymentNotificationWorker,
        args: %{order_id: order.id, organization_id: org.id},
        prefix: "public"
      )
    end
  end

  describe "shopify order" do
    setup do
      org = insert(:organization)

      location = insert(:location, organization: org)
      insert(:location_mapping, organization: org, location: location)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, organization: org, product: product)

      order = insert(:order, %{organization: org, location: location})

      {:ok, _} = Rms.Settings.create_organization_setting(org.id, "ecommerce", "shopify")

      %{
        org: org,
        location: location,
        order: order,
        product: product,
        product_variant: product_variant
      }
    end

    test "ensure :create_order flow is called - delivery, in-store with enabled feature flag", %{
      org: org,
      order: order
    } do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, fn _ -> true end)

      f1 =
        insert(:fulfillment, %{
          shipping_method: "delivery",
          organization: org,
          order: order
        })

      f2 =
        insert(:fulfillment, %{
          shipping_method: "in-store",
          organization: org,
          order: order
        })

      :ok =
        perform_job(SyncOrder, %{
          event_name: "order.paid",
          resource: Order.event_payload(order)
        })

      assert_enqueued(
        worker: Rms.Integrations.Shopify.CreateOrderWorker,
        args: %{fulfillment_id: f1.id, organization_id: org.id},
        prefix: "public"
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.CreateOrderWorker,
        args: %{fulfillment_id: f2.id, organization_id: org.id},
        prefix: "public"
      )
    end

    test "ensure :draft_order flow is called - with disabled feature flag", %{
      org: org,
      order: order
    } do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, fn _ -> false end)

      f1 =
        insert(:fulfillment, %{
          shipping_method: "in-store",
          organization: org,
          order: order,
          external_reference: "shopify_id"
        })

      :ok =
        perform_job(SyncOrder, %{
          event_name: "order.paid",
          resource: Order.event_payload(order)
        })

      default_arg = %{id: f1.id, draft_order_id: f1.external_reference, organization_id: org.id}

      assert_enqueued(
        worker: Rms.Integrations.Shopify.MarkDraftOrderAsCompletedWorker,
        args: default_arg,
        prefix: "public"
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.UpdateOrderNameWorker,
        args: default_arg,
        prefix: "public"
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.MoveOrderWorker,
        args: default_arg,
        prefix: "public"
      )
    end
  end
end
