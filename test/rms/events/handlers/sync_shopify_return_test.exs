defmodule Rms.Events.Handlers.SyncShopifyReturnTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  alias Rms.Events.Handlers.SyncShopifyReturn

  describe "process/1" do
    setup do
      # Create common test data
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      staff = insert(:staff, organization: organization)
      order = insert(:order, organization: organization)
      product = build(:product, organization: organization)
      product_variant = insert(:product_variant, organization: organization, product: product)

      # Create a completed reverse fulfillment
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed"
        )

      # Enable feature flag by default
      {:ok, flag} = :ldclient_testdata.flag("shopify-return-sync")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([true], flag))

      %{
        organization: organization,
        location: location,
        staff: staff,
        order: order,
        product_variant: product_variant,
        reverse_fulfillment: reverse_fulfillment
      }
    end

    test "starts the Shopify return workflow when a reverse fulfillment is completed with a single fulfillment",
         %{
           organization: organization,
           location: location,
           order: order,
           product_variant: product_variant,
           reverse_fulfillment: reverse_fulfillment
         } do
      # Create a fulfillment
      fulfillment =
        insert(:fulfillment,
          organization: organization,
          order: order,
          ecommerce: "shopify"
        )

      # Create line items
      line_item =
        insert(:line_item,
          organization: organization,
          fulfillment: fulfillment,
          product_variant: product_variant,
          location: location
        )

      # Create reverse fulfillment line item
      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item
      )

      # Process the event
      args = %{
        "event_name" => "reverse_fulfillment.completed",
        "resource" => %{
          "id" => reverse_fulfillment.id,
          "organization_id" => organization.id
        }
      }

      assert {:ok, jobs} = perform_job(SyncShopifyReturn, args)

      assert length(jobs) == 3
      # Assert the specific workflow steps are enqueued
      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker,
        args: %{fulfillment_id: fulfillment.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker,
        args: %{fulfillment_id: fulfillment.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker,
        args: %{fulfillment_id: fulfillment.id}
      )
    end

    test "does not start the workflow when feature flag is disabled",
         %{
           organization: organization,
           location: location,
           order: order,
           product_variant: product_variant,
           reverse_fulfillment: reverse_fulfillment
         } do
      # Disable the feature flag
      {:ok, flag} = :ldclient_testdata.flag("shopify-return-sync")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([false], flag))

      # Create a fulfillment
      fulfillment =
        insert(:fulfillment,
          organization: organization,
          order: order,
          ecommerce: "shopify"
        )

      # Create line items
      line_item =
        insert(:line_item,
          organization: organization,
          fulfillment: fulfillment,
          product_variant: product_variant,
          location: location
        )

      # Create reverse fulfillment line item
      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item
      )

      # Process the event
      args = %{
        "event_name" => "reverse_fulfillment.completed",
        "resource" => %{
          "id" => reverse_fulfillment.id,
          "organization_id" => organization.id
        }
      }

      assert :ok = perform_job(SyncShopifyReturn, args)

      # No workflow steps should be enqueued
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker)
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker)
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker)
    end

    test "starts multiple Shopify return workflows when a reverse fulfillment contains items from multiple fulfillments",
         %{
           organization: organization,
           order: order,
           reverse_fulfillment: reverse_fulfillment,
           product_variant: product_variant,
           location: location
         } do
      # Create two fulfillments
      fulfillment1 =
        insert(:fulfillment,
          organization: organization,
          order: order,
          ecommerce: "shopify"
        )

      fulfillment2 =
        insert(:fulfillment,
          organization: organization,
          order: order,
          ecommerce: "shopify"
        )

      # Create line items for each fulfillment
      line_item1 =
        insert(:line_item,
          organization: organization,
          fulfillment: fulfillment1,
          product_variant: product_variant,
          location: location
        )

      line_item2 =
        insert(:line_item,
          organization: organization,
          fulfillment: fulfillment2,
          product_variant: product_variant,
          location: location
        )

      # Create reverse fulfillment line items for both fulfillments
      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item1
      )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item2
      )

      # Process the event
      args = %{
        "event_name" => "reverse_fulfillment.completed",
        "resource" => %{
          "id" => reverse_fulfillment.id,
          "organization_id" => organization.id
        }
      }

      assert {:ok, jobs} = perform_job(SyncShopifyReturn, args)

      # Should create two workflows (one for each fulfillment) with 3 steps each
      assert length(jobs) == 6

      # Assert workflow steps for fulfillment1
      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker,
        args: %{fulfillment_id: fulfillment1.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker,
        args: %{fulfillment_id: fulfillment1.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker,
        args: %{fulfillment_id: fulfillment1.id}
      )

      # Assert workflow steps for fulfillment2
      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker,
        args: %{fulfillment_id: fulfillment2.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker,
        args: %{fulfillment_id: fulfillment2.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker,
        args: %{fulfillment_id: fulfillment2.id}
      )
    end

    test "only creates workflows for Shopify fulfillments", %{
      organization: organization,
      location: location,
      order: order,
      reverse_fulfillment: reverse_fulfillment,
      product_variant: product_variant
    } do
      # Create a Shopify fulfillment
      shopify_fulfillment =
        insert(:fulfillment,
          organization: organization,
          order: order,
          ecommerce: "shopify"
        )

      # Create a non-Shopify fulfillment
      other_fulfillment =
        insert(:fulfillment,
          organization: organization,
          order: order,
          # Not shopify
          ecommerce: "vtex"
        )

      # Create line items for each fulfillment
      shopify_line_item =
        insert(:line_item,
          organization: organization,
          fulfillment: shopify_fulfillment,
          product_variant: product_variant,
          location: location
        )

      other_line_item =
        insert(:line_item,
          organization: organization,
          fulfillment: other_fulfillment,
          product_variant: product_variant,
          location: location
        )

      # Create reverse fulfillment line items for both fulfillments
      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: shopify_line_item
      )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        reverse_fulfillment: reverse_fulfillment,
        line_item: other_line_item
      )

      # Process the event
      args = %{
        "event_name" => "reverse_fulfillment.completed",
        "resource" => %{
          "id" => reverse_fulfillment.id,
          "organization_id" => organization.id
        }
      }

      assert {:ok, jobs} = perform_job(SyncShopifyReturn, args)

      # Should only create one workflow for the Shopify fulfillment (3 steps)
      assert length(jobs) == 3

      # Assert workflow steps for Shopify fulfillment
      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker,
        args: %{fulfillment_id: shopify_fulfillment.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker,
        args: %{fulfillment_id: shopify_fulfillment.id}
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker,
        args: %{fulfillment_id: shopify_fulfillment.id}
      )

      # Make sure no workflow was created for the other fulfillment
      refute_enqueued(
        worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker,
        args: %{fulfillment_id: other_fulfillment.id}
      )
    end

    test "handles error when reverse fulfillment is not found" do
      # Process the event with a non-existent reverse fulfillment ID
      args = %{
        "event_name" => "reverse_fulfillment.completed",
        "resource" => %{
          "id" => Ecto.UUID.generate(),
          "organization_id" => 999_999
        }
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(SyncShopifyReturn, args)
      end

      # No workflow steps should be enqueued
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.CreateReturnWorker)
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.DisposeItemsWorker)
      refute_enqueued(worker: Rms.Integrations.Shopify.Returns.CloseReturnWorker)
    end
  end
end
