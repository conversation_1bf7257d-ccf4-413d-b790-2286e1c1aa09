defmodule Rms.Events.Handlers.UpdateEcommecrOrderTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Events.Handlers.UpdateEcommcerOrder
  alias Rms.Fiscal

  alias Rms.Integrations.Shopify

  import Mox

  import Rms.Factory

  setup :verify_on_exit!

  setup do
    Mox.stub_with(VincoClientMock, VincoClientStub)

    :ok
  end

  describe "process/1" do
    test "correct update a shopify order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        authorized_at: DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase),
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, fn _, _ ->
        {:ok, %{"id" => 1}}
      end)

      assert {:ok, ["ecomerce order updated gid://shopify/Order/1"]} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end

    test "returns a error when do not update shopify order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        authorized_at: DateTime.now!("America/Sao_Paulo", Tz.TimeZoneDatabase),
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, fn _, _ ->
        {:error, "error"}
      end)

      assert {:error, "error"} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end

    test "do not find a valid shopify order id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/1196958417206",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, 0, fn _, _ ->
        {:ok, %{"id" => 1}}
      end)

      assert {:snooze, 20} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end

    test "do not find a valid shopify order type" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/1196958417206",
          shipping_method: "delivery"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, 0, fn _, _ ->
        {:ok, %{"id" => 1}}
      end)

      assert {:ok, []} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end

    test "do not run for other ecommerces" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "ecommerce"}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/1196958417206",
          shipping_method: "delivery"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, 0, fn _, _ ->
        {:ok, %{"id" => 1}}
      end)

      assert {:discard, "does noe update ecommerce orders after invoice issued"} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end

    test "discards if send to shopify is disabled" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      insert(:shopify_credential, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      insert(:organization_setting,
        organization: org,
        key: "send_order_to_shopify",
        value: %{data: false}
      )

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/1196958417206",
          shipping_method: "delivery"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      Mox.expect(Shopify.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :update_order, 0, fn _, _ ->
        {:ok, %{"id" => 1}}
      end)

      assert {:discard, :send_to_shopify_disabled} =
               perform_job(
                 UpdateEcommcerOrder,
                 %{
                   resource: %{
                     id: fiscal_invoice.id,
                     organization_id: fiscal_invoice.organization_id
                   }
                 }
               )
    end
  end
end
