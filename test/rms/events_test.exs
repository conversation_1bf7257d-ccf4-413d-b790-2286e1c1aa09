defmodule Rms.EventsTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo, prefix: "events"

  alias Rms.Events

  describe "emit/2" do
    test "enqueues Rms.Events.Handler with correct arguments" do
      assert {:ok, _job} = Events.emit("anything", "any other thing")

      assert_enqueued(
        worker: Rms.Events.Handler,
        args: %{"event_name" => "anything", "resource" => %{}}
      )
    end
  end
end
