defmodule Rms.Finance.CustomerTest do
  use Rms.DataCase, async: true

  alias Rms.Finance.Customer

  describe "changeset/1" do
    test "validates document and document_type" do
      invalid_cpf_attrs =
        params_for(:transaction_customer, document: "invalid cpf", document_type: "cpf")

      changeset = Customer.changeset(%Customer{}, invalid_cpf_attrs)
      refute changeset.valid?

      assert ["document (cpf) is invalid"] = errors_on(changeset).document

      invalid_cnpj_attrs =
        params_for(:transaction_customer, document: "invalid cnpj", document_type: "cnpj")

      changeset = Customer.changeset(%Customer{}, invalid_cnpj_attrs)
      refute changeset.valid?

      assert ["document (cnpj) is invalid"] = errors_on(changeset).document
    end

    test "validate customer address" do
      customer_params =
        params_for(:transaction_customer,
          document: "42816402000109",
          document_type: "cnpj",
          address: %{}
        )

      changeset = Customer.changeset(%Customer{}, customer_params)
      refute changeset.valid?

      address_changeset = changeset.changes.address

      assert %{
               state: ["can't be blank"],
               zip: ["can't be blank"],
               city_name: ["can't be blank"],
               country_name: ["can't be blank"],
               street: ["can't be blank"]
             } = errors_on(address_changeset)
    end

    test "correct validate customer address" do
      customer_params =
        params_for(:transaction_customer,
          document: "42816402000109",
          document_type: "cnpj",
          address: %{
            state: "state",
            zip: "zip",
            number: "123",
            city_name: "city_name",
            city_code: "city_code",
            country_name: "country_name",
            neighborhood: "neighborhood",
            street: "street"
          }
        )

      changeset = Customer.changeset(%Customer{}, customer_params)
      assert changeset.valid?

      address_changeset = changeset.changes.address

      assert %{} == errors_on(address_changeset)
    end
  end
end
