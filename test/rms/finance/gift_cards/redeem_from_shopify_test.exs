defmodule Rms.Finance.GiftCards.RedeemFromShopifyTest do
  use Rms.DataCase, async: true

  import Rms.Factory
  import Mox

  alias Rms.Finance.GiftCards.RedeemFromShopify

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    insert(:shopify_credential, organization: organization)

    stub(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

    {:ok,
     payment:
       insert(:payment,
         status: "pending",
         method: "gift_card",
         amount: "10.0",
         metadata: %{
           provider: "shopify",
           card_number: "123456"
         },
         organization: organization,
         transaction: build(:transaction, organization: organization)
       ),
     organization: organization}
  end

  describe "redeem/1" do
    test "successfully redeem value from gift card", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"
      debit_transaction_id = "gid://shopify/GiftCardDebitTransaction/456"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to succeed
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 debit_input ->
        assert debit_input == %{
                 debitAmount: %{
                   amount: Decimal.to_string(payment.amount),
                   currencyCode: "BRL"
                 },
                 note: "iglu_payment_id:#{payment.id}"
               }

        {:ok,
         %{
           "giftCardDebitTransaction" => %{
             "id" => debit_transaction_id,
             "amount" => %{
               "amount" => Decimal.to_string(payment.amount),
               "currencyCode" => "BRL"
             }
           }
         }}
      end)

      assert {:ok, updated_payment} = RedeemFromShopify.redeem(payment)

      assert updated_payment.id == payment.id
      assert updated_payment.status == "settled"
      assert updated_payment.metadata["provider_transaction_id"] == debit_transaction_id
    end

    test "redeem is idempotent via transactions note check", %{
      payment: payment
    } do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"
      debit_transaction_id = "gid://shopify/GiftCardDebitTransaction/456"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}",
                     "transactions" => %{
                       "nodes" => [
                         %{
                           "id" => "gid://shopify/GiftCardCreditTransaction/242747965750",
                           "note" => "iglu_payment_id:#{payment.id}"
                         },
                         %{
                           "id" => debit_transaction_id,
                           "note" => "iglu_payment_id:#{payment.id}"
                         }
                       ]
                     }
                   }
                 }
               ]
             }
           }
         }}
      end)

      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)
      assert {:ok, updated_payment} = RedeemFromShopify.redeem(payment)

      assert updated_payment.id == payment.id
      assert updated_payment.status == "settled"
      assert updated_payment.metadata["provider_transaction_id"] == debit_transaction_id
    end

    test "retries on shopify internal error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "5.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "INTERNAL_ERROR",
            "field" => ["debitInput"],
            "message" => "Internal server error"
          }}}
      end)

      assert {:error,
              {:shopify_error,
               %{
                 "code" => "INTERNAL_ERROR",
                 "field" => ["debitInput"],
                 "message" => "Internal server error"
               }}} = RedeemFromShopify.redeem(payment)

      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "pending"
    end

    test "retries on network errors", %{payment: payment} do
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, _ ->
        {:error, :timeout}
      end)

      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:error, {:unhandled_error, :timeout}} = RedeemFromShopify.redeem(payment)

      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "pending"
    end

    test "returns cancel if payment is not pending", %{payment: payment} do
      # Update payment status to something other than pending
      payment = Rms.Repo.update!(Ecto.Changeset.change(payment, %{status: "settled"}))

      # Ensure no Shopify calls are made
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)
      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:cancel, {_payment, :invalid_payment_status}} = RedeemFromShopify.redeem(payment)
    end

    test "returns cancel if payment is not a gift card payment", %{payment: payment} do
      # Update payment method to something other than gift_card
      payment = Rms.Repo.update!(Ecto.Changeset.change(payment, %{method: "credit_card"}))

      # Ensure no Shopify calls are made
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)
      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:cancel, {_payment, :invalid_payment_method}} = RedeemFromShopify.redeem(payment)
    end

    test "returns cancel if payment metadata does not contain the card number", %{
      payment: payment
    } do
      # Update payment metadata to remove the card number
      payment =
        Rms.Repo.update!(
          Ecto.Changeset.change(payment, %{
            metadata: Map.delete(payment.metadata, :card_number)
          })
        )

      # Ensure no Shopify calls are made
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)
      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:cancel, {_payment, :missing_card_number}} = RedeemFromShopify.redeem(payment)
    end

    test "returns cancel if multiple matcing gift cards are found", %{payment: payment} do
      card_number = payment.metadata[:card_number]

      # Expect fetch_gift_card to find multiple cards
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/123",
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 },
                 %{
                   "node" => %{
                     # Second card
                     "id" => "gid://shopify/GiftCard/456",
                     "balance" => %{"amount" => "25.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:cancel, {returned_payment, :multiple_gift_cards_found}} =
               RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on insufficient funds error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     # Lower balance than payment amount
                     "balance" => %{"amount" => "5.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to return insufficient funds error
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "INSUFFICIENT_FUNDS",
            "field" => ["debitInput", "debitAmount", "amount"],
            "message" => "Insufficient funds to perform this operation."
          }}}
      end)

      assert {:cancel, {returned_payment, :insufficient_funds}} =
               RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on generic debit error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to return a generic error (e.g., INVALID)
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "INVALID",
            "field" => ["debitInput"],
            "message" => "Some generic validation error occurred."
          }}}
      end)

      assert {:cancel,
              {returned_payment,
               {:shopify_error,
                %{
                  "code" => "INVALID",
                  "field" => ["debitInput"],
                  "message" => "Some generic validation error occurred."
                }}}} = RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on mismatching currency error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      # Expect fetch_gift_card to find the card with different currency
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     # Different currency
                     "balance" => %{"amount" => "50.0", "currencyCode" => "USD"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to return mismatching currency error
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "MISMATCHING_CURRENCY",
            "field" => ["debitInput", "debitAmount", "currencyCode"],
            "message" => "Currency does not match the gift card currency."
          }}}
      end)

      assert {:cancel, {returned_payment, :currency_mismatch}} = RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on negative or zero amount error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to return negative/zero amount error
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "NEGATIVE_OR_ZERO_AMOUNT",
            "field" => ["debitInput", "debitAmount", "amount"],
            "message" => "A positive amount must be used."
          }}}
      end)

      assert {:cancel, {returned_payment, :invalid_amount}} = RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on card not found error", %{payment: payment} do
      card_number = payment.metadata[:card_number]

      # Expect fetch_gift_card to not find the card (empty edges)
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               # No card found
               "edges" => []
             }
           }
         }}
      end)

      assert {:cancel, {returned_payment, :gift_card_not_found}} =
               RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end

    test "returns cancel on gift card limit exceeded error", %{payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Expect debit_gift_card to return limit exceeded error
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "GIFT_CARD_LIMIT_EXCEEDED",
            "field" => ["debitInput"],
            "message" => "Gift card limit exceeded."
          }}}
      end)

      assert {:cancel, {returned_payment, :limit_exceeded}} = RedeemFromShopify.redeem(payment)

      # Verify payment status is canceled
      assert returned_payment.status == "canceled"
      reloaded_payment = Rms.Repo.get!(Rms.Finance.Payment, payment.id)
      assert reloaded_payment.status == "canceled"
    end
  end
end
