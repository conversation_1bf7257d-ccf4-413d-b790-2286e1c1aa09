defmodule Rms.Finance.GiftCards.RedeemFromShopifyWorkerTest do
  use Oban.Pro.Testing, repo: Rms.Repo
  use Rms.DataCase, async: true

  import Mox

  alias Rms.Finance.GiftCards.RedeemFromShopifyWorker

  describe "process/1" do
    setup do
      organization = insert(:organization)

      {:ok,
       payment:
         insert(:payment,
           status: "pending",
           method: "gift_card",
           amount: "10.0",
           metadata: %{
             provider: "shopify",
             card_number: "123456"
           },
           organization: organization,
           transaction: build(:transaction, organization: organization)
         ),
       organization: organization}
    end

    test "does not enqueue multiple jobs for the same payment", %{payment: payment} do
      job =
        RedeemFromShopifyWorker.new(%{id: payment.id, organization_id: payment.organization_id})

      assert {:ok, inserted_job1} = Oban.insert(job)
      assert {:ok, inserted_job2} = Oban.insert(job)
      assert inserted_job1.id == inserted_job2.id
    end

    test "cancel payment after max attempts is reached", %{
      payment: payment,
      organization: organization
    } do
      insert(:shopify_credential, organization: organization)

      expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn _, _ ->
        {:error, :timeout}
      end)

      assert {:error, _} =
               perform_job(
                 RedeemFromShopifyWorker,
                 %{id: payment.id, organization_id: payment.organization_id},
                 max_attempts: 1
               )

      payment = Rms.Repo.reload!(payment)
      assert payment.status == "canceled"
    end
  end
end
