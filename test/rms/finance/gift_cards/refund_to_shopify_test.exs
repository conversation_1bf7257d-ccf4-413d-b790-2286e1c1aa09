defmodule Rms.Finance.GiftCards.RefundToShopifyTest do
  use Rms.DataCase, async: true

  import Rms.Factory
  import Mox

  alias Rms.Finance.GiftCards.RefundToShopify

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    insert(:shopify_credential, organization: organization)

    stub(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

    {:ok,
     pending_payment:
       insert(:payment,
         status: "pending",
         method: "gift_card",
         amount: "10.0",
         metadata: %{
           provider: "shopify",
           card_number: "123456"
         },
         organization: organization,
         transaction: build(:transaction, organization: organization)
       ),
     settled_payment:
       insert(:payment,
         status: "settled",
         method: "gift_card",
         amount: "10.0",
         metadata: %{
           provider: "shopify",
           card_number: "123456"
         },
         organization: organization,
         transaction: build(:transaction, organization: organization)
       ),
     organization: organization}
  end

  describe "refund/1" do
    test "successfully refund value from gift card", %{settled_payment: settled_payment} do
      card_number = settled_payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"
      credit_transaction_id = "gid://shopify/GiftCardCreditTransaction/456"

      # Expect fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      expect(Rms.Integrations.Shopify.Mock, :credit_gift_card, fn :mock_client,
                                                                  ^gift_card_id,
                                                                  credit_input ->
        assert credit_input == %{
                 creditAmount: %{
                   amount: Decimal.to_string(settled_payment.amount),
                   currencyCode: "BRL"
                 },
                 note: "refund_iglu_payment_id:#{settled_payment.id}"
               }

        {:ok,
         %{
           "giftCardCreditTransaction" => %{
             "id" => credit_transaction_id,
             "amount" => %{
               "amount" => Decimal.to_string(settled_payment.amount),
               "currencyCode" => "BRL"
             }
           }
         }}
      end)

      assert {:ok, updated_payment} = RefundToShopify.refund(settled_payment)

      assert updated_payment.id == settled_payment.id
      assert updated_payment.status == "canceled"
      assert updated_payment.metadata["provider_refund_transaction_id"] == credit_transaction_id
    end

    test "refund is idempotent via transactions note check", %{
      settled_payment: settled_payment
    } do
      card_number = settled_payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"
      credit_transaction_id = "gid://shopify/GiftCardCreditTransaction/456"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}",
                     "transactions" => %{
                       "nodes" => [
                         %{
                           "id" => credit_transaction_id,
                           "note" => "refund_iglu_payment_id:#{settled_payment.id}"
                         }
                       ]
                     }
                   }
                 }
               ]
             }
           }
         }}
      end)

      deny(Rms.Integrations.Shopify.Mock, :credit_gift_card, 3)
      assert {:ok, updated_payment} = RefundToShopify.refund(settled_payment)

      assert updated_payment.id == settled_payment.id
      assert updated_payment.status == "canceled"
      assert updated_payment.metadata["provider_refund_transaction_id"] == credit_transaction_id
    end

    test "returns cancel if payment is not settled or canceled", %{pending_payment: payment} do
      card_number = payment.metadata[:card_number]
      gift_card_id = "gid://shopify/GiftCard/123"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}",
                     "transactions" => %{
                       "nodes" => []
                     }
                   }
                 }
               ]
             }
           }
         }}
      end)

      deny(Rms.Integrations.Shopify.Mock, :credit_gift_card, 3)

      assert {:error, :invalid_status_for_refund} = RefundToShopify.refund(payment)
    end

    test "returns error if payment is not a gift card payment", %{settled_payment: payment} do
      # Update payment method to something other than gift_card
      payment = Rms.Repo.update!(Ecto.Changeset.change(payment, %{method: "credit_card"}))

      # Ensure no Shopify calls are made
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)
      deny(Rms.Integrations.Shopify.Mock, :credit_gift_card, 3)

      assert {:error, {_, :invalid_payment_method}} = RefundToShopify.refund(payment)
    end

    test "returns error if payment metadata does not contain the card number", %{
      settled_payment: payment
    } do
      # Update payment metadata to remove the card number
      payment =
        Rms.Repo.update!(
          Ecto.Changeset.change(payment, %{
            metadata: Map.delete(payment.metadata, :card_number)
          })
        )

      # Ensure no Shopify calls are made
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)
      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:error, {_, :missing_card_number}} = RefundToShopify.refund(payment)
    end

    test "returns error if multiple matching gift cards are found", %{settled_payment: payment} do
      card_number = payment.metadata[:card_number]

      # Expect fetch_gift_card to find multiple cards
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/123",
                     "balance" => %{"amount" => "50.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 },
                 %{
                   "node" => %{
                     # Second card
                     "id" => "gid://shopify/GiftCard/456",
                     "balance" => %{"amount" => "25.0", "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      assert {:error, :multiple_gift_cards_found} = RefundToShopify.refund(payment)
    end
  end
end
