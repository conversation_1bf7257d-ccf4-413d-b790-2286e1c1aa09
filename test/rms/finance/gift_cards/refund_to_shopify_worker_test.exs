defmodule Rms.Finance.GiftCards.RefundToShopifyWorkerTest do
  use Oban.Pro.Testing, repo: Rms.Repo
  use Rms.DataCase, async: true

  import Mox

  alias Rms.Finance.GiftCards.RefundToShopifyWorker

  describe "process/1" do
    setup do
      organization = insert(:organization)

      {:ok,
       payment:
         insert(:payment,
           status: "settled",
           method: "gift_card",
           amount: "10.0",
           metadata: %{
             provider: "shopify",
             card_number: "123456"
           },
           organization: organization,
           transaction: build(:transaction, organization: organization)
         ),
       organization: organization}
    end

    test "does not enqueue multiple jobs for the same payment", %{payment: payment} do
      job =
        RefundToShopifyWorker.new(%{id: payment.id, organization_id: payment.organization_id})

      assert {:ok, inserted_job1} = Oban.insert(job)
      assert {:ok, inserted_job2} = Oban.insert(job)
      assert inserted_job1.id == inserted_job2.id
    end

    test "cancel payment after max attempts is reached", %{
      payment: settled_payment,
      organization: organization
    } do
      insert(:shopify_credential, organization: organization)

      expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn _, _ ->
        {:error, :timeout}
      end)

      assert {:error, _} =
               perform_job(
                 RefundToShopifyWorker,
                 %{id: settled_payment.id, organization_id: organization.id},
                 max_attempts: 1
               )

      payment = Rms.Repo.reload!(settled_payment)
      assert payment.status == settled_payment.status
    end
  end
end
