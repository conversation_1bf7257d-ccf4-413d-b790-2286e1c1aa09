defmodule Rms.Finance.IgluCreditTest do
  use Rms.DataCase, async: true

  alias Rms.Finance.IgluCredit

  describe "changeset/2" do
    setup do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      reverse_fulfillment = insert(:reverse_fulfillment, organization: organization)

      valid_attrs = %{
        amount: Decimal.new("100.00"),
        used_amount: Decimal.new("0.00"),
        status: "available",
        reason: "return",
        organization_id: organization.id,
        customer_id: customer.id,
        reverse_fulfillment_id: reverse_fulfillment.id
      }

      {:ok, %{valid_attrs: valid_attrs}}
    end

    test "with valid attributes creates a valid changeset", %{valid_attrs: valid_attrs} do
      changeset =
        IgluCredit.changeset(%IgluCredit{}, valid_attrs)

      assert changeset.valid?
    end

    test "requires amount to be greater than or equal to 0", %{valid_attrs: valid_attrs} do
      attrs = Map.put(valid_attrs, :amount, Decimal.new("-1.00"))
      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)
      assert "must be greater than or equal to 0" in errors_on(changeset).amount
    end

    test "requires used_amount to be greater than or equal to 0", %{valid_attrs: valid_attrs} do
      attrs = Map.put(valid_attrs, :used_amount, Decimal.new("-1.00"))
      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)
      assert "must be greater than or equal to 0" in errors_on(changeset).used_amount
    end

    test "validates used_amount cannot exceed amount", %{valid_attrs: valid_attrs} do
      attrs =
        Map.merge(valid_attrs, %{
          amount: Decimal.new("100.00"),
          used_amount: Decimal.new("150.00")
        })

      assert {:error, changeset} = Repo.insert(IgluCredit.changeset(%IgluCredit{}, attrs))
      assert %{used_amount: ["cannot be greater than total amount"]} = errors_on(changeset)
    end

    test "allows used_amount equal to amount", %{valid_attrs: valid_attrs} do
      attrs = %{valid_attrs | used_amount: Decimal.new("100.00")}
      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)
      assert changeset.valid?
    end

    test "validates status inclusion", %{valid_attrs: valid_attrs} do
      attrs = Map.put(valid_attrs, :status, "invalid")
      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)
      assert "is invalid" in errors_on(changeset).status
    end

    test "validates reason inclusion", %{valid_attrs: valid_attrs} do
      attrs = Map.put(valid_attrs, :reason, "invalid")
      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)
      assert "is invalid" in errors_on(changeset).reason
    end

    test "validates expires_at must be in the future when present" do
      attrs = %{
        amount: Decimal.new("100"),
        reason: "return",
        status: "available",
        expires_at: DateTime.utc_now() |> DateTime.add(-1, :day),
        organization_id: 1
      }

      changeset = IgluCredit.changeset(%IgluCredit{}, attrs)

      refute changeset.valid?
      assert "must be in the future (utc timezone)" in errors_on(changeset).expires_at
    end

    test "allows expires_at to be nil" do
      attrs = %{
        amount: Decimal.new("100"),
        reason: "return",
        status: "available",
        reverse_fulfillment_id: "1"
      }

      changeset = IgluCredit.changeset(%IgluCredit{organization_id: 1}, attrs)

      assert changeset.valid?
    end

    test "creates valid changeset with all fields" do
      attrs = %{
        amount: Decimal.new("100"),
        reason: "return",
        status: "used",
        expires_at: DateTime.utc_now() |> DateTime.add(30, :day),
        customer_id: 1,
        reverse_fulfillment_id: "1",
        metadata: %{note: "test credit"}
      }

      changeset = IgluCredit.changeset(%IgluCredit{organization_id: 1}, attrs)

      assert changeset.valid?
      assert get_change(changeset, :amount) == Decimal.new("100")
      assert get_change(changeset, :reason) == "return"
      assert get_change(changeset, :status) == "used"
      assert get_change(changeset, :metadata) == %{note: "test credit"}
    end
  end
end
