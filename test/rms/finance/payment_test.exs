defmodule Rms.Finance.PaymentTest do
  use Rms.DataCase, async: true

  alias Rms.Finance.Payment

  describe "changeset/2" do
    test "settled credit card payment requires nsu and aut" do
      attrs = params_for(:payment, status: "settled")

      assert %Ecto.Changeset{valid?: false} = changeset = Payment.changeset(%Payment{}, attrs)

      assert %{
               "metadata.nsu": [
                 "nsu can't be nil for settled payments."
               ],
               "metadata.aut": [
                 "aut can't be nil for settled payments."
               ]
             } = errors_on(changeset)
    end

    test "settled payment of methods other than credit card does not require nsu and aut" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      attrs =
        params_with_assocs(:payment,
          status: "settled",
          method: "payment_link",
          transaction: transaction
        )

      assert %Ecto.Changeset{valid?: true} = Payment.changeset(%Payment{}, attrs)
    end

    test "status can only be 'authorized' if method is credit_card or payment_link" do
      methods = ["bank_slip", "pix", "debit_card"]

      for method <- methods do
        attrs = params_for(:payment, status: "authorized", method: method)
        assert %Ecto.Changeset{valid?: false} = changeset = Payment.changeset(%Payment{}, attrs)

        assert %{
                 status: [
                   "status can only be 'authorized' if method is credit_card, payment_link or gift_card"
                 ]
               } = errors_on(changeset)
      end
    end

    test "payment method cash will change status to settled" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      attrs =
        params_with_assocs(:payment,
          status: "settled",
          method: "payment_link",
          transaction: transaction
        )

      assert {:ok, payment} = %Payment{} |> Payment.changeset(attrs) |> apply_action(:insert)
      assert payment.status == "settled"
    end

    test "invalid status transaction" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      attrs =
        params_with_assocs(:payment,
          status: "settled",
          method: "payment_link",
          transaction: transaction
        )

      assert {:ok, payment} = %Payment{} |> Payment.changeset(attrs) |> apply_action(:insert)

      invalid_status = ["pending"]

      for status <- invalid_status do
        changeset = Payment.changeset(payment, %{status: status})
        errors = errors_on(changeset)

        assert Enum.any?(errors.status, fn error ->
                 String.contains?(error, "status transition not allowed")
               end)
      end
    end

    test "gift_card payments requires provider to be set" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      attrs =
        params_with_assocs(:payment,
          method: "gift_card",
          transaction: transaction
        )

      assert {:error, changeset} =
               %Payment{}
               |> Payment.changeset(attrs)
               |> apply_action(:insert)

      assert "provider can't be nil for gift card." in errors_on(changeset)[:"metadata.provider"]
    end
  end
end
