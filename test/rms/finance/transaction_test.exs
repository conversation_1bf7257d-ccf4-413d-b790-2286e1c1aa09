defmodule Rms.Finance.TransactionTest do
  use Rms.DataCase, async: true

  alias Rms.Finance.Transaction

  describe "changeset/2" do
    test "cast associated customer" do
      cpf = Brcpfcnpj.cpf_generate()

      attrs = %{
        customer: %{name: "<PERSON>", document_type: "cpf", document: cpf},
        payments: [%{method: "credit_card", amount: "10.0", metadata: %{aut: "0", nsu: "0"}}],
        order_id: 5
      }

      changeset = Transaction.changeset(%Transaction{}, attrs)

      assert {:ok, transaction} = apply_action(changeset, :insert)

      # Assert customer creation
      assert transaction.customer.name == "<PERSON>"
      assert transaction.customer.document_type == "cpf"
      assert transaction.customer.document == cpf

      # Assert payment creation
      assert [payment] = transaction.payments
      assert payment.method == "credit_card"
      assert Decimal.equal?(payment.amount, Decimal.new("10.0"))
      assert payment.metadata == %{aut: "0", nsu: "0"}
    end

    test "cast associated customer with address" do
      cpf = Brcpfcnpj.cpf_generate()

      attrs = %{
        customer: %{
          name: "<PERSON>",
          document_type: "cpf",
          document: cpf,
          address: %{
            state: "state",
            zip: "zip",
            number: "123",
            city_name: "city_name",
            city_code: "city_code",
            country_name: "country_name",
            neighborhood: "neighborhood",
            street: "street"
          }
        },
        payments: [%{method: "credit_card", amount: "10.0", metadata: %{aut: "0", nsu: "0"}}],
        order_id: 5
      }

      changeset = Transaction.changeset(%Transaction{}, attrs)

      assert {:ok, transaction} = apply_action(changeset, :insert)

      # Assert customer creation
      assert transaction.customer.name == "John Doe"
      assert transaction.customer.document_type == "cpf"
      assert transaction.customer.document == cpf

      assert transaction.customer.address == %Rms.Finance.Customer.CustomerAddress{
               state: "state",
               zip: "zip",
               number: "123",
               city_code: "city_code",
               city_name: "city_name",
               country_name: "country_name",
               neighborhood: "neighborhood",
               street: "street",
               complement: nil,
               id: nil
             }

      # Assert payment creation
      assert [payment] = transaction.payments
      assert payment.method == "credit_card"
      assert Decimal.equal?(payment.amount, Decimal.new("10.0"))
      assert payment.metadata == %{aut: "0", nsu: "0"}
    end
  end

  describe "transaction_done?/1" do
    test "returns false when settled payments amount is different from order amount" do
      org = insert(:organization)
      order = insert(:order, total_price: "100", organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:payment,
        method: "payment_link",
        amount: 1,
        status: "pending",
        transaction: transaction,
        organization: org
      )

      insert(:payment,
        method: "payment_link",
        amount: 99,
        status: "settled",
        transaction: transaction,
        organization: org
      )

      assert false ==
               Transaction.done?(Repo.preload(transaction, [:payments, :order]))
    end

    test "returns true when settled payments amount is the same as order amount" do
      org = insert(:organization)
      order = insert(:order, total_price: "100", organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:payment,
        method: "payment_link",
        amount: 1,
        status: "settled",
        transaction: transaction,
        organization: org
      )

      insert(:payment,
        method: "payment_link",
        amount: 99,
        status: "settled",
        transaction: transaction,
        organization: org
      )

      assert true ==
               Transaction.done?(Repo.preload(transaction, [:payments, :order]))
    end
  end
end
