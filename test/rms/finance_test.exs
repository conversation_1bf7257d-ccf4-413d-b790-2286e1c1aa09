defmodule Rms.FinanceTest do
  use Rms.DataCase
  use Rms.EventsCase

  import Mox

  alias Rms.Repo
  alias Rms.Finance
  alias Rms.Finance.Payment
  alias Rms.Finance.Transaction

  alias Rms.Commerce
  alias Rms.Commerce.Orders.Order

  setup :verify_on_exit!

  describe "create_transaction/1" do
    test "can create a transaction without customer and payment" do
      order = insert(:order)
      attrs = params_with_assocs(:transaction, order: order)
      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      transaction = Repo.preload(transaction, [:customer, :payments])
      refute transaction.customer
      assert Enum.empty?(transaction.payments)
    end

    test "can create a transaction without customer and with a single payment" do
      order = insert(:order)
      attrs = params_with_assocs(:transaction, payments: [params_for(:payment)], order: order)
      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      transaction = Repo.preload(transaction, [:customer, :payments])
      refute Enum.empty?(transaction.payments)
      refute transaction.customer
    end

    test "can create a transaction with a customer and no payments" do
      order = insert(:order)

      attrs =
        params_with_assocs(:transaction,
          customer: build(:transaction_customer),
          payments: [],
          order: order
        )

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      transaction = Repo.preload(transaction, [:customer, :payments])
      assert transaction.customer
      assert Enum.empty?(transaction.payments)
    end

    test "can create a transaction with multiple payments" do
      order = insert(:order)

      attrs =
        params_with_assocs(:transaction,
          payments: [
            params_for(:payment),
            params_for(:payment)
          ],
          order: order
        )

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      transaction = Repo.preload(transaction, [:customer, :payments])
      assert length(transaction.payments) == 2
    end

    test "can't have multiple done transactions for a single order" do
      order = insert(:order)

      attrs =
        params_with_assocs(:transaction,
          status: "done",
          order: order,
          payments: [
            build(:payment, method: "cash", amount: order.total_price, status: "settled")
          ]
        )

      assert {:ok, _} = Finance.create_transaction(order.organization_id, attrs)
      assert {:error, changeset} = Finance.create_transaction(order.organization_id, attrs)

      assert errors_on(changeset).order_id == ["already has a open transaction"]

      transactions = Repo.all(Finance.Transaction)
      assert length(transactions) == 1
    end

    test "can have multiple canceled transactions for a single order" do
      order = insert(:order)
      attrs = params_with_assocs(:transaction, status: "canceled", order: order)

      assert {:ok, _} = Finance.create_transaction(order.organization_id, attrs)
      assert {:ok, _} = Finance.create_transaction(order.organization_id, attrs)

      transactions = Repo.all(from t in Finance.Transaction, where: t.order_id == ^order.id)
      assert Enum.all?(transactions, &(&1.status == "canceled"))
      assert length(transactions) == 2
    end

    test "cannot have more than one open transaction for a single order" do
      order = insert(:order)
      attrs = params_with_assocs(:transaction, status: "open", order: order)

      assert {:ok, _} = Finance.create_transaction(order.organization_id, attrs)
      assert {:error, _} = Finance.create_transaction(order.organization_id, attrs)

      transactions =
        Repo.all(
          from t in Finance.Transaction, where: t.order_id == ^order.id and t.status == "open"
        )

      assert length(transactions) == 1
    end

    test "transaction is set to done if settled payments sum equals order total amount" do
      order = insert(:order)
      payment_total = order.total_price

      payments = [
        build(:payment, amount: Decimal.div(payment_total, 2), method: "cash", status: "settled"),
        build(:payment, amount: Decimal.div(payment_total, 2), method: "cash", status: "settled")
      ]

      attrs = params_with_assocs(:transaction, order: order, payments: payments)

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)

      assert transaction.status == "done"
    end

    test "transaction is not set to done if payments sum other than settled equals order total amount" do
      order = insert(:order)
      payment_total = order.total_price

      payments = [
        build(:payment,
          amount: Decimal.div(payment_total, 2),
          method: "pix",
          status: "pending"
        ),
        build(:payment,
          amount: Decimal.div(payment_total, 2),
          method: "payment_link",
          status: "pending"
        )
      ]

      attrs = params_with_assocs(:transaction, order: order, payments: payments)

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      assert transaction.status != "done"
    end

    test "canceled payments does not move transaction to done" do
      order = insert(:order, total_price: 100)

      payments = [
        build(:payment, amount: 50, status: "canceled"),
        build(:payment, amount: 50, status: "canceled")
      ]

      attrs = params_with_assocs(:transaction, order: order, payments: payments, status: "open")

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)
      assert transaction.status == "open"
    end

    test "transaction is set to open if payments sum is less than order total amount" do
      order = insert(:order)
      payment_total = order.total_price
      payment_partial = Decimal.div(payment_total, 3)

      payments = [
        build(:payment, amount: payment_partial),
        build(:payment, amount: payment_partial)
      ]

      attrs = params_with_assocs(:transaction, order: order, payments: payments)

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, attrs)

      assert transaction.status == "open"
    end

    test "returns an error if payment total is higher than order total amount" do
      order = insert(:order)
      payment_total = Decimal.add(order.total_price, Decimal.new(1))

      payments = [
        build(:payment, amount: payment_total)
      ]

      attrs = params_with_assocs(:transaction, order: order, payments: payments)

      assert {:error, _} = Finance.create_transaction(order.organization_id, attrs)
    end

    test "canceled payments can exceed order total" do
      order = insert(:order, total_price: 100)

      payments = [
        build(:payment,
          amount: 150,
          status: "canceled"
        )
      ]

      transaction =
        params_with_assocs(:transaction,
          order: order,
          payments: payments,
          status: "open"
        )

      assert {:ok, transaction} = Finance.create_transaction(order.organization_id, transaction)
      assert transaction.status == "open"
    end

    test "done transaction moves the order status to 'paid' automatically" do
      order = insert(:order, status: "open")
      payment_total = order.total_price

      payments = [
        build(:payment, amount: payment_total, method: "cash", status: "settled")
      ]

      attrs = params_with_assocs(:transaction, status: "done", order: order, payments: payments)

      assert {:ok, _transaction} = Finance.create_transaction(order.organization_id, attrs)

      updated_order = Repo.get(Rms.Commerce.Orders.Order, order.id)

      assert updated_order.status == "paid"
    end
  end

  describe "get_transaction!/2" do
    test "returns transaction when it exists" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      transaction = Repo.preload(transaction, [:order, :organization])
      result = Finance.get_transaction!(organization.id, transaction.id)

      assert result.id == transaction.id
      assert result.status == transaction.status
      assert result.order_id == transaction.order_id
      assert result.organization_id == transaction.organization_id
    end

    test "raises Ecto.NoResultsError if transaction does not exist" do
      organization = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Finance.get_transaction!(organization.id, -1)
      end
    end

    test "payments are ordered by inserted_at in ascending order" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)

      transaction = insert(:transaction, order: order, organization: organization)

      payment1 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-01T10:00:00Z],
          organization: organization
        )

      payment2 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-02T11:00:00Z],
          organization: organization
        )

      payment3 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-03T12:00:00Z],
          organization: organization
        )

      fetched_transaction =
        Finance.get_transaction!(organization.id, transaction.id, preloads: [:payments])

      assert Enum.map(fetched_transaction.payments, & &1.id) ==
               Enum.map([payment1, payment2, payment3], & &1.id)

      assert Enum.map(fetched_transaction.payments, & &1.inserted_at) ==
               Enum.map([payment1, payment2, payment3], & &1.inserted_at)
    end

    test "payments are ordered correctly even if inserted out of order" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)

      transaction = insert(:transaction, order: order, organization: organization)

      payment1 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-03T12:00:00Z],
          organization: organization
        )

      payment2 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-01T10:00:00Z],
          organization: organization
        )

      payment3 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-02T11:00:00Z],
          organization: organization
        )

      fetched_transaction =
        Finance.get_transaction!(organization.id, transaction.id, preloads: [:payments])

      assert Enum.map(fetched_transaction.payments, & &1.id) ==
               Enum.map([payment2, payment3, payment1], & &1.id)

      assert Enum.map(fetched_transaction.payments, & &1.inserted_at) ==
               Enum.map([payment2, payment3, payment1], & &1.inserted_at)
    end

    test "payments are ordered correctly when fetched with other preloads" do
      organization = insert(:organization)

      customer = insert(:customer, organization: organization)

      order = insert(:order, organization: organization, customer: customer)

      transaction = insert(:transaction, order: order, organization: organization)

      payment1 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-01T10:00:00Z],
          organization: organization
        )

      payment2 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-02T11:00:00Z],
          organization: organization
        )

      payment3 =
        insert(:payment,
          transaction: transaction,
          inserted_at: ~U[2023-10-03T12:00:00Z],
          organization: organization
        )

      fetched_transaction =
        Finance.get_transaction!(organization.id, transaction.id,
          preloads: [:payments, order: :customer]
        )

      assert Enum.map(fetched_transaction.payments, & &1.id) ==
               Enum.map([payment1, payment2, payment3], & &1.id)

      assert Enum.map(fetched_transaction.payments, & &1.inserted_at) ==
               Enum.map([payment1, payment2, payment3], & &1.inserted_at)

      assert fetched_transaction.order.customer.id == customer.id
    end
  end

  describe "close_transaction/1" do
    test "moves status to done" do
      org = insert(:organization)
      order = insert(:order, status: "open", organization: org)
      transaction = insert(:transaction, status: "open", order: order, organization: org)

      insert(:payment,
        amount: transaction.order.total_price,
        transaction: transaction,
        method: "cash",
        status: "settled",
        organization: org
      )

      assert {:ok, transaction} = Finance.close_transaction(transaction)
      assert transaction.status == "done"
    end

    test "close transaction with addon to create subscription" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      frequency = "12 month"

      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "prime",
            "content" => %{"vtex.subscription.key.frequency" => "12 month"}
          }
        )

      customer = insert(:customer, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: addon.price,
        customer_id: customer.id,
        line_items: [],
        addons: [
          %{
            quantity: 1,
            name: "Prime",
            price: addon.price,
            list_price: addon.price,
            type: "subscription",
            image_url: "url",
            addon_id: addon.id,
            metadata: addon.metadata,
            description: addon.description
          }
        ],
        location_id: loc.id
      }

      assert {:ok, order} =
               Rms.Commerce.Orders.create_order(org.id, attrs, allowed_location_ids: :all)

      transaction = insert(:transaction, status: "open", order: order, organization: org)

      insert(:payment,
        amount: transaction.order.total_price_with_addons,
        transaction: transaction,
        method: "credit_card",
        status: "settled",
        organization: org
      )

      assert {:ok, transaction} = Finance.close_transaction(transaction)
      assert transaction.status == "done"

      subscriptions = Rms.Repo.all(Rms.Finance.Subscription)

      assert length(subscriptions) == 1
      subscription = hd(subscriptions)
      assert subscription.frequency == frequency
      assert subscription.value == addon.price
      assert subscription.customer_id == customer.id
    end

    test "returns error if settled payment does not sum to order total amount" do
      org = insert(:organization)
      order = insert(:order, status: "open", organization: org)
      transaction = insert(:transaction, status: "open", order: order, organization: org)
      order_total = transaction.order.total_price
      # Less than the total amount
      payment_amount = Decimal.sub(order_total, Decimal.new(1))

      insert(:payment,
        amount: payment_amount,
        transaction: transaction,
        method: "cash",
        status: "settled",
        organization: org
      )

      assert {:error, changeset} = Finance.close_transaction(transaction)

      assert errors_on(changeset).status == [
               "sum of settled payments is not enough to close transaction"
             ]
    end

    test "canceled payment does not count towards the total payments" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price: 100)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:payment,
        amount: 50,
        status: "settled",
        transaction: transaction,
        organization: org
      )

      insert(:payment,
        amount: 50,
        status: "canceled",
        transaction: transaction,
        organization: org
      )

      assert {:error, changeset} = Finance.close_transaction(transaction)
      transaction = Repo.reload!(transaction, force: true)
      assert transaction.status == "open"

      assert "sum of settled payments is not enough to close transaction" in errors_on(changeset).status
    end
  end

  describe "cancel_transaction/1" do
    test "successfully cancels an open transaction" do
      organization = insert(:organization)
      order = insert(:order, status: "open", organization: organization)
      transaction = insert(:transaction, status: "open", order: order, organization: organization)

      assert {:ok, canceled_transaction} = Finance.cancel_transaction(transaction)

      assert canceled_transaction.status == "canceled"
    end

    test "returns an error when trying to cancel a done transaction" do
      organization = insert(:organization)
      order = insert(:order, status: "open", organization: organization)
      transaction = insert(:transaction, status: "done", order: order, organization: organization)

      assert {:error, changeset} = Finance.cancel_transaction(transaction)
      assert errors_on(changeset).status == ["status transition not allowed"]
    end

    test "cancel payments associated with transaction" do
      organization = insert(:organization)
      order = insert(:order, status: "open", organization: organization)
      transaction = insert(:transaction, status: "open", order: order, organization: organization)
      insert(:payment, transaction: transaction, status: "authorized", organization: organization)
      insert(:payment, transaction: transaction, status: "authorized", organization: organization)

      assert {:ok, _canceled_transaction} = Finance.cancel_transaction(transaction)

      payments = Repo.all(from p in Payment, where: p.transaction_id == ^transaction.id)

      assert Enum.all?(payments, &(&1.status == "canceled"))
    end

    # test "returns an error if there's non cancellable payments within transaction" do
    #   organization = insert(:organization)
    #   order = insert(:order, status: "open", organization: organization)
    #   transaction = insert(:transaction, status: "open", order: order, organization: organization)
    #   insert(:payment, transaction: transaction, status: "settled", organization: organization)
    #   assert {:error, changeset} = Finance.cancel_transaction(transaction)
    #   assert [payment_changeset_errors] = errors_on(changeset).payments
    #   assert "status transition not allowed" in payment_changeset_errors.status
    # end
  end

  describe "create_payment/2" do
    test "creating payment to a transaction that's not open returns an error" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, status: "done", order: order, organization: org)
      payment_attrs = params_for(:payment, transaction_id: transaction.id, organization: org)

      assert {:error, changeset} = Finance.create_payment(org.id, payment_attrs)

      assert errors_on(changeset).transaction_id == [
               "can't create payment for a done transaction"
             ]
    end

    test "creating payment with card token" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      transaction = insert(:transaction, status: "open", order: order, organization: org)

      payment_attrs = %{
        "status" => "pending",
        "metadata" => %{},
        "organization_id" => org.id,
        "transaction_id" => transaction.id,
        "method" => "credit_card",
        "amount" => "10.00",
        "card_token" => "card_token"
      }

      assert {:ok, _} = Finance.create_payment(org.id, payment_attrs)
      assert [_] = Rms.Repo.all(Rms.Finance.CardToken)
    end

    test "create payment with gift card" do
      org = insert(:organization)
      order = insert(:order, organization: org, location: build(:location, organization: org))
      transaction = insert(:transaction, status: "open", order: order, organization: org)
      insert(:gift_promo_credential, organization: org, location: order.location)

      payment_attrs =
        string_params_for(:payment,
          transaction: transaction,
          organization: org,
          amount: "1.00",
          method: "gift_card",
          metadata: %{
            "provider" => "gift_promo",
            "card_number" => "6367030104262382",
            "card_pin" => "4357"
          }
        )

      Mox.expect(Rms.Integrations.GiftPromo.Mock, :client, fn _ -> :mock_client end)

      Mox.expect(Rms.Integrations.GiftPromo.Mock, :check_gift_card, fn :mock_client, _ ->
        {:ok,
         %{
           "sucesso" => "1",
           "cartaoStatus" => "Ativo",
           "cartaoSaldo" => "29,10"
         }}
      end)

      Mox.expect(Rms.Integrations.GiftPromo.Mock, :redeem_gift_card, fn :mock_client, _ ->
        {:ok,
         %{
           "codigoErro" => "0",
           "mensagem" => "Transação OK",
           "sucesso" => "1",
           "nsuHost" => "123123",
           "autorizacao" => "123123"
         }}
      end)

      assert {:ok, %{status: "settled"}} = Finance.create_payment(org.id, payment_attrs)
    end
  end

  describe "cancel_payment/2" do
    test "successfully cancels a payment with authorized status" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment, transaction: transaction, status: "authorized", organization: org)

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment)
      assert_emit("payment.canceled", Payment.event_payload(canceled_payment))

      assert canceled_payment.status == "canceled"
    end

    test "successfully cancels a payment with return_credit method" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      location = build(:location, organization: org)

      product = insert(:product, organization: org)
      variant = insert(:product_variant, product: product, organization: org)

      line_item =
        build(:line_item,
          organization: org,
          product_variant: variant,
          location: location
        )

      # Create reverse fulfillment and credit
      fulfillment =
        insert(:reverse_fulfillment,
          organization: org,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: org,
              line_item: line_item,
              returned_quantity: 1
            )
          ]
        )

      credit =
        insert(:iglu_credit,
          organization: org,
          customer: customer,
          amount: "100",
          status: "available",
          reverse_fulfillment: fulfillment
        )

      # Create payment using the credit
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          organization: org,
          transaction: transaction,
          method: "return_credit",
          status: "settled",
          metadata: %{"credit_id" => credit.id}
        )

      insert(:iglu_credit_payment, organization: org, iglu_credit: credit, payment: payment)

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment)
      assert canceled_payment.status == "canceled"

      # Verify reverse fulfillment was canceled
      fulfillment = Repo.reload!(fulfillment)
      assert fulfillment.status == "cancelled"

      # Verify credit was canceled
      credit = Repo.reload!(credit)
      assert credit.status == "cancelled"
    end

    test "successfully cancels a payment with receipt metadata" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)

      receipt_data = %{
        "customer_refund_receipt" => "customer-receipt-123",
        "store_refund_receipt" => "store-receipt-456"
      }

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment, receipt_data)
      assert canceled_payment.status == "canceled"
      assert canceled_payment.metadata["customer_refund_receipt"] == "customer-receipt-123"
      assert canceled_payment.metadata["store_refund_receipt"] == "store-receipt-456"
      assert_emit("payment.canceled", Payment.event_payload(canceled_payment))
    end

    test "successfully cancels a payment without receipt metadata" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment)
      assert canceled_payment.status == "canceled"
      refute canceled_payment.metadata["customer_refund_receipt"]
      refute canceled_payment.metadata["store_refund_receipt"]
      assert_emit("payment.canceled", Payment.event_payload(canceled_payment))
    end

    test "preserves existing metadata when canceling payment" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      existing_metadata = %{"existing_key" => "existing_value"}

      payment =
        insert(:payment, transaction: transaction, organization: org, metadata: existing_metadata)

      receipt_data = %{
        "customer_refund_receipt" => "customer-receipt-123",
        "store_refund_receipt" => "store-receipt-456"
      }

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment, receipt_data)
      assert canceled_payment.status == "canceled"
      assert canceled_payment.metadata["existing_key"] == "existing_value"
      assert canceled_payment.metadata["customer_refund_receipt"] == "customer-receipt-123"
      assert canceled_payment.metadata["store_refund_receipt"] == "store-receipt-456"
      assert_emit("payment.canceled", Payment.event_payload(canceled_payment))
    end

    test "handles nil metadata correctly" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org, metadata: nil)

      receipt_data = %{
        "customer_refund_receipt" => "customer-receipt-123",
        "store_refund_receipt" => "store-receipt-456"
      }

      assert {:ok, canceled_payment} = Finance.cancel_payment(payment, receipt_data)
      assert canceled_payment.status == "canceled"
      assert canceled_payment.metadata["customer_refund_receipt"] == "customer-receipt-123"
      assert canceled_payment.metadata["store_refund_receipt"] == "store-receipt-456"
      assert_emit("payment.canceled", Payment.event_payload(canceled_payment))
    end
  end

  describe "find_payment_by_external_reference/2" do
    test "find a payment by external_id and partner" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)
      epr = insert(:external_payment_reference, payment: payment, organization: org)

      {:ok, %Payment{}} = Finance.get_payment_by_external_reference(epr.partner, epr.external_id)
    end

    test "does not find a payment by external_id and partner" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)
      epr = insert(:external_payment_reference, payment: payment, organization: org)

      {:error, :not_found} = Finance.get_payment_by_external_reference(epr.partner, "partner")
    end
  end

  describe "get_payment_by_external_reference_and_organization/3" do
    test "finds a payment by external_id, partner, and organization_id" do
      org1 = insert(:organization)
      # Another org for isolation testing
      org2 = insert(:organization)

      payment_org1 = insert(:payment, organization: org1)

      _epr_org1 =
        insert(:external_payment_reference,
          payment: payment_org1,
          partner: "pagarme",
          external_id: "ext123",
          organization: org1
        )

      # Create a similar payment in another org to ensure we don't fetch it
      payment_org2 = insert(:payment, organization: org2)

      _epr_org2 =
        insert(:external_payment_reference,
          payment: payment_org2,
          partner: "pagarme",
          external_id: "ext123",
          organization: org2
        )

      assert {:ok, %Payment{id: payment_id}} =
               Finance.get_payment_by_external_reference_and_organization(
                 org1.id,
                 "pagarme",
                 "ext123"
               )

      assert payment_id == payment_org1.id
    end

    test "does not find a payment if external_id matches but organization_id does not" do
      org1 = insert(:organization)
      org2 = insert(:organization)

      payment_org1 = insert(:payment, organization: org1)

      _epr_org1 =
        insert(:external_payment_reference,
          payment: payment_org1,
          partner: "pagarme",
          external_id: "ext456",
          organization: org1
        )

      assert {:error, :not_found} ==
               Finance.get_payment_by_external_reference_and_organization(
                 org2.id,
                 "pagarme",
                 "ext456"
               )
    end

    test "does not find a payment if partner or external_id does not match" do
      org1 = insert(:organization)
      payment_org1 = insert(:payment, organization: org1)

      _epr_org1 =
        insert(:external_payment_reference,
          payment: payment_org1,
          partner: "pagarme",
          external_id: "ext789",
          organization: org1
        )

      assert {:error, :not_found} ==
               Finance.get_payment_by_external_reference_and_organization(
                 org1.id,
                 "other_partner",
                 "ext789"
               )

      assert {:error, :not_found} ==
               Finance.get_payment_by_external_reference_and_organization(
                 org1.id,
                 "pagarme",
                 "wrong_id"
               )
    end

    test "returns not_found for a external_id that does not exist" do
      org1 = insert(:organization)

      assert {:error, :not_found} ==
               Finance.get_payment_by_external_reference_and_organization(
                 org1.id,
                 "pagarme",
                 "123"
               )
    end
  end

  describe "sync_payment/3" do
    test "finish order and transaction when all paymenyt is settled" do
      org = insert(:organization)
      order = insert(:order, total_price: "100", organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "authorized",
          transaction: transaction,
          organization: org
        )

      insert(:external_payment_reference, payment: payment, organization: org)

      assert {:ok, :updated_payment} ==
               Finance.sync_payment(
                 payment,
                 %{status: "settled"}
               )

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "done"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "paid"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "does not finish order and transaction when there is a paymenyt not settled" do
      org = insert(:organization)
      order = insert(:order, total_price: 100, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: 70,
          status: "authorized",
          transaction: transaction,
          organization: org
        )

      insert(:external_payment_reference, payment: payment, organization: org)

      another_payment =
        insert(:payment,
          method: "payment_link",
          amount: 30,
          status: "authorized",
          transaction: transaction,
          organization: org
        )

      _another_epr =
        insert(:external_payment_reference, payment: another_payment, organization: org)

      assert {:ok, :updated_payment} ==
               Finance.sync_payment(
                 payment,
                 %{status: "settled"}
               )

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "open"} = Commerce.Orders.get_order!(order.organization_id, order.id)
    end

    test "does not update payment when it is not a valid status transition" do
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "authorized",
          transaction: transaction,
          organization: org
        )

      insert(:external_payment_reference, payment: payment, organization: org)

      assert {:error, changeset} = Finance.sync_payment(payment, %{status: "pending"})

      assert Enum.any?(
               errors_on(changeset).status,
               &String.contains?(&1, "status transition not allowed")
             )

      assert %Payment{status: "authorized"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end
  end

  describe "create_iglu_credit/2" do
    test "creates credit with valid data" do
      # Setup organization and customer
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      # Create reverse fulfillment with product return
      product = insert(:product, organization: organization)
      variant = insert(:product_variant, product: product, organization: organization)

      line_item =
        build(:line_item,
          organization: organization,
          product_variant: variant,
          location: build(:location, organization: organization)
        )

      fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1
            )
          ]
        )

      # Create credit attributes
      attrs = %{
        amount: Decimal.new("100"),
        reason: "return",
        status: "available",
        expires_at: DateTime.utc_now() |> DateTime.add(30, :day),
        customer_id: customer.id,
        reverse_fulfillment_id: fulfillment.id
      }

      # Verify credit creation
      assert {:ok, credit} = Finance.create_iglu_credit(organization.id, attrs)
      assert credit.amount == Decimal.new("100")
      assert credit.reason == "return"
      assert credit.status == "available"
      assert credit.customer_id == customer.id
      assert credit.reverse_fulfillment_id == fulfillment.id
    end

    test "fails with invalid data" do
      organization = insert(:organization)

      assert {:error, changeset} = Finance.create_iglu_credit(organization.id, %{})

      assert errors_on(changeset).amount
      assert errors_on(changeset).reason
    end
  end

  describe "list_customer_iglu_credits/3" do
    test "returns customer credits" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      fulfillment1 = insert(:reverse_fulfillment, organization: organization)
      fulfillment2 = insert(:reverse_fulfillment, organization: organization)

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "100",
        status: "available",
        reverse_fulfillment_id: fulfillment1.id
      )

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "50",
        status: "used",
        reverse_fulfillment_id: fulfillment2.id
      )

      credits = Finance.list_customer_iglu_credits(organization.id, customer.id)
      assert length(credits) == 2

      available_credits =
        Finance.list_customer_iglu_credits(organization.id, customer.id, status: "available")

      assert length(available_credits) == 1
    end
  end

  describe "get_customer_available_credit/2" do
    test "returns total available credit" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      future = DateTime.utc_now() |> DateTime.add(30, :day)

      # Create reverse fulfillments for each credit
      fulfillment1 = insert(:reverse_fulfillment, organization: organization)
      fulfillment2 = insert(:reverse_fulfillment, organization: organization)
      fulfillment3 = insert(:reverse_fulfillment, organization: organization)

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "100",
        status: "available",
        expires_at: future,
        reverse_fulfillment_id: fulfillment1.id
      )

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "50",
        status: "available",
        expires_at: future,
        reverse_fulfillment_id: fulfillment2.id
      )

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "25",
        status: "used",
        expires_at: future,
        reverse_fulfillment_id: fulfillment3.id
      )

      total = Finance.get_customer_available_credit(organization.id, customer.id)
      assert Decimal.eq?(total, Decimal.new("150"))
    end

    test "excludes expired credits from total" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      past = DateTime.utc_now() |> DateTime.add(-1, :day)
      future = DateTime.utc_now() |> DateTime.add(30, :day)

      # Create reverse fulfillments
      fulfillment1 = insert(:reverse_fulfillment, organization: organization)
      fulfillment2 = insert(:reverse_fulfillment, organization: organization)

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "100",
        status: "available",
        expires_at: past,
        reverse_fulfillment_id: fulfillment1.id
      )

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        amount: "50",
        status: "available",
        expires_at: future,
        reverse_fulfillment_id: fulfillment2.id
      )

      total = Finance.get_customer_available_credit(organization.id, customer.id)
      assert Decimal.eq?(total, Decimal.new("50"))
    end
  end

  describe "expire_iglu_credits/1" do
    test "expires outdated credits" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      past = DateTime.utc_now() |> DateTime.add(-1, :day)
      future = DateTime.utc_now() |> DateTime.add(30, :day)

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        status: "available",
        expires_at: past,
        reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
      )

      insert(:iglu_credit,
        organization: organization,
        customer: customer,
        status: "available",
        expires_at: future,
        reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
      )

      Finance.expire_iglu_credits(organization.id)

      expired =
        Finance.list_customer_iglu_credits(organization.id, customer.id, status: "expired")

      assert length(expired) == 1

      available =
        Finance.list_customer_iglu_credits(organization.id, customer.id, status: "available")

      assert length(available) == 1
    end
  end

  describe "create_iglu_credit_payment/3" do
    setup do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      fulfillment = insert(:reverse_fulfillment, organization: organization)

      transaction =
        insert(:transaction,
          organization: organization,
          order: build(:order, organization: organization)
        )

      credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          amount: "100",
          status: "available",
          reverse_fulfillment_id: fulfillment.id
        )

      {:ok,
       %{organization: organization, customer: customer, credit: credit, transaction: transaction}}
    end

    test "creates a valid credit payment with association", %{
      organization: organization,
      credit: credit,
      transaction: transaction
    } do
      payment_attrs = %{
        status: "settled",
        amount: "50.00",
        method: "return_credit",
        transaction_id: transaction.id,
        metadata: %{
          "credit_id" => credit.id
        }
      }

      assert {:ok, credit_payment} =
               Finance.create_iglu_credit_payment(organization.id, credit, payment_attrs)

      assert credit_payment.iglu_credit_id == credit.id
      assert credit_payment.organization_id == organization.id
      assert credit_payment.payment_id
    end

    test "fails with invalid payment attributes", %{organization: organization, credit: credit} do
      payment_attrs = %{
        status: "invalid_status",
        amount: "-50.00",
        method: "return_credit"
      }

      assert {:error, changeset} =
               Finance.create_iglu_credit_payment(organization.id, credit, payment_attrs)

      assert errors_on(changeset).payment.status
      assert errors_on(changeset).payment.transaction_id
    end

    test "fails when using credit amount smaller than payment amount", %{
      organization: organization,
      customer: customer,
      transaction: transaction
    } do
      credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          amount: Decimal.div(transaction.order.total_price, 2),
          status: "available",
          reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
        )

      # First payment using full credit amount
      payment_attrs = %{
        status: "settled",
        amount: credit.amount,
        method: "return_credit",
        transaction_id: transaction.id,
        metadata: %{
          "credit_id" => credit.id
        }
      }

      assert {:ok, _} = Finance.create_iglu_credit_payment(organization.id, credit, payment_attrs)

      # Try to use half of credit amount again
      payment_attrs = %{
        status: "settled",
        amount: Decimal.div(credit.amount, 2),
        method: "return_credit",
        transaction_id: transaction.id,
        metadata: %{
          "credit_id" => credit.id
        }
      }

      assert {:error, changeset} =
               Finance.create_iglu_credit_payment(organization.id, credit, payment_attrs)

      assert "insufficient balance" in errors_on(changeset).iglu_credit
    end
  end

  describe "get_iglu_credit_remaining_balance/1" do
    test "returns full balance when no payments exist" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      credit_amount = Decimal.new("100")

      credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          amount: credit_amount,
          status: "available",
          reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
        )

      assert Decimal.eq?(Finance.get_iglu_credit_remaining_balance(credit), credit_amount)
    end

    test "returns remaining balance after partial payment" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      credit_amount = Decimal.new("100")
      payment_amount = Decimal.new("30")

      credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          amount: credit_amount,
          status: "available",
          reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
        )

      payment =
        insert(:payment,
          organization: organization,
          amount: payment_amount,
          method: "return_credit",
          status: "settled",
          transaction:
            build(:transaction,
              organization: organization,
              order: build(:order, organization: organization)
            )
        )

      insert(:iglu_credit_payment,
        organization: organization,
        iglu_credit: credit,
        payment: payment
      )

      expected_remaining = Decimal.new("70")
      assert Decimal.eq?(Finance.get_iglu_credit_remaining_balance(credit), expected_remaining)
    end

    test "returns zero when fully used" do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)
      credit_amount = Decimal.new("100")

      credit =
        insert(:iglu_credit,
          organization: organization,
          customer: customer,
          amount: credit_amount,
          status: "available",
          reverse_fulfillment: build(:reverse_fulfillment, organization: organization)
        )

      payment =
        insert(:payment,
          organization: organization,
          amount: credit_amount,
          method: "return_credit",
          status: "settled",
          transaction:
            build(:transaction,
              organization: organization,
              order: build(:order, organization: organization)
            )
        )

      insert(:iglu_credit_payment,
        organization: organization,
        iglu_credit: credit,
        payment: payment
      )

      assert Decimal.eq?(Finance.get_iglu_credit_remaining_balance(credit), Decimal.new("0"))
    end
  end
end
