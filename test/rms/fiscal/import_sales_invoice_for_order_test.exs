defmodule Rms.Fiscal.ImportSalesInvoiceForOrderTest do
  use Rms.DataCase, async: true

  import Mox
  import Rms.Factory

  alias Rms.Fiscal.ImportSalesInvoiceForOrder
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock
  alias Rms.Integrator.Mock, as: IntegratorMock

  alias Rms.Repo

  setup :verify_on_exit!

  describe "call/1" do
    setup do
      {:ok, order: insert(:order)}
    end

    test "when the order has no fulfillments, it does nothing", %{order: order} do
      assert {:error, :no_fulfillments_without_fiscal_invoice} =
               ImportSalesInvoiceForOrder.call(order)
    end

    test "when all fulfillments already have fiscal invoices, it does nothing", %{order: order} do
      fulfillment = insert(:fulfillment, order: order, organization: order.organization)
      insert(:fiscal_invoice, fulfillment: fulfillment, organization: order.organization)

      assert {:error, :no_fulfillments_without_fiscal_invoice} =
               ImportSalesInvoiceForOrder.call(order)
    end
  end

  describe "call/1 with orders imported from Shopify" do
    setup [:create_shopify_order]

    test "when it has a single fulfillment without an invoice, it tries to find it on Shopify first",
         %{order: order, fulfillment: fulfillment} do
      mock_order_response = %{
        "data" => %{
          "order" => %{
            "name" => "1001",
            "customAttributes" => [
              %{"key" => "nfce-key", "value" => "35240111111111000111550010000987651234567898"}
            ]
          }
        }
      }

      order_external_id = order.external_id

      ShopifyMock
      |> expect(:client, 1, fn _, _ -> :mock_client end)
      |> expect(:get_order!, 1, fn :mock_client, ^order_external_id, _, _ ->
        {:ok, mock_order_response}
      end)

      expect(VincoClientMock, :get_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:ok,
         %{
           "ChaveDFe" => "35240443253315000234653000000003071110814728",
           "CodStatus" => 100,
           "CodStatusLote" => 104,
           "DFeProtocolo" => "135240000777778",
           "IdAssincrono" => 1_258_790_202,
           "Motivo" => "Autorizado o uso da NF-e",
           "MotivoLote" => "Lote processado",
           "QrCode" => "https://example.com/qrcode",
           "UrlDanfe" => "https://example.com/danfe",
           "XmlDFe" => """
           <?xml version="1.0" encoding="UTF-8"?>
           <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
             <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
             <infNFe versao="4.00" Id="NFe352401...">
               <ide>
                 <cUF>35</cUF>
                 <cNF>12345678</cNF>
                 <natOp>VENDA DE MERCADORIA</natOp>
                 <mod>55</mod>
                 <serie>1</serie>
                 <nNF>98765</nNF>
                 <dhEmi>2024-01-15T10:00:00-03:00</dhEmi>
                 <tpNF>1</tpNF>
                 <idDest>1</idDest>
                 <cMunFG>3550308</cMunFG>
                 <tpImp>1</tpImp>
                 <tpEmis>1</tpEmis>
                 <cDV>8</cDV>
                 <tpAmb>1</tpAmb>
                 <finNFe>1</finNFe>
                 <indFinal>1</indFinal>
                 <indPres>1</indPres>
                 <procEmi>0</procEmi>
                 <verProc>App v1.0</verProc>
               </ide>
               <!-- Other tags like emit, dest, det, total, etc. -->
             </infNFe>
           <infNFeSupl>
           <qrCode>
           <![CDATA[ https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240943253315000234652610000000011248668588|2|2|1|bc552bf1f84ae73f39ae2901e020fc9e7b2186a5 ]]>
           </qrCode>
           <urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave>
           </infNFeSupl>
           </NFe>
           <protNFe versao="4.00">
               <infProt>
                   <tpAmb>1</tpAmb>
                   <verAplic>SP_NFE_PL_009_V4</verAplic>
                   <chNFe>35240111111111000111550010000987651234567898</chNFe>
                   <dhRecbto>2024-01-15T10:01:00-03:00</dhRecbto>
                   <nProt>135240000000001</nProt>
                   <digVal>...</digVal>
                   <cStat>100</cStat>
                   <xMotivo>Autorizado o uso da NF-e</xMotivo>
               </infProt>
           </protNFe>
           </nfeProc>
           """,
           "XmlRecebimento" =>
             "<?xml version=\"1.0\" encoding=\"utf-8\"?><retEnviNFe xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><tpAmb>2</tpAmb><verAplic>SP_NFE_PL009_V4</verAplic><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo><cUF>35</cUF><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto></retEnviNFe>",
           "XmlRecibo" =>
             "<?xml version=\"1.0\" encoding=\"utf-8\"?><retConsReciNFe xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><tpAmb>2</tpAmb><verAplic>SP_NFE_PL009_V4</verAplic><nRec>123456789012345</nRec><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto></retConsReciNFe>"
         }}
      end)

      assert {:ok, _import_result} = ImportSalesInvoiceForOrder.call(order)

      updated_fulfillment = Repo.preload(fulfillment, :fiscal_invoices, force: true)

      assert [fiscal_invoice] = updated_fulfillment.fiscal_invoices
      assert fiscal_invoice.operation_type == "sale"
      assert fiscal_invoice.df_key == "35240111111111000111550010000987651234567898"
      assert fiscal_invoice.fulfillment_id == fulfillment.id
      assert fiscal_invoice.organization_id == order.organization_id
    end

    test "when it has a single fulfillment and invoice is not on Shopify, it tries the Integrator",
         %{order: order, fulfillment: fulfillment} do
      # Mock Shopify lookup to fail
      ShopifyMock
      |> expect(:client, 1, fn _, _ -> :mock_client end)
      |> expect(:get_order!, 1, fn :mock_client, _, _, _ ->
        {:ok, %{"data" => %{"order" => %{"customAttributes" => []}}}}
      end)

      # Mock Integrator lookup to succeed
      expect(IntegratorMock, :fetch_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "xml" => """
             <?xml version="1.0" encoding="UTF-8"?>
             <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
               <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
                 <infNFe versao="4.00" Id="NFe352401...">
                   <ide>
                     <cUF>35</cUF>
                     <cNF>12345678</cNF>
                     <natOp>VENDA DE MERCADORIA</natOp>
                     <mod>55</mod>
                     <serie>1</serie>
                     <nNF>98765</nNF>
                     <dhEmi>2024-01-15T10:00:00-03:00</dhEmi>
                     <tpNF>1</tpNF>
                     <idDest>1</idDest>
                     <cMunFG>3550308</cMunFG>
                     <tpImp>1</tpImp>
                     <tpEmis>1</tpEmis>
                     <cDV>8</cDV>
                     <tpAmb>1</tpAmb>
                     <finNFe>1</finNFe>
                     <indFinal>1</indFinal>
                     <indPres>1</indPres>
                     <procEmi>0</procEmi>
                     <verProc>App v1.0</verProc>
                   </ide>
                 </infNFe>
               </NFe>
             <protNFe versao="4.00">
                <infProt>
                    <tpAmb>1</tpAmb>
                    <verAplic>SP_NFE_PL_009_V4</verAplic>
                    <chNFe>35240111111111000111550010000987651234567898</chNFe>
                    <dhRecbto>2024-01-15T10:01:00-03:00</dhRecbto>
                    <nProt>135240000000001</nProt>
                    <digVal>...</digVal>
                    <cStat>100</cStat>
                    <xMotivo>Autorizado o uso da NF-e</xMotivo>
                </infProt>
             </protNFe>
             </nfeProc>
             """
           }
         }}
      end)

      assert {:ok, _import_result} = ImportSalesInvoiceForOrder.call(order)

      updated_fulfillment = Repo.preload(fulfillment, :fiscal_invoices, force: true)
      assert [fiscal_invoice] = updated_fulfillment.fiscal_invoices
      assert fiscal_invoice.operation_type == "sale"
    end

    test "when it has a single fulfillment and invoice is not found anywhere",
         %{order: order, fulfillment: fulfillment} do
      # Mock Shopify lookup to fail
      ShopifyMock
      |> expect(:client, 1, fn _, _ -> :mock_client end)
      |> expect(:get_order!, 1, fn :mock_client, _, _, _ ->
        {:ok, %{"data" => %{"order" => %{"customAttributes" => []}}}}
      end)

      # Mock Integrator lookup to fail
      expect(IntegratorMock, :fetch_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:error, :not_found}
      end)

      assert {:error, :invoice_not_found} = ImportSalesInvoiceForOrder.call(order)

      updated_fulfillment = Repo.preload(fulfillment, :fiscal_invoices, force: true)
      assert [] = updated_fulfillment.fiscal_invoices
    end

    defp create_shopify_order(context) do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      insert(:shopify_credential, organization: organization)
      insert(:vinco_credential, organization: organization, location: location)
      insert(:api_token, organization: organization)

      order =
        insert(:order,
          organization: organization,
          location: location,
          source: "shopify",
          external_id: "gid://shopify/Order/#{Ecto.UUID.generate()}"
        )

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: organization,
          external_reference: "gid://shopify/Fulfillment/#{Ecto.UUID.generate()}"
        )

      Map.merge(context, %{
        order: order,
        organization: organization,
        location: location,
        fulfillment: fulfillment
      })
    end
  end
end
