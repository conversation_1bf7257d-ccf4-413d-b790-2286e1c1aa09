defmodule Rms.Fiscal.InvoiceCreatorTest do
  use Rms.DataCase

  alias Rms.Fiscal.InvoiceCreator

  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  describe "execute/1" do
    test "create a nfc invoice for the 2 line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      nfc_serie =
        insert(:invoice_serie,
          invoice_env: "test",
          invoice_type: "nfc",
          location: loc,
          organization: org
        )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      line_item1 =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} = InvoiceCreator.execute(transaction)

      invoice =
        invoices["fulfillment:#{fulfillment.id}-shipping_method:#{fulfillment.shipping_method}"]

      assert invoice.operation_type == "sale"
      assert invoice.serie_id == nfc_serie.id
      assert invoice.fulfillment_id == fulfillment.id

      line_item_id = line_item.id
      line_item_id1 = line_item1.id

      assert [%{line_item_id: ^line_item_id}, %{line_item_id: ^line_item_id1}] =
               invoice.invoice_items

      assert_enqueued(
        worker: Rms.Workers.InvoiceIssuerWorker,
        args: %{"fiscal_invoice_id" => invoice.id}
      )
    end

    test "create a nfc invoice for the 2 line items without customer" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      nfc_serie =
        insert(:invoice_serie,
          invoice_env: "test",
          invoice_type: "nfc",
          location: loc,
          organization: org
        )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      line_item1 =
        insert(:line_item,
          shipping_method: "in-store",
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} = InvoiceCreator.execute(transaction)

      invoice =
        invoices["fulfillment:#{fulfillment.id}-shipping_method:#{fulfillment.shipping_method}"]

      assert invoice.operation_type == "sale"
      assert invoice.serie_id == nfc_serie.id
      assert is_nil(invoice.customer_id)

      line_item_id = line_item.id
      line_item_id1 = line_item1.id

      assert [%{line_item_id: ^line_item_id}, %{line_item_id: ^line_item_id1}] =
               invoice.invoice_items

      assert_enqueued(
        worker: Rms.Workers.InvoiceIssuerWorker,
        args: %{"fiscal_invoice_id" => invoice.id}
      )
    end

    test "create a nf invoice for the 2 line items" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      nf_serie =
        insert(:invoice_serie,
          invoice_env: "test",
          invoice_type: "nfc",
          location: loc,
          organization: org
        )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer,
        transaction: transaction,
        organization: org,
        document_type: "cnpj"
      )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} = InvoiceCreator.execute(transaction)

      invoice =
        invoices["fulfillment:#{fulfillment.id}-shipping_method:#{fulfillment.shipping_method}"]

      assert invoice.operation_type == "sale"
      assert invoice.serie_id == nf_serie.id
    end

    test "does not create for delivery shipping methods" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      loc2 = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc2,
        organization: org
      )

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nf",
        location: loc2,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      f1 = insert(:fulfillment, order: order, organization: org, shipping_method: "local-pickup")
      f2 = insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

      insert(:line_item,
        shipping_method: "local-pickup",
        fulfillment: f1,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:line_item,
        shipping_method: "delivery",
        fulfillment: f2,
        organization: org,
        product_variant: pv,
        location: loc2
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      {:ok, invoices} = InvoiceCreator.execute(transaction)

      Enum.each(invoices, fn {_, invoice} ->
        assert invoice == "sent to e-commerce"
      end)
    end

    test "creates invoice with only settled payments" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)

      # Insert payments with different statuses
      settled_payment1 =
        insert(:payment, status: "settled", transaction: transaction, organization: org)

      settled_payment2 =
        insert(:payment, status: "settled", transaction: transaction, organization: org)

      insert(:payment, status: "pending", transaction: transaction, organization: org)
      insert(:payment, status: "failed", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      assert {:ok, invoices} = InvoiceCreator.execute(transaction)

      {_, invoice} = invoices |> Map.to_list() |> hd()
      payment_ids = Enum.map(invoice.invoice_payments, & &1.payment_id)
      assert length(invoice.invoice_payments) == 2
      assert settled_payment1.id in payment_ids
      assert settled_payment2.id in payment_ids
    end

    test "raises error when trying to create invoice with no settled payments" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:invoice_serie,
        invoice_env: "test",
        invoice_type: "nfc",
        location: loc,
        organization: org
      )

      order = insert(:order, organization: org, location: loc)
      transaction = insert(:transaction, order: order, organization: org)

      # Insert only non-settled payments
      insert(:payment, status: "pending", transaction: transaction, organization: org)
      insert(:payment, status: "failed", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      insert(:line_item,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

      insert(:fiscal_settings,
        organization: org,
        location: loc,
        environment: "test"
      )

      assert_raise MatchError, fn ->
        InvoiceCreator.execute(transaction)
      end
    end
  end
end
