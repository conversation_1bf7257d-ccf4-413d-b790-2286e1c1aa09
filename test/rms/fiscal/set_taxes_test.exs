defmodule Rms.Fiscal.SetTaxesTest do
  alias Rms.Fiscal.SetTaxes

  use Rms.DataCase

  import Rms.Factory

  describe "set_taxes/4 when both uf are the same" do
    test "with FCP" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      p1 = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: p1, organization: org)

      order = insert(:order, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "SP")

      assert taxes.cest == "0101000"
      assert taxes.cfop == "5102"

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.percentage_fcp == Decimal.round(Decimal.new("2.00"), 2)
      assert taxes.icms.value_fcp == Decimal.round(Decimal.new("2.00"), 2)

      assert taxes.pis.cst == "08"
      assert taxes.pis.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.pis.percentage_pis == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.pis.value_pis == Decimal.round(Decimal.new("8.20"), 2)

      assert taxes.cofins.cst == "08"
      assert taxes.cofins.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.cofins.percentage_cofins == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.cofins.value_cofins == Decimal.round(Decimal.new("8.20"), 2)
    end

    test "without FCP" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      p1 = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: p1, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.00"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "SP")

      assert taxes.cest == "0101000"
      assert taxes.cfop == "5102"

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.percentage_fcp == Decimal.round(Decimal.new("0.00"), 2)
      assert taxes.icms.value_fcp == Decimal.round(Decimal.new("0.00"), 2)

      assert taxes.pis.cst == "08"
      assert taxes.pis.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.pis.percentage_pis == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.pis.value_pis == Decimal.round(Decimal.new("8.20"), 2)

      assert taxes.cofins.cst == "08"
      assert taxes.cofins.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.cofins.percentage_cofins == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.cofins.value_cofins == Decimal.round(Decimal.new("8.20"), 2)
    end

    test "when icms is not paid" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      p1 = insert(:product, organization: org)

      pv1 = insert(:product_variant, product: p1, organization: org)

      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "400",
        fcp_percentage: Decimal.new("0.00"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "SP")

      assert taxes.cest == "0101000"
      assert taxes.cfop == "5102"

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "400"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("0.00"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("0.00"), 2)
      assert taxes.icms.percentage_fcp == Decimal.round(Decimal.new("0.00"), 2)
      assert taxes.icms.value_fcp == Decimal.round(Decimal.new("0.00"), 2)

      assert taxes.pis.cst == "08"
      assert taxes.pis.value_bc == Decimal.round(Decimal.new("100.00"), 2)
      assert taxes.pis.percentage_pis == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.pis.value_pis == Decimal.round(Decimal.new("10"), 2)

      assert taxes.cofins.cst == "08"
      assert taxes.cofins.value_bc == Decimal.round(Decimal.new("100.00"), 2)
      assert taxes.cofins.percentage_cofins == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.cofins.value_cofins == Decimal.round(Decimal.new("10"), 2)
    end

    test "uses ncm from product_taxes when product.ncm is nil" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      p1 = insert(:product, organization: org, ncm: nil)

      pv1 = insert(:product_variant, product: p1, organization: org)

      order = insert(:order, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: "22041010",
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "SP")

      assert taxes.ncm == "22041010"
      assert taxes.cest == "0101000"
      assert taxes.cfop == "5102"

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("18.00"), 2)
      assert taxes.icms.percentage_fcp == Decimal.round(Decimal.new("2.00"), 2)
      assert taxes.icms.value_fcp == Decimal.round(Decimal.new("2.00"), 2)

      assert taxes.pis.cst == "08"
      assert taxes.pis.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.pis.percentage_pis == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.pis.value_pis == Decimal.round(Decimal.new("8.20"), 2)

      assert taxes.cofins.cst == "08"
      assert taxes.cofins.value_bc == Decimal.round(Decimal.new("82.00"), 2)
      assert taxes.cofins.percentage_cofins == Decimal.round(Decimal.new("10.00"), 2)
      assert taxes.cofins.value_cofins == Decimal.round(Decimal.new("8.20"), 2)
    end
  end

  describe "set_taxes/4 when interstate" do
    test "with origin in [1, 2, 6, 7] should apply 4% ICMS" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "2",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "RJ")

      assert taxes.icms.origin == "2"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("4.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("4.00"), 2)
    end

    test "with origin not in [1, 2, 6, 7] should use configured percentage" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.12"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "RJ")

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("12.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("12.00"), 2)
    end

    test "from South/Southeast to North/Northeast/Center-West should apply 7% ICMS" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "AM")

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("7.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("7.00"), 2)
    end

    test "from North/Northeast/Center-West to South/Southeast should apply 12% ICMS" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "AM",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "AM", "SP")

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("12.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("12.00"), 2)
    end

    test "between North/Northeast/Center-West states should apply 12% ICMS" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "AM",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "AM", "BA")

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("12.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("12.00"), 2)
    end

    test "between South/Southeast states should apply 12% ICMS" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      p1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: p1, organization: org)
      order = insert(:order, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: p1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, taxes} =
        SetTaxes.set_taxes(line_item1, Decimal.new("100"), "SP", "RJ")

      assert taxes.icms.origin == "0"
      assert taxes.icms.cstcsosn == "30"
      assert taxes.icms.value_bc == Decimal.round(Decimal.new("100"), 2)
      assert taxes.icms.percentage_icms == Decimal.round(Decimal.new("12.00"), 2)
      assert taxes.icms.value_icms == Decimal.round(Decimal.new("12.00"), 2)
    end
  end
end
