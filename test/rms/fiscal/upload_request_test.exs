defmodule Rms.Fiscal.UploadRequestTest do
  use Rms.DataCase
  import Mox

  alias Rms.Fiscal
  import Rms.Factory

  @upload_bucket "test-bucket"

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    {:ok, %{organization: organization}}
  end

  describe "create_product_tax_upload_request/1" do
    test "creates upload request with correct settings", %{organization: organization} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn bucket, key, opts ->
        assert bucket == @upload_bucket
        assert String.starts_with?(key, "product_taxes/")
        assert Keyword.get(opts, :content_type) == "text/csv"
        assert Keyword.get(opts, :content_length) == 10 * 1024 * 1024
        assert Keyword.get(opts, :expires_in) == 3600

        {:ok,
         %{
           url: "https://example.com/test-url",
           fields: %{
             "key" => key,
             "Content-Type" => "text/csv"
           }
         }}
      end)

      assert {:ok, %{upload: upload}} =
               Fiscal.create_product_tax_upload_request(organization.id)

      assert upload.organization_id == organization.id
      assert upload.status == "pending"
      assert String.starts_with?(upload.s3_key, "product_taxes/")
    end

    test "sets correct expiration time", %{organization: organization} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn _bucket, key, _opts ->
        {:ok,
         %{
           url: "https://example.com/test-url",
           fields: %{
             "key" => key,
             "Content-Type" => "text/csv"
           }
         }}
      end)

      assert {:ok, %{upload: upload}} = Fiscal.create_product_tax_upload_request(organization.id)

      assert upload.organization_id == organization.id
      assert upload.status == "pending"
      assert String.starts_with?(upload.s3_key, "product_taxes/")
    end
  end
end
