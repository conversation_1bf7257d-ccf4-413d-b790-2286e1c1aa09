defmodule Rms.Fiscal.Workers.BuildTransferFiscalInvoiceTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Commerce.Orders
  alias Rms.Fiscal.Workers.BuildTransferFiscalInvoice

  defp create_order_with_reverse_fulfillment(context, opts \\ []) do
    %{
      organization: organization,
      location: location,
      location_2: location_2,
      customer: customer,
      staff: staff,
      product_variant: product_variant
    } = context

    cart_items =
      Keyword.get(opts, :cart_items, [
        %{product_variant: product_variant, quantity: 5, price: "10"}
      ])

    total_price = Keyword.get(opts, :total_price, Decimal.new("50"))

    # Create original order
    cart =
      insert(:cart,
        organization: organization,
        customer: customer,
        staff: staff,
        location: location,
        total_price: total_price,
        total_items_list_price: total_price,
        total_items_selling_price: total_price,
        total_manual_discount: Decimal.new("0"),
        total_delivery_price: Decimal.new("0")
      )

    delivery_group =
      insert(:delivery_group,
        cart: cart,
        organization: organization,
        fulfillment_type: "in-store"
      )

    # Create cart items
    Enum.each(cart_items, fn item ->
      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: item.product_variant,
        quantity: item.quantity,
        list_price: item.price,
        selling_price: item.price,
        total_price: Decimal.mult(Decimal.new(item.price), item.quantity) |> Decimal.to_string()
      )
    end)

    cart =
      Rms.Repo.preload(cart, [
        :discounts,
        :shipping_address,
        :cart_addons,
        delivery_groups: [cart_items: [:discounts, product_variant: :product]]
      ])

    order_params = %{
      "cart_id" => cart.id,
      "location_id" => location.id
    }

    order_attrs = Orders.format_cart(cart, order_params)
    {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

    # Create fulfillment and line items for the original order
    fulfillment = insert(:fulfillment, order: order, organization: organization)

    line_items =
      Enum.map(cart_items, fn item ->
        insert(:line_item,
          fulfillment: fulfillment,
          organization: organization,
          product_variant: item.product_variant,
          location: location,
          staff: staff,
          quantity: item.quantity,
          price: Decimal.new(item.price)
        )
      end)

    # Create reverse fulfillment
    reverse_fulfillment =
      insert(:reverse_fulfillment,
        organization: organization,
        location: location_2,
        status: "pending"
      )

    insert(:fiscal_invoice,
      organization: organization,
      fulfillment: fulfillment,
      operation_type: "return"
    )

    # Link the reverse fulfillment to the original order through line items
    line_items
    |> Enum.with_index()
    |> Enum.each(fn {line_item, index} ->
      reason = if length(line_items) > 1, do: "defective_item#{index + 1}", else: "defective"

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        line_item: line_item,
        reverse_fulfillment: reverse_fulfillment,
        returned_quantity: 1,
        reason: reason
      )
    end)

    # Create new order for the destination location
    new_total = Decimal.mult(Decimal.new("2.00"), length(cart_items))

    new_cart =
      insert(:cart,
        organization: organization,
        customer: customer,
        staff: staff,
        location: location_2,
        total_price: new_total,
        total_items_list_price: new_total,
        total_items_selling_price: new_total,
        total_manual_discount: Decimal.new("0"),
        total_delivery_price: Decimal.new("0")
      )

    new_delivery_group =
      insert(:delivery_group,
        cart: new_cart,
        organization: organization,
        fulfillment_type: "in-store"
      )

    # Create cart items for new order
    Enum.each(cart_items, fn item ->
      insert(:cart_item,
        delivery_group: new_delivery_group,
        organization: organization,
        product_variant: item.product_variant,
        quantity: 1,
        list_price: "10",
        selling_price: "10",
        total_price: "10"
      )
    end)

    new_cart =
      Rms.Repo.preload(new_cart, [
        :discounts,
        :shipping_address,
        :cart_addons,
        delivery_groups: [cart_items: [:discounts, product_variant: :product]]
      ])

    new_order_params = %{
      "cart_id" => new_cart.id,
      "location_id" => location_2.id
    }

    new_order_attrs = Orders.format_cart(new_cart, new_order_params)

    {:ok, new_order} =
      Orders.create_order(organization.id, new_order_attrs, allowed_location_ids: :all)

    # Create iglu_credit with the reverse_fulfillment
    iglu_credit =
      insert(:iglu_credit, %{
        organization: organization,
        reverse_fulfillment_id: reverse_fulfillment.id,
        reason: "return",
        amount: new_total
      })

    # Create transaction and payment
    new_transaction = insert(:transaction, order: new_order, organization: organization)

    new_payment =
      insert(:payment, %{
        transaction: new_transaction,
        organization: organization,
        status: "settled",
        method: "iglu_credit"
      })

    # Create iglu_credit_payment linking everything
    _iglu_credit_payment =
      insert(:iglu_credit_payment, %{
        payment: new_payment,
        iglu_credit: iglu_credit,
        organization: organization
      })

    %{
      order: order,
      new_order: new_order,
      reverse_fulfillment: reverse_fulfillment,
      line_items: line_items
    }
  end

  describe "execute/1" do
    setup do
      organization = insert(:organization)

      # Create addresses
      address =
        insert(:address, %{
          state: "SP",
          city_code: "3550308",
          city_name: "São Paulo",
          zip: "01234567",
          neighborhood: "Centro",
          street: "Rua Test",
          number: "123"
        })

      address_2 =
        insert(:address, %{
          state: "SP",
          city_code: "3550308",
          city_name: "São Paulo",
          zip: "89012345",
          neighborhood: "Vila Test",
          street: "Av Test",
          number: "456"
        })

      # Create locations
      location = insert(:location, organization: organization, address: address)
      location_2 = insert(:location, organization: organization, address: address_2)

      # Create fiscal settings (default without transfer_without_taxes)
      fiscal_settings = insert(:fiscal_settings, organization: organization, location: location)

      fiscal_settings_2 =
        insert(:fiscal_settings, organization: organization, location: location_2)

      # Insert location taxes for both locations
      location_tax =
        insert(:location_tax, %{
          organization: organization,
          location: location,
          ie: "*********",
          crt: "1",
          name: "Location 1 Tax",
          current_tax_uf: "SP"
        })

      location_tax_2 =
        insert(:location_tax, %{
          organization: organization,
          location: location_2,
          ie: "*********",
          crt: "1",
          name: "Location 2 Tax",
          current_tax_uf: "SP"
        })

      # Create invoice series for both locations
      invoice_serie =
        insert(:invoice_serie,
          location: location,
          invoice_env: "dev",
          organization: organization,
          invoice_type: "nf",
          invoice_serie: "1",
          available_number: 1,
          status: "active"
        )

      invoice_serie_2 =
        insert(:invoice_serie,
          location: location_2,
          invoice_env: "dev",
          organization: organization,
          invoice_type: "nf",
          invoice_serie: "1",
          available_number: 1,
          status: "active"
        )

      # Add product taxes
      product_taxes =
        insert(:product_taxes,
          ncm: nil,
          sku: nil,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: organization
        )

      # Create customer and staff
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      # Create product and variant
      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          sku: "530",
          product: product
        )

      {:ok,
       %{
         organization: organization,
         address: address,
         address_2: address_2,
         location: location,
         location_2: location_2,
         fiscal_settings: fiscal_settings,
         fiscal_settings_2: fiscal_settings_2,
         location_tax: location_tax,
         location_tax_2: location_tax_2,
         invoice_serie: invoice_serie,
         invoice_serie_2: invoice_serie_2,
         product_taxes: product_taxes,
         customer: customer,
         staff: staff,
         product: product,
         product_variant: product_variant
       }}
    end

    test "processes transfer fiscal invoice job successfully", context do
      %{reverse_fulfillment: reverse_fulfillment} =
        create_order_with_reverse_fulfillment(context)

      %{organization: organization} = context

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "dev"
      }

      expected_base = Decimal.new("10.00")
      expected_fcp = Decimal.new("0.20")
      expected_icms = Decimal.new("1.80")
      expected_base_icms = Decimal.new("18.00")
      expected_zero = Decimal.new("0")
      expected_zero_decimal = Decimal.new("0.00")
      expected_unit_price = Decimal.new("2.00")

      assert {:ok,
              %{
                total: %{
                  ICMSTot: %{
                    vBC: ^expected_base,
                    vCOFINS: ^expected_zero,
                    vPIS: ^expected_zero,
                    vBCST: ^expected_zero,
                    vICMSDeson: ^expected_zero,
                    vFCP: ^expected_fcp,
                    vICMS: ^expected_icms,
                    vDesc: ^expected_zero_decimal,
                    vFrete: ^expected_zero,
                    vProd: ^expected_base,
                    vFCPST: ^expected_zero,
                    vFCPSTRet: ^expected_zero,
                    vICMSUFDest: ^expected_zero,
                    vII: ^expected_zero,
                    vIPI: ^expected_zero,
                    vIPIDevol: ^expected_zero,
                    vNF: ^expected_base,
                    vOutro: ^expected_zero,
                    vST: ^expected_zero,
                    vSeg: ^expected_zero
                  }
                },
                dest: %{
                  enderDest: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "89012345",
                    nro: "456",
                    xBairro: "Vila Test",
                    xLgr: "Av Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj,
                  indIEDest: "1",
                  xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",
                  CRT: "1",
                  IE: "*********"
                },
                det: [
                  %{
                    prod: %{
                      CFOP: "5152",
                      CEST: "0101000",
                      NCM: _ncm,
                      cEAN: "SEM GTIN",
                      cEANTrib: "SEM GTIN",
                      cProd: "530",
                      indTot: "1",
                      qCom: "5",
                      qTrib: "5",
                      uCom: "UN",
                      uTrib: "UN",
                      vDesc: ^expected_zero_decimal,
                      vFrete: "0",
                      vProd: ^expected_base,
                      vUnCom: ^expected_unit_price,
                      vUnTrib: ^expected_unit_price,
                      xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
                    },
                    imposto: %{
                      ICMS: %{
                        vBC: ^expected_base,
                        CSTCSOSN: "30",
                        modBC: "3",
                        orig: "0",
                        pFCP: ^expected_unit_price,
                        pICMS: ^expected_base_icms,
                        vFCP: ^expected_fcp,
                        vICMS: ^expected_icms
                      },
                      COFINS: %{
                        CST: "08",
                        pCOFINS: "0",
                        vBC: "0",
                        vCOFINS: "0"
                      },
                      PIS: %{CST: "08", vBC: "0", pPIS: "0", vPIS: "0"}
                    },
                    nItem: 1
                  }
                ],
                emit: %{
                  enderEmit: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "01234567",
                    nro: "123",
                    xBairro: "Centro",
                    xLgr: "Rua Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj_emit,
                  xNome: "Location 1 Tax",
                  CRT: "1",
                  IE: "*********"
                },
                Sincrono: true,
                IdKeySistema: _id,
                Venda: _venda,
                PDV: "0",
                ide: %{
                  mod: "55",
                  serie: 1,
                  cUF: "35",
                  cMunFG: "3550308",
                  natOp: "Transferencia interna",
                  nNF: 1,
                  dhEmi: _date,
                  tpAmb: "2",
                  indFinal: "1",
                  tpNF: "1",
                  tpImp: "1",
                  idDest: "1",
                  tpEmis: "1",
                  finNFe: "1",
                  procEmi: "0",
                  indPres: "1"
                },
                transp: %{modFrete: "9", vol: [%{qVol: "1"}]},
                pag: %{detPag: [%{tPag: "90", vPag: "0"}]},
                infAdic: %{
                  infCpl: _inf_cpl
                },
                infRespTec: %{
                  email: "<EMAIL>",
                  CNPJ: "46991783000113",
                  xContato: "Iglu Tecnologia",
                  fone: "16991080570"
                }
              }} = perform_job(BuildTransferFiscalInvoice, args)
    end

    test "processes transfer fiscal without taxes invoice job successfully", context do
      # Update fiscal settings to enable transfer_without_taxes
      %{fiscal_settings: fiscal_settings, fiscal_settings_2: fiscal_settings_2} = context

      Rms.Repo.update!(Ecto.Changeset.change(fiscal_settings, transfer_without_taxes: true))
      Rms.Repo.update!(Ecto.Changeset.change(fiscal_settings_2, transfer_without_taxes: true))

      %{reverse_fulfillment: reverse_fulfillment} =
        create_order_with_reverse_fulfillment(context)

      %{organization: organization} = context

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "dev"
      }

      expected_base = Decimal.new("10.00")
      expected_zero = Decimal.new("0")
      expected_zero_decimal = Decimal.new("0.00")
      expected_unit_price = Decimal.new("2.00")

      assert {:ok,
              %{
                total: %{
                  ICMSTot: %{
                    vBC: ^expected_zero,
                    vCOFINS: ^expected_zero,
                    vPIS: ^expected_zero,
                    vBCST: ^expected_zero,
                    vICMSDeson: ^expected_zero,
                    vFCP: ^expected_zero,
                    vICMS: ^expected_zero,
                    vDesc: ^expected_zero_decimal,
                    vFrete: ^expected_zero,
                    vProd: ^expected_base,
                    vFCPST: ^expected_zero,
                    vFCPSTRet: ^expected_zero,
                    vICMSUFDest: ^expected_zero,
                    vII: ^expected_zero,
                    vIPI: ^expected_zero,
                    vIPIDevol: ^expected_zero,
                    vNF: ^expected_base,
                    vOutro: ^expected_zero,
                    vST: ^expected_zero,
                    vSeg: ^expected_zero
                  }
                },
                dest: %{
                  enderDest: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "89012345",
                    nro: "456",
                    xBairro: "Vila Test",
                    xLgr: "Av Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj,
                  indIEDest: "1",
                  xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",
                  CRT: "1",
                  IE: "*********"
                },
                det: [
                  %{
                    prod: %{
                      CFOP: "5152",
                      CEST: "0101000",
                      NCM: _ncm,
                      cEAN: "SEM GTIN",
                      cEANTrib: "SEM GTIN",
                      cProd: "530",
                      indTot: "1",
                      qCom: "5",
                      qTrib: "5",
                      uCom: "UN",
                      uTrib: "UN",
                      vDesc: ^expected_zero_decimal,
                      vFrete: "0",
                      vProd: ^expected_base,
                      vUnCom: ^expected_unit_price,
                      vUnTrib: ^expected_unit_price,
                      xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
                    },
                    imposto: %{
                      ICMS: %{
                        vBC: "0",
                        CSTCSOSN: "41",
                        modBC: "3",
                        orig: "0",
                        pFCP: "0",
                        pICMS: "0",
                        vFCP: "0",
                        vICMS: "0"
                      },
                      COFINS: %{
                        CST: "08",
                        pCOFINS: "0",
                        vBC: "0",
                        vCOFINS: "0"
                      },
                      PIS: %{CST: "08", vBC: "0", pPIS: "0", vPIS: "0"}
                    },
                    nItem: 1
                  }
                ],
                emit: %{
                  enderEmit: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "01234567",
                    nro: "123",
                    xBairro: "Centro",
                    xLgr: "Rua Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj_emit,
                  xNome: "Location 1 Tax",
                  CRT: "1",
                  IE: "*********"
                },
                Sincrono: true,
                IdKeySistema: _id,
                Venda: _venda,
                PDV: "0",
                ide: %{
                  mod: "55",
                  serie: 1,
                  cUF: "35",
                  cMunFG: "3550308",
                  natOp: "Transferencia interna",
                  nNF: 1,
                  dhEmi: _date,
                  tpAmb: "2",
                  indFinal: "1",
                  tpNF: "1",
                  tpImp: "1",
                  idDest: "1",
                  tpEmis: "1",
                  finNFe: "1",
                  procEmi: "0",
                  indPres: "1"
                },
                transp: %{modFrete: "9", vol: [%{qVol: "1"}]},
                pag: %{detPag: [%{tPag: "90", vPag: "0"}]},
                infAdic: %{
                  infCpl: _inf_cpl
                },
                infRespTec: %{
                  email: "<EMAIL>",
                  CNPJ: "46991783000113",
                  xContato: "Iglu Tecnologia",
                  fone: "16991080570"
                }
              }} = perform_job(BuildTransferFiscalInvoice, args)
    end

    test "processes transfer fiscal invoice job with multiple items and checks nItem order",
         context do
      # Create additional product and variant for multi-item test
      %{organization: organization} = context

      product2 = insert(:product, organization: organization, name: "Product B")

      product_variant2 =
        insert(:product_variant,
          organization: organization,
          sku: "531",
          product: product2,
          price: Decimal.new("10.00")
        )

      # Create order with multiple items
      cart_items = [
        %{product_variant: context.product_variant, quantity: 5, price: "10"},
        %{product_variant: product_variant2, quantity: 5, price: "10"}
      ]

      %{reverse_fulfillment: reverse_fulfillment} =
        create_order_with_reverse_fulfillment(context,
          cart_items: cart_items,
          total_price: Decimal.new("100")
        )

      %{organization: organization} = context

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "dev"
      }

      item_q_com = "5"
      item_v_un_com_trib = Decimal.new("2.00")
      item_v_prod = Decimal.new("10.00")

      item_v_bc_icms = item_v_prod
      item_p_fcp_icms = Decimal.new("2.00")
      item_p_icms_icms = Decimal.new("18.00")
      item_v_fcp_icms = Decimal.new("0.20")
      item_v_icms_icms = Decimal.new("1.80")

      total_v_prod = Decimal.new("20.00")
      total_v_bc_icmstot = total_v_prod
      total_v_fcp_icmstot = Decimal.new("0.40")
      total_v_icms_icmstot = Decimal.new("3.60")
      total_v_nf_icmstot = total_v_prod

      expected_zero = Decimal.new("0")
      expected_zero_decimal = Decimal.new("0.00")

      assert {:ok,
              %{
                total: %{
                  ICMSTot: %{
                    vBC: ^total_v_bc_icmstot,
                    vCOFINS: ^expected_zero,
                    vPIS: ^expected_zero,
                    vBCST: ^expected_zero,
                    vICMSDeson: ^expected_zero,
                    vFCP: ^total_v_fcp_icmstot,
                    vICMS: ^total_v_icms_icmstot,
                    vDesc: ^expected_zero_decimal,
                    vFrete: ^expected_zero,
                    vProd: ^total_v_prod,
                    vFCPST: ^expected_zero,
                    vFCPSTRet: ^expected_zero,
                    vICMSUFDest: ^expected_zero,
                    vII: ^expected_zero,
                    vIPI: ^expected_zero,
                    vIPIDevol: ^expected_zero,
                    vNF: ^total_v_nf_icmstot,
                    vOutro: ^expected_zero,
                    vST: ^expected_zero,
                    vSeg: ^expected_zero
                  }
                },
                dest: %{
                  enderDest: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "89012345",
                    nro: "456",
                    xBairro: "Vila Test",
                    xLgr: "Av Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj_dest,
                  indIEDest: "1",
                  xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",
                  CRT: "1",
                  IE: "*********"
                },
                det: [
                  %{
                    nItem: 1,
                    prod: %{
                      CFOP: "5152",
                      CEST: "0101000",
                      NCM: _ncm1,
                      cEAN: "SEM GTIN",
                      cEANTrib: "SEM GTIN",
                      cProd: "530",
                      indTot: "1",
                      qCom: ^item_q_com,
                      qTrib: ^item_q_com,
                      uCom: "UN",
                      uTrib: "UN",
                      vDesc: ^expected_zero_decimal,
                      vFrete: "0",
                      vProd: ^item_v_prod,
                      vUnCom: ^item_v_un_com_trib,
                      vUnTrib: ^item_v_un_com_trib,
                      xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
                    },
                    imposto: %{
                      ICMS: %{
                        vBC: ^item_v_bc_icms,
                        CSTCSOSN: "30",
                        modBC: "3",
                        orig: "0",
                        pFCP: ^item_p_fcp_icms,
                        pICMS: ^item_p_icms_icms,
                        vFCP: ^item_v_fcp_icms,
                        vICMS: ^item_v_icms_icms
                      },
                      COFINS: %{CST: "08", pCOFINS: "0", vBC: "0", vCOFINS: "0"},
                      PIS: %{CST: "08", vBC: "0", pPIS: "0", vPIS: "0"}
                    }
                  },
                  %{
                    nItem: 2,
                    prod: %{
                      CFOP: "5152",
                      CEST: "0101000",
                      NCM: _ncm2,
                      cEAN: "SEM GTIN",
                      cEANTrib: "SEM GTIN",
                      cProd: "531",
                      indTot: "1",
                      qCom: ^item_q_com,
                      qTrib: ^item_q_com,
                      uCom: "UN",
                      uTrib: "UN",
                      vDesc: ^expected_zero_decimal,
                      vFrete: "0",
                      vProd: ^item_v_prod,
                      vUnCom: ^item_v_un_com_trib,
                      vUnTrib: ^item_v_un_com_trib,
                      xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
                    },
                    imposto: %{
                      ICMS: %{
                        vBC: ^item_v_bc_icms,
                        CSTCSOSN: "30",
                        modBC: "3",
                        orig: "0",
                        pFCP: ^item_p_fcp_icms,
                        pICMS: ^item_p_icms_icms,
                        vFCP: ^item_v_fcp_icms,
                        vICMS: ^item_v_icms_icms
                      },
                      COFINS: %{CST: "08", pCOFINS: "0", vBC: "0", vCOFINS: "0"},
                      PIS: %{CST: "08", vBC: "0", pPIS: "0", vPIS: "0"}
                    }
                  }
                ],
                emit: %{
                  enderEmit: %{
                    UF: "SP",
                    cMun: "3550308",
                    CEP: "01234567",
                    nro: "123",
                    xBairro: "Centro",
                    xLgr: "Rua Test",
                    xMun: "São Paulo"
                  },
                  CNPJ: _cnpj_emit,
                  xNome: "Location 1 Tax",
                  CRT: "1",
                  IE: "*********"
                },
                Sincrono: true,
                IdKeySistema: _id,
                Venda: _venda,
                PDV: "0",
                ide: %{
                  mod: "55",
                  serie: 1,
                  cUF: "35",
                  cMunFG: "3550308",
                  natOp: "Transferencia interna",
                  nNF: 1,
                  dhEmi: _date,
                  tpAmb: "2",
                  indFinal: "1",
                  tpNF: "1",
                  tpImp: "1",
                  idDest: "1",
                  tpEmis: "1",
                  finNFe: "1",
                  procEmi: "0",
                  indPres: "1"
                },
                transp: %{modFrete: "9", vol: [%{qVol: "1"}]},
                pag: %{detPag: [%{tPag: "90", vPag: "0"}]},
                infAdic: %{
                  infCpl: _inf_cpl
                },
                infRespTec: %{
                  email: "<EMAIL>",
                  CNPJ: "46991783000113",
                  xContato: "Iglu Tecnologia",
                  fone: "16991080570"
                }
              }} = perform_job(BuildTransferFiscalInvoice, args)
    end

    test "does not create duplicate fiscal invoices if run twice with same args", %{
      organization: organization
    } do
      address = insert(:address)

      origin_location =
        insert(:location, organization: organization, address: address, cnpj: "53287647000119")

      destination_location =
        insert(:location,
          organization: organization,
          address: address,
          cnpj: "13937073000156",
          name: "Dest Location"
        )

      insert(:location_tax, %{
        organization: organization,
        location: origin_location,
        ie: "*********",
        crt: "1",
        name: "Location 1 Tax",
        current_tax_uf: "SP"
      })

      insert(:location_tax, %{
        organization: organization,
        location: destination_location,
        ie: "*********",
        crt: "1",
        name: "Location 2 Tax",
        current_tax_uf: "SP"
      })

      insert(:address, %{
        location: origin_location,
        state: "SP",
        city_code: "3550308",
        city_name: "São Paulo",
        zip: "01234567",
        neighborhood: "Centro",
        street: "Rua Test",
        number: "123"
      })

      insert(:address, %{
        location: destination_location,
        state: "SP",
        city_code: "3550308",
        city_name: "São Paulo",
        zip: "89012345",
        neighborhood: "Vila Test",
        street: "Av Test",
        number: "456"
      })

      insert(:fiscal_settings, organization: organization, location: origin_location)
      insert(:fiscal_settings, organization: organization, location: destination_location)

      original_order =
        insert(:order, organization: organization, location: origin_location, status: "delivered")

      reverse_fulfillment =
        insert(:reverse_fulfillment, organization: organization, location: destination_location)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant, product: product, organization: organization, sku: "TESTSKU123")

      line_item =
        insert(:line_item,
          fulfillment: build(:fulfillment, order: original_order, organization: organization),
          location: origin_location,
          product_variant: product_variant,
          price: Decimal.new("10.00"),
          quantity: 1,
          organization: organization
        )

      insert(:reverse_fulfillment_line_item,
        reverse_fulfillment: reverse_fulfillment,
        line_item: line_item,
        returned_quantity: 1,
        organization: organization
      )

      _invoice_serie =
        insert(:invoice_serie,
          organization: organization,
          location: origin_location,
          invoice_type: "nf",
          status: "active",
          invoice_env: "dev",
          invoice_serie: "1",
          available_number: 100
        )

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "dev"
      }

      assert {:ok, _transferred_fiscal_invoice1} =
               perform_job(BuildTransferFiscalInvoice, args)

      fiscal_invoices1 =
        Rms.Fiscal.list_fiscal_invoice(organization.id,
          reverse_fulfillment_id: reverse_fulfillment.id
        )

      assert length(fiscal_invoices1) == 1
      first_invoice_id = Enum.at(fiscal_invoices1, 0).id

      assert {:ok, _transferred_fiscal_invoice2} =
               perform_job(BuildTransferFiscalInvoice, args)

      fiscal_invoices2 =
        Rms.Fiscal.list_fiscal_invoice(organization.id,
          reverse_fulfillment_id: reverse_fulfillment.id
        )

      assert length(fiscal_invoices2) == 1
      assert Enum.at(fiscal_invoices2, 0).id == first_invoice_id
    end
  end
end
