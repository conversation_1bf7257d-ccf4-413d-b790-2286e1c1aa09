defmodule Rms.Fiscal.Workers.IssueReturnFiscalInvoiceTest do
  use Rms.DataCase

  alias Rms.Commerce.Orders
  alias Rms.Fiscal

  describe "execute/1" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          sku: "530",
          product: product
        )

      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)

      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      # Create transaction first
      transaction = insert(:transaction, order: order, organization: organization)

      serie =
        insert(:invoice_serie,
          location: location,
          invoice_env: "prod",
          organization: organization,
          invoice_type: "nf",
          invoice_serie: "1",
          available_number: 1
        )

      insert(:location_tax,
        location: location,
        organization: organization
      )

      insert(:product_taxes,
        ncm: nil,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: organization
      )

      payment =
        insert(:payment, %{
          transaction: transaction,
          organization: organization,
          status: "settled",
          method: "credit_card"
        })

      fulfillment = insert(:fulfillment, order: order, organization: organization)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: organization,
          product_variant: product_variant,
          location: location,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        df_key: "CHAVE",
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        fulfillment_id: fulfillment.id,
        xml:
          "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240531777304000274655100000000011248668583\"><ide><cUF>35</cUF><cNF>24866858</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>510</serie><nNF>1</nNF><dhEmi>2024-05-03T11:23:16-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>3</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>31777304000274</CNPJ><xNome>Linus do Brasil Acessorios Ltda.</xNome><enderEmit><xLgr>Rua Joaquim Antunes</xLgr><nro>601</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05415011</CEP></enderEmit><IE>136469880111</IE><CRT>3</CRT></emit><det nItem=\"1\"><prod><cProd>530</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>2</qCom><vUnCom>176.00</vUnCom><vProd>352.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>2</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMS00><orig>0</orig><CST>00</CST><modBC>3</modBC><vBC>352.00</vBC><pICMS>18.00</pICMS><vICMS>63.36</vICMS></ICMS00></ICMS><PIS><PISAliq><CST>01</CST><vBC>288.64</vBC><pPIS>1.65</pPIS><vPIS>4.76</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>288.64</vBC><pCOFINS>7.60</pCOFINS><vCOFINS>21.94</vCOFINS></COFINSAliq></COFINS></imposto></det><det nItem=\"2\"><prod><cProd>528</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMS00><orig>0</orig><CST>00</CST><modBC>3</modBC><vBC>176.00</vBC><pICMS>18.00</pICMS><vICMS>31.68</vICMS></ICMS00></ICMS><PIS><PISAliq><CST>01</CST><vBC>144.32</vBC><pPIS>1.65</pPIS><vPIS>2.38</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>144.32</vBC><pCOFINS>7.60</pCOFINS><vCOFINS>10.97</vCOFINS></COFINSAliq></COFINS></imposto></det><total><ICMSTot><vBC>352.00</vBC><vICMS>63.36</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>352.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>4.76</vPIS><vCOFINS>21.94</vCOFINS><vOutro>0</vOutro><vNF>352.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>01</tPag><vPag>352.00</vPag></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240531777304000274655100000000011248668583|2|2|1|4f6bcda2ad955da5938358b7d48025bdce1ffd3f]]></qrCode><urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240531777304000274655100000000011248668583\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>dZzYfFyG/rsydMhSuN9RCCLmNpM=</DigestValue></Reference></SignedInfo><SignatureValue>OrGz9AZlc0jvIBgq+2IY82nEZakz37Lo43cnM5gCOIB/P/ayTPaZrWkXN1HdA325Q6M/vP+YZ+z9gwDE2cH+fH7MLyYrM7JxI4MdIIf00tZ7A/bM+AALimnOoJuaW6S8GO1ZxKqV509U/R2Z3IOLW/gP30LqLxqW3HcbveEc6ENsSMVKzJbVERtsb5tQwTfP09yWrVA4R7CMPkO8UCjnONIxran1VFXwEPDqsRn+rbaZFe90o74kQ+YBZ8pmFcngjiYhnWBtKnnY9kPEpoXzMzhY4n/m4lyDyBVDT6dsZEYZd9LALCas5KXKqrKTphT0Zew9gYRIDd8PFx+pcdXezA==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240531777304000274655100000000011248668583</chNFe><dhRecbto>2024-05-03T11:23:17-03:00</dhRecbto><nProt>135240000966684</nProt><digVal>dZzYfFyG/rsydMhSuN9RCCLmNpM=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>",
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(organization.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [:staff, product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:organization, location: [:address]]
        ])

      # Create reverse fulfillment with a line item
      order = Rms.Repo.preload(order, fulfillments: [line_items: [:product_variant]])

      new_fulfillment = List.first(order.fulfillments)
      _new_line_item = List.first(new_fulfillment.line_items)

      new_order =
        insert(:order,
          organization: organization,
          location: location,
          fulfillments: [new_fulfillment],
          staff: staff
        )

      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          line_items: [
            %{
              organization_id: organization.id,
              line_item_id: line_item.id,
              returned_quantity: 1,
              reason: "defective"
            }
          ]
        )

      # Then create iglu_credit with the reverse_fulfillment
      iglu_credit =
        insert(:iglu_credit, %{
          organization: organization,
          reverse_fulfillment_id: reverse_fulfillment.id,
          reason: "return",
          amount: Decimal.new("50")
        })

      # Create transaction first
      new_transaction = insert(:transaction, order: new_order, organization: organization)

      # Then create payment with the transaction
      new_payment =
        insert(:payment, %{
          transaction: new_transaction,
          organization: organization,
          status: "settled",
          method: "iglu_credit"
        })

      # Finally create iglu_credit_payment linking everything
      _iglu_credit_payment =
        insert(:iglu_credit_payment, %{
          payment: new_payment,
          iglu_credit: iglu_credit,
          organization: organization
        })

      {:ok,
       order: order,
       new_order: new_order,
       reverse_fulfillment: reverse_fulfillment,
       organization: organization,
       fiscal_invoice: fiscal_invoice}
    end

    test "returns the original order associated with a reverse fulfillment", %{
      order: _order,
      new_order: _new_order,
      reverse_fulfillment: _reverse_fulfillment,
      fiscal_invoice: _fiscal_invoice,
      organization: _organization
    } do
      # Rms.Fiscal.Workers.BuildReturnFiscalInvoice.execute(%{
      #   "reverse_fulfillment_id" => reverse_fulfillment.id,
      #   "organization_id" => organization.id
      # })

      # _fiscal_invoice =
      #   Rms.Fiscal.Workers.IssueReturnFiscalInvoice.execute(%{
      #     "reverse_fulfillment_id" => reverse_fulfillment.id,
      #     "organization_id" => organization.id,
      #     "return_vinco_object" => %{foo: "bar"}
      #   })

      assert true
    end
  end
end
