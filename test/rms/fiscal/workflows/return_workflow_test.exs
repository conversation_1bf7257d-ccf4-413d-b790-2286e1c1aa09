defmodule Rms.Fiscal.Workflows.ReturnWorkflowTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  alias Rms.Commerce.Orders
  alias Rms.Fiscal.Workflows.ReturnWorkflow
  alias Rms.Fiscal.Workers.BuildTransferFiscalInvoice

  describe "maybe_create_transfer_steps/4 with different locations" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      location_2 = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          sku: "530",
          product: product
        )

      # Create original order with location
      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)
      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      # Create fulfillment and line item for the original order
      fulfillment = insert(:fulfillment, order: order, organization: organization)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: organization,
          product_variant: product_variant,
          location: location,
          staff: staff
        )

      # Create new order with different location
      new_cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location_2,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      new_delivery_group =
        insert(:delivery_group,
          cart: new_cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: new_delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      new_cart =
        Rms.Repo.preload(new_cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      new_order_params = %{
        "cart_id" => new_cart.id,
        "location_id" => location_2.id
      }

      new_order_attrs = Orders.format_cart(new_cart, new_order_params)

      {:ok, new_order} =
        Orders.create_order(organization.id, new_order_attrs, allowed_location_ids: :all)

      # Create fulfillment and line item for the new order
      new_fulfillment = insert(:fulfillment, order: new_order, organization: organization)

      _new_line_item =
        insert(:line_item,
          fulfillment: new_fulfillment,
          organization: organization,
          product_variant: product_variant,
          location: location_2,
          staff: staff
        )

      # Create reverse fulfillment linking the orders through line items
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location_2,
          staff: staff,
          status: "pending"
        )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        line_item: line_item,
        reverse_fulfillment: reverse_fulfillment,
        returned_quantity: 1,
        reason: "defective"
      )

      # Create iglu_credit with the reverse_fulfillment
      iglu_credit =
        insert(:iglu_credit, %{
          organization: organization,
          reverse_fulfillment_id: reverse_fulfillment.id,
          reason: "return",
          amount: Decimal.new("50")
        })

      # Create transaction first
      new_transaction = insert(:transaction, order: new_order, organization: organization)

      # Then create payment with the transaction
      new_payment =
        insert(:payment, %{
          transaction: new_transaction,
          organization: organization,
          status: "settled",
          method: "iglu_credit"
        })

      # Finally create iglu_credit_payment linking everything
      _iglu_credit_payment =
        insert(:iglu_credit_payment, %{
          payment: new_payment,
          iglu_credit: iglu_credit,
          organization: organization
        })

      {:ok,
       organization: organization,
       order: order,
       new_order: new_order,
       location: location,
       location_2: location_2,
       reverse_fulfillment: reverse_fulfillment}
    end

    test "creates transfer steps when locations are different", %{
      organization: organization,
      location: location,
      reverse_fulfillment: reverse_fulfillment
    } do
      insert(:fiscal_settings,
        organization: organization,
        location: location,
        handle_transfer: true
      )

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "prod"
      }

      assert :ok = ReturnWorkflow.process(%{args: args})

      assert_enqueued(
        worker: BuildTransferFiscalInvoice,
        args: %{
          "reverse_fulfillment_id" => reverse_fulfillment.id,
          "organization_id" => organization.id
        }
      )
    end

    test "does not creates transfer steps when locations are different but handle_transfer is false",
         %{
           organization: organization,
           location: location,
           reverse_fulfillment: reverse_fulfillment
         } do
      insert(:fiscal_settings,
        organization: organization,
        location: location,
        handle_transfer: false
      )

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "prod"
      }

      assert :ok = ReturnWorkflow.process(%{args: args})

      refute_enqueued(worker: BuildTransferFiscalInvoice)
    end
  end

  describe "maybe_create_transfer_steps/4 with same location" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)
      staff = insert(:staff, organization: organization)

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          sku: "530",
          product: product
        )

      _fiscal_settings =
        insert(:fiscal_settings,
          organization: organization,
          location: location,
          handle_transfer: true
        )

      # Create original order
      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Orders.format_cart(cart, order_params)
      {:ok, order} = Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      # Create fulfillment and line item for the original order
      fulfillment = insert(:fulfillment, order: order, organization: organization)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: organization,
          product_variant: product_variant,
          location: location,
          staff: staff
        )

      # Create new order with same location
      new_cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          # Same location as original order
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      new_delivery_group =
        insert(:delivery_group,
          cart: new_cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: new_delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      new_cart =
        Rms.Repo.preload(new_cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      new_order_params = %{
        "cart_id" => new_cart.id,
        # Same location as original order
        "location_id" => location.id
      }

      new_order_attrs = Orders.format_cart(new_cart, new_order_params)

      {:ok, new_order} =
        Orders.create_order(organization.id, new_order_attrs, allowed_location_ids: :all)

      # Create fulfillment and line item for the new order
      new_fulfillment = insert(:fulfillment, order: new_order, organization: organization)

      _new_line_item =
        insert(:line_item,
          fulfillment: new_fulfillment,
          organization: organization,
          product_variant: product_variant,
          # Same location as original order
          location: location,
          staff: staff
        )

      # Create reverse fulfillment linking the orders through line items
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          status: "pending"
        )

      insert(:reverse_fulfillment_line_item,
        organization: organization,
        line_item: line_item,
        reverse_fulfillment: reverse_fulfillment,
        returned_quantity: 1,
        reason: "defective"
      )

      # Create iglu_credit with the reverse_fulfillment
      iglu_credit =
        insert(:iglu_credit, %{
          organization: organization,
          reverse_fulfillment_id: reverse_fulfillment.id,
          reason: "return",
          amount: Decimal.new("50")
        })

      # Create transaction first
      new_transaction = insert(:transaction, order: new_order, organization: organization)

      # Then create payment with the transaction
      new_payment =
        insert(:payment, %{
          transaction: new_transaction,
          organization: organization,
          status: "settled",
          method: "iglu_credit"
        })

      # Finally create iglu_credit_payment linking everything
      _iglu_credit_payment =
        insert(:iglu_credit_payment, %{
          payment: new_payment,
          iglu_credit: iglu_credit,
          organization: organization
        })

      {:ok,
       organization: organization,
       order: order,
       new_order: new_order,
       location: location,
       reverse_fulfillment: reverse_fulfillment}
    end

    test "does not create transfer steps when locations are the same", %{
      organization: organization,
      reverse_fulfillment: reverse_fulfillment
    } do
      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id,
        "env" => "prod"
      }

      assert :ok = ReturnWorkflow.process(%{args: args})

      refute_enqueued(worker: BuildTransferFiscalInvoice)
    end
  end
end
