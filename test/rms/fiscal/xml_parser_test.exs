defmodule Rms.Fiscal.XmlParserTest do
  use ExUnit.Case, async: true

  alias Rms.Fiscal.XmlParser

  # Basic Example NFe XML Structure (Simplified for testing core parsing)
  # Note: Real NFe XML is much more complex. This includes key structures.
  @sample_nfe_xml """
  <?xml version="1.0" encoding="UTF-8"?>
  <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
    <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
      <infNFe versao="4.00" Id="NFe352401...">
        <ide>
          <cUF>35</cUF>
          <cNF>12345678</cNF>
          <natOp>VENDA DE MERCADORIA</natOp>
          <mod>55</mod>
          <serie>1</serie>
          <nNF>98765</nNF>
          <dhEmi>2024-01-15T10:00:00-03:00</dhEmi>
          <tpNF>1</tpNF>
          <idDest>1</idDest>
          <cMunFG>3550308</cMunFG>
          <tpImp>1</tpImp>
          <tpEmis>1</tpEmis>
          <cDV>8</cDV>
          <tpAmb>1</tpAmb>
          <finNFe>1</finNFe>
          <indFinal>1</indFinal>
          <indPres>1</indPres>
          <procEmi>0</procEmi>
          <verProc>App v1.0</verProc>
        </ide>
        <emit>
          <CNPJ>11111111000111</CNPJ>
          <xNome>Empresa Emitente LTDA</xNome>
          <enderEmit>
            <xLgr>Rua Exemplo</xLgr>
            <nro>123</nro>
            <xBairro>Centro</xBairro>
            <cMun>3550308</cMun>
            <xMun>Sao Paulo</xMun>
            <UF>SP</UF>
            <CEP>01000000</CEP>
            <cPais>1058</cPais>
            <xPais>BRASIL</xPais>
          </enderEmit>
          <IE>111111111111</IE>
          <CRT>3</CRT>
        </emit>
        <dest>
          <CNPJ>22222222000122</CNPJ>
          <xNome>Empresa Destinataria SA</xNome>
          <enderDest>
            <xLgr>Avenida Principal</xLgr>
            <nro>456</nro>
            <xBairro>Bairro Novo</xBairro>
            <cMun>3550308</cMun>
            <xMun>Sao Paulo</xMun>
            <UF>SP</UF>
            <CEP>02000000</CEP>
            <cPais>1058</cPais>
            <xPais>BRASIL</xPais>
          </enderDest>
          <indIEDest>1</indIEDest>
          <IE>222222222222</IE>
        </dest>
        <det nItem="1">
          <prod>
            <cProd>PROD001</cProd>
            <cEAN>7890000000011</cEAN>
            <xProd>Produto Exemplo 1</xProd>
            <NCM>99999999</NCM>
            <CFOP>5102</CFOP>
            <uCom>UN</uCom>
            <qCom>2.0000</qCom>
            <vUnCom>100.00</vUnCom>
            <vProd>200.00</vProd>
            <cEANTrib>7890000000011</cEANTrib>
            <uTrib>UN</uTrib>
            <qTrib>2.0000</qTrib>
            <vUnTrib>100.00</vUnTrib>
            <indTot>1</indTot>
          </prod>
          <imposto>
            <ICMS>
              <ICMS00>
                <orig>0</orig>
                <CST>00</CST>
                <modBC>3</modBC>
                <vBC>200.00</vBC>
                <pICMS>18.00</pICMS>
                <vICMS>36.00</vICMS>
              </ICMS00>
            </ICMS>
            <PIS>
              <PISAliq>
                <CST>01</CST>
                <vBC>200.00</vBC>
                <pPIS>1.65</pPIS>
                <vPIS>3.30</vPIS>
              </PISAliq>
            </PIS>
            <COFINS>
              <COFINSAliq>
                <CST>01</CST>
                <vBC>200.00</vBC>
                <pCOFINS>7.60</pCOFINS>
                <vCOFINS>15.20</vCOFINS>
              </COFINSAliq>
            </COFINS>
          </imposto>
        </det>
         <det nItem="2">
          <prod>
            <cProd>PROD002</cProd>
            <xProd>Produto Exemplo 2</xProd>
            <NCM>88888888</NCM>
            <CFOP>5102</CFOP>
            <uCom>CX</uCom>
            <qCom>1.0000</qCom>
            <vUnCom>50.00</vUnCom>
            <vProd>50.00</vProd>
            <uTrib>CX</uTrib>
            <qTrib>1.0000</qTrib>
            <vUnTrib>50.00</vUnTrib>
            <indTot>1</indTot>
          </prod>
          <imposto>
             <ICMS><ICMS00><orig>0</orig><CST>00</CST><vBC>50.00</vBC><pICMS>18.00</pICMS><vICMS>9.00</vICMS></ICMS00></ICMS>
             <PIS><PISAliq><CST>01</CST><vBC>50.00</vBC><pPIS>1.65</pPIS><vPIS>0.83</vPIS></PISAliq></PIS>
             <COFINS><COFINSAliq><CST>01</CST><vBC>50.00</vBC><pCOFINS>7.60</pCOFINS><vCOFINS>3.80</vCOFINS></COFINSAliq></COFINS>
          </imposto>
        </det>
        <total>
          <ICMSTot>
            <vBC>250.00</vBC>
            <vICMS>45.00</vICMS>
            <vICMSDeson>0.00</vICMSDeson>
            <vFCPUFDest>0.00</vFCPUFDest>
            <vICMSUFDest>0.00</vICMSUFDest>
            <vICMSUFRemet>0.00</vICMSUFRemet>
            <vFCP>0.00</vFCP>
            <vBCST>0.00</vBCST>
            <vST>0.00</vST>
            <vFCPST>0.00</vFCPST>
            <vFCPSTRet>0.00</vFCPSTRet>
            <vProd>250.00</vProd>
            <vFrete>0.00</vFrete>
            <vSeg>0.00</vSeg>
            <vDesc>0.00</vDesc>
            <vII>0.00</vII>
            <vIPI>0.00</vIPI>
            <vIPIDevol>0.00</vIPIDevol>
            <vPIS>4.13</vPIS>
            <vCOFINS>19.00</vCOFINS>
            <vOutro>0.00</vOutro>
            <vNF>269.00</vNF> <!-- Note: Total NF value often includes taxes -->
          </ICMSTot>
        </total>
        <transp>
          <modFrete>9</modFrete>
        </transp>
        <pag>
          <detPag>
            <tPag>01</tPag>
            <vPag>269.00</vPag>
          </detPag>
        </pag>
        <infAdic>
          <infCpl>Informacoes Complementares</infCpl>
        </infAdic>
        <infRespTec>
            <CNPJ>46991783000113</CNPJ>
            <xContato>Iglu Tecnologia</xContato>
            <email><EMAIL></email>
            <fone>16991080570</fone>
        </infRespTec>
        <infNFeSupl>
            <qrCode><![CDATA[http://www.fazenda.sp.gov.br/nfce/consulta?p=...]]></qrCode>
            <urlChave>http://www.fazenda.sp.gov.br/nfce/consulta</urlChave>
        </infNFeSupl>
      </infNFe>
    </NFe>
    <protNFe versao="4.00">
        <infProt>
            <tpAmb>1</tpAmb>
            <verAplic>SP_NFE_PL_009_V4</verAplic>
            <chNFe>35240111111111000111550010000987651234567898</chNFe>
            <dhRecbto>2024-01-15T10:01:00-03:00</dhRecbto>
            <nProt>135240000000001</nProt>
            <digVal>...</digVal>
            <cStat>100</cStat>
            <xMotivo>Autorizado o uso da NF-e</xMotivo>
        </infProt>
    </protNFe>
  </nfeProc>
  """

  @sample_nfce_xml """
  <?xml version="1.0" encoding="UTF-8"?>
  <nfeProc xmlns="http://www.portalfiscal.inf.br/nfe" versao="4.00">
  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
  <infNFe versao="4.00" Id="NFe35240943253315000234652610000000011248668588">
  <ide>
  <cUF>35</cUF>
  <cNF>24866858</cNF>
  <natOp>Operacao interna</natOp>
  <mod>65</mod>
  <serie>261</serie>
  <nNF>1</nNF>
  <dhEmi>2024-09-26T17:19:49-03:00</dhEmi>
  <tpNF>1</tpNF>
  <idDest>1</idDest>
  <cMunFG>3550308</cMunFG>
  <tpImp>4</tpImp>
  <tpEmis>1</tpEmis>
  <cDV>8</cDV>
  <tpAmb>2</tpAmb>
  <finNFe>1</finNFe>
  <indFinal>1</indFinal>
  <indPres>1</indPres>
  <procEmi>0</procEmi>
  <verProc>iContNFe 1.22</verProc>
  </ide>
  <emit>
  <CNPJ>43253315000234</CNPJ>
  <xNome>ANDERSON CAIO SANTOS SILVA LTDA.</xNome>
  <enderEmit>
  <xLgr>Vitoria Regia</xLgr>
  <nro>148</nro>
  <xBairro>Cidade Universitaria</xBairro>
  <cMun>3550308</cMun>
  <xMun>São Paulo</xMun>
  <UF>SP</UF>
  <CEP>21941808</CEP>
  </enderEmit>
  <IE>138557519112</IE>
  <CRT>1</CRT>
  </emit>
  <det nItem="1">
  <prod>
  <cProd>113</cProd>
  <cEAN>SEM GTIN</cEAN>
  <xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd>
  <NCM>64029990</NCM>
  <CFOP>5102</CFOP>
  <uCom>UN</uCom>
  <qCom>1</qCom>
  <vUnCom>200.00</vUnCom>
  <vProd>200.00</vProd>
  <cEANTrib>SEM GTIN</cEANTrib>
  <uTrib>UN</uTrib>
  <qTrib>1</qTrib>
  <vUnTrib>200.00</vUnTrib>
  <indTot>1</indTot>
  </prod>
  <imposto>
  <ICMS>
  <ICMSSN102>
  <orig>0</orig>
  <CSOSN>400</CSOSN>
  </ICMSSN102>
  </ICMS>
  <PIS>
  <PISNT>
  <CST>08</CST>
  </PISNT>
  </PIS>
  <COFINS>
  <COFINSNT>
  <CST>08</CST>
  </COFINSNT>
  </COFINS>
  </imposto>
  </det>
  <total>
  <ICMSTot>
  <vBC>0.00</vBC>
  <vICMS>0.00</vICMS>
  <vICMSDeson>0</vICMSDeson>
  <vFCP>0.00</vFCP>
  <vBCST>0</vBCST>
  <vST>0</vST>
  <vFCPST>0</vFCPST>
  <vFCPSTRet>0</vFCPSTRet>
  <vProd>200.00</vProd>
  <vFrete>0</vFrete>
  <vSeg>0</vSeg>
  <vDesc>0.00</vDesc>
  <vII>0</vII>
  <vIPI>0</vIPI>
  <vIPIDevol>0</vIPIDevol>
  <vPIS>0.00</vPIS>
  <vCOFINS>0.00</vCOFINS>
  <vOutro>0</vOutro>
  <vNF>200.00</vNF>
  </ICMSTot>
  </total>
  <transp>
  <modFrete>9</modFrete>
  <vol>
  <qVol>1</qVol>
  </vol>
  </transp>
  <pag>
  <detPag>
  <tPag>01</tPag>
  <vPag>200.00</vPag>
  </detPag>
  </pag>
  </infNFe>
  <infNFeSupl>
  <qrCode>
  <![CDATA[ https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240943253315000234652610000000011248668588|2|2|1|bc552bf1f84ae73f39ae2901e020fc9e7b2186a5 ]]>
  </qrCode>
  <urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave>
  </infNFeSupl>
  <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
  <SignedInfo>
  <CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
  <SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
  <Reference URI="#NFe35240943253315000234652610000000011248668588">
  <Transforms>
  <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
  <Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
  </Transforms>
  <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
  <DigestValue>epbE7iPRe6FdZqZ9pofQh9hw2WQ=</DigestValue>
  </Reference>
  </SignedInfo>
  <SignatureValue>CowGrvs7NpFDpMsqCtMR7hTGgzL1kZbJESQApLXxV522AW/R9EmzNC+FadwVfl1rdcsgPcciDPjCIl2TuJpjxeuDF6Lwca1k2dFyEnWbL6I86h0l660+Hw1wqIQq+Vabr8um/uCtKD049tPWZbGHnKB50Jh2ceNXKp6fD68/MYZHb/24+/63Qqs1ET8IS2E4efvTKscPGh0LAYvJcvz3Y00TnqRMaquQ6Iv3rTiodNtng3rth4IrFHExLUarI1gSi9ozzlzawRIr4EmASjtoMYT3M57dB7skP8/5Djju0d7/etMcfsFvlR3DgXEeLM9zQfZfcYYwBgHx7rnUjkIC7w==</SignatureValue>
  <KeyInfo>
  <X509Data>
  <X509Certificate>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</X509Certificate>
  </X509Data>
  </KeyInfo>
  </Signature>
  </NFe>
  <protNFe versao="4.00">
  <infProt>
  <tpAmb>2</tpAmb>
  <verAplic>SP_NFCE_PL_009_V400</verAplic>
  <chNFe>35240943253315000234652610000000011248668588</chNFe>
  <dhRecbto>2024-09-26T17:19:49-03:00</dhRecbto>
  <nProt>135240002311843</nProt>
  <digVal>epbE7iPRe6FdZqZ9pofQh9hw2WQ=</digVal>
  <cStat>100</cStat>
  <xMotivo>Autorizado o uso da NF-e</xMotivo>
  </infProt>
  </protNFe>
  </nfeProc>
  """

  test "parses a valid NFe XML" do
    assert {:ok, parsed_data} = XmlParser.parse(@sample_nfe_xml)
    # Check top-level keys
    assert Map.has_key?(parsed_data, :ide)
    assert Map.has_key?(parsed_data, :emit)
    assert Map.has_key?(parsed_data, :dest)
    assert Map.has_key?(parsed_data, :det)
    assert Map.has_key?(parsed_data, :total)
    assert Map.has_key?(parsed_data, :transp)
    assert Map.has_key?(parsed_data, :pag)

    # Check IDE data
    assert parsed_data.ide.nNF == "98765"
    assert parsed_data.ide.serie == "1"
    assert parsed_data.ide.mod == "55"
    # Check IDE date (important for fallback if dhRecbto is missing)
    assert parsed_data.ide.dhEmi == "2024-01-15T10:00:00-03:00"

    # Check Emit data
    assert parsed_data.emit[:CNPJ] == "11111111000111"
    assert parsed_data.emit[:enderEmit][:UF] == "SP"

    # Check Dest data
    assert parsed_data.dest.xNome == "Empresa Destinataria SA"
    assert parsed_data.dest.enderDest.xLgr == "Avenida Principal"

    # Check Det data (items)
    assert length(parsed_data.det) == 2
    item1 = Enum.at(parsed_data.det, 0)
    assert item1.nItem == "1"
    assert item1.prod.cProd == "PROD001"
    assert item1.prod.qCom == "2.0000"
    assert item1.prod.vUnCom == "100.00"
    assert item1.imposto[:ICMS][:CSTCSOSN] == "00"
    assert item1.imposto[:PIS][:CST] == "01"

    item2 = Enum.at(parsed_data.det, 1)
    assert item2.nItem == "2"
    assert item2.prod.cProd == "PROD002"
    assert item2.prod.vProd == "50.00"

    # Check Total data
    assert parsed_data.total[:ICMStot][:vBC] == "250.00"
    assert parsed_data.total[:ICMStot][:vNF] == "269.00"

    # Check Payment data
    assert length(parsed_data.pag.detPag) == 1
    payment1 = Enum.at(parsed_data.pag.detPag, 0)
    assert payment1["tPag"] == "01"
    assert payment1["vPag"] == "269.00"

    # Check Transport data
    assert parsed_data.transp.modFrete == "9"

    # Check InfProt data (Added parsing)
    assert Map.has_key?(parsed_data, :infProt)
    assert parsed_data.infProt[:chNFe] == "35240111111111000111550010000987651234567898"
    assert parsed_data.infProt[:dhRecbto] == "2024-01-15T10:01:00-03:00"
    assert parsed_data.infProt[:cStat] == "100"

    # Check InfNFeSupl data (Added parsing)
    assert Map.has_key?(parsed_data, :infNFeSupl)
    assert parsed_data.infNFeSupl[:qrCode] == "http://www.fazenda.sp.gov.br/nfce/consulta?p=..."
    assert parsed_data.infNFeSupl[:urlChave] == "http://www.fazenda.sp.gov.br/nfce/consulta"
  end

  test "parses a valid NFC-e XML" do
    assert {:ok, parsed_data} = XmlParser.parse(@sample_nfce_xml)

    # Check top-level keys
    assert Map.has_key?(parsed_data, :ide)
    assert Map.has_key?(parsed_data, :emit)
    # NFCe usually doesn't have <dest> tag, check parser handles absence
    # assert Map.get(parsed_data, :dest) == %{} or is_nil(Map.get(parsed_data, :dest)) # Depending on implementation
    # Let's check specific field absence
    assert Map.get(parsed_data.dest, :xNome) == ""

    assert Map.has_key?(parsed_data, :det)
    assert Map.has_key?(parsed_data, :total)
    # Present in this sample, but not required
    assert Map.has_key?(parsed_data, :transp)
    assert Map.has_key?(parsed_data, :pag)
    assert Map.has_key?(parsed_data, :infProt)
    assert Map.has_key?(parsed_data, :infNFeSupl)

    # Check IDE data specific to NFC-e
    # NFC-e model
    assert parsed_data.ide.mod == "65"
    assert parsed_data.ide.serie == "261"
    assert parsed_data.ide.nNF == "1"
    # DANFE NFC-e print type
    assert parsed_data.ide.tpImp == "4"
    # Homologation environment
    assert parsed_data.ide.tpAmb == "2"
    assert parsed_data.ide.dhEmi == "2024-09-26T17:19:49-03:00"

    # Check Emit data
    assert parsed_data.emit[:CNPJ] == "43253315000234"
    assert parsed_data.emit[:enderEmit][:UF] == "SP"

    # Check Det data (items) - including Simples Nacional tax
    assert length(parsed_data.det) == 1
    item1 = Enum.at(parsed_data.det, 0)
    assert item1.nItem == "1"
    assert item1.prod.cProd == "113"
    assert item1.prod.qCom == "1"
    assert item1.prod.vUnCom == "200.00"
    # Check specific tax group (Simples Nacional CSOSN 400 in this case)
    # CSOSN
    assert item1.imposto[:ICMS][:CSTCSOSN] == "400"
    # PISNT
    assert item1.imposto[:PIS][:CST] == "08"
    # COFINSNT
    assert item1.imposto[:COFINS][:CST] == "08"

    # Check Total data
    assert parsed_data.total[:ICMStot][:vProd] == "200.00"
    assert parsed_data.total[:ICMStot][:vNF] == "200.00"

    # Check Payment data
    assert length(parsed_data.pag.detPag) == 1
    payment1 = Enum.at(parsed_data.pag.detPag, 0)
    assert payment1["tPag"] == "01"
    assert payment1["vPag"] == "200.00"

    # Check InfProt data
    assert parsed_data.infProt[:chNFe] == "35240943253315000234652610000000011248668588"
    assert parsed_data.infProt[:dhRecbto] == "2024-09-26T17:19:49-03:00"
    assert parsed_data.infProt[:cStat] == "100"

    # Check InfNFeSupl data
    assert String.contains?(
             parsed_data.infNFeSupl[:qrCode],
             "35240943253315000234652610000000011248668588"
           )

    assert parsed_data.infNFeSupl[:urlChave] ==
             "https://www.homologacao.nfce.fazenda.sp.gov.br/consulta"
  end
end
