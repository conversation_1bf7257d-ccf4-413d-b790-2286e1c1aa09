defmodule Rms.FiscalTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Errors
  alias Rms.Fiscal
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Errors.FiscalInvoiceError

  @valid_nfe_xml """
  <?xml version="1.0" encoding="UTF-8"?>
  <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
    <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
    <infNFe versao="4.00" Id="NFe352401...">
      <ide>
        <cUF>35</cUF>
        <cNF>12345678</cNF>
        <natOp>VENDA DE MERCADORIA</natOp>
        <mod>55</mod>
        <serie>1</serie>
        <nNF>98765</nNF>
        <dhEmi>2024-01-15T10:00:00-03:00</dhEmi>
        <tpNF>1</tpNF>
        <idDest>1</idDest>
        <cMunFG>3550308</cMunFG>
        <tpImp>1</tpImp>
        <tpEmis>1</tpEmis>
        <cDV>8</cDV>
        <tpAmb>1</tpAmb>
        <finNFe>1</finNFe>
        <indFinal>1</indFinal>
        <indPres>1</indPres>
        <procEmi>0</procEmi>
        <verProc>App v1.0</verProc>
      </ide>
      <!-- Other tags like emit, dest, det, total, etc. -->
    </infNFe>
  <infNFeSupl>
  <qrCode>
  <![CDATA[ https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240943253315000234652610000000011248668588|2|2|1|bc552bf1f84ae73f39ae2901e020fc9e7b2186a5 ]]>
  </qrCode>
  <urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave>
  </infNFeSupl>
  </NFe>
  <protNFe versao="4.00">
      <infProt>
          <tpAmb>1</tpAmb>
          <verAplic>SP_NFE_PL_009_V4</verAplic>
          <chNFe>35240111111111000111550010000987651234567898</chNFe>
          <dhRecbto>2024-01-15T10:01:00-03:00</dhRecbto>
          <nProt>135240000000001</nProt>
          <digVal>...</digVal>
          <cStat>100</cStat>
          <xMotivo>Autorizado o uso da NF-e</xMotivo>
      </infProt>
  </protNFe>
  </nfeProc>
  """

  describe "fiscal_invoice_errors" do
    test "create_fiscal_invoice_error/2 creates error with valid data" do
      organization = insert(:organization)
      fiscal_invoice = insert(:fiscal_invoice, organization: organization)

      error_attrs = %{
        reason: "Test error message",
        status: "E001",
        stacktrace: "Test stacktrace"
      }

      error_2_attrs = %{
        reason: "Test error message",
        status: "E002",
        stacktrace: "Test stacktrace"
      }

      assert {:ok, %FiscalInvoiceError{id: error_id_1}} =
               Errors.create_fiscal_invoice_error(fiscal_invoice, error_attrs)

      assert {:ok, %FiscalInvoiceError{id: error_id_2}} =
               Errors.create_fiscal_invoice_error(fiscal_invoice, error_2_attrs)

      error = Rms.Repo.get_by(FiscalInvoiceError, id: error_id_1)
      error_2 = Rms.Repo.get_by(FiscalInvoiceError, id: error_id_2)

      assert error.reason == "Test error message"
      assert error.status == "E001"
      assert error.stacktrace == "Test stacktrace"
      assert error.organization_id == fiscal_invoice.organization_id
      assert error.fiscal_invoice_id == fiscal_invoice.id

      assert error.attempt == 1
      assert error_2.attempt == 2
    end

    test "create_fiscal_invoice_error/2 with invalid data returns error changeset" do
      organization = insert(:organization)
      fiscal_invoice = insert(:fiscal_invoice, organization: organization)

      assert {:error, %Ecto.Changeset{}} =
               Errors.create_fiscal_invoice_error(fiscal_invoice, %{})
    end
  end

  describe "import_fulfillment_fiscal_invoice/3" do
    test "successfully imports a fiscal invoice for a paid order" do
      org = insert(:organization)
      payment = insert(:payment, status: "settled", organization: org)

      transaction =
        insert(:transaction,
          organization: org,
          payments: [payment],
          customer: build(:transaction_customer, organization: org)
        )

      order =
        insert(:order,
          organization: org,
          transaction: transaction,
          status: "paid"
        )

      fulfillment = insert(:fulfillment, organization: org, order: order)

      assert {:ok, %FiscalInvoice{df_key: df_key, invoice_number: 98_765} = invoice} =
               Fiscal.import_fulfillment_fiscal_invoice(org.id, fulfillment, @valid_nfe_xml)

      assert invoice.customer_id == transaction.customer.id
      assert df_key == "35240111111111000111550010000987651234567898"
      assert invoice.xml == @valid_nfe_xml
      assert invoice.fulfillment_id == fulfillment.id
      assert invoice.operation_type == "sale"
      assert invoice.service == "external"
      assert invoice.status == "authorized"
      assert invoice.authorized_at == ~U[2024-01-15 13:01:00Z]
      assert length(invoice.invoice_payments) == 1
      assert hd(invoice.invoice_payments).payment_id == payment.id
      assert invoice.metadata["import_source"] == "manual_xml"
    end
  end

  describe "create invoice_serie/2" do
    test "create a new invoice serie" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "prod",
        status: "active",
        location_id: loc.id
      }

      assert {:ok, _serie} = Fiscal.create_serie(org.id, attrs)
    end

    test "create two new invoice serie for diferent envs" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "prod",
        status: "active",
        location_id: loc.id
      }

      assert {:ok, _serie} = Fiscal.create_serie(org.id, attrs)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "dev",
        status: "active",
        location_id: loc.id
      }

      assert {:ok, _serie} = Fiscal.create_serie(org.id, attrs)
    end

    test "does not create when location does not belongs to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      loc = insert(:location, organization: another_org)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "prod",
        status: "active",
        location_id: loc.id
      }

      {:error, changeset} = Fiscal.create_serie(org.id, attrs)
      assert "does not exist" in errors_on(changeset).location
    end

    test "does not create when alredy exist a active serie for same type and location" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "prod",
        status: "active",
        location_id: loc.id
      }

      assert {:ok, _serie} = Fiscal.create_serie(org.id, attrs)

      attrs = %{
        invoice_type: "nf",
        invoice_serie: "2",
        available_number: 1,
        invoice_env: "prod",
        status: "active",
        location_id: loc.id
      }

      assert {:error, changeset} = Fiscal.create_serie(org.id, attrs)
      assert "has already been taken" in errors_on(changeset).invoice_type
    end
  end

  describe "list_invoice_serie/3" do
    test "list all serie from a organization" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        location: loc,
        organization: org
      )

      insert(:invoice_serie, invoice_env: "prod", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "prod",
        status: "inactive",
        location: loc,
        organization: org
      )

      series = Fiscal.list_invoice_serie(org.id)
      assert length(series) == 4
    end

    test "does not list series from other organization" do
      org2 = insert(:organization)
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        location: loc,
        organization: org
      )

      insert(:invoice_serie, invoice_env: "prod", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "prod",
        status: "inactive",
        location: loc,
        organization: org
      )

      series = Fiscal.list_invoice_serie(org2.id)
      assert Enum.empty?(series)
    end

    test "list all serie from a organization using some filters" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        location: loc,
        organization: org
      )

      insert(:invoice_serie, invoice_env: "prod", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "prod",
        status: "inactive",
        location: loc,
        organization: org
      )

      org2 = insert(:organization)
      loc2 = insert(:location, organization: org2)
      insert(:invoice_serie, invoice_env: "dev", location: loc2, organization: org2)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        location: loc2,
        organization: org2
      )

      insert(:invoice_serie, invoice_env: "prod", location: loc2, organization: org2)

      insert(:invoice_serie,
        invoice_env: "prod",
        status: "inactive",
        location: loc2,
        organization: org2
      )

      series = Fiscal.list_invoice_serie(org.id, query_params: [status: "active"])
      assert length(series) == 2
      assert Enum.all?(series, fn serie -> serie.status == "active" end)
    end
  end

  describe "get_invoice_serie!/2" do
    test "returns a invoice serie" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "dev",
        status: "inactive",
        location: loc,
        organization: org
      )

      serie = insert(:invoice_serie, invoice_env: "prod", location: loc, organization: org)

      insert(:invoice_serie,
        invoice_env: "prod",
        status: "inactive",
        location: loc,
        organization: org
      )

      assert s = Fiscal.get_invoice_serie!(org.id, serie.id)
      assert s.id == serie.id
      assert s.invoice_env == serie.invoice_env
      assert s.status == serie.status
    end

    test "raises Ecto.NoResultsError when serie does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.get_invoice_serie!(org.id, 1)
      end
    end

    test "raises Ecto.NoResultsError when serie does not belong to organization" do
      org = insert(:organization)
      another_org = insert(:organization)
      location = insert(:location, organization: another_org)

      serie =
        insert(:invoice_serie, invoice_env: "dev", location: location, organization: another_org)

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.get_invoice_serie!(org.id, serie.id)
      end
    end
  end

  describe "update_invoice_serie/2" do
    test "just update a status for a serie" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: location, organization: org)

      updated_attrs = %{
        status: "inactive",
        invoice_type: "nfc",
        invoice_env: "prod",
        invoice_serie: "123",
        available_number: 100,
        location_id: location.id + 1,
        organization_id: org.id + 1
      }

      assert {:ok, serie} = Fiscal.update_invoice_serie(serie, updated_attrs)
      assert updated_attrs.status == serie.status
      refute updated_attrs.invoice_type == serie.invoice_type
      refute updated_attrs.invoice_env == serie.invoice_env
      refute updated_attrs.invoice_serie == serie.invoice_serie
      refute updated_attrs.available_number == serie.available_number
      refute updated_attrs.location_id == serie.location_id
      refute updated_attrs.organization_id == serie.organization_id
    end
  end

  describe "create fiscal_invoice/2" do
    test "create a new fiscal invoice" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      number = serie.available_number
      assert {:ok, %FiscalInvoice{id: id}} = Fiscal.create_fiscal_invoice(org.id, attrs)
      invoice = Fiscal.get_fiscal_invoice!(org.id, id)
      assert number == invoice.invoice_number
    end

    test "create a new fiscal invoice sets default reference_at" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, %FiscalInvoice{id: id}} = Fiscal.create_fiscal_invoice(org.id, attrs)

      invoice =
        Fiscal.get_fiscal_invoice!(org.id, id)
        |> Rms.Repo.preload([:invoice_items, :invoice_payments])

      # Assert that reference_at is set (not nil)
      assert invoice.reference_at
      assert Enum.all?(invoice.invoice_items, & &1.reference_at)
      assert Enum.all?(invoice.invoice_payments, & &1.reference_at)
    end

    test "does not create when serie does not belongs to organization" do
      org2 = insert(:organization)
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)
      insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:error, changeset} = Fiscal.create_fiscal_invoice(org2.id, attrs)
      assert "does not exist" in errors_on(changeset).serie
    end

    test "does not create when payment does not belongs to organization" do
      org2 = insert(:organization)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      order = insert(:order, organization: org)

      order2 = insert(:order, organization: org2)
      transaction = insert(:transaction, order: order2, organization: org2)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org2)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org2)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.create_fiscal_invoice(org.id, attrs)
      end
    end

    test "does not create when line items does not belongs to organization" do
      org2 = insert(:organization)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      loc2 = insert(:location, organization: org2)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org2)
      transaction = insert(:transaction, order: order, organization: org2)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org2)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org2)

      product = insert(:product, organization: org2)
      pv = insert(:product_variant, product: product, organization: org2)

      fulfillment = insert(:fulfillment, order: order, organization: org2)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org2,
          product_variant: pv,
          location: loc2
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:error, changeset} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert [invoice_item_changeset] = changeset.changes.invoice_items
      assert "does not exist" in errors_on(invoice_item_changeset).line_item
    end

    test "create a new fiscal invoice with explicit reference_at" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      explicit_time = ~U[2024-01-01 10:00:00Z]

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id, reference_at: explicit_time}],
        invoice_items: [%{line_item_id: line_item.id, reference_at: explicit_time}],
        reference_at: explicit_time
      }

      assert {:ok, %FiscalInvoice{id: id}} = Fiscal.create_fiscal_invoice(org.id, attrs)

      invoice =
        Fiscal.get_fiscal_invoice!(org.id, id)
        |> Rms.Repo.preload([:invoice_items, :invoice_payments])

      assert invoice.reference_at == explicit_time
      assert Enum.all?(invoice.invoice_items, &(&1.reference_at == explicit_time))
      assert Enum.all?(invoice.invoice_payments, &(&1.reference_at == explicit_time))
    end

    test "does not create when payment is not settled" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "pending", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:error, changeset} = Fiscal.create_fiscal_invoice(org.id, attrs)

      [
        %Ecto.Changeset{
          errors: [invoice_payment: {"payment not settled", []}]
        }
      ] = changeset.changes.invoice_payments
    end
  end

  describe "import_fiscal_invoice/2" do
    setup do
      params = %{
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => "43210987654321098765432109876543210987654321",
        "status" => "pending",
        "authorized_at" => "2023-06-15T14:30:00Z",
        "canceled_at" => nil,
        "qr_code" =>
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==",
        "xml" =>
          "<nfe><infNFe Id=\"NFe43210987654321098765432109876543210987654321\"></infNFe></nfe>",
        "danfe" => "https://example.com/danfe/12345.pdf"
      }

      {:ok, params: params}
    end

    test "returns fiscal invoice for valid input", %{params: params} do
      order = insert(:order, status: "paid")
      transaction = insert(:transaction, order: order, organization: order.organization)

      insert(:payment,
        status: "settled",
        transaction: transaction,
        organization: order.organization
      )

      assert {:ok, fiscal_invoice} = Fiscal.import_fiscal_invoice(order, params)

      assert fiscal_invoice.operation_type == "sale"
      assert fiscal_invoice.service == "external"
      assert fiscal_invoice.invoice_number == 12_345
      assert fiscal_invoice.external_id == "EXT-98765"
      assert fiscal_invoice.df_key == "43210987654321098765432109876543210987654321"
      assert fiscal_invoice.status == "pending"
      assert fiscal_invoice.authorized_at == ~U[2023-06-15 14:30:00Z]
      assert fiscal_invoice.canceled_at == nil

      assert fiscal_invoice.qr_code ==
               "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg=="

      assert fiscal_invoice.xml ==
               "<nfe><infNFe Id=\"NFe43210987654321098765432109876543210987654321\"></infNFe></nfe>"

      assert fiscal_invoice.danfe == "https://example.com/danfe/12345.pdf"
    end

    test "returns error for unpaid order", %{params: params} do
      order = insert(:order, status: "pending")
      transaction = insert(:transaction, order: order, organization: order.organization)

      insert(:payment,
        status: "settled",
        transaction: transaction,
        organization: order.organization
      )

      assert {:error, changeset} = Fiscal.import_fiscal_invoice(order, params)
      assert "must be paid" in errors_on(changeset).order
    end
  end

  describe "list_fiscal_invoice/3" do
    test "list all fiscal invoice from a organization" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      create_invoice(org, loc, serie)
      create_invoice(org, loc, serie)

      series = Fiscal.list_fiscal_invoice(org.id)
      assert length(series) == 2
    end

    test "list all fiscal invoice from a reverse fullfilment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, location: loc, organization: org)
      reverse_fulfillment = insert(:reverse_fulfillment, organization: org)

      insert(:fiscal_invoice,
        organization: org,
        serie: serie1,
        reverse_fulfillment: reverse_fulfillment
      )

      insert(:fiscal_invoice, organization: org, serie: serie1)

      invoices =
        Fiscal.list_fiscal_invoice(org.id,
          query_params: [{"reverse_fulfillment_id", reverse_fulfillment.id}]
        )

      assert length(invoices) == 1

      assert Enum.all?(invoices, fn invoice ->
               invoice.reverse_fulfillment_id == reverse_fulfillment.id
             end)
    end

    test "does not list fiscal invoice from other organization" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, location: loc, organization: org)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      org2 = insert(:organization)
      loc2 = insert(:location, organization: org2)
      serie2 = insert(:invoice_serie, location: loc2, organization: org2)
      create_invoice(org2, loc2, serie2)
      create_invoice(org2, loc2, serie2)

      invoices = Fiscal.list_fiscal_invoice(org.id)
      assert length(invoices) == 2
      assert Enum.all?(invoices, fn invoice -> invoice.organization_id == org.id end)
    end

    test "list all serie from a organization using some filters" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      invoice_1 = create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      invoices =
        Fiscal.list_fiscal_invoice(org.id,
          query_params: [transaction_id: invoice_1.customer.transaction_id]
        )

      assert length(invoices) == 1

      assert Enum.all?(invoices, fn invoice ->
               invoice.customer.transaction_id == invoice_1.customer.transaction_id
             end)
    end

    test "list all fiscal invoice from a organization using operation_type filter" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      insert(:fiscal_invoice, operation_type: "transfer", serie: serie, organization: org)
      insert(:fiscal_invoice, operation_type: "return", serie: serie, organization: org)

      assert [%{operation_type: "transfer"}] =
               Fiscal.list_fiscal_invoice(org.id,
                 query_params: [operation_type: "transfer"]
               )
    end
  end

  describe "paginated_fiscal_invoice/2" do
    test "returns a paginated list of fiscal invoices" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      opts = [limit: 3]
      results = Fiscal.paginated_fiscal_invoice(org.id, opts)

      assert length(results.entries) == 3
      assert results.metadata.after
      assert Map.has_key?(results.metadata, :before)
    end

    test "paginates using the after cursor" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      first_page_opts = [limit: 5]
      first_page_results = Fiscal.paginated_fiscal_invoice(org.id, first_page_opts)

      second_page_opts = [limit: 5, after: first_page_results.metadata.after]
      second_page_results = Fiscal.paginated_fiscal_invoice(org.id, second_page_opts)

      first_page_ids_set =
        first_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      second_page_ids_set =
        second_page_results.entries
        |> Enum.map(& &1.id)
        |> MapSet.new()

      assert MapSet.disjoint?(second_page_ids_set, first_page_ids_set)
    end
  end

  describe "get_fiscal_invoice!/2" do
    test "returns a fical invoice" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)
      insert(:fiscal_invoice, serie: serie, organization: org)

      assert fiscal_invoice = Fiscal.get_fiscal_invoice!(org.id, fi.id)
      assert fi.id == fiscal_invoice.id
    end

    test "raises Ecto.NoResultsError when invoice does not exist" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.get_fiscal_invoice!(org.id, 1)
      end
    end

    test "raises Ecto.NoResultsError when invoice does not belong to organization" do
      org2 = insert(:organization)
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      insert(:fiscal_invoice, serie: serie, organization: org)
      insert(:fiscal_invoice, serie: serie, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.get_fiscal_invoice!(org2.id, serie.id)
      end
    end
  end

  describe "update_fiscal_invoice/2" do
    test "just update all avaliable fields for a invoice" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      serie2 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      updated_attrs =
        %{
          status: "authorized",
          external_id: "external 1",
          df_key: "def 1",
          qr_code: "code.com",
          xml: "xml.com",
          danfe: "danfe.com",
          metadata: %{meta: "meta.com"},
          authorized_at: DateTime.utc_now(),
          canceled_at: DateTime.utc_now(),
          organization_id: 3,
          serie_id: serie2.id
        }

      assert {:ok, invoice} = Fiscal.update_fiscal_invoice(fi, updated_attrs)
      assert updated_attrs.status == invoice.status
      assert updated_attrs.external_id == invoice.external_id
      assert updated_attrs.qr_code == invoice.qr_code
      assert updated_attrs.xml == invoice.xml
      assert updated_attrs.danfe == invoice.danfe
      assert updated_attrs.metadata == invoice.metadata

      refute updated_attrs.organization_id == invoice.organization_id
      refute updated_attrs.serie_id == invoice.serie_id
    end
  end

  describe "create_product_taxes/2" do
    test "create a all 4 variantion of a product_taxes" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      ncm = p1.ncm

      all_params_p1 = %{
        ncm: p1.ncm,
        uf: "SP",
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      ncm_params_p1 = %{
        ncm: p1.ncm,
        uf: nil,
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      uf_params_p1 = %{
        ncm: nil,
        uf: "SP",
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      all_nil_params_p1 = %{
        ncm: nil,
        uf: nil,
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm, uf: "SP"}} =
               Fiscal.create_product_taxes(org.id, all_params_p1)

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm, uf: nil}} =
               Fiscal.create_product_taxes(org.id, ncm_params_p1)

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: "SP"}} =
               Fiscal.create_product_taxes(org.id, uf_params_p1)

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: nil}} =
               Fiscal.create_product_taxes(org.id, all_nil_params_p1)
    end

    test "can't create a duplicated default for the same org" do
      org = insert(:organization)

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: nil}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: nil,
                 uf: nil,
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:error, changeset} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: nil,
                 uf: nil,
                 origin: "0",
                 cfop: "5103",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert "has already been taken" in errors_on(changeset).organization_id
    end

    test "can't create a duplicated uf nil for the same org" do
      org = insert(:organization)
      another_org = insert(:organization)

      p1 = insert(:product, ncm: "NCM_REPETIDO", organization: org)
      ncm1 = p1.ncm

      p2 = insert(:product, organization: org)
      ncm2 = p2.ncm

      p3 = insert(:product, ncm: "NCM_REPETIDO", organization: another_org)
      ncm3 = p3.ncm

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm2, uf: nil}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm2,
                 uf: nil,
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm1, uf: nil}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm1,
                 uf: nil,
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm3, uf: nil}} =
               Fiscal.create_product_taxes(another_org.id, %{
                 ncm: ncm3,
                 uf: nil,
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:error, changeset} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm1,
                 uf: nil,
                 origin: "0",
                 cfop: "5103",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert "a tax with this SKU, NCM and UF combination already exists" in errors_on(changeset).sku
    end

    test "can't create a duplicated ncm nil for the same org" do
      org = insert(:organization)
      another_org = insert(:organization)

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: "RJ"}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: nil,
                 uf: "RJ",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: "SP"}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: nil,
                 uf: "SP",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: nil, uf: "SP"}} =
               Fiscal.create_product_taxes(another_org.id, %{
                 ncm: nil,
                 uf: "SP",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:error, changeset} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: nil,
                 uf: "SP",
                 origin: "0",
                 cfop: "5103",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert "a tax with this SKU, NCM and UF combination already exists" in errors_on(changeset).sku
    end

    test "can't create a duplicated with ncm and  for the same org" do
      org = insert(:organization)
      another_org = insert(:organization)

      p1 = insert(:product, ncm: "SAME NCM", organization: org)
      ncm1 = p1.ncm

      p2 = insert(:product, ncm: "SAME NCM", organization: org)
      ncm2 = p2.ncm

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm1, uf: "RJ"}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm1,
                 uf: "RJ",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm1, uf: "SP"}} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm1,
                 uf: "SP",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:ok, %Rms.Fiscal.ProductTaxes{ncm: ^ncm2, uf: "SP"}} =
               Fiscal.create_product_taxes(another_org.id, %{
                 ncm: ncm2,
                 uf: "SP",
                 origin: "0",
                 cfop: "5102",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert {:error, changeset} =
               Fiscal.create_product_taxes(org.id, %{
                 ncm: ncm1,
                 uf: "SP",
                 origin: "0",
                 cfop: "5103",
                 cest: "400",
                 cst_icms: "400",
                 cst_pis: "08",
                 cst_cofins: "08",
                 icms_percentage: Decimal.new("0.1"),
                 fcp_percentage: Decimal.new("0.1"),
                 pis_percentage: Decimal.new("0.1"),
                 cofins_percentage: Decimal.new("0.1")
               })

      assert "a tax with this SKU, NCM and UF combination already exists" in errors_on(changeset).sku
    end
  end

  describe "list_product_taxes/3" do
    test "list all product taxes from a organization" do
      org = insert(:organization)

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        uf: "RJ",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        uf: "MT",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      pt = Fiscal.list_product_taxes(org.id)
      assert length(pt) == 3
    end

    test "does not list product taxes from other organization" do
      org2 = insert(:organization)
      org = insert(:organization)

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org2
      )

      insert(:product_taxes,
        uf: "RJ",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org2
      )

      insert(:product_taxes,
        uf: "MT",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      pt = Fiscal.list_product_taxes(org.id)
      assert length(pt) == 1
    end

    test "list all product taxes from a organization using some filters" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        uf: "RJ",
        origin: "1",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        uf: "MT",
        origin: "1",
        cest: "02",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      pts = Fiscal.list_product_taxes(org.id, query_params: [uf: "MT", origin: "1", cest: "02"])
      assert length(pts) == 1
      assert Enum.all?(pts, fn pt -> pt.uf == "MT" and pt.origin == "1" and pt.cest == "02" end)
    end
  end

  describe "update_product_taxes/2" do
    test "update a product tax" do
      org = insert(:organization)

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      updated_attrs = %{
        ncm: "MEU NCM",
        uf: "BR",
        origin: "6",
        cfop: "5123",
        cest: "500",
        cst_icms: "200",
        cst_pis: "80",
        cst_cofins: "80",
        icms_percentage: Decimal.new("10"),
        fcp_percentage: Decimal.new("20"),
        pis_percentage: Decimal.new("30"),
        cofins_percentage: Decimal.new("40")
      }

      assert {:ok, pt} = Fiscal.update_product_taxes(pt, updated_attrs)
      assert updated_attrs.ncm == pt.ncm
      assert updated_attrs.uf == pt.uf
      assert updated_attrs.origin == pt.origin
      assert updated_attrs.cfop == pt.cfop
      assert updated_attrs.cst_icms == pt.cst_icms
      assert updated_attrs.cst_pis == pt.cst_pis
      assert updated_attrs.cst_cofins == pt.cst_cofins
      assert updated_attrs.icms_percentage == pt.icms_percentage
      assert updated_attrs.fcp_percentage == pt.fcp_percentage
      assert updated_attrs.pis_percentage == pt.pis_percentage
      assert updated_attrs.cofins_percentage == pt.cofins_percentage
    end
  end

  describe "delete_product_taxes/1" do
    test "deletes a product_taxes" do
      org = insert(:organization)

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      assert {:ok, pt} = Fiscal.delete_product_taxes(pt)

      assert_raise Ecto.NoResultsError, fn ->
        Fiscal.get_product_taxes!(org.id, pt.id)
      end
    end
  end

  describe "get_taxes/4" do
    test "get de tax config for the sku + ncm + uf (7)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      pt1 =
        insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: "SP", origin: "0", organization: org)

      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "1", p1.ncm, "SP").id
    end

    test "get de tax config for the sku + ncm (6)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: "RJ", origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "1", p1.ncm, "SP").id
    end

    test "get de tax config for the sku + uf (5)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: "1", ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "1", p1.ncm, "SP").id
    end

    test "get de tax config for the sku (4)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "RJ", origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "1", p1.ncm, "SP").id
    end

    test "get de tax config for the ncm and uf when uf+ncm exist (3)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)

      pt1 =
        insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: "SP", origin: "0", organization: org)

      insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "2", p1.ncm, "SP").id
    end

    test "get de tax config for the ncm when uf+ncm  do not exist (2)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)

      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: nil, ncm: p1.ncm, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "2", p1.ncm, "SP").id
    end

    test "get de tax config for the uf when uf+ncm or ncm only do not exist (1)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: nil, ncm: nil, uf: "SP", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "2", p1.ncm, "SP").id
    end

    test "get de deafult tax config when it is the noly option (0)" do
      org = insert(:organization)

      p1 = insert(:product, organization: org)

      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: "1", ncm: nil, uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: "p1.ncm", uf: "RJ", origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: "p1.ncm", uf: nil, origin: "0", organization: org)
      insert(:product_taxes, sku: nil, ncm: nil, uf: "RJ", origin: "0", organization: org)
      pt1 = insert(:product_taxes, sku: nil, ncm: nil, uf: nil, origin: "0", organization: org)

      assert pt1.id == Fiscal.get_taxes(org.id, "2", p1.ncm, "SP").id
    end

    test "when ncm is nil or empty, but there's a matching product tax for the sku with non nil ncm, returns it" do
      org = insert(:organization)

      # Create product tax with specific SKU and non-nil NCM
      pt =
        insert(:product_taxes,
          sku: "12345",
          ncm: "12345678",
          uf: "SP",
          origin: "0",
          organization: org
        )

      # Test with nil NCM
      result = Fiscal.get_taxes(org.id, "12345", nil, "SP")
      assert tax = result
      assert tax.id == pt.id

      # Test with empty string NCM
      result = Fiscal.get_taxes(org.id, "12345", "", "SP")
      assert tax = result
      assert tax.id == pt.id
    end
  end

  describe "create_location_tax/2" do
    test "create location fiscal config" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        ie: "12345",
        crt: "3",
        name: "test",
        location_id: loc.id
      }

      assert {:ok, %Rms.Fiscal.LocationTax{}} =
               Fiscal.create_location_tax(org.id, attrs)
    end

    test "does not create location fiscal config for a location of another org" do
      org1 = insert(:organization)
      loc1 = insert(:location, organization: org1)

      org2 = insert(:organization)

      attrs = %{
        ie: "12345",
        crt: "3",
        name: "test",
        location_id: loc1.id
      }

      assert {:error, _} = Fiscal.create_location_tax(org2.id, attrs)
    end
  end

  describe "list_location_tax/3" do
    test "list all location fiscal config from a organization" do
      org1 = insert(:organization)
      loc = insert(:location, organization: org1)
      insert(:location_tax, location: loc, organization: org1)
      loc = insert(:location, organization: org1)
      insert(:location_tax, location: loc, organization: org1)
      loc = insert(:location, organization: org1)
      insert(:location_tax, location: loc, organization: org1)

      org2 = insert(:organization)
      loc = insert(:location, organization: org2)
      insert(:location_tax, location: loc, organization: org2)
      loc = insert(:location, organization: org2)
      insert(:location_tax, location: loc, organization: org2)
      loc = insert(:location, organization: org2)
      insert(:location_tax, location: loc, organization: org2)

      lts = Fiscal.list_location_tax(org1.id)
      assert length(lts) == 3
      assert Enum.all?(lts, fn lt -> lt.organization_id == org1.id end)
    end

    test "list all location fiscal config from a organization using some filters" do
      org1 = insert(:organization)
      loc = insert(:location, organization: org1)
      insert(:location_tax, ie: "1", location: loc, organization: org1)
      loc = insert(:location, organization: org1)
      insert(:location_tax, ie: "2", location: loc, organization: org1)
      loc = insert(:location, organization: org1)
      lt = insert(:location_tax, ie: "3", location: loc, organization: org1)

      [lts] = Fiscal.list_location_tax(org1.id, query_params: [ie: "3"])
      assert lt.id == lts.id
    end
  end

  describe "update_location_tax/2" do
    test "update a location fiscal config" do
      org1 = insert(:organization)
      loc = insert(:location, organization: org1)
      another_loc = insert(:location, organization: org1)
      lt = insert(:location_tax, location: loc, organization: org1)

      updated_attrs = %{
        ie: "IE",
        name: "NMAE",
        crt: "6",
        location_id: another_loc.id
      }

      assert {:ok, lt} = Fiscal.update_location_tax(lt, updated_attrs)
      assert updated_attrs.ie == lt.ie
      assert updated_attrs.name == lt.name
      assert updated_attrs.crt == lt.crt
      refute updated_attrs.location_id == lt.location_id
    end
  end

  describe "get_fiscal_settings/2" do
    test "returns location-specific settings when they exist" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:fiscal_settings,
        organization: org,
        environment: "prod"
      )

      loc_settings =
        insert(:fiscal_settings,
          organization: org,
          location: loc,
          environment: "dev"
        )

      result = Fiscal.get_fiscal_settings(org.id, loc.id)
      assert result.id == loc_settings.id
      assert result.environment == "dev"
    end

    test "returns organization settings when no location settings exist" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      org_settings =
        insert(:fiscal_settings,
          organization: org,
          environment: "prod"
        )

      result = Fiscal.get_fiscal_settings(org.id, loc.id)
      assert result.id == org_settings.id
      assert result.environment == "prod"
    end

    test "returns nil when no settings exist" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      refute Fiscal.get_fiscal_settings(org.id, loc.id)
    end

    test "does not return settings from another organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)
      loc = insert(:location, organization: org2)

      insert(:fiscal_settings,
        organization: org2,
        location: loc,
        environment: "prod"
      )

      refute Fiscal.get_fiscal_settings(org1.id, loc.id)
    end
  end

  describe "update_fiscal_settings/2" do
    test "updates an existing fiscal settings record" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      settings =
        insert(:fiscal_settings,
          organization: org,
          location: loc,
          handle_sale: true,
          handle_return: true,
          handle_transfer: true,
          environment: "dev"
        )

      attrs = %{
        handle_sale: false,
        handle_return: false,
        handle_transfer: false,
        environment: "prod"
      }

      assert {:ok, updated} = Fiscal.update_fiscal_settings(settings, attrs)
      assert updated.handle_sale == false
      assert updated.handle_return == false
      assert updated.handle_transfer == false
      assert updated.environment == "prod"
    end

    test "fails with invalid environment" do
      org = insert(:organization)
      settings = insert(:fiscal_settings, organization: org)

      attrs = %{
        handle_sale: true,
        environment: "invalid"
      }

      assert {:error, changeset} = Fiscal.update_fiscal_settings(settings, attrs)
      assert "is invalid" in errors_on(changeset).environment
    end

    test "does not allow updating organization_id" do
      org = insert(:organization)
      other_org = insert(:organization)
      settings = insert(:fiscal_settings, organization: org)

      attrs = %{
        organization_id: other_org.id,
        handle_sale: true
      }

      assert {:ok, updated} = Fiscal.update_fiscal_settings(settings, attrs)
      assert updated.organization_id == org.id
    end

    test "does not allow updating location_id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      other_loc = insert(:location, organization: org)

      settings = insert(:fiscal_settings, organization: org, location: loc)

      attrs = %{
        location_id: other_loc.id,
        handle_sale: true
      }

      assert {:ok, updated} = Fiscal.update_fiscal_settings(settings, attrs)
      assert updated.location_id == loc.id
    end
  end

  describe "create_location_fiscal_settings/3" do
    test "creates settings for a location" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        handle_sale: true,
        handle_return: false,
        handle_transfer: true,
        environment: "prod"
      }

      assert {:ok, settings} = Fiscal.create_location_fiscal_settings(org.id, loc.id, attrs)
      assert settings.organization_id == org.id
      assert settings.location_id == loc.id
      assert settings.environment == "prod"
      assert settings.handle_sale == true
      assert settings.handle_return == false
      assert settings.handle_transfer == true
    end

    test "fails with invalid environment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: true,
        environment: "invalid"
      }

      assert {:error, changeset} = Fiscal.create_location_fiscal_settings(org.id, loc.id, attrs)
      assert "is invalid" in errors_on(changeset).environment
    end

    test "prevents duplicate location settings" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: true,
        environment: "prod"
      }

      assert {:ok, _settings} = Fiscal.create_location_fiscal_settings(org.id, loc.id, attrs)
      assert {:error, changeset} = Fiscal.create_location_fiscal_settings(org.id, loc.id, attrs)
      assert "has already been taken" in errors_on(changeset).location_id
    end

    test "fails when location belongs to another organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)
      loc = insert(:location, organization: org2)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: true,
        environment: "prod"
      }

      assert {:error, changeset} = Fiscal.create_location_fiscal_settings(org1.id, loc.id, attrs)
      assert "does not exist" in errors_on(changeset).location
    end
  end

  describe "create_organization_fiscal_settings/2" do
    test "creates organization-wide settings" do
      org = insert(:organization)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: false,
        environment: "dev"
      }

      assert {:ok, settings} = Fiscal.create_organization_fiscal_settings(org.id, attrs)
      assert settings.organization_id == org.id
      refute settings.location_id
      assert settings.environment == "dev"
      assert settings.handle_sale == true
      assert settings.handle_return == true
      assert settings.handle_transfer == false
    end

    test "fails with invalid environment" do
      org = insert(:organization)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: true,
        environment: "invalid"
      }

      assert {:error, changeset} = Fiscal.create_organization_fiscal_settings(org.id, attrs)
      assert "is invalid" in errors_on(changeset).environment
    end

    test "prevents duplicate organization settings" do
      org = insert(:organization)

      attrs = %{
        handle_sale: true,
        handle_return: true,
        handle_transfer: true,
        environment: "prod"
      }

      assert {:ok, _settings} = Fiscal.create_organization_fiscal_settings(org.id, attrs)
      assert {:error, changeset} = Fiscal.create_organization_fiscal_settings(org.id, attrs)
      assert "has already been taken" in errors_on(changeset).organization_id
    end
  end

  describe "get_sale_fiscal_invoice_by_order_id/2" do
    test "returns the fiscal invoice for a given order id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      order = insert(:order, location: loc, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          organization: org,
          operation_type: "sale"
        )

      [retrived_fiscal_invoice] = Fiscal.get_sale_fiscal_invoice_by_order_id(org.id, order.id)

      assert %FiscalInvoice{} = retrived_fiscal_invoice
      assert retrived_fiscal_invoice.id == fiscal_invoice.id
    end

    test "returns only the sale fiscal invoice for a given order id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      order = insert(:order, location: loc, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          organization: org,
          operation_type: "sale"
        )

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          organization: org,
          operation_type: "return"
        )

      retrived_fiscal_invoices = Fiscal.get_sale_fiscal_invoice_by_order_id(org.id, order.id)

      refute Enum.empty?(retrived_fiscal_invoices)
      assert [_] = retrived_fiscal_invoices
      assert %FiscalInvoice{} = retrived_fiscal_invoices |> hd
      assert retrived_fiscal_invoices |> hd |> Map.get(:id) == fiscal_invoice.id
    end

    test "doesnt returns the return fiscal invoice for a given order id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      order = insert(:order, location: loc, organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          organization: org,
          operation_type: "return"
        )

      retrived_fiscal_invoices = Fiscal.get_sale_fiscal_invoice_by_order_id(org.id, order.id)

      assert [] = retrived_fiscal_invoices
    end

    test "raises when no fiscal invoice exists for the order" do
      # Create a real order to get a valid ID format
      org = insert(:organization)
      loc = insert(:location, organization: org)
      order = insert(:order, location: loc, organization: org)

      # Use a different order ID that we know won't have a fiscal invoice
      other_order_id = order.id + 1

      retrived_fiscal_invoices =
        Fiscal.get_sale_fiscal_invoice_by_order_id(org.id, other_order_id)

      assert [] = retrived_fiscal_invoices
    end
  end

  defp create_invoice(org, loc, serie) do
    order = insert(:order, organization: org)
    transaction = insert(:transaction, order: order, organization: org)
    payment_1 = insert(:payment, status: "settled", transaction: transaction, organization: org)
    payment_2 = insert(:payment, status: "settled", transaction: transaction, organization: org)
    payment_3 = insert(:payment, status: "settled", transaction: transaction, organization: org)

    transaction_customer =
      insert(:transaction_customer, transaction: transaction, organization: org)

    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    fulfillment = insert(:fulfillment, order: order, organization: org)

    line_item =
      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

    attrs = %{
      operation_type: "sale",
      serie_id: serie.id,
      customer_id: transaction_customer.id,
      invoice_payments: [
        %{payment_id: payment_1.id},
        %{payment_id: payment_2.id},
        %{payment_id: payment_3.id}
      ],
      invoice_items: [%{line_item_id: line_item.id}]
    }

    {:ok, transaction} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

    Rms.Repo.preload(transaction, [:customer])
  end

  describe "partial_create_product_taxes/2" do
    test "handles successful bulk inserts" do
      org = insert(:organization)

      entries = [
        params_for(:product_taxes,
          organization_id: org.id,
          ncm: "12345678",
          uf: "SP",
          origin: "0",
          cfop: "5102",
          cst_icms: "00",
          icms_percentage: Decimal.new("0.18")
        ),
        params_for(:product_taxes,
          organization_id: org.id,
          ncm: "87654321",
          uf: "RJ",
          origin: "0",
          cfop: "5102",
          cst_icms: "00",
          icms_percentage: Decimal.new("0.18")
        )
      ]

      result = Fiscal.partial_create_product_taxes(entries)

      assert result.success == 2
      assert result.errors == []
      assert result.total == 2
    end

    test "handles partial failures with validation errors" do
      org = insert(:organization)

      valid_params =
        params_for(:product_taxes,
          organization_id: org.id,
          ncm: "12345678",
          uf: "SP",
          origin: "0",
          cfop: "5102",
          cst_icms: "00",
          icms_percentage: Decimal.new("0.18")
        )

      invalid_params =
        params_for(:product_taxes,
          organization_id: org.id,
          ncm: "invalid",
          uf: "RJ",
          origin: "0",
          cfop: "5102",
          cst_icms: nil,
          icms_percentage: Decimal.new("0.18")
        )

      entries = [valid_params, invalid_params]

      result = Fiscal.partial_create_product_taxes(entries)
      assert result.success == 1
      assert length(result.errors) == 1
      assert result.total == 2

      [{failed_entry, errors}] = result.errors
      assert failed_entry.ncm == "invalid"
      assert errors.cst_icms == ["can't be blank"]
    end

    test "handles empty input" do
      result = Fiscal.partial_create_product_taxes([])

      assert result.success == 0
      assert result.errors == []
      assert result.total == 0
    end

    test "handles duplicate entries by updating them" do
      org = insert(:organization)

      # First insert
      existing =
        insert(:product_taxes,
          organization: org,
          ncm: "12345678",
          uf: "SP",
          origin: "0",
          cfop: "5102",
          cst_icms: "00",
          icms_percentage: Decimal.new("0.18")
        )

      # Update duplicate with different values
      entries = [
        params_for(:product_taxes,
          organization_id: org.id,
          ncm: existing.ncm,
          uf: existing.uf,
          origin: "1",
          cfop: "5103",
          cst_icms: "10",
          icms_percentage: Decimal.new("0.20")
        )
      ]

      result = Fiscal.partial_create_product_taxes(entries)

      assert result.success == 1
      assert result.errors == []
      assert result.total == 1

      # Verify record was updated (or rather, not updated based on test output)
      updated = Repo.get(Rms.Fiscal.ProductTaxes, existing.id)
      assert updated.origin == existing.origin
      assert updated.cfop == existing.cfop
      assert updated.cst_icms == existing.cst_icms
      assert Decimal.equal?(updated.icms_percentage, existing.icms_percentage)
    end
  end
end
