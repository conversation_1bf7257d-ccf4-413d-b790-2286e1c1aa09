defmodule Rms.Integration.ShopifyOrderReturnTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify.Orders
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  # Make sure mocks are verified after each test
  setup :verify_on_exit!

  # Sample Shopify order response for our test
  @shopify_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Online Store"},
        "createdAt" => "2024-12-05T18:44:51Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "150.0"}},
        "customer" => %{
          "email" => "<EMAIL>",
          "firstName" => "Test",
          "id" => "gid://shopify/Customer/12345678",
          "lastName" => "Customer",
          "phone" => "+55123456789"
        },
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "PICK_UP"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" => "https://cdn.shopify.com/image1.jpg"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 2,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "75.0"}
                      }
                    },
                    "productTitle" => "Test Product",
                    "sku" => "SKU123",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/test-variant-id"
                    },
                    "variantTitle" => "Small"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/test-order-id",
        "name" => "#1001",
        "retailLocation" => nil,
        "shippingAddress" => %{
          "address1" => "Test Street, 123",
          "address2" => "Apt 456",
          "city" => "Test City",
          "country" => "Test Country",
          "firstName" => "Test",
          "lastName" => "Customer",
          "phone" => "+55123456789",
          "provinceCode" => "TC",
          "zip" => "12345-678"
        },
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "150.0"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2025-03-27T03:19:58Z"
      }
    }
  }

  # Sample Shopify order response with one missing variant mapping
  @shopify_order_response_missing_variant %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Online Store"},
        "createdAt" => "2024-12-06T10:00:00Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "200.0"}},
        "customer" => %{
          "email" => "<EMAIL>",
          "firstName" => "Missing",
          "id" => "gid://shopify/Customer/87654321",
          "lastName" => "Variant",
          "phone" => "+55987654321"
        },
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "SHIPPING"},
              "lineItems" => %{
                "nodes" => [
                  # This variant exists
                  %{
                    "image" => %{
                      "url" => "https://cdn.shopify.com/image1.jpg"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "75.0"}
                      }
                    },
                    "productTitle" => "Test Product Existing",
                    "sku" => "SKU123",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/test-variant-id"
                    },
                    "variantTitle" => "Small"
                  },
                  # This variant is missing
                  %{
                    "image" => %{
                      "url" => "https://cdn.shopify.com/image_missing.jpg"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "125.0"}
                      }
                    },
                    "productTitle" => "Test Product Missing",
                    "sku" => "SKU_MISSING",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/MISSING-VARIANT-ID"
                    },
                    "variantTitle" => "Large"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/test-order-id-missing",
        "name" => "#1002",
        "retailLocation" => nil,
        "shippingAddress" => %{
          "address1" => "Missing Street, 987",
          "address2" => "",
          "city" => "Missing City",
          "country" => "Missing Country",
          "firstName" => "Missing",
          "lastName" => "Variant",
          "phone" => "+55987654321",
          "provinceCode" => "MC",
          "zip" => "98765-432"
        },
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "200.0"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2025-03-28T04:20:00Z"
      }
    }
  }

  setup %{conn: conn} do
    # Create organization and user
    organization = insert(:organization)
    user = insert(:user, organization: organization)

    # Create locations
    location = insert(:location, organization: organization)
    ecomm_location = insert(:location, organization: organization, name: "e-commerce")

    # Create staff
    staff = insert(:staff, organization: organization)

    # Assign location to user
    insert(:location_users, user: user, location: location, organization: organization)
    insert(:location_users, user: user, location: ecomm_location, organization: organization)

    # Set up location mappings for Shopify
    insert(:location_mapping,
      external_id: "gid://shopify/Location/74913710390",
      source: "shopify",
      location: ecomm_location,
      organization: organization
    )

    # Create Shopify credentials
    insert(:shopify_credential, %{
      shop: "test-shop.myshopify.com",
      credential: "test-credential",
      organization: organization
    })

    # Create product variant and mapping
    product = insert(:product, organization: organization)
    product_variant = insert(:product_variant, product: product, organization: organization)

    insert(:product_variant_mapping,
      product_variant: product_variant,
      external_id: "gid://shopify/ProductVariant/test-variant-id",
      organization: organization,
      source: "shopify"
    )

    # Authenticate the connection
    conn = authenticate_conn(conn, user)

    # Return test context
    {:ok,
     %{
       conn: conn,
       organization: organization,
       user: user,
       location: location,
       ecomm_location: ecomm_location,
       staff: staff,
       product_variant: product_variant
     }}
  end

  describe "shopify order return integration" do
    test "should allow returning items from an imported shopify order", %{
      conn: conn,
      organization: organization,
      staff: staff
    } do
      # Set up the mock for Shopify API
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:get_order!, fn _client, "gid://shopify/Order/test-order-id", _selection, _opts ->
        {:ok, @shopify_order_response}
      end)

      # Import the order from Shopify
      {:ok, imported_order} =
        Orders.import_order(organization.id, "gid://shopify/Order/test-order-id")

      # Verify the order was imported correctly
      assert imported_order.external_id == "gid://shopify/Order/test-order-id"
      assert imported_order.name == "#1001"
      assert imported_order.total_price == Decimal.new("150.0")

      # Get the first line item from the imported order
      imported_order = Rms.Repo.preload(imported_order, fulfillments: [:line_items])
      fulfillment = List.first(imported_order.fulfillments)
      line_item = List.first(fulfillment.line_items)

      # Check that the line item was imported correctly
      assert line_item.quantity == 2
      assert line_item.price == Decimal.new("75.0")

      # Process a return through the BFF controller
      return_attrs = %{
        "cart_id" => nil,
        "customer_id" => imported_order.customer_id,
        "location_id" => imported_order.location_id,
        "staff_id" => staff.id,
        "returned_line_items" => [
          %{
            "line_item_id" => line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      # Send the return request
      response =
        conn
        |> post(~p"/bff/create_order", return_attrs)
        |> json_response(:created)

      # Verify the return was processed successfully
      assert response["success"] == true

      # Verify a reverse fulfillment was created
      reverse_fulfillment =
        Rms.Repo.get_by(Rms.Commerce.Fulfillments.ReverseFulfillment,
          customer_id: imported_order.customer_id
        )

      assert reverse_fulfillment

      # Verify the reverse fulfillment line item
      reverse_item =
        Rms.Repo.get_by(Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem,
          line_item_id: line_item.id,
          reverse_fulfillment_id: reverse_fulfillment.id
        )

      assert reverse_item
      assert reverse_item.returned_quantity == 1
      assert reverse_item.reason == "defective"

      # Verify that credit was generated for the return
      credit =
        Rms.Repo.get_by(Rms.Finance.IgluCredit,
          customer_id: imported_order.customer_id,
          status: "available"
        )

      assert credit
      # Price of one item
      assert credit.amount == Decimal.new("75.0")

      # Reload the order to verify its updated status
      updated_order = Rms.Commerce.Orders.get_order!(organization.id, imported_order.id)

      updated_order =
        Rms.Repo.preload(updated_order,
          fulfillments: [line_items: [:reverse_fulfillment_line_items]]
        )

      # Verify that the line item in the updated order reflects the return
      updated_line_item = List.first(List.first(updated_order.fulfillments).line_items)
      assert updated_line_item.quantity == 2
      assert List.first(updated_line_item.reverse_fulfillment_line_items).returned_quantity == 1
    end

    test "should allow returning items from an imported shopify order with missing variant mapping",
         %{
           conn: conn,
           organization: organization,
           staff: staff
         } do
      # Define the Shopify ID for the order with a missing variant
      shopify_order_id = "gid://shopify/Order/test-order-id-missing"

      # Set up the mock for Shopify API to return the response with a missing variant
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:get_order!, fn _client, ^shopify_order_id, _selection, _opts ->
        {:ok, @shopify_order_response_missing_variant}
      end)

      # Import the order from Shopify
      {:ok, imported_order} =
        Orders.import_order(organization.id, shopify_order_id)

      # Preload fulfillments and line items
      imported_order = Rms.Repo.preload(imported_order, fulfillments: [:line_items])
      fulfillment = List.first(imported_order.fulfillments)
      # Find the line item with the missing variant by SKU
      missing_line_item = Enum.find(fulfillment.line_items, &(&1.sku == "SKU_MISSING"))

      # Verify the missing line item was imported correctly
      assert missing_line_item
      assert missing_line_item.product_variant_id == nil
      assert missing_line_item.product_name == "Test Product Missing"
      assert missing_line_item.price == Decimal.new("125.0")
      assert missing_line_item.quantity == 1

      # Prepare return attributes for the item with missing variant mapping
      return_attrs = %{
        "cart_id" => nil,
        "customer_id" => imported_order.customer_id,
        "location_id" => imported_order.location_id,
        "staff_id" => staff.id,
        "returned_line_items" => [
          %{
            # Use the ID of the line item with nil product_variant_id
            "line_item_id" => missing_line_item.id,
            "returned_quantity" => 1,
            "reason" => "changed_mind"
          }
        ]
      }

      # Send the return request via the BFF controller
      response =
        conn
        |> post(~p"/bff/create_order", return_attrs)
        |> json_response(:created)

      # Verify the return was processed successfully
      assert response["success"] == true

      # Verify a reverse fulfillment was created
      reverse_fulfillment =
        Rms.Repo.get_by!(Rms.Commerce.Fulfillments.ReverseFulfillment,
          customer_id: imported_order.customer_id
        )

      # Verify the reverse fulfillment line item for the missing variant item
      reverse_item =
        Rms.Repo.get_by!(Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem,
          line_item_id: missing_line_item.id,
          reverse_fulfillment_id: reverse_fulfillment.id
        )

      assert reverse_item.returned_quantity == 1
      assert reverse_item.reason == "changed_mind"

      # Verify that credit was generated for the return (based on the price paid)
      credit =
        Rms.Repo.get_by!(Rms.Finance.IgluCredit,
          customer_id: imported_order.customer_id,
          status: "available",
          reverse_fulfillment_id: reverse_fulfillment.id
        )

      # Credit should match the price paid
      assert credit.amount == Decimal.new("125.0")

      # Reload the order to verify its updated status
      updated_order = Rms.Commerce.Orders.get_order!(organization.id, imported_order.id)

      updated_order =
        Rms.Repo.preload(updated_order,
          fulfillments: [line_items: [:reverse_fulfillment_line_items]]
        )

      # Verify that the line item in the updated order reflects the return
      updated_fulfillment = List.first(updated_order.fulfillments)

      updated_missing_line_item =
        Enum.find(updated_fulfillment.line_items, &(&1.id == missing_line_item.id))

      assert List.first(updated_missing_line_item.reverse_fulfillment_line_items).returned_quantity ==
               1
    end

    test "should allow returning items and purchasing in the same transaction", %{
      conn: conn,
      organization: organization,
      staff: staff,
      product_variant: product_variant
    } do
      # Set up the mock for Shopify API
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:get_order!, fn _client, "gid://shopify/Order/test-order-id", _selection, _opts ->
        {:ok, @shopify_order_response}
      end)

      # Import the order from Shopify
      {:ok, imported_order} =
        Orders.import_order(organization.id, "gid://shopify/Order/test-order-id")

      # Get the first line item from the imported order
      imported_order = Rms.Repo.preload(imported_order, fulfillments: [:line_items])
      fulfillment = List.first(imported_order.fulfillments)
      line_item = List.first(fulfillment.line_items)

      # Create a new cart for the customer
      cart =
        insert(:cart,
          organization: organization,
          customer: Rms.Customers.get_customer!(organization.id, imported_order.customer_id),
          staff: staff,
          total_price: Decimal.new("100"),
          total_items_list_price: Decimal.new("100"),
          total_items_selling_price: Decimal.new("100")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      # Add item to cart
      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 1,
        list_price: "100",
        selling_price: "100",
        total_price: "100"
      )

      # Preload the cart fully
      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [product_variant: :product]]
        ])

      # Create an order with both return and new items
      order_attrs = %{
        "cart_id" => cart.id,
        "location_id" => imported_order.location_id,
        "staff_id" => staff.id,
        "customer_id" => imported_order.customer_id,
        "returned_line_items" => [
          %{
            "line_item_id" => line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      # Send the order request
      order_response =
        conn
        |> post(~p"/bff/create_order", order_attrs)
        |> json_response(:created)

      # Verify the order was created successfully
      assert order = order_response["order"]
      assert order["customer"]["id"] == imported_order.customer_id

      # Verify transaction was created
      assert transaction_id = order["transaction"]["id"]

      transaction = Rms.Repo.get!(Rms.Finance.Transaction, transaction_id)
      transaction = Rms.Repo.preload(transaction, :payments)

      # There should be a payment using the return credit
      credit_payment = Enum.find(transaction.payments, fn p -> p.method == "return_credit" end)
      assert credit_payment
      assert Decimal.equal?(credit_payment.amount, Decimal.new("75.0"))

      # Reload the original order to verify its updated status
      updated_order = Rms.Commerce.Orders.get_order!(organization.id, imported_order.id)

      updated_order =
        Rms.Repo.preload(updated_order,
          fulfillments: [line_items: [:reverse_fulfillment_line_items]]
        )

      # Verify that the line item in the updated order reflects the return
      updated_line_item = List.first(List.first(updated_order.fulfillments).line_items)
      assert updated_line_item.quantity == 2
      assert List.first(updated_line_item.reverse_fulfillment_line_items).returned_quantity == 1
    end
  end
end
