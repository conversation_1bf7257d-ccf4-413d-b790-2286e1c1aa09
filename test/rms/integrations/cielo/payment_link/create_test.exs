defmodule Rms.Integrations.Cielo.PaymentLink.CreateTest do
  use Rms.DataCase

  alias Rms.Integrations.Cielo.PaymentLink.Create

  import Mox
  setup :verify_on_exit!

  describe "execute/2" do
    test "return the link and a external_id" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price_with_addons: "100")

      insert(:cielo_credential, organization: org)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 0, fn _, _ ->
        {:ok,
         %{
           "access_token" => "a",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_payment_link, 1, fn _, _ ->
        {:ok,
         %{
           "id" => "d27a1b27-b31a-43e4-b8c4-b6deed0da3c3",
           "type" => "Asset",
           "orderNumber" => "123456",
           "name" => "Hortifruti",
           "description" => "Hortaliças da semana",
           "showDescription" => false,
           "price" => 6000,
           "weight" => 3000,
           "shipping" => %{
             "type" => "FixedAmount",
             "name" => "frete da loja",
             "price" => 1000,
             "package" => %{
               "weight" => 3000,
               "dimension" => %{}
             },
             "pickupData" => %{
               "pickupDelay" => 0
             }
           },
           "sku" => "teste",
           "softDescriptor" => "Pedido1234",
           "expirationDate" => "2027-06-19 00:00:00",
           "createdDate" => "2024-11-10 19:27:00",
           "maxNumberOfInstallments" => 1,
           "quantity" => 12,
           "shortUrl" => "https://cielolink.com.br/3YKhhR1",
           "links" => [
             %{
               "method" => "GET",
               "rel" => "self",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             },
             %{
               "method" => "PUT",
               "rel" => "update",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             },
             %{
               "method" => "DELETE",
               "rel" => "delete",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             }
           ]
         }}
      end)

      assert {:ok, "https://cielolink.com.br/3YKhhR1", "d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"} ==
               Create.execute(org.id, order, nil)
    end

    test "updates the existing credential" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price_with_addons: "100")

      insert(:cielo_credential,
        organization: org,
        updated_at: DateTime.add(DateTime.utc_now(), -6, :hour)
      )

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 1, fn _, _ ->
        {:ok,
         %{
           "access_token" => "new_token",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_payment_link, 1, fn _, _ ->
        {:ok,
         %{
           "id" => "d27a1b27-b31a-43e4-b8c4-b6deed0da3c3",
           "type" => "Asset",
           "orderNumber" => "123456",
           "name" => "Hortifruti",
           "description" => "Hortaliças da semana",
           "showDescription" => false,
           "price" => 6000,
           "weight" => 3000,
           "shipping" => %{
             "type" => "FixedAmount",
             "name" => "frete da loja",
             "price" => 1000,
             "package" => %{
               "weight" => 3000,
               "dimension" => %{}
             },
             "pickupData" => %{
               "pickupDelay" => 0
             }
           },
           "sku" => "teste",
           "softDescriptor" => "Pedido1234",
           "expirationDate" => "2027-06-19 00:00:00",
           "createdDate" => "2024-11-10 19:27:00",
           "maxNumberOfInstallments" => 1,
           "quantity" => 12,
           "shortUrl" => "https://cielolink.com.br/3YKhhR1",
           "links" => [
             %{
               "method" => "GET",
               "rel" => "self",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             },
             %{
               "method" => "PUT",
               "rel" => "update",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             },
             %{
               "method" => "DELETE",
               "rel" => "delete",
               "href" =>
                 "https://cieloecommerce.cielo.com.br/api/public/v1/products/d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"
             }
           ]
         }}
      end)

      assert {:ok, "https://cielolink.com.br/3YKhhR1", "d27a1b27-b31a-43e4-b8c4-b6deed0da3c3"} ==
               Create.execute(org.id, order, nil)
    end
  end
end
