defmodule Rms.Integrations.Cielo.PaymentLink.UpdateTest do
  use Rms.DataCase

  alias Rms.Finance
  alias Rms.Finance.Payment
  alias Rms.Finance.Transaction

  alias Rms.Commerce
  alias Rms.Commerce.Orders.Order

  alias Rms.Integrations.Cielo.PaymentLink.Update

  import Mox
  setup :verify_on_exit!

  describe "execute/2" do
    test "updates payment status, metadata and order" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price_with_addons: "100")

      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          organization: org
        )

      epr =
        insert(:external_payment_reference,
          partner: "cielo-link",
          payment: payment,
          organization: org
        )

      insert(:cielo_credential, organization: org)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 0, fn _, _ ->
        {:ok,
         %{
           "access_token" => "a",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :get_payment_link, 1, fn _, _ ->
        {:ok,
         %{
           "$id" => "1",
           "productId" => "231f3111-0dc8-4305-a705-f3c1ed7d4c3c",
           "createdDate" => "2024-11-11T10:32:58.907",
           "orders" => [
             %{
               "$id" => "2",
               "orderNumber" => "e8a56c28b1324f1d93a4",
               "createdDate" => "2024-11-11T13:48:33.043",
               "payment" => %{
                 "$id" => "3",
                 "price" => "20000",
                 "numberOfPayments" => 3,
                 "createdDate" => "2024-11-11T13:48:33.043",
                 "status" => "Paid"
               },
               "links" => [
                 %{
                   "$id" => "4",
                   "method" => "GET",
                   "rel" => "self",
                   "href" =>
                     "https://cieloecommerce.cielo.com.br/api/public/v2/orders/e8a56c28b1324f1d93a4"
                 }
               ]
             }
           ]
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :get_payment_details, 1, fn _, order_id ->
        assert order_id == "e8a56c28b1324f1d93a4"

        {:ok,
         %{
           "payment" => %{
             "status" => "Paid",
             "nsu" => "123456",
             "authorizationCode" => "789012",
             "brand" => "Visa",
             "cardMaskedNumber" => "411111****1111",
             "numberOfPayments" => 1,
             "type" => "CreditCard"
           }
         }}
      end)

      assert {:ok, :updated_payment} =
               Update.execute(%{"external_id" => epr.external_id})

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      payment = Finance.get_payment!(payment.organization_id, payment.id)
      assert payment.status == "settled"
      assert payment.metadata["payment_details"]["nsu"] == "123456"
      assert payment.metadata["payment_details"]["aut"] == "789012"
      assert payment.metadata["payment_details"]["card_brand"] == "Visa"
      assert payment.metadata["payment_details"]["card_number"] == "411111****1111"
      assert payment.metadata["payment_details"]["installments"] == 1
      assert payment.metadata["payment_details"]["method"] == "credit_card"

      assert %Transaction{status: "done"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "paid"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "updates payment status and order when ther is more than one order" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price_with_addons: "100")

      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          organization: org
        )

      epr =
        insert(:external_payment_reference,
          partner: "cielo-link",
          payment: payment,
          organization: org
        )

      insert(:cielo_credential, organization: org)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 0, fn _, _ ->
        {:ok,
         %{
           "access_token" => "a",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :get_payment_link, 1, fn _, _ ->
        {:ok,
         %{
           "$id" => "1",
           "productId" => "231f3111-0dc8-4305-a705-f3c1ed7d4c3c",
           "createdDate" => "2024-11-11T10:32:58.907",
           "orders" => [
             %{
               "$id" => "2",
               "orderNumber" => "e8a56c28b1324f1d93a4",
               "createdDate" => "2024-11-11T13:48:33.043",
               "payment" => %{
                 "$id" => "3",
                 "price" => "20000",
                 "numberOfPayments" => 3,
                 "createdDate" => "2024-11-11T13:48:33.043",
                 "status" => "Paid"
               },
               "links" => [
                 %{
                   "$id" => "4",
                   "method" => "GET",
                   "rel" => "Invalid",
                   "href" =>
                     "https://cieloecommerce.cielo.com.br/api/public/v2/orders/e8a56c28b1324f1d93a4"
                 }
               ]
             },
             %{
               "$id" => "2",
               "orderNumber" => "e8a56c28b1324f1d93a4",
               "createdDate" => "2024-11-11T13:48:33.043",
               "payment" => %{
                 "$id" => "3",
                 "price" => "300",
                 "numberOfPayments" => 3,
                 "createdDate" => "2024-11-11T13:48:33.043",
                 "status" => "Paid"
               },
               "links" => [
                 %{
                   "$id" => "4",
                   "method" => "GET",
                   "rel" => "self",
                   "href" =>
                     "https://cieloecommerce.cielo.com.br/api/public/v2/orders/e8a56c28b1324f1d93a4"
                 }
               ]
             },
             %{
               "$id" => "2",
               "orderNumber" => "e8a56c28b1324f1d93a4",
               "createdDate" => "2024-11-11T13:48:33.043",
               "payment" => %{
                 "$id" => "3",
                 "price" => "13700",
                 "numberOfPayments" => 3,
                 "createdDate" => "2024-11-11T13:48:33.043",
                 "status" => "Paid"
               },
               "links" => [
                 %{
                   "$id" => "4",
                   "method" => "GET",
                   "rel" => "self",
                   "href" =>
                     "https://cieloecommerce.cielo.com.br/api/public/v2/orders/e8a56c28b1324f1d93a4"
                 }
               ]
             }
           ]
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :get_payment_details, 1, fn _, order_id ->
        assert order_id == "e8a56c28b1324f1d93a4"

        {:ok,
         %{
           "payment" => %{
             "status" => "Paid",
             "nsu" => "123456",
             "authorizationCode" => "789012",
             "brand" => "Visa",
             "cardMaskedNumber" => "411111****1111",
             "numberOfPayments" => 1,
             "type" => "CreditCard"
           }
         }}
      end)

      assert {:ok, :updated_payment} =
               Update.execute(%{"external_id" => epr.external_id})

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "done"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "paid"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "does not update payment and order" do
      org = insert(:organization)
      order = insert(:order, organization: org, total_price_with_addons: "100")

      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          organization: org
        )

      epr =
        insert(:external_payment_reference,
          partner: "cielo-link",
          payment: payment,
          organization: org
        )

      insert(:cielo_credential, organization: org)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 0, fn _, _ ->
        {:ok,
         %{
           "access_token" => "a",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      Mox.expect(Rms.Integrations.Cielo.Mock, :get_payment_link, 1, fn _, _ ->
        {:ok,
         %{
           "$id" => "1",
           "productId" => "231f3111-0dc8-4305-a705-f3c1ed7d4c3c",
           "createdDate" => "2024-11-11T10:32:58.907",
           "orders" => [
             %{
               "$id" => "2",
               "orderNumber" => "e8a56c28b1324f1d93a4",
               "createdDate" => "2024-11-11T13:48:33.043",
               "payment" => %{
                 "$id" => "3",
                 "price" => "20000",
                 "numberOfPayments" => 3,
                 "createdDate" => "2024-11-11T13:48:33.043",
                 "status" => "Invalid"
               },
               "links" => [
                 %{
                   "$id" => "4",
                   "method" => "GET",
                   "rel" => "self",
                   "href" =>
                     "https://cieloecommerce.cielo.com.br/api/public/v2/orders/e8a56c28b1324f1d93a4"
                 }
               ]
             }
           ]
         }}
      end)

      assert {:error, :no_paid_orders} =
               Update.execute(%{"external_id" => epr.external_id})

      assert %Payment{status: "pending"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)
               |> Rms.Repo.preload([:payments, :order])

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end
  end
end
