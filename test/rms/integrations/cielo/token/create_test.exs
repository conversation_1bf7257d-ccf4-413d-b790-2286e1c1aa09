defmodule Rms.Integrations.Cielo.Token.CreateTest do
  use Rms.DataCase

  alias Rms.Integrations.Cielo.Token.Create

  import Mox
  setup :verify_on_exit!

  describe "execute/2" do
    test "return the existing credential" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      original_credential = insert(:cielo_credential, organization: org)

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 0, fn _, _ ->
        {:ok,
         %{
           "access_token" => "a",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      assert {:ok, credential} = Create.execute(org.id, loc.id)

      assert credential.token == original_credential.token
      assert credential.updated_at == original_credential.updated_at
    end

    test "updates the existing credential" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      original_credential =
        insert(:cielo_credential,
          organization: org,
          updated_at: DateTime.add(DateTime.utc_now(), -6, :hour)
        )

      Mox.expect(Rms.Integrations.Cielo.Mock, :create_credential, 1, fn _, _ ->
        {:ok,
         %{
           "access_token" => "new_token",
           "token_type" => "bearer",
           "expires_in" => 1199
         }}
      end)

      assert {:ok, credential} = Create.execute(org.id, loc.id)

      assert credential.token != original_credential.token
      assert credential.updated_at != original_credential.updated_at

      assert credential.token == "new_token"
    end
  end
end
