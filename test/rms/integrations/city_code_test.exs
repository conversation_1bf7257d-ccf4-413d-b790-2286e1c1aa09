defmodule Rms.Integrations.CityCodeTest do
  use Rms.DataCase
  import Mox

  alias Rms.Integrations.CityCode
  alias Rms.CitiesDatas
  alias Rms.Repo

  @ibge_mock Application.compile_env(:rms, :ibge_client, Rms.Integrations.Ibge.Mock)
  @viacep_mock Application.compile_env(:rms, :viacep_client, Rms.Integrations.Viacep.Mock)

  setup :verify_on_exit!

  describe "get_address_city_code/3" do
    test "returns city code when city data exists and is up to date" do
      city_data = %{
        ibge_id: 3_550_308,
        name: "São Paulo",
        uf_name: "São Paulo",
        uf_initials: "<PERSON>",
        updated_at: DateTime.utc_now()
      }

      {:ok, _} = CitiesDatas.upsert_city_data(city_data)

      assert {:ok, "3550308"} = CityCode.get_address_city_code("São Paulo", "SP", nil)
    end

    test "updates city data when it's outdated" do
      old_city_data = %{
        ibge_id: 3_550_308,
        name: "São Paulo",
        uf_name: "São Paulo",
        uf_initials: "SP"
      }

      {:ok, city} = CitiesDatas.upsert_city_data(old_city_data)

      old_timestamp =
        DateTime.utc_now()
        |> DateTime.add(-2, :day)
        |> DateTime.truncate(:second)

      Ecto.Changeset.change(city, updated_at: old_timestamp)
      |> Repo.update!()

      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      assert {:ok, "3550308"} = CityCode.get_address_city_code("São Paulo", "SP", nil)
    end

    test "updates city data when it's empty" do
      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      assert {:ok, "3550308"} = CityCode.get_address_city_code("São Paulo", "SP", nil)
    end

    test "falls back to postal code when city is not found" do
      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      Mox.expect(@viacep_mock, :get_info_by_postal_code, fn "01306010" ->
        {:ok, %{"ibge" => "3550308"}}
      end)

      assert {:ok, "3550308"} = CityCode.get_address_city_code("Unknown City", "XX", "01306010")
    end

    test "returns error when city is not found and postal code is invalid" do
      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      Mox.expect(@viacep_mock, :get_info_by_postal_code, fn "00000000" ->
        {:error, "not found"}
      end)

      assert {:error, _} = CityCode.get_address_city_code("Unknown City", "XX", "00000000")
    end

    test "returns error when city is not found and postal code is nil" do
      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      assert {:error, _} = CityCode.get_address_city_code("Unknown City", "XX", nil)
    end

    test "returns error when IBGE API fails" do
      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:error, "API error"}
      end)

      assert {:error, _} = CityCode.get_address_city_code("São Paulo", "SP", nil)
    end
  end
end
