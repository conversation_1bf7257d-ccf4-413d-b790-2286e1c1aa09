defmodule Rms.Integrations.Customers.CustomerEndpointTest do
  use Rms.DataCase, async: true

  alias Rms.Integrations.Customers.CustomerEndpoint

  describe "changeset/2" do
    test "accepts endpoint with valid URL" do
      attrs = %{
        endpoint: "https://example.com",
        unique_on: ["id"],
        headers: [["Content-Type", "application/json"]],
        active: true
      }

      changeset = CustomerEndpoint.changeset(%CustomerEndpoint{}, attrs)
      assert changeset.valid?
    end

    test "rejects endpoint with invalid scheme" do
      attrs = %{
        endpoint: "ftp://invalid_scheme.com",
        unique_on: ["id"],
        headers: [["Content-Type", "application/json"]],
        active: true
      }

      changeset = CustomerEndpoint.changeset(%CustomerEndpoint{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).endpoint
    end

    test "rejects endpoint with IP address as host" do
      attrs = %{
        endpoint: "http://***********",
        unique_on: ["id"],
        headers: [["Content-Type", "application/json"]],
        active: true
      }

      changeset = CustomerEndpoint.changeset(%CustomerEndpoint{}, attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).endpoint
    end

    test "validates presence of required fields" do
      attrs = %{
        endpoint: nil,
        unique_on: nil,
        headers: nil,
        active: nil
      }

      changeset = CustomerEndpoint.changeset(%CustomerEndpoint{}, attrs)
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).endpoint
      assert "can't be blank" in errors_on(changeset).unique_on
      assert "can't be blank" in errors_on(changeset).headers
      assert "can't be blank" in errors_on(changeset).active
    end
  end
end
