defmodule Rms.Integrations.CustomersTest do
  use Rms.DataCase

  alias Rms.Integrations.Customers

  import Rms.Factory

  describe "import_customers/3" do
    setup do
      organization = insert(:organization)
      {:ok, organization: organization}
    end

    test "imports customer with no address", %{organization: organization} do
      customer_data = %{
        name: "<PERSON>",
        email: "<EMAIL>",
        document: "12345678901",
        document_type: "CPF",
        primary_phone_number: "1234567890"
      }

      assert {:ok, customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert customer.name == "<PERSON>"
      assert customer.email == "<EMAIL>"
      assert customer.document == "12345678901"
      assert customer.document_type == "CPF"
      assert customer.primary_phone_number == "1234567890"
      assert customer.addresses == []
    end

    test "imports customer with address", %{organization: organization} do
      customer_data = %{
        name: "<PERSON>",
        email: "<EMAIL>",
        document: "98765432109",
        document_type: "CPF",
        primary_phone_number: "9876543210",
        addresses: [
          %{
            street: "Main St",
            number: "123",
            city_name: "New York",
            state: "NY",
            zip: "10001000",
            country_name: "USA",
            receiver_name: "<PERSON>"
          }
        ]
      }

      assert {:ok, customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert customer.name == "Jane Smith"
      assert customer.email == "<EMAIL>"
      assert customer.document == "98765432109"
      assert customer.document_type == "CPF"
      assert customer.primary_phone_number == "9876543210"
      assert length(customer.addresses) == 1

      assert [address] = customer.addresses
      assert address.street == "Main St"
      assert address.number == "123"
      assert address.city_name == "New York"
      assert address.state == "NY"
      assert address.zip == "10001000"
      assert address.country_name == "USA"
    end

    test "updates customer with address", %{organization: organization} do
      existing_customer =
        insert(:customer, organization: organization, email: "<EMAIL>")

      customer_data = %{
        name: "Updated Name",
        email: "<EMAIL>",
        document_type: "CPF",
        primary_phone_number: "1122334455",
        addresses: [
          %{
            street: "New St",
            number: "456",
            city_name: "Los Angeles",
            state: "CA",
            zip: "90001000",
            country_name: "USA",
            receiver_name: "Updated Name"
          }
        ]
      }

      assert {:ok, updated_customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "Updated Name"
      assert updated_customer.email == "<EMAIL>"
      assert updated_customer.document_type == "CPF"
      assert updated_customer.primary_phone_number == "1122334455"
      assert length(updated_customer.addresses) == 1

      [address] = updated_customer.addresses
      assert address.street == "New St"
      assert address.number == "456"
      assert address.city_name == "Los Angeles"
      assert address.state == "CA"
      assert address.zip == "90001000"
      assert address.country_name == "USA"
      assert address.receiver_name == "Updated Name"
    end

    test "updates customer without address", %{organization: organization} do
      existing_customer =
        insert(:customer, organization: organization, email: "<EMAIL>")

      insert(:address, customer: existing_customer)

      customer_data = %{
        name: "No Address Update",
        email: "<EMAIL>",
        document_type: "CPF",
        primary_phone_number: "5566778899"
      }

      assert {:ok, updated_customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "No Address Update"
      assert updated_customer.email == "<EMAIL>"
      assert updated_customer.document_type == "CPF"
      assert updated_customer.primary_phone_number == "5566778899"
      # The existing address should remain
      assert length(updated_customer.addresses) == 1
    end

    test "updates customer with different address", %{organization: organization} do
      existing_customer =
        insert(:customer, organization: organization, email: "<EMAIL>")

      insert(:address, customer: existing_customer, street: "Old St", number: "789")

      customer_data = %{
        name: "Different Address",
        email: "<EMAIL>",
        addresses: [
          %{
            street: "New Different St",
            number: "101",
            city_name: "Chicago",
            state: "IL",
            zip: "60601000",
            country_name: "USA",
            receiver_name: "Different Address"
          }
        ]
      }

      assert {:ok, updated_customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "Different Address"
      assert length(updated_customer.addresses) == 2

      new_address = Enum.find(updated_customer.addresses, &(&1.street == "New Different St"))
      assert new_address.number == "101"
      assert new_address.city_name == "Chicago"
      assert new_address.state == "IL"
      assert new_address.zip == "60601000"
      assert new_address.country_name == "USA"
      assert new_address.receiver_name == "Different Address"

      old_address = Enum.find(updated_customer.addresses, &(&1.street == "Old St"))
      assert old_address.number == "789"
    end

    test "updates customer with same address", %{organization: organization} do
      existing_customer = insert(:customer, organization: organization, email: "<EMAIL>")

      existing_address =
        insert(:address, customer: existing_customer, street: "Same St", number: "999")

      customer_data = %{
        name: "Same Address",
        email: "<EMAIL>",
        addresses: [
          %{
            street: existing_address.street,
            number: existing_address.number,
            city_name: existing_address.city_name,
            state: existing_address.state,
            zip: existing_address.zip,
            country_name: existing_address.country_name,
            receiver_name: existing_address.receiver_name
          }
        ]
      }

      assert {:ok, updated_customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "Same Address"
      assert length(updated_customer.addresses) == 1

      [address] = updated_customer.addresses
      assert address.id == existing_address.id
      assert address.street == "Same St"
      assert address.number == "999"
      assert address.receiver_name == "John Doe"
    end

    test "does not create new address when db has nil values and import has empty strings", %{
      organization: organization
    } do
      existing_customer =
        insert(:customer,
          organization: organization,
          email: "<EMAIL>",
          document: nil,
          primary_phone_number: nil
        )

      existing_address =
        insert(:address,
          customer: existing_customer,
          street: "Something",
          number: nil,
          city_name: nil,
          state: nil,
          zip: nil,
          country_name: nil,
          receiver_name: nil,
          complement: nil
        )

      customer_data = %{
        name: "Null Mismatch",
        email: "<EMAIL>",
        document: "",
        primary_phone_number: "",
        addresses: [
          %{
            street: "Something",
            number: "",
            city_name: "",
            state: "",
            zip: "",
            country_name: "",
            receiver_name: "",
            complement: ""
          }
        ]
      }

      assert {:ok, updated_customer} =
               Customers.import_customers(organization.id, customer_data, ["email"])

      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "Null Mismatch"
      assert updated_customer.email == "<EMAIL>"
      assert updated_customer.document == nil
      assert updated_customer.primary_phone_number == nil
      assert length(updated_customer.addresses) == 1

      assert [address] = updated_customer.addresses
      assert address.id == existing_address.id
      assert address.street == "Something"
      assert address.number == nil
      assert address.city_name == nil
      assert address.state == nil
      assert address.zip == nil
      assert address.country_name == nil
      assert address.receiver_name == nil
      assert address.complement == nil
    end
  end
end
