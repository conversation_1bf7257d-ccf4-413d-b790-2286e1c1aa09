defmodule Rms.Integrations.Erp.Amaro.FiscalInvoice.InsertTest do
  use Rms.DataCase
  use ExUnit.Case

  import Mox

  alias Rms.Integrations.Erp.Amaro
  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  describe "insert/2" do
    test "inserts a fiscal invoice successfully" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:shopify_credential, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        fulfillment_id: fulfillment.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        xml: "xml"
      }

      {:ok, fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:erp_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {
          :ok,
          %{
            "data" => %{
              "order" => %{
                "id" => "gid://shopify/Order/1",
                "name" => "#1001",
                "displayFinancialStatus" => "PAID",
                "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "1.17"}},
                "customer" => %{
                  "cpf" => %{"value" => "05774088529"},
                  "displayName" => "Gustavo",
                  "email" => "<EMAIL>",
                  "id" => "gid://shopify/Customer/6488827068467"
                }
              }
            }
          }
        }
      end)

      Mox.expect(Amaro.Mock, :client, 1, fn _ ->
        :mock_client
      end)

      Mox.expect(Amaro.Mock, :insert_new_invoice, 1, fn _, _ ->
        {:ok, :ok}
      end)

      assert {:ok, "fiscal invoice send successfully"} =
               Amaro.FiscalInvoice.Insert.execute(org.id, fiscal_invoice.id)
    end

    test "return a error when insertion fails" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:shopify_credential, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        fulfillment_id: fulfillment.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        xml: "xml"
      }

      {:ok, fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:erp_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_order!, fn _, _, _, _ ->
        {
          :ok,
          %{
            "data" => %{
              "order" => %{
                "id" => "gid://shopify/Order/1",
                "name" => "#1001",
                "displayFinancialStatus" => "PAID",
                "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "1.17"}},
                "customer" => %{
                  "cpf" => %{"value" => "05774088529"},
                  "displayName" => "Gustavo",
                  "email" => "<EMAIL>",
                  "id" => "gid://shopify/Customer/6488827068467"
                }
              }
            }
          }
        }
      end)

      Mox.expect(Amaro.Mock, :client, fn _ ->
        :mock_client
      end)

      Mox.expect(Amaro.Mock, :insert_new_invoice, fn _, _ ->
        {:error, "error"}
      end)

      assert {:error, "error"} =
               Amaro.FiscalInvoice.Insert.execute(org.id, fiscal_invoice.id)
    end

    test "return a error when there is no shopify order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:shopify_credential, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        fulfillment_id: fulfillment.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        xml: "xml"
      }

      {:ok, fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:erp_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_order!, fn _, _, _, _ ->
        {
          :error,
          "shopify error"
        }
      end)

      Mox.expect(Amaro.Mock, :client, 0, fn _ ->
        :mock_client
      end)

      Mox.expect(Amaro.Mock, :insert_new_invoice, 0, fn _, _ ->
        {:error, "ok"}
      end)

      assert {:error, {:error, "shopify error"}} =
               Amaro.FiscalInvoice.Insert.execute(org.id, fiscal_invoice.id)
    end

    test "return a error when do not find a fulfillment by external_id" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:shopify_credential, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/1",
          shipping_method: "in-store"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        fulfillment_id: fulfillment.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        xml: "xml"
      }

      {:ok, fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:erp_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_order!, 0, fn _, _, _, _ ->
        {
          :ok,
          %{
            "data" => %{
              "order" => %{
                "id" => "gid://shopify/Order/1",
                "name" => "#1001",
                "displayFinancialStatus" => "PAID",
                "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "1.17"}},
                "customer" => %{
                  "cpf" => %{"value" => "05774088529"},
                  "displayName" => "Gustavo",
                  "email" => "<EMAIL>",
                  "id" => "gid://shopify/Customer/6488827068467"
                }
              }
            }
          }
        }
      end)

      Mox.expect(Amaro.Mock, :client, 0, fn _ ->
        :mock_client
      end)

      Mox.expect(Amaro.Mock, :insert_new_invoice, 0, fn _, _ ->
        {:ok, :ok}
      end)

      assert {:error, "ecomerce order not found"} =
               Amaro.FiscalInvoice.Insert.execute(org.id, fiscal_invoice.id)
    end

    test "return a error when do not find a fulfillment by type" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      insert(:shopify_credential, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "gid://shopify/Order/1",
          shipping_method: "delivery"
        )

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        fulfillment_id: fulfillment.id,
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        xml: "xml"
      }

      {:ok, fiscal_invoice} = Rms.Fiscal.create_fiscal_invoice(org.id, attrs)

      insert(:erp_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_order!, 0, fn _, _, _, _ ->
        {
          :ok,
          %{
            "data" => %{
              "order" => %{
                "id" => "gid://shopify/Order/1",
                "name" => "#1001",
                "displayFinancialStatus" => "PAID",
                "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "1.17"}},
                "customer" => %{
                  "cpf" => %{"value" => "05774088529"},
                  "displayName" => "Gustavo",
                  "email" => "<EMAIL>",
                  "id" => "gid://shopify/Customer/6488827068467"
                }
              }
            }
          }
        }
      end)

      Mox.expect(Amaro.Mock, :client, 0, fn _ ->
        :mock_client
      end)

      Mox.expect(Amaro.Mock, :insert_new_invoice, 0, fn _, _ ->
        {:ok, :ok}
      end)

      assert {:error, "ecomerce order not found"} =
               Amaro.FiscalInvoice.Insert.execute(org.id, fiscal_invoice.id)
    end
  end
end
