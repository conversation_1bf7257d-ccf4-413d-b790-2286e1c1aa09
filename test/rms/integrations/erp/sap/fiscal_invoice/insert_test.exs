defmodule Rms.Integrations.Erp.Sallve.FiscalInvoice.InsertTest do
  use Rms.DataCase
  use ExUnit.Case

  import Mox

  alias Rms.Integrations.Erp.Sallve

  setup :verify_on_exit!

  describe "insert/2" do
    test "correct insert a invoice in a ERP" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      order = insert(:order, location: loc, organization: org, name: "TESTE")

      fulfillment = insert(:fulfillment, order: order, organization: org)

      fi = insert(:fiscal_invoice, serie: serie, fulfillment: fulfillment, organization: org)

      insert(:erp_credential, organization: org)

      Mox.expect(Sallve.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Sallve.Mock, :insert_new_invoice, 1, fn _, _ ->
        {:ok, :ok}
      end)

      assert {:ok, "fiscal invoice inserted and marked as integrated"} =
               Sallve.FiscalInvoice.Insert.execute(org.id, fi.id)
    end

    test "return a error when insertion fails" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      order = insert(:order, location: loc, organization: org, name: "NAME")

      fulfillment = insert(:fulfillment, order: order, organization: org)

      fi = insert(:fiscal_invoice, serie: serie, fulfillment: fulfillment, organization: org)

      insert(:erp_credential, organization: org)

      Mox.expect(Sallve.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Sallve.Mock, :insert_new_invoice, 1, fn _, _ ->
        {:error, "error"}
      end)

      assert {:error, "error"} = Sallve.FiscalInvoice.Insert.execute(org.id, fi.id)
    end

    test "raise a error when there is no invoice" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)

      insert(:erp_credential, organization: org)

      Mox.expect(Sallve.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Sallve.Mock, :insert_new_invoice, 0, fn _, _ ->
        {:ok, :ok}
      end)

      assert_raise Ecto.NoResultsError, fn ->
        Sallve.FiscalInvoice.Insert.execute(org.id, serie.id)
      end
    end

    test "raise a error when there is no credential" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, organization: org)
      fi = insert(:fiscal_invoice, serie: serie, organization: org)

      Mox.expect(Sallve.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Sallve.Mock, :insert_new_invoice, 0, fn _, _ ->
        {:ok, :ok}
      end)

      assert_raise Ecto.NoResultsError, fn ->
        Sallve.FiscalInvoice.Insert.execute(org.id, fi.id)
      end
    end
  end
end
