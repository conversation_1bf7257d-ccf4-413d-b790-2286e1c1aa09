defmodule Rms.Integrations.OrdersTest do
  use Rms.DataCase

  import Rms.Factory
  import Tesla.Mock

  alias Rms.Integrations.Orders
  alias Rms.Integrations.Orders.CancelationEndpoint

  describe "cancelation_endpoints" do
    @invalid_attrs %{endpoint: nil, headers: nil, active: nil}

    test "list_cancelation_endpoints/1 returns all cancelation endpoints for an organization" do
      organization = insert(:organization)
      cancelation_endpoint = insert(:cancelation_endpoint, organization: organization)

      cancelation_endpoints =
        organization.id
        |> Orders.list_cancelation_endpoints()
        |> Repo.preload([:organization])

      assert cancelation_endpoints ==
               [cancelation_endpoint]
    end

    test "get_cancelation_endpoint!/2 returns the cancelation endpoint with given id" do
      organization = insert(:organization)
      cancelation_endpoint = insert(:cancelation_endpoint, organization: organization)

      db_cancelation_endpoint =
        organization.id
        |> Orders.get_cancelation_endpoint!(cancelation_endpoint.id)
        |> Repo.preload([:organization])

      assert db_cancelation_endpoint == cancelation_endpoint
    end

    test "create_cancelation_endpoint/2 with valid data creates a cancelation endpoint" do
      organization = insert(:organization)

      valid_attrs = %{
        endpoint: "https://example.com/cancel",
        headers: [["Content-Type", "application/json"]],
        active: true
      }

      assert {:ok, %CancelationEndpoint{} = cancelation_endpoint} =
               Orders.create_cancelation_endpoint(organization.id, valid_attrs)

      assert cancelation_endpoint.endpoint == "https://example.com/cancel"
      assert cancelation_endpoint.headers == [["Content-Type", "application/json"]]
      assert cancelation_endpoint.active == true
    end

    test "create_cancelation_endpoint/2 with invalid data returns error changeset" do
      organization = insert(:organization)

      assert {:error, %Ecto.Changeset{}} =
               Orders.create_cancelation_endpoint(organization.id, @invalid_attrs)
    end

    test "update_cancelation_endpoint/2 with valid data updates the cancelation endpoint" do
      cancelation_endpoint = insert(:cancelation_endpoint)

      update_attrs = %{
        endpoint: "https://example.com/new_cancel",
        headers: [["Authorization", "Bearer token"]],
        active: false
      }

      assert {:ok, %CancelationEndpoint{} = cancelation_endpoint} =
               Orders.update_cancelation_endpoint(cancelation_endpoint, update_attrs)

      assert cancelation_endpoint.endpoint == "https://example.com/new_cancel"
      assert cancelation_endpoint.headers == [["Authorization", "Bearer token"]]
      assert cancelation_endpoint.active == false
    end

    test "update_cancelation_endpoint/2 with invalid data returns error changeset" do
      cancelation_endpoint = insert(:cancelation_endpoint)

      assert {:error, %Ecto.Changeset{}} =
               Orders.update_cancelation_endpoint(cancelation_endpoint, @invalid_attrs)

      db_endpoint =
        Orders.get_cancelation_endpoint!(
          cancelation_endpoint.organization_id,
          cancelation_endpoint.id
        )

      assert cancelation_endpoint == Repo.preload(db_endpoint, :organization)
    end

    test "delete_cancelation_endpoint/1 deletes the cancelation endpoint" do
      cancelation_endpoint = insert(:cancelation_endpoint)

      assert {:ok, %CancelationEndpoint{}} =
               Orders.delete_cancelation_endpoint(cancelation_endpoint)

      assert_raise Ecto.NoResultsError, fn ->
        Orders.get_cancelation_endpoint!(
          cancelation_endpoint.organization_id,
          cancelation_endpoint.id
        )
      end
    end
  end

  describe "cancel_order/2" do
    setup do
      cancelation_endpoint =
        insert(:cancelation_endpoint,
          endpoint: "https://api.example.com/cancel",
          headers: [["Authorization", "Bearer token"]],
          active: true
        )

      {:ok, cancelation_endpoint: cancelation_endpoint}
    end

    test "successfully cancels an order", %{cancelation_endpoint: endpoint} do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: _body} ->
          %Tesla.Env{status: 200, body: %{"status" => "cancelled"}}
      end)

      assert {:ok, %{"status" => "cancelled"}} = Orders.cancel_order(endpoint, "order123")
    end

    test "returns an error when the API responds with a 4xx status", %{
      cancelation_endpoint: endpoint
    } do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: _body} ->
          %Tesla.Env{status: 400, body: %{"error" => "Invalid order ID"}}
      end)

      assert {:error, {400, %{"error" => "Invalid order ID"}}} =
               Orders.cancel_order(endpoint, "invalid_order")
    end

    test "returns an error when the API responds with a 5xx status", %{
      cancelation_endpoint: endpoint
    } do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: _body} ->
          %Tesla.Env{status: 500, body: %{"error" => "Internal server error"}}
      end)

      assert {:error, {500, %{"error" => "Internal server error"}}} =
               Orders.cancel_order(endpoint, "order123")
    end

    test "returns an error when there's a network issue", %{cancelation_endpoint: endpoint} do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: _body} ->
          {:error, :timeout}
      end)

      assert {:error, :timeout} = Orders.cancel_order(endpoint, "order123")
    end

    test "sends the correct payload and headers", %{cancelation_endpoint: endpoint} do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: _body} = env ->
          assert {"Authorization", "Bearer token"} in env.headers
          assert {"content-type", "application/json"} in env.headers
          %Tesla.Env{status: 200, body: %{"status" => "cancelled"}}
      end)

      assert {:ok, %{"status" => "cancelled"}} = Orders.cancel_order(endpoint, "order123")
    end

    test "transforms payload correctly when cancelation_transformer is set to 'areco'", %{
      cancelation_endpoint: endpoint
    } do
      insert(:organization_setting,
        organization: endpoint.organization,
        key: "cancelation_transformer",
        value: %{"data" => "areco"}
      )

      loc = insert(:location, cnpj: "12345678901234", organization: endpoint.organization)
      order = insert(:order, organization: endpoint.organization, location: loc)

      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: body} ->
          assert Jason.decode!(body) == %{
                   "idIglu" => order.id,
                   "motivoCancelamento" => "test_reason",
                   "cnpj" => "12345678901234"
                 }

          %Tesla.Env{status: 200, body: %{"status" => "cancelled"}}
      end)

      assert {:ok, %{"status" => "cancelled"}} =
               Orders.cancel_order(endpoint, order.id, "test_reason")
    end

    test "does not transform payload when cancelation_transformer is set to a random value", %{
      cancelation_endpoint: endpoint
    } do
      insert(:organization_setting,
        organization: endpoint.organization,
        key: "cancelation_transformer",
        value: %{"data" => "random_value"}
      )

      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: body} ->
          assert Jason.decode!(body) == %{"order_id" => "order123", "reason" => "test_reason"}
          %Tesla.Env{status: 200, body: %{"status" => "cancelled"}}
      end)

      assert {:ok, %{"status" => "cancelled"}} =
               Orders.cancel_order(endpoint, "order123", "test_reason")
    end

    test "does not transform payload when cancelation_transformer is not set", %{
      cancelation_endpoint: endpoint
    } do
      mock(fn
        %{method: :post, url: "https://api.example.com/cancel", body: body} ->
          assert Jason.decode!(body) == %{"order_id" => "order123", "reason" => "test_reason"}
          %Tesla.Env{status: 200, body: %{"status" => "cancelled"}}
      end)

      assert {:ok, %{"status" => "cancelled"}} =
               Orders.cancel_order(endpoint, "order123", "test_reason")
    end
  end
end
