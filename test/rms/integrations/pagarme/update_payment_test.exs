defmodule Rms.Integrations.PagarMe.UpdatePaymentTest do
  use Rms.DataCase, async: true

  import Rms.Factory

  alias Rms.Commerce.Orders
  alias Rms.Finance
  alias Rms.Integrations.PagarMe.UpdatePayment

  describe "execute/2" do
    test "updates payment to 'settled' and updates transaction/order when PagarMe status is 'paid'" do
      organization = insert(:organization)

      order =
        insert(:order,
          organization: organization,
          status: "open",
          total_price: Decimal.new("100.00")
        )

      transaction = insert(:transaction, order: order, organization: organization, status: "open")

      payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          method: "payment_link",
          status: "pending",
          amount: order.total_price
        )

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "ext_paid_123"
        )

      pagarme_order_data = %{"id" => epr.external_id, "status" => "paid"}

      assert {:ok, :updated_payment} =
               UpdatePayment.execute(organization.id, pagarme_order_data)

      updated_payment = Finance.get_payment!(organization.id, payment.id)
      assert updated_payment.status == "settled"

      updated_transaction = Finance.get_transaction!(organization.id, transaction.id)
      assert updated_transaction.status == "done"

      updated_order = Orders.get_order!(organization.id, order.id)
      assert updated_order.status == "paid"
    end

    test "updates payment to 'canceled' when PagarMe status is 'canceled'" do
      organization = insert(:organization)
      order = insert(:order, organization: organization, status: "open")
      transaction = insert(:transaction, order: order, organization: organization, status: "open")

      payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          status: "pending"
        )

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "ext_canceled_123"
        )

      pagarme_order_data = %{"id" => epr.external_id, "status" => "canceled"}

      assert {:ok, :updated_payment} =
               UpdatePayment.execute(organization.id, pagarme_order_data)

      updated_payment = Finance.get_payment!(organization.id, payment.id)
      assert updated_payment.status == "canceled"

      original_transaction = Finance.get_transaction!(organization.id, transaction.id)
      assert original_transaction.status == "open"

      original_order = Orders.get_order!(organization.id, order.id)
      assert original_order.status == "open"
    end

    test "returns {:error, {:invalid_status, \"pending\"}} for 'pending' PagarMe status and does not update payment" do
      organization = insert(:organization)
      payment = insert(:payment, organization: organization, status: "authorized")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "ext_pending_123"
        )

      pagarme_order_data = %{"id" => epr.external_id, "status" => "pending"}

      assert {:error, {:invalid_status, "pending"}} =
               UpdatePayment.execute(organization.id, pagarme_order_data)

      original_payment = Finance.get_payment!(organization.id, payment.id)
      assert original_payment.status == "authorized"
    end

    test "returns {:error, {:invalid_status, \"failed\"}} for 'failed' PagarMe status and does not update payment" do
      organization = insert(:organization)
      payment = insert(:payment, organization: organization, status: "authorized")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "ext_failed_123"
        )

      pagarme_order_data = %{"id" => epr.external_id, "status" => "failed"}

      assert {:error, {:invalid_status, "failed"}} =
               UpdatePayment.execute(organization.id, pagarme_order_data)

      original_payment = Finance.get_payment!(organization.id, payment.id)
      assert original_payment.status == "authorized"
    end

    test "returns {:error, :not_found} when payment is not found for the given external_id and organization_id" do
      organization = insert(:organization)
      pagarme_order_data = %{"id" => "non_existent_id_123", "status" => "paid"}

      assert {:error, :not_found} ==
               UpdatePayment.execute(organization.id, pagarme_order_data)
    end

    test "returns {:error, :not_found} when payment exists for external_id but in a different organization" do
      org1 = insert(:organization)
      payment_org1 = insert(:payment, organization: org1)

      epr_org1 =
        insert(:external_payment_reference,
          payment: payment_org1,
          organization: org1,
          partner: "pagarme",
          external_id: "shared_ext_id_123"
        )

      # Target organization for the call
      org2 = insert(:organization)

      pagarme_order_data = %{"id" => epr_org1.external_id, "status" => "paid"}

      assert {:error, :not_found} ==
               UpdatePayment.execute(org2.id, pagarme_order_data)
    end

    test "propagates error from Finance.sync_payment if update fails (e.g. invalid state transition)" do
      organization = insert(:organization)
      # Setup a payment that is already in a terminal state "canceled"
      payment = insert(:payment, organization: organization, status: "canceled")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "ext_sync_fail_123"
        )

      # Attempting to move it to "settled" (via "paid") should be rejected by Finance.sync_payment's state machine
      pagarme_order_data = %{"id" => epr.external_id, "status" => "paid"}

      result = UpdatePayment.execute(organization.id, pagarme_order_data)

      # Assert that the error is from Ecto.Changeset due to invalid status transition
      assert {:error, changeset} = result
      assert "status transition not allowed canceled -> settled" in errors_on(changeset).status

      original_payment = Finance.get_payment!(organization.id, payment.id)
      # Status remains unchanged
      assert original_payment.status == "canceled"
    end

    test "saves transaction metadata including tid, nsu and authorization code when PagarMe status is 'paid'" do
      organization = insert(:organization)

      order =
        insert(:order,
          organization: organization,
          status: "open",
          total_price: Decimal.new("795.00")
        )

      transaction = insert(:transaction, order: order, organization: organization, status: "open")

      payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          method: "payment_link",
          status: "pending",
          amount: order.total_price,
          metadata: %{}
        )

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme",
          external_id: "or_34QMO2U6bhK1j5lv"
        )

      # Sample PagarMe order data with multiple charges
      pagarme_order_data = %{
        "id" => epr.external_id,
        "status" => "paid",
        "charges" => [
          %{
            "amount" => 39_750,
            "last_transaction" => %{
              "acquirer_auth_code" => "087767",
              "acquirer_nsu" => "3958796684",
              "acquirer_tid" => "3958796684",
              "amount" => 39_750,
              "status" => "captured",
              "success" => true
            }
          },
          %{
            "amount" => 39_750,
            "last_transaction" => %{
              "acquirer_auth_code" => "087768",
              "acquirer_nsu" => "3958796685",
              "acquirer_tid" => "3958796685",
              "amount" => 39_750,
              "status" => "captured",
              "success" => true
            }
          }
        ]
      }

      assert {:ok, :updated_payment} =
               UpdatePayment.execute(organization.id, pagarme_order_data)

      updated_payment = Finance.get_payment!(organization.id, payment.id)
      assert updated_payment.status == "settled"

      # Verify that the transaction metadata was saved correctly
      # We should have an array of charges in the metadata
      assert is_list(updated_payment.metadata["charges"])
      assert length(updated_payment.metadata["charges"]) == 2

      # Check first charge
      first_charge = Enum.at(updated_payment.metadata["charges"], 0)
      assert first_charge["tid"] == "3958796684"
      assert first_charge["nsu"] == "3958796684"
      assert first_charge["aut"] == "087767"

      # Check second charge
      second_charge = Enum.at(updated_payment.metadata["charges"], 1)
      assert second_charge["tid"] == "3958796685"
      assert second_charge["nsu"] == "3958796685"
      assert second_charge["aut"] == "087768"
    end
  end
end
