defmodule Rms.Integrations.PagamrMeTest do
  use ExUnit.Case

  import Mox

  alias Rms.Integrations.PagarMe

  setup :verify_on_exit!

  setup do
    Mox.stub_with(PagarMeClientMock, PagarMeClientStub)

    :ok
  end

  describe "get_order/1" do
    test "returns ok tuple" do
      expect(PagarMeClientMock, :get_order, fn _client, "ORDER1" ->
        {:ok, order_builder("ORDER1", "paid", 100)}
      end)

      client = PagarMe.client("any token")
      assert {:ok, _response} = PagarMe.get_order(client, "ORDER1")
    end

    test "returns not found error" do
      expect(PagarMeClientMock, :get_order, fn _client, "not_found_id" ->
        {:error, :not_found}
      end)

      client = PagarMe.client("any token")
      assert {:error, :not_found} = PagarMe.get_order(client, "not_found_id")
    end
  end

  def order_builder(order_id, status, amount) do
    %{
      "amount" => amount,
      "charges" => [],
      "closed" => true,
      "closed_at" => "2017-04-19T16:01:11Z",
      "code" => "62LVFN7I4R",
      "created_at" => "2017-04-19T16:01:09Z",
      "currency" => "BRL",
      "customer" => %{},
      "id" => "#{order_id}",
      "items" => [],
      "status" => "#{status}",
      "updated_at" => "2017-04-19T16:01:11Z"
    }
  end
end
