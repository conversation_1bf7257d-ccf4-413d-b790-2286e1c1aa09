defmodule Rms.Integrations.ProductSyncConfigurationTest do
  use Rms.DataCase, async: true

  alias Rms.Integrations.ProductSyncConfiguration

  setup do
    valid_attrs = %{
      field_priorities: %{
        "name" => ["shopify", "linx_pos", "vtex"],
        "price" => ["linx_pos", "shopify", "vtex"]
      },
      default_priority: ["shopify", "linx_pos", "vtex"],
      organization_id: 1
    }

    {:ok, valid_attrs: valid_attrs}
  end

  describe "changeset/2" do
    test "with valid attributes", %{valid_attrs: valid_attrs} do
      changeset = ProductSyncConfiguration.changeset(%ProductSyncConfiguration{}, valid_attrs)
      assert changeset.valid?
    end

    test "with invalid field_priorities format" do
      invalid_attrs = %{
        field_priorities: %{"name" => "not_an_array"},
        default_priority: ["shopify"],
        organization_id: 1
      }

      changeset = ProductSyncConfiguration.changeset(%ProductSyncConfiguration{}, invalid_attrs)
      refute changeset.valid?

      assert "must be a map with array of strings as values" in errors_on(changeset).field_priorities
    end

    test "with invalid default_priority format" do
      invalid_attrs = %{
        field_priorities: %{"name" => ["shopify"]},
        default_priority: [["shopify", "vtex"], ["linx_pos"]],
        organization_id: 1
      }

      changeset = ProductSyncConfiguration.changeset(%ProductSyncConfiguration{}, invalid_attrs)
      refute changeset.valid?
      assert "is invalid" in errors_on(changeset).default_priority
    end
  end
end
