defmodule Rms.Integrations.Shopify.Carts.BuildParamsTest do
  use Rms.DataCase

  import Rms.Factory

  describe "execute/1" do
    test "build cart params with all params" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      assert %{
               lines: [%{quantity: _, merchandiseId: "2"}, %{quantity: _, merchandiseId: "3"}],
               buyerIdentity: %{
                 email: _,
                 countryCode: "BR",
                 deliveryAddressPreferences: [
                   %{deliveryAddress: %{zip: "04088004", country: "BR", province: "SP"}}
                 ]
               },
               discountCodes: ["TEST123"]
             } =
               Rms.Integrations.Shopify.Carts.BuildParams.execute(
                 cart_info,
                 %{p1.id => "2", p2.id => "3"},
                 org.id
               )
    end

    test "build cart params without cupon discounts" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "percentage",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      assert %{
               lines: [%{quantity: _, merchandiseId: "2"}, %{quantity: _, merchandiseId: "3"}],
               buyerIdentity: %{
                 email: _,
                 countryCode: "BR",
                 deliveryAddressPreferences: [
                   %{deliveryAddress: %{zip: "04088004", country: "BR", province: "SP"}}
                 ]
               },
               discountCodes: []
             } =
               Rms.Integrations.Shopify.Carts.BuildParams.execute(
                 cart_info,
                 %{p1.id => "2", p2.id => "3"},
                 org.id
               )
    end

    test "build cart params without client" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => nil,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      assert %{
               lines: [%{quantity: _, merchandiseId: "2"}, %{quantity: _, merchandiseId: "3"}],
               buyerIdentity: %{
                 countryCode: "BR",
                 deliveryAddressPreferences: [
                   %{deliveryAddress: %{zip: "04088004", country: "BR", province: "SP"}}
                 ]
               },
               discountCodes: ["TEST123"]
             } =
               Rms.Integrations.Shopify.Carts.BuildParams.execute(
                 cart_info,
                 %{p1.id => "2", p2.id => "3"},
                 org.id
               )
    end

    test "build cart params with cart attributes" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "3"
      )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "discounts" => [],
        "postal_code" => "04088004",
        "customer_id" => nil,
        "staff_id" => nil,
        "country" => "BRA"
      }

      cart_attributes = %{
        "iglu_organization_id" => org.id,
        "iglu_location_id" => "123"
      }

      assert %{
               lines: [%{quantity: _, merchandiseId: "2"}, %{quantity: _, merchandiseId: "3"}],
               buyerIdentity: %{
                 countryCode: "BR",
                 deliveryAddressPreferences: [
                   %{deliveryAddress: %{zip: "04088004", country: "BR", province: "SP"}}
                 ]
               },
               attributes: [
                 %{key: "iglu_location_id", value: "123"},
                 %{key: "iglu_organization_id", value: _}
               ]
             } =
               Rms.Integrations.Shopify.Carts.BuildParams.execute(
                 cart_info,
                 %{p1.id => "2", p2.id => "3"},
                 org.id,
                 cart_attributes: cart_attributes
               )
    end
  end
end
