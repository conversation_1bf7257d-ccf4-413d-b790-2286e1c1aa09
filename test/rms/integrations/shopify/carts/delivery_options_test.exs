defmodule Rms.Integrations.Shopify.Carts.DeliveryOptionsTest do
  use Rms.DataCase
  use ExUnit.Case

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  setup do
    Mox.stub_with(Rms.Clients.FeatureFlag.Mock, FeatureFlagStub)
    :ok
  end

  describe "execute/1" do
    test "fetch delivery options from shopify" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      [p1 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      Mox.expect(Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1138\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQyRTFYWVBQUkNQQkhSTVlGWUpEUjU3?key=fbfd89205bbcceedbc8d72939bd00231\",\"createdAt\":\"2024-07-30T18:22:18Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQyRTFYWVBQUkNQQkhSTVlGWUpEUjU3?key=fbfd89205bbcceedbc8d72939bd00231\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"107.0\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[],\"discountAllocations\":[],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":1}}]}},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 706\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"deliveryMethodType\":\"LOCAL\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Standard\",\"handle\":\"01aa06e0a4934c2234bed0fd85fdc97e\",\"deliveryMethodType\":\"SHIPPING\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Express\",\"handle\":\"66e69106431f8094e6af4be8e2ccb287\",\"deliveryMethodType\":\"SHIPPING\",\"estimatedCost\":{\"amount\":\"17.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"deliveryMethodType\":\"LOCAL\",\"estimatedCost\":{\"amount\":\"7.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1
          }
        ],
        "postal_code" => "04088004",
        "country" => "BRA"
      }

      response =
        {:ok,
         %{
           "items" => [
             %{
               "delivery_options" => [
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                   "metadata" => %{
                     "deliveryMethodType" => "LOCAL",
                     "estimatedCost" => %{"amount" => "7.0"},
                     "handle" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                     "title" => "Local Delivery"
                   },
                   "name" => "Local Delivery",
                   "price" => 700,
                   "quantity" => 1
                 },
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "01aa06e0a4934c2234bed0fd85fdc97e",
                   "metadata" => %{
                     "deliveryMethodType" => "SHIPPING",
                     "estimatedCost" => %{"amount" => "10.0"},
                     "handle" => "01aa06e0a4934c2234bed0fd85fdc97e",
                     "title" => "Standard"
                   },
                   "name" => "Standard",
                   "price" => 1000,
                   "quantity" => 1
                 },
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "66e69106431f8094e6af4be8e2ccb287",
                   "metadata" => %{
                     "deliveryMethodType" => "SHIPPING",
                     "estimatedCost" => %{"amount" => "17.0"},
                     "handle" => "66e69106431f8094e6af4be8e2ccb287",
                     "title" => "Express"
                   },
                   "name" => "Express",
                   "price" => 1700,
                   "quantity" => 1
                 }
               ],
               "item_index" => 0,
               "product_variant_id" => p1.id
             }
           ],
           "pickup_points" => []
         }}

      assert response == Rms.Integrations.Shopify.Carts.DeliveryOptions.execute(org.id, cart_info)
    end

    test "fetch delivery options from shopify when there is none" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      [p1 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      Mox.expect(Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1138\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQyRTFYWVBQUkNQQkhSTVlGWUpEUjU3?key=fbfd89205bbcceedbc8d72939bd00231\",\"createdAt\":\"2024-07-30T18:22:18Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQyRTFYWVBQUkNQQkhSTVlGWUpEUjU3?key=fbfd89205bbcceedbc8d72939bd00231\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"107.0\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[],\"discountAllocations\":[],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":1}}]}},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 706\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"deliveryMethodType\":\"LOCAL\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Standard\",\"handle\":\"01aa06e0a4934c2234bed0fd85fdc97e\",\"deliveryMethodType\":\"SHIPPING\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Express\",\"handle\":\"66e69106431f8094e6af4be8e2ccb287\",\"deliveryMethodType\":\"SHIPPING\",\"estimatedCost\":{\"amount\":\"17.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"deliveryMethodType\":\"LOCAL\",\"estimatedCost\":{\"amount\":\"7.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 1
          }
        ],
        "postal_code" => "04088004",
        "country" => "BRA"
      }

      response =
        {:ok,
         %{
           "items" => [
             %{
               "delivery_options" => [
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                   "metadata" => %{
                     "deliveryMethodType" => "LOCAL",
                     "estimatedCost" => %{"amount" => "7.0"},
                     "handle" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                     "title" => "Local Delivery"
                   },
                   "name" => "Local Delivery",
                   "price" => 700,
                   "quantity" => 1
                 },
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "01aa06e0a4934c2234bed0fd85fdc97e",
                   "metadata" => %{
                     "deliveryMethodType" => "SHIPPING",
                     "estimatedCost" => %{"amount" => "10.0"},
                     "handle" => "01aa06e0a4934c2234bed0fd85fdc97e",
                     "title" => "Standard"
                   },
                   "name" => "Standard",
                   "price" => 1000,
                   "quantity" => 1
                 },
                 %{
                   "delivery_time" => "1bd",
                   "delivery_type" => "delivery",
                   "id" => "66e69106431f8094e6af4be8e2ccb287",
                   "metadata" => %{
                     "deliveryMethodType" => "SHIPPING",
                     "estimatedCost" => %{"amount" => "17.0"},
                     "handle" => "66e69106431f8094e6af4be8e2ccb287",
                     "title" => "Express"
                   },
                   "name" => "Express",
                   "price" => 1700,
                   "quantity" => 1
                 }
               ],
               "item_index" => 0,
               "product_variant_id" => p1.id
             }
           ],
           "pickup_points" => []
         }}

      assert response == Rms.Integrations.Shopify.Carts.DeliveryOptions.execute(org.id, cart_info)
    end
  end

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  describe "execute_variant_stock_strategy/2" do
    setup do
      org = insert(:organization)
      product_variant = insert(:product_variant, organization: org)

      %{id: external_id} =
        insert(:product_variant_mapping,
          product_variant: product_variant,
          source: "shopify",
          organization: org
        )

      insert(:shopify_credential, organization: org)

      {:ok, %{org: org, product_variant: product_variant, external_id: external_id}}
    end

    test "returns the delivery options from shopify when has delivery stock", %{
      org: org,
      product_variant: product_variant
    } do
      expect(ShopifyMock, :storefront_client, fn _, _, _ -> :mock_client end)

      expect(Rms.Clients.FeatureFlag.Mock, :should_get_variant_stock?, fn _ -> true end)

      expect(ShopifyMock, :get_product_variant_stock, fn _, _ ->
        {:ok,
         %{
           "availableForSale" => true,
           "quantityAvailable" => 0
         }}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => product_variant.id,
            "quantity" => 1
          }
        ],
        "postal_code" => "04088004",
        "country" => "BRA"
      }

      assert {:ok, %{"items" => items}} =
               Rms.Integrations.Shopify.Carts.DeliveryOptions.execute(org.id, cart_info)

      assert items == [
               %{
                 "delivery_options" => [
                   %{
                     "delivery_time" => "1bd",
                     "delivery_type" => "delivery",
                     "id" => "delivery",
                     "metadata" => %{
                       "deliveryMethodType" => "SHIPPING",
                       "estimatedCost" => %{
                         "amount" => "0.00",
                         "currencyCode" => "BRL"
                       },
                       "handle" => "delivery",
                       "title" => "Fake Delivery"
                     },
                     "name" => "Fake Delivery",
                     "price" => 0,
                     "quantity" => 1
                   }
                 ],
                 "item_index" => 0,
                 "product_variant_id" => product_variant.id
               }
             ]
    end

    test "returns empty list when has no delivery stock", %{
      org: org,
      product_variant: product_variant
    } do
      expect(ShopifyMock, :storefront_client, fn _, _, _ -> :mock_client end)

      expect(Rms.Clients.FeatureFlag.Mock, :should_get_variant_stock?, fn _ -> true end)

      expect(ShopifyMock, :get_product_variant_stock, fn _, _ ->
        {:ok,
         %{
           "availableForSale" => false,
           "quantityAvailable" => 0
         }}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => product_variant.id,
            "quantity" => 1
          }
        ],
        "postal_code" => "04088004",
        "country" => "BRA"
      }

      assert {:ok, %{"items" => items}} =
               Rms.Integrations.Shopify.Carts.DeliveryOptions.execute(org.id, cart_info)

      assert items == [
               %{
                 "delivery_options" => [],
                 "item_index" => 0,
                 "product_variant_id" => product_variant.id
               }
             ]
    end
  end
end
