defmodule Rms.Integrations.Shopify.Carts.SimulateTest do
  use Rms.DataCase
  use ExUnit.Case

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  setup do
    Mox.stub_with(Rms.Clients.FeatureFlag.Mock, FeatureFlagStub)
    :ok
  end

  describe "execute/1" do
    test "simulate a cart with delivery items" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "1"
      )

      Mox.expect(Shopify.Mock, :storefront_client, 1, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, 1, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1705\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQxVzcyNFMwRkNYWFM4MVlHRkdWOE5F?key=4b513795b7c16eafd2143d176a5dfb7a\",\"createdAt\":\"2024-07-30T13:10:32Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQxVzcyNFMwRkNYWFM4MVlHRkdWOE5F?key=4b513795b7c16eafd2143d176a5dfb7a\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"212.9\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"222.25\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"212.9\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[{\"applicable\":true,\"code\":\"TEST123\"}],\"discountAllocations\":[{\"targetType\":\"LINE_ITEM\",\"discountedAmount\":{\"amount\":\"10.0\",\"currencyCode\":\"BRL\"},\"code\":\"TEST123\"},{\"targetType\":\"LINE_ITEM\",\"discountedAmount\":{\"amount\":\"0.65\",\"currencyCode\":\"BRL\"},\"code\":\"TEST123\"}],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"200.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"200.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":2}},{\"node\":{\"merchandise\":{\"id\":\"1\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"6.45\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"12.9\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"12.9\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":2}}]}},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 571\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Standard\",\"handle\":\"a1130253b6d3329db24887f2a16ff61a\",\"deliveryMethodType\":\"SHIPPING\"},{\"title\":\"Express\",\"handle\":\"665940656bbd6c4b9b37d6ed38928447\",\"deliveryMethodType\":\"SHIPPING\"}],\"selectedDeliveryOption\":{\"title\":\"Standard\",\"handle\":\"a1130253b6d3329db24887f2a16ff61a\",\"deliveryMethodType\":\"SHIPPING\"}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      response = %{
        "customer_id" => customer.id,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 1,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "handle" => "665940656bbd6c4b9b37d6ed38928447",
                  "title" => "Express"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "handle" => "665940656bbd6c4b9b37d6ed38928447",
                  "title" => "Express"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9")
              },
              %{
                "avaliable_fulfillment_type" => "delivery",
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "handle" => "665940656bbd6c4b9b37d6ed38928447",
                  "title" => "Express"
                },
                "metadata" => %{
                  "deliveryMethodType" => "SHIPPING",
                  "handle" => "665940656bbd6c4b9b37d6ed38928447",
                  "title" => "Express"
                },
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0")
              }
            ],
            "fulfillment_type" => "delivery",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("0.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("202.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65"),
        "metadata" => %{"notes" => nil}
      }

      assert response == Rms.Integrations.Shopify.Carts.Simulate.execute(org.id, cart_info)
    end

    test "simulate a cart with in-store items" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p2,
        external_id: "1"
      )

      Mox.expect(Shopify.Mock, :storefront_client, 1, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, 1, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1705\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQxWjlOWjg0S1BYODNGSkhCMkZCR1RC?key=2baa4ddf1e50f7499e7227a6714dd14e\",\"createdAt\":\"2024-07-30T14:04:23Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQxWjlOWjg0S1BYODNGSkhCMkZCR1RC?key=2baa4ddf1e50f7499e7227a6714dd14e\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"212.9\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"222.25\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"212.9\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[{\"applicable\":true,\"code\":\"TEST123\"}],\"discountAllocations\":[{\"targetType\":\"LINE_ITEM\",\"discountedAmount\":{\"amount\":\"10.0\",\"currencyCode\":\"BRL\"},\"code\":\"TEST123\"},{\"targetType\":\"LINE_ITEM\",\"discountedAmount\":{\"amount\":\"0.65\",\"currencyCode\":\"BRL\"},\"code\":\"TEST123\"}],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"1\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"200.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"200.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":2}},{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"6.45\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"12.9\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"12.9\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":2}}]}},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 571\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Standard\",\"handle\":\"a1130253b6d3329db24887f2a16ff61a\",\"deliveryMethodType\":\"SHIPPING\"},{\"title\":\"Express\",\"handle\":\"665940656bbd6c4b9b37d6ed38928447\",\"deliveryMethodType\":\"SHIPPING\"}],\"selectedDeliveryOption\":{\"title\":\"Standard\",\"handle\":\"a1130253b6d3329db24887f2a16ff61a\",\"deliveryMethodType\":\"SHIPPING\"}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      response = %{
        "customer_id" => customer.id,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "in-store",
                "fulfillment_type" => "in-store",
                "item_index" => 1,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => Decimal.new("100.0"),
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("100.0"),
                "total_price" => Decimal.new("200.0"),
                "ecommerce_discounts" => []
              },
              %{
                "avaliable_fulfillment_type" => "in-store",
                "fulfillment_type" => "in-store",
                "item_index" => 0,
                "list_price" => Decimal.new("6.45"),
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => Decimal.new("6.45"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("6.45"),
                "total_price" => Decimal.new("12.9"),
                "ecommerce_discounts" => []
              }
            ],
            "fulfillment_type" => "in-store",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("0.00"),
        "total_items_list_price" => Decimal.new("212.90"),
        "total_items_selling_price" => Decimal.new("212.90"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("202.25"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("10.65"),
        "metadata" => %{"notes" => nil}
      }

      assert response == Rms.Integrations.Shopify.Carts.Simulate.execute(org.id, cart_info)
    end

    test "simulate a cart with local items" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1, p2 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      Mox.expect(Shopify.Mock, :storefront_client, 1, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, 0, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1273\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"createdAt\":\"2024-07-30T17:25:05Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[{\"applicable\":false,\"code\":\"TEST123\"}],\"discountAllocations\":[],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":0}}]}},\"userErrors\":[{\"field\":[\"input\",\"lines\",\"0\",\"quantity\"],\"message\":\"O produto 'Sandálias Amazônia - 40/41' já esgotou.\"}]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 103\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p2.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      response = %{
        "customer_id" => customer.id,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "LOCAL-IN-STORE",
                "fulfillment_type" => "in-store",
                "item_index" => 0,
                "list_price" => p1.list_price,
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => p1.list_price,
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => p1.list_price,
                "total_price" => Decimal.mult(p1.list_price, "2")
              },
              %{
                "avaliable_fulfillment_type" => "LOCAL-IN-STORE",
                "fulfillment_type" => "in-store",
                "item_index" => 1,
                "list_price" => p2.list_price,
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => p2.list_price,
                "product_variant_id" => p2.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => p2.list_price,
                "total_price" => Decimal.mult(p2.list_price, "2")
              }
            ],
            "fulfillment_type" => "in-store",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [],
        "staff_id" => nil,
        "total_delivery_price" => Decimal.new("0.00"),
        "total_items_list_price" => Decimal.new("40.00"),
        "total_items_selling_price" => Decimal.new("40.00"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("40.00"),
        "location_id" => nil,
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("0.00"),
        "metadata" => %{"notes" => nil}
      }

      assert response == Rms.Integrations.Shopify.Carts.Simulate.execute(org.id, cart_info)
    end

    test "simulate a cart with no product stock" do
      org = insert(:organization)

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      customer = insert(:customer, organization: org)

      [p1 | _] =
        insert_list(5, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: p1,
        external_id: "2"
      )

      Mox.expect(Shopify.Mock, :storefront_client, 1, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, 2, fn _, _ ->
        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1273\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"createdAt\":\"2024-07-30T17:25:05Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[{\"applicable\":false,\"code\":\"TEST123\"}],\"discountAllocations\":[],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":0}}]}},\"userErrors\":[{\"field\":[\"input\",\"lines\",\"0\",\"quantity\"],\"message\":\"O produto 'Sandálias Amazônia - 40/41' já esgotou.\"}]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 103\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          },
          %{
            "product_variant_id" => p1.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store",
            "metadata" => nil
          }
        ],
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => nil,
        "discounts" => [
          %{
            "type" => "coupon",
            "value" => "TEST123",
            "description" => ""
          }
        ],
        "country" => "BRA"
      }

      response = %{
        "customer_id" => customer.id,
        "delivery_groups" => [
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => nil,
                "fulfillment_type" => "delivery",
                "item_index" => 0,
                "list_price" => Decimal.new("100.0"),
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => Decimal.new("0.0"),
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => Decimal.new("0.0"),
                "total_price" => Decimal.new("0.0")
              }
            ],
            "fulfillment_type" => "unfulfillable",
            "pickup_point" => ""
          },
          %{
            "cart_items" => [
              %{
                "avaliable_fulfillment_type" => "in-store",
                "fulfillment_type" => "in-store",
                "item_index" => 1,
                "list_price" => p1.list_price,
                "logistics_info" => nil,
                "metadata" => nil,
                "original_metadata" => nil,
                "price" => p1.list_price,
                "product_variant_id" => p1.id,
                "quantity" => 2,
                "request_index" => 0,
                "selling_price" => p1.list_price,
                "total_price" => Decimal.mult(p1.list_price, "2")
              }
            ],
            "fulfillment_type" => "in-store",
            "pickup_point" => ""
          }
        ],
        "ecommerce" => "shopify",
        "messages" => [
          %{
            "code" => "unfulfillable_items",
            "info" => %{
              "items" => [
                %{
                  "avaliable_fulfillment_type" => nil,
                  "fulfillment_type" => "delivery",
                  "item_index" => 0,
                  "list_price" => Decimal.new("100.0"),
                  "logistics_info" => nil,
                  "metadata" => nil,
                  "original_metadata" => nil,
                  "price" => Decimal.new("0.0"),
                  "product_variant_id" => p1.id,
                  "quantity" => 2,
                  "request_index" => 0,
                  "selling_price" => Decimal.new("100.0"),
                  "total_price" => Decimal.new("0.0")
                }
              ]
            },
            "message" => "Some items are not available for the selected fulfillment_type",
            "type" => "error"
          },
          %{
            "code" => "coupon_not_found",
            "info" => %{"code" => "TEST123"},
            "message" => "invalid discount code",
            "type" => "warning"
          }
        ],
        "staff_id" => nil,
        "location_id" => nil,
        "total_delivery_price" => Decimal.new("0.00"),
        "total_items_list_price" => Decimal.new("20.00"),
        "total_items_selling_price" => Decimal.new("20.00"),
        "total_manual_discount" => Decimal.new("0.00"),
        "total_price" => Decimal.new("20.00"),
        "shipping_address_id" => nil,
        "discounts" => [%{"description" => "", "type" => "coupon", "value" => "TEST123"}],
        "total_ecommerce_discounts" => Decimal.new("0.00"),
        "metadata" => %{"notes" => nil}
      }

      assert response == Rms.Integrations.Shopify.Carts.Simulate.execute(org.id, cart_info)
    end

    test "passes cart attributes including store location" do
      shopify_client = %{}
      org = insert(:organization)
      location = insert(:location, organization: org)

      # Add location mapping with Shopify external ID
      insert(:location_mapping,
        location: location,
        organization: org,
        source: "shopify",
        external_id: "gid://shopify/Location/12345"
      )

      insert(:shopify_credential, organization: org)
      insert(:shopify_storefront_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "2"
      )

      cart_info = %{
        "items" => [
          %{
            "product_variant_id" => pv.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery",
            "metadata" => nil
          }
        ],
        "discounts" => [],
        "country" => "BRA",
        "postal_code" => "04088004",
        "location_id" => location.id
      }

      expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        shopify_client
      end)

      # Capture the formatted cart in a MoxExpectation
      expect(Rms.Integrations.Shopify.Mock, :simulate_cart, fn client, formatted_cart ->
        assert client == shopify_client

        # Verify cart attributes are present and correct
        assert %{attributes: attributes} = formatted_cart

        # Find the specific attributes we care about
        store_location = Enum.find(attributes, &(&1.key == "store_location"))
        org_id = Enum.find(attributes, &(&1.key == "iglu_organization_id"))
        location_id = Enum.find(attributes, &(&1.key == "iglu_location_id"))

        # Assert their values
        assert store_location.value == "12345"
        assert org_id.value == to_string(org.id)
        assert location_id.value == to_string(location.id)

        {:ok,
         "\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 1273\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"createdAt\":\"2024-07-30T17:25:05Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjQyQVM1WUJDUUtZVjM0M1FWNlFLTkZU?key=86a754469673de13feb45c6a2f760abe\",\"cost\":{\"subtotalAmountEstimated\":true,\"totalAmountEstimated\":true,\"totalDutyAmountEstimated\":true,\"totalTaxAmountEstimated\":true,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalDutyAmount\":null,\"totalTaxAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"checkoutChargeAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountCodes\":[{\"applicable\":false,\"code\":\"TEST123\"}],\"discountAllocations\":[],\"lines\":{\"edges\":[{\"node\":{\"merchandise\":{\"id\":\"2\"},\"cost\":{\"amountPerQuantity\":{\"amount\":\"100.0\",\"currencyCode\":\"BRL\"},\"compareAtAmountPerQuantity\":null,\"subtotalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"},\"totalAmount\":{\"amount\":\"0.0\",\"currencyCode\":\"BRL\"}},\"discountAllocations\":[],\"quantity\":0}}]}},\"userErrors\":[{\"field\":[\"input\",\"lines\",\"0\",\"quantity\"],\"message\":\"O produto 'Sandálias Amazônia - 40/41' já esgotou.\"}]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 103\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[]}}}],\"hasNext\":false}\r\n\r\n--graphql--"}
      end)

      Rms.Integrations.Shopify.Carts.Simulate.execute(org.id, cart_info)
    end
  end
end
