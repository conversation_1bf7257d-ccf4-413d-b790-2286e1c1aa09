defmodule Rms.Integrations.Shopify.Clients.Webhooks.CreateTest do
  use Rms.DataCase
  import Mox

  alias Rms.Integrations.Shopify.Clients.Webhooks.Create
  alias Rms.Repo

  setup :verify_on_exit!
  @ibge_mock Application.compile_env(:rms, :ibge_client, Rms.Integrations.Ibge.Mock)

  describe "execute/2" do
    test "creates a new customer when one doesn't exist" do
      organization = insert(:organization)

      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "Test City",
             uf_name: "Test Province",
             uf_initials: "TP"
           }
         ]}
      end)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "id" => 10_719_097_291_047,
            "address1" => "123 Test St",
            "address2" => "Apt 1",
            "city" => "Test City",
            "name" => "Test User",
            "province" => "Test Province",
            "country" => "Brazil",
            "zip" => "60192105",
            "province_code" => "TP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "default" => true
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, %{customer: customer, sync_mapping: mapping}} =
               Create.execute(organization.id, customer_data)

      assert customer.name == "Test User"
      assert customer.email == "<EMAIL>"
      assert customer.primary_phone_number == "+5585996532132"
      assert mapping.external_id == "gid://shopify/Customer/8616466579751"
      assert mapping.source == "shopify"
    end

    test "updates an existing customer when one exists" do
      organization = insert(:organization)

      customer =
        insert(:customer,
          organization: organization,
          name: "Old Name",
          email: "<EMAIL>",
          primary_phone_number: "+5585999999999"
        )

      _mapping =
        insert(:customer_sync_mapping,
          organization: organization,
          customer: customer,
          source: "shopify",
          external_id: "gid://shopify/Customer/8616466579751"
        )

      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "New City",
             uf_name: "New Province",
             uf_initials: "NP"
           }
         ]}
      end)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "New",
        "last_name" => "Name",
        "phone" => "+5585996532132",
        "updated_at" =>
          "#{NaiveDateTime.add(NaiveDateTime.utc_now(), 3600) |> DateTime.from_naive!("Etc/UTC") |> DateTime.to_iso8601()}",
        "addresses" => [
          %{
            "id" => 10_719_097_291_047,
            "address1" => "123 New St",
            "address2" => "Apt 2",
            "city" => "New City",
            "province" => "New Province",
            "country" => "Brazil",
            "zip" => "12345678",
            "name" => "Matheus Cunha",
            "province_code" => "NP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "default" => true
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _result} = Create.execute(organization.id, customer_data)

      updated_customer = Repo.get(Rms.Customers.Customer, customer.id) |> Repo.preload(:addresses)

      assert updated_customer.name == "New Name"
      assert updated_customer.email == "<EMAIL>"
      assert updated_customer.primary_phone_number == "+5585996532132"
    end

    test "doesn't update customer when incoming data is older" do
      organization = insert(:organization)

      customer =
        insert(:customer,
          organization: organization,
          name: "Current Name",
          email: "<EMAIL>",
          primary_phone_number: "+5585999999999",
          updated_at: NaiveDateTime.utc_now()
        )

      _mapping =
        insert(:customer_sync_mapping,
          organization: organization,
          customer: customer,
          source: "shopify",
          external_id: "gid://shopify/Customer/8616466579751"
        )

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Old",
        "last_name" => "Name",
        "phone" => "+5585996532132",
        "updated_at" =>
          "#{NaiveDateTime.add(NaiveDateTime.utc_now(), -3600) |> DateTime.from_naive!("Etc/UTC") |> DateTime.to_iso8601()}",
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _result} = Create.execute(organization.id, customer_data)

      unchanged_customer = Repo.get(Rms.Customers.Customer, customer.id)

      assert unchanged_customer.name == "Current Name"
      assert unchanged_customer.email == "<EMAIL>"
    end
  end

  describe "customer data transformation" do
    test "correctly transforms customer data with addresses through execute" do
      organization = insert(:organization)

      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "Test City",
             uf_name: "Test Province",
             uf_initials: "TP"
           }
         ]}
      end)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "address1" => "123 Test St",
            "address2" => "Apt 1",
            "city" => "Test City",
            "province" => "Test Province",
            "country" => "Brazil",
            "zip" => "12345678",
            "name" => "Test User",
            "province_code" => "TP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "default" => true
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, %{customer: customer}} = Create.execute(organization.id, customer_data)

      assert customer.name == "Test User"
      assert customer.email == "<EMAIL>"
      assert customer.primary_phone_number == "+5585996532132"
    end

    test "correctly transforms customer data without addresses through execute" do
      organization = insert(:organization)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, %{customer: customer}} = Create.execute(organization.id, customer_data)

      assert customer.name == "Test User"
      assert customer.email == "<EMAIL>"
      assert customer.primary_phone_number == "+5585996532132"
    end
  end

  describe "address handling" do
    test "creates customer with address from Shopify format" do
      organization = insert(:organization)

      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           }
         ]}
      end)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "address1" => "Rua Paim 262",
            "address2" => "Ap 411",
            "city" => "São Paulo",
            "province" => "São Paulo",
            "country" => "Brazil",
            "zip" => "01306-010",
            "province_code" => "SP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "name" => "Matheus Cunha"
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, %{customer: customer}} = Create.execute(organization.id, customer_data)

      customer_with_addresses = Repo.preload(customer, :addresses)

      assert length(customer_with_addresses.addresses) == 1
      [address] = customer_with_addresses.addresses

      assert address.receiver_name == "Matheus Cunha"
      assert address.city_name == "São Paulo"
      assert address.state == "SP"
      assert address.country_name == "Brazil"
      assert address.complement == "Ap 411"
    end

    test "handles address updates correctly" do
      organization = insert(:organization)

      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "São Paulo",
             uf_name: "São Paulo",
             uf_initials: "SP"
           },
           %{
             ibge_id: 3_550_309,
             name: "Fortaleza",
             uf_name: "Ceará",
             uf_initials: "CE"
           }
         ]}
      end)

      initial_customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "address1" => "Rua Paim 262",
            "address2" => "Ap 411",
            "city" => "São Paulo",
            "province" => "São Paulo",
            "country" => "Brazil",
            "zip" => "01306-010",
            "province_code" => "SP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "name" => "Matheus Cunha"
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      {:ok, %{customer: customer}} = Create.execute(organization.id, initial_customer_data)

      updated_customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "address1" => "Travessa Jataí 12",
            "address2" => "Ap 12",
            "city" => "Fortaleza",
            "province" => "Ceará",
            "country" => "Brazil",
            "zip" => "60192-105",
            "province_code" => "CE",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "name" => "Matheus Cunha"
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _result} = Create.execute(organization.id, updated_customer_data)

      # Check that the address was updated
      updated_customer = Repo.get(Rms.Customers.Customer, customer.id) |> Repo.preload(:addresses)

      assert length(updated_customer.addresses) == 1
      [address] = updated_customer.addresses

      assert address.city_name == "Fortaleza"
      assert address.street == "Travessa Jataí 12"
      assert address.complement == "Ap 12"
    end

    test "doesn't update address when it matches existing one" do
      organization = insert(:organization)

      # Create a customer with an address
      customer = insert(:customer, organization: organization)

      insert(:address,
        customer: customer,
        city_name: "São Paulo",
        street: "Rua Paim",
        number: "262",
        complement: "Ap 411",
        country_name: "Brazil"
      )

      # Create mapping
      insert(:customer_sync_mapping,
        organization: organization,
        customer: customer,
        source: "shopify",
        external_id: "gid://shopify/Customer/8616466579751"
      )

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "phone" => "+5585996532132",
        "addresses" => [
          %{
            "address1" => "Rua Paim 262",
            "address2" => "Ap 411",
            "city" => "São Paulo",
            "province" => "São Paulo",
            "country" => "Brazil",
            "zip" => "01306-010",
            "province_code" => "SP",
            "country_code" => "BR",
            "country_name" => "Brazil",
            "name" => "Matheus Cunha"
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      # Get the address count before update
      customer_before = Repo.get(Rms.Customers.Customer, customer.id) |> Repo.preload(:addresses)
      address_count_before = length(customer_before.addresses)
      address_id_before = hd(customer_before.addresses).id

      assert {:ok, _result} = Create.execute(organization.id, customer_data)

      # Check that no addresses were added or removed
      customer_after = Repo.get(Rms.Customers.Customer, customer.id) |> Repo.preload(:addresses)
      assert length(customer_after.addresses) == address_count_before
      assert hd(customer_after.addresses).id == address_id_before
    end
  end
end
