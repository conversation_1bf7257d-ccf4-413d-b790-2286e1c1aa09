defmodule Rms.Integrations.Shopify.DeleteWebhookHandlerTest do
  use Rms.DataCase, async: true

  import Rms.Factory

  alias Rms.Repo
  alias Rms.Integrations.Shopify.DeleteWebhookHandler

  # Payloads from controller test for convenience
  @product_delete_payload %{
    "metadata" => %{
      "action" => "DELETE",
      "type" => "INCREMENTAL",
      "resource" => "PRODUCT",
      "truncatedFields" => [],
      "occurred_at" => "2025-04-09T10:00:04-03:00"
    },
    "productFeed" => %{
      "id" => "gid://shopify/ProductFeed/6595051815",
      "shop_id" => "gid://shopify/Shop/68076077351",
      "country" => "BR",
      "language" => "PT_BR"
    },
    "product" => %{
      "id" => "gid://shopify/Product/9685825683751",
      "updatedAt" => "2025-04-09T10:00:04-03:00"
    },
    "products" => nil
  }

  @variant_delete_payload %{
    "metadata" => %{
      "action" => "DELETE",
      "type" => "INCREMENTAL",
      "resource" => "VARIANT",
      "truncatedFields" => [],
      "occurred_at" => "2025-04-09T10:21:24-03:00"
    },
    "productFeed" => %{
      "id" => "gid://shopify/ProductFeed/6595051815",
      "shop_id" => "gid://shopify/Shop/68076077351",
      "country" => "BR",
      "language" => "PT_BR"
    },
    "product" => %{
      "id" => "gid://shopify/Product/9797255659815",
      "variants" => %{
        "edges" => [
          %{
            "node" => %{
              "id" => "gid://shopify/ProductVariant/49914794606887"
            }
          }
        ]
      },
      "updatedAt" => "2025-04-09T10:21:24-03:00"
    },
    "products" => nil
  }

  describe "handle_delet.ide/2" do
    setup do
      org = insert(:organization)
      product = insert(:product, organization: org)

      variant1 = insert(:product_variant, product: product, organization: org, sku: "VAR1")
      variant2 = insert(:product_variant, product: product, organization: org, sku: "VAR2")

      insert(:product_mapping,
        product_id: product.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/Product/9685825683751"
      )

      insert(:product_variant_mapping,
        product_variant_id: variant1.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/ProductVariant/49914794606887"
      )

      insert(:product_variant_mapping,
        product_variant_id: variant2.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/ProductVariant/VARIANT2_ID"
      )

      {:ok,
       %{
         org: org,
         product: product,
         variant1: variant1,
         variant2: variant2
       }}
    end

    test "when resource is PRODUCT, archives all associated variants", %{
      org: org,
      variant1: variant1,
      variant2: variant2
    } do
      assert :ok = DeleteWebhookHandler.handle_delete(org.id, @product_delete_payload)

      variant1 = Repo.reload!(variant1)
      assert variant1.archived_at

      variant2 = Repo.reload!(variant2)
      assert variant2.archived_at
    end

    test "when resource is PRODUCT and product mapping not found, returns :ok and does nothing",
         %{
           org: org,
           variant1: variant1,
           variant2: variant2
         } do
      not_found_payload =
        @product_delete_payload
        |> put_in(["product", "id"], "gid://shopify/Product/NOT_FOUND")

      assert :ok = DeleteWebhookHandler.handle_delete(org.id, not_found_payload)

      variant1 = Repo.reload!(variant1)
      refute variant1.archived_at

      variant2 = Repo.reload!(variant2)
      refute variant2.archived_at
    end

    test "when resource is VARIANT, archives the specific variant", %{
      org: org,
      variant1: variant1,
      variant2: variant2
    } do
      assert :ok = DeleteWebhookHandler.handle_delete(org.id, @variant_delete_payload)

      variant1 = Repo.reload!(variant1)
      assert variant1.archived_at

      variant2 = Repo.reload!(variant2)
      refute variant2.archived_at
    end

    test "when resource is VARIANT and variant mapping not found, returns :ok and does nothing",
         %{
           org: org,
           variant1: variant1,
           variant2: variant2
         } do
      not_found_payload =
        @variant_delete_payload
        |> put_in(
          ["product", "variants", "edges", Access.at(0), "node", "id"],
          "gid://shopify/ProductVariant/NOT_FOUND"
        )

      assert :ok = DeleteWebhookHandler.handle_delete(org.id, not_found_payload)

      variant1 = Repo.reload!(variant1)
      refute variant1.archived_at

      variant2 = Repo.reload!(variant2)
      refute variant2.archived_at
    end

    test "when resource is unknown, returns error", %{org: org} do
      unknown_payload = put_in(@product_delete_payload, ["metadata", "resource"], "UNKNOWN")

      assert {:error, :unknown_resource_type} =
               DeleteWebhookHandler.handle_delete(org.id, unknown_payload)
    end
  end
end
