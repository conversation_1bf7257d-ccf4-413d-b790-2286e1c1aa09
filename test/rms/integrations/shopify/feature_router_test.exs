defmodule Rms.Integrations.Shopify.FeatureRouterTest do
  use Rms.DataCase

  import Mox

  alias Rms.Integrations.Shopify.FeatureRouter

  setup :verify_on_exit!

  setup_all do
    {:ok,
     %{
       create_order_fun: fn -> :create_order_called end,
       draft_order_fun: fn -> :draft_order_called end
     }}
  end

  describe "route/3" do
    test "raises error when feature_name is not in feature_keys" do
      assert_raise RuntimeError,
                   "Feature not defined on. Got invalid_feature on [:create_order]",
                   fn ->
                     FeatureRouter.route(:invalid_feature, %{}, %{})
                   end
    end

    test "raises error when has some unimplemented for :create_order feature", %{
      create_order_fun: create_order_fun
    } do
      assert_raise RuntimeError, fn ->
        FeatureRouter.route(:create_order, %{organization_id: 123}, %{
          create_order: create_order_fun
        })
      end
    end

    test "calls the correct flow function for create_order with correct params", %{
      create_order_fun: create_order_fun,
      draft_order_fun: draft_order_fun
    } do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, fn _ -> true end)

      assert :create_order_called ==
               FeatureRouter.route(
                 :create_order,
                 %{
                   organization_id: 123,
                   fulfillments: [%{shipping_method: "delivery"}, %{shipping_method: "in-store"}]
                 },
                 %{create_order: create_order_fun, draft_order: draft_order_fun}
               )
    end

    test "calls draft_order flow when feature flag is false", %{
      draft_order_fun: draft_order_fun,
      create_order_fun: create_order_fun
    } do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, fn _ -> false end)

      assert :draft_order_called ==
               FeatureRouter.route(
                 :create_order,
                 %{organization_id: 123, fulfillments: []},
                 %{draft_order: draft_order_fun, create_order: create_order_fun}
               )
    end
  end
end
