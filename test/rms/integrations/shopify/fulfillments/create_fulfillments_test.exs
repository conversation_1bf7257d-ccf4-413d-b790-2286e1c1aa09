defmodule Rms.Integrations.Shopify.Fulfillments.CreateFulfillmentsTest do
  use Rms.DataCase
  use Rms.EventsCase

  import Mox
  alias Rms.Integrations.Shopify.Fulfillments.CreateFulfillments

  setup do
    org = insert(:organization)

    location = insert(:location, organization: org)

    insert(:location_mapping, organization: org, location: location)

    product = insert(:product, organization: org)
    product_variant = insert(:product_variant, organization: org, product: product)

    order =
      insert(:order, %{
        organization: org,
        location: location,
        fulfillments: [
          insert(:fulfillment, %{
            shipping_method: "delivery",
            organization: org
          }),
          insert(:fulfillment, %{
            shipping_method: "in-store",
            organization: org
          })
        ]
      })

    {:ok, _} = Rms.Settings.create_organization_setting(org.id, "ecommerce", "shopify")

    %{
      org: org,
      location: location,
      order: order,
      product: product,
      product_variant: product_variant
    }
  end

  describe "execute/1" do
    test "ensure: :draft_order flow is called - with disabled feature flag",
         %{org: org, order: order} do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, 2, fn _ -> false end)

      {:ok, _} = CreateFulfillments.execute(order)

      assert_enqueued(
        worker: Rms.Integrations.Shopify.CreateDraftOrderWorker,
        args: %{
          id: order.fulfillments |> Enum.at(0) |> Map.get(:id),
          organization_id: org.id
        },
        prefix: "public"
      )

      assert_enqueued(
        worker: Rms.Integrations.Shopify.CreateDraftOrderWorker,
        args: %{
          id: order.fulfillments |> Enum.at(1) |> Map.get(:id),
          organization_id: org.id
        },
        prefix: "public"
      )
    end

    test "ensure: :create_order flow is called - with enabled feature flag",
         %{org: org, order: order} do
      expect(Rms.Clients.FeatureFlag.Mock, :should_create_order?, 2, fn _ -> true end)

      {:ok, _} = CreateFulfillments.execute(order)

      refute_enqueued(
        worker: Rms.Integrations.Shopify.CreateDraftOrderWorker,
        args: %{
          id: order.fulfillments |> Enum.at(0) |> Map.get(:id),
          organization_id: org.id
        },
        prefix: "public"
      )

      refute_enqueued(
        worker: Rms.Integrations.Shopify.CreateDraftOrderWorker,
        args: %{
          id: order.fulfillments |> Enum.at(1) |> Map.get(:id),
          organization_id: org.id
        },
        prefix: "public"
      )
    end
  end
end
