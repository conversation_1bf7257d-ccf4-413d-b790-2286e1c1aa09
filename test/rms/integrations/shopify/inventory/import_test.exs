defmodule Rms.Integrations.Shopify.Inventory.ImportTest do
  use Rms.DataCase
  use ExUnit.Case

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  describe "execute/1 when ther is an inventory mapping" do
    test "correct return the inventory quantity" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/InventoryItem/1"
      )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory, fn _,
                                                             "gid://shopify/InventoryItem/1",
                                                             "gid://shopify/Location/2" ->
        {:ok,
         %{
           "id" => "gid://shopify/InventoryItem/1",
           "inventoryLevel" => %{
             "id" => "gid://shopify/InventoryLevel/1?inventory_item_id=2",
             "location" => %{
               "id" => "gid://shopify/Location/2",
               "name" => "Matriz (Ecommerce)"
             },
             "quantities" => [%{"name" => "available", "quantity" => 497}]
           }
         }}
      end)

      assert {:ok,
              %{
                "external_inventory_id" => "gid://shopify/InventoryItem/1",
                "quantity" => 497
              }} == Shopify.Inventory.Import.execute(org.id, pv, location)
    end

    test "correct return the inventory quantity when it is negative" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/InventoryItem/1"
      )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory, fn _,
                                                             "gid://shopify/InventoryItem/1",
                                                             "gid://shopify/Location/2" ->
        {:ok,
         %{
           "id" => "gid://shopify/InventoryItem/1",
           "inventoryLevel" => %{
             "id" => "gid://shopify/InventoryLevel/1?inventory_item_id=2",
             "location" => %{
               "id" => "gid://shopify/Location/2",
               "name" => "Matriz (Ecommerce)"
             },
             "quantities" => [%{"name" => "available", "quantity" => -497}]
           }
         }}
      end)

      assert {:ok,
              %{
                "external_inventory_id" => "gid://shopify/InventoryItem/1",
                "quantity" => 0
              }} == Shopify.Inventory.Import.execute(org.id, pv, location)
    end

    test "returns a error when there is no shopify inventory" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/InventoryItem/1"
      )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory, fn _,
                                                             "gid://shopify/InventoryItem/1",
                                                             "gid://shopify/Location/2" ->
        {:ok, nil}
      end)

      assert {:error, "inventory not found"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)
    end

    test "returns a error when there is no shopify location" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/InventoryItem/1"
      )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory, fn _,
                                                             "gid://shopify/InventoryItem/1",
                                                             "gid://shopify/Location/2" ->
        {:ok,
         %{
           "id" => "gid://shopify/InventoryItem/1",
           "inventoryLevel" => nil
         }}
      end)

      assert {:error, "location not found"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)
    end

    test "returns a error when there is no location mapping" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "vtex",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/InventoryItem/1"
      )

      assert {:error, "location_mapping not found"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)
    end
  end

  describe "execute/1 when there is no inventory mapping" do
    test "uses variant mapping to fetch inventory when available" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        source: "shopify",
        organization: org,
        product_variant: pv,
        external_id: "gid://shopify/ProductVariant/1"
      )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_variant_inventory, fn _,
                                                            "gid://shopify/ProductVariant/1",
                                                            "gid://shopify/Location/2" ->
        {:ok,
         %{
           "productVariant" => %{
             "inventoryItem" => %{
               "id" => "gid://shopify/InventoryItem/1",
               "inventoryLevel" => %{
                 "quantities" => [%{"name" => "available", "quantity" => 497}]
               }
             }
           }
         }}
      end)

      assert {:ok,
              %{
                "external_inventory_id" => "gid://shopify/InventoryItem/1",
                "quantity" => 497
              }} == Shopify.Inventory.Import.execute(org.id, pv, location)

      [mapping] = Rms.Repo.all(Rms.Integrations.InventoryItemMapping)
      assert mapping.source == "shopify"
      assert mapping.product_variant_id == pv.id
      assert mapping.external_id == "gid://shopify/InventoryItem/1"
    end

    test "correct return the inventory quantity and creste a inventory mapping" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory_by_sku, fn _,
                                                                    _,
                                                                    "gid://shopify/Location/2",
                                                                    _ ->
        {:ok,
         %{
           "nodes" => [
             %{
               "id" => "gid://shopify/InventoryItem/1",
               "inventoryLevel" => %{
                 "id" =>
                   "gid://shopify/InventoryLevel/114082906422?inventory_item_id=46211038675254",
                 "location" => %{
                   "id" => "gid://shopify/Location/2",
                   "name" => "Matriz (Ecommerce)"
                 },
                 "quantities" => [%{"name" => "available", "quantity" => 497}]
               }
             }
           ]
         }}
      end)

      assert {:ok,
              %{
                "external_inventory_id" => "gid://shopify/InventoryItem/1",
                "quantity" => 497
              }} == Shopify.Inventory.Import.execute(org.id, pv, location)

      [mapping] = Rms.Repo.all(Rms.Integrations.InventoryItemMapping)
      assert mapping.source == "shopify"
      assert mapping.product_variant_id == pv.id
      assert mapping.external_id == "gid://shopify/InventoryItem/1"
    end

    test "returns a error when there is no shopify inventory" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory_by_sku, fn _,
                                                                    _,
                                                                    "gid://shopify/Location/2",
                                                                    _ ->
        {:ok,
         %{
           "nodes" => []
         }}
      end)

      assert {:error, "inventory not fund"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)

      [] = Rms.Repo.all(Rms.Integrations.InventoryItemMapping)
    end

    test "returns a error when there is no shopify location" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location_inventory_by_sku, fn _,
                                                                    _,
                                                                    "gid://shopify/Location/2",
                                                                    _ ->
        {:ok,
         %{
           "nodes" => [
             %{
               "id" => "gid://shopify/InventoryItem/1",
               "inventoryLevel" => nil
             }
           ]
         }}
      end)

      assert {:error, "location not found"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)

      [] = Rms.Repo.all(Rms.Integrations.InventoryItemMapping)
    end

    test "returns a error when there is no location mapping" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      insert(:location_mapping,
        source: "vtex",
        organization: org,
        location: location,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      assert {:error, "location_mapping not found"} ==
               Shopify.Inventory.Import.execute(org.id, pv, location)

      [] = Rms.Repo.all(Rms.Integrations.InventoryItemMapping)
    end
  end
end
