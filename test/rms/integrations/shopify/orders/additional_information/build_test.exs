defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.BuildTest do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.Build

  @preload [
    :shipping_settings,
    order: [
      :discounts,
      [transaction: :payments],
      customer: [:addresses],
      location: [:location_mappings]
    ],
    line_items: [:discounts, product_variant: [:product_variant_mappings]]
  ]

  describe "execute/1" do
    setup do
      org = insert(:organization)
      location = insert(:location, organization: org)
      insert(:location_mapping, organization: org, location: location)

      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, organization: org, product: product)

      order = insert(:order, %{organization: org, location: location})
      # Ensure the order has a transaction and payments
      transaction = insert(:transaction, organization: org, order: order)

      insert(:payment, %{
        organization: org,
        transaction: transaction,
        method: "credit_card",
        amount: order.total_price || Decimal.new("10.00")
      })

      {:ok,
       %{
         organization: org,
         location: location,
         product_variant: product_variant,
         order: order
       }}
    end

    test "returns additional information for fulfillment", %{
      organization: org,
      location: location,
      order: order,
      product_variant: product_variant
    } do
      fulfillment =
        insert(:fulfillment, %{
          shipping_method: "in-store",
          organization: org,
          order: order,
          line_items: [
            insert(:line_item, %{
              organization: org,
              location: location,
              price: Decimal.new("10.50"),
              list_price: Decimal.new("151.0"),
              manual_discount: Decimal.new("0.00"),
              quantity: 1,
              product_variant: product_variant
            })
          ]
        })
        |> Rms.Repo.preload(@preload)

      additional_infos = Build.execute(fulfillment)
      assert is_list(additional_infos)
    end

    test "should include discount keys and customer keys", %{
      organization: org,
      location: location,
      order: order,
      product_variant: product_variant
    } do
      fulfillment =
        insert(:fulfillment, %{
          shipping_method: "in-store",
          organization: org,
          order: order,
          line_items: [
            insert(:line_item, %{
              organization: org,
              location: location,
              price: Decimal.new("10.50"),
              list_price: Decimal.new("151.0"),
              manual_discount: Decimal.new("0.00"),
              quantity: 1,
              product_variant: product_variant
            })
          ]
        })
        |> Rms.Repo.preload(@preload)

      additional_infos = Build.execute(fulfillment)

      assert Enum.any?(additional_infos, &(&1.key == "line_items_discount_info"))
      assert Enum.any?(additional_infos, &(&1.key == "order_discounts_info"))
      assert Enum.any?(additional_infos, &(&1.key == "customer_document"))
    end
  end
end
