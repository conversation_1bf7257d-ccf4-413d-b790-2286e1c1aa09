defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsTest do
  use Rms.DataCase

  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.Payments
  import Rms.Factory

  describe "build/1" do
    setup do
      {:ok, flag} = :ldclient_testdata.flag("new-shopify-payments")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([false], flag))
    end

    test "returns empty list when no payments are present" do
      assert %{key: "iglu_payments", value: "[]"} == Payments.build(%{})
    end

    test "formats credit card payment with conciliation" do
      org = insert(:organization)

      payment =
        insert(:payment,
          organization: org,
          method: "credit_card",
          amount: Decimal.new("100.00"),
          metadata: %{
            "installments" => "3",
            "nsu" => "123456",
            "aut" => "789012"
          }
        )

      result = Payments.build(%{order: %{transaction: %{payments: [payment]}}})

      expected = [
        %{
          "method" => "credit",
          "installments" => "3",
          "nsu" => "123456",
          "aut" => "789012",
          "amount" => 100.00,
          "conciliation" => true
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats debit card payment" do
      org = insert(:organization)

      payment =
        insert(:payment,
          organization: org,
          method: "debit_card",
          amount: Decimal.new("50.00"),
          metadata: %{
            "nsu" => "123456",
            "aut" => "789012",
            "store_payment_receipt" => true
          }
        )

      result = Payments.build(%{order: %{transaction: %{payments: [payment]}}})

      expected = [
        %{
          "method" => "debit",
          "installments" => "1",
          "nsu" => "123456",
          "aut" => "789012",
          "amount" => 50.00,
          "conciliation" => false
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats pix payment with conciliation" do
      org = insert(:organization)

      payment =
        insert(:payment,
          organization: org,
          method: "pix",
          amount: Decimal.new("75.50"),
          metadata: %{
            "nsu" => "123456"
          }
        )

      result = Payments.build(%{order: %{transaction: %{payments: [payment]}}})

      expected = [
        %{
          "method" => "pix",
          "installments" => "1",
          "amount" => 75.50,
          "conciliation" => true,
          "pix_id" => 123_456
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats payment link with pagarme reference" do
      {:ok, flag} = :ldclient_testdata.flag("new-shopify-payments")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([true], flag))
      org = insert(:organization)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          transaction: transaction,
          organization: org,
          method: "payment_link",
          amount: Decimal.new("150.00"),
          metadata: %{}
        )

      insert(:external_payment_reference,
        payment: payment,
        organization: payment.organization,
        partner: "pagarme",
        external_id: "pay_123456"
      )

      result = Payments.build(%{order: order})

      expected = [
        %{
          "method" => "link",
          "installments" => "1",
          "amount" => "150.00",
          "conciliation" => false,
          "pagarmeId" => "pay_123456"
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats payment link without pagarme reference" do
      org = insert(:organization)

      payment =
        insert(:payment,
          organization: org,
          method: "payment_link",
          amount: Decimal.new("150.00"),
          metadata: %{}
        )

      result = Payments.build(%{order: %{transaction: %{payments: [payment]}}})

      expected = [
        %{
          "method" => "link",
          "installments" => "1",
          "amount" => 150.00,
          "conciliation" => false
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats multiple payments" do
      org = insert(:organization)

      credit_payment =
        insert(:payment,
          organization: org,
          method: "credit_card",
          amount: Decimal.new("100.00"),
          metadata: %{
            "installments" => "2",
            "nsu" => "123456",
            "aut" => "789012"
          }
        )

      pix_payment =
        insert(:payment,
          organization: org,
          method: "pix",
          amount: Decimal.new("50.00"),
          metadata: %{
            "nsu" => "123"
          }
        )

      result =
        Payments.build(%{order: %{transaction: %{payments: [credit_payment, pix_payment]}}})

      expected = [
        %{
          "method" => "credit",
          "installments" => "2",
          "nsu" => "123456",
          "aut" => "789012",
          "amount" => 100.00,
          "conciliation" => true
        },
        %{
          "method" => "pix",
          "installments" => "1",
          "amount" => 50.00,
          "conciliation" => true,
          "pix_id" => 123
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end

    test "formats invoice payments" do
      org = insert(:organization)

      payment =
        insert(:payment,
          organization: org,
          method: "credit_card",
          amount: Decimal.new("200.00"),
          metadata: %{
            "installments" => "1",
            "nsu" => "123456",
            "aut" => "789012"
          }
        )

      result = Payments.build(%{invoice_payments: [%{payment_id: payment.id, payment: payment}]})

      expected = [
        %{
          "method" => "credit",
          "installments" => "1",
          "nsu" => "123456",
          "aut" => "789012",
          "amount" => 200.00,
          "conciliation" => true
        }
      ]

      %{key: "iglu_payments", value: value} = result
      assert Jason.decode!(value) == expected
    end
  end
end
