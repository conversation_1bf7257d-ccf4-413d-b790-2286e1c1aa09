defmodule Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV2Test do
  use Rms.DataCase

  import Rms.Factory

  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.PaymentsV2

  describe "build/1" do
    test "creates multiple iglu_payments entries for payment_link with multiple charges" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment_link =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          method: "payment_link",
          amount: Decimal.new("200"),
          metadata: %{
            "charges" => [
              %{
                "amount" => 100,
                "tid" => "3958796684",
                "nsu" => "3958796684",
                "aut" => "087767",
                "installments" => "1"
              },
              %{
                "amount" => 100,
                "tid" => "3958796685",
                "nsu" => "3958796685",
                "aut" => "087768",
                "installments" => "1"
              }
            ]
          }
        )

      insert(:external_payment_reference,
        payment: payment_link,
        partner: "pagarme",
        external_id: "or_multi_123",
        organization: organization
      )

      # Add a credit card payment
      insert(:payment,
        transaction: transaction,
        organization: organization,
        method: "credit_card",
        amount: Decimal.new("100"),
        metadata: %{
          "installments" => 3,
          "nsu" => "123456",
          "aut" => "654321"
        }
      )

      order = Rms.Repo.preload(order, transaction: [payments: [:external_payment_reference]])

      result = PaymentsV2.build(%{order: order})

      payments = Jason.decode!(result.value)

      assert length(payments) == 3

      payment_link_payments = Enum.filter(payments, fn payment -> payment["method"] == "link" end)

      assert length(payment_link_payments) == 2

      first_charge = Enum.find(payment_link_payments, fn p -> p["tid"] == "3958796684" end)
      assert first_charge["nsu"] == "3958796684"
      assert first_charge["aut"] == "087767"
      assert first_charge["pagarmeId"] == "or_multi_123"
      assert first_charge["conciliation"] == false

      second_charge = Enum.find(payment_link_payments, fn p -> p["tid"] == "3958796685" end)
      assert second_charge["nsu"] == "3958796685"
      assert second_charge["aut"] == "087768"
      assert second_charge["pagarmeId"] == "or_multi_123"
      assert second_charge["conciliation"] == false

      credit_payment = Enum.find(payments, fn p -> p["method"] == "credit" end)
      assert credit_payment["installments"] == "3"
      assert credit_payment["nsu"] == "123456"
      assert credit_payment["aut"] == "654321"
    end
  end

  describe "build_payment/1" do
    test "formats return_credit payment method correctly" do
      payment =
        build(:payment, method: "return_credit", amount: Decimal.new("100"), metadata: %{})

      assert %{
               method: "return-credit",
               installments: "1",
               amount: payment.amount,
               conciliation: false
             } == PaymentsV2.build_payment(payment)
    end

    test "formats other payment methods correctly" do
      credit_card_payment = build(:payment, method: "credit_card", metadata: %{})
      debit_card_payment = build(:payment, method: "debit_card", metadata: %{})
      payment_link_payment = build(:payment, method: "payment_link", metadata: %{})
      pix_payment = build(:payment, method: "pix", metadata: %{})
      cash_payment = build(:payment, method: "cash", metadata: %{})

      assert %{method: "credit"} = PaymentsV2.build_payment(credit_card_payment)
      assert %{method: "debit"} = PaymentsV2.build_payment(debit_card_payment)
      assert %{method: "link"} = PaymentsV2.build_payment(payment_link_payment)
      assert %{method: "pix"} = PaymentsV2.build_payment(pix_payment)
      assert %{method: "cash"} = PaymentsV2.build_payment(cash_payment)
    end

    test "formats payment_link with single charge correctly" do
      organization = insert(:organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: Decimal.new("100"),
          organization: organization,
          metadata: %{
            "charges" => [
              %{
                "amount" => 100,
                "tid" => "3958796684",
                "nsu" => "3958796684",
                "aut" => "087767",
                "installments" => "1"
              }
            ]
          }
        )

      insert(:external_payment_reference,
        payment: payment,
        partner: "pagarme",
        external_id: "or_123456",
        organization: organization
      )

      payment = Rms.Repo.preload(payment, :external_payment_reference)

      [payment_result] = PaymentsV2.build_payment(payment)

      assert payment_result.method == "link"
      assert payment_result.installments == "1"
      assert payment_result.amount == payment.amount
      assert payment_result.conciliation == false
      assert payment_result.pagarmeId == "or_123456"
      assert payment_result.tid == "3958796684"
      assert payment_result.nsu == "3958796684"
      assert payment_result.aut == "087767"
    end
  end
end
