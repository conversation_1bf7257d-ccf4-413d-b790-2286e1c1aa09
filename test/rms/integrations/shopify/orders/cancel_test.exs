defmodule Rms.Integrations.Shopify.Orders.CancelTest do
  use Rms.DataCase

  alias Rms.Integrations.Shopify.Orders.Cancel

  import Mox
  setup :verify_on_exit!

  describe "execute/1" do
    test "correct cancel a shopify order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          external_reference: "gid://shopify/Order/1",
          order: order,
          external_reference: "123",
          organization: org
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "order" => %{
               "cancelledAt" => "2024-09-06T16:43:30Z",
               "fulfillmentOrders" => %{
                 "nodes" => [
                   %{
                     "fulfillments" => %{
                       "nodes" => [
                         %{
                           "id" => "gid://shopify/Fulfillment/1",
                           "status" => "CANCELLED"
                         }
                       ]
                     },
                     "id" => "gid://shopify/FulfillmentOrder/2",
                     "status" => "CLOSED",
                     "supportedActions" => []
                   },
                   %{
                     "fulfillments" => %{"nodes" => []},
                     "id" => "gid://shopify/FulfillmentOrder/3",
                     "status" => "CLOSED",
                     "supportedActions" => []
                   }
                 ]
               },
               "id" => "gid://shopify/Order/4"
             }
           },
           "extensions" => %{
             "cost" => %{
               "actualQueryCost" => 8,
               "requestedQueryCost" => 168,
               "throttleStatus" => %{
                 "currentlyAvailable" => 1992,
                 "maximumAvailable" => 2000.0,
                 "restoreRate" => 100.0
               }
             }
           }
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_fulfillment, 1, fn _, _ ->
        {:ok,
         %{
           "fulfillment" => %{
             "id" => "gid://shopify/Fulfillment/1",
             "status" => "CANCELLED"
           },
           "userErrors" => []
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_order, 1, fn _, _ ->
        {:ok, %{"job" => %{"done" => false}, "userErrors" => []}}
      end)

      assert {:ok, _} =
               Cancel.execute(org.id, external_id, "")
    end

    test "return error when there is no shopify order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          external_reference: "gid://shopify/DraftOrder/1",
          order: order,
          external_reference: "123",
          organization: org
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "order" => nil
           },
           "extensions" => %{
             "cost" => %{
               "actualQueryCost" => 8,
               "requestedQueryCost" => 168,
               "throttleStatus" => %{
                 "currentlyAvailable" => 1992,
                 "maximumAvailable" => 2000.0,
                 "restoreRate" => 100.0
               }
             }
           }
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_fulfillment, 0, fn _, _ ->
        {:ok,
         %{
           "fulfillment" => %{
             "id" => "gid://shopify/Fulfillment/1",
             "status" => "CANCELLED"
           },
           "userErrors" => []
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_order, 0, fn _, _ ->
        {:ok, %{"job" => %{"done" => false}, "userErrors" => []}}
      end)

      assert {:error, "shopify order not found"} ==
               Cancel.execute(org.id, external_id, "")
    end

    test "return error when can not cancel a fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          external_reference: "gid://shopify/Order/1",
          order: order,
          external_reference: "123",
          organization: org
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "order" => %{
               "cancelledAt" => "2024-09-06T16:43:30Z",
               "fulfillmentOrders" => %{
                 "nodes" => [
                   %{
                     "fulfillments" => %{
                       "nodes" => [
                         %{
                           "id" => "gid://shopify/Fulfillment/1",
                           "status" => "CANCELLED"
                         }
                       ]
                     },
                     "id" => "gid://shopify/FulfillmentOrder/2",
                     "status" => "CLOSED",
                     "supportedActions" => []
                   },
                   %{
                     "fulfillments" => %{"nodes" => []},
                     "id" => "gid://shopify/FulfillmentOrder/3",
                     "status" => "CLOSED",
                     "supportedActions" => []
                   }
                 ]
               },
               "id" => "gid://shopify/Order/4"
             }
           },
           "extensions" => %{
             "cost" => %{
               "actualQueryCost" => 8,
               "requestedQueryCost" => 168,
               "throttleStatus" => %{
                 "currentlyAvailable" => 1992,
                 "maximumAvailable" => 2000.0,
                 "restoreRate" => 100.0
               }
             }
           }
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_fulfillment, 1, fn _, _ ->
        {:error,
         %{
           "fulfillment" => nil,
           "userErrors" => ["error"]
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :cancel_order, 0, fn _, _ ->
        {:ok, %{"job" => %{"done" => false}, "userErrors" => []}}
      end)

      assert {:error, %{"fulfillment" => nil, "userErrors" => ["error"]}} ==
               Cancel.execute(org.id, external_id, "")
    end
  end
end
