defmodule Rms.Integrations.Shopify.CreateDraftOrderTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Integrations.Shopify.Orders.CreateDraftOrder

  import Mox
  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    loc = insert(:location, organization: org)
    insert(:shopify_storefront_credential, organization: org)
    {:ok, %{org: org, location: loc}}
  end

  # test "process/1 successfully creates a new draft order on Shopify", %{org: org, location: loc} do
  #   Mox.expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
  #     :mock_client
  #   end)

  #   Mox.expect(Rms.Integrations.Shopify.Mock, :simulate_cart, 1, fn _, _ ->
  #     {:ok,
  #      """
  #      \r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 361\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\",\"createdAt\":\"2024-07-18T18:49:56Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\"},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 780\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"deliveryMethodType\":\"LOCAL\",\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Bicicleta\",\"handle\":\"********************************\",\"estimatedCost\":{\"amount\":\"25.0\"}},{\"title\":\"Moto\",\"handle\":\"f20de5611356f6f568bdb9387afa955d\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Carro\",\"handle\":\"fd2120274e392769c872754fb2eac8f2\",\"estimatedCost\":{\"amount\":\"100.0\"}},{\"title\":\"Caminhão\",\"handle\":\"81d69ad7e95d44f50319e91517cd16fb\",\"estimatedCost\":{\"amount\":\"100.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"3.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--
  #      """}
  #   end)

  #   Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
  #     :mock_client
  #   end)

  #   Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
  #     {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
  #   end)

  #   customer = insert(:customer, organization: org)
  #   order = insert(:order, organization: org, customer: customer, location: loc)
  #   product = insert(:product, organization: org)

  #   pv =
  #     insert(:product_variant,
  #       product: product,
  #       product_variant_mappings:
  #         build_list(1, :product_variant_mapping, source: "shopify", organization: org),
  #       organization: org
  #     )

  #   insert(:shopify_credential, organization: org)

  #   insert(:organization_setting,
  #     key: "ecommerce",
  #     value: %{data: "shopify"},
  #     organization: org
  #   )

  #   fulfillment =
  #     insert(:fulfillment,
  #       ecommerce: "shopify",
  #       shipping_method: "in-store",
  #       order: order,
  #       organization: org
  #     )

  #   _line_item =
  #     insert(:line_item,
  #       organization: org,
  #       product_variant: pv,
  #       location: loc,
  #       shipping_method: "in-store",
  #       fulfillment: fulfillment
  #     )

  #   id = fulfillment.id

  #   assert {:ok, %{id: ^id, external_reference: "gid://shopify/DraftOrder/123"}} =
  #            CreateDraftOrder.run(org.id, fulfillment.id, customer.id, %{})
  # end

  test "process/1 returns a error for invalid shipping method", %{org: org, location: loc} do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    end)

    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "local-pickup",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup",
        fulfillment: fulfillment
      )

    assert {:discard, "invalid shipping method"} =
             CreateDraftOrder.run(org.id, fulfillment.id, customer.id)
  end

  describe "for a delivery fulfiilment" do
    # test "process/1 successfully creates a new draft order on Shopify", %{org: org, location: loc} do
    #   Mox.expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
    #     :mock_client
    #   end)

    #   Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
    #     :mock_client
    #   end)

    #   Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
    #     {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    #   end)

    #   Mox.expect(Rms.Integrations.Shopify.Mock, :simulate_cart, 1, fn _, _ ->
    #     {:ok,
    #      """
    #      \r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 361\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\",\"createdAt\":\"2024-07-18T18:49:56Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\"},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 780\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Bicicleta\",\"handle\":\"********************************\",\"estimatedCost\":{\"amount\":\"25.0\"}},{\"title\":\"Moto\",\"handle\":\"f20de5611356f6f568bdb9387afa955d\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Carro\",\"handle\":\"fd2120274e392769c872754fb2eac8f2\",\"estimatedCost\":{\"amount\":\"100.0\"}},{\"title\":\"Caminhão\",\"handle\":\"81d69ad7e95d44f50319e91517cd16fb\",\"estimatedCost\":{\"amount\":\"100.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"3.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--
    #      """}
    #   end)

    #   shipping_address = %{
    #     receiver_name: "teste",
    #     city_name: "São Paulo",
    #     state: "SP",
    #     country_name: "BRA",
    #     neighborhood: "Jardim Paulista",
    #     street: "Av. Paulista",
    #     street_type: "Avenue",
    #     number: "1000",
    #     zip: "01310100",
    #     complement: "Apt 1001"
    #   }

    #   customer = insert(:customer, organization: org)

    #   order =
    #     insert(:order,
    #       organization: org,
    #       customer: customer,
    #       shipping_address: shipping_address,
    #       location: loc
    #     )

    #   product = insert(:product, organization: org)

    #   pv =
    #     insert(:product_variant,
    #       product: product,
    #       product_variant_mappings:
    #         build_list(1, :product_variant_mapping, source: "shopify", organization: org),
    #       organization: org
    #     )

    #   insert(:shopify_credential, organization: org)

    #   insert(:organization_setting,
    #     key: "ecommerce",
    #     value: %{data: "shopify"},
    #     organization: org
    #   )

    #   fulfillment =
    #     insert(:fulfillment,
    #       ecommerce: "shopify",
    #       shipping_method: "delivery",
    #       shipping_settings: %{
    #         price: 0,
    #         ecommerce: "shopify",
    #         organization: org,
    #         settings: %{
    #           "deliveryMethodType" => "SHIPPING",
    #           "estimatedCost" => %{
    #             "amount" => "100.0"
    #           },
    #           "handle" => "3",
    #           "title" => "Carro"
    #         }
    #       },
    #       order: order,
    #       organization: org
    #     )

    #   _line_item =
    #     insert(:line_item,
    #       organization: org,
    #       product_variant: pv,
    #       location: loc,
    #       shipping_method: "delivery",
    #       fulfillment: fulfillment,
    #       shipping_settings: %{
    #         "deliveryMethodType" => "SHIPPING",
    #         "estimatedCost" => %{
    #           "amount" => "100.0"
    #         },
    #         "handle" => "3",
    #         "title" => "Carro"
    #       }
    #     )

    #   id = fulfillment.id

    #   assert {:ok, %{id: ^id, external_reference: "gid://shopify/DraftOrder/123"}} =
    #            CreateDraftOrder.run(org.id, fulfillment.id, customer.id, %{})
    # end

    test "process/1 does not create a draft order when settings is nil", %{
      org: org,
      location: loc
    } do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      shipping_address = %{
        city_name: "São Paulo",
        state: "SP",
        country_name: "BRA",
        neighborhood: "Jardim Paulista",
        street: "Av. Paulista",
        street_type: "Avenue",
        number: "1000",
        zip: "01310100",
        complement: "Apt 1001"
      }

      customer = insert(:customer, organization: org)

      order =
        insert(:order, organization: org, customer: customer, shipping_address: shipping_address)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          shipping_settings: %{
            price: 0,
            ecommerce: "shopify",
            organization: org,
            settings: nil
          },
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "delivery",
          fulfillment: fulfillment,
          shipping_settings: %{
            "deliveryMethodType" => "SHIPPING",
            "estimatedCost" => %{
              "amount" => "100.0"
            },
            "handle" => "3",
            "title" => "Carro"
          }
        )

      assert {:error, "Shipping Settings not found"} =
               CreateDraftOrder.run(org.id, fulfillment.id, customer.id)
    end

    test "process/1 does not create a draft order when shipping address is nil", %{
      org: org,
      location: loc
    } do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      shipping_address = nil
      customer = insert(:customer, organization: org)

      order =
        insert(:order, organization: org, customer: customer, shipping_address: shipping_address)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          shipping_settings: %{
            price: 0,
            ecommerce: "shopify",
            organization: org,
            settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "3",
              "title" => "Carro"
            }
          },
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "delivery",
          fulfillment: fulfillment,
          shipping_settings: %{
            "deliveryMethodType" => "SHIPPING",
            "estimatedCost" => %{
              "amount" => "100.0"
            },
            "handle" => "3",
            "title" => "Carro"
          }
        )

      assert {:error, "Shipping Address not found"} =
               CreateDraftOrder.run(org.id, fulfillment.id, customer.id)
    end
  end

  test "process/1 handles invalid email domain by temporarily nullifying it", %{
    org: org,
    location: loc
  } do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error,
       {:unknown_error_code,
        %{"field" => ["email"], "message" => "Email contains an invalid domain name"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customer, 1, fn _, _ ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _, %{email: ""} ->
      {:ok, %{"email" => "", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _,
                                                                      %{
                                                                        email:
                                                                          "<EMAIL>"
                                                                      } ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        quantity: 1
      )

    assert {:ok, _} =
             CreateDraftOrder.run(org.id, fulfillment.id, customer.id)

    updated_fulfillment = Rms.Repo.get!(Fulfillment, fulfillment.id)
    assert updated_fulfillment.external_reference == "gid://shopify/DraftOrder/123"
  end

  test "process/1 restores customer email even when draft order creation fails", %{
    org: org,
    location: loc
  } do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error,
       {:unknown_error_code,
        %{"field" => ["email"], "message" => "Email contains an invalid domain name"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customer, 1, fn _, _ ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _, %{email: ""} ->
      {:ok, %{"email" => "", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error, {:unknown_error_code, %{"message" => "Some other error"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _,
                                                                      %{
                                                                        email:
                                                                          "<EMAIL>"
                                                                      } ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        quantity: 1
      )

    assert {:error, _} =
             CreateDraftOrder.run(org.id, fulfillment.id, customer.id)

    updated_fulfillment = Rms.Repo.get!(Fulfillment, fulfillment.id)
    assert updated_fulfillment.external_reference == nil
  end

  describe "simulate_and_check_price/1" do
    test "simulate_and_check_price validate is same price", %{org: org, location: loc} do
      Mox.expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :simulate_cart, 1, fn _, _ ->
        {:ok,
         """
         \r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 361\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\",\"createdAt\":\"2024-07-18T18:49:56Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\"},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 780\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Bicicleta\",\"handle\":\"********************************\",\"estimatedCost\":{\"amount\":\"25.0\"}},{\"title\":\"Moto\",\"handle\":\"f20de5611356f6f568bdb9387afa955d\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Carro\",\"handle\":\"fd2120274e392769c872754fb2eac8f2\",\"estimatedCost\":{\"amount\":\"100.0\"}},{\"title\":\"Caminhão\",\"handle\":\"81d69ad7e95d44f50319e91517cd16fb\",\"estimatedCost\":{\"amount\":\"100.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"3.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--
         """}
      end)

      order =
        insert(:order,
          organization: org,
          location: loc,
          total_items_selling_price: "10.00"
        )
        |> Rms.Repo.preload(:discounts)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      line_item =
        insert(:line_item,
          price: "100.0",
          list_price: "100.0",
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )
        |> Rms.Repo.preload(:discounts)

      fulfillment = %{fulfillment | line_items: [line_item]}

      {result, _} = CreateDraftOrder.simulate_and_check_price(fulfillment)
      assert result
    end

    test "simulate_and_check_price validate is different price", %{org: org, location: loc} do
      Mox.expect(Rms.Integrations.Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :simulate_cart, 1, fn _, _ ->
        {:ok,
         """
         \r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 361\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\",\"createdAt\":\"2024-07-18T18:49:56Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\"},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 780\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Bicicleta\",\"handle\":\"********************************\",\"estimatedCost\":{\"amount\":\"25.0\"}},{\"title\":\"Moto\",\"handle\":\"f20de5611356f6f568bdb9387afa955d\",\"estimatedCost\":{\"amount\":\"10.0\"}},{\"title\":\"Carro\",\"handle\":\"fd2120274e392769c872754fb2eac8f2\",\"estimatedCost\":{\"amount\":\"100.0\"}},{\"title\":\"Caminhão\",\"handle\":\"81d69ad7e95d44f50319e91517cd16fb\",\"estimatedCost\":{\"amount\":\"100.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"3.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--
         """}
      end)

      order =
        insert(:order,
          organization: org,
          location: loc,
          total_items_selling_price: "100.00"
        )
        |> Rms.Repo.preload(:discounts)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      line_item =
        insert(:line_item,
          price: "100.0",
          list_price: "100.0",
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )
        |> Rms.Repo.preload(:discounts)

      fulfillment = %{fulfillment | line_items: [line_item]}

      {result, _} = CreateDraftOrder.simulate_and_check_price(fulfillment)
      assert !result
    end
  end
end
