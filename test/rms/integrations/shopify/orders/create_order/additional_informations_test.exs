defmodule Rms.Integrations.Shopify.Orders.CreateOrder.AdditionalInformationsTest do
  use Rms.DataCase

  alias Rms.Integrations.Shopify.Orders.AdditionalInformations.DiscountInfo,
    as: AdditionalInformations

  @preload [
    order: [:discounts],
    line_items: [:discounts]
  ]

  setup do
    org = insert(:organization)
    location = insert(:location, organization: org)

    product = insert(:product, organization: org)
    product_variant = insert(:product_variant, organization: org, product: product, sku: "123")

    insert(:product_variant_mapping,
      external_id: "external_id_variant",
      source: "shopify",
      product_variant: product_variant,
      organization: org
    )

    order = insert(:order, %{organization: org, location: location})

    line_item =
      insert(:line_item, %{
        organization: org,
        location: location,
        price: Decimal.new("25.50"),
        list_price: Decimal.new("51.0"),
        manual_discount: Decimal.new("0.00"),
        quantity: 1,
        product_variant: product_variant
      })

    f1 =
      :fulfillment
      |> insert(%{
        shipping_method: "in-store",
        organization: org,
        order: order,
        line_items: [
          line_item
        ]
      })

    insert(:line_item_discount, %{
      line_item: List.first(f1.line_items),
      organization: org,
      value: "10.0",
      type: "percentage",
      description: "Descontinho no itenzinho"
    })

    order2 =
      insert(:order, %{
        organization: org,
        location: location,
        discounts: [
          insert(:discount, %{
            organization: org,
            value: "10.0",
            type: "percentage",
            description: "Cliente bacana"
          })
        ]
      })

    f2 =
      :fulfillment
      |> insert(%{
        shipping_method: "delivery",
        organization: org,
        order: order2,
        line_items: [line_item]
      })

    order3 =
      insert(:order, %{
        organization: org,
        location: location
      })

    f3 =
      :fulfillment
      |> insert(%{
        shipping_method: "delivery",
        organization: org,
        order: order3,
        line_items: [
          insert(:line_item, %{
            organization: org,
            location: location,
            price: Decimal.new("30.50"),
            list_price: Decimal.new("51.0"),
            manual_discount: Decimal.new("0.00"),
            quantity: 1,
            product_variant: product_variant
          })
        ]
      })

    {:ok,
     %{
       org: org,
       order: order,
       f1: f1,
       f2: f2,
       f3: f3
     }}
  end

  test "it should create additional information to line_items discounts", %{f1: f1} do
    assert [
             %{
               key: "line_items_discount_info",
               value:
                 "[{\"sku\":\"123\",\"quantity\":1,\"discounts\":[{\"type\":\"percentage\",\"value\":\"10.0\",\"description\":\"Descontinho no itenzinho\"}]}]"
             },
             %{value: "[]", key: "order_discounts_info"}
           ] =
             f1
             |> Rms.Repo.preload(@preload)
             |> AdditionalInformations.execute()
  end

  test "it should create additional information to order discounts", %{f2: f2} do
    assert [
             %{
               value:
                 "[{\"sku\":\"123\",\"quantity\":1,\"discounts\":[{\"type\":\"percentage\",\"value\":\"10.0\",\"description\":\"Descontinho no itenzinho\"}]}]",
               key: "line_items_discount_info"
             },
             %{
               key: "order_discounts_info",
               value:
                 "[{\"type\":\"percentage\",\"value\":\"10.0\",\"description\":\"Cliente bacana\"}]"
             }
           ] =
             AdditionalInformations.execute(Rms.Repo.preload(f2, @preload))
  end

  test "it should not send when discounts is empty", %{f3: f3} do
    assert [
             %{value: "[]", key: "line_items_discount_info"},
             %{value: "[]", key: "order_discounts_info"}
           ] = AdditionalInformations.execute(Rms.Repo.preload(f3, @preload))
  end
end
