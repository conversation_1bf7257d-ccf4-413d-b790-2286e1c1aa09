defmodule Rms.Integrations.Shopify.Orders.CreateOrder.BuildParamsTest do
  use Rms.DataCase

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify.Orders.CreateOrder.BuildParams

  @preload [
    :shipping_settings,
    order: [
      :discounts,
      [transaction: :payments],
      customer: [:addresses],
      location: [:location_mappings]
    ],
    line_items: [:discounts, product_variant: [:product_variant_mappings]]
  ]

  setup do
    org = insert(:organization)
    location = insert(:location, organization: org)
    insert(:location_mapping, %{location: location, organization: org})

    shopify_credential = insert(:shopify_credential, organization: org)
    product = insert(:product, organization: org)
    product_variant = insert(:product_variant, organization: org, product: product)

    insert(:product_variant_mapping,
      external_id: "external_id_variant",
      source: "shopify",
      product_variant: product_variant,
      organization: org
    )

    order = insert(:order, %{organization: org, location: location})
    transaction = insert(:transaction, order: order, organization: org)

    {:ok,
     %{
       org: org,
       location: location,
       shopify_credential: shopify_credential,
       product_variant: product_variant,
       order: order,
       transaction: transaction
     }}
  end

  test "build to delivery shipping_method", %{
    org: org,
    location: location,
    product_variant: product_variant,
    order: order,
    transaction: transaction
  } do
    expect(Rms.Clients.FeatureFlag.Mock, :should_return_mocked_delivery_options?, fn _ ->
      false
    end)

    shipping_address =
      %Rms.Commerce.Orders.Order.ShippingAddress{
        receiver_name: "Victória De Simone",
        city_name: "São Paulo",
        state: "SP",
        country_name: "Brazil",
        neighborhood: nil,
        street: "Avenida Dom Pedro I, 920 - Vila Monumento, ⁠920",
        street_type: nil,
        number: nil,
        zip: "01552-000",
        complement: "Ap 930, ⁠Vila monumento"
      }

    order = put_key!(order, :shipping_address, shipping_address)

    fulfillment =
      :fulfillment
      |> insert(%{
        shipping_method: "delivery",
        organization: org,
        order: order,
        shipping_settings:
          insert(:shipping_setting, %{
            organization: org,
            price: Decimal.new("0"),
            ecommerce: "shopify",
            settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{"amount" => "19.67"},
              "handle" => "********************************",
              "title" => "Loggi"
            }
          }),
        line_items: [
          insert(:line_item, %{
            organization: org,
            location: location,
            price: Decimal.new("25.50"),
            list_price: Decimal.new("51.0"),
            manual_discount: Decimal.new("0.00"),
            quantity: 1,
            product_variant: product_variant
          })
        ]
      })
      |> Rms.Repo.preload(@preload)

    line_item = List.first(fulfillment.line_items)
    payment = insert(:payment, %{transaction: transaction, organization: org, amount: 25.5})

    assert {:ok,
            %{
              lineItems: [result_line_item],
              transactions: [result_transaction],
              shippingAddress: _shipping_address,
              customAttributes: _custom_attributes,
              shippingLines: shipping_lines
            }} = BuildParams.execute(fulfillment)

    assert get_in(result_line_item, [:priceSet, :shopMoney, :amount]) ==
             Decimal.to_float(line_item.price)

    assert get_in(result_line_item, [:quantity]) == line_item.quantity

    product_variant_mapping = List.first(line_item.product_variant.product_variant_mappings)
    assert get_in(result_line_item, [:variantId]) == product_variant_mapping.external_id

    assert get_in(result_transaction, [:amountSet, :shopMoney, :amount]) ==
             Decimal.to_float(payment.amount)

    assert [
             %{
               title: "Loggi",
               priceSet: %{shopMoney: %{amount: 19.67, currencyCode: "BRL"}}
             }
           ] = shipping_lines
  end

  test "build to in-store shipping_method", %{
    org: org,
    location: location,
    product_variant: product_variant,
    order: order,
    transaction: transaction
  } do
    fulfillment =
      :fulfillment
      |> insert(%{
        shipping_method: "in-store",
        organization: org,
        order: order,
        line_items: [
          insert(:line_item, %{
            organization: org,
            location: location,
            price: Decimal.new("25.50"),
            list_price: Decimal.new("51.0"),
            manual_discount: Decimal.new("0.00"),
            quantity: 1,
            product_variant: product_variant
          })
        ]
      })
      |> Rms.Repo.preload(@preload)

    payment = insert(:payment, %{transaction: transaction, organization: org, amount: 25.5})

    line_item = List.first(fulfillment.line_items)
    location_mapping = List.first(fulfillment.order.location.location_mappings)

    assert {:ok,
            %{
              lineItems: [result_line_item],
              transactions: [result_transaction],
              fulfillment: result_fulfillment,
              customAttributes: _custom_attributes
            }} = BuildParams.execute(fulfillment)

    assert get_in(result_line_item, [:priceSet, :shopMoney, :amount]) ==
             Decimal.to_float(line_item.price)

    assert get_in(result_line_item, [:quantity]) == line_item.quantity

    product_variant_mapping = List.first(line_item.product_variant.product_variant_mappings)
    assert get_in(result_line_item, [:variantId]) == product_variant_mapping.external_id

    assert get_in(result_transaction, [:amountSet, :shopMoney, :amount]) ==
             Decimal.to_float(payment.amount)

    assert result_fulfillment.locationId == location_mapping.external_id
  end

  test "should send line_items_discount_info", %{
    product_variant: product_variant,
    org: org,
    location: location,
    order: order
  } do
    fulfillment =
      :fulfillment
      |> insert(%{
        shipping_method: "in-store",
        organization: org,
        order: order,
        line_items: [
          insert(:line_item, %{
            organization: org,
            location: location,
            price: Decimal.new("25.50"),
            list_price: Decimal.new("51.0"),
            manual_discount: Decimal.new("0.00"),
            quantity: 1,
            product_variant: product_variant
          })
        ]
      })

    insert(:line_item_discount, %{
      line_item: List.first(fulfillment.line_items),
      organization: org,
      value: "10.0",
      type: "percentage",
      description: "Cliente bacana"
    })

    fulfillment = Rms.Repo.preload(fulfillment, @preload)

    assert {:ok, %{customAttributes: custom_attributes}} = BuildParams.execute(fulfillment)

    line_item_discount_info =
      Enum.find(custom_attributes, &(&1.key == "line_items_discount_info"))

    decoded_line = line_item_discount_info.value |> Jason.decode!() |> List.first()
    assert product_variant.sku == decoded_line["sku"]

    assert [%{"description" => "Cliente bacana", "type" => "percentage", "value" => "10.0"}] =
             decoded_line["discounts"]
  end

  defp put_key!(entity, key, value) do
    Ecto.Changeset.change(entity, %{key => value}) |> Rms.Repo.update!()
  end
end
