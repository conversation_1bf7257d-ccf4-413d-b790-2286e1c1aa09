defmodule Rms.Integrations.Shopify.Orders.CreateOrder.ShippingAddressTest do
  use Rms.DataCase

  alias Rms.Integrations.Shopify.Orders.CreateOrder.ShippingAddress

  setup do
    org = insert(:organization)

    shipping_address = %{
      receiver_name: "<PERSON>",
      city_name: "São Paulo",
      state: "<PERSON>",
      country_name: "BRA",
      neighborhood: "Jardim Paulista",
      street: "Av. Paulista",
      street_type: "Avenue",
      number: "1000",
      zip: "01310100",
      complement: "Apt 1001"
    }

    customer = insert(:customer, primary_phone_number: "11999999999")

    order =
      insert(:order,
        organization: org,
        shipping_address: shipping_address,
        customer: customer
      )

    {:ok, %{org: org, order: order, shipping_address: shipping_address, customer: customer}}
  end

  describe "execute/1" do
    test "returns error when no parameters are provided" do
      assert {:error, "Shipping Address not found"} =
               ShippingAddress.execute(%{order: %{shipping_address: nil}})
    end

    test "returns error when order is not found" do
      assert {:error, "Shipping Address not found"} =
               ShippingAddress.execute(%{order: %{shipping_address: nil}})
    end

    test "returns shipping address when order is valid", %{order: order} do
      assert {:ok, address} = ShippingAddress.execute(%{order: order})

      assert address.firstName == "João"
      assert address.lastName == "Silva"
      assert address.address1 == "Av. Paulista 1000, Jardim Paulista"
      assert address.city == "São Paulo"
      assert address.provinceCode == "SP"
      assert address.countryCode == "BR"
      assert address.zip == "01310100"
      assert address.phone == "11999999999"
    end

    test "returns shipping address with single name", %{org: org} do
      shipping_address = %{
        receiver_name: "João",
        city_name: "Rio de Janeiro",
        state: "RJ",
        country_name: "BRA",
        neighborhood: "Copacabana",
        street: "Av. Atlântica",
        number: "500",
        zip: "20000000"
      }

      customer = insert(:customer, primary_phone_number: "21999999999")

      order =
        insert(:order,
          organization: org,
          shipping_address: shipping_address,
          customer: customer
        )

      assert {:ok, address} = ShippingAddress.execute(%{order: order})
      assert address.firstName == "João"
      assert address.lastName == "(sobrenome não informado)"
      assert address.address1 == "Av. Atlântica 500, Copacabana"
      assert address.city == "Rio de Janeiro"
      assert address.provinceCode == "RJ"
      assert address.countryCode == "BR"
      assert address.zip == "20000000"
      assert address.phone == "21999999999"
    end
  end
end
