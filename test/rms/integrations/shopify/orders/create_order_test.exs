defmodule Rms.Integrations.Shopify.Orders.CreateOrderTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify
  alias Rms.Integrations.Shopify.Orders.CreateOrder

  alias Rms.Commerce.Fulfillments.Fulfillment

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    location = insert(:location, organization: org)
    insert(:location_mapping, organization: org, location: location)

    product = insert(:product, organization: org)
    product_variant = insert(:product_variant, organization: org, product: product)

    product_variant_mapping =
      insert(:product_variant_mapping,
        external_id: "external_id_variant",
        source: "shopify",
        product_variant: product_variant,
        organization: org
      )

    order = insert(:order, %{organization: org, location: location})
    transaction = insert(:transaction, order: order, organization: org)
    payment = insert(:payment, %{transaction: transaction, organization: org})

    insert(:shopify_storefront_credential, organization: org)
    insert(:shopify_credential, organization: org)

    {:ok,
     %{
       org: org,
       location: location,
       order: order,
       product_variant: product_variant,
       product_variant_mapping: product_variant_mapping,
       transaction: transaction,
       payment: payment
     }}
  end

  describe "when shipping method is in-store" do
    test "create a order", %{
      org: org,
      location: location,
      order: order,
      product_variant: product_variant
    } do
      fulfillment =
        insert(:fulfillment, %{
          shipping_method: "in-store",
          organization: org,
          order: order,
          line_items: [
            insert(:line_item, %{
              organization: org,
              location: location,
              price: Decimal.new("50.50"),
              list_price: Decimal.new("51.0"),
              manual_discount: Decimal.new("0.00"),
              quantity: 1,
              product_variant: product_variant
            })
          ]
        })

      expect(Shopify.Mock, :client, fn _, _ -> :mock_client end)

      shopify_order_id = "gid://shopify/Order/#{:rand.uniform(100_000)}"

      expect(Shopify.Mock, :create_order, fn _client, arguments ->
        assert %{lineItems: _line_items, transactions: _transactions, fulfillment: _fulfillment} =
                 arguments

        {:ok, %{"id" => shopify_order_id, "name" => "Order #{shopify_order_id}}}"}}
      end)

      {:ok, %Fulfillment{external_reference: ^shopify_order_id}} =
        CreateOrder.run(org.id, fulfillment.id)
    end
  end

  describe "when order is to delivery" do
    test "create order without fulfillment", %{
      org: org,
      location: location,
      order: order,
      product_variant: product_variant
    } do
      expect(Rms.Clients.FeatureFlag.Mock, :should_return_mocked_delivery_options?, fn _ ->
        false
      end)

      shipping_address =
        %Rms.Commerce.Orders.Order.ShippingAddress{
          receiver_name: "Victória De Simone",
          city_name: "São Paulo",
          state: "SP",
          country_name: "Brazil",
          neighborhood: nil,
          street: "Avenida Dom Pedro I, 920 - Vila Monumento, ⁠920",
          street_type: nil,
          number: nil,
          zip: "01552-000",
          complement: "Ap 930, Vila monumento"
        }

      put_key!(order, :shipping_address, shipping_address)

      fulfillment =
        insert(:fulfillment, %{
          shipping_method: "delivery",
          organization: org,
          order: order,
          shipping_settings:
            insert(:shipping_setting, %{
              organization: org,
              price: Decimal.new("0"),
              ecommerce: "shopify",
              settings: %{
                "deliveryMethodType" => "SHIPPING",
                "estimatedCost" => %{"amount" => "19.67"},
                "handle" => "********************************",
                "title" => "Loggi"
              }
            }),
          line_items: [
            insert(:line_item, %{
              organization: org,
              location: location,
              price: Decimal.new("50.50"),
              list_price: Decimal.new("51.0"),
              manual_discount: Decimal.new("0.00"),
              quantity: 1,
              product_variant: product_variant
            })
          ]
        })

      expect(Shopify.Mock, :client, fn _, _ -> :mock_client end)

      shopify_order_id = "gid://shopify/Order/#{:rand.uniform(100_000)}"

      expect(Shopify.Mock, :create_order, fn _client, arguments ->
        assert %{
                 lineItems: _line_items,
                 transactions: _transactions,
                 shippingAddress: _shipping_address
               } =
                 arguments

        refute Map.has_key?(arguments, :fulfillment)

        {:ok, %{"id" => shopify_order_id, "name" => "Order #{shopify_order_id}"}}
      end)

      {:ok, %Fulfillment{external_reference: ^shopify_order_id}} =
        CreateOrder.run(org.id, fulfillment.id)
    end
  end

  describe "when shipping method is only in-store" do
    test "create a order with draft order" do
    end

    test "create order with fulfillment" do
    end

    test "create order at in-store location" do
    end
  end

  describe "when order has discount" do
    test "apply discount at line item priceSet" do
    end
  end

  defp put_key!(entity, key, value) do
    Ecto.Changeset.change(entity, %{key => value}) |> Rms.Repo.update!()
  end
end
