defmodule Rms.Integration.Shopify.Orders.DraftOrder.AppliedDiscountTest do
  use Rms.DataCase
  import Rms.Factory

  import Mox

  setup :verify_on_exit!

  alias Rms.Integrations.Shopify.Orders.DraftOrder.AppliedDiscount
  alias Rms.Integrations.Shopify

  @preload [
    :shipping_settings,
    order: [:discounts, customer: [:addresses], location: [:address]],
    line_items: [:discounts, product_variant: [:product_variant_mappings]]
  ]

  setup do
    org = insert(:organization)
    location = insert(:location, organization: org)
    shopify_credential = insert(:shopify_credential, organization: org)
    {:ok, %{org: org, location: location, shopify_credential: shopify_credential}}
  end

  describe "execute/3" do
    test "has 50% off automatic discount", %{org: org, location: location} do
      line_item =
        insert(:line_item, %{
          organization: org,
          location: location,
          price: Decimal.new("25.50"),
          list_price: Decimal.new("51.0"),
          manual_discount: Decimal.new("0.00"),
          quantity: 1
        })

      order =
        insert(:order, %{
          total_price: Decimal.new("25.50"),
          total_price_with_addons: Decimal.new("25.50"),
          total_discount_with_addons: Decimal.new("25.50"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("25.50"),
          total_items_list_price: Decimal.new("51.00"),
          total_discount: Decimal.new("25.50"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "automatic_ecommerce",
        value: "LENTES-50OFF"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: [line_item])
        |> Rms.Repo.preload(@preload)

      assert %{value: +0.0} = AppliedDiscount.execute(fulfillment, nil, Decimal.new("25.50"))
    end

    test "has gifts (automatic discount)", %{org: org, location: location} do
      list_items_spec = [
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00"),
          quantity: 1
        },
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00"),
          quantity: 1
        },
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00"),
          quantity: 1
        },
        %{
          list_price: Decimal.new("540.0"),
          price: Decimal.new("539.70"),
          is_gift: false,
          manual_discount: Decimal.new("0.00"),
          quantity: 1
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      order =
        insert(:order, %{
          total_price: Decimal.new("540.00"),
          total_price_with_addons: Decimal.new("540.00"),
          total_discount_with_addons: Decimal.new("3.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("540.00"),
          total_items_list_price: Decimal.new("543.00"),
          total_discount: Decimal.new("3.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      assert %{value: 3.0} = AppliedDiscount.execute(fulfillment, nil, Decimal.new("3.00"))
    end

    test "has fixed amount coupon discount", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"amount" => %{"amount" => 20.0}}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.09"),
          is_gift: true,
          manual_discount: Decimal.new("0.01")
        },
        %{
          list_price: Decimal.new("150.0"),
          price: Decimal.new("129.91"),
          is_gift: false,
          manual_discount: Decimal.new("19.99")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      order =
        insert(:order, %{
          total_price: Decimal.new("130.00"),
          total_price_with_addons: Decimal.new("130.00"),
          total_discount_with_addons: Decimal.new("21.00"),
          total_ecommerce_discounts: Decimal.new("20.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("150.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("21.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "CARLOSTESTE"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 1.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has percentage coupon discount", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"percentage" => 0.2}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          discounts: [],
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          discounts: [],
          list_price: Decimal.new("150.0"),
          price: Decimal.new("119.90"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      order =
        insert(:order, %{
          total_price: Decimal.new("120.00"),
          total_price_with_addons: Decimal.new("120.00"),
          total_discount_with_addons: Decimal.new("31.00"),
          total_ecommerce_discounts: Decimal.new("30.2"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("120.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("31.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "PORC-MANU"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 0.80} = AppliedDiscount.execute(fulfillment, client, Decimal.new("31.00"))
    end

    test "has percentage line item discount and percentage coupon discount to order", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"percentage" => 0.2}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          discounts: [],
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          discounts: [],
          list_price: Decimal.new("150.0"),
          price: Decimal.new("107.91"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "10",
        description: "Testines",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("108.01"),
          total_price_with_addons: Decimal.new("108.01"),
          total_discount_with_addons: Decimal.new("42.99"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("11.99"),
          total_items_selling_price: Decimal.new("108.01"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("42.99"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "PORC-MANU"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 12.79} = AppliedDiscount.execute(fulfillment, client, Decimal.new("31.00"))
    end

    test "has fixed line item discount and percentage coupon discount to order", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"percentage" => 0.2}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          discounts: [],
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          list_price: Decimal.new("150.0"),
          price: Decimal.new("99.90"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "fixed",
        value: "20.00",
        description: "eu quero",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("100.00"),
          total_price_with_addons: Decimal.new("100.00"),
          total_discount_with_addons: Decimal.new("51.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("20.00"),
          total_items_selling_price: Decimal.new("100.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("51.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "PORC-MANU"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 20.80} = AppliedDiscount.execute(fulfillment, client, Decimal.new("31.00"))
    end

    test "has fixed coupon discount", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"amount" => %{"amount" => 20.0}}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          discounts: [],
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.09"),
          is_gift: true,
          manual_discount: Decimal.new("0.01")
        },
        %{
          list_price: Decimal.new("150.0"),
          price: Decimal.new("109.91"),
          is_gift: false,
          manual_discount: Decimal.new("19.99")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "fixed",
        value: "20.00",
        description: "eu quero 2",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("110.00"),
          total_price_with_addons: Decimal.new("110.00"),
          total_discount_with_addons: Decimal.new("41.00"),
          total_ecommerce_discounts: Decimal.new("20.00"),
          total_items_manual_discount: Decimal.new("20.00"),
          total_items_selling_price: Decimal.new("130.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("41.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "CARLOSTESTE"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 21.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has percentage line item discount and percentage coupon discount", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"percentage" => 0.20}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          list_price: Decimal.new("150.0"),
          price: Decimal.new("107.91"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "10",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("108.01"),
          total_price_with_addons: Decimal.new("108.01"),
          total_discount_with_addons: Decimal.new("42.99"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("11.99"),
          total_items_selling_price: Decimal.new("108.01"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("42.99"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "PORC-MANU"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 12.79} = AppliedDiscount.execute(fulfillment, client, Decimal.new("31.00"))
    end

    test "percentage discount on order and line item - more thant one item", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      expect(Rms.Integrations.Shopify.Mock, :get_discount_code, fn _client, _params ->
        {:ok,
         %{
           "codeDiscountNodeByCode" => %{
             "codeDiscount" => %{
               "customerGets" => %{"value" => %{"percentage" => 0.20}}
             }
           }
         }}
      end)

      list_items_spec = [
        %{
          list_price: Decimal.new("1.0"),
          price: Decimal.new("0.10"),
          is_gift: true,
          manual_discount: Decimal.new("0.00"),
          quantity: 3
        },
        %{
          list_price: Decimal.new("150.0"),
          price: Decimal.new("107.91"),
          is_gift: false,
          manual_discount: Decimal.new("0.00"),
          quantity: 3
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "10",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("216.02"),
          total_price_with_addons: Decimal.new("216.02"),
          total_discount_with_addons: Decimal.new("85.98"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("23.98"),
          total_items_selling_price: Decimal.new("216.02"),
          total_items_list_price: Decimal.new("302.00"),
          total_discount: Decimal.new("85.98"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "coupon",
        value: "PORC-MANU"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 57.38} = AppliedDiscount.execute(fulfillment, client, Decimal.new("31.00"))
    end

    test "has fixed discount on line item", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("0.10"),
          list_price: Decimal.new("1.0"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          price: Decimal.new("139.90"),
          list_price: Decimal.new("150.0"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "fixed",
        value: "10",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("140.00"),
          total_price_with_addons: Decimal.new("140.00"),
          total_discount_with_addons: Decimal.new("11.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("10.00"),
          total_items_selling_price: Decimal.new("140.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("11.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 11.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has fixed discount on order", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("0.10"),
          list_price: Decimal.new("1.0"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          price: Decimal.new("139.90"),
          list_price: Decimal.new("150.0"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      order =
        insert(:order, %{
          total_price: Decimal.new("140.00"),
          total_price_with_addons: Decimal.new("140.00"),
          total_discount_with_addons: Decimal.new("11.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("10.00"),
          total_items_selling_price: Decimal.new("140.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("11.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "fixed",
        value: "10"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 11.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has percentage discount on order", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          discounts: [],
          price: Decimal.new("0.09"),
          list_price: Decimal.new("1.0"),
          is_gift: true,
          manual_discount: Decimal.new("0.01")
        },
        %{
          discounts: [],
          price: Decimal.new("134.91"),
          list_price: Decimal.new("150.0"),
          is_gift: false,
          manual_discount: Decimal.new("14.99")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      order =
        insert(:order, %{
          total_price: Decimal.new("135.00"),
          total_price_with_addons: Decimal.new("135.00"),
          total_discount_with_addons: Decimal.new("16.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("135.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("16.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        order: order,
        type: "percentage",
        value: "10"
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 16.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has percentage discount on line item", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("0.10"),
          list_price: Decimal.new("1.0"),
          is_gift: true,
          manual_discount: Decimal.new("0.00")
        },
        %{
          price: Decimal.new("139.90"),
          list_price: Decimal.new("150.0"),
          is_gift: false,
          manual_discount: Decimal.new("0.00")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "10",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("140.00"),
          total_price_with_addons: Decimal.new("140.00"),
          total_discount_with_addons: Decimal.new("11.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("10.00"),
          total_items_selling_price: Decimal.new("140.00"),
          total_items_list_price: Decimal.new("151.00"),
          total_discount: Decimal.new("11.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 11.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("1.00"))
    end

    test "has coupon percentage discount (50%-OFF)", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("196.00"),
          list_price: Decimal.new("245.0"),
          manual_discount: Decimal.new("49.00"),
          is_gift: false
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "20",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("196.00"),
          total_price_with_addons: Decimal.new("196.00"),
          total_discount_with_addons: Decimal.new("49.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("196.00"),
          total_items_list_price: Decimal.new("245.00"),
          total_discount: Decimal.new("49.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 49.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("0.00"))
    end

    test "has percentage discount on order without gifts", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("196.00"),
          list_price: Decimal.new("245.0"),
          manual_discount: Decimal.new("49.00"),
          is_gift: false
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "20",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("196.00"),
          total_price_with_addons: Decimal.new("196.00"),
          total_discount_with_addons: Decimal.new("49.00"),
          total_ecommerce_discounts: Decimal.new("0.00"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("196.00"),
          total_items_list_price: Decimal.new("245.00"),
          total_discount: Decimal.new("49.00"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: 49.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("0.00"))
    end

    test "PRD example - has percentage discount on order without gifts", %{
      org: org,
      location: location,
      shopify_credential: shopify_credential
    } do
      expect(Rms.Integrations.Shopify.Mock, :client, fn _shop, _cred -> :mock_client end)

      list_items_spec = [
        %{
          price: Decimal.new("122.50"),
          list_price: Decimal.new("245.0"),
          is_gift: false,
          manual_discount: Decimal.new("122.50")
        }
      ]

      line_items =
        Enum.map(
          list_items_spec,
          &insert(:line_item, Map.merge(&1, %{organization: org, location: location}))
        )

      insert(:line_item_discount, %{
        organization: org,
        type: "percentage",
        value: "20",
        description: "eu quero, eu posso",
        line_item: Enum.find(line_items, &(&1.is_gift == false))
      })

      order =
        insert(:order, %{
          total_price: Decimal.new("122.50"),
          total_price_with_addons: Decimal.new("122.50"),
          total_discount_with_addons: Decimal.new("122.50"),
          total_ecommerce_discounts: Decimal.new("122.50"),
          total_items_manual_discount: Decimal.new("0.00"),
          total_items_selling_price: Decimal.new("245.00"),
          total_items_list_price: Decimal.new("245.00"),
          total_discount: Decimal.new("122.50"),
          total_delivery_price: Decimal.new("0.00"),
          organization: org
        })

      insert(:discount, %{
        organization: org,
        type: "coupon",
        value: "DESCONTO50",
        order: order
      })

      fulfillment =
        insert(:fulfillment, organization: org, order: order, line_items: line_items)
        |> Rms.Repo.preload(@preload)

      client = Shopify.client(shopify_credential.shop, shopify_credential.credential)
      assert %{value: +0.0} = AppliedDiscount.execute(fulfillment, client, Decimal.new("122.50"))
    end
  end
end
