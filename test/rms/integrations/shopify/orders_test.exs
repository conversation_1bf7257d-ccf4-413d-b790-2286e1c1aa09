defmodule Rms.Integrations.Shopify.OrdersTest do
  use Rms.DataCase
  use Rms.EventsCase
  import Mox

  # Make sure mocks are verified after each test
  setup :verify_on_exit!

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock
  alias Rms.Integrations.Shopify.Orders
  alias Rms.Commerce.Orders.Order

  # Common test data for all tests
  @ecommerce_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Online Store"},
        "createdAt" => "2024-12-05T18:44:51Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "10.0"}},
        "customer" => %{
          "email" => "<EMAIL>",
          "firstName" => "Julia",
          "id" => "gid://shopify/Customer/7525440487734",
          "lastName" => "Rezende",
          "phone" => "+5511971748987"
        },
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "PICK_UP"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "10.0"}
                      }
                    },
                    "productTitle" => "Sandálias Amazônia (MAIS VELHO)",
                    "sku" => "108",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162076115254"
                    },
                    "variantTitle" => "34/35"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/6275942777142",
        "name" => "#2700",
        "retailLocation" => nil,
        "shippingAddress" => %{
          "address1" => "Rua dos Pinheiros, 423",
          "address2" => "812",
          "city" => "São Paulo",
          "country" => "Brazil",
          "firstName" => "Julia",
          "lastName" => "Rezende",
          "phone" => "",
          "provinceCode" => "SP",
          "zip" => "05422-010"
        },
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "10.0"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2025-03-27T03:19:58Z"
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 17,
        "requestedQueryCost" => 467,
        "throttleStatus" => %{
          "currentlyAvailable" => 1983,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @pos_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Point of Sale"},
        "createdAt" => "2024-11-28T17:30:55Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "17.0"}},
        "customer" => nil,
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/77699186998"}
              },
              "deliveryMethod" => %{"methodType" => "RETAIL"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 2,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "8.5"}
                      }
                    },
                    "productTitle" => "Sandálias Amazônia",
                    "sku" => "108",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162076115254"
                    },
                    "variantTitle" => "34/35"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/6258798362934",
        "name" => "#2691",
        "retailLocation" => %{"id" => "gid://shopify/Location/77699186998"},
        "shippingAddress" => nil,
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "17.0"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "3.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2024-11-28T17:31:41Z"
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 17,
        "requestedQueryCost" => 467,
        "throttleStatus" => %{
          "currentlyAvailable" => 1983,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @discount_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Point of Sale"},
        "createdAt" => "2023-08-31T11:04:12Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "8.7"}},
        "customer" => nil,
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "RETAIL"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 3,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "0.75"}
                      }
                    },
                    "productTitle" => "Sandálias Amazônia",
                    "sku" => "108",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162076115254"
                    },
                    "variantTitle" => "34/35"
                  }
                ]
              },
              "status" => "CLOSED"
            },
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "RETAIL"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/bag-site-1.jpg?v=1671548122"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "6.45"}
                      }
                    },
                    "productTitle" => "Ecobag para Presente",
                    "sku" => "197",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162059534646"
                    },
                    "variantTitle" => nil
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/5537148797238",
        "name" => "#1670",
        "retailLocation" => %{"id" => "gid://shopify/Location/74913710390"},
        "shippingAddress" => nil,
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "9.45"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "1.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2023-08-31T11:05:47Z"
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 17,
        "requestedQueryCost" => 467,
        "throttleStatus" => %{
          "currentlyAvailable" => 1983,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  # Response specifically for testing missing variant mapping
  @missing_variant_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Point of Sale"},
        "createdAt" => "2023-08-31T11:04:12Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "8.7"}},
        "customer" => nil,
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "RETAIL"},
              "lineItems" => %{
                "nodes" => [
                  # This variant exists in setup
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 3,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "0.75"}
                      }
                    },
                    "productTitle" => "Sandálias Amazônia",
                    "sku" => "108",
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162076115254"
                    },
                    "variantTitle" => "34/35"
                  },
                  # This variant does NOT exist in setup
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/missing-product.jpg?v=1671548122"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "6.45"}
                      }
                    },
                    "productTitle" => "Missing Product",
                    "sku" => "MISSING",
                    "variant" => %{
                      # This ID won't have a mapping
                      "id" => "gid://shopify/ProductVariant/MISSING-VARIANT-ID"
                    },
                    "variantTitle" => "One Size"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        "id" => "gid://shopify/Order/5537148797238",
        "name" => "#1670",
        "retailLocation" => %{"id" => "gid://shopify/Location/74913710390"},
        "shippingAddress" => nil,
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "9.45"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "1.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2023-08-31T11:05:47Z"
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 17,
        "requestedQueryCost" => 467,
        "throttleStatus" => %{
          "currentlyAvailable" => 1983,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @customer_without_identifiers_response put_in(
                                           @ecommerce_order_response,
                                           ["data", "order", "customer"],
                                           %{
                                             "email" => nil,
                                             "firstName" => "Nameless",
                                             "id" => "gid://shopify/Customer/NO_IDENTIFIERS",
                                             "lastName" => "Customer",
                                             "phone" => nil
                                           }
                                         )

  @sku_fallback_order_response %{
    "data" => %{
      "order" => %{
        "app" => %{"name" => "Online Store"},
        "createdAt" => "2024-12-05T18:44:51Z",
        "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "10.0"}},
        "customer" => %{
          "email" => "<EMAIL>",
          "firstName" => "Julia",
          "id" => "gid://shopify/Customer/7525440487734",
          "lastName" => "Rezende",
          "phone" => "+5511971748987"
        },
        "displayFinancialStatus" => "PAID",
        "fulfillmentOrders" => %{
          "nodes" => [
            %{
              "assignedLocation" => %{
                "location" => %{"id" => "gid://shopify/Location/74913710390"}
              },
              "deliveryMethod" => %{"methodType" => "PICK_UP"},
              "lineItems" => %{
                "nodes" => [
                  %{
                    "image" => %{
                      "url" =>
                        "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458"
                    },
                    "lineItem" => %{
                      "currentQuantity" => 1,
                      "discountedUnitPriceAfterAllDiscountsSet" => %{
                        "shopMoney" => %{"amount" => "10.0"}
                      }
                    },
                    "productTitle" => "Sandálias Amazônia (MAIS VELHO)",
                    # MODIFIED FOR SKU FALLBACK TEST
                    "sku" => nil,
                    # This variant ID maps to pv_for_sku_test
                    "variant" => %{
                      "id" => "gid://shopify/ProductVariant/44162076115254"
                    },
                    "variantTitle" => "34/35"
                  }
                ]
              },
              "status" => "CLOSED"
            }
          ]
        },
        # Unique ID for this test case
        "id" => "gid://shopify/Order/SKU_FALLBACK_ORDER_ID",
        # Unique name
        "name" => "#SKU_FALLBACK_ORDER",
        "retailLocation" => nil,
        "shippingAddress" => %{
          "address1" => "Rua dos Pinheiros, 423",
          "address2" => "812",
          "city" => "São Paulo",
          "country" => "Brazil",
          "firstName" => "Julia",
          "lastName" => "Rezende",
          "phone" => "",
          "provinceCode" => "SP",
          "zip" => "05422-010"
        },
        "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "10.0"}},
        "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
        "updatedAt" => "2025-03-27T03:19:58Z"
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 17,
        "requestedQueryCost" => 467,
        "throttleStatus" => %{
          "currentlyAvailable" => 1983,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  setup do
    org = insert(:organization)
    ecomm_loc = insert(:location, organization: org, name: "e-commerce")
    loc = insert(:location, organization: org, name: "physical store")

    insert(:location_mapping,
      external_id: "gid://shopify/Location/74913710390",
      source: "shopify",
      location: ecomm_loc,
      organization: org
    )

    insert(:location_mapping,
      external_id: "gid://shopify/Location/77699186998",
      source: "shopify",
      location: loc,
      organization: org
    )

    insert(:shopify_credential, %{
      shop: "iglu-demo.myshopify.com",
      credential: "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",
      organization: org
    })

    pv =
      insert(:product_variant,
        sku: "PV_SKU_FOR_FALLBACK",
        product: build(:product, organization: org),
        organization: org
      )

    insert(:product_variant_mapping,
      product_variant: pv,
      external_id: "gid://shopify/ProductVariant/44162076115254",
      organization: org,
      source: "shopify"
    )

    pv2 =
      insert(:product_variant, product: build(:product, organization: org), organization: org)

    insert(:product_variant_mapping,
      product_variant: pv2,
      external_id: "gid://shopify/ProductVariant/44162059534646",
      organization: org,
      source: "shopify"
    )

    Mox.stub(ShopifyMock, :client, fn _, _ -> :mock_client end)

    {:ok, %{organization: org, location: loc, pv_for_sku_test: pv}}
  end

  # Helper to set up the mock expectations for Shopify API calls
  defp expect_shopify_get_order(shopify_id, response) do
    ShopifyMock
    |> expect(:get_order!, fn _client, ^shopify_id, _selection, _opts ->
      {:ok, response}
    end)
  end

  describe "import_order/2" do
    test "successfully imports from ecommerce", %{organization: org} do
      shopify_id = "gid://shopify/Order/6275942777142"

      # Set up the mock expectation
      expect_shopify_get_order(shopify_id, @ecommerce_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))

      assert order.id
      assert order.external_id == "gid://shopify/Order/6275942777142"
      assert order.name == "#2700"
      assert order.status == "paid"
      assert order.source == "shopify"
      assert order.sales_channel == "Online Store"
      assert order.organization_id == org.id

      # Preload related data for assertions
      order = Rms.Repo.preload(order, fulfillments: [:line_items])

      # Check reference_at timestamp
      expected_reference_at = ~U[2024-12-05 18:44:51Z]
      assert order.reference_at == expected_reference_at
      assert hd(order.fulfillments).reference_at == expected_reference_at
      assert hd(hd(order.fulfillments).line_items).reference_at == expected_reference_at

      assert %{
               receiver_name: "Julia Rezende",
               city_name: "São Paulo",
               state: "SP",
               country_name: "Brazil",
               street: "Rua dos Pinheiros, 423",
               zip: "05422-010",
               complement: "812"
             } = order.shipping_address
    end

    test "successfully imports from shopify pos", %{organization: org, location: loc} do
      shopify_id = "gid://shopify/Order/6258798362934"

      expect_shopify_get_order(shopify_id, @pos_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))
      assert order.location_id == loc.id
      assert order.source == "shopify"
      assert order.sales_channel == "Point of Sale"
    end

    test "successfully imports the customer if not exists", %{organization: org} do
      shopify_id = "gid://shopify/Order/6275942777142"

      expect_shopify_get_order(shopify_id, @ecommerce_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))
      assert order.customer_id

      mapping =
        Rms.Repo.get_by(Rms.Integrations.CustomerSyncMapping,
          customer_id: order.customer_id,
          source: "shopify",
          external_id: "gid://shopify/Customer/7525440487734"
        )

      assert mapping
    end

    test "re-uses customer if it already exists", %{organization: org} do
      # Create a customer in our system
      customer = insert(:customer, organization: org)

      # Create a mapping between Shopify customer and our customer
      insert(:customer_sync_mapping,
        customer: customer,
        external_id: "gid://shopify/Customer/7525440487734",
        organization: org,
        source: "shopify"
      )

      shopify_id = "gid://shopify/Order/6275942777142"

      expect_shopify_get_order(shopify_id, @ecommerce_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))

      # Verify the order uses the existing customer
      assert order.customer_id == customer.id
    end

    test "successfully imports customer without email or phone", %{organization: org} do
      shopify_order_id = get_in(@customer_without_identifiers_response, ["data", "order", "id"])

      shopify_customer_id =
        get_in(@customer_without_identifiers_response, ["data", "order", "customer", "id"])

      # Ensure no mapping exists initially
      refute Rms.Integrations.get_customer_sync_mapping(
               org.id,
               "shopify",
               shopify_customer_id
             )

      expect_shopify_get_order(shopify_order_id, @customer_without_identifiers_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_order_id)
      assert_emit("order.imported", Order.event_payload(order))

      # Assert customer was created and linked
      assert order.customer_id
      customer = Rms.Customers.get_customer!(order.organization_id, order.customer_id)
      assert customer.name == "Nameless Customer"
      assert customer.email == nil
      assert customer.primary_phone_number == nil

      # Assert mapping was created
      mapping =
        Rms.Integrations.get_customer_sync_mapping(org.id, "shopify", shopify_customer_id)

      assert mapping
      assert mapping.customer_id == customer.id
    end

    test "successfully imports discount information", %{organization: org} do
      shopify_id = "gid://shopify/Order/5537148797238"

      expect_shopify_get_order(shopify_id, @discount_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))

      assert order.total_price == Decimal.new("8.70")
      # we use the selling price directly
      assert order.total_discount == Decimal.new("0.00")
      # but we do save what was the discount given on the ecommerce
      assert order.total_ecommerce_discounts == Decimal.new("1.0")
    end

    test "successfully imports fulfillments with line items", %{organization: org} do
      shopify_id = "gid://shopify/Order/6275942777142"

      expect_shopify_get_order(shopify_id, @ecommerce_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))

      assert [fulfillment] = order.fulfillments
      assert fulfillment.shipping_method == "local-pickup"

      assert [line_item] = fulfillment.line_items
      assert line_item.product_variant_id
      assert line_item.quantity == 1
      assert line_item.price == Decimal.new("10.0")
      assert line_item.list_price == Decimal.new("10.0")
      assert line_item.manual_discount == 0

      # Check reference_at timestamp
      expected_reference_at = ~U[2024-12-05 18:44:51Z]
      assert order.reference_at == expected_reference_at
      assert fulfillment.reference_at == expected_reference_at
      assert line_item.reference_at == expected_reference_at

      assert order.total_price == Decimal.new("10.0")
      assert order.total_items_selling_price == Decimal.new("10.0")
      assert order.total_items_list_price == Decimal.new("10.0")
      assert order.total_discount == Decimal.new("0.0")
      assert order.total_delivery_price == Decimal.new("0.0")
      assert order.total_ecommerce_discounts == Decimal.new("0.0")
      assert order.total_items_manual_discount == Decimal.new("0")
    end

    test "sucessfully imports line items for products that do not exist", %{organization: org} do
      # Use the response with a missing variant ID
      shopify_id = "gid://shopify/Order/5537148797238"
      expect_shopify_get_order(shopify_id, @missing_variant_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      assert_emit("order.imported", Order.event_payload(order))

      # Preload fulfillments and line items
      order = Rms.Repo.preload(order, fulfillments: [:line_items])

      assert [fulfillment] = order.fulfillments
      # Sort by SKU for predictable order
      assert [line_item_existing, line_item_missing] =
               fulfillment.line_items |> Enum.sort_by(& &1.sku)

      # Assertions for the existing item
      # Should have an ID
      assert line_item_existing.product_variant_id
      assert line_item_existing.sku == "108"
      assert line_item_existing.quantity == 3
      assert line_item_existing.price == Decimal.new("0.75")

      # Assertions for the missing item
      # Should be nil
      assert line_item_missing.product_variant_id == nil
      assert line_item_missing.sku == "MISSING"
      assert line_item_missing.product_name == "Missing Product"
      assert line_item_missing.quantity == 1
      assert line_item_missing.price == Decimal.new("6.45")
    end

    test "returns proper error if fulfillment location is not found internally", %{
      organization: org
    } do
      # Define a location ID that does not exist in our setup
      missing_location_id = "gid://shopify/Location/MISSING-LOCATION-ID-999"

      # Modify the ecommerce response to use this missing location ID
      missing_location_response =
        put_in(
          @ecommerce_order_response,
          [
            "data",
            "order",
            "fulfillmentOrders",
            "nodes",
            Access.at(0),
            "assignedLocation",
            "location",
            "id"
          ],
          missing_location_id
        )

      # Get the order ID from the modified response
      shopify_id = get_in(missing_location_response, ["data", "order", "id"])

      # Set up the mock expectation to return the modified response
      expect_shopify_get_order(shopify_id, missing_location_response)

      # Call the function under test and assert the specific error
      assert {:error, {:location_mapping_not_found, ^missing_location_id}} =
               Orders.import_order(org.id, shopify_id)
    end

    test "returns proper error if retailLocation exists on shopify and not internally", %{
      organization: org
    } do
      # Define a location ID that does not exist in our setup
      missing_location_id = "gid://shopify/Location/MISSING-RETAIL-LOCATION-ID-123"

      # Modify the POS response to use this missing location ID in retailLocation
      missing_retail_location_response =
        put_in(
          @pos_order_response,
          ["data", "order", "retailLocation", "id"],
          missing_location_id
        )
        |> put_in(
          [
            "data",
            "order",
            "fulfillmentOrders",
            "nodes",
            Access.at(0),
            "assignedLocation",
            "location",
            "id"
          ],
          nil
        )

      # Get the order ID from the modified response
      shopify_id = get_in(missing_retail_location_response, ["data", "order", "id"])

      # Set up the mock expectation to return the modified response
      expect_shopify_get_order(shopify_id, missing_retail_location_response)

      # Call the function under test and assert the specific error
      assert {:error, {:location_mapping_not_found, ^missing_location_id}} =
               Orders.import_order(org.id, shopify_id)
    end

    test "uses product_variant SKU if Shopify SKU is nil", %{
      organization: org,
      pv_for_sku_test: pv
    } do
      shopify_id = "gid://shopify/Order/SKU_FALLBACK_ORDER_ID"

      expect_shopify_get_order(shopify_id, @sku_fallback_order_response)

      assert {:ok, order} = Orders.import_order(org.id, shopify_id)
      order = Rms.Repo.preload(order, fulfillments: [:line_items])

      assert [fulfillment] = order.fulfillments
      assert [line_item] = fulfillment.line_items

      assert line_item.sku == pv.sku
      assert line_item.sku == "PV_SKU_FOR_FALLBACK"
    end
  end
end
