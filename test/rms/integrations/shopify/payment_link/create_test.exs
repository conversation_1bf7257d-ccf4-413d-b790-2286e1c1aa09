defmodule Rms.Integrations.Shopify.PaymentLink.CreateTest do
  use Rms.DataCase

  alias Rms.Integrations.Shopify.PaymentLink.Create

  import Mox
  setup :verify_on_exit!

  describe "execute/1" do
    test "return a link to Shopify checkout" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          external_reference: "123",
          organization: org
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "shopify"}
      )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_draft_order, 1, fn _, ^external_id ->
        {:ok,
         %{
           "id" => external_id,
           "ready" => true,
           "status" => "OPEN",
           "invoiceUrl" => "url"
         }}
      end)

      assert {:ok, _, ^external_id} =
               Create.execute(org.id, order)
    end

    test "return a error when external reference is null" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          external_reference: nil,
          organization: org
        )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "shopify"}
      )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_draft_order, 0, fn _, ^external_id ->
        {:ok,
         %{
           "id" => external_id,
           "ready" => true,
           "status" => "OPEN",
           "invoiceUrl" => "url"
         }}
      end)

      assert {:error, "can not create payment link for this order"} =
               Create.execute(org.id, order)
    end

    test "return a error when order has more than 1 fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          external_reference: "123",
          organization: org
        )

      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        external_reference: "1234",
        organization: org
      )

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store"
        )

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "shopify"}
      )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_draft_order, 0, fn _, ^external_id ->
        {:ok,
         %{
           "id" => external_id,
           "ready" => true,
           "status" => "OPEN",
           "invoiceUrl" => "url"
         }}
      end)

      assert {:error, "can not create payment link for this order"} =
               Create.execute(org.id, order)
    end
  end
end
