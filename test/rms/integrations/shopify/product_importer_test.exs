defmodule Rms.Integrations.Shopify.ProductImporterTest do
  use Rms.DataCase
  use Rms.EventsCase

  alias Rms.Integrations.ProductSyncMapping
  alias Rms.Integrations.ProductVariantMapping
  alias Rms.Integrations.Shopify.ProductImporter

  describe "import_product/3" do
    test "handles archived variants with same SKU correctly" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization)

      # Create an archived variant with SKU "123"
      archived_variant =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: NaiveDateTime.utc_now(),
          organization: organization
        )

      # Create an active variant with the same SKU
      active_variant =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: nil,
          bar_code: "old_barcod",
          organization: organization
        )

      # Create the product variant mapping for the active variant
      insert(:product_variant_mapping,
        organization: organization,
        product_variant: active_variant,
        external_id: "shopify_variant_id",
        source: "shopify"
      )

      external_product = %{
        "id" => "shopify_product_id",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "shopify_variant_id",
                "sku" => "123",
                "barcode" => "123456",
                "price" => %{"amount" => "10.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, %{product: _updated_product}} =
               ProductImporter.import_product(product_sync, external_product)

      # Verify the archived variant remains archived
      assert Repo.reload!(archived_variant).archived_at

      # Verify the active variant remains unarchived and gets updated
      updated_active = Repo.reload!(active_variant)
      refute updated_active.archived_at
      assert updated_active.sku == "123"
      assert updated_active.bar_code == "123456"
      assert Decimal.equal?(updated_active.price, Decimal.new("10.00"))

      # Verify mapping points to active variant
      mapping =
        Repo.get_by(ProductVariantMapping,
          external_id: "shopify_variant_id",
          source: "shopify"
        )

      assert mapping.product_variant_id == active_variant.id
    end

    test "creates new variant if none exist with that SKU" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization)

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "shopify_product_id",
        source: "shopify"
      )

      external_product = %{
        "id" => "shopify_product_id",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "shopify_variant_id",
                "sku" => "new_sku",
                "barcode" => "123456",
                "price" => %{"amount" => "10.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, %{product: updated_product}} =
               ProductImporter.import_product(product_sync, external_product)

      updated_product = Repo.reload!(updated_product)
      variants = Repo.preload(updated_product, :product_variants).product_variants
      assert length(variants) == 1
      assert hd(variants).sku == "new_sku"
      assert hd(variants).bar_code == "123456"
      assert Decimal.equal?(hd(variants).price, Decimal.new("10.00"))
    end

    test "handles duplicate SKU mapping correctly" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)

      # Create an existing product with SKU
      existing_product = insert(:product, organization: organization)

      existing_variant =
        insert(:product_variant,
          product: existing_product,
          sku: "DC000208",
          organization: organization
        )

      # Create an existing mapping for this product to a different Shopify ID
      insert(:product_sync_mapping,
        organization: organization,
        product: existing_product,
        external_id: "gid://shopify/Product/existing_product_id",
        source: "shopify"
      )

      other_existing_product = insert(:product, organization: organization)

      insert(:product_variant,
        product: other_existing_product,
        sku: "DC000208",
        organization: organization
      )

      # Create an existing mapping for this product to a different Shopify ID
      insert(:product_sync_mapping,
        organization: organization,
        product: other_existing_product,
        external_id: "gid://shopify/Product/other_existing_product_id",
        source: "shopify"
      )

      # Try to import a new Shopify product with same SKU
      external_product = %{
        "id" => "gid://shopify/Product/new_product_id",
        "title" => "New Product Title",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductVariant/new_variant_id",
                "sku" => "DC000208",
                "barcode" => "7908335406111",
                "price" => %{"amount" => "129.90"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, _} =
               ProductImporter.import_product(product_sync, external_product)

      # Verify the existing mapping wasn't changed
      mapping =
        Repo.get_by(ProductSyncMapping,
          organization_id: organization.id,
          product_id: existing_product.id,
          source: "shopify"
        )

      assert mapping.external_id == "gid://shopify/Product/existing_product_id"

      other_mapping =
        Repo.get_by(ProductSyncMapping,
          organization_id: organization.id,
          product_id: other_existing_product.id,
          source: "shopify"
        )

      assert other_mapping.external_id == "gid://shopify/Product/other_existing_product_id"
      # Verify the variant wasn't changed
      variant = Repo.reload!(existing_variant)
      assert variant.bar_code != "7908335406111"

      # Verify a new product was created
      new_product_mapping =
        Repo.get_by(ProductSyncMapping,
          organization_id: organization.id,
          external_id: "gid://shopify/Product/new_product_id",
          source: "shopify"
        )

      assert new_product_mapping != nil

      # Verify new product has the variant with same SKU
      new_product = Repo.preload(new_product_mapping, product: [:product_variants]).product
      assert length(new_product.product_variants) == 1
      new_variant = hd(new_product.product_variants)
      assert new_variant.sku == "DC000208"
      assert new_variant.bar_code == "7908335406111"
    end

    test "handles multiple existing variants with matching SKUs correctly" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)

      # Create an existing product with multiple variants
      existing_product = insert(:product, organization: organization)

      # Create variants with SKUs matching the external product
      variant_1 =
        insert(:product_variant,
          product: existing_product,
          sku: "SKU001",
          bar_code: "old_barcode_1",
          organization: organization
        )

      variant_2 =
        insert(:product_variant,
          product: existing_product,
          sku: "SKU002",
          bar_code: "old_barcode_2",
          organization: organization
        )

      # Create mapping for existing product
      insert(:product_sync_mapping,
        organization: organization,
        product: existing_product,
        external_id: "existing_shopify_id",
        source: "shopify"
      )

      # External product with matching SKUs
      external_product = %{
        "id" => "new_shopify_id",
        "title" => "New Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "new_variant_1",
                "sku" => "SKU001",
                "barcode" => "new_barcode_1",
                "price" => %{"amount" => "10.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            },
            %{
              "node" => %{
                "id" => "new_variant_2",
                "sku" => "SKU002",
                "barcode" => "new_barcode_2",
                "price" => %{"amount" => "20.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, _} = ProductImporter.import_product(product_sync, external_product)

      # Verify existing product mapping wasn't changed
      existing_mapping =
        Repo.get_by(ProductSyncMapping,
          organization_id: organization.id,
          product_id: existing_product.id,
          source: "shopify"
        )

      assert existing_mapping.external_id == "existing_shopify_id"

      # Verify existing variants weren't changed
      variant_1 = Repo.reload!(variant_1)
      variant_2 = Repo.reload!(variant_2)
      assert variant_1.bar_code == "old_barcode_1"
      assert variant_2.bar_code == "old_barcode_2"

      # Verify new product was created with matching SKUs
      new_mapping =
        Repo.get_by(ProductSyncMapping,
          organization_id: organization.id,
          external_id: "new_shopify_id",
          source: "shopify"
        )

      assert new_mapping != nil

      new_product = Repo.preload(new_mapping, product: [:product_variants]).product
      assert length(new_product.product_variants) == 2

      new_variants = Enum.sort_by(new_product.product_variants, & &1.sku)
      assert Enum.map(new_variants, & &1.sku) == ["SKU001", "SKU002"]
      assert Enum.map(new_variants, & &1.bar_code) == ["new_barcode_1", "new_barcode_2"]
    end

    test "handles Shopify variant ID changes correctly" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization, name: "Test Product")

      # Create initial variant and mapping
      variant =
        insert(:product_variant,
          product: product,
          organization: organization,
          sku: "TEST123",
          bar_code: "123456"
        )

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "gid://shopify/Product/12345",
        source: "shopify"
      )

      insert(:product_variant_mapping,
        organization: organization,
        product_variant: variant,
        external_id: "gid://shopify/ProductVariant/old_variant_id",
        source: "shopify"
      )

      # Import same product with new variant ID
      external_product = %{
        "id" => "gid://shopify/Product/12345",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductVariant/new_variant_id",
                "sku" => "TEST123",
                "barcode" => "123456",
                "price" => %{"amount" => "10.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, _result} = ProductImporter.import_product(product_sync, external_product)

      # Verify old mapping was replaced
      assert mapping =
               Repo.get_by(ProductVariantMapping,
                 organization_id: organization.id,
                 product_variant_id: variant.id,
                 source: "shopify"
               )

      assert mapping.external_id == "gid://shopify/ProductVariant/new_variant_id"
      # Verify no new product variant was created
      variants =
        Repo.all(
          from v in Rms.Commerce.Products.ProductVariant,
            where:
              v.organization_id == ^organization.id and
                v.sku == "TEST123"
        )

      assert length(variants) == 1
    end

    test "handles variants with same SKU, one archived" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization)

      # Create an initial archived variant
      archived_variant =
        insert(:product_variant,
          product: product,
          sku: "SKU123",
          archived_at: NaiveDateTime.utc_now(),
          organization: organization
        )

      # Create a mapping for the archived variant
      insert(:product_variant_mapping,
        organization: organization,
        product_variant: archived_variant,
        external_id: "gid://shopify/ProductVariant/123",
        source: "shopify"
      )

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "gid://shopify/Product/456",
        source: "shopify"
      )

      # Import product with same SKU
      external_product = %{
        "id" => "gid://shopify/Product/456",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductVariant/123",
                "sku" => "SKU123",
                "barcode" => "NEW_BARCODE",
                "price" => %{"amount" => "29.90"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, _result} = ProductImporter.import_product(product_sync, external_product)

      # archived variant should have been unarchived and updated
      archived_variant = Repo.reload!(archived_variant)
      refute archived_variant.archived_at
      assert archived_variant.sku == "SKU123"
      assert archived_variant.bar_code == "NEW_BARCODE"

      # Verify mapping still points to archived variant
      mapping =
        Repo.get_by(ProductVariantMapping,
          external_id: "gid://shopify/ProductVariant/123",
          source: "shopify"
        )

      assert mapping.product_variant_id == archived_variant.id
    end

    test "uses SKU as barcode when barcode is nil or empty" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization)

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "shopify_product_id",
        source: "shopify"
      )

      external_product = %{
        "id" => "shopify_product_id",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_with_nil_barcode",
                "sku" => "SKU123",
                "barcode" => nil,
                "price" => %{"amount" => "10.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            },
            %{
              "node" => %{
                "id" => "variant_with_empty_barcode",
                "sku" => "SKU456",
                "barcode" => "",
                "price" => %{"amount" => "20.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            },
            %{
              "node" => %{
                "id" => "variant_without_barcode",
                "sku" => "SKU789",
                "price" => %{"amount" => "30.00"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, %{product: updated_product}} =
               ProductImporter.import_product(product_sync, external_product)

      variants =
        updated_product
        |> Repo.reload!()
        |> Repo.preload(:product_variants)
        |> Map.get(:product_variants)
        |> Enum.sort_by(& &1.sku)

      assert length(variants) == 3

      [variant1, variant2, variant3] = variants
      assert variant1.sku == "SKU123"
      # Barcode defaulted to SKU
      assert variant1.bar_code == "SKU123"

      assert variant2.sku == "SKU456"
      # Barcode defaulted to SKU
      assert variant2.bar_code == "SKU456"

      assert variant3.sku == "SKU789"
      # Barcode defaulted to SKU
      assert variant3.bar_code == "SKU789"
    end

    test "prefers unarchived variant when both archived and unarchived exist" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)
      product = insert(:product, organization: organization)

      # Create an archived variant
      archived_variant =
        insert(:product_variant,
          product: product,
          sku: "SKU123",
          bar_code: "OLD_BARCODE_1",
          archived_at: NaiveDateTime.utc_now(),
          organization: organization
        )

      # Create an unarchived variant with same SKU
      unarchived_variant =
        insert(:product_variant,
          product: product,
          sku: "SKU123",
          bar_code: "OLD_BARCODE_2",
          organization: organization
        )

      # Create mapping for the unarchived variant
      insert(:product_variant_mapping,
        organization: organization,
        product_variant: unarchived_variant,
        external_id: "gid://shopify/ProductVariant/123",
        source: "shopify"
      )

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "gid://shopify/Product/456",
        source: "shopify"
      )

      external_product = %{
        "id" => "gid://shopify/Product/456",
        "title" => "Test Product",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductVariant/123",
                "sku" => "SKU123",
                "barcode" => "NEW_BARCODE",
                "price" => %{"amount" => "29.90"},
                "selectedOptions" => [],
                "image" => nil
              }
            }
          ]
        },
        "images" => %{"edges" => []}
      }

      assert {:ok, _result} = ProductImporter.import_product(product_sync, external_product)

      # Verify archived variant remains archived and unchanged
      archived_variant = Repo.reload!(archived_variant)
      assert archived_variant.archived_at
      assert archived_variant.bar_code == "OLD_BARCODE_1"

      # Verify unarchived variant was updated
      unarchived_variant = Repo.reload!(unarchived_variant)
      refute unarchived_variant.archived_at
      assert unarchived_variant.bar_code == "NEW_BARCODE"
      assert Decimal.equal?(unarchived_variant.price, Decimal.new("29.90"))

      # Verify mapping still points to unarchived variant
      mapping =
        Repo.get_by(ProductVariantMapping,
          external_id: "gid://shopify/ProductVariant/123",
          source: "shopify"
        )

      assert mapping.product_variant_id == unarchived_variant.id
    end

    test "updates existing product and variant with full shopify payload" do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)

      # Create existing product
      product =
        insert(:product,
          organization: organization,
          name: "Oma - Óculos de Sol POS"
        )

      # Create existing variant
      variant =
        insert(:product_variant,
          product: product,
          organization: organization,
          sku: "000729274001",
          bar_code: nil,
          price: Decimal.new("500.00")
        )

      # Create existing mappings
      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "gid://shopify/Product/8111896658085",
        source: "shopify"
      )

      insert(:product_variant_mapping,
        organization: organization,
        product_variant: variant,
        external_id: "gid://shopify/ProductVariant/42098185535653",
        source: "shopify"
      )

      external_product = %{
        "id" => "gid://shopify/Product/8111896658085",
        "title" => "Oma - Óculos de Sol POS",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductVariant/42098185535653",
                "sku" => "000729274001",
                "barcode" => "",
                "price" => %{"amount" => "545.0"},
                "selectedOptions" => [
                  %{
                    "name" => "Cor",
                    "value" => "tortoise giorno 274"
                  }
                ],
                "image" => %{
                  "id" => "gid://shopify/ProductImage/33837384106149",
                  "url" =>
                    "https://cdn.shopify.com/s/files/1/0399/6470/4933/products/produto_acetato_oma_tortoise-giorno_solar_01.jpg?v=1670549491"
                }
              }
            }
          ]
        },
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "gid://shopify/ProductImage/33837384106149",
                "url" =>
                  "https://cdn.shopify.com/s/files/1/0399/6470/4933/products/produto_acetato_oma_tortoise-giorno_solar_01.jpg?v=1670549491"
              }
            }
          ]
        }
      }

      assert {:ok, %{product: updated_product}} =
               ProductImporter.import_product(product_sync, external_product, :incremental)

      # Verify product was updated
      updated_product = Repo.reload!(updated_product)
      assert updated_product.id == product.id
      assert updated_product.name == "Oma - Óculos de Sol POS"

      # Verify variant was updated
      updated_variant =
        updated_product
        |> Repo.preload(:product_variants)
        |> Map.get(:product_variants)
        |> List.first()

      assert updated_variant.id == variant.id
      assert updated_variant.sku == "000729274001"
      # SKU used as barcode since barcode was empty
      assert updated_variant.bar_code == "000729274001"
      assert Decimal.equal?(updated_variant.price, Decimal.new("545.0"))
    end
  end
end
