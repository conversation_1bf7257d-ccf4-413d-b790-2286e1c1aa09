defmodule Rms.Integrations.Shopify.Products.GetVariantEcommerceStockTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  alias Rms.Integrations.Shopify.Products.GetVariantEcommerceStock

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    product_variant = insert(:product_variant, organization: org)

    %{id: external_id} =
      insert(:product_variant_mapping,
        product_variant: product_variant,
        source: "shopify",
        organization: org
      )

    insert(:shopify_credential, organization: org)

    {:ok, %{org: org, product_variant: product_variant, external_id: external_id}}
  end

  describe "get_remote_stock/2" do
    test "returns the stock of a product variant from Shopify", %{
      org: org,
      product_variant: product_variant
    } do
      expect(ShopifyMock, :storefront_client, fn _, _, _ -> :mock_client end)

      expect(ShopifyMock, :get_product_variant_stock, fn _, _ ->
        {:ok,
         %{
           "availableForSale" => false,
           "quantityAvailable" => 0
         }}
      end)

      assert {:ok, %{available_for_sale: false, quantity_available: 0}} =
               GetVariantEcommerceStock.call(org.id, product_variant.id)
    end
  end
end
