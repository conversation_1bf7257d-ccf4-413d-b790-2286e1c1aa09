defmodule Rms.Integrations.Shopify.Product.ImportTest do
  use Rms.DataCase
  use ExUnit.Case

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  describe "execute/1" do
    test "import and format a product" do
      org = insert(:organization)
      insert(:shopify_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_product, fn _, _ ->
        {:ok,
         %{
           "createdAt" => "2022-12-20T15:00:56Z",
           "description" =>
             "Sandália Verde | Amazônia Esta sandália verde é a linha Amazônia - uma cor que desenvolvemos para uma ação bem especial! Nós acreditamos que estamos diretamente conectados à vida na floresta. Mais do que mantê-la intocada, queremos travar uma relação de respeito com a Amazônia - e isso só é possível a partir da valorização dos direitos dos povos indígenas, que são os principais responsáveis pela proteção e preservação da Amazônia Brasileira. Por isso, 100% dos lucros da linha Amazônia serão doados à Operação Amazônia Nativa. Há 52 anos, a OPAN atua pelo fortalecimento do protagonismo indígena e pela conservação do meio ambiente. Além disso, você ainda leva pra casa: A primeira sandália de plástico vegana do Brasil A sandália mais confortável que já experimentou Uma palmilha anatômica única, desenvolvida por especialistas em palmilhas A práticidade de caminhar confortável sem abrir mão do seu estilo",
           "handle" => "sandalias-amazonia",
           "id" => "gid://shopify/Product/8056763908406",
           "images" => %{
             "edges" => [
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528254262",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458",
                   "width" => 811
                 }
               },
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528287030",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM2FB.jpg?v=1671548459",
                   "width" => 811
                 }
               },
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528319798",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1.jpg?v=1671548459",
                   "width" => 811
                 }
               }
             ]
           },
           "onlineStoreUrl" => nil,
           "options" => [
             %{
               "name" => "Size",
               "values" => ["34/35", "36/37", "38/39", "40/41", "42/43", "44/45"]
             }
           ],
           "productType" => "Sandália",
           "publishedAt" => "2024-10-01T21:33:42Z",
           "seo" => %{"description" => nil, "title" => nil},
           "tags" => [],
           "title" => "Sandálias Amazônia",
           "updatedAt" => "2024-10-02T13:50:10Z",
           "variants" => %{
             "edges" => [
               %{
                 "node" => %{
                   "barcode" => "7898535802052",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076115254",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "100.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "34/35"}],
                   "sku" => "108",
                   "title" => "34/35",
                   "updatedAt" => "2024-09-28T19:37:46Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               },
               %{
                 "node" => %{
                   "barcode" => "",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076148022",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "100.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "36/37"}],
                   "sku" => "109",
                   "title" => "36/37",
                   "updatedAt" => "2024-09-23T16:52:56Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               },
               %{
                 "node" => %{
                   "barcode" => "0207008",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076180790",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "100.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "38/39"}],
                   "sku" => "0207008",
                   "title" => "38/39",
                   "updatedAt" => "2024-08-09T14:37:53Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               },
               %{
                 "node" => %{
                   "barcode" => "",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076213558",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "100.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "40/41"}],
                   "sku" => "111",
                   "title" => "40/41",
                   "updatedAt" => "2024-09-18T18:15:26Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               },
               %{
                 "node" => %{
                   "barcode" => "",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076246326",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "200.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "42/43"}],
                   "sku" => "112",
                   "title" => "42/43",
                   "updatedAt" => "2024-10-01T20:44:24Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               },
               %{
                 "node" => %{
                   "barcode" => "",
                   "compareAtPrice" => "250.00",
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076279094",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "15.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "44/45"}],
                   "sku" => "113",
                   "title" => "44/45",
                   "updatedAt" => "2024-10-01T20:54:48Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               }
             ]
           },
           "vendor" => "iglu-demo"
         }}
      end)

      assert {:ok,
              %{
                "createdAt" => "2022-12-20T15:00:56Z",
                "description" =>
                  "Sandália Verde | Amazônia Esta sandália verde é a linha Amazônia - uma cor que desenvolvemos para uma ação bem especial! Nós acreditamos que estamos diretamente conectados à vida na floresta. Mais do que mantê-la intocada, queremos travar uma relação de respeito com a Amazônia - e isso só é possível a partir da valorização dos direitos dos povos indígenas, que são os principais responsáveis pela proteção e preservação da Amazônia Brasileira. Por isso, 100% dos lucros da linha Amazônia serão doados à Operação Amazônia Nativa. Há 52 anos, a OPAN atua pelo fortalecimento do protagonismo indígena e pela conservação do meio ambiente. Além disso, você ainda leva pra casa: A primeira sandália de plástico vegana do Brasil A sandália mais confortável que já experimentou Uma palmilha anatômica única, desenvolvida por especialistas em palmilhas A práticidade de caminhar confortável sem abrir mão do seu estilo",
                "handle" => "sandalias-amazonia",
                "id" => "gid://shopify/Product/8056763908406",
                "images" => %{
                  "edges" => [
                    %{
                      "node" => %{
                        "height" => 1024,
                        "id" => "gid://shopify/ProductImage/40038528254262",
                        "url" =>
                          "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458",
                        "width" => 811
                      }
                    },
                    %{
                      "node" => %{
                        "height" => 1024,
                        "id" => "gid://shopify/ProductImage/40038528287030",
                        "url" =>
                          "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM2FB.jpg?v=1671548459",
                        "width" => 811
                      }
                    },
                    %{
                      "node" => %{
                        "height" => 1024,
                        "id" => "gid://shopify/ProductImage/40038528319798",
                        "url" =>
                          "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1.jpg?v=1671548459",
                        "width" => 811
                      }
                    }
                  ]
                },
                "onlineStoreUrl" => nil,
                "options" => [
                  %{
                    "name" => "Size",
                    "values" => ["34/35", "36/37", "38/39", "40/41", "42/43", "44/45"]
                  }
                ],
                "productType" => "Sandália",
                "publishedAt" => "2024-10-01T21:33:42Z",
                "seo" => %{"description" => nil, "title" => nil},
                "tags" => [],
                "title" => "Sandálias Amazônia",
                "updatedAt" => "2024-10-02T13:50:10Z",
                "variants" => %{
                  "edges" => [
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "7898535802052",
                        "compareAtPrice" => nil,
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076115254",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "100.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "34/35"}],
                        "sku" => "108",
                        "title" => "34/35",
                        "updatedAt" => "2024-09-28T19:37:46Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    },
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "",
                        "compareAtPrice" => nil,
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076148022",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "100.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "36/37"}],
                        "sku" => "109",
                        "title" => "36/37",
                        "updatedAt" => "2024-09-23T16:52:56Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    },
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "0207008",
                        "compareAtPrice" => nil,
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076180790",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "100.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "38/39"}],
                        "sku" => "0207008",
                        "title" => "38/39",
                        "updatedAt" => "2024-08-09T14:37:53Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    },
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "",
                        "compareAtPrice" => nil,
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076213558",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "100.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "40/41"}],
                        "sku" => "111",
                        "title" => "40/41",
                        "updatedAt" => "2024-09-18T18:15:26Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    },
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "",
                        "compareAtPrice" => nil,
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076246326",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "200.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "42/43"}],
                        "sku" => "112",
                        "title" => "42/43",
                        "updatedAt" => "2024-10-01T20:44:24Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    },
                    %{
                      "node" => %{
                        "availableForSale" => nil,
                        "barcode" => "",
                        "compareAtPrice" => "250.00",
                        "createdAt" => "2022-12-20T15:00:56Z",
                        "id" => "gid://shopify/ProductVariant/44162076279094",
                        "image" => nil,
                        "inventoryPolicy" => "DENY",
                        "price" => %{"amount" => "15.00", "currencyCode" => "BRA"},
                        "quantityAvailable" => nil,
                        "selectedOptions" => [%{"name" => "Size", "value" => "44/45"}],
                        "sku" => "113",
                        "title" => "44/45",
                        "updatedAt" => "2024-10-01T20:54:48Z",
                        "weight" => 0.0,
                        "weightUnit" => "KILOGRAMS"
                      }
                    }
                  ]
                },
                "vendor" => "iglu-demo"
              }} == Rms.Integrations.Shopify.Product.Import.execute(org.id, "external_id")
    end

    test "does not find a product" do
      org = insert(:organization)
      insert(:shopify_credential, organization: org)

      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :get_product, fn _, _ ->
        {:ok, nil}
      end)

      assert {:error, :not_found} ==
               Rms.Integrations.Shopify.Product.Import.execute(org.id, "external_id")
    end
  end
end
