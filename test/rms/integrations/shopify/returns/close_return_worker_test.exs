defmodule Rms.Integrations.Shopify.Returns.CloseReturnWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify.Returns.CloseReturnWorker
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  setup :verify_on_exit!

  describe "process/1" do
    test "closes a return in Shopify" do
      # Create test data
      organization = insert(:organization)

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Set up return ID
      return_id = "gid://shopify/Return/123456"

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> %Tesla.Client{} end)
      |> expect(:return_close, fn _, ^return_id ->
        {:ok, %{return_id: return_id, status: "CLOSED"}}
      end)

      # Process the job
      assert {:ok, %{return_id: ^return_id, status: "CLOSED"}} =
               perform_job(CloseReturnWorker, %{
                 "organization_id" => organization.id,
                 "return_id" => return_id
               })
    end

    test "handles error when return_id is not found" do
      # Create test data
      organization = insert(:organization)

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Process the job without a return_id
      assert {:error, :shopify_return_not_found} =
               perform_job(CloseReturnWorker, %{"organization_id" => organization.id},
                 meta: %{workflow_id: "123"}
               )
    end

    test "handles error when Shopify API returns an error" do
      # Create test data
      organization = insert(:organization)
      return_id = "gid://shopify/Return/123456"

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Mock the Shopify client to return an error
      ShopifyMock
      |> expect(:client, fn _, _ -> %Tesla.Client{} end)
      |> expect(:return_close, fn _, ^return_id ->
        {:error, "shopify return close failed: Return not found"}
      end)

      # Process the job
      assert {:error, "shopify return close failed: Return not found"} =
               perform_job(CloseReturnWorker, %{
                 "organization_id" => organization.id,
                 "return_id" => return_id
               })
    end

    test "handles unexpected response from Shopify API" do
      # Create test data
      organization = insert(:organization)
      return_id = "gid://shopify/Return/123456"

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Mock the Shopify client to return an unexpected response
      ShopifyMock
      |> expect(:client, fn _, _ -> %Tesla.Client{} end)
      |> expect(:return_close, fn _, ^return_id ->
        {:error,
         "unexpected response from shopify: %{\"return\" => %{\"id\" => \"#{return_id}\", \"status\" => \"OPEN\"}}"}
      end)

      # Process the job
      assert {:error, "unexpected response from shopify: " <> _} =
               perform_job(CloseReturnWorker, %{
                 "organization_id" => organization.id,
                 "return_id" => return_id
               })
    end

    test "handles Shopify API request failure" do
      # Create test data
      organization = insert(:organization)
      return_id = "gid://shopify/Return/123456"

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Mock the Shopify client to return a request failure
      ShopifyMock
      |> expect(:client, fn _, _ -> %Tesla.Client{} end)
      |> expect(:return_close, fn _, ^return_id ->
        {:error, "shopify api request failed: :timeout"}
      end)

      # Process the job
      assert {:error, "shopify api request failed: :timeout"} =
               perform_job(CloseReturnWorker, %{
                 "organization_id" => organization.id,
                 "return_id" => return_id
               })
    end
  end
end
