defmodule Rms.Integrations.Shopify.Returns.CreateReturnWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify.Returns.CreateReturnWorker
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    location = insert(:location, organization: organization)
    staff = insert(:staff, organization: organization)

    # Create a shopify credential
    insert(:shopify_credential, organization: organization)

    # Create a product and variant
    product = insert(:product, organization: organization)
    product_variant = insert(:product_variant, product: product, organization: organization)

    # Create an order and fulfillment
    order = insert(:order, organization: organization)

    fulfillment =
      insert(:fulfillment,
        order: order,
        organization: organization,
        external_reference: "gid://shopify/Order/123456"
      )

    %{
      organization: organization,
      location: location,
      staff: staff,
      product: product,
      product_variant: product_variant,
      order: order,
      fulfillment: fulfillment
    }
  end

  describe "process/1" do
    test "creates a return in Shopify", %{
      organization: organization,
      location: location,
      staff: staff,
      product_variant: product_variant,
      fulfillment: fulfillment
    } do
      # Create a line item
      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: "TEST-SKU-123"
        )

      # Create a reverse fulfillment with a line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1,
              reason: "Produto com defeito"
            )
          ]
        )

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            "quantity" => 2,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => "TEST-SKU-123",
              "title" => "Test Product"
            }
          }
        ]

        {:ok, returnable_items}
      end)
      |> expect(:return_create, fn :mock_client, _return_input ->
        {:ok, "gid://shopify/Return/123456"}
      end)

      # Process the job
      assert {:ok, %{return_id: "gid://shopify/Return/123456"}} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end

    test "handles error when SKU is not found in returnable items", %{
      organization: organization,
      location: location,
      staff: staff,
      product_variant: product_variant,
      fulfillment: fulfillment
    } do
      # Create a line item with a non-returnable SKU
      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: "NON-RETURNABLE-SKU"
        )

      # Create a reverse fulfillment with a line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1,
              reason: "Produto com defeito"
            )
          ]
        )

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            "quantity" => 2,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => "DIFFERENT-SKU",
              "title" => "Test Product"
            }
          }
        ]

        {:ok, returnable_items}
      end)

      # Process the job
      assert {:error, :non_returnable_items} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end

    test "handles error when returnable quantity is insufficient", %{
      organization: organization,
      location: location,
      staff: staff,
      product_variant: product_variant,
      fulfillment: fulfillment
    } do
      # Create a line item with the same SKU
      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: "TEST-SKU-123",
          quantity: 5
        )

      # Create a reverse fulfillment with a line item requesting more than available
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              # More than the 1 available
              returned_quantity: 5,
              reason: "Produto com defeito"
            )
          ]
        )

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            # Only 1 available
            "quantity" => 1,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => "TEST-SKU-123",
              "title" => "Test Product"
            }
          }
        ]

        {:ok, returnable_items}
      end)

      # Process the job
      assert {:error, :non_returnable_items} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end

    test "handles multiple line items with different SKUs", %{
      organization: organization,
      location: location,
      staff: staff,
      fulfillment: fulfillment
    } do
      # Create products and variants with different SKUs
      product1 = insert(:product, organization: organization)

      product_variant1 =
        insert(:product_variant,
          product: product1,
          organization: organization
        )

      product2 = insert(:product, organization: organization)

      product_variant2 =
        insert(:product_variant,
          product: product2,
          organization: organization
        )

      # Create line items with different SKUs
      line_item1 =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant1,
          fulfillment: fulfillment,
          location: location,
          sku: "SKU-1"
        )

      line_item2 =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant2,
          fulfillment: fulfillment,
          location: location,
          quantity: 2,
          sku: "SKU-2"
        )

      # Create a reverse fulfillment with multiple line items
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item1,
              returned_quantity: 1,
              reason: "Produto com defeito"
            ),
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item2,
              returned_quantity: 2,
              reason: "Produto ficou pequeno"
            )
          ]
        )

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            "quantity" => 1,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => "SKU-1",
              "title" => "Product 1"
            }
          },
          %{
            "id" => "gid://shopify/FulfillmentLineItem/790",
            "quantity" => 3,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/102",
              "sku" => "SKU-2",
              "title" => "Product 2"
            }
          }
        ]

        {:ok, returnable_items}
      end)
      |> expect(:return_create, fn :mock_client, _return_input ->
        {:ok, "gid://shopify/Return/123456"}
      end)

      # Process the job
      assert {:ok, %{return_id: "gid://shopify/Return/123456"}} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end

    test "handles error when reverse fulfillment is not found" do
      # Process the job with a non-existent reverse fulfillment ID
      assert_raise Ecto.NoResultsError,
                   fn ->
                     perform_job(CreateReturnWorker, %{
                       "reverse_fulfillment_id" => Ecto.UUID.generate(),
                       "fulfillment_id" => 999_999,
                       "organization_id" => 999_999
                     })
                   end
    end

    test "handles error when shopify credential is not found", %{
      organization: organization,
      fulfillment: fulfillment
    } do
      # Create a reverse fulfillment
      reverse_fulfillment = insert(:reverse_fulfillment, organization: organization)

      # Delete the shopify credential created in the setup
      Rms.Repo.delete_all(Rms.Integrations.ShopifyCredential)

      assert_raise Ecto.NoResultsError,
                   fn ->
                     perform_job(CreateReturnWorker, %{
                       "reverse_fulfillment_id" => reverse_fulfillment.id,
                       "fulfillment_id" => fulfillment.id,
                       "organization_id" => organization.id
                     })
                   end
    end

    test "handles error when Shopify API returns an error", %{
      organization: organization,
      location: location,
      staff: staff,
      product_variant: product_variant,
      fulfillment: fulfillment
    } do
      # Create a line item
      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: "TEST-SKU-123"
        )

      # Create a reverse fulfillment with a line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1,
              reason: "Produto com defeito"
            )
          ]
        )

      # Mock the Shopify client
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            "quantity" => 2,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => "TEST-SKU-123",
              "title" => "Test Product"
            }
          }
        ]

        {:ok, returnable_items}
      end)
      |> expect(:return_create, fn :mock_client, _return_input ->
        {:error,
         "shopify return creation failed: Return line items must be associated with the order"}
      end)

      # Process the job
      assert {:error,
              "shopify return creation failed: Return line items must be associated with the order"} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end

    test "handles error when Shopify API returns an error for returnable_fulfillments", %{
      organization: organization,
      location: location,
      staff: staff,
      product_variant: product_variant,
      fulfillment: fulfillment
    } do
      # Create a line item
      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: "TEST-SKU-123"
        )

      # Create a reverse fulfillment with a line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1,
              reason: "Produto com defeito"
            )
          ]
        )

      # Mock the Shopify client to return an error for returnable_fulfillments
      ShopifyMock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        {:error,
         "shopify api error: [%{\"extensions\" => %{\"code\" => \"NOT_FOUND\"}, \"message\" => \"Order not found\"}]"}
      end)

      # Process the job
      assert {:error,
              "shopify api error: [%{\"extensions\" => %{\"code\" => \"NOT_FOUND\"}, \"message\" => \"Order not found\"}]"} =
               perform_job(CreateReturnWorker, %{
                 "reverse_fulfillment_id" => reverse_fulfillment.id,
                 "fulfillment_id" => fulfillment.id,
                 "organization_id" => organization.id
               })
    end
  end
end
