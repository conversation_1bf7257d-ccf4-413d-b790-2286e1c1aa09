defmodule Rms.Integrations.Shopify.Returns.DisposeItemsWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox
  import Rms.Factory

  alias Rms.Integrations.Shopify.Returns.DisposeItemsWorker
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  setup :verify_on_exit!

  setup do
    # Create common test data
    organization = insert(:organization)
    return_id = "gid://shopify/Return/7835123758"
    shopify_location_id = "gid://shopify/Location/12345"

    insert(:shopify_credential, organization: organization)

    # Create location with shopify mapping
    location = insert(:location, organization: organization)

    insert(:location_mapping,
      organization: organization,
      location: location,
      source: "shopify",
      external_id: shopify_location_id
    )

    pv = insert(:product_variant, organization: organization)

    %{
      organization: organization,
      location: location,
      return_id: return_id,
      shopify_location_id: shopify_location_id,
      product_variant: pv
    }
  end

  describe "process/1" do
    test "disposes items with RESTOCKED disposition type when return_to_inventory is true", %{
      organization: organization,
      location: location,
      return_id: return_id,
      shopify_location_id: shopify_location_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true
      )

      # Setup mocks
      mock_shopify_client()
      mock_get_return(return_id, "TEST-SKU-123")
      mock_dispose_items(shopify_location_id, "RESTOCKED")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)
      assert {:ok, result} = perform_job(DisposeItemsWorker, args)

      # Verify results
      assert length(result) == 1

      [processed_item] = result
      assert processed_item.type == "RESTOCKED"
      assert processed_item.quantity == 1
      assert processed_item.location_id == shopify_location_id
    end

    test "disposes items with NOT_RESTOCKED disposition type when return_to_inventory is false",
         %{
           organization: organization,
           location: location,
           return_id: return_id,
           shopify_location_id: shopify_location_id,
           product_variant: pv
         } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: false
      )

      # Setup mocks
      mock_shopify_client()
      mock_get_return(return_id, "TEST-SKU-123")
      mock_dispose_items(shopify_location_id, "NOT_RESTOCKED")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)
      assert {:ok, result} = perform_job(DisposeItemsWorker, args)

      # Verify results
      assert length(result) == 1

      [processed_item] = result
      assert processed_item.type == "NOT_RESTOCKED"
      assert processed_item.quantity == 1
      # Don't check for location_id since it's not included for NOT_RESTOCKED items
    end

    test "handles multiple reverse fulfillment orders and uses a single API call", %{
      organization: organization,
      location: location,
      return_id: return_id,
      shopify_location_id: shopify_location_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      # Create two line items with different SKUs
      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true,
        sku: "SKU-1"
      )

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: false,
        sku: "SKU-2"
      )

      # Setup mocks
      mock_shopify_client()

      # Mock return with multiple fulfillment orders
      ShopifyMock
      |> expect(:get_return, fn :mock_client, ^return_id ->
        {:ok,
         %{
           "id" => return_id,
           "status" => "OPEN",
           "reverseFulfillmentOrders" => %{
             "edges" => [
               %{
                 "node" => %{
                   "id" => "gid://shopify/ReverseFulfillmentOrder/1",
                   "status" => "OPEN",
                   "lineItems" => %{
                     "edges" => [
                       %{
                         "node" => %{
                           "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/1",
                           "totalQuantity" => 1,
                           "fulfillmentLineItem" => %{
                             "lineItem" => %{
                               "sku" => "SKU-1"
                             }
                           }
                         }
                       }
                     ]
                   }
                 }
               },
               %{
                 "node" => %{
                   "id" => "gid://shopify/ReverseFulfillmentOrder/2",
                   "status" => "OPEN",
                   "lineItems" => %{
                     "edges" => [
                       %{
                         "node" => %{
                           "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/2",
                           "totalQuantity" => 1,
                           "fulfillmentLineItem" => %{
                             "lineItem" => %{
                               "sku" => "SKU-2"
                             }
                           }
                         }
                       }
                     ]
                   }
                 }
               }
             ]
           }
         }}
      end)

      # Mock a single dispose call for all dispositions
      ShopifyMock
      |> expect(:reverse_fulfillment_order_dispose, fn :mock_client, dispositions ->
        # Verify we have both dispositions in a single call
        assert length(dispositions) == 2

        # First disposition (RESTOCKED)
        restocked_disposition = Enum.find(dispositions, &(&1.dispositionType == "RESTOCKED"))

        assert restocked_disposition.reverseFulfillmentOrderLineItemId ==
                 "gid://shopify/ReverseFulfillmentOrderLineItem/1"

        assert restocked_disposition.quantity == 1
        assert restocked_disposition.locationId == shopify_location_id

        # Second disposition (NOT_RESTOCKED)
        not_restocked_disposition =
          Enum.find(dispositions, &(&1.dispositionType == "NOT_RESTOCKED"))

        assert not_restocked_disposition.reverseFulfillmentOrderLineItemId ==
                 "gid://shopify/ReverseFulfillmentOrderLineItem/2"

        assert not_restocked_disposition.quantity == 1
        refute Map.has_key?(not_restocked_disposition, :locationId)

        # Return both dispositions in the response
        {:ok,
         [
           %{
             "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/1",
             "dispositions" => [
               %{
                 "id" => "gid://shopify/Disposition/1",
                 "type" => "RESTOCKED",
                 "quantity" => 1,
                 "location" => %{
                   "id" => shopify_location_id
                 }
               }
             ]
           },
           %{
             "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/2",
             "dispositions" => [
               %{
                 "id" => "gid://shopify/Disposition/2",
                 "type" => "NOT_RESTOCKED",
                 "quantity" => 1,
                 "location" => nil
               }
             ]
           }
         ]}
      end)

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)
      assert {:ok, processed_items} = perform_job(DisposeItemsWorker, args)

      assert length(processed_items) == 2

      # Find items by type
      restocked_item = Enum.find(processed_items, &(&1.type == "RESTOCKED"))
      not_restocked_item = Enum.find(processed_items, &(&1.type == "NOT_RESTOCKED"))

      # Verify RESTOCKED item
      assert restocked_item.quantity == 1
      assert restocked_item.location_id == shopify_location_id

      # Verify NOT_RESTOCKED item
      assert not_restocked_item.quantity == 1
      assert not_restocked_item.location_id == nil
    end

    test "handles error when all reverse fulfillment orders are canceled or closed", %{
      organization: organization,
      location: location,
      return_id: return_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true
      )

      # Setup mocks
      mock_shopify_client()

      # Mock return with only closed/canceled fulfillment orders
      ShopifyMock
      |> expect(:get_return, fn :mock_client, ^return_id ->
        {:ok,
         %{
           "id" => return_id,
           "status" => "OPEN",
           "reverseFulfillmentOrders" => %{
             "edges" => [
               %{
                 "node" => %{
                   "id" => "gid://shopify/ReverseFulfillmentOrder/1",
                   "status" => "CLOSED",
                   "lineItems" => %{
                     "edges" => [
                       %{
                         "node" => %{
                           "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/1",
                           "totalQuantity" => 1,
                           "fulfillmentLineItem" => %{
                             "lineItem" => %{
                               "sku" => "TEST-SKU-123"
                             }
                           }
                         }
                       }
                     ]
                   }
                 }
               },
               %{
                 "node" => %{
                   "id" => "gid://shopify/ReverseFulfillmentOrder/2",
                   "status" => "CANCELED",
                   "lineItems" => %{
                     "edges" => [
                       %{
                         "node" => %{
                           "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/2",
                           "totalQuantity" => 1,
                           "fulfillmentLineItem" => %{
                             "lineItem" => %{
                               "sku" => "TEST-SKU-123"
                             }
                           }
                         }
                       }
                     ]
                   }
                 }
               }
             ]
           }
         }}
      end)

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)

      assert {:error, "no matching line items found between shopify return and our system"} =
               perform_job(DisposeItemsWorker, args)
    end

    test "handles error when reverse fulfillment is not found", %{
      organization: organization,
      return_id: return_id
    } do
      # Use a non-existent reverse fulfillment ID
      non_existent_id = Ecto.UUID.generate()

      # Execute worker
      args = %{
        "reverse_fulfillment_id" => non_existent_id,
        "organization_id" => organization.id,
        "return_id" => return_id
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(DisposeItemsWorker, args)
      end
    end

    test "handles error when shopify credential is not found", %{
      organization: organization,
      location: location,
      return_id: return_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location
      )

      # Delete the shopify credential
      Rms.Repo.delete_all("shopify_credentials")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(DisposeItemsWorker, args)
      end
    end

    test "handles error when return_id is not found", %{organization: organization} do
      # Create a valid reverse fulfillment to avoid the NoResultsError
      reverse_fulfillment =
        create_reverse_fulfillment(organization, insert(:location, organization: organization))

      args = %{
        "reverse_fulfillment_id" => reverse_fulfillment.id,
        "organization_id" => organization.id
        # Intentionally omit return_id
      }

      assert {:error, :shopify_return_not_found} =
               perform_job(DisposeItemsWorker, args, meta: %{workflow_id: "123"})
    end

    test "handles error when location mapping is not found for RESTOCKED items", %{
      organization: organization,
      location: location,
      return_id: return_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true
      )

      # Delete the location mapping
      Rms.Repo.delete_all("location_mappings")

      # Setup mocks
      mock_shopify_client()
      mock_get_return(return_id, "TEST-SKU-123")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)

      assert_raise RuntimeError,
                   "Missing Shopify location mapping for location #{location.id}. Please add a location mapping with source 'shopify'.",
                   fn ->
                     perform_job(DisposeItemsWorker, args)
                   end
    end

    test "handles error when Shopify API returns an error", %{
      organization: organization,
      location: location,
      return_id: return_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true
      )

      # Setup mocks - client succeeds but dispose call fails
      mock_shopify_client()
      mock_get_return(return_id, "TEST-SKU-123")
      mock_dispose_items_error("API rate limit exceeded")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)
      assert {:error, "API rate limit exceeded"} = perform_job(DisposeItemsWorker, args)
    end

    test "handles error when reverse fulfillment order is not found", %{
      organization: organization,
      location: location,
      return_id: return_id,
      product_variant: pv
    } do
      # Setup test data
      reverse_fulfillment = create_reverse_fulfillment(organization, location)

      create_reverse_fulfillment_line_item(
        reverse_fulfillment,
        organization,
        pv,
        location,
        return_to_inventory: true,
        # SKU that won't match what's in the return
        sku: "UNKNOWN-SKU"
      )

      # Setup mocks
      mock_shopify_client()
      # Different SKU than what we have
      mock_get_return(return_id, "TEST-SKU-123")

      # Execute worker
      args = create_worker_args(reverse_fulfillment, organization, return_id)

      assert {:error, "no matching line items found between shopify return and our system"} =
               perform_job(DisposeItemsWorker, args)
    end
  end

  # Helper functions to reduce duplication and improve readability
  defp create_reverse_fulfillment(organization, location) do
    insert(:reverse_fulfillment,
      organization: organization,
      location: location
    )
  end

  defp create_reverse_fulfillment_line_item(
         reverse_fulfillment,
         organization,
         pv,
         location,
         opts \\ []
       ) do
    return_to_inventory = Keyword.get(opts, :return_to_inventory, true)
    sku = Keyword.get(opts, :sku, "TEST-SKU-123")
    quantity = Keyword.get(opts, :quantity, 1)

    insert(:reverse_fulfillment_line_item,
      reverse_fulfillment_id: reverse_fulfillment.id,
      organization: organization,
      line_item:
        build(:line_item,
          sku: sku,
          organization: organization,
          product_variant: pv,
          location: location
        ),
      returned_quantity: quantity,
      return_to_inventory: return_to_inventory
    )
  end

  defp mock_shopify_client do
    ShopifyMock
    |> expect(:client, fn _shop, _credential -> :mock_client end)
  end

  defp mock_get_return(return_id, sku, status \\ "OPEN") do
    ShopifyMock
    |> expect(:get_return, fn :mock_client, ^return_id ->
      {:ok,
       %{
         "id" => return_id,
         "status" => status,
         "reverseFulfillmentOrders" => %{
           "edges" => [
             %{
               "node" => %{
                 "id" => "gid://shopify/ReverseFulfillmentOrder/7552303150",
                 "status" => "OPEN",
                 "lineItems" => %{
                   "edges" => [
                     %{
                       "node" => %{
                         "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/10569580590",
                         "totalQuantity" => 1,
                         "fulfillmentLineItem" => %{
                           "lineItem" => %{
                             "sku" => sku
                           }
                         }
                       }
                     }
                   ]
                 }
               }
             }
           ]
         }
       }}
    end)
  end

  defp mock_dispose_items(shopify_location_id, disposition_type) do
    line_item_id = "gid://shopify/ReverseFulfillmentOrderLineItem/10569580590"

    ShopifyMock
    |> expect(:reverse_fulfillment_order_dispose, fn :mock_client, dispositions ->
      # Verify the disposition has the right structure and type
      assert length(dispositions) == 1
      [disposition] = dispositions

      assert disposition.reverseFulfillmentOrderLineItemId == line_item_id
      assert disposition.quantity == 1
      assert disposition.dispositionType == disposition_type

      # Only verify locationId for RESTOCKED items
      if disposition_type == "RESTOCKED" do
        assert disposition.locationId == shopify_location_id
      else
        # For NOT_RESTOCKED, ensure locationId is not present
        refute Map.has_key?(disposition, :locationId)
      end

      # Prepare response based on disposition type
      disposition_response = %{
        "id" => "gid://shopify/Disposition/1234567890",
        "type" => disposition_type,
        "quantity" => 1,
        "location" => %{
          "id" => shopify_location_id
        }
      }

      # Add locationId to response only for RESTOCKED items
      disposition_response =
        if disposition_type == "RESTOCKED" do
          Map.put(disposition_response, "locationId", shopify_location_id)
        else
          disposition_response
        end

      {:ok,
       [
         %{
           "id" => line_item_id,
           "dispositions" => [disposition_response]
         }
       ]}
    end)
  end

  defp mock_dispose_items_error(error_message) do
    ShopifyMock
    |> expect(:reverse_fulfillment_order_dispose, fn :mock_client, _dispositions ->
      {:error, error_message}
    end)
  end

  defp create_worker_args(reverse_fulfillment, organization, return_id) do
    %{
      "reverse_fulfillment_id" => reverse_fulfillment.id,
      "organization_id" => organization.id,
      "return_id" => return_id
    }
  end
end
