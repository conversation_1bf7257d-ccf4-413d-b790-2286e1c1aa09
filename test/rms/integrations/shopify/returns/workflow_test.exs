defmodule Rms.Integrations.Shopify.Returns.WorkflowTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  import Mox

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock
  alias Rms.Integrations.Shopify.Returns.Workflow

  alias Rms.Integrations.Shopify.Returns.CloseReturnWorker
  alias Rms.Integrations.Shopify.Returns.CreateReturnWorker
  alias Rms.Integrations.Shopify.Returns.DisposeItemsWorker

  setup :verify_on_exit!

  describe "build_workflow/2" do
    test "builds a workflow with three steps" do
      reverse_fulfillment = %{
        id: Ecto.UUID.generate(),
        organization_id: 1
      }

      jobs =
        reverse_fulfillment
        |> Workflow.build_workflow(1)
        |> Oban.insert_all()

      assert length(jobs) == 3

      assert_enqueued(
        worker: CloseReturnWorker,
        args: %{
          reverse_fulfillment_id: reverse_fulfillment.id,
          organization_id: reverse_fulfillment.organization_id,
          fulfillment_id: 1
        }
      )

      assert_enqueued(
        worker: <PERSON>reate<PERSON><PERSON>urn<PERSON><PERSON><PERSON>,
        args: %{
          reverse_fulfillment_id: reverse_fulfillment.id,
          organization_id: reverse_fulfillment.organization_id,
          fulfillment_id: 1
        }
      )

      assert_enqueued(
        worker: <PERSON>spose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        args: %{
          reverse_fulfillment_id: reverse_fulfillment.id,
          organization_id: reverse_fulfillment.organization_id,
          fulfillment_id: 1
        }
      )
    end

    test "builds a workflow with a provided return_id" do
      reverse_fulfillment = %{
        id: Ecto.UUID.generate(),
        organization_id: 1
      }

      jobs =
        reverse_fulfillment |> Workflow.build_workflow(1, return_id: "123") |> Oban.insert_all()

      assert length(jobs) == 2
      refute_enqueued(worker: CreateReturnWorker)

      assert_enqueued(
        worker: DisposeItemsWorker,
        args: %{
          reverse_fulfillment_id: reverse_fulfillment.id,
          organization_id: reverse_fulfillment.organization_id,
          fulfillment_id: 1,
          return_id: "123"
        }
      )

      assert_enqueued(
        worker: CloseReturnWorker,
        args: %{
          reverse_fulfillment_id: reverse_fulfillment.id,
          organization_id: reverse_fulfillment.organization_id,
          fulfillment_id: 1,
          return_id: "123"
        }
      )
    end
  end

  describe "integration tests" do
    setup do
      # Create common test data
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      staff = insert(:staff, organization: organization)

      # Create location mapping for shopify
      shopify_location_id = "gid://shopify/Location/12345"

      insert(:location_mapping,
        organization: organization,
        location: location,
        source: "shopify",
        external_id: shopify_location_id
      )

      # Create a shopify credential
      insert(:shopify_credential, organization: organization)

      # Create a product variant
      product = insert(:product, organization: organization)
      product_variant = insert(:product_variant, product: product, organization: organization)

      # Create an order and fulfillment
      order = insert(:order, organization: organization)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: organization,
          external_reference: "gid://shopify/Order/123456"
        )

      # Create a line item
      sku = "TEST-SKU-123"

      line_item =
        insert(:line_item,
          organization: organization,
          product_variant: product_variant,
          fulfillment: fulfillment,
          location: location,
          sku: sku
        )

      # Create a reverse fulfillment with a line item
      reverse_fulfillment =
        insert(:reverse_fulfillment,
          organization: organization,
          location: location,
          staff: staff,
          status: "completed",
          line_items: [
            build(:reverse_fulfillment_line_item,
              organization: organization,
              line_item: line_item,
              returned_quantity: 1,
              return_to_inventory: true,
              reason: "Produto com defeito"
            )
          ]
        )

      # Set up return ID
      return_id = "gid://shopify/Return/123456"

      # Common mock for disposing items
      dispose_items_mock = fn :mock_client, dispositions ->
        assert length(dispositions) == 1
        [disposition] = dispositions

        assert disposition.reverseFulfillmentOrderLineItemId ==
                 "gid://shopify/ReverseFulfillmentOrderLineItem/10569580590"

        assert disposition.quantity == 1
        assert disposition.dispositionType == "RESTOCKED"
        assert disposition.locationId == shopify_location_id

        {:ok,
         [
           %{
             "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/10569580590",
             "dispositions" => [
               %{
                 "id" => "gid://shopify/Disposition/1234567890",
                 "type" => "RESTOCKED",
                 "quantity" => 1,
                 "location" => %{
                   "id" => shopify_location_id
                 }
               }
             ]
           }
         ]}
      end

      # Common mock for getting return details
      get_return_mock = fn :mock_client, ^return_id ->
        {:ok,
         %{
           "id" => return_id,
           "status" => "OPEN",
           "reverseFulfillmentOrders" => %{
             "edges" => [
               %{
                 "node" => %{
                   "id" => "gid://shopify/ReverseFulfillmentOrder/7552303150",
                   "status" => "OPEN",
                   "lineItems" => %{
                     "edges" => [
                       %{
                         "node" => %{
                           "id" => "gid://shopify/ReverseFulfillmentOrderLineItem/10569580590",
                           "totalQuantity" => 1,
                           "fulfillmentLineItem" => %{
                             "lineItem" => %{
                               "sku" => sku
                             }
                           }
                         }
                       }
                     ]
                   }
                 }
               }
             ]
           }
         }}
      end

      # Return common test data
      %{
        organization: organization,
        location: location,
        staff: staff,
        shopify_location_id: shopify_location_id,
        product: product,
        product_variant: product_variant,
        order: order,
        fulfillment: fulfillment,
        sku: sku,
        line_item: line_item,
        reverse_fulfillment: reverse_fulfillment,
        return_id: return_id,
        dispose_items_mock: dispose_items_mock,
        get_return_mock: get_return_mock
      }
    end

    test "runs the complete workflow successfully", %{
      reverse_fulfillment: reverse_fulfillment,
      fulfillment: fulfillment,
      sku: sku,
      return_id: return_id,
      dispose_items_mock: dispose_items_mock,
      get_return_mock: get_return_mock
    } do
      # Mock the Shopify client for all steps
      ShopifyMock
      |> expect(:client, 3, fn _, _ -> :mock_client end)
      |> expect(:returnable_fulfillments, fn :mock_client, "gid://shopify/Order/123456" ->
        returnable_items = [
          %{
            "id" => "gid://shopify/FulfillmentLineItem/789",
            "quantity" => 2,
            "lineItem" => %{
              "id" => "gid://shopify/LineItem/101",
              "sku" => sku,
              "title" => "Test Product"
            }
          }
        ]

        {:ok, returnable_items}
      end)
      |> expect(:return_create, fn :mock_client, _return_input ->
        {:ok, return_id}
      end)
      |> expect(:get_return, get_return_mock)
      |> expect(:reverse_fulfillment_order_dispose, dispose_items_mock)
      |> expect(:return_close, fn :mock_client, ^return_id ->
        {:ok, %{return_id: return_id, status: "CLOSED"}}
      end)

      # Build and run the workflow
      workflow = Workflow.build_workflow(reverse_fulfillment, fulfillment.id)

      # Run the workflow and get the completed jobs
      jobs = run_workflow(workflow, with_summary: false)

      # Verify we have 3 completed jobs
      assert length(jobs) == 3

      # Extract the jobs
      [create_job, dispose_job, close_job] = jobs

      # Verify all jobs completed successfully
      assert create_job.state == "completed"
      assert dispose_job.state == "completed"
      assert close_job.state == "completed"
    end

    test "runs workflow with pre-existing return_id", %{
      reverse_fulfillment: reverse_fulfillment,
      fulfillment: fulfillment,
      return_id: return_id,
      dispose_items_mock: dispose_items_mock,
      get_return_mock: get_return_mock
    } do
      # Mock the Shopify client and API calls - note we don't expect return_create to be called
      ShopifyMock
      |> expect(:client, 2, fn _, _ -> :mock_client end)
      |> expect(:get_return, get_return_mock)
      |> expect(:reverse_fulfillment_order_dispose, dispose_items_mock)
      |> expect(:return_close, fn :mock_client, ^return_id ->
        {:ok, %{return_id: return_id, status: "CLOSED"}}
      end)

      # Build and run the workflow with a pre-existing return_id
      workflow =
        Workflow.build_workflow(reverse_fulfillment, fulfillment.id, return_id: return_id)

      # Run the workflow and get the completed jobs
      jobs = run_workflow(workflow, with_summary: false)

      # Verify that only two jobs completed (create_return is skipped)
      assert length(jobs) == 2

      # Extract the jobs
      [dispose_job, close_job] = jobs

      # Verify all jobs completed successfully
      assert dispose_job.state == "completed"
      assert close_job.state == "completed"
    end
  end
end
