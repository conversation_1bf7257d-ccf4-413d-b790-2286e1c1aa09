defmodule Rms.Integrations.Shopify.CreateCustomerWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify
  alias Rms.Integrations.Shopify.CreateCustomerWorker

  describe "" do
    test "process/1 only returns the customer mapping" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      insert(:customer_sync_mapping,
        customer: customer,
        source: "shopify",
        external_id: "teste",
        organization: org
      )

      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Mox.expect(Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_customers, 0, fn _, _ ->
        {:ok,
         %{
           items: [],
           cursor: 1
         }}
      end)

      Mox.expect(Shopify.Mock, :create_customer, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/Customer/123"}}
      end)

      assert perform_job(CreateCustomerWorker, %{id: fulfillment.id, organization_id: org.id}) ==
               {:ok, %{"id" => "teste"}}

      assert [customer_mapping] = Rms.Repo.all(Rms.Integrations.CustomerSyncMapping)
      assert customer_mapping.source == "shopify"
      assert customer_mapping.external_id == "teste"
    end

    test "process/1 successfully creates a new customer on Shopify" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Mox.expect(Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_customers, 2, fn _, _ ->
        {:ok,
         %{
           items: [],
           cursor: 1
         }}
      end)

      Mox.expect(Shopify.Mock, :create_customer, 1, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/Customer/123"}}
      end)

      assert perform_job(CreateCustomerWorker, %{id: fulfillment.id, organization_id: org.id}) ==
               {:ok, %{"id" => "gid://shopify/Customer/123"}}

      assert [customer_mapping] = Rms.Repo.all(Rms.Integrations.CustomerSyncMapping)
      assert customer_mapping.source == "shopify"
      assert customer_mapping.external_id == "gid://shopify/Customer/123"
    end

    test "process/1 successfully find a customer on Shopify" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, email: "<EMAIL>", organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Mox.expect(Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_customers, 1, fn _, _ ->
        {:ok,
         %{
           items: [
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "24507138"
               },
               "email" => "<EMAIL>",
               "firstName" => "Jake",
               "id" => "gid://shopify/Customer/7525413486902",
               "lastName" => "Peralta",
               "phone" => "+5511999981081"
             }
           ],
           cursor: 1
         }}
      end)

      Mox.expect(Shopify.Mock, :create_customer, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/Customer/123"}}
      end)

      assert perform_job(CreateCustomerWorker, %{id: fulfillment.id, organization_id: org.id}) ==
               {:ok, %{"id" => "gid://shopify/Customer/7525413486902"}}

      assert [customer_mapping] = Rms.Repo.all(Rms.Integrations.CustomerSyncMapping)
      assert customer_mapping.source == "shopify"
      assert customer_mapping.external_id == "gid://shopify/Customer/7525413486902"
    end
  end
end
