defmodule Rms.Integrations.Shopify.CreateDraftOrderWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Commerce.Fulfillments.Fulfillment
  alias Rms.Integrations.Shopify.CreateDraftOrderWorker

  import Mox
  setup :verify_on_exit!

  test "process/1 successfully creates a new draft order on Shopify" do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    end)

    org = insert(:organization)
    loc = insert(:location, organization: org)
    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        fulfillment: fulfillment
      )

    id = fulfillment.id

    assert {:ok, %{id: ^id, external_reference: "gid://shopify/DraftOrder/123"}} =
             perform_job(CreateDraftOrderWorker, %{
               id: fulfillment.id,
               organization_id: org.id,
               result: Rms.ShopifyMock.customer_data()
             })
  end

  test "process/1 returns a error for invalid shipping method" do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    end)

    org = insert(:organization)
    loc = insert(:location, organization: org)
    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "local-pickup",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "local-pickup",
        fulfillment: fulfillment
      )

    assert {:discard, "invalid shipping method"} =
             perform_job(CreateDraftOrderWorker, %{
               id: fulfillment.id,
               organization_id: org.id,
               result: Rms.ShopifyMock.customer_data()
             })
  end

  describe "for a delivery fulfiilment" do
    test "process/1 successfully creates a new draft order on Shopify" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      shipping_address = %{
        receiver_name: "teste",
        city_name: "São Paulo",
        state: "SP",
        country_name: "BRA",
        neighborhood: "Jardim Paulista",
        street: "Av. Paulista",
        street_type: "Avenue",
        number: "1000",
        zip: "01310100",
        complement: "Apt 1001"
      }

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          shipping_address: shipping_address,
          location: loc
        )

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          shipping_settings: %{
            price: 0,
            ecommerce: "shopify",
            organization: org,
            settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "3",
              "title" => "Carro"
            }
          },
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "delivery",
          fulfillment: fulfillment,
          shipping_settings: %{
            "deliveryMethodType" => "SHIPPING",
            "estimatedCost" => %{
              "amount" => "100.0"
            },
            "handle" => "3",
            "title" => "Carro"
          }
        )

      id = fulfillment.id

      assert {:ok, %{id: ^id, external_reference: "gid://shopify/DraftOrder/123"}} =
               perform_job(CreateDraftOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: Rms.ShopifyMock.customer_data()
               })
    end

    test "process/1 does not create a draft order when settings is nil" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      shipping_address = %{
        city_name: "São Paulo",
        state: "SP",
        country_name: "BRA",
        neighborhood: "Jardim Paulista",
        street: "Av. Paulista",
        street_type: "Avenue",
        number: "1000",
        zip: "01310100",
        complement: "Apt 1001"
      }

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      order =
        insert(:order, organization: org, customer: customer, shipping_address: shipping_address)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          shipping_settings: %{
            price: 0,
            ecommerce: "shopify",
            organization: org,
            settings: nil
          },
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "delivery",
          fulfillment: fulfillment,
          shipping_settings: %{
            "deliveryMethodType" => "SHIPPING",
            "estimatedCost" => %{
              "amount" => "100.0"
            },
            "handle" => "3",
            "title" => "Carro"
          }
        )

      assert {:error, "Shipping Settings not found"} =
               perform_job(CreateDraftOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: Rms.ShopifyMock.customer_data()
               })
    end

    test "process/1 does not create a draft order when shipping address is nil" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 0, fn _, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      shipping_address = nil

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      order =
        insert(:order, organization: org, customer: customer, shipping_address: shipping_address)

      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          shipping_settings: %{
            price: 0,
            ecommerce: "shopify",
            organization: org,
            settings: %{
              "deliveryMethodType" => "SHIPPING",
              "estimatedCost" => %{
                "amount" => "100.0"
              },
              "handle" => "3",
              "title" => "Carro"
            }
          },
          order: order,
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "delivery",
          fulfillment: fulfillment,
          shipping_settings: %{
            "deliveryMethodType" => "SHIPPING",
            "estimatedCost" => %{
              "amount" => "100.0"
            },
            "handle" => "3",
            "title" => "Carro"
          }
        )

      assert {:error, "Shipping Address not found"} =
               perform_job(CreateDraftOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: Rms.ShopifyMock.customer_data()
               })
    end
  end

  test "process/1 handles invalid email domain by temporarily nullifying it" do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error,
       {:unknown_error_code,
        %{"field" => ["email"], "message" => "Email contains an invalid domain name"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customer, 1, fn _, _ ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _, %{email: ""} ->
      {:ok, %{"email" => "", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _,
                                                                      %{
                                                                        email:
                                                                          "<EMAIL>"
                                                                      } ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    org = insert(:organization)
    loc = insert(:location, organization: org)
    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        quantity: 1
      )

    assert {:ok, _} =
             perform_job(CreateDraftOrderWorker, %{
               "id" => fulfillment.id,
               "organization_id" => org.id,
               "result" => Rms.ShopifyMock.customer_data()
             })

    updated_fulfillment = Rms.Repo.get!(Fulfillment, fulfillment.id)
    assert updated_fulfillment.external_reference == "gid://shopify/DraftOrder/123"
  end

  test "process/1 restores customer email even when draft order creation fails" do
    Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
      :mock_client
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error,
       {:unknown_error_code,
        %{"field" => ["email"], "message" => "Email contains an invalid domain name"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customer, 1, fn _, _ ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _, %{email: ""} ->
      {:ok, %{"email" => "", "id" => "gid://shopify/Customer/123"}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, _ ->
      {:error, {:unknown_error_code, %{"message" => "Some other error"}}}
    end)

    Mox.expect(Rms.Integrations.Shopify.Mock, :update_customer, 1, fn _,
                                                                      %{
                                                                        email:
                                                                          "<EMAIL>"
                                                                      } ->
      {:ok, %{"email" => "<EMAIL>", "id" => "gid://shopify/Customer/123"}}
    end)

    org = insert(:organization)
    loc = insert(:location, organization: org)
    customer = insert(:customer, organization: org)
    order = insert(:order, organization: org, customer: customer, location: loc)
    product = insert(:product, organization: org)

    pv =
      insert(:product_variant,
        product: product,
        product_variant_mappings:
          build_list(1, :product_variant_mapping, source: "shopify", organization: org),
        organization: org
      )

    insert(:shopify_credential, organization: org)

    insert(:organization_setting,
      key: "ecommerce",
      value: %{data: "shopify"},
      organization: org
    )

    fulfillment =
      insert(:fulfillment,
        ecommerce: "shopify",
        shipping_method: "in-store",
        order: order,
        organization: org
      )

    _line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        quantity: 1
      )

    assert {:error, _} =
             perform_job(CreateDraftOrderWorker, %{
               "id" => fulfillment.id,
               "organization_id" => org.id,
               "result" => Rms.ShopifyMock.customer_data()
             })

    updated_fulfillment = Rms.Repo.get!(Fulfillment, fulfillment.id)
    assert updated_fulfillment.external_reference == nil
  end

  describe "price handling in line items" do
    test "correctly handles price differences for draft orders" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, order_data ->
        Process.put(:draft_order_data, order_data)
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      org = insert(:organization)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org,
          list_price: Decimal.new("188.99")
        )

      customer = insert(:customer, organization: org)
      loc = insert(:location, organization: org)
      order = insert(:order, organization: org, customer: customer, location: loc)

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      insert(:line_item,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        price: Decimal.new("209.00"),
        list_price: Decimal.new("209.00"),
        quantity: 1
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      {:ok, _} =
        perform_job(CreateDraftOrderWorker, %{
          "id" => fulfillment.id,
          "organization_id" => org.id,
          "result" => Rms.ShopifyMock.customer_data()
        })

      draft_order_data = Process.get(:draft_order_data)

      assert %{lineItems: [line_item | _]} = draft_order_data

      assert Map.has_key?(line_item, :priceOverride)

      assert %{priceOverride: %{amount: amount, currencyCode: currency}} = line_item
      assert amount == 209.0
      assert currency == "BRL"
    end

    test "uses priceOverride for all price scenarios" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 2, fn _, order_data ->
        case order_data do
          %{lineItems: [%{quantity: 1, priceOverride: %{amount: 150.0}} | _]} ->
            Process.put(:draft_order_data_lower, order_data)

          %{lineItems: [%{quantity: 1, priceOverride: %{amount: 250.0}} | _]} ->
            Process.put(:draft_order_data_higher, order_data)
        end

        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
      end)

      org = insert(:organization)
      product = insert(:product, organization: org)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      pv_lower =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org,
          list_price: Decimal.new("200.00")
        )

      pv_higher =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org,
          list_price: Decimal.new("200.00")
        )

      order = insert(:order, organization: org, customer: customer, location: loc)

      fulfillment_lower =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      fulfillment_higher =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      insert(:line_item,
        organization: org,
        product_variant: pv_lower,
        location: loc,
        shipping_method: "in-store",
        fulfillment: fulfillment_lower,
        price: Decimal.new("150.00"),
        list_price: Decimal.new("150.00"),
        quantity: 1
      )

      insert(:line_item,
        organization: org,
        product_variant: pv_higher,
        location: loc,
        shipping_method: "in-store",
        fulfillment: fulfillment_higher,
        price: Decimal.new("250.00"),
        list_price: Decimal.new("250.00"),
        quantity: 1
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      {:ok, _} =
        perform_job(CreateDraftOrderWorker, %{
          "id" => fulfillment_lower.id,
          "organization_id" => org.id,
          "result" => Rms.ShopifyMock.customer_data()
        })

      {:ok, _} =
        perform_job(CreateDraftOrderWorker, %{
          "id" => fulfillment_higher.id,
          "organization_id" => org.id,
          "result" => Rms.ShopifyMock.customer_data()
        })

      lower_data = Process.get(:draft_order_data_lower)
      assert %{lineItems: [line_item_lower | _]} = lower_data
      assert %{priceOverride: %{amount: amount_lower}} = line_item_lower
      assert amount_lower == 150.0

      higher_data = Process.get(:draft_order_data_higher)
      assert %{lineItems: [line_item_higher | _]} = higher_data
      assert %{priceOverride: %{amount: amount_higher}} = line_item_higher
      assert amount_higher == 250.0
    end

    test "verifies Shopify draft order response contains the correct price override" do
      line_price = Decimal.new("209.00")
      catalog_price = Decimal.new("188.99")

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :create_draft_order, 1, fn _, order_data ->
        Process.put(:sent_draft_order, order_data)

        %{lineItems: [%{priceOverride: %{amount: sent_price}} | _]} = order_data

        {:ok,
         %{
           "id" => "gid://shopify/DraftOrder/123",
           "ready" => true,
           "lineItems" => [
             %{
               "id" => "gid://shopify/DraftOrderLineItem/456",
               "title" => "Test Product",
               "quantity" => 1,
               "price" => "#{sent_price}",
               "originalPrice" => "188.99"
             }
           ],
           "subtotalPrice" => "#{sent_price}",
           "totalPrice" => "#{sent_price}"
         }}
      end)

      org = insert(:organization)
      product = insert(:product, organization: org)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer, location: loc)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org,
          list_price: catalog_price
        )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      insert(:line_item,
        organization: org,
        product_variant: pv,
        location: loc,
        shipping_method: "in-store",
        fulfillment: fulfillment,
        price: line_price,
        list_price: line_price,
        quantity: 1
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      {:ok, result} =
        perform_job(CreateDraftOrderWorker, %{
          "id" => fulfillment.id,
          "organization_id" => org.id,
          "result" => Rms.ShopifyMock.customer_data()
        })

      sent_draft_order = Process.get(:sent_draft_order)
      %{lineItems: [%{priceOverride: %{amount: sent_price}} | _]} = sent_draft_order
      assert sent_price == Decimal.to_float(line_price)

      assert match?(%{external_reference: "gid://shopify/DraftOrder/123"}, result)
    end
  end
end
