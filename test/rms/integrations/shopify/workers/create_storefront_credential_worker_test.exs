defmodule Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorker

  test "process/1 successfully creates a new storefront on Shopify" do
    Rms.ShopifyMock.create_storefront_credential_mock()

    org = insert(:organization)
    insert(:shopify_credential, organization: org)

    assert {:ok, "created"} =
             perform_job(CreateStorefrontCredentialWorker, %{
               organization_id: org.id,
               storefront_credential: nil
             })
  end

  test "process/1 does not create a new storefront on Shopify when there is alredy one" do
    Rms.ShopifyMock.create_storefront_credential_mock()

    org = insert(:organization)
    insert(:shopify_credential, organization: org)
    insert(:shopify_storefront_credential, organization: org)

    assert {:discard, "discard"} =
             perform_job(CreateStorefrontCredentialWorker, %{
               organization_id: org.id,
               storefront_credential: nil
             })
  end

  test "process/1 create when a storefront is informed" do
    org = insert(:organization)
    insert(:shopify_credential, organization: org)
    insert(:shopify_storefront_credential, organization: org)

    assert {:ok, "created"} =
             perform_job(CreateStorefrontCredentialWorker, %{
               organization_id: org.id,
               storefront_credential: "teste"
             })

    credential = Rms.Integrations.get_shopify_storefront_credential(org.id)

    assert credential.credential == "teste"
  end

  test "process/1 update when a storefront is informed" do
    Rms.ShopifyMock.create_storefront_credential_mock()

    org = insert(:organization)
    insert(:shopify_credential, organization: org)
    insert(:shopify_storefront_credential, organization: org)

    assert {:ok, "created"} =
             perform_job(CreateStorefrontCredentialWorker, %{
               organization_id: org.id,
               storefront_credential: "teste"
             })

    credential = Rms.Integrations.get_shopify_storefront_credential(org.id)
    assert credential.credential == "teste"
  end
end
