defmodule Rms.Integrations.Shopify.ImportCustomerWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Integrations

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    customer_sync = insert(:customer_sync, organization: organization)

    insert(:shopify_credential, organization: organization)
    %{items: [customer]} = Rms.ShopifyMock.fetch_customers_data()

    {:ok, customer_sync: customer_sync, customer: customer}
  end

  describe "import customers" do
    test "creates a new customer", %{customer_sync: customer_sync, customer: customer} do
      assert {:ok, %{status: "success"}} = run(customer_sync, customer)
    end

    test "updates the customer found by customer sync mapping id",
         %{customer_sync: customer_sync, customer: customer} do
      customer_db = insert(:customer, organization: customer_sync.organization)

      insert(:customer_sync_mapping,
        external_id: customer["id"],
        source: "shopify",
        organization: customer_sync.organization,
        customer: customer_db
      )

      assert {:ok, [%{status: "success"}]} = run(customer_sync, customer)
    end

    test "updates the customer found by email and document",
         %{customer_sync: customer_sync, customer: customer} do
      customer_db =
        insert(:customer,
          email: customer["email"],
          email_hash: customer["email"],
          document: customer["defaultAddress"]["company"],
          document_type: "cpf",
          organization: customer_sync.organization
        )

      insert(:customer_sync_mapping,
        external_id: customer["id"],
        source: "shopify",
        organization: customer_sync.organization,
        customer: customer_db
      )

      assert {:ok, [%{status: "success"}]} = run(customer_sync, customer)
    end

    test "imports customer without defaultAddress", %{customer_sync: customer_sync} do
      customer = %{
        "defaultAddress" => nil,
        "email" => "<EMAIL>",
        "firstName" => nil,
        "id" => "gid://shopify/Customer/6212619829413",
        "lastName" => nil,
        "phone" => nil
      }

      assert {:ok, %{status: "success"}} = run(customer_sync, customer)

      assert [imported_customer] = Rms.Repo.all(Rms.Customers.Customer)
      imported_customer = Rms.Repo.preload(imported_customer, [:addresses])

      assert imported_customer.email == customer["email"]
      assert imported_customer.name == nil
      assert imported_customer.primary_phone_number == nil
      assert Enum.empty?(imported_customer.addresses)
    end

    test "imports customer with defaultAddress but without email and does not duplicate on reimport",
         %{customer_sync: customer_sync} do
      customer = %{
        "defaultAddress" => %{
          "address1" => "",
          "address2" => "",
          "city" => "São Paulo",
          "company" => "12345678909",
          "country" => "Brazil",
          "name" => "Alberto Rodriguez",
          "provinceCode" => "SP",
          "zip" => ""
        },
        "email" => nil,
        "firstName" => "Alberto",
        "id" => "gid://shopify/Customer/7473898881189",
        "lastName" => "Rodriguez",
        "phone" => "+13059512688"
      }

      assert {:ok, %{status: "success"}} = run(customer_sync, customer)

      assert [imported_customer] = Rms.Repo.all(Rms.Customers.Customer)
      imported_customer = Rms.Repo.preload(imported_customer, [:addresses])

      assert imported_customer.email == nil
      assert imported_customer.name == "Alberto Rodriguez"
      assert imported_customer.primary_phone_number == "+13059512688"
      assert imported_customer.document == "12345678909"
      assert imported_customer.document_type == "CPF"
      assert Enum.empty?(imported_customer.addresses)

      # Attempt to import the same customer again
      assert {:ok, [%{status: "success"}]} = run(customer_sync, customer)

      # Check that no duplicate was created
      assert [updated_customer] = Rms.Repo.all(Rms.Customers.Customer)
      assert updated_customer.id == imported_customer.id
    end
  end

  defp run(customer_sync, customer) do
    attrs = %{
      "id" => customer_sync.id,
      "organization_id" => customer_sync.organization.id,
      "customer" => customer
    }

    perform_job(Integrations.Shopify.ImportCustomerWorker, attrs)
  end
end
