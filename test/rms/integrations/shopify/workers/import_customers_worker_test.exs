defmodule Rms.Integrations.Shopify.ImportCustomersWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Integrations.Shopify

  setup :verify_on_exit!

  test "perform/1 enqueues the correct jobs" do
    organization = insert(:organization)
    customer_sync = insert(:customer_sync, organization: organization)

    insert(:shopify_credential, organization: organization)
    %{items: [item], cursor: cursor} = data = Rms.ShopifyMock.fetch_customers_data("foobarbaz")
    Rms.ShopifyMock.fetch_customers_mock(data)

    assert {:ok, %{next_page: _job, customer_import_jobs: _}} =
             perform_job(Shopify.ImportCustomersWorker, %{
               "customer_sync_id" => customer_sync.id,
               "organization_id" => organization.id
             })

    assert_enqueued(
      worker: Shopify.ImportCustomersWorker,
      args: %{
        "customer_sync_id" => customer_sync.id,
        "organization_id" => organization.id,
        "cursor" => cursor
      }
    )

    assert_enqueued(
      worker: Shopify.ImportCustomerWorker,
      args: %{
        "id" => customer_sync.id,
        "organization_id" => organization.id,
        "customer" => item
      }
    )

    Mox.verify!()

    Rms.ShopifyMock.fetch_customers_mock(%{data | cursor: nil})

    assert {:ok, result} =
             perform_job(Shopify.ImportCustomersWorker, %{
               "customer_sync_id" => customer_sync.id,
               "organization_id" => organization.id
             })

    refute is_map_key(result, :next_page)
  end
end
