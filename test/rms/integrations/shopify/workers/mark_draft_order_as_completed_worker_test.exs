defmodule Rms.Integrations.Shopify.MarkDraftOrderAsCompletedWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify.MarkDraftOrderAsCompletedWorker

  import Mox
  setup :verify_on_exit!

  describe "process/1" do
    test "successfully completes the fulfillment order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      transaction = insert(:transaction, status: "done", order: order, organization: org)

      insert(:payment,
        amount: transaction.order.total_price,
        transaction: transaction,
        method: "cash",
        status: "settled",
        organization: org
      )

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123"
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Rms.Integrations.Shopify.Mock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:fetch_draft_order, fn :mock_client, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true, "status" => "OPEN"}}
      end)
      |> expect(:mark_order_as_completed, fn :mock_client, _ ->
        {:ok,
         %{
           "id" => "gid://shopify/DraftOrder/123",
           "ready" => true,
           "status" => "COMPLETED",
           "order" => %{"id" => "gid://shopify/Order/123"}
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :update_draft_order, 1, fn _, _, _ ->
        {:ok,
         %{
           "draftOrderde" => %{
             "id" => "gid://shopify/DraftOrder/6093936558390"
           },
           "userErrors" => []
         }}
      end)

      assert {:ok, _} =
               perform_job(MarkDraftOrderAsCompletedWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })
    end

    test "process/1 successfully completes the fulfillment order for a delivery order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      transaction = insert(:transaction, status: "done", order: order, organization: org)

      insert(:payment,
        amount: transaction.order.total_price,
        transaction: transaction,
        method: "cash",
        status: "settled",
        organization: org
      )

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123"
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Rms.Integrations.Shopify.Mock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:fetch_draft_order, fn :mock_client, _ ->
        {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true, "status" => "OPEN"}}
      end)
      |> expect(:mark_order_as_completed, fn :mock_client, _ ->
        {:ok,
         %{
           "id" => "gid://shopify/DraftOrder/123",
           "ready" => true,
           "status" => "COMPLETED",
           "order" => %{"id" => "gid://shopify/Order/123"}
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :update_draft_order, 1, fn _, _, _ ->
        {:ok,
         %{
           "draftOrderde" => %{
             "id" => "gid://shopify/DraftOrder/6093936558390"
           },
           "userErrors" => []
         }}
      end)

      assert {:ok, _} =
               perform_job(MarkDraftOrderAsCompletedWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })
    end

    test "process/1 skips mark_order_as_completed when draft order is already paid" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      transaction = insert(:transaction, status: "done", order: order, organization: org)

      insert(:payment,
        amount: transaction.order.total_price,
        transaction: transaction,
        method: "cash",
        status: "settled",
        organization: org
      )

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123"
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      Rms.Integrations.Shopify.Mock
      |> expect(:client, fn _, _ -> :mock_client end)
      |> expect(:fetch_draft_order, fn :mock_client, _ ->
        {:ok,
         %{
           "id" => "gid://shopify/DraftOrder/123",
           "ready" => true,
           "status" => "COMPLETED",
           "order" => %{
             "id" => "gid://shopify/Order/123",
             "name" => "#1780980",
             "fulfillmentOrders" => %{
               "nodes" => [
                 %{
                   "id" => "gid://shopify/FulfillmentOrder/6681321504815",
                   "lineItems" => %{
                     "nodes" => [
                       %{
                         "id" => "gid://shopify/FulfillmentOrderLineItem/14121384804399",
                         "totalQuantity" => 1
                       },
                       %{
                         "id" => "gid://shopify/FulfillmentOrderLineItem/14121384837167",
                         "totalQuantity" => 1
                       },
                       %{
                         "id" => "gid://shopify/FulfillmentOrderLineItem/14121384869935",
                         "totalQuantity" => 1
                       }
                     ]
                   }
                 }
               ]
             }
           }
         }}
      end)

      assert {:ok, _} =
               perform_job(MarkDraftOrderAsCompletedWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })

      # get the draft_order directly to verify it through the pipeline
      fulfillment = Repo.get(Rms.Commerce.Fulfillments.Fulfillment, fulfillment.id)
      assert fulfillment.status == "completed"

      assert %{
               "order_name" => "#1780980",
               "order_id" => "gid://shopify/Order/123",
               "draft_order_id" => "gid://shopify/DraftOrder/123"
             } =
               fulfillment.metadata

      assert fulfillment.external_reference == "gid://shopify/Order/123"
    end
  end
end
