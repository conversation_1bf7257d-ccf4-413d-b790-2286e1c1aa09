defmodule Rms.Integrations.Shopify.MoveOrderWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify.MoveOrderWorker
  alias Rms.Integrations.Shopify.FulfillOrderWorker

  import Mox
  setup :verify_on_exit!

  describe "process/1" do
    test "successfully moves and fulfill a order" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :move_fulfillment, 1, fn _, _, _ ->
        {:ok,
         %{
           "movedFulfillmentOrder" => %{
             "channelId" => nil,
             "createdAt" => "2024-08-26T14:51:23Z",
             "fulfillAt" => "2024-08-26T14:00:00Z",
             "fulfillBy" => nil,
             "id" => "gid://shopify/FulfillmentOrder/7044188373302",
             "orderId" => "gid://shopify/Order/6093936558390",
             "orderName" => "#2466",
             "orderProcessedAt" => "2024-08-26T14:51:21Z",
             "requestStatus" => "UNSUBMITTED",
             "status" => "OPEN",
             "updatedAt" => "2024-08-26T14:51:23Z"
           },
           "userErrors" => []
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "order" => %{
               "fulfillmentOrders" => %{
                 "nodes" => [
                   %{
                     "id" => "gid://shopify/FulfillmentOrder/7325133734183",
                     "fulfillAt" => "2025-01-09T16:00:00Z",
                     "status" => "OPEN",
                     "assignedLocation" => %{
                       "location" => %{
                         "id" => "gid://shopify/Location/1"
                       }
                     }
                   },
                   %{
                     "id" => "gid://shopify/FulfillmentOrder/7325143630119",
                     "fulfillAt" => "2025-01-09T16:00:00Z",
                     "status" => "OPEN",
                     "assignedLocation" => %{
                       "location" => %{
                         "id" => "gid://shopify/Location/2"
                       }
                     }
                   }
                 ]
               }
             }
           }
         }}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer, location: loc)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: loc,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      assert {:ok, "order fulfilled"} =
               perform_job(MoveOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: %{
                   "nodes" => [
                     %{
                       "id" => "gid://shopify/FulfillmentOrder/1",
                       "lineItems" => %{
                         "nodes" => [
                           %{
                             "id" => "gid://shopify/FulfillmentOrderLineItem/2"
                           }
                         ]
                       }
                     }
                   ]
                 }
               })
    end

    test "successfully moves and fulfill a order even if alredy in correct location" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :move_fulfillment, 1, fn _, _, _ ->
        {:error,
         {:unknown_error_code,
          %{"field" => nil, "message" => "Cannot move to the current origin location."}}}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_order!, 1, fn _, _, _, _ ->
        {:ok,
         %{
           "data" => %{
             "order" => %{
               "fulfillmentOrders" => %{
                 "nodes" => [
                   %{
                     "id" => "gid://shopify/FulfillmentOrder/7325133734183",
                     "fulfillAt" => "2025-01-09T16:00:00Z",
                     "status" => "OPEN",
                     "assignedLocation" => %{
                       "location" => %{
                         "id" => "gid://shopify/Location/1"
                       }
                     }
                   },
                   %{
                     "id" => "gid://shopify/FulfillmentOrder/7325143630119",
                     "fulfillAt" => "2025-01-09T16:00:00Z",
                     "status" => "OPEN",
                     "assignedLocation" => %{
                       "location" => %{
                         "id" => "gid://shopify/Location/2"
                       }
                     }
                   }
                 ]
               }
             }
           }
         }}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer, location: loc)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: loc,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org
        )

      assert {:ok, "order fulfilled"} =
               perform_job(MoveOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: %{
                   "nodes" => [
                     %{
                       "id" => "gid://shopify/FulfillmentOrder/1",
                       "lineItems" => %{
                         "nodes" => [
                           %{
                             "id" => "gid://shopify/FulfillmentOrderLineItem/2"
                           }
                         ]
                       }
                     }
                   ]
                 }
               })

      assert_enqueued(
        worker: MoveOrderWorker,
        args: %{
          "id" => fulfillment.id,
          "organization_id" => org.id,
          "fulfillment_order_id" => "gid://shopify/FulfillmentOrder/7325133734183"
        }
      )

      assert_enqueued(
        worker: FulfillOrderWorker,
        args: %{
          "id" => fulfillment.id,
          "organization_id" => org.id,
          "fulfillment_order_id" => "gid://shopify/FulfillmentOrder/7325143630119"
        }
      )
    end

    test "ignore fulfillments other than in-store" do
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :move_fulfillment, 0, fn _, _, _ ->
        {:ok,
         %{
           "movedFulfillmentOrder" => %{
             "channelId" => nil,
             "createdAt" => "2024-08-26T14:51:23Z",
             "fulfillAt" => "2024-08-26T14:00:00Z",
             "fulfillBy" => nil,
             "id" => "gid://shopify/FulfillmentOrder/7044188373302",
             "orderId" => "gid://shopify/Order/6093936558390",
             "orderName" => "#2466",
             "orderProcessedAt" => "2024-08-26T14:51:21Z",
             "requestStatus" => "UNSUBMITTED",
             "status" => "OPEN",
             "updatedAt" => "2024-08-26T14:51:23Z"
           },
           "userErrors" => []
         }}
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fulfill_order, 0, fn _, _ ->
        {:ok,
         %{
           "fulfillment" => %{
             "createdAt" => "2024-08-26T14:37:57Z",
             "deliveredAt" => nil,
             "displayStatus" => "FULFILLED",
             "estimatedDeliveryAt" => nil,
             "id" => "gid://shopify/Fulfillment/1",
             "inTransitAt" => nil,
             "legacyResourceId" => "1",
             "name" => "#2465-F1",
             "requiresShipping" => true,
             "status" => "SUCCESS",
             "totalQuantity" => 2,
             "updatedAt" => "2024-08-26T14:37:57Z"
           },
           "userErrors" => []
         }}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer, location: loc)

      insert(:location_mapping,
        source: "shopify",
        organization: org,
        location: loc,
        external_id: "gid://shopify/Location/2"
      )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "delivery",
          order: order,
          organization: org
        )

      assert {:discard, "Do not fulfill delivery orders"} =
               perform_job(MoveOrderWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id,
                 result: %{
                   "nodes" => [
                     %{
                       "id" => "gid://shopify/FulfillmentOrder/1",
                       "lineItems" => %{
                         "nodes" => [
                           %{
                             "id" => "gid://shopify/FulfillmentOrderLineItem/2"
                           }
                         ]
                       }
                     }
                   ]
                 }
               })
    end
  end
end
