defmodule Rms.Integrations.Shopify.UpdateOrderNameWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.Shopify.UpdateOrderNameWorker

  describe "process/1" do
    test "updates order name" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123",
          metadata: %{order_name: "order_name_1"}
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      assert {:ok, _} =
               perform_job(UpdateOrderNameWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })

      _updated_order = Rms.Commerce.Orders.get_order!(order.organization_id, order.id)
    end

    test "updates order name with 2 fulfillments" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123",
          metadata: %{order_name: "order_name_1"}
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      another_fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/124",
          metadata: %{order_name: "order_name_2"}
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: another_fulfillment
        )

      assert {:ok, _} =
               perform_job(UpdateOrderNameWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })

      updated_order = Rms.Commerce.Orders.get_order!(order.organization_id, order.id)
      assert updated_order.name == "order_name_1"

      assert {:ok, _} =
               perform_job(UpdateOrderNameWorker, %{
                 id: another_fulfillment.id,
                 organization_id: org.id
               })

      updated_order = Rms.Commerce.Orders.get_order!(order.organization_id, order.id)
      assert updated_order.name == "order_name_1, order_name_2"
    end

    test "returns a error there is no order name" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/123",
          metadata: %{order_name: "order_name_1"}
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      another_fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          organization: org,
          external_reference: "gid://shopify/DraftOrder/124",
          metadata: %{order_name: nil}
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: another_fulfillment
        )

      assert {:ok, _} =
               perform_job(UpdateOrderNameWorker, %{
                 id: fulfillment.id,
                 organization_id: org.id
               })

      updated_order = Rms.Commerce.Orders.get_order!(order.organization_id, order.id)
      assert updated_order.name == "order_name_1"

      assert {:error, _} =
               perform_job(UpdateOrderNameWorker, %{
                 id: another_fulfillment.id,
                 organization_id: org.id
               })

      updated_order = Rms.Commerce.Orders.get_order!(order.organization_id, order.id)
      assert updated_order.name == "order_name_1"
    end
  end
end
