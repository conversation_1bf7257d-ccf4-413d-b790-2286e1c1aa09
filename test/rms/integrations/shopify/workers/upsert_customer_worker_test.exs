defmodule Rms.Integrations.Shopify.UpsertCustomerWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo
  import Mox

  alias Rms.Integrations.Shopify.UpsertCustomerWorker
  alias Rms.Customers.Customer
  alias Rms.Repo

  setup :verify_on_exit!

  @ibge_mock Application.compile_env(:rms, :ibge_client, Rms.Integrations.Ibge.Mock)

  describe "process/1" do
    test "processes customer webhook data successfully" do
      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "Test City",
             uf_name: "Test Province",
             uf_initials: "TP"
           }
         ]}
      end)

      organization = insert(:organization)

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Test",
        "last_name" => "User",
        "addresses" => [
          %{
            "id" => 10_719_097_291_047,
            "address1" => "123 Test St",
            "address2" => "Apt 1",
            "city" => "Test City",
            "province" => "Test Province",
            "country" => "Test Country",
            "zip" => "12345678",
            "name" => "Test User",
            "province_code" => "TP",
            "country_code" => "TC",
            "country_name" => "Test Country",
            "default" => true
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _} =
               perform_job(UpsertCustomerWorker, %{
                 "organization_id" => organization.id,
                 "shopify_event" => "customers/create",
                 "customer_external_id" => "gid://shopify/Customer/8616466579751",
                 "external_customer" => customer_data
               })

      customer = Repo.get_by(Customer, email: "<EMAIL>")
      assert customer != nil
      assert customer.name == "Test User"
    end

    test "updates existing customer" do
      expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "New City",
             uf_name: "New Province",
             uf_initials: "NP"
           }
         ]}
      end)

      organization = insert(:organization)

      customer =
        insert(:customer,
          organization: organization,
          name: "Old Name",
          email: "<EMAIL>",
          updated_at: ~N[2023-01-01 00:00:00]
        )

      insert(:customer_sync_mapping,
        organization: organization,
        customer: customer,
        source: "shopify",
        external_id: "gid://shopify/Customer/8616466579751"
      )

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "New",
        "last_name" => "Name",
        "updated_at" => "2025-01-01T00:00:00Z",
        "addresses" => [
          %{
            "id" => 10_719_097_291_047,
            "address1" => "456 New St",
            "address2" => "Apt 2",
            "city" => "New City",
            "province" => "New Province",
            "country" => "Test Country",
            "zip" => "54321234",
            "name" => "New Name",
            "province_code" => "NP",
            "country_code" => "TC",
            "country_name" => "Test Country",
            "default" => true
          }
        ],
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _} =
               perform_job(UpsertCustomerWorker, %{
                 "organization_id" => organization.id,
                 "shopify_event" => "customers/update",
                 "customer_external_id" => "gid://shopify/Customer/8616466579751",
                 "external_customer" => customer_data
               })

      updated_customer = Repo.get(Customer, customer.id)
      assert updated_customer.name == "New Name"
    end

    test "doesn't update customer with older data" do
      organization = insert(:organization)

      customer =
        insert(:customer,
          organization: organization,
          name: "Current Name",
          email: "<EMAIL>",
          updated_at: ~N[2025-01-02 00:00:00]
        )

      insert(:customer_sync_mapping,
        organization: organization,
        customer: customer,
        source: "shopify",
        external_id: "gid://shopify/Customer/8616466579751"
      )

      customer_data = %{
        "id" => 8_616_466_579_751,
        "email" => "<EMAIL>",
        "first_name" => "Old",
        "last_name" => "Name",
        "updated_at" => "2024-04-03T14:25:07-03:00",
        "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
      }

      assert {:ok, _} =
               perform_job(UpsertCustomerWorker, %{
                 "organization_id" => organization.id,
                 "shopify_event" => "customers/update",
                 "customer_external_id" => "gid://shopify/Customer/8616466579751",
                 "external_customer" => customer_data
               })

      unchanged_customer = Repo.get(Customer, customer.id)
      assert unchanged_customer.name == "Current Name"
    end
  end
end
