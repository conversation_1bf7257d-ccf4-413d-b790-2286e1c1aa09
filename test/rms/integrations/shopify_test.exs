defmodule Rms.Integrations.ShopifyTest do
  use ExUnit.Case

  alias Rms.Integrations.Shopify

  test "get_order!/2 returns ok tuple" do
    Rms.ShopifyMock.order_mock()

    client = Shopify.client("salva-01", "any token")
    assert {:ok, response} = Shopify.get_order!(client, "gid://shopify/Order/random_id")
    assert get_in(response, ["data", "order", "name"]) == "#1001"
  end

  test "create_customer/2 returns ok tuple" do
    Rms.ShopifyMock.customer_mock()
    client = Shopify.client("salva-01", "any token")

    input = %{
      email: "<EMAIL>",
      addresses: [
        %{
          zip: "69727",
          phone: "(96) 4063-4322",
          firstName: "Sheila",
          lastName: "Smitham",
          address1: "822 Johns Locks",
          address2: "Suite 367",
          city: "Kasey",
          provinceCode: "MA",
          countryCode: "HT"
        }
      ],
      phone: "(83) 4506-4694",
      firstName: "Summer",
      lastName: "Kertzmann"
    }

    assert Shopify.create_customer(client, input) ==
             {:ok, %{"id" => "gid://shopify/Customer/123"}}
  end

  test "create_draft_order/2 returns ok tuple" do
    Rms.ShopifyMock.draft_order_mock()
    client = Shopify.client("salva-01", "any token")

    input = %{
      purchasingEntity: %{customerId: "gid://shopify/Customer/123"},
      lineItems: [
        %{quantity: 1, variantId: "gid://shopify/ProductVariant/123"}
      ]
    }

    assert Shopify.create_draft_order(client, input) ==
             {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}}
  end

  test "fetch_draft_order/2 returns ok tuple" do
    Rms.ShopifyMock.fetch_draft_order_mock()
    client = Shopify.client("salva-01", "any token")

    assert Shopify.fetch_draft_order(client, "gid://shopify/DraftOrder/123") ==
             {:ok,
              %{
                "id" => "gid://shopify/DraftOrder/123",
                "ready" => true,
                "status" => "OPEN",
                "invoiceUrl" => "url"
              }}
  end

  test "fetch_location/2 returns ok tuple" do
    Rms.ShopifyMock.fetch_location_mock()
    client = Shopify.client("salva-01", "any token")

    assert Shopify.fetch_location(client, "name:Shopping Eldorado") ==
             {
               :ok,
               %{
                 "nodes" => [
                   %{
                     "address" => %{
                       "address1" => "Avenida Rebouças 3970",
                       "address2" => "",
                       "city" => "São Paulo",
                       "country" => "Brazil",
                       "countryCode" => "BR",
                       "formatted" => [
                         "Avenida Rebouças 3970",
                         "São Paulo SP",
                         "05402-600",
                         "Brazil"
                       ],
                       "latitude" => -23.571601,
                       "longitude" => -46.69621069999999,
                       "phone" => "+5511123456789",
                       "province" => "São Paulo",
                       "provinceCode" => "SP",
                       "zip" => "05402-600"
                     },
                     "id" => "gid://shopify/Location/1234567890",
                     "isActive" => true,
                     "name" => "Shopping Eldorado"
                   }
                 ]
               }
             }
  end

  test "mark_order_as_completed/2 returns ok tuple" do
    Rms.ShopifyMock.mark_order_as_completed_mock()
    client = Shopify.client("salva-01", "any token")

    assert Shopify.mark_order_as_completed(client, "gid://shopify/DraftOrder/123") ==
             {:ok,
              %{
                "id" => "gid://shopify/DraftOrder/123",
                "ready" => true,
                "order" => %{"id" => "gid://shopify/Order/123"},
                "status" => "COMPLETED"
              }}
  end

  describe "simulate_cart/2" do
    test "returns a correct response" do
      Mox.expect(Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         """
         \r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 361\r\n\r\n{\"data\":{\"cartCreate\":{\"cart\":{\"checkoutUrl\":\"https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\",\"createdAt\":\"2024-07-18T18:49:56Z\",\"id\":\"gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb\"},\"userErrors\":[]}},\"hasNext\":true}\r\n\r\n--graphql\r\nContent-Type: application/json\r\nContent-Length: 780\r\n\r\n{\"incremental\":[{\"path\":[\"cartCreate\",\"cart\"],\"data\":{\"deliveryGroups\":{\"edges\":[{\"node\":{\"deliveryOptions\":[{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\"}},{\"title\":\"Bicicleta\",\"handle\":\"3a3f14ec37c4797d9e7a7352324c2a18\",\"estimatedCost\":{\"amount\":\"25.0\"}},{\"title\":\"Moto\",\"handle\":\"f20de5611356f6f568bdb9387afa955d\",\"estimatedCost\":{\"amount\":\"50.0\"}},{\"title\":\"Carro\",\"handle\":\"fd2120274e392769c872754fb2eac8f2\",\"estimatedCost\":{\"amount\":\"100.0\"}},{\"title\":\"Caminhão\",\"handle\":\"81d69ad7e95d44f50319e91517cd16fb\",\"estimatedCost\":{\"amount\":\"200.0\"}}],\"selectedDeliveryOption\":{\"title\":\"Local Delivery\",\"handle\":\"ae5bf93fce163cc44e00cf3e2bd2fd85\",\"estimatedCost\":{\"amount\":\"7.0\",\"currencyCode\":\"BRL\"}}}}]}}}],\"hasNext\":false}\r\n\r\n--graphql--
         """}
      end)

      client = Shopify.storefront_client("salva-01", "any token", organization_id: 1)

      assert Shopify.simulate_cart(client, %{}, 1) ==
               {
                 :ok,
                 %{
                   cart: %{
                     "checkoutUrl" =>
                       "https://iglu-demo.myshopify.com/cart/c/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb",
                     "createdAt" => "2024-07-18T18:49:56Z",
                     "id" =>
                       "gid://shopify/Cart/Z2NwLXVzLWVhc3QxOjAxSjMzSlZXS1o5NkZGVDBCM05TUEIyOEY0?key=cf7cf3a417efe52e1b635cf65d595fdb"
                   },
                   delivery: %{
                     "deliveryOptions" => [
                       %{
                         "estimatedCost" => %{"amount" => "7.0"},
                         "handle" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                         "title" => "Local Delivery"
                       },
                       %{
                         "estimatedCost" => %{"amount" => "25.0"},
                         "handle" => "3a3f14ec37c4797d9e7a7352324c2a18",
                         "title" => "Bicicleta"
                       },
                       %{
                         "estimatedCost" => %{"amount" => "50.0"},
                         "handle" => "f20de5611356f6f568bdb9387afa955d",
                         "title" => "Moto"
                       },
                       %{
                         "estimatedCost" => %{"amount" => "100.0"},
                         "handle" => "fd2120274e392769c872754fb2eac8f2",
                         "title" => "Carro"
                       },
                       %{
                         "estimatedCost" => %{"amount" => "200.0"},
                         "handle" => "81d69ad7e95d44f50319e91517cd16fb",
                         "title" => "Caminhão"
                       }
                     ],
                     "selectedDeliveryOption" => %{
                       "estimatedCost" => %{"amount" => "7.0", "currencyCode" => "BRL"},
                       "handle" => "ae5bf93fce163cc44e00cf3e2bd2fd85",
                       "title" => "Local Delivery"
                     }
                   },
                   message: nil
                 }
               }
    end

    test "returns a cartCreat error" do
      Mox.expect(Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, fn _, _ ->
        {:ok,
         %{
           "data" => %{
             "cartCreate" => %{
               "cart" => nil,
               "userErrors" => [
                 %{
                   "field" => [
                     "input",
                     "buyerIdentity",
                     "deliveryAddressPreferences",
                     "0",
                     "deliveryAddress",
                     "country"
                   ],
                   "message" => "invalid value"
                 }
               ]
             }
           }
         }}
      end)

      client = Shopify.storefront_client("salva-01", "any token", organization_id: 1)

      assert Shopify.simulate_cart(client, %{}, 1) ==
               {
                 :error,
                 {
                   :unknown_error_code,
                   %{
                     "field" => [
                       "input",
                       "buyerIdentity",
                       "deliveryAddressPreferences",
                       "0",
                       "deliveryAddress",
                       "country"
                     ],
                     "message" => "invalid value"
                   },
                   %{
                     "cart" => nil,
                     "userErrors" => [
                       %{
                         "field" => [
                           "input",
                           "buyerIdentity",
                           "deliveryAddressPreferences",
                           "0",
                           "deliveryAddress",
                           "country"
                         ],
                         "message" => "invalid value"
                       }
                     ]
                   }
                 }
               }
    end

    test "returns a general error" do
      Mox.expect(Shopify.Mock, :storefront_client, fn _, _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :simulate_cart, fn _, _ ->
        {:error,
         {:unknown_error_code,
          %{
            "extensions" => %{
              "problems" => [
                %{
                  "explanation" =>
                    "Expected \"BRA\" to be one of: AF, AX, AL, DZ, AD, AO, AI, AG, AR, AM, AW, AC, AU, AT, AZ, BS, BH, BD, BB, BY, BE, BZ, BJ, BM, BT, BO, BA, BW, BV, BR, IO, BN, BG, BF, BI, KH, CA, CV, BQ, KY, CF, TD, CL, CN, CX, CC, CO, KM, CG, CD, CK, CR, HR, CU, CW, CY, CZ, CI, DK, DJ, DM, DO, EC, EG, SV, GQ, ER, EE, SZ, ET, FK, FO, FJ, FI, FR, GF, PF, TF, GA, GM, GE, DE, GH, GI, GR, GL, GD, GP, GT, GG, GN, GW, GY, HT, HM, VA, HN, HK, HU, IS, IN, ID, IR, IQ, IE, IM, IL, IT, JM, JP, JE, JO, KZ, KE, KI, KP, XK, KW, KG, LA, LV, LB, LS, LR, LY, LI, LT, LU, MO, MG, MW, MY, MV, ML, MT, MQ, MR, MU, YT, MX, MD, MC, MN, ME, MS, MA, MZ, MM, NA, NR, NP, NL, AN, NC, NZ, NI, NE, NG, NU, NF, MK, NO, OM, PK, PS, PA, PG, PY, PE, PH, PN, PL, PT, QA, CM, RE, RO, RU, RW, BL, SH, KN, LC, MF, PM, WS, SM, ST, SA, SN, RS, SC, SL, SG, SX, SK, SI, SB, SO, ZA, GS, KR, SS, ES, LK, VC, SD, SR, SJ, SE, CH, SY, TW, TJ, TZ, TH, TL, TG, TK, TO, TT, TA, TN, TR, TM, TC, TV, UG, UA, AE, GB, US, UM, UY, UZ, VU, VE, VN, VG, WF, EH, YE, ZM, ZW, ZZ",
                  "path" => ["buyerIdentity", "countryCode"]
                }
              ],
              "value" => %{
                "buyerIdentity" => %{
                  "countryCode" => "BRA",
                  "deliveryAddressPreferences" => [
                    %{
                      "deliveryAddress" => %{
                        "address1" => "Rua Duquesa de Tancos",
                        "city" => "São Paulo",
                        "country" => "BRA",
                        "firstName" => "IGLU",
                        "lastName" => "TEST",
                        "province" => "SP",
                        "zip" => "04920140"
                      }
                    }
                  ]
                },
                "lines" => [
                  %{
                    "merchandiseId" => "gid://shopify/ProductVariant/44162076279094",
                    "quantity" => 1
                  }
                ]
              }
            },
            "locations" => [%{"column" => 21, "line" => 1}],
            "message" =>
              "Variable $input of type CartInput! was provided invalid value for buyerIdentity.countryCode (Expected \"BRA\" to be one of: AF, AX, AL, DZ, AD, AO, AI, AG, AR, AM, AW, AC, AU, AT, AZ, BS, BH, BD, BB, BY, BE, BZ, BJ, BM, BT, BO, BA, BW, BV, BR, IO, BN, BG, BF, BI, KH, CA, CV, BQ, KY, CF, TD, CL, CN, CX, CC, CO, KM, CG, CD, CK, CR, HR, CU, CW, CY, CZ, CI, DK, DJ, DM, DO, EC, EG, SV, GQ, ER, EE, SZ, ET, FK, FO, FJ, FI, FR, GF, PF, TF, GA, GM, GE, DE, GH, GI, GR, GL, GD, GP, GT, GG, GN, GW, GY, HT, HM, VA, HN, HK, HU, IS, IN, ID, IR, IQ, IE, IM, IL, IT, JM, JP, JE, JO, KZ, KE, KI, KP, XK, KW, KG, LA, LV, LB, LS, LR, LY, LI, LT, LU, MO, MG, MW, MY, MV, ML, MT, MQ, MR, MU, YT, MX, MD, MC, MN, ME, MS, MA, MZ, MM, NA, NR, NP, NL, AN, NC, NZ, NI, NE, NG, NU, NF, MK, NO, OM, PK, PS, PA, PG, PY, PE, PH, PN, PL, PT, QA, CM, RE, RO, RU, RW, BL, SH, KN, LC, MF, PM, WS, SM, ST, SA, SN, RS, SC, SL, SG, SX, SK, SI, SB, SO, ZA, GS, KR, SS, ES, LK, VC, SD, SR, SJ, SE, CH, SY, TW, TJ, TZ, TH, TL, TG, TK, TO, TT, TA, TN, TR, TM, TC, TV, UG, UA, AE, GB, US, UM, UY, UZ, VU, VE, VN, VG, WF, EH, YE, ZM, ZW, ZZ)"
          }}}
      end)

      client = Shopify.storefront_client("salva-01", "any token", organization_id: 1)

      assert Shopify.simulate_cart(client, %{}, 1) ==
               {
                 :error,
                 {:unknown_error_code,
                  %{
                    "extensions" => %{
                      "problems" => [
                        %{
                          "explanation" =>
                            "Expected \"BRA\" to be one of: AF, AX, AL, DZ, AD, AO, AI, AG, AR, AM, AW, AC, AU, AT, AZ, BS, BH, BD, BB, BY, BE, BZ, BJ, BM, BT, BO, BA, BW, BV, BR, IO, BN, BG, BF, BI, KH, CA, CV, BQ, KY, CF, TD, CL, CN, CX, CC, CO, KM, CG, CD, CK, CR, HR, CU, CW, CY, CZ, CI, DK, DJ, DM, DO, EC, EG, SV, GQ, ER, EE, SZ, ET, FK, FO, FJ, FI, FR, GF, PF, TF, GA, GM, GE, DE, GH, GI, GR, GL, GD, GP, GT, GG, GN, GW, GY, HT, HM, VA, HN, HK, HU, IS, IN, ID, IR, IQ, IE, IM, IL, IT, JM, JP, JE, JO, KZ, KE, KI, KP, XK, KW, KG, LA, LV, LB, LS, LR, LY, LI, LT, LU, MO, MG, MW, MY, MV, ML, MT, MQ, MR, MU, YT, MX, MD, MC, MN, ME, MS, MA, MZ, MM, NA, NR, NP, NL, AN, NC, NZ, NI, NE, NG, NU, NF, MK, NO, OM, PK, PS, PA, PG, PY, PE, PH, PN, PL, PT, QA, CM, RE, RO, RU, RW, BL, SH, KN, LC, MF, PM, WS, SM, ST, SA, SN, RS, SC, SL, SG, SX, SK, SI, SB, SO, ZA, GS, KR, SS, ES, LK, VC, SD, SR, SJ, SE, CH, SY, TW, TJ, TZ, TH, TL, TG, TK, TO, TT, TA, TN, TR, TM, TC, TV, UG, UA, AE, GB, US, UM, UY, UZ, VU, VE, VN, VG, WF, EH, YE, ZM, ZW, ZZ",
                          "path" => ["buyerIdentity", "countryCode"]
                        }
                      ],
                      "value" => %{
                        "buyerIdentity" => %{
                          "countryCode" => "BRA",
                          "deliveryAddressPreferences" => [
                            %{
                              "deliveryAddress" => %{
                                "address1" => "Rua Duquesa de Tancos",
                                "city" => "São Paulo",
                                "country" => "BRA",
                                "firstName" => "IGLU",
                                "lastName" => "TEST",
                                "province" => "SP",
                                "zip" => "04920140"
                              }
                            }
                          ]
                        },
                        "lines" => [
                          %{
                            "merchandiseId" => "gid://shopify/ProductVariant/44162076279094",
                            "quantity" => 1
                          }
                        ]
                      }
                    },
                    "locations" => [%{"column" => 21, "line" => 1}],
                    "message" =>
                      "Variable $input of type CartInput! was provided invalid value for buyerIdentity.countryCode (Expected \"BRA\" to be one of: AF, AX, AL, DZ, AD, AO, AI, AG, AR, AM, AW, AC, AU, AT, AZ, BS, BH, BD, BB, BY, BE, BZ, BJ, BM, BT, BO, BA, BW, BV, BR, IO, BN, BG, BF, BI, KH, CA, CV, BQ, KY, CF, TD, CL, CN, CX, CC, CO, KM, CG, CD, CK, CR, HR, CU, CW, CY, CZ, CI, DK, DJ, DM, DO, EC, EG, SV, GQ, ER, EE, SZ, ET, FK, FO, FJ, FI, FR, GF, PF, TF, GA, GM, GE, DE, GH, GI, GR, GL, GD, GP, GT, GG, GN, GW, GY, HT, HM, VA, HN, HK, HU, IS, IN, ID, IR, IQ, IE, IM, IL, IT, JM, JP, JE, JO, KZ, KE, KI, KP, XK, KW, KG, LA, LV, LB, LS, LR, LY, LI, LT, LU, MO, MG, MW, MY, MV, ML, MT, MQ, MR, MU, YT, MX, MD, MC, MN, ME, MS, MA, MZ, MM, NA, NR, NP, NL, AN, NC, NZ, NI, NE, NG, NU, NF, MK, NO, OM, PK, PS, PA, PG, PY, PE, PH, PN, PL, PT, QA, CM, RE, RO, RU, RW, BL, SH, KN, LC, MF, PM, WS, SM, ST, SA, SN, RS, SC, SL, SG, SX, SK, SI, SB, SO, ZA, GS, KR, SS, ES, LK, VC, SD, SR, SJ, SE, CH, SY, TW, TJ, TZ, TH, TL, TG, TK, TO, TT, TA, TN, TR, TM, TC, TV, UG, UA, AE, GB, US, UM, UY, UZ, VU, VE, VN, VG, WF, EH, YE, ZM, ZW, ZZ)"
                  }}
               }
    end
  end

  test "fetch_product_variants/2 returns ok tuple" do
    Rms.ShopifyMock.fetch_product_variants_mock()
    client = Shopify.storefront_client("salva-01", "any token", organization_id: 1)

    assert Shopify.fetch_product_variants(client, "name:Shopping Eldorado") ==
             {
               :ok,
               %{
                 items: [
                   %{
                     "availableForSale" => true,
                     "displayName" => "Bo - Óculos de Grau - Tortoise Nero",
                     "id" => "gid://shopify/ProductVariant/44161969357110",
                     "inventoryQuantity" => 15,
                     "product" => %{
                       "id" => "gid://shopify/Product/8056750244150",
                       "storefrontId" => "gid://shopify/Product/8056750244150",
                       "title" => "Bo - Óculos de Grau"
                     },
                     "selectedOptions" => [
                       %{"name" => "Color", "value" => "Tortoise Nero"}
                     ],
                     "sku" => "000600277001",
                     "title" => "Tortoise Nero"
                   }
                 ],
                 cursor: "foobarbaz"
               }
             }
  end

  test "fetch_product_store_availability/4 returns ok tuple" do
    Rms.ShopifyMock.fetch_product_store_availability_mock()
    client = Shopify.storefront_client("salva-01", "any token", organization_id: 1)

    assert Shopify.fetch_product_store_availability(client, "123", [
             %{"name" => "Color", "value" => "Preto"}
           ]) ==
             {
               :ok,
               %{
                 items: [
                   %{
                     "available" => true,
                     "location" => %{
                       "address" => %{
                         "address1" => "Avenida Rebouças 3970",
                         "address2" => "",
                         "city" => "São Paulo",
                         "country" => "Brazil",
                         "countryCode" => "BR",
                         "latitude" => -23.571601,
                         "longitude" => -46.6962107,
                         "phone" => "+5511998111994",
                         "province" => "São Paulo",
                         "provinceCode" => "SP",
                         "zip" => "05402-600"
                       },
                       "id" => "gid://shopify/Location/74913710390",
                       "name" => "Shopping Eldorado"
                     },
                     "pickUpTime" => "Usually ready in 24 hours"
                   }
                 ],
                 cursor: "foobarbaz"
               }
             }
  end
end
