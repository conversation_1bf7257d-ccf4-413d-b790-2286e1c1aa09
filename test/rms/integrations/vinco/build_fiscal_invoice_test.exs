defmodule Rms.Integrations.Vinco.BuildFiscalInvoiceTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco

  import Rms.Factory

  describe "execute/1 for sale NF" do
    setup do
      org = insert(:organization)
      {:ok, org: org}
    end

    test "successfully build an NF", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      lt =
        insert(:location_tax,
          location: loc,
          organization: org
        )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, sku: "SKU1234", product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        fulfillment_id: fulfillment.id
      }

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert {:ok, invoice_params} =
               Vinco.BuildFiscalInvoice.execute(fiscal_invoice)

      customer_document = transaction_customer.document

      expected_map = %{
        total: %{
          ICMStot: %{
            vBC: Decimal.new("10.00"),
            vFCP: Decimal.new("0.20"),
            vICMS: Decimal.new("1.80"),
            vPIS: Decimal.new("0.82"),
            vCOFINS: Decimal.new("0.82"),
            vDesc: Decimal.new("0.00"),
            vFrete: Decimal.new("0"),
            vProd: Decimal.new("10.00"),
            vBCST: Decimal.new("0"),
            vFCPST: Decimal.new("0"),
            vFCPSTRet: Decimal.new("0"),
            vICMSDeson: Decimal.new("0"),
            vII: Decimal.new("0"),
            vIPI: Decimal.new("0"),
            vIPIDevol: Decimal.new("0"),
            vNF: Decimal.new("10.00"),
            vOutro: Decimal.new("0"),
            vST: Decimal.new("0"),
            vSeg: Decimal.new("0"),
            vICMSUFDest: Decimal.new("0")
          }
        },
        dest: %{
          email: "<EMAIL>",
          enderDest: %{
            CEP: "1256789",
            UF: "SP",
            nro: "123",
            xBairro: "Pinheiros",
            xCpl: "lado",
            xLgr: "rua",
            xMun: nil,
            xPais: nil,
            cMun: nil
          },
          CPF: customer_document,
          indIEDest: "9",
          xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
        },
        IdKeySistema: "1",
        Sincrono: true,
        Venda: "1",
        ide: %{
          mod: "55",
          serie: 0,
          cMunFG: 3_550_308,
          cUF: 35,
          finNFe: "1",
          idDest: "1",
          indFinal: "1",
          indPres: "1",
          nNF: nil,
          natOp: "Operacao interna",
          procEmi: "0",
          tpAmb: "2",
          tpEmis: "1",
          tpImp: "4",
          tpNF: "1"
        },
        infRespTec: %{
          email: "<EMAIL>",
          CNPJ: "46991783000113",
          fone: "16991080570",
          xContato: "Iglu Tecnologia"
        },
        transp: %{modFrete: "9", vol: [%{qVol: "1"}]},
        emit: %{
          CNPJ: loc.cnpj,
          CRT: lt.crt,
          IE: lt.ie,
          enderEmit: %{
            CEP: loc.address.zip,
            UF: "SP",
            cMun: "3550308",
            nro: "100",
            xBairro: "Pinheiros",
            xLgr: "Cunha Gago",
            xMun: "City"
          },
          xNome: lt.name
        },
        pag: %{detPag: [%{vPag: Decimal.new("10.00"), tPag: "17", card: %{tpIntegra: 2}}]},
        det: [
          %{
            prod: %{
              CEST: "0101000",
              CFOP: "5102",
              NCM: product.ncm,
              cEAN: "SEM GTIN",
              cEANTrib: "SEM GTIN",
              cProd: pv.sku,
              indTot: "1",
              qCom: "1",
              qTrib: "1",
              uCom: "UN",
              uTrib: "UN",
              vDesc: Decimal.new("0.00"),
              vFrete: "0",
              vProd: Decimal.new("10.00"),
              vUnCom: Decimal.new("10.00"),
              vUnTrib: Decimal.new("10.00"),
              xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
            },
            imposto: %{
              COFINS: %{
                vBC: Decimal.new("8.20"),
                CST: "08",
                pCOFINS: Decimal.new("10.00"),
                vCOFINS: Decimal.new("0.82")
              },
              ICMS: %{
                CSTCSOSN: "30",
                modBC: "3",
                orig: "0",
                pFCP: Decimal.new("2.00"),
                pICMS: Decimal.new("18.00"),
                vBC: Decimal.new("10.00"),
                vFCP: Decimal.new("0.20"),
                vICMS: Decimal.new("1.80")
              },
              PIS: %{
                vBC: Decimal.new("8.20"),
                CST: "08",
                pPIS: Decimal.new("10.00"),
                vPIS: Decimal.new("0.82")
              }
            },
            nItem: 1
          }
        ]
      }

      assert expected_map.dest == invoice_params.dest
      assert expected_map.total == invoice_params.total
      assert expected_map.emit == invoice_params.emit
      assert expected_map.pag == invoice_params.pag
      assert expected_map.det == invoice_params.det
    end

    test "recive a error when client is nil", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      insert(:transaction_customer,
        transaction: transaction,
        organization: org,
        email: "<EMAIL>",
        address: %{
          cityName: "são paulo",
          state: "SP",
          countryName: "Brasil",
          neighborhood: "Pinheiros",
          street: "rua",
          number: "123",
          zip: "1256789",
          complement: "lado"
        }
      )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert {:error, "A client for NF is mandatory"} =
               Vinco.BuildFiscalInvoice.execute(fiscal_invoice)
    end

    test "recive a error when client address is nil", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: nil
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert {:error, "address is mandatory"} =
               Vinco.BuildFiscalInvoice.execute(fiscal_invoice)
    end

    test "recive a error when client email is nil", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: nil,
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert {:error, "email is mandatory"} =
               Vinco.BuildFiscalInvoice.execute(fiscal_invoice)
    end
  end

  describe "execute/1 for sale NFC" do
    setup do
      org = insert(:organization)
      {:ok, org: org}
    end

    test "successfully build an NFC with real taxes params", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      lt =
        insert(:location_tax,
          location: loc,
          organization: org
        )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      staff = insert(:staff, organization: org)

      product = insert(:product, organization: org, ncm: "61061000")

      pv =
        insert(:product_variant,
          product: product,
          organization: org,
          sku: "51225320_0029_M"
        )

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff,
          quantity: 1,
          list_price: Decimal.new("89.9"),
          price: Decimal.new("89.9")
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        cfop: "5102",
        uf: "SP",
        origin: "0",
        cst_icms: "00",
        cst_pis: "01",
        cst_cofins: "01",
        fcp_percentage: Decimal.new("0.00"),
        icms_percentage: Decimal.new("0.18"),
        pis_percentage: Decimal.new("0.0165"),
        cofins_percentage: Decimal.new("0.076"),
        organization: org
      )

      product = insert(:product, organization: org, ncm: "62045900")

      pv =
        insert(:product_variant,
          product: product,
          organization: org,
          sku: "50305150_0008_44"
        )

      fulfillment2 =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item2 =
        insert(:line_item,
          fulfillment: fulfillment2,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff,
          quantity: 1,
          list_price: Decimal.new("319.9"),
          price: Decimal.new("319.9")
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        cfop: "5102",
        uf: "SP",
        origin: "0",
        cst_icms: "00",
        cst_pis: "01",
        cst_cofins: "01",
        fcp_percentage: Decimal.new("0.00"),
        icms_percentage: Decimal.new("0.18"),
        pis_percentage: Decimal.new("0.0165"),
        cofins_percentage: Decimal.new("0.076"),
        organization: org
      )

      product = insert(:product, organization: org, ncm: "62064000")

      pv =
        insert(:product_variant,
          product: product,
          organization: org,
          sku: "50501799_0017_M"
        )

      line_item3 =
        insert(:line_item,
          fulfillment: fulfillment2,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff,
          quantity: 1,
          list_price: Decimal.new("239.9"),
          price: Decimal.new("239.9")
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        cfop: "5102",
        uf: "SP",
        origin: "0",
        cst_icms: "00",
        cst_pis: "01",
        cst_cofins: "01",
        fcp_percentage: Decimal.new("0.00"),
        icms_percentage: Decimal.new("0.18"),
        pis_percentage: Decimal.new("0.0165"),
        cofins_percentage: Decimal.new("0.076"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        fulfillment_id: fulfillment.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [
          %{line_item_id: line_item1.id},
          %{line_item_id: line_item2.id},
          %{line_item_id: line_item3.id}
        ]
      }

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      assert {:ok, invoice_params} =
               Vinco.BuildFiscalInvoice.execute(fiscal_invoice)

      customer_document = transaction_customer.document

      expected_map = %{
        total: %{
          ICMStot: %{
            vBC: Decimal.new("649.70"),
            vFCP: Decimal.new("0.00"),
            vICMS: Decimal.new("116.94"),
            vPIS: Decimal.new("8.80"),
            vCOFINS: Decimal.new("40.49"),
            vDesc: Decimal.new("0.00"),
            vFrete: Decimal.new("0"),
            vProd: Decimal.new("649.70"),
            vBCST: Decimal.new("0"),
            vFCPST: Decimal.new("0"),
            vFCPSTRet: Decimal.new("0"),
            vICMSDeson: Decimal.new("0"),
            vII: Decimal.new("0"),
            vIPI: Decimal.new("0"),
            vIPIDevol: Decimal.new("0"),
            vNF: Decimal.new("649.70"),
            vOutro: Decimal.new("0"),
            vST: Decimal.new("0"),
            vSeg: Decimal.new("0"),
            vICMSUFDest: Decimal.new("0")
          }
        },
        dest: %{
          email: "<EMAIL>",
          enderDest: %{
            CEP: "1256789",
            UF: "SP",
            nro: "123",
            xBairro: "Pinheiros",
            xCpl: "lado",
            xLgr: "rua",
            xMun: nil,
            cMun: nil,
            xPais: nil
          },
          CPF: customer_document,
          indIEDest: "9",
          xNome: "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
        },
        IdKeySistema: "1",
        Sincrono: true,
        Venda: "1",
        ide: %{
          mod: "55",
          serie: 0,
          cMunFG: 3_550_308,
          cUF: 35,
          finNFe: "1",
          idDest: "1",
          indFinal: "1",
          indPres: "1",
          nNF: nil,
          natOp: "Operacao interna",
          procEmi: "0",
          tpAmb: "2",
          tpEmis: "1",
          tpImp: "4",
          tpNF: "1"
        },
        infRespTec: %{
          email: "<EMAIL>",
          CNPJ: "46991783000113",
          fone: "16991080570",
          xContato: "Iglu Tecnologia"
        },
        transp: %{modFrete: "9", vol: [%{qVol: "1"}]},
        emit: %{
          CNPJ: loc.cnpj,
          CRT: lt.crt,
          IE: lt.ie,
          enderEmit: %{
            CEP: loc.address.zip,
            UF: "SP",
            cMun: "3550308",
            nro: "100",
            xBairro: "Pinheiros",
            xLgr: "Cunha Gago",
            xMun: "City"
          },
          xNome: lt.name
        },
        pag: %{detPag: [%{vPag: Decimal.new("10.00"), tPag: "17", card: %{tpIntegra: 2}}]},
        det: [
          %{
            prod: %{
              vDesc: Decimal.new("0.00"),
              vFrete: "0",
              vProd: Decimal.new("89.90"),
              CEST: "0101000",
              CFOP: "5102",
              NCM: "61061000",
              cEAN: "SEM GTIN",
              cEANTrib: "SEM GTIN",
              cProd: "512253200029M",
              indTot: "1",
              qCom: "1",
              qTrib: "1",
              uCom: "UN",
              uTrib: "UN",
              vUnCom: Decimal.new("89.90"),
              vUnTrib: Decimal.new("89.90"),
              xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
            },
            imposto: %{
              COFINS: %{
                vBC: Decimal.new("73.72"),
                vCOFINS: Decimal.new("5.60"),
                CST: "01",
                pCOFINS: Decimal.new("7.60")
              },
              ICMS: %{
                vBC: Decimal.new("89.90"),
                vFCP: Decimal.new("0.00"),
                vICMS: Decimal.new("16.18"),
                CSTCSOSN: "00",
                modBC: "3",
                orig: "0",
                pFCP: Decimal.new("0.00"),
                pICMS: Decimal.new("18.00")
              },
              PIS: %{
                vBC: Decimal.new("73.72"),
                vPIS: Decimal.new("1.22"),
                CST: "01",
                pPIS: Decimal.new("1.65")
              }
            },
            nItem: 1
          },
          %{
            prod: %{
              vDesc: Decimal.new("0.00"),
              vFrete: "0",
              vProd: Decimal.new("319.90"),
              CEST: "0101000",
              CFOP: "5102",
              NCM: "62045900",
              cEAN: "SEM GTIN",
              cEANTrib: "SEM GTIN",
              cProd: "50305150000844",
              indTot: "1",
              qCom: "1",
              qTrib: "1",
              uCom: "UN",
              uTrib: "UN",
              vUnCom: Decimal.new("319.90"),
              vUnTrib: Decimal.new("319.90"),
              xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
            },
            imposto: %{
              COFINS: %{
                vBC: Decimal.new("262.32"),
                vCOFINS: Decimal.new("19.94"),
                CST: "01",
                pCOFINS: Decimal.new("7.60")
              },
              ICMS: %{
                vBC: Decimal.new("319.90"),
                vFCP: Decimal.new("0.00"),
                vICMS: Decimal.new("57.58"),
                CSTCSOSN: "00",
                modBC: "3",
                orig: "0",
                pFCP: Decimal.new("0.00"),
                pICMS: Decimal.new("18.00")
              },
              PIS: %{
                vBC: Decimal.new("262.32"),
                vPIS: Decimal.new("4.33"),
                CST: "01",
                pPIS: Decimal.new("1.65")
              }
            },
            nItem: 2
          },
          %{
            imposto: %{
              COFINS: %{
                CST: "01",
                pCOFINS: Decimal.new("7.60"),
                vBC: Decimal.new("196.72"),
                vCOFINS: Decimal.new("14.95")
              },
              ICMS: %{
                CSTCSOSN: "00",
                modBC: "3",
                orig: "0",
                pFCP: Decimal.new("0.00"),
                pICMS: Decimal.new("18.00"),
                vBC: Decimal.new("239.90"),
                vFCP: Decimal.new("0.00"),
                vICMS: Decimal.new("43.18")
              },
              PIS: %{
                CST: "01",
                pPIS: Decimal.new("1.65"),
                vBC: Decimal.new("196.72"),
                vPIS: Decimal.new("3.25")
              }
            },
            nItem: 3,
            prod: %{
              CEST: "0101000",
              CFOP: "5102",
              NCM: "62064000",
              cEAN: "SEM GTIN",
              cEANTrib: "SEM GTIN",
              cProd: "505017990017M",
              indTot: "1",
              qCom: "1",
              qTrib: "1",
              uCom: "UN",
              uTrib: "UN",
              vDesc: Decimal.new("0.00"),
              vFrete: "0",
              vProd: Decimal.new("239.90"),
              vUnCom: Decimal.new("239.90"),
              vUnTrib: Decimal.new("239.90"),
              xProd: "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
            }
          }
        ]
      }

      assert expected_map.dest == invoice_params.dest
      assert expected_map.total == invoice_params.total
      assert expected_map.emit == invoice_params.emit
      assert expected_map.pag == invoice_params.pag
      assert expected_map.det == invoice_params.det
    end
  end
end
