defmodule Rms.Integrations.Vinco.CancelFiscalInvoiceTest do
  alias Rms.Fiscal.FiscalInvoice
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco

  import Mox

  import Rms.Factory

  setup :verify_on_exit!

  setup do
    Mox.stub_with(VincoClientMock, VincoClientStub)

    :ok
  end

  describe "execute/1" do
    test "successfully cancel an NF" do
      expect(VincoClientMock, :cancel_fiscal_invoice, 1, fn _client, _, _ ->
        {:ok, nf_response_builder(:ok)}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        status: "authorized",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [:staff, product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:organization, location: [:address]]
        ])

      assert {:ok, %FiscalInvoice{}} =
               Vinco.CancelFiscalInvoice.execute(fiscal_invoice)
    end

    test "cancel an NF with error status" do
      expect(VincoClientMock, :cancel_fiscal_invoice, 1, fn _client, _, _ ->
        {:ok, nf_response_builder(:soft_error)}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        status: "authorized",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [:staff, product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:organization, location: [:address]]
        ])

      assert {:error, _} =
               Vinco.CancelFiscalInvoice.execute(fiscal_invoice)
    end

    test "cancel an NF with error" do
      expect(VincoClientMock, :cancel_fiscal_invoice, 1, fn _client, _, _ ->
        {:error, nf_response_builder(:error)}
      end)

      org = insert(:organization)
      loc = insert(:location, organization: org)

      serie =
        insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      insert(:location_tax,
        location: loc,
        organization: org
      )

      insert(:vinco_credential,
        location: loc,
        organization: org
      )

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            cityName: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      staff = insert(:staff, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          staff: staff
        )

      attrs = %{
        operation_type: "sale",
        status: "authorized",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [:staff, product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:organization, location: [:address]]
        ])

      assert {:error, _error} =
               Vinco.CancelFiscalInvoice.execute(fiscal_invoice)
    end
  end

  def nf_response_builder(:ok) do
    %{
      "IdEvento" => 115,
      "CodStatus" => 1,
      "Motivo" => "Evento registrado e vinculado a NF-e",
      "ChaveEvento" => 1111,
      "ProtocoloEvento" => 12_344,
      "XmlEvento" => "_xml"
    }
  end

  def nf_response_builder(:soft_error) do
    %{
      "IdEvento" => 115,
      "CodStatus" => -1,
      "Motivo" => "Evento registrado e vinculado a NF-e",
      "ChaveEvento" => 1111,
      "ProtocoloEvento" => 12_344,
      "XmlEvento" => "_xml"
    }
  end

  def nf_response_builder(:error) do
    %{
      IdEvento: 0,
      CodStatus: -215,
      Motivo: "Comprimento da chave diferente de 44. [43]."
    }
  end
end
