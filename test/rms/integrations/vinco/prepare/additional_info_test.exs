defmodule Rms.Integrations.Vinco.Prepare.AdditionalInfoTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco.Prepare.AdditionalInfo

  import Rms.Factory

  describe "execute/3" do
    test "concatenates order info with existing infCpl" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: "Special Order #123")
      staff = insert(:staff, name: "<PERSON>", organization: org)
      fulfillment = insert(:fulfillment, order: %{order | staff: staff}, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      # Test with existing infCpl
      vinco_invoice = %{infAdic: %{infCpl: "Existing Info"}}

      {:ok, result} = AdditionalInfo.execute(vinco_invoice, serie, invoice_items)

      assert get_in(result, [:infAdic, :infCpl]) ==
               "Existing Info | iGlu ID: #{order.id} | Shopify Name: Special Order #123 | Vendedor: John Seller"

      # Test without existing infCpl
      vinco_invoice = %{emit: %{enderEmit: %{UF: "SP"}}}
      {:ok, result} = AdditionalInfo.execute(vinco_invoice, serie, invoice_items)

      assert get_in(result, [:infAdic, :infCpl]) ==
               "iGlu ID: #{order.id} | Shopify Name: Special Order #123 | Vendedor: John Seller"
    end

    test "uses shopify name when organization setting specifies shopify as ecommerce" do
      org = insert(:organization)
      loc = insert(:location, organization: org, name: "Store A")
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: "Shopify Order #123")
      staff = insert(:staff, name: "John Seller", organization: org)

      # Insert organization setting for shopify
      insert(:organization_setting,
        key: "organization_order_integration_services",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment = insert(:fulfillment, order: %{order | staff: staff}, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      vinco_invoice = %{}
      {:ok, result} = AdditionalInfo.execute(vinco_invoice, serie, invoice_items)

      assert get_in(result, [:infAdic, :infCpl]) =~
               "iGlu ID: #{order.id} | Shopify Name: Shopify Order #123"

      assert get_in(result, [:infAdic, :infCpl]) =~
               "Vendedor: John Seller"
    end
  end

  describe "prepare_additional_info/2" do
    test "includes only iGlu ID when order has no name" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: nil)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: %{fulfillment | order: Repo.preload(order, :staff)},
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      {:ok, result} = AdditionalInfo.execute(%{}, serie, invoice_items)
      assert get_in(result, [:infAdic, :infCpl]) == "iGlu ID: #{order.id}"
    end

    test "includes iGlu ID and Shopify Name when order has name" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: "Special Order #123")
      fulfillment = insert(:fulfillment, order: order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: %{fulfillment | order: Repo.preload(order, :staff)},
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      {:ok, result} = AdditionalInfo.execute(%{}, serie, invoice_items)

      assert get_in(result, [:infAdic, :infCpl]) ==
               "iGlu ID: #{order.id} | Shopify Name: Special Order #123"
    end

    test "includes iGlu ID and staff name when order has no name but has staff" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: nil)
      staff = insert(:staff, name: "John Seller", organization: org)
      order = Repo.preload(order, :staff)
      order = %{order | staff: staff}

      fulfillment = insert(:fulfillment, order: order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      {:ok, result} = AdditionalInfo.execute(%{}, serie, invoice_items)
      assert get_in(result, [:infAdic, :infCpl]) == "iGlu ID: #{order.id} | Vendedor: John Seller"
    end

    test "includes all information when order has both name and staff" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org, name: "Special Order #123")
      staff = insert(:staff, name: "John Seller", organization: org)
      order = Repo.preload(order, :staff)
      order = %{order | staff: staff}

      fulfillment = insert(:fulfillment, order: order, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          product_variant: pv,
          organization: org,
          location: loc
        )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      {:ok, result} = AdditionalInfo.execute(%{}, serie, invoice_items)

      assert get_in(result, [:infAdic, :infCpl]) ==
               "iGlu ID: #{order.id} | Shopify Name: Special Order #123 | Vendedor: John Seller"
    end
  end
end
