defmodule Rms.Integrations.Vinco.Prepare.CustomerTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco

  import Rms.Factory

  describe "execute/2" do
    test "prepare a NF-e customer params in dev env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, invoice_env: "dev", location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            city_name: "são paulo",
            state: "SP",
            country_name: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL" ==
               customer_map.dest[:xNome]

      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "prepare a NF-e customer params in prod env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "prod", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            city_name: "são paulo",
            state: "SP",
            country_name: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert transaction_customer.name == customer_map.dest[:xNome]
      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "returns a error when client is null for a NF" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "prod", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:error, "A client for NF is mandatory"} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )
    end

    test "returns a error when client name is null for a NF" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "prod", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          name: nil,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            city_name: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        customer_id: transaction_customer.id,
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:error, "name is mandatory"} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )
    end

    test "returns a error when client address is null for a NF" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "prod", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: nil
        )

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        customer_id: transaction_customer.id,
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:error, "address is mandatory"} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )
    end

    test "prepare a NFC-e customer params in dev env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL" ==
               customer_map.dest[:xNome]

      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "prepare a NFC-e customer with address params in dev env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          name: nil,
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            city_name: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL" ==
               customer_map.dest[:xNome]

      assert %{
               xMun: "são paulo",
               UF: "SP",
               xBairro: "Pinheiros",
               xLgr: "rua",
               nro: "123",
               CEP: "1256789",
               xCpl: "lado"
             } = customer_map.dest.enderDest

      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "prepare a NFC-e customer params in prod env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      serie =
        insert(:invoice_serie,
          invoice_type: "nfc",
          location: loc,
          invoice_env: "prod",
          organization: org
        )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert transaction_customer.name == customer_map.dest[:xNome]
      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "prepare a NFC-e customer with address params in prod env" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      serie =
        insert(:invoice_serie,
          invoice_type: "nfc",
          invoice_env: "prod",
          location: loc,
          organization: org
        )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      transaction_customer =
        insert(:transaction_customer,
          name: "Nome",
          transaction: transaction,
          organization: org,
          email: "<EMAIL>",
          address: %{
            city_name: "são paulo",
            state: "SP",
            countryName: "Brasil",
            neighborhood: "Pinheiros",
            street: "rua",
            number: "123",
            zip: "1256789",
            complement: "lado"
          }
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, customer_map} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )

      assert %{
               xMun: "são paulo",
               UF: "SP",
               xBairro: "Pinheiros",
               xLgr: "rua",
               nro: "123",
               CEP: "1256789",
               xCpl: "lado"
             } = customer_map.dest[:enderDest]

      assert transaction_customer.name == customer_map.dest[:xNome]
      assert transaction_customer.document == customer_map.dest[:CPF]
      assert "9" == customer_map.dest[:indIEDest]
    end

    test "returns a error when client is null for a NFC-e" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      serie =
        insert(:invoice_serie,
          invoice_type: "nfc",
          location: loc,
          invoice_env: "prod",
          organization: org
        )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        invoice_payments: [%{payment_id: payment.id}],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{dest: %{}}} =
               Vinco.Prepare.Customer.execute(
                 %{},
                 fiscal_invoice.serie,
                 fiscal_invoice.customer
               )
    end
  end
end
