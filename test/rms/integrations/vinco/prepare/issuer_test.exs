defmodule Rms.Integrations.Vinco.Prepare.IssuerTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco

  import Rms.Factory

  describe "execute/2" do
    test "prepare issue params" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      serie =
        insert(:invoice_serie,
          invoice_env: "dev",
          location: loc,
          invoice_env: "prod",
          organization: org
        )

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      lt =
        insert(:location_tax,
          location: loc,
          organization: org
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:organization, location: [:address]]
        ])

      assert {:ok, %{ide: ide, emit: emit}} =
               Vinco.Prepare.Issuer.execute(
                 %{ide: %{cUF: "", cMunFG: ""}},
                 fiscal_invoice.serie
               )

      assert ide.cUF == 35
      assert ide.cMunFG == 3_550_308
      assert emit[:CNPJ] == loc.cnpj
      assert emit[:CRT] == lt.crt
      assert emit[:IE] == lt.ie

      assert emit.enderEmit[:CEP] == loc.address.zip
      assert emit.enderEmit[:UF] == loc.address.state
      assert emit.enderEmit.cMun == loc.address.city_code
      assert emit.enderEmit.nro == loc.address.number
      assert emit.enderEmit.xBairro == loc.address.neighborhood
      assert emit.enderEmit.xLgr == loc.address.street
      assert emit.enderEmit.xMun == loc.address.city_name
    end
  end
end
