defmodule Rms.Integrations.Vinco.Prepare.ItemBuilderTest do
  use Rms.DataCase, async: true

  alias Rms.Integrations.Vinco.Prepare.ItemBuilder

  # Unused aliases Rms.Accounts.Organization, Rms.Inventory.Product, Rms.Inventory.ProductVariant removed

  # Helper to create a basic line item for testing
  defp basic_line_item_map(organization, attrs) do
    attrs_map = Enum.into(attrs, %{})

    product_ncm = Map.get(attrs_map, :ncm, "********")
    product_sku = Map.get(attrs_map, :sku, "SKU123")
    product_name_attr = Map.get(attrs_map, :product_name, "Test Product by basic_line_item_map")
    variant_name_attr = Map.get(attrs_map, :name, "Test Variant by basic_line_item_map")

    product =
      insert(:product, organization: organization, ncm: product_ncm, name: product_name_attr)

    variant =
      insert(:product_variant,
        product: product,
        organization: organization,
        sku: product_sku,
        name: variant_name_attr
      )

    line_item_base_attrs = %{
      organization_id: organization.id,
      organization: organization,
      product_variant_id: variant.id,
      product_variant: variant,
      product_name: product.name,
      variant_name: variant.name,
      sku: variant.sku,
      quantity: 2,
      price: Decimal.new("8.00"),
      list_price: Decimal.new("10.00"),
      fulfillment_id: nil,
      staff_id: nil
    }

    line_item_final_attrs = Map.merge(line_item_base_attrs, attrs_map)

    %{line_item: build(:line_item, line_item_final_attrs)}
  end

  describe "build_items/6" do
    setup do
      organization = insert(:organization)
      # Minimal setup for SetTaxes - it might need product_taxes, location_tax etc.
      # Depending on how deep SetTaxes goes, more setup might be required here.
      # For now, assume SetTaxes can run with basic item data.
      # A common product_taxes record for SP
      insert(:product_taxes,
        organization: organization,
        uf: "SP",
        cfop: "5102",
        cst_icms: "00",
        icms_percentage: Decimal.new("0.18"),
        fcp_percentage: Decimal.new("0.02"),
        cst_pis: "01",
        pis_percentage: Decimal.new("0.0165"),
        cst_cofins: "01",
        cofins_percentage: Decimal.new("0.0760")
      )

      {:ok, organization: organization}
    end

    test "correctly processes a single item with nItem, taxes, and discount", %{organization: org} do
      # Ensure specific product_taxes for the item created by basic_line_item_map
      insert(:product_taxes,
        organization: org,
        sku: "SKU123",
        ncm: "********",
        uf: "SP",
        cfop: "5102",
        cst_icms: "00",
        icms_percentage: Decimal.new("0.18"),
        fcp_percentage: Decimal.new("0.02"),
        cst_pis: "01",
        pis_percentage: Decimal.new("0.0165"),
        cst_cofins: "01",
        cofins_percentage: Decimal.new("0.0760")
      )

      item1_map =
        basic_line_item_map(org,
          quantity: 1,
          list_price: Decimal.new("100"),
          price: Decimal.new("90")
        )

      items_to_build = [item1_map]

      env = "dev"
      # Mock location_id, ensure SetTaxes can work with it or mock get_current_uf if needed
      location_id = 1
      uf_origin = "SP"
      uf_destiny = "SP"

      # (100*1 - 90*1)
      expected_discount = Decimal.new("10.00")
      # list_price * quantity
      expected_total_price = Decimal.new("100.00")
      # effective_total_for_taxes = 90.00
      # ICMS: 90 * 0.18 = 16.20; FCP: 90 * 0.02 = 1.80
      # PIS: 90 * 0.0165 = 1.485 -> 1.49; COFINS: 90 * 0.0760 = 6.84

      {:ok, {additional_info, built_items}} =
        ItemBuilder.build_items(env, location_id, org.id, items_to_build, uf_origin, uf_destiny)

      # Updated to expect the additional_info message if SetTaxes provides it
      assert additional_info == ["Info Message"]
      assert length(built_items) == 1

      item = hd(built_items)
      assert item.nItem == 1
      assert item.prod[:cProd] == "SKU123"

      assert item.prod[:xProd] ==
               "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"

      assert item.prod[:qCom] == "1"
      assert item.prod[:vUnCom] == Decimal.new("100.00")
      assert item.prod[:vProd] == expected_total_price
      assert item.prod[:vDesc] == expected_discount
      assert item.prod[:CFOP] == "5102"

      # Verify tax structure (actual values depend on SetTaxes logic which is complex)
      assert item.imposto[:ICMS][:vBC] == Decimal.new("90.00")
      assert item.imposto[:ICMS][:pICMS] == Decimal.new("18.00")
      assert item.imposto[:ICMS][:vICMS] == Decimal.new("16.20")
      assert item.imposto[:ICMS][:pFCP] == Decimal.new("2.00")
      assert item.imposto[:ICMS][:vFCP] == Decimal.new("1.80")

      assert item.imposto[:PIS][:vBC] == Decimal.new("73.80")
      assert item.imposto[:PIS][:pPIS] == Decimal.new("1.65")
      assert item.imposto[:PIS][:vPIS] == Decimal.new("1.22")

      assert item.imposto[:COFINS][:vBC] == Decimal.new("73.80")
      assert item.imposto[:COFINS][:pCOFINS] == Decimal.new("7.60")
      assert item.imposto[:COFINS][:vCOFINS] == Decimal.new("5.61")
    end

    test "correctly processes multiple items with sequential nItem and respects order", %{
      organization: org
    } do
      product1 = insert(:product, organization: org, ncm: "111")
      variant1 = insert(:product_variant, product: product1, organization: org, sku: "PROD001")
      # Add specific product_taxes for item1
      insert(:product_taxes,
        organization: org,
        sku: "PROD001",
        ncm: "111",
        uf: "SP",
        cfop: "5102",
        cst_icms: "00",
        icms_percentage: Decimal.new("0.18"),
        fcp_percentage: Decimal.new("0.02"),
        cst_pis: "01",
        pis_percentage: Decimal.new("0.0165"),
        cst_cofins: "01",
        cofins_percentage: Decimal.new("0.0760")
      )

      item1_li =
        build(:line_item,
          organization: org,
          organization_id: org.id,
          product_variant_id: variant1.id,
          product_variant: variant1,
          sku: "PROD001",
          quantity: 1,
          list_price: Decimal.new("10"),
          price: Decimal.new("10")
        )

      product2 = insert(:product, organization: org, ncm: "222")
      variant2 = insert(:product_variant, product: product2, organization: org, sku: "PROD002")
      # Add specific product_taxes for item2
      insert(:product_taxes,
        organization: org,
        sku: "PROD002",
        ncm: "222",
        uf: "SP",
        cfop: "5102",
        cst_icms: "00",
        icms_percentage: Decimal.new("0.18"),
        fcp_percentage: Decimal.new("0.02"),
        cst_pis: "01",
        pis_percentage: Decimal.new("0.0165"),
        cst_cofins: "01",
        cofins_percentage: Decimal.new("0.0760")
      )

      item2_li =
        build(:line_item,
          organization: org,
          organization_id: org.id,
          product_variant_id: variant2.id,
          product_variant: variant2,
          sku: "PROD002",
          quantity: 2,
          list_price: Decimal.new("20"),
          price: Decimal.new("20")
        )

      items_to_build = [
        %{line_item: item1_li},
        %{line_item: item2_li}
      ]

      {:ok, {_additional_info, built_items}} =
        ItemBuilder.build_items("dev", 1, org.id, items_to_build, "SP", "SP")

      assert length(built_items) == 2
      item_one = Enum.at(built_items, 0)
      item_two = Enum.at(built_items, 1)

      assert item_one.nItem == 1
      assert item_one.prod[:cProd] == "PROD001"
      assert item_one.prod[:qCom] == "1"
      assert item_one.prod[:vProd] == Decimal.new("10.00")

      assert item_two.nItem == 2
      assert item_two.prod[:cProd] == "PROD002"
      assert item_two.prod[:qCom] == "2"
      assert item_two.prod[:vProd] == Decimal.new("40.00")
    end

    test "uses product name for prod environment", %{organization: org} do
      prod_name = "Actual Product Name"
      var_name = "Actual Variant Name"
      product = insert(:product, organization: org, name: prod_name, ncm: "PN1NCM")

      variant =
        insert(:product_variant, product: product, organization: org, sku: "PN1", name: var_name)

      insert(:product_taxes,
        organization: org,
        uf: "SP",
        sku: "PN1",
        ncm: "PN1NCM",
        cfop: "5102",
        cst_icms: "00",
        icms_percentage: Decimal.new("0.18"),
        fcp_percentage: Decimal.new("0.02"),
        cst_pis: "01",
        pis_percentage: Decimal.new("0.0165"),
        cst_cofins: "01",
        cofins_percentage: Decimal.new("0.0760")
      )

      item_map =
        basic_line_item_map(org,
          product_name: prod_name,
          variant_name: var_name,
          product_variant: variant,
          sku: "PN1"
        )

      {:ok, {_info, built_items}} =
        ItemBuilder.build_items("prod", 1, org.id, [item_map], "SP", "SP")

      item = hd(built_items)

      # ItemBuilder get_product_name prefers product_variant.name, then product_name, then variant_name from line_item fields
      # basic_line_item_map puts variant.name into line_item.variant_name and product.name into line_item.product_name
      # and it preloads product_variant into line_item.product_variant which has its own name field.
      # Map.get(item_struct.product_variant || %{}, :name) -> this should be var_name
      assert item.prod[:xProd] == var_name
    end
  end
end
