defmodule Rms.Integrations.Vinco.PrepareItemsTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco
  alias Rms.Integrations.Vinco.Prepare

  import Rms.Factory

  describe "execute/2" do
    setup do
      org = insert(:organization)
      {:ok, org: org}
    end

    test "prepare items params", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, sku: "#20005998_0027_P", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: [payment_map]}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      assert payment_map.prod.cProd == "200059980027P"
      assert payment_map.prod[:NCM] == product.ncm
      assert payment_map.prod.vProd == Decimal.round(line_item.price, 2)
      assert payment_map.prod.qCom == "#{line_item.quantity}"

      assert payment_map.imposto == %{
               COFINS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pCOFINS: Decimal.new("10.00"),
                 vCOFINS: Decimal.new("0.82")
               },
               ICMS: %{
                 CSTCSOSN: "30",
                 modBC: "3",
                 orig: "0",
                 pFCP: Decimal.new("2.00"),
                 pICMS: Decimal.new("18.00"),
                 vBC: Decimal.new("10.00"),
                 vFCP: Decimal.new("0.20"),
                 vICMS: Decimal.new("1.80")
               },
               PIS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pPIS: Decimal.new("10.00"),
                 vPIS: Decimal.new("0.82")
               }
             }
    end

    test "prepare items params when sku has special characters", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, sku: "#20005998_0027_P", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: [payment_map]}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      assert payment_map.prod.cProd == "200059980027P"
      assert payment_map.prod[:NCM] == product.ncm
      assert payment_map.prod.vProd == Decimal.round(line_item.price, 2)
      assert payment_map.prod.qCom == "#{line_item.quantity}"

      assert payment_map.imposto == %{
               COFINS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pCOFINS: Decimal.new("10.00"),
                 vCOFINS: Decimal.new("0.82")
               },
               ICMS: %{
                 CSTCSOSN: "30",
                 modBC: "3",
                 orig: "0",
                 pFCP: Decimal.new("2.00"),
                 pICMS: Decimal.new("18.00"),
                 vBC: Decimal.new("10.00"),
                 vFCP: Decimal.new("0.20"),
                 vICMS: Decimal.new("1.80")
               },
               PIS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pPIS: Decimal.new("10.00"),
                 vPIS: Decimal.new("0.82")
               }
             }
    end

    test "when there is no tax config", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, ncm: "123", organization: org)
      pv = insert(:product_variant, product: product, sku: "200059980027P", organization: org)

      product1 = insert(:product, ncm: "321", organization: org)
      pv1 = insert(:product_variant, sku: "200059980027M", product: product1, organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        ncm: product1.ncm,
        sku: pv1.sku,
        uf: nil,
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.03"),
        icms_percentage: Decimal.new("0.17"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}, %{line_item_id: line_item1.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: payment_maps}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      payment_map = Enum.find(payment_maps, fn pm -> pm.prod[:NCM] == product1.ncm end)
      assert payment_map.prod.cProd == pv1.sku
      assert payment_map.prod[:NCM] == product1.ncm
      assert payment_map.prod.vProd == Decimal.round(line_item1.price, 2)
      assert payment_map.prod.qCom == "#{line_item1.quantity}"

      assert payment_map.imposto == %{
               COFINS: %{
                 vBC: Decimal.new("8.30"),
                 CST: "08",
                 pCOFINS: Decimal.new("10.00"),
                 vCOFINS: Decimal.new("0.83")
               },
               ICMS: %{
                 CSTCSOSN: "30",
                 modBC: "3",
                 orig: "0",
                 pFCP: Decimal.new("3.00"),
                 pICMS: Decimal.new("17.00"),
                 vBC: Decimal.new("10.00"),
                 vFCP: Decimal.new("0.30"),
                 vICMS: Decimal.new("1.70")
               },
               PIS: %{
                 vBC: Decimal.new("8.30"),
                 CST: "08",
                 pPIS: Decimal.new("10.00"),
                 vPIS: Decimal.new("0.83")
               }
             }

      payment_map = Enum.find(payment_maps, fn pm -> pm.prod[:NCM] == product.ncm end)
      assert payment_map.prod.cProd == pv.sku
      assert payment_map.prod[:NCM] == product.ncm
      assert payment_map.prod.vProd == Decimal.round(line_item.price, 2)
      assert payment_map.prod.qCom == "#{line_item.quantity}"

      assert payment_map.imposto == %{
               COFINS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pCOFINS: Decimal.new("10.00"),
                 vCOFINS: Decimal.new("0.82")
               },
               ICMS: %{
                 CSTCSOSN: "30",
                 modBC: "3",
                 orig: "0",
                 pFCP: Decimal.new("2.00"),
                 pICMS: Decimal.new("18.00"),
                 vBC: Decimal.new("10.00"),
                 vFCP: Decimal.new("0.20"),
                 vICMS: Decimal.new("1.80")
               },
               PIS: %{
                 vBC: Decimal.new("8.20"),
                 CST: "08",
                 pPIS: Decimal.new("10.00"),
                 vPIS: Decimal.new("0.82")
               }
             }
    end

    test "prepare items params for many items", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, ncm: "123", organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      product1 = insert(:product, ncm: "321", organization: org)
      pv1 = insert(:product_variant, product: product1, organization: org)

      fulfillment1 = insert(:fulfillment, order: order, organization: org)
      fulfillment2 = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment1,
          organization: org,
          product_variant: pv,
          location: loc
        )

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment2,
          organization: org,
          product_variant: pv1,
          location: loc
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}, %{line_item_id: line_item1.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      expected_error = %{
        reason: "no valid config found for sku <#{pv1.sku}>, ncm <#{product1.ncm}> and uf <SP>",
        status: :missing_taxes
      }

      assert {:error, ^expected_error} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )
    end

    test "prepare items params with additional info message", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      _transaction = insert(:transaction, order: order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST123", organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org,
        additional_info_message: "Test Info Message"
      )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      vinco_invoice = %{emit: %{enderEmit: %{UF: "SP"}}}

      assert {:ok, result} = Prepare.Items.execute(vinco_invoice, serie, invoice_items)
      assert get_in(result, [:infAdic, :infCpl]) == "Test Info Message"
    end

    test "prepare items params with multiple additional info messages", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      _transaction = insert(:transaction, order: order, organization: org)

      # First item
      product1 = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product1, sku: "TEST123", organization: org)
      # Second item
      product2 = insert(:product, organization: org)
      pv2 = insert(:product_variant, product: product2, sku: "TEST456", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item1 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv1,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      line_item2 =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv2,
          location: loc,
          list_price: Decimal.new("20.00"),
          quantity: 1
        )

      insert(:product_taxes,
        ncm: product1.ncm,
        sku: pv1.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org,
        additional_info_message: "Info Message 1"
      )

      insert(:product_taxes,
        ncm: product2.ncm,
        sku: pv2.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org,
        additional_info_message: "Info Message 2"
      )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item1
        },
        %Fiscal.InvoiceItem{
          line_item: line_item2
        }
      ]

      vinco_invoice = %{emit: %{enderEmit: %{UF: "SP"}}}

      assert {:ok, result} = Prepare.Items.execute(vinco_invoice, serie, invoice_items)
      assert get_in(result, [:infAdic, :infCpl]) == "Info Message 1 | Info Message 2"
    end

    test "prepare items params with nil additional info message", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)
      order = insert(:order, organization: org)
      _transaction = insert(:transaction, order: order, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST123", organization: org)
      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      insert(:product_taxes,
        ncm: product.ncm,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org,
        additional_info_message: nil
      )

      invoice_items = [
        %Fiscal.InvoiceItem{
          line_item: line_item
        }
      ]

      vinco_invoice = %{emit: %{enderEmit: %{UF: "SP"}}}

      assert {:ok, result} = Prepare.Items.execute(vinco_invoice, serie, invoice_items)
      assert get_in(result, [:infAdic, :infCpl]) == nil
    end

    test "uses NCM from taxes instead of product", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      # Create a product with a specific NCM
      product = insert(:product, ncm: nil, organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST_NCM", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      # Create taxes with the same NCM for lookup but it will return a different NCM
      taxes =
        insert(:product_taxes,
          ncm: "TESTNCM",
          sku: pv.sku,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      # Update the taxes with a different NCM to verify in the return
      taxes
      |> Ecto.Changeset.change(ncm: "87654321")
      |> Rms.Repo.update!()

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: [payment_map]}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      # Verify that the NCM in the result matches the one from taxes, not from the product
      assert payment_map.prod.cProd == "TESTNCM"
      assert payment_map.prod[:NCM] == "87654321"
      refute payment_map.prod[:NCM] == product.ncm
    end

    test "uses NCM from taxes when taxes.ncm is present", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, ncm: nil, organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST_SKU", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      insert(:product_taxes,
        ncm: "TAXES_NCM",
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: [payment_map]}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      assert payment_map.prod[:NCM] == "TAXES_NCM"
      refute payment_map.prod[:NCM] == product.ncm
    end

    test "falls back to product NCM when taxes.ncm is nil", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      # Product with NCM
      product = insert(:product, ncm: "PRODUCT_NCM", organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST_SKU", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      # Insert taxes using product NCM for lookup, then set ncm to nil
      insert(:product_taxes,
        ncm: nil,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, %{det: [payment_map]}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      # Should use product.ncm when taxes.ncm is nil
      assert payment_map.prod[:NCM] == "PRODUCT_NCM"
      assert payment_map.prod[:NCM] == product.ncm
    end

    test "fails with missing NCM error when both taxes and product NCM are nil", %{org: org} do
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "dev", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      # Product with nil NCM
      product = insert(:product, ncm: nil, organization: org)
      pv = insert(:product_variant, product: product, sku: "TEST_SKU", organization: org)

      fulfillment = insert(:fulfillment, order: order, organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          list_price: Decimal.new("10.00"),
          quantity: 1
        )

      insert(:product_taxes,
        ncm: nil,
        sku: pv.sku,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      # This should fail with missing NCM error since both are nil
      assert {:error, %{reason: reason, status: :missing_ncm}} =
               Vinco.Prepare.Items.execute(
                 %{emit: %{enderEmit: %{UF: "SP"}}},
                 fiscal_invoice.serie,
                 fiscal_invoice.invoice_items
               )

      assert reason =~ "ncm is empty"
    end
  end
end
