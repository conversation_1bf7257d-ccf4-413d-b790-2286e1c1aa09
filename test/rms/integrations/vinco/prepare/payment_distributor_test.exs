defmodule Rms.Integrations.Vinco.Prepare.PaymentDistributorTest do
  use Rms.DataCase
  import Rms.Factory

  describe "payment distributor" do
    test "only in-store fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      order = insert(:order, organization: org, total_price: Decimal.new("100.00"))
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          transaction: transaction,
          organization: org,
          amount: Decimal.new("100.00")
        )

      fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("100.00")
        )

      fiscal_invoice = insert(:fiscal_invoice, fulfillment: fulfillment, organization: org)
      invoice_payments = [%{payment: payment}]

      result =
        Rms.Integrations.Vinco.Prepare.PaymentDistributor.execute(
          fiscal_invoice,
          invoice_payments
        )

      [{returned_payment, returned_amount}] = result
      assert returned_payment.id == payment.id
      assert returned_amount == Decimal.new("100.00")
    end

    test "in-store and delivery fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      order = insert(:order, organization: org, total_price: Decimal.new("100.00"))
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org,
          amount: Decimal.new("100.00")
        )

      fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

      _line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("55.00")
        )

      delivery_fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

      _delivery_line_item =
        insert(:line_item,
          fulfillment: delivery_fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("45.00")
        )

      fiscal_invoice_in_store =
        insert(:fiscal_invoice, fulfillment: fulfillment, organization: org)

      invoice_payments = [%{payment: payment}]

      result_in_store =
        Rms.Integrations.Vinco.Prepare.PaymentDistributor.execute(
          fiscal_invoice_in_store,
          invoice_payments
        )

      [{returned_payment_in_store, returned_amount_in_store}] = result_in_store
      assert returned_payment_in_store.id == payment.id
      assert returned_amount_in_store == Decimal.new("55.00")
    end

    test "in-store, delivery and local-pickup fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      order = insert(:order, organization: org, total_price: Decimal.new("100.00"))
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org,
          amount: Decimal.new("150.00")
        )

      fulfillment_in_store =
        insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

      _line_item_in_store =
        insert(:line_item,
          fulfillment: fulfillment_in_store,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("50.00")
        )

      delivery_fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

      _delivery_line_item =
        insert(:line_item,
          fulfillment: delivery_fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("50.00")
        )

      local_pickup_fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "local-pickup")

      _local_pickup_line_item =
        insert(:line_item,
          fulfillment: local_pickup_fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("25.00")
        )

      fiscal_invoice_in_store =
        insert(:fiscal_invoice, fulfillment: fulfillment_in_store, organization: org)

      invoice_payments = [%{payment: payment}]

      result_in_store =
        Rms.Integrations.Vinco.Prepare.PaymentDistributor.execute(
          fiscal_invoice_in_store,
          invoice_payments
        )

      [{returned_payment_in_store, returned_amount_in_store}] = result_in_store
      assert returned_payment_in_store.id == payment.id
      assert returned_amount_in_store == Decimal.new("75.00")
    end

    test "only delivery fulfillment" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          transaction: transaction,
          organization: org,
          amount: Decimal.new("100.00")
        )

      delivery_fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

      _delivery_line_item =
        insert(:line_item,
          fulfillment: delivery_fulfillment,
          organization: org,
          product_variant: pv,
          location: loc,
          price: Decimal.new("100.00")
        )

      fiscal_invoice_delivery =
        insert(:fiscal_invoice, fulfillment: delivery_fulfillment, organization: org)

      invoice_payments = [%{payment: payment}]

      result_delivery =
        Rms.Integrations.Vinco.Prepare.PaymentDistributor.execute(
          fiscal_invoice_delivery,
          invoice_payments
        )

      [{returned_payment_delivery, returned_amount_delivery}] = result_delivery
      assert returned_payment_delivery.id == payment.id
      assert returned_amount_delivery == Decimal.new("0.00")
    end
  end

  test "vPag equals vNF when there is a sale with in-store and delivery" do
    org = insert(:organization)
    loc = insert(:location, organization: org)
    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    order =
      insert(:order,
        organization: org,
        total_price: Decimal.new("150.00"),
        total_delivery_price: Decimal.new("15.00")
      )

    transaction = insert(:transaction, order: order, organization: org)

    payment =
      insert(:payment,
        method: "pix",
        status: "settled",
        transaction: transaction,
        organization: org,
        amount: Decimal.new("150.00")
      )

    in_store_fulfillment =
      insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

    _in_store_line_item =
      insert(:line_item,
        fulfillment: in_store_fulfillment,
        organization: org,
        product_variant: pv,
        location: loc,
        price: Decimal.new("85.00")
      )

    delivery_fulfillment =
      insert(:fulfillment, order: order, organization: org, shipping_method: "delivery")

    _delivery_line_item =
      insert(:line_item,
        fulfillment: delivery_fulfillment,
        organization: org,
        product_variant: pv,
        location: loc,
        price: Decimal.new("50.00")
      )

    fiscal_invoice_in_store =
      insert(:fiscal_invoice, fulfillment: in_store_fulfillment, organization: org)

    invoice_payments = [%{payment: payment}]

    result_in_store =
      Rms.Integrations.Vinco.Prepare.PaymentDistributor.execute(
        fiscal_invoice_in_store,
        invoice_payments
      )

    [{returned_payment_in_store, returned_amount_in_store}] = result_in_store
    assert returned_payment_in_store.id == payment.id

    expected_vnf = Decimal.new("85.00")

    total_non_nf = Decimal.add(Decimal.new("50.00"), Decimal.new("15.00"))
    proportion = Decimal.div(payment.amount, order.total_price)

    expected_vpag =
      Decimal.sub(payment.amount, Decimal.mult(total_non_nf, proportion))
      |> Decimal.round(2)

    assert returned_amount_in_store == expected_vpag
    assert expected_vpag == expected_vnf
  end
end
