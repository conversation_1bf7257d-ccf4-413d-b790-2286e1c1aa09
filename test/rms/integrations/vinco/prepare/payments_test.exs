defmodule Rms.Integrations.Vinco.Prepare.PaymentsTest do
  use Rms.DataCase

  alias Rms.Fiscal
  alias Rms.Integrations.Vinco

  import Rms.Factory

  describe "execute/2" do
    test "prepare payment params" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      serie = insert(:invoice_serie, location: loc, invoice_env: "prod", organization: org)

      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_pix =
        insert(:payment,
          method: "pix",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      payment_credit =
        insert(:payment,
          method: "credit_card",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      payment_debit =
        insert(:payment,
          method: "debit_card",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      payment_cash =
        insert(:payment,
          method: "cash",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      payment_link =
        insert(:payment,
          method: "payment_link",
          status: "settled",
          transaction: transaction,
          organization: org
        )

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: org)

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, shipping_method: "in-store", organization: org)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: org,
          product_variant: pv,
          location: loc
        )

      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_pix.id},
          %{payment_id: payment_credit.id},
          %{payment_id: payment_debit.id},
          %{payment_id: payment_cash.id},
          %{payment_id: payment_link.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}],
        fulfillment_id: fulfillment.id
      }

      assert {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(org.id, attrs)

      fiscal_invoice =
        Rms.Repo.preload(fiscal_invoice, [
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location]
        ])

      assert {:ok, payment_map} =
               Vinco.Prepare.Payments.execute(
                 %{},
                 fiscal_invoice
               )

      Enum.each(payment_map.pag.detPag, fn
        %{vPag: value, tPag: "17"} ->
          assert payment_pix.amount == value

        %{vPag: value, tPag: "03", card: card} ->
          assert payment_credit.amount == value
          assert %{tpIntegra: 2} == card

        %{vPag: value, tPag: "04", card: card} ->
          assert payment_debit.amount == value
          assert %{tpIntegra: 2} == card

        %{vPag: value, tPag: "01"} ->
          assert payment_cash.amount == value

        %{vPag: value, tPag: "99", xPag: method} ->
          assert payment_link.amount == value
          assert payment_link.method == method
      end)
    end
  end
end
