defmodule Rms.Integrations.Vinco.Prepare.Return.AdditionalInfoTest do
  use Rms.DataCase

  alias Rms.Integrations.Vinco.Prepare.Return.AdditionalInfo

  import Rms.Factory

  describe "execute/3" do
    setup do
      organization = insert(:organization)
      location = insert(:location, organization: organization, name: "Store A")
      staff = insert(:staff, organization: organization, name: "<PERSON>")

      # Insert organization setting for shopify
      insert(:organization_setting,
        key: "organization_order_integration_services",
        value: %{data: "shopify"},
        organization: organization
      )

      original_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          name: "Shopify Order #123"
        )

      fiscal_invoice = %Rms.Fiscal.FiscalInvoice{}

      {:ok,
       %{
         organization: organization,
         location: location,
         staff: staff,
         original_order: original_order,
         fiscal_invoice: fiscal_invoice
       }}
    end

    test "formats additional info correctly for return with shopify order", %{
      location: location,
      original_order: original_order,
      fiscal_invoice: fiscal_invoice
    } do
      {:ok, additional_info} = AdditionalInfo.execute(fiscal_invoice, original_order, location)

      assert additional_info =~ "NFe referenciada - #{fiscal_invoice.df_key}"
      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Shopify Name: Shopify Order #123"
      assert additional_info =~ "Vendedor: John Doe"
      assert additional_info =~ "Origem: Store A"
      assert additional_info =~ "Destino: Store A"
      assert additional_info =~ "Devolucao: Store A"
    end

    test "formats additional info correctly for return without location", %{
      original_order: original_order,
      fiscal_invoice: fiscal_invoice
    } do
      {:ok, additional_info} = AdditionalInfo.execute(fiscal_invoice, original_order, nil)

      assert additional_info =~ "NFe referenciada - #{fiscal_invoice.df_key}"
      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Shopify Name: Shopify Order #123"
      assert additional_info =~ "Vendedor: John Doe"
      assert additional_info =~ "Origem: Store A"
      refute additional_info =~ "Destino:"
      refute additional_info =~ "Devolucao:"
    end

    test "formats additional info correctly when order has no staff", %{
      organization: organization,
      location: location,
      fiscal_invoice: fiscal_invoice
    } do
      order_without_staff =
        insert(:order,
          organization: organization,
          location: location,
          staff: nil,
          name: "Shopify Order #456"
        )

      {:ok, additional_info} =
        AdditionalInfo.execute(fiscal_invoice, order_without_staff, location)

      assert additional_info =~ "NFe referenciada - #{fiscal_invoice.df_key}"
      assert additional_info =~ "iGlu ID: #{order_without_staff.id}"
      assert additional_info =~ "Shopify Name: Shopify Order #456"
      refute additional_info =~ "Vendedor:"
      assert additional_info =~ "Origem: Store A"
      assert additional_info =~ "Destino: Store A"
      assert additional_info =~ "Devolucao: Store A"
    end
  end
end
