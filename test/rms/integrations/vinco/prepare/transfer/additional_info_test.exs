defmodule Rms.Integrations.Vinco.Prepare.Transfer.AdditionalInfoTest do
  use Rms.DataCase

  alias Rms.Integrations.Vinco.Prepare.Transfer.AdditionalInfo

  describe "execute/2" do
    test "formats additional info correctly for transfer between locations" do
      organization = insert(:organization)
      location = insert(:location, organization: organization, name: "Store A")
      location_2 = insert(:location, organization: organization, name: "Store B")
      staff = insert(:staff, organization: organization, name: "<PERSON>")

      original_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff
        )

      _new_order =
        insert(:order,
          organization: organization,
          location: location_2
        )

      {:ok, additional_info} = AdditionalInfo.execute("", original_order, location_2)

      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Vendedor: <PERSON>"
      assert additional_info =~ "Origem: Store A"
      assert additional_info =~ "Destino: Store B"
    end

    test "formats additional info correctly for transfer with shopify order" do
      organization = insert(:organization)
      location = insert(:location, organization: organization, name: "Store A")
      location_2 = insert(:location, organization: organization, name: "<PERSON> B")
      staff = insert(:staff, organization: organization, name: "<PERSON>")

      # Insert organization setting for shopify
      insert(:organization_setting,
        key: "organization_order_integration_services",
        value: %{data: "shopify"},
        organization: organization
      )

      original_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          name: "Shopify Order #123"
        )

      {:ok, additional_info} = AdditionalInfo.execute("", original_order, location_2)

      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Shopify Name: Shopify Order #123"
      assert additional_info =~ "Vendedor: John Doe"
      assert additional_info =~ "Origem: Store A"
      assert additional_info =~ "Destino: Store B"
    end

    test "formats additional info correctly for transfer without destination location" do
      organization = insert(:organization)
      location = insert(:location, organization: organization, name: "Store A")
      staff = insert(:staff, organization: organization, name: "John Doe")

      # Insert organization setting for shopify
      insert(:organization_setting,
        key: "organization_order_integration_services",
        value: %{data: "shopify"},
        organization: organization
      )

      original_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          name: "Shopify Order #123"
        )

      {:ok, additional_info} = AdditionalInfo.execute("", original_order, nil)

      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Shopify Name: Shopify Order #123"
      assert additional_info =~ "Vendedor: John Doe"
      assert additional_info =~ "Origem: Store A"
      refute additional_info =~ "Destino:"
    end

    test "formats additional info correctly for transfer with nil staff" do
      organization = insert(:organization)
      location = insert(:location, organization: organization, name: "Store A")
      location_2 = insert(:location, organization: organization, name: "Store B")

      original_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: nil
        )

      {:ok, additional_info} = AdditionalInfo.execute("", original_order, location_2)

      assert additional_info =~ "iGlu ID: #{original_order.id}"
      assert additional_info =~ "Vendedor: N/A"
      assert additional_info =~ "Origem: Store A"
      assert additional_info =~ "Destino: Store B"
    end
  end
end
