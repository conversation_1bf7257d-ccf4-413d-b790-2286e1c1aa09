defmodule Rms.Integrations.Vinco.Prepare.Transfer.ItemsTest do
  use Rms.DataCase

  alias Rms.Integrations.Vinco.Prepare.Transfer.Items

  import Rms.Factory

  describe "execute/6" do
    setup do
      organization = insert(:organization)

      address =
        insert(:address, %{
          state: "SP",
          city_code: "3550308",
          city_name: "São Paulo",
          zip: "01234567",
          neighborhood: "Centro",
          street: "Rua Test",
          number: "123"
        })

      location = insert(:location, organization: organization, address: address)

      _fiscal_settings =
        insert(:fiscal_settings, %{
          organization: organization,
          location: location,
          transfer_without_taxes: false
        })

      # Add product taxes for SP (origin)
      insert(:product_taxes,
        sku: nil,
        ncm: nil,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        benefit_code: nil,
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        benefit_code: nil,
        organization: organization
      )

      # Add product taxes for BA (destination)
      insert(:product_taxes,
        sku: nil,
        ncm: nil,
        uf: "BA",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        benefit_code: nil,
        organization: organization
      )

      # Add product taxes for RJ (destination)
      insert(:product_taxes,
        ncm: nil,
        sku: nil,
        uf: "RJ",
        origin: "0",
        cst_icms: "30",
        benefit_code: nil,
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: organization
      )

      # Insert interstate taxes for SP -> BA
      insert(:interstate_taxes,
        uf_origin: "SP",
        uf_destiny: "BA",
        icms_percentage: Decimal.new("0.07")
      )

      # Insert interstate taxes for SP -> RJ
      insert(:interstate_taxes,
        uf_origin: "SP",
        uf_destiny: "RJ",
        icms_percentage: Decimal.new("0.12")
      )

      # Insert location taxes
      insert(:location_tax, %{
        organization: organization,
        location: location,
        ie: "*********",
        crt: "1",
        name: "Location 1 Tax",
        current_tax_uf: "SP"
      })

      invoice_serie =
        insert(:invoice_serie,
          location: location,
          invoice_env: "dev",
          organization: organization,
          invoice_type: "nf",
          invoice_serie: "1",
          available_number: 1,
          status: "active"
        )

      product = insert(:product, organization: organization)

      product_variant =
        insert(:product_variant,
          organization: organization,
          sku: "530",
          product: product
        )

      base_item = %{
        line_item: %{
          price: Decimal.new("100.00"),
          list_price: Decimal.new("100.00"),
          quantity: 1,
          sku: product_variant.sku,
          organization_id: organization.id,
          product_variant: product_variant
        }
      }

      {:ok,
       organization: organization,
       location: location,
       invoice_serie: invoice_serie,
       base_item: base_item}
    end

    test "does not calculate ICMSUFDest for intrastate transfer SP -> SP", %{
      organization: organization,
      location: location,
      invoice_serie: invoice_serie,
      base_item: base_item
    } do
      items = [base_item]
      uf_origin = "SP"
      uf_destiny = "SP"

      {:ok, processed_items, _additional_info} =
        Items.execute(organization.id, location.id, invoice_serie, items, uf_origin, uf_destiny)

      [processed_item] = processed_items

      refute Map.has_key?(processed_item[:imposto], :ICMSUFDest)
      assert processed_item[:prod][:CFOP] == "5152"
    end
  end
end
