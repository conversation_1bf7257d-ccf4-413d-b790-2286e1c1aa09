defmodule Rms.Integrations.VincoTest do
  use Rms.DataCase

  import Mox

  alias Rms.Integrations.Vinco

  setup :verify_on_exit!

  setup do
    Mox.stub_with(VincoClientMock, VincoClientStub)

    :ok
  end

  describe "create_fiscal_invoice/4" do
    test "returns a corrected created invoice" do
      expect(VincoClientMock, :create_fiscal_invoice, 1, fn _client, _, _, _ ->
        {:ok, nf_response_builder(:ok)}
      end)

      {:ok, _} =
        Vinco.create_fiscal_invoice("type", "uf", %{invoice: "invoice"}, %{
          "vinco_key" => "vinco_id_key",
          "vinco_url" => "vinco_url"
        })
    end

    test "returns a incorrected created invoice" do
      expect(VincoClientMock, :create_fiscal_invoice, 1, fn _client, _, _, _ ->
        {:ok, nf_response_builder(:error)}
      end)

      {:ok, _} =
        Vinco.create_fiscal_invoice("type", "uf", %{invoice: "invoice"}, %{
          "vinco_key" => "vinco_id_key",
          "vinco_url" => "vinco_url"
        })
    end

    test "returns error" do
      expect(VincoClientMock, :create_fiscal_invoice, 1, fn _client, _, _, _ ->
        {:error, :not_found}
      end)

      {:error, _} =
        Vinco.create_fiscal_invoice("type", "uf", %{invoice: "invoice"}, %{
          "vinco_key" => "vinco_id_key",
          "vinco_url" => "vinco_url"
        })
    end
  end

  def nf_response_builder(:ok) do
    %{
      "ChaveDFe" => "35240443253315000234653000000003071110814728",
      "CodStatus" => 100,
      "CodStatusLote" => 104,
      "DFeProtocolo" => "135240000777778",
      "IdAssincrono" => 1_258_790_202,
      "Motivo" => "Autorizado o uso da NF-e",
      "MotivoLote" => "Lote processado",
      "QrCode" =>
        "https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240443253315000234653000000003071110814728|2|2|1|b091031f67143a8d78ac99ebf0efde96b1150961",
      "ReportZip" =>
        "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",
      "TimeOutAssincrono" => 0,
      "Token" => "3QZPAFUzdCxkfEJHlJzmIQ3i5NLWUGyRjNF2G6baGhc=",
      "UrlDanfe" =>
        "https://www.homologacao.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaPublica.aspx",
      "XmlDFe" =>
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240443253315000234653000000003071110814728\"><ide><cUF>35</cUF><cNF>11081472</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>300</serie><nNF>307</nNF><dhEmi>2024-04-21T16:01:51-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>8</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>43253315000234</CNPJ><xNome>name</xNome><enderEmit><xLgr>Rua Rua</xLgr><nro>1537</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05404014</CEP></enderEmit><IE>138557519112</IE><CRT>1</CRT></emit><dest><CPF>46373120198</CPF><xNome>NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xNome><indIEDest>9</indIEDest></dest><det nItem=\"1\"><prod><cProd>1</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>33049990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>10.00</vUnCom><vProd>10.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>10.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISNT><CST>08</CST></PISNT></PIS><COFINS><COFINSNT><CST>08</CST></COFINSNT></COFINS></imposto></det><total><ICMSTot><vBC>0</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>10.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>0.00</vPIS><vCOFINS>0.00</vCOFINS><vOutro>0</vOutro><vNF>10.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>10.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240443253315000234653000000003071110814728|2|2|1|b091031f67143a8d78ac99ebf0efde96b1150961]]></qrCode><urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240443253315000234653000000003071110814728\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>/7vG6p1H96i+NBhbcbIBSfKalTQ=</DigestValue></Reference></SignedInfo><SignatureValue>CcC0lipPzUqOZY0UVQxiEjDp6KDBA20nMNEeNr0/BWWHSdK51iB+tPizl7eabIopVEuVS0zz3rNJIYL6fCeE29ZaVcd8UB8xA67He1HZWnJUpg2Fo1Br3TE3T7PlD1Xan6aNl7YHxM+pwti+yxOkjiLKvtjkaPflh08ajb1wf80bHLLSj4Osfs1pqpXKLiClgMY8clUk9oKMCDtTXGp1iRY6Dxct6hloEWiDI4ylqJciXOp9lSeLrddu3T8krbQv0wsgwVNAn/GLsfKHoI8KrD/jZ/t9Cs7lJ3+pxAF01P8fMnAb0VpJEpyrn+wYflIvcpnOYU/kqBcpggUSSgrENQ==</SignatureValue><KeyInfo><X509Data><X509Certificate>MIIIAjCCBeqgAwIBAgIIBe7AMI0SRDUwDQYJKoZIhvcNAQELBQAwdTELMAkGA1UEBhMCQlIxEzARBgNVBAoMCklDUC1CcmFzaWwxNjA0BgNVBAsMLVNlY3JldGFyaWEgZGEgUmVjZWl0YSBGZWRlcmFsIGRvIEJyYXNpbCAtIFJGQjEZMBcGA1UEAwwQQUMgU0VSQVNBIFJGQiB2NTAeFw0yMzExMjkwMjEzMDBaFw0yNDExMjgwMjEyNTlaMIIBGDELMAkGA1UEBhMCQlIxCzAJBgNVBAgMAlNQMRIwEAYDVQQHDAlTYW8gUGF1bG8xEzARBgNVBAoMCklDUC1CcmFzaWwxNjA0BgNVBAsMLVNlY3JldGFyaWEgZGEgUmVjZWl0YSBGZWRlcmFsIGRvIEJyYXNpbCAtIFJGQjEWMBQGA1UECwwNUkZCIGUtQ05QSiBBMTEWMBQGA1UECwwNQUMgU0VSQVNBIFJGQjEXMBUGA1UECwwONjIxNzM2MjAwMDAxODAxGTAXBgNVBAsMEFZJREVPQ09ORkVSRU5DSUExNzA1BgNVBAMMLkFOREVSU09OIENBSU8gU0FOVE9TIFNJTFZBIExUREE6NDMyNTMzMTUwMDAxNTMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDl/TwPqF6bX2odB38T8K43lfzEoZ1SbOtvgGoz",
      "XmlRecebimento" =>
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><retConsReciNFe versao=\"4.00\" xmlns=\"http://www.portalfiscal.inf.br/nfe\"><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240443253315000234653000000003071110814728</chNFe><dhRecbto>2024-04-21T16:01:52-03:00</dhRecbto><nProt>135240000777778</nProt><digVal>/7vG6p1H96i+NBhbcbIBSfKalTQ=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></retConsReciNFe>",
      "XmlRecibo" =>
        "<retEnviNFe versao=\"4.00\" xmlns=\"http://www.portalfiscal.inf.br/nfe\"><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><dhRecbto>2024-04-21T16:01:52-03:00</dhRecbto><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240443253315000234653000000003071110814728</chNFe><dhRecbto>2024-04-21T16:01:52-03:00</dhRecbto><nProt>135240000777778</nProt><digVal>/7vG6p1H96i+NBhbcbIBSfKalTQ=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></retEnviNFe>"
    }
  end

  def nf_response_builder(:error) do
    %{
      "ChaveDFe" => "35240443253315000234653000000003061588389405",
      "CodStatus" => 204,
      "CodStatusLote" => 104,
      "DFeProtocolo" => "0",
      "IdAssincrono" => 1_258_789_992,
      "Motivo" => "Rejeição: Duplicidade de NF-e [nRec:351000026747736]",
      "MotivoLote" => "Lote processado",
      "TimeOutAssincrono" => 0,
      "XmlDFe" => "",
      "XmlRecebimento" =>
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><retConsReciNFe versao=\"4.00\" xmlns=\"http://www.portalfiscal.inf.br/nfe\"><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240443253315000234653000000003061588389405</chNFe><dhRecbto>2024-04-21T16:01:18-03:00</dhRecbto><nProt>135240000777774</nProt><cStat>204</cStat><xMotivo>Rejeição: Duplicidade de NF-e [nRec:351000026747736]</xMotivo></infProt></protNFe></retConsReciNFe>",
      "XmlRecibo" =>
        "<retEnviNFe versao=\"4.00\" xmlns=\"http://www.portalfiscal.inf.br/nfe\"><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><dhRecbto>2024-04-21T16:01:18-03:00</dhRecbto><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240443253315000234653000000003061588389405</chNFe><dhRecbto>2024-04-21T16:01:18-03:00</dhRecbto><nProt>135240000777774</nProt><cStat>204</cStat><xMotivo>Rejeição: Duplicidade de NF-e [nRec:351000026747736]</xMotivo></infProt></protNFe></retEnviNFe>"
    }
  end
end
