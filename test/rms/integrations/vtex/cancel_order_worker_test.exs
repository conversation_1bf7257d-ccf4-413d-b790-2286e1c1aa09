defmodule Rms.Integrations.VTEX.CancelOrderWorkerTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.CancelOrderWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)
    order = insert(:order, organization: org, external_id: "1234-5678")
    {:ok, org: org, order: order}
  end

  describe "Webhookb CancelOrderStatus Vtex" do
    test "calls when order is {canceled} in Vtex", %{
      org: org,
      order: order
    } do
      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: order.external_id
        )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn :mock_client, _ ->
        {:ok, %{"status" => "canceled"}}
      end)

      assert {:ok, _} =
               perform_job(CancelOrderWorker, %{
                 "marketplace_order_id" => "1234-#{fulfillment.id}"
               })
    end

    test "calls when order is not {canceled} in Vtex", %{
      org: org,
      order: order
    } do
      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: order.external_id
        )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn :mock_client, _ ->
        {:ok, %{"status" => "payment_approve"}}
      end)

      assert {:discard, "cannot update payment_approve"} =
               perform_job(CancelOrderWorker, %{
                 "marketplace_order_id" => "1234-#{fulfillment.id}"
               })
    end

    test "calls when there is no order", %{
      org: org,
      order: order
    } do
      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: order.external_id
        )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      reason = %{
        "error" => %{
          "code" => "OMS007",
          "message" => "Order Not Found",
          "exception" => nil
        }
      }

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn :mock_client, _ ->
        {:error, reason}
      end)

      assert {:error, ^reason} =
               perform_job(CancelOrderWorker, %{
                 "marketplace_order_id" => "1234-#{fulfillment.id}"
               })

      assert_raise(
        Ecto.NoResultsError,
        fn ->
          Rms.Commerce.Fulfillments.unsafe_get_fulfillment_by_external_reference!("1234-1234")
        end
      )
    end
  end
end
