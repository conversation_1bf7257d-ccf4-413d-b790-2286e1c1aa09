defmodule Rms.Integrations.VTEX.ClientTest do
  # Since we're updating a configuration to not use Tesla.Mock adapter
  # we can't run these tests in async mode
  use Rms.DataCase, async: false

  import Rms.VTEXIntegrationCase

  alias Rms.Integrations.VTEX.Client
  alias Rms.Integrations.VTEX.Orders.BuildParams

  setup :vtex_integration

  describe "client/2" do
    test "creates a Tesla client with sale channel middleware" do
      org = insert(:organization)
      vtex_credential = insert(:vtex_credential, organization: org)

      client = Rms.Integrations.VTEX.Client.client(vtex_credential, use_sale_channel: true)

      query_middleware =
        Tesla.Client.middleware(client)
        |> Enum.filter(fn
          {middleware, _} -> middleware == Tesla.Middleware.Query
          _ -> false
        end)

      assert length(query_middleware) == 1
      assert [{Tesla.Middleware.Query, [sc: _]}] = query_middleware
    end

    test "creates a Tesla client without sale channel middleware" do
      org = insert(:organization)
      vtex_credential = insert(:vtex_credential, organization: org)

      client = Rms.Integrations.VTEX.Client.client(vtex_credential, use_sale_channel: false)

      query_middleware =
        Tesla.Client.middleware(client)
        |> Enum.filter(fn
          {middleware, _} -> middleware == Tesla.Middleware.Query
          _ -> false
        end)

      assert Enum.empty?(query_middleware)
      assert [] = query_middleware
    end
  end

  describe "create_external_ecommerce_order/4" do
    @tag :vtex_integration
    test "creates a local pickup order", %{client: client, account_name: account_name} do
      affiliate_id = "GLL"

      org = insert(:organization)

      shipping_settings = %{
        "id" => "Retirada em Loja (2)",
        "tax" => 0,
        "name" => "Retirada em Loja (2)",
        "price" => 0,
        "lockTTL" => nil,
        "listPrice" => 0,
        "deliveryIds" => [
          %{
            "dockId" => "18c1bdc",
            "quantity" => 1,
            "courierId" => "2",
            "courierName" => "Retirada em Loja",
            "warehouseId" => "1c3cbfe",
            "kitItemDetails" => [],
            "accountCarrierName" => "bawclothinghomolog"
          }
        ],
        "polygonName" => "",
        "transitTime" => "0bd",
        "pickupPointId" => "1_2",
        "deliveryWindow" => nil,
        "pickupDistance" => 1.****************,
        "deliveryChannel" => "pickup-in-point",
        "pickupStoreInfo" => %{
          "dockId" => "18c1bdc",
          "address" => %{
            "city" => "São Paulo",
            "state" => "SP",
            "number" => "1089",
            "street" => "Avenida Roque Petroni Júnior",
            "country" => "BRA",
            "addressId" => "2",
            "reference" => nil,
            "complement" => "",
            "postalCode" => "04707-000",
            "addressType" => "pickup",
            "isDisposable" => true,
            "neighborhood" => "Jardim das Acacias",
            "receiverName" => nil,
            "geoCoordinates" => [-46.69802, -23.62317]
          },
          "friendlyName" => "Baw® Morumbi",
          "isPickupStore" => true,
          "additionalInfo" => ""
        },
        "shippingEstimate" => "0bd",
        "shippingEstimateDate" => nil,
        "availableDeliveryWindows" => []
      }

      loc = insert(:location, organization: org)

      pv =
        insert(:product_variant, product: build(:product, organization: org), organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        external_id: "1",
        source: "vtex",
        organization: org
      )

      order =
        insert(:order,
          customer: build(:customer),
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          },
          organization: org,
          location: loc
        )

      fulfillment =
        insert(:fulfillment,
          shipping_method: "local-pickup",
          order: order,
          organization: org
        )

      insert(:line_item,
        shipping_settings: shipping_settings,
        shipping_method: "local-pickup",
        fulfillment: fulfillment,
        product_variant: pv,
        organization: org,
        location: loc
      )

      fulfillment = Rms.Repo.preload(fulfillment, :line_items)

      assert {:ok, order_params} = BuildParams.execute(fulfillment)

      assert {:ok, %{"marketplaceOrderId" => order_id, "success" => true}} =
               Client.create_external_ecommerce_order(
                 client,
                 account_name,
                 affiliate_id,
                 order_params
               )

      assert is_binary(order_id)
    end

    test "returns error when vtex-order-creation flag is false" do
      {:ok, flag} = :ldclient_testdata.flag("vtex-order-creation")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([false], flag))

      affiliate_id = "GLL"
      # You can use minimal params for this test
      order_params = %{}

      assert {:error, {:kill_switch, :vtex_create_order}} =
               Client.create_external_ecommerce_order(
                 Tesla.client([]),
                 "iglu-test",
                 affiliate_id,
                 order_params
               )
    end

    test "does not returns error when vtex-order-creation flag is true" do
      {:ok, flag} = :ldclient_testdata.flag("vtex-order-creation")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations([true], flag))

      affiliate_id = "GLL"
      # You can use minimal params for this test
      order_params = %{}

      Tesla.Mock.mock(fn _env ->
        Tesla.Mock.json(%{}, status: 200)
      end)

      assert {:ok, _env} =
               Client.create_external_ecommerce_order(
                 Tesla.client([]),
                 "iglu-test",
                 affiliate_id,
                 order_params
               )
    end
  end
end
