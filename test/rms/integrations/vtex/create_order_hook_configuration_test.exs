defmodule Rms.Integrations.VTEX.CreateOrderHookConfigurationTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.Orders.CreateOrderHookConfiguration
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    cred = insert(:vtex_credential, organization: org)

    {:ok, org: org, cred: cred}
  end

  describe "process" do
    test "successfully creates an order hook configuration", %{org: org, cred: cred} do
      expect(VTEX.Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :create_order_hook_configuration, fn :mock_client, payload ->
        assert %{
                 "filter" => %{
                   "type" => "FromWorkflow",
                   "status" => ["payment-approved", "ready-for-handling"]
                 },
                 "hook" => %{
                   "url" => url,
                   "headers" => %{"x-vtex-api-appkey" => app_key}
                 }
               } = payload

        assert url =~ "/webhooks/vtex/orders/hook"
        assert app_key == cred.app_key

        {:ok, %{"id" => "test_hook_id"}}
      end)

      assert {:ok, _} = perform_job(CreateOrderHookConfiguration, %{"organization_id" => org.id})
    end
  end
end
