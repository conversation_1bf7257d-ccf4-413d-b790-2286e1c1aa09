defmodule Rms.Integrations.VTEX.Customers.CreateAddressWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.Customers.CreateAddressWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)
    {:ok, org: org}
  end

  describe "process/1" do
    test "create a address for a user_id", %{org: org} do
      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :create_document, 1, fn _, "AD", _ ->
        {:ok,
         %{
           "DocumentId" => "1",
           "Href" => "http://boboqa.vtexcommercestable.com.br/api/dataentities/AD/documents/1",
           "Id" => "AD-1"
         }}
      end)

      customer_payload = %{
        id: "cust_003",
        name: "<PERSON>",
        email: "<EMAIL>",
        organization_id: org.id,
        addresses: [
          %{
            id: "addr_004",
            receiver_name: "<PERSON>",
            city_name: nil,
            state: "MG",
            country_name: "Brasil",
            neighborhood: "Centro",
            street: "Rua dos Andradas",
            street_type: "Rua",
            number: "100",
            zip: "30180-001",
            complement: nil
          }
        ]
      }

      assert {:ok, [{:ok, _}]} =
               perform_job(CreateAddressWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id,
                 "result" => %{
                   "DocumentId" => "3",
                   "Href" =>
                     "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
                   "Id" => "CL-3"
                 }
               })
    end

    test "create more than 1 address", %{org: org} do
      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :create_document, 3, fn _, "AD", _ ->
        {:ok,
         %{
           "DocumentId" => "1",
           "Href" => "http://boboqa.vtexcommercestable.com.br/api/dataentities/AD/documents/1",
           "Id" => "AD-1"
         }}
      end)

      customer_payload = %{
        id: "cust_003",
        name: "Carlos Pereira",
        email: "<EMAIL>",
        organization_id: org.id,
        addresses: [
          %{
            id: "addr_004",
            receiver_name: "Carlos Pereira",
            city_name: nil,
            state: "MG",
            country_name: "Brasil",
            neighborhood: "Centro",
            street: "Rua dos Andradas",
            street_type: "Rua",
            number: "100",
            zip: "30180-001",
            complement: nil
          },
          %{
            id: "addr_004",
            receiver_name: "Carlos Pereira",
            city_name: nil,
            state: "MG",
            country_name: "Brasil",
            neighborhood: "Centro",
            street: "Rua dos Andradas",
            street_type: "Rua",
            number: "100",
            zip: "30180-001",
            complement: nil
          },
          %{
            id: "addr_004",
            receiver_name: "Carlos Pereira",
            city_name: nil,
            state: "MG",
            country_name: "Brasil",
            neighborhood: "Centro",
            street: "Rua dos Andradas",
            street_type: "Rua",
            number: "100",
            zip: "30180-001",
            complement: nil
          }
        ]
      }

      assert {:ok, [{:ok, _}, {:ok, _}, {:ok, _}]} =
               perform_job(CreateAddressWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id,
                 "result" => %{
                   "DocumentId" => "3",
                   "Href" =>
                     "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
                   "Id" => "CL-3"
                 }
               })
    end
  end
end
