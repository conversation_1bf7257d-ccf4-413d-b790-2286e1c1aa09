defmodule Rms.Integrations.VTEX.Customers.CreateCustomerWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.Customers.CreateCustomerWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)
    {:ok, org: org}
  end

  describe "process/1" do
    test "create a customer", %{org: org} do
      customer = insert(:customer, organization: org)
      address = insert(:address, customer: customer, organization: org)

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :create_document, 1, fn _, "CL", params ->
        assert params.firstName == customer.name |> String.split() |> List.first()
        assert params.lastName == customer.name |> String.split() |> List.last()
        assert params.email == customer.email
        assert params.phone == customer.primary_phone_number

        {:ok,
         %{
           "DocumentId" => "3",
           "Href" => "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
           "Id" => "CL-3"
         }}
      end)

      customer_payload = %{
        "id" => customer.id,
        "name" => customer.name,
        "email" => customer.email,
        "primary_phone_number" => customer.primary_phone_number,
        "document" => customer.document,
        "document_type" => customer.document_type,
        "birthdate" => customer.birthdate,
        "organization_id" => org.id,
        "addresses" => [
          %{
            "id" => address.id,
            "receiver_name" => address.receiver_name,
            "city_name" => address.city_name,
            "state" => address.state,
            "country_name" => address.country_name,
            "neighborhood" => address.neighborhood,
            "street" => address.street,
            "street_type" => address.street_type,
            "number" => address.number,
            "zip" => address.zip,
            "complement" => address.complement
          }
        ]
      }

      assert {:ok,
              %{
                "DocumentId" => "3",
                "Href" =>
                  "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
                "Id" => "CL-3"
              }} ==
               perform_job(CreateCustomerWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id
               })
    end

    test "create a customer with nil birthdate", %{org: org} do
      customer = insert(:customer, organization: org)
      address = insert(:address, customer: customer, organization: org)

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :create_document, 1, fn _, "CL", params ->
        assert params.firstName == customer.name |> String.split() |> List.first()
        assert params.lastName == customer.name |> String.split() |> List.last()
        assert params.email == customer.email
        assert params.phone == customer.primary_phone_number

        {:ok,
         %{
           "DocumentId" => "3",
           "Href" => "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
           "Id" => "CL-3"
         }}
      end)

      customer_payload = %{
        "id" => customer.id,
        "name" => customer.name,
        "email" => customer.email,
        "primary_phone_number" => customer.primary_phone_number,
        "document" => customer.document,
        "document_type" => customer.document_type,
        "birthdate" => nil,
        "organization_id" => org.id,
        "addresses" => [
          %{
            "id" => address.id,
            "receiver_name" => address.receiver_name,
            "city_name" => address.city_name,
            "state" => address.state,
            "country_name" => address.country_name,
            "neighborhood" => address.neighborhood,
            "street" => address.street,
            "street_type" => address.street_type,
            "number" => address.number,
            "zip" => address.zip,
            "complement" => address.complement
          }
        ]
      }

      assert {:ok,
              %{
                "DocumentId" => "3",
                "Href" =>
                  "http://boboqa.vtexcommercestable.com.br/api/dataentities/CL/documents/3",
                "Id" => "CL-3"
              }} ==
               perform_job(CreateCustomerWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id
               })
    end

    test "discard when customer email is nil", %{org: org} do
      customer = insert(:customer, organization: org, email: nil)

      customer_payload = %{
        "id" => customer.id,
        "name" => customer.name,
        "email" => customer.email,
        "birthdate" => customer.birthdate,
        "organization_id" => org.id,
        "addresses" => []
      }

      assert {:discard, "customer missing email"} ==
               perform_job(CreateCustomerWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id
               })
    end

    test "handle duplicated entry error", %{org: org} do
      customer = insert(:customer, organization: org)

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :create_document, 1, fn _, "CL", _params ->
        {:error, {:bad_request, %{"Message" => "duplicated entry"}}}
      end)

      customer_payload = %{
        "id" => customer.id,
        "name" => customer.name,
        "email" => customer.email,
        "primary_phone_number" => customer.primary_phone_number,
        "document" => customer.document,
        "birthdate" => customer.birthdate,
        "document_type" => customer.document_type,
        "organization_id" => org.id,
        "addresses" => []
      }

      assert {:discard, :duplicate_customer} ==
               perform_job(CreateCustomerWorker, %{
                 "customer" => customer_payload,
                 "organization_id" => org.id
               })
    end
  end
end
