defmodule Rms.Integrations.VTEX.Customers.CreateMappingWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.Customers.CreateMappingWorker
  alias Rms.Integrations

  import Rms.Factory

  describe "process/1" do
    setup do
      organization = insert(:organization)
      customer = insert(:customer, organization: organization)

      %{organization: organization, customer: customer}
    end

    test "creates customer sync mapping", %{organization: organization, customer: customer} do
      vtex_id = "123456"
      customer_id = customer.id

      args = %{
        "customer" => %{"id" => customer.id},
        "organization_id" => organization.id
      }

      args = Map.put(args, "result", %{"DocumentId" => vtex_id})
      assert {:ok, _} = perform_job(CreateMappingWorker, args)

      assert %Integrations.CustomerSyncMapping{
               customer_id: ^customer_id,
               external_id: ^vtex_id,
               source: "vtex"
             } = Integrations.get_customer_sync_mapping(organization.id, "vtex", vtex_id)
    end
  end
end
