defmodule Rms.Integrations.VTEX.Customers.ImportCustomerTest do
  use Rms.DataCase

  alias Rms.Integrations.VTEX.Customers.ImportCustomer
  alias Rms.Customers
  alias Rms.Integrations
  alias Rms.Repo

  import Rms.Factory

  setup do
    %{organization: insert(:organization)}
  end

  describe "import/2" do
    test "successfully creates a new customer with mapping", %{organization: organization} do
      customer_attrs = %{
        name: "<PERSON>",
        email: "<EMAIL>",
        vtex_id: "12345"
      }

      assert {:ok, customer} = ImportCustomer.import(organization.id, customer_attrs)
      assert customer.name == "<PERSON>"
      assert customer.email == "<EMAIL>"

      assert Repo.get_by(Customers.Customer, id: customer.id)

      mapping = Integrations.get_customer_sync_mapping(organization.id, "vtex", "12345")
      assert mapping
      assert mapping.customer_id == customer.id
    end

    test "successfully updates an existing customer", %{organization: organization} do
      {:ok, existing_customer} =
        Customers.create_customer(organization.id, %{name: "<PERSON>", email: "<EMAIL>"})

      customer_attrs = %{
        name: "<PERSON>",
        email: "<EMAIL>",
        vtex_id: "67890"
      }

      assert {:ok, updated_customer} = ImportCustomer.import(organization.id, customer_attrs)
      assert updated_customer.id == existing_customer.id
      assert updated_customer.name == "Jane Smith"

      mapping = Integrations.get_customer_sync_mapping(organization.id, "vtex", "67890")
      assert mapping
      assert mapping.customer_id == updated_customer.id
    end

    test "does not create customer if mapping fails", %{organization: organization} do
      customer_attrs = %{
        name: "Alice Brown",
        email: "<EMAIL>",
        vtex_id: nil
      }

      assert {:error, _} = ImportCustomer.import(organization.id, customer_attrs)

      # Verify that no customer was created
      refute Repo.get_by(Customers.Customer, email: "<EMAIL>")

      # Verify that no mapping was created
      refute Integrations.get_customer_sync_mapping(organization.id, "vtex", "13579")
    end

    test "successfully updates existing customer address", %{organization: organization} do
      existing_customer =
        insert(:customer,
          organization: organization,
          name: "Bob Johnson",
          email: "<EMAIL>"
        )

      insert(:address, %{
        customer: existing_customer,
        street: "123 Main St",
        number: "10",
        zip: "12345678",
        city_name: "New York",
        state: "NY",
        country_name: "USA",
        complement: "House 1"
      })

      customer_attrs = %{
        name: "Bob Johnson",
        email: "<EMAIL>",
        vtex_id: "54321",
        addresses: [
          %{
            street: "123 Main St",
            number: "10",
            zip: "12345678",
            city_name: "New York",
            state: "NY",
            country_name: "USA",
            complement: "Apt 4B"
          },
          %{
            street: "456 Elm St",
            number: "20",
            zip: "67890123",
            city_name: "Los Angeles",
            state: "CA",
            country_name: "USA",
            receiver_name: "Bob Johnson"
          }
        ]
      }

      assert {:ok, updated_customer} = ImportCustomer.import(organization.id, customer_attrs)
      assert updated_customer.id == existing_customer.id
      assert length(updated_customer.addresses) == 2

      assert [first_address, second_address] = Enum.sort_by(updated_customer.addresses, & &1.id)

      assert first_address.street == "123 Main St"
      assert first_address.number == "10"
      assert first_address.zip == "12345678"
      assert first_address.city_name == "New York"
      assert first_address.state == "NY"
      assert first_address.complement == "Apt 4B"

      assert second_address.street == "456 Elm St"
      assert second_address.number == "20"
      assert second_address.zip == "67890123"
      assert second_address.city_name == "Los Angeles"
      assert second_address.state == "CA"
      assert second_address.receiver_name == "Bob Johnson"
    end
  end
end
