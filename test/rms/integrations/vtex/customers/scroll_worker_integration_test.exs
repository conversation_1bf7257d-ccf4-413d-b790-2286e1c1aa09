defmodule Rms.Integrations.Vtex.Customers.ScrollWorkerIntegrationTest do
  use Rms.VTEXIntegrationCase
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.VTEXIntegrationCase
  import Rms.Factory

  setup :vtex_integration

  describe "process/1" do
    @tag :vtex_integration
    test "enqueues scroll worker and imports customers", %{credential: vtex_credential} do
      org = insert(:organization)
      Mox.stub_with(Rms.Integrations.VTEX.Mock, Rms.Integrations.VTEX.Client)

      insert(:vtex_credential,
        account_name: vtex_credential.account_name,
        main_account_name: vtex_credential.account_name,
        app_key: vtex_credential.app_key,
        app_token: vtex_credential.app_token,
        organization: org
      )

      assert :ok =
               perform_job(Rms.Integrations.VTEX.Customers.ScrollWorker, %{
                 "organization_id" => org.id
               })

      assert_enqueued(
        worker: Rms.Integrations.VTEX.Customers.ScrollWorker,
        args: %{"organization_id" => org.id}
      )

      customers = Rms.Customers.list_customers(org.id)
      assert length(customers) > 1, "Expected multiple customers to be imported"

      addresses = Enum.flat_map(customers, fn customer -> customer.addresses end)
      assert length(addresses) > 1, "Expected multiple addresses to be imported"
    end
  end
end
