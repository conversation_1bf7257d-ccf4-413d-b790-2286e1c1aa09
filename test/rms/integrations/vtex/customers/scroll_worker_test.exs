defmodule Rms.Integrations.VTEX.Customers.ScrollWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.Customers.ScrollWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)
    {:ok, org: org}
  end

  describe "process/1" do
    test "enqueues scroll worker", %{org: org} do
      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :search_customer_address, 4, fn _, _, _ ->
        {:ok, []}
      end)

      expect(VTEX.Mock, :scroll_masterdata, 1, fn _, _, _ ->
        {:ok,
         %{
           status: 200,
           body: [
             %{
               "id" => "1",
               "email" => "<EMAIL>",
               "firstName" => "John",
               "lastName" => "Doe",
               "document" => "123.456.789-01"
             },
             %{
               "id" => "2",
               "email" => "<EMAIL>",
               "firstName" => "Jane",
               "lastName" => "Smith",
               "document" => "770.012.694-17"
             },
             %{
               "id" => "3",
               "email" => "<EMAIL>",
               "firstName" => "Alice",
               "lastName" => "Johnson",
               "document" => "345.678.901-23"
             },
             %{
               "id" => "4",
               "email" => "<EMAIL>",
               "firstName" => "Bob",
               "lastName" => "Brown",
               "document" => "456.789.012-34"
             }
           ],
           headers: [{"X-VTEX-MD-TOKEN", "qwerwerwer"}]
         }}
      end)

      assert :ok =
               perform_job(ScrollWorker, %{
                 "organization_id" => org.id
               })

      assert_enqueued(
        worker: ScrollWorker,
        args: %{
          "token" => "qwerwerwer",
          "organization_id" => org.id
        }
      )
    end

    test "enqueues scroll worker with token", %{org: org} do
      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :scroll_masterdata, 1, fn _, _, opts ->
        assert opts[:_token] == "existing_token"

        {:ok,
         %{
           status: 200,
           body: [
             %{
               "id" => "1",
               "email" => "<EMAIL>",
               "firstName" => "John",
               "lastName" => "Doe",
               "document" => "123.456.789-01"
             }
           ]
         }}
      end)

      expect(VTEX.Mock, :search_customer_address, 1, fn _, _, _ ->
        {:ok, []}
      end)

      assert :ok =
               perform_job(ScrollWorker, %{
                 "organization_id" => org.id,
                 "token" => "existing_token"
               })

      assert_enqueued(
        worker: ScrollWorker,
        args: %{
          "token" => "existing_token",
          "organization_id" => org.id
        }
      )
    end

    test "imports customers with addresses", %{org: org} do
      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :scroll_masterdata, 1, fn _, _, _ ->
        {:ok,
         %{
           status: 200,
           body: [
             %{
               "id" => "123",
               "email" => "<EMAIL>",
               "firstName" => "John",
               "lastName" => "Doe",
               "document" => "082.534.671-17"
             },
             %{
               "id" => "4c62030e-250c-11ef-8452-0affc08f7ef5",
               "email" => "<EMAIL>",
               "firstName" => "Jane",
               "lastName" => "Smith",
               "document" => "770.012.694-17"
             },
             %{
               "id" => "125",
               "email" => "<EMAIL>",
               "firstName" => "Bob",
               "lastName" => "Williams",
               "document" => nil
             }
           ],
           headers: [{"X-VTEX-MD-TOKEN", "qwerwerwer"}]
         }}
      end)

      expect(VTEX.Mock, :search_customer_address, 3, fn
        _, "4c62030e-250c-11ef-8452-0affc08f7ef5", _ ->
          {:ok,
           [
             %{
               "city" => "São Paulo",
               "id" => "4c62030e-250c-11ef-8452-0affc08f7ef5",
               "street" => "Alameda dos Jurupis",
               "state" => "SP",
               "complement" => "43A",
               "neighborhood" => "Indianópolis",
               "number" => "1267",
               "country" => "BRA",
               "receiverName" => "Gustavo Aguiar",
               "postalCode" => "04088-004"
             }
           ]}

        _, _, _ ->
          {:ok, []}
      end)

      assert :ok =
               perform_job(ScrollWorker, %{
                 "organization_id" => org.id
               })

      customers = Rms.Customers.list_customers(org.id) |> Enum.sort_by(& &1.name)

      assert [
               %{
                 email: "<EMAIL>",
                 name: "Bob Williams",
                 document: nil,
                 document_type: nil,
                 addresses: []
               },
               %{
                 email: "<EMAIL>",
                 name: "Jane Smith",
                 document: "77001269417",
                 document_type: "CPF",
                 addresses: [
                   %{
                     city_name: "São Paulo",
                     state: "SP",
                     country_name: "BRA",
                     neighborhood: "Indianópolis",
                     street: "Alameda dos Jurupis",
                     number: "1267",
                     zip: "04088004",
                     complement: "43A",
                     receiver_name: "Gustavo Aguiar"
                   }
                 ]
               },
               %{
                 email: "<EMAIL>",
                 name: "John Doe",
                 document: "08253467117",
                 document_type: "CPF",
                 addresses: []
               }
             ] = customers
    end

    test "updates existing customers with addresses", %{org: org} do
      existing_customer_no_address =
        insert(:customer,
          organization: org,
          email: "<EMAIL>",
          name: "John Doe",
          document: "08253467117",
          document_type: "CPF"
        )

      existing_customer_different_address =
        insert(:customer,
          organization: org,
          email: "<EMAIL>",
          name: "Jane Smith",
          document: "77001269417",
          document_type: "CPF"
        )

      insert(:address,
        customer: existing_customer_different_address,
        city_name: "Rio de Janeiro",
        state: "RJ",
        country_name: "BRA",
        neighborhood: "Copacabana",
        street: "Avenida Atlântica",
        number: "1000",
        zip: "22010000",
        complement: "Apt 500",
        receiver_name: "Jane Smith"
      )

      existing_customer_same_address =
        insert(:customer,
          organization: org,
          email: "<EMAIL>",
          name: "Bob Williams",
          document: nil,
          document_type: nil
        )

      insert(:address,
        customer: existing_customer_same_address,
        city_name: "São Paulo",
        state: "SP",
        country_name: "BRA",
        neighborhood: "Indianópolis",
        street: "Alameda dos Jurupis",
        number: "1267",
        zip: "04088004",
        complement: "43A",
        receiver_name: "Gustavo Aguiar"
      )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        Tesla.client([])
      end)

      expect(VTEX.Mock, :scroll_masterdata, 1, fn _, _, _ ->
        {:ok,
         %{
           status: 200,
           body: [
             %{
               "id" => existing_customer_no_address.id,
               "email" => existing_customer_no_address.email,
               "firstName" => String.split(existing_customer_no_address.name) |> List.first(),
               "lastName" => String.split(existing_customer_no_address.name) |> List.last(),
               "document" => existing_customer_no_address.document
             },
             %{
               "id" => existing_customer_different_address.id,
               "email" => existing_customer_different_address.email,
               "firstName" =>
                 String.split(existing_customer_different_address.name) |> List.first(),
               "lastName" =>
                 String.split(existing_customer_different_address.name) |> List.last(),
               "document" => existing_customer_different_address.document
             },
             %{
               "id" => existing_customer_same_address.id,
               "email" => existing_customer_same_address.email,
               "firstName" => String.split(existing_customer_same_address.name) |> List.first(),
               "lastName" => String.split(existing_customer_same_address.name) |> List.last(),
               "document" => existing_customer_same_address.document
             }
           ],
           headers: [{"X-VTEX-MD-TOKEN", "qwerwerwer"}]
         }}
      end)

      expect(VTEX.Mock, :search_customer_address, 3, fn
        _, id, _ when id == existing_customer_different_address.id ->
          {:ok,
           [
             %{
               "city" => "São Paulo",
               "id" => existing_customer_different_address.id,
               "street" => "Alameda dos Jurupis",
               "state" => "SP",
               "complement" => "43A",
               "neighborhood" => "Indianópolis",
               "number" => "1267",
               "country" => "BRA",
               "receiverName" => "Gustavo Aguiar",
               "postalCode" => "04088-004"
             }
           ]}

        _, id, _ when id == existing_customer_same_address.id ->
          {:ok,
           [
             %{
               "city" => "São Paulo",
               "id" => "4c62030e-250c-11ef-8452-0affc08f7ef5",
               "street" => "Alameda dos Jurupis",
               "state" => "SP",
               "complement" => "43A",
               "neighborhood" => "Indianópolis",
               "number" => "1267",
               "country" => "BRA",
               "receiverName" => "Gustavo Aguiar",
               "postalCode" => "04088-004"
             }
           ]}

        _, _, _ ->
          {:ok, []}
      end)

      assert :ok =
               perform_job(ScrollWorker, %{
                 "organization_id" => org.id
               })

      updated_customers = Rms.Customers.list_customers(org.id) |> Enum.sort_by(& &1.email)

      same_email = existing_customer_same_address.email
      same_name = existing_customer_same_address.name
      same_document = existing_customer_same_address.document
      same_document_type = existing_customer_same_address.document_type

      different_email = existing_customer_different_address.email
      different_name = existing_customer_different_address.name
      different_document = existing_customer_different_address.document
      different_document_type = existing_customer_different_address.document_type

      no_address_email = existing_customer_no_address.email
      no_address_name = existing_customer_no_address.name
      no_address_document = existing_customer_no_address.document
      no_address_document_type = existing_customer_no_address.document_type

      assert [
               %{
                 email: ^same_email,
                 name: ^same_name,
                 document: ^same_document,
                 document_type: ^same_document_type,
                 addresses: [
                   %{
                     city_name: "São Paulo",
                     state: "SP",
                     country_name: "BRA",
                     neighborhood: "Indianópolis",
                     street: "Alameda dos Jurupis",
                     number: "1267",
                     zip: "04088004",
                     complement: "43A",
                     receiver_name: "Gustavo Aguiar"
                   }
                 ]
               },
               %{
                 email: ^different_email,
                 name: ^different_name,
                 document: ^different_document,
                 document_type: ^different_document_type,
                 addresses: addresses
               },
               %{
                 email: ^no_address_email,
                 name: ^no_address_name,
                 document: ^no_address_document,
                 document_type: ^no_address_document_type,
                 addresses: []
               }
             ] = updated_customers

      assert length(addresses) == 2

      assert Enum.any?(addresses, fn address ->
               address.city_name == "Rio de Janeiro" and
                 address.state == "RJ" and
                 address.country_name == "BRA" and
                 address.neighborhood == "Copacabana" and
                 address.street == "Avenida Atlântica" and
                 address.number == "1000" and
                 address.zip == "22010000" and
                 address.complement == "Apt 500" and
                 address.receiver_name == "Jane Smith"
             end)

      assert Enum.any?(addresses, fn address ->
               address.city_name == "São Paulo" and
                 address.state == "SP" and
                 address.country_name == "BRA" and
                 address.neighborhood == "Indianópolis" and
                 address.street == "Alameda dos Jurupis" and
                 address.number == "1267" and
                 address.zip == "04088004" and
                 address.complement == "43A" and
                 address.receiver_name == "Gustavo Aguiar"
             end)
    end
  end
end
