defmodule Rms.Integrations.VTEX.Orders.BuildParamsTest do
  use Rms.DataCase

  import Rms.Factory

  describe "execute/1 " do
    test "correct return order params for delivery" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "********",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "delivery",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 0,
            settings: shipping_settings1
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)
      assert {:ok, params} = Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment)
      refute params[:pickupAccountName]
    end

    test "correct return order params for local-pickup" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "********",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Retirada em Loja (1)",
        "deliveryChannel" => "pickup-in-point",
        "name" => "Retirada em Loja (1)",
        "deliveryIds" => [
          %{
            "courierId" => "2",
            "warehouseId" => "1e10a41",
            "dockId" => "1dd5911",
            "courierName" => "Retirada em Loja",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "2",
            "warehouseId" => "1c3cbfe",
            "dockId" => "18c1bdc",
            "courierName" => "Retirada em Loja",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "0bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 0,
        "listPrice" => 0,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => true,
          "friendlyName" => "Baw Birth Store",
          "address" => %{
            "addressType" => "pickup",
            "receiverName" => nil,
            "addressId" => "1",
            "isDisposable" => true,
            "postalCode" => "01131-000",
            "city" => "São Paulo",
            "state" => "SP",
            "country" => "BRA",
            "street" => "Rua dos Italianos",
            "number" => "998",
            "neighborhood" => "Bom Retiro",
            "complement" => "",
            "reference" => nil,
            "geoCoordinates" => [
              -46.64538,
              -23.52124
            ]
          },
          "additionalInfo" => "",
          "dockId" => "1dd5911"
        },
        "pickupPointId" => "1_1",
        "pickupDistance" => 6.259303569793701,
        "polygonName" => "",
        "transitTime" => "0bd"
      }

      shipping_settings2 = %{
        "id" => "Retirada em Loja (1)",
        "deliveryChannel" => "pickup-in-point",
        "name" => "Retirada em Loja (1)",
        "deliveryIds" => [
          %{
            "courierId" => "2",
            "warehouseId" => "1e10a41",
            "dockId" => "1dd5911",
            "courierName" => "Retirada em Loja",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "2",
            "warehouseId" => "1c3cbfe",
            "dockId" => "18c1bdc",
            "courierName" => "Retirada em Loja",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "0bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 0,
        "listPrice" => 0,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => true,
          "friendlyName" => "Baw Birth Store",
          "address" => %{
            "addressType" => "pickup",
            "receiverName" => nil,
            "addressId" => "1",
            "isDisposable" => true,
            "postalCode" => "01131-000",
            "city" => "São Paulo",
            "state" => "SP",
            "country" => "BRA",
            "street" => "Rua dos Italianos",
            "number" => "998",
            "neighborhood" => "Bom Retiro",
            "complement" => "",
            "reference" => nil,
            "geoCoordinates" => [
              -46.64538,
              -23.52124
            ]
          },
          "additionalInfo" => "",
          "dockId" => "1dd5911"
        },
        "pickupPointId" => "1_1",
        "pickupDistance" => 6.259303569793701,
        "polygonName" => "",
        "transitTime" => "0bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "local-pickup",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 0,
            settings: shipping_settings1
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "local-pickup",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "local-pickup",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      assert {:ok, params} =
               Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment,
                 default_pickup_account_name: "iglu-demo"
               )

      assert params[:pickupAccountName] == "iglu-demo"
    end

    test "return error when there is no client" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: nil,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "********",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "delivery",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 0,
            settings: shipping_settings1
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      assert {:error,
              %{
                reason: "A customer is required",
                stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.Customer"
              }} =
               Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment)
    end

    test "return error when there is no shipping address" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: nil
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "delivery",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 0,
            settings: shipping_settings1
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      assert {
               :error,
               %{
                 reason: "Order shipping address required",
                 stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.ShippingAddress"
               }
             } =
               Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment)
    end

    test "return error when there is no variant mapping" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "********",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "delivery",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 0,
            settings: shipping_settings1
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      assert {
               :error,
               %{
                 reason: "Mapping not found for VTEX variant",
                 stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.Items"
               }
             } =
               Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment)
    end

    test "ensure marketplacePaymentValue is the total without manual_discount plus shipping price" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "********",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      shipping_settings = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [],
        "price" => 1000,
        "listPrice" => 1000,
        "tax" => 0
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          shipping_method: "delivery",
          order: order,
          shipping_settings: %{
            organization: org,
            price: 1000,
            settings: shipping_settings
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings,
        price: Decimal.new("100.00"),
        manual_discount: Decimal.new("10.00"),
        quantity: 2
      )

      fulfillment = preload_params(fulfillment)

      assert {:ok, params} = Rms.Integrations.VTEX.Orders.BuildParams.execute(fulfillment)
      assert params[:marketplacePaymentValue] == 23_000
    end
  end

  defp preload_params(fulfillment) do
    Rms.Repo.preload(
      fulfillment,
      [
        :shipping_settings,
        order: [:customer],
        line_items: [product_variant: [:product_variant_mappings]]
      ]
    )
  end
end
