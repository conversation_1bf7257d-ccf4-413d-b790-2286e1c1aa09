defmodule Rms.Integrations.VTEX.Orders.IssueOrderTest do
  use Rms.DataCase

  import Mox

  alias Rms.Integrations.VTEX.Mock

  setup :verify_on_exit!

  describe "execute/1" do
    test "correct sends a order to vtex" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:vtex_credential, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => [],
            "accountCarrierName" => "bawclothinghomolog"
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => [],
            "accountCarrierName" => "bawclothinghomolog"
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery",
          shipping_settings: %{
            settings: shipping_settings1,
            organization: org,
            price: 0
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      expect(Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :create_external_ecommerce_order, 1, fn :mock_client, _, _, _ ->
        {:ok, vtex_response(:ok)}
      end)

      fulfillment = preload_params(fulfillment)
      assert {:ok, _} = Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
    end

    test "return a error while sending a order" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:vtex_credential, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery",
          shipping_settings: %{
            settings: shipping_settings1,
            organization: org,
            price: 0
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      expect(Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :create_external_ecommerce_order, 1, fn :mock_client, _, _, _ ->
        {:error, vtex_response(:error)}
      end)

      fulfillment = preload_params(fulfillment)
      assert {:error, _} = Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
    end

    test "return error when there is no client" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:vtex_credential, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: nil,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery",
          shipping_settings: %{
            settings: shipping_settings1,
            organization: org,
            price: 0
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      expect(Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :create_external_ecommerce_order, 0, fn :mock_client, _, _, _ ->
        {:ok, vtex_response(:ok)}
      end)

      assert {:error,
              %{
                reason: "A customer is required",
                stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.Customer"
              }} = Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
    end

    test "return error when there is no shipping address" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:vtex_credential, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: nil
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv2, organization: org, source: "vtex")

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery",
          shipping_settings: %{
            settings: shipping_settings1,
            organization: org,
            price: 0
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      expect(Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :create_external_ecommerce_order, 0, fn :mock_client, _, _, _ ->
        {:ok, vtex_response(:ok)}
      end)

      assert {
               :error,
               %{
                 reason: "Order shipping address required",
                 stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.ShippingAddress"
               }
             } =
               Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
    end

    test "return error when there is no variant mapping" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      insert(:vtex_credential, organization: org)

      customer = insert(:customer, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          location: loc,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      insert(:product_variant_mapping, product_variant: pv1, organization: org, source: "vtex")

      pv2 = insert(:product_variant, product: product, organization: org)

      shipping_settings1 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 3298,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      shipping_settings2 = %{
        "id" => "Normal",
        "deliveryChannel" => "delivery",
        "name" => "Normal",
        "deliveryIds" => [
          %{
            "courierId" => "1",
            "warehouseId" => "16f4675",
            "dockId" => "16c7f6f",
            "courierName" => "Transportadora",
            "quantity" => 30,
            "kitItemDetails" => []
          },
          %{
            "courierId" => "1",
            "warehouseId" => "1_1",
            "dockId" => "1",
            "courierName" => "Transportadora",
            "quantity" => 10,
            "kitItemDetails" => []
          }
        ],
        "shippingEstimate" => "3bd",
        "shippingEstimateDate" => nil,
        "lockTTL" => nil,
        "availableDeliveryWindows" => [],
        "deliveryWindow" => nil,
        "price" => 2250,
        "listPrice" => 3298,
        "tax" => 0,
        "pickupStoreInfo" => %{
          "isPickupStore" => false,
          "friendlyName" => nil,
          "address" => nil,
          "additionalInfo" => nil,
          "dockId" => nil
        },
        "pickupPointId" => nil,
        "pickupDistance" => 0.0,
        "polygonName" => "",
        "transitTime" => "3bd"
      }

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery",
          shipping_settings: %{
            settings: shipping_settings1,
            organization: org,
            price: 0
          }
        )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv1,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings1
      )

      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv2,
        location: loc,
        shipping_method: "delivery",
        shipping_settings: shipping_settings2
      )

      fulfillment = preload_params(fulfillment)

      expect(Mock, :client, 0, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :create_external_ecommerce_order, 0, fn :mock_client, _, _, _ ->
        {:ok, vtex_response(:ok)}
      end)

      assert {
               :error,
               %{
                 reason: "Mapping not found for VTEX variant",
                 stacktrace: "Elixir.Rms.Integrations.VTEX.Orders.BuildParams.Items"
               }
             } =
               Rms.Integrations.VTEX.IssueOrder.execute(fulfillment)
    end
  end

  defp preload_params(fulfillment) do
    Rms.Repo.preload(
      fulfillment,
      [
        :shipping_settings,
        order: [:customer],
        line_items: [product_variant: [:product_variant_mappings]]
      ]
    )
  end

  defp vtex_response(:ok) do
    %{
      "marketplaceOrderId" => "bd192c2c-b95b-4efc-983c-abea158f947e",
      "accountName" => "bawclothinghomolog",
      "code" => "SOI003",
      "flow" => "PlaceOrder",
      "success" => true,
      "operationId" => "c9fb34da-442f-4b5a-8a6b-93c88e3f7de9",
      "errors" => nil,
      "fields" => nil,
      "message" => "Order successfully enqueued"
    }
  end

  defp vtex_response(:error) do
    %{
      "marketplaceOrderId" => nil,
      "accountName" => "bawclothinghomolog",
      "code" => "EOI005",
      "flow" => "Unknown",
      "success" => false,
      "operationId" => nil,
      "errors" => [
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'items' must not be empty."
        },
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'shippingData' must not be empty."
        },
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'clientProfileData' must not be empty."
        },
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'marketplaceOrderId' must not be empty."
        },
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'marketplaceOrderStatus' must not be empty."
        },
        %{
          "source" => "Order Integration",
          "code" => nil,
          "message" => "'marketplacePaymentValue' must not be empty."
        }
      ],
      "fields" => nil,
      "message" => "A validation error occurred while trying to process the order"
    }
  end
end
