defmodule Rms.Integrations.VTEX.CreatePackingWorkerTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.CreatePackingWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)

    {:ok, org: org}
  end

  describe "process" do
    test "groups different line items into same packing", %{org: org} do
      customer = insert(:customer, organization: org)
      order = insert(:order, customer: customer, organization: org)

      loc1 = insert(:location, organization: org)
      loc2 = insert(:location, organization: org)

      dock1 = insert(:dock, location: loc1, organization: org, external_id: "16c7f6f")
      dock2 = insert(:dock, location: loc1, organization: org, external_id: "18c1bdc")

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: Ecto.UUID.autogenerate(),
          shipping_method: "local-pickup"
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)
      pv2 = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        organization: org,
        product_variant: pv1,
        external_id: "1",
        source: "vtex"
      )

      insert(:product_variant_mapping,
        organization: org,
        product_variant: pv2,
        external_id: "2",
        source: "vtex"
      )

      line_item1 =
        insert(:line_item,
          organization: org,
          fulfillment: fulfillment,
          product_variant: pv1,
          quantity: 21,
          price: 10,
          location: loc1
        )

      line_item2 =
        insert(:line_item,
          organization: org,
          fulfillment: fulfillment,
          product_variant: pv2,
          quantity: 3,
          price: 1000,
          location: loc2
        )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn _, _ ->
        {:ok,
         %{
           "shippingData" => %{
             "id" => "shippingData",
             "logisticsInfo" => [
               %{
                 "itemIndex" => 0,
                 "itemId" => "1",
                 "deliveryIds" => [
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja",
                     "dockId" => "16c7f6f",
                     "quantity" => 20,
                     "warehouseId" => "1_1",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   },
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja 2",
                     "dockId" => "18c1bdc",
                     "quantity" => 1,
                     "warehouseId" => "1c3cbfe",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   }
                 ]
               },
               %{
                 "itemIndex" => 1,
                 "itemId" => "2",
                 "deliveryIds" => [
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja 2",
                     "dockId" => "18c1bdc",
                     "quantity" => 3,
                     "warehouseId" => "1c3cbfe",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   }
                 ]
               }
             ]
           }
         }}
      end)

      assert {:ok, packings} =
               perform_job(CreatePackingWorker, %{
                 "organization_id" => org.id,
                 "fulfillment_id" => fulfillment.id
               })

      [p1, p2] =
        packings
        |> Map.values()
        |> Enum.sort_by(& &1.dock_id)

      assert p1.dock_id == dock1.id
      assert p1.courier_name == "Retirada em Loja"
      assert p1.fulfillment_id == fulfillment.id
      assert p1.organization_id == org.id
      assert p1.customer_id == customer.id
      assert p1.status == "pending"
      assert p1.shipping_method == "local-pickup"
      assert p1.total_price == Decimal.new("200")

      assert [
               pi1
             ] = p1.packing_items

      assert pi1.quantity == 20
      assert pi1.line_item_id == line_item1.id
      assert pi1.fulfillment_id == fulfillment.id

      assert p2.dock_id == dock2.id
      assert p2.courier_name == "Retirada em Loja 2"
      assert p2.fulfillment_id == fulfillment.id
      assert p2.organization_id == org.id
      assert p2.customer_id == customer.id
      assert p2.status == "pending"
      assert p2.shipping_method == "local-pickup"
      assert p2.total_price == Decimal.new("3010")

      assert [
               pi2,
               pi3
             ] = p2.packing_items |> Enum.sort_by(& &1.line_item_id)

      assert pi2.quantity == 1
      assert pi2.line_item_id == line_item1.id
      assert pi2.fulfillment_id == fulfillment.id

      assert pi3.quantity == 3
      assert pi3.line_item_id == line_item2.id
      assert pi3.fulfillment_id == fulfillment.id
    end

    test "returns {:discard, 'skip in-store packing'} for in-store fulfillment", %{org: org} do
      order = insert(:order, organization: org)

      fulfillment =
        insert(:fulfillment, order: order, organization: org, shipping_method: "in-store")

      assert {:discard, "skip in-store packing"} =
               perform_job(CreatePackingWorker, %{
                 "organization_id" => org.id,
                 "fulfillment_id" => fulfillment.id
               })
    end

    test "requires mappings for docks", %{org: org} do
      order = insert(:order, organization: org)

      loc1 = insert(:location, organization: org)

      _dock1 = insert(:dock, location: loc1, organization: org, external_id: "16c7f6f")

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: Ecto.UUID.autogenerate(),
          shipping_method: "local-pickup"
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        organization: org,
        product_variant: pv1,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv1,
        quantity: 3,
        location: loc1
      )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn _, _ ->
        {:ok,
         %{
           "shippingData" => %{
             "id" => "shippingData",
             "logisticsInfo" => [
               %{
                 "itemIndex" => 0,
                 "itemId" => "1",
                 "deliveryIds" => [
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja",
                     "dockId" => "18c1bdc",
                     "quantity" => 3,
                     "warehouseId" => "1c3cbfe",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   }
                 ]
               }
             ]
           }
         }}
      end)

      assert {:cancel, "no docks associated with org and order"} =
               perform_job(CreatePackingWorker, %{
                 "organization_id" => org.id,
                 "fulfillment_id" => fulfillment.id
               })
    end

    test "requires mappings for product variants", %{org: org} do
      order = insert(:order, organization: org)

      loc1 = insert(:location, organization: org)

      _dock1 = insert(:dock, location: loc1, organization: org, external_id: "18c1bdc")

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: Ecto.UUID.autogenerate(),
          shipping_method: "local-pickup"
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)

      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        product_variant: pv1,
        quantity: 3,
        location: loc1
      )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn _, _ ->
        {:ok,
         %{
           "shippingData" => %{
             "id" => "shippingData",
             "logisticsInfo" => [
               %{
                 "itemIndex" => 0,
                 "itemId" => "1",
                 "deliveryIds" => [
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja",
                     "dockId" => "18c1bdc",
                     "quantity" => 3,
                     "warehouseId" => "1c3cbfe",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   }
                 ]
               }
             ]
           }
         }}
      end)

      assert {:cancel, "no 1<>1 mapping between line items and external order items"} =
               perform_job(CreatePackingWorker, %{
                 "organization_id" => org.id,
                 "fulfillment_id" => fulfillment.id
               })
    end

    test "sends notification to stocker staff", %{org: org} do
      order = insert(:order, organization: org)

      loc1 = insert(:location, organization: org)
      loc2 = insert(:location, organization: org)

      staff1 = insert(:staff, organization: org)
      insert(:staff_location, staff: staff1, location: loc1, organization: org)
      insert(:staff_role, staff: staff1, seller: true, stocker: true, organization: org)

      staff2 = insert(:staff, organization: org)
      insert(:staff_location, staff: staff2, location: loc1, organization: org)
      insert(:staff_role, staff: staff2, seller: true, stocker: false, organization: org)

      staff3 = insert(:staff, organization: org)
      insert(:staff_location, staff: staff3, location: loc1, organization: org)
      insert(:staff_role, staff: staff3, seller: false, stocker: true, organization: org)

      staff4 = insert(:staff, organization: org)
      insert(:staff_location, staff: staff4, location: loc2, organization: org)
      insert(:staff_role, staff: staff4, seller: false, stocker: true, organization: org)

      dock1 = insert(:dock, location: loc1, organization: org, external_id: "16c7f6f")

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: Ecto.UUID.autogenerate(),
          shipping_method: "local-pickup"
        )

      product = insert(:product, organization: org)
      pv1 = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        organization: org,
        product_variant: pv1,
        external_id: "1",
        source: "vtex"
      )

      line_item1 =
        insert(:line_item,
          organization: org,
          fulfillment: fulfillment,
          product_variant: pv1,
          quantity: 4,
          location: loc1
        )

      expect(VTEX.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      expect(VTEX.Mock, :get_ecommerce_order, 1, fn _, _ ->
        {:ok,
         %{
           "shippingData" => %{
             "id" => "shippingData",
             "logisticsInfo" => [
               %{
                 "itemIndex" => 0,
                 "itemId" => "1",
                 "deliveryIds" => [
                   %{
                     "courierId" => "2",
                     "courierName" => "Retirada em Loja",
                     "dockId" => "16c7f6f",
                     "quantity" => 4,
                     "warehouseId" => "1c3cbfe",
                     "accountCarrierName" => "bawclothinghomolog",
                     "kitItemDetails" => []
                   }
                 ]
               }
             ]
           }
         }}
      end)

      expect(Rms.Integrations.OneSignal.Mock, :send_push_notification, 1, fn %{
                                                                               staffs_ids:
                                                                                 staff_ids
                                                                             } ->
        assert staff_ids |> Enum.sort() == [staff1.id, staff3.id]
        {:ok, "response"}
      end)

      assert {:ok, packings} =
               perform_job(CreatePackingWorker, %{
                 "organization_id" => org.id,
                 "fulfillment_id" => fulfillment.id
               })

      [p1] =
        packings
        |> Map.values()

      assert p1.dock_id == dock1.id
      assert p1.fulfillment_id == fulfillment.id
      assert p1.organization_id == org.id
      assert p1.status == "pending"
      assert p1.shipping_method == "local-pickup"
      assert is_nil(p1.staff_id)

      assert [
               pi1
             ] = p1.packing_items

      assert pi1.quantity == 4
      assert pi1.line_item_id == line_item1.id
      assert pi1.fulfillment_id == fulfillment.id
    end
  end
end
