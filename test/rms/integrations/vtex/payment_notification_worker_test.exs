defmodule Rms.Integrations.VTEX.PaymentNotificationWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.PaymentNotificationWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org, payment_system: 201)
    {:ok, org: org}
  end

  describe "snooze functionality" do
    test "snoozes the job if fulfillment is less than 30 minutes old", %{org: org} do
      order = insert(:order, organization: org)

      insert(:fulfillment,
        order: order,
        organization: org,
        external_reference: "VTEX123",
        status: "created",
        inserted_at: DateTime.utc_now()
      )

      assert {:snooze, delay} =
               perform_job(PaymentNotificationWorker, %{
                 "order_id" => order.id,
                 "organization_id" => org.id
               })

      assert delay <= 30 * 60
      assert delay > 0
    end

    test "processes the job if fulfillment is older than 30 minutes", %{org: org} do
      thirty_one_minutes_ago =
        DateTime.utc_now()
        |> DateTime.add(-31 * 60, :second)

      order = insert(:order, organization: org)

      insert(:fulfillment,
        order: order,
        organization: org,
        external_reference: "VTEX123",
        status: "created",
        inserted_at: thirty_one_minutes_ago
      )

      expect(VTEX.Mock, :client, 1, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :get_ecommerce_order, fn _, "VTEX123" ->
        {:ok,
         %{
           "paymentData" => %{
             "transactions" => [
               %{
                 "payments" => [
                   %{"id" => "payment123"}
                 ]
               }
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_notification, fn _, "VTEX123", "payment123" ->
        {:ok, %{}}
      end)

      assert {:ok, _} =
               perform_job(PaymentNotificationWorker, %{
                 "order_id" => order.id,
                 "organization_id" => org.id
               })
    end
  end

  describe "perform/1" do
    test "successfully sends payment notification", %{org: org} do
      order = insert(:order, organization: org)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: org,
          external_reference: "VTEX123",
          status: "created",
          inserted_at: DateTime.add(DateTime.utc_now(), -31 * 60, :second)
        )

      expect(VTEX.Mock, :client, 1, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :get_ecommerce_order, fn _, "VTEX123" ->
        {:ok,
         %{
           "paymentData" => %{
             "transactions" => [
               %{
                 "payments" => [
                   %{"id" => "payment123"}
                 ]
               }
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_notification, fn _, "VTEX123", "payment123" ->
        {:ok, %{}}
      end)

      assert {:ok, _} =
               perform_job(PaymentNotificationWorker, %{
                 "order_id" => order.id,
                 "organization_id" => org.id
               })

      fulfillment = Rms.Repo.reload!(fulfillment)
      assert fulfillment.status == "approved"
    end

    test "returns error when payment id is not found", %{org: org} do
      order = insert(:order, organization: org)

      insert(:fulfillment,
        order: order,
        organization: org,
        external_reference: "VTEX123",
        status: "created",
        inserted_at: DateTime.add(DateTime.utc_now(), -31 * 60, :second)
      )

      expect(VTEX.Mock, :client, 1, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :get_ecommerce_order, fn _, "VTEX123" ->
        {:ok,
         %{
           "paymentData" => %{
             "transactions" => []
           }
         }}
      end)

      assert {:error, :payment_id_not_found} =
               perform_job(PaymentNotificationWorker, %{
                 "order_id" => order.id,
                 "organization_id" => org.id
               })
    end

    test "returns error when order fetch fails", %{org: org} do
      order = insert(:order, organization: org)

      insert(:fulfillment,
        order: order,
        organization: org,
        external_reference: "VTEX123",
        status: "created",
        inserted_at: DateTime.add(DateTime.utc_now(), -31 * 60, :second)
      )

      expect(VTEX.Mock, :client, 1, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :get_ecommerce_order, fn _, "VTEX123" ->
        {:error, :not_found}
      end)

      assert {:error, :not_found} =
               perform_job(PaymentNotificationWorker, %{
                 "order_id" => order.id,
                 "organization_id" => org.id
               })
    end
  end
end
