defmodule Rms.Integrations.VTEX.PlaceOrderWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.PlaceOrderWorker
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org, payment_system: 201)
    {:ok, org: org}
  end

  describe "process/1" do
    test "successfully places an order with VTEX", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, params ->
        assert is_map(params)

        {:ok,
         %{
           "orders" => [
             %{
               "orderId" => "VTEX123",
               "orderGroup" => "group123",
               "shippingData" => %{
                 "logisticsInfo" => [
                   %{
                     "selectedSla" => "Econômica",
                     "selectedDeliveryChannel" => "delivery"
                   }
                 ]
               }
             }
           ],
           "transactionData" => %{
             "merchantTransactions" => [
               %{"transactionId" => "txn_123"}
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_information, fn _,
                                                      transaction_id,
                                                      order_id,
                                                      payment_params ->
        assert is_binary(transaction_id)
        assert order_id == "VTEX123"
        assert is_list(payment_params)
        {:ok, %{}}
      end)

      expect(VTEX.Mock, :process_order, fn _, "group123" ->
        {:ok, %{}}
      end)

      assert {:ok, _} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })

      fulfillment = Rms.Repo.reload!(fulfillment)
      assert fulfillment.external_reference == "VTEX123"
      assert fulfillment.status == "created"
    end

    test "returns error when place order fails", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, _ ->
        {:error, "VTEX API Error"}
      end)

      assert {:error, "VTEX API Error"} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })
    end

    test "returns error when payment information fails", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, _ ->
        {:ok,
         %{
           "orders" => [
             %{
               "orderId" => "VTEX123",
               "orderGroup" => "group123",
               "shippingData" => %{
                 "logisticsInfo" => [
                   %{
                     "selectedSla" => "Econômica",
                     "selectedDeliveryChannel" => "delivery"
                   }
                 ]
               }
             }
           ],
           "transactionData" => %{
             "merchantTransactions" => [
               %{"transactionId" => "txn_123"}
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_information, fn _, _, _, _ ->
        {:error, "Payment error"}
      end)

      assert {:error, "Payment error"} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })
    end

    test "returns error when process order fails", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, _ ->
        {:ok,
         %{
           "orders" => [
             %{
               "orderId" => "VTEX123",
               "orderGroup" => "group123",
               "shippingData" => %{
                 "logisticsInfo" => [
                   %{
                     "selectedSla" => "Econômica",
                     "selectedDeliveryChannel" => "delivery"
                   }
                 ]
               }
             }
           ],
           "transactionData" => %{
             "merchantTransactions" => [
               %{"transactionId" => "txn_123"}
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_information, fn _, _, _, _ ->
        {:ok, %{}}
      end)

      expect(VTEX.Mock, :process_order, fn _, _ ->
        {:error, "Process order error"}
      end)

      assert {:error, "Process order error"} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })
    end

    test "successfully places an order with coupon", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      insert(:discount,
        order: order,
        organization: org,
        type: "coupon",
        value: "TEST123"
      )

      fulfillment =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, params ->
        assert is_map(params)
        assert get_in(params, [:marketingData, :coupon]) == "TEST123"

        {:ok,
         %{
           "orders" => [
             %{
               "orderId" => "VTEX123",
               "orderGroup" => "group123",
               "shippingData" => %{
                 "logisticsInfo" => [
                   %{
                     "selectedSla" => "Econômica",
                     "selectedDeliveryChannel" => "delivery"
                   }
                 ]
               }
             }
           ],
           "transactionData" => %{
             "merchantTransactions" => [
               %{"transactionId" => "txn_123"}
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_information, fn _,
                                                      transaction_id,
                                                      order_id,
                                                      payment_params ->
        assert is_binary(transaction_id)
        assert order_id == "VTEX123"
        assert is_list(payment_params)
        {:ok, %{}}
      end)

      expect(VTEX.Mock, :process_order, fn _, "group123" ->
        {:ok, %{}}
      end)

      assert {:ok, _} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })

      fulfillment = Rms.Repo.reload!(fulfillment)
      assert fulfillment.external_reference == "VTEX123"
      assert fulfillment.status == "created"
    end

    test "successfully places an order when logisticsInfo has split delivery channel", %{org: org} do
      customer = insert(:customer, organization: org)
      staff = insert(:staff, organization: org)
      location = insert(:location, organization: org)

      order =
        insert(:order,
          customer: customer,
          organization: org,
          staff: staff,
          location: location,
          shipping_address: %{
            receiver_name: "Test Receiver",
            city_name: "Test City",
            state: "TS",
            country_name: "Test Country",
            street: "Test Street",
            zip: "12345678"
          }
        )

      fulfillment_delivery =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "delivery"
        )

      fulfillment_pickup =
        insert(:fulfillment,
          organization: org,
          order: order,
          shipping_method: "local-pickup"
        )

      product = insert(:product, organization: org)
      pv = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: pv,
        organization: org,
        external_id: "1",
        source: "vtex"
      )

      insert(:line_item,
        fulfillment: fulfillment_delivery,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "delivery",
          "id" => "Econômica"
        }
      )

      insert(:line_item,
        fulfillment: fulfillment_pickup,
        location: location,
        organization: org,
        product_variant: pv,
        quantity: 1,
        price: Decimal.new("50.00"),
        shipping_settings: %{
          "price" => 1000,
          "deliveryChannel" => "pickup-in-point",
          "id" => "in-store"
        }
      )

      expect(VTEX.Mock, :client, 2, fn _, _ -> :mock_client end)

      expect(VTEX.Mock, :place_order, fn _, params ->
        assert is_map(params)

        {:ok,
         %{
           "orders" => [
             %{
               "orderId" => "VTEX123",
               "orderGroup" => "group123",
               "shippingData" => %{
                 "logisticsInfo" => [
                   %{
                     "selectedSla" => "Econômica",
                     "selectedDeliveryChannel" => "delivery"
                   },
                   %{
                     "selectedSla" => "in-store",
                     "selectedDeliveryChannel" => "pickup-in-point"
                   }
                 ]
               }
             }
           ],
           "transactionData" => %{
             "merchantTransactions" => [
               %{"transactionId" => "txn_123"}
             ]
           }
         }}
      end)

      expect(VTEX.Mock, :send_payment_information, fn _,
                                                      transaction_id,
                                                      order_id,
                                                      payment_params ->
        assert is_binary(transaction_id)
        assert order_id == "VTEX123"
        assert is_list(payment_params)
        {:ok, %{}}
      end)

      expect(VTEX.Mock, :process_order, fn _, "group123" ->
        {:ok, %{}}
      end)

      assert {:ok, _} =
               perform_job(PlaceOrderWorker, %{
                 "organization_id" => org.id,
                 "order_id" => order.id
               })

      fulfillment_delivery = Rms.Repo.reload!(fulfillment_delivery)
      assert fulfillment_delivery.external_reference == "VTEX123"
      assert fulfillment_delivery.status == "created"

      fulfillment_pickup = Rms.Repo.reload!(fulfillment_pickup)
      assert fulfillment_pickup.external_reference == "VTEX123"
      assert fulfillment_pickup.status == "created"
    end
  end
end
