defmodule Rms.Integrations.VTEX.ProductSyncBatcherTest do
  use Rms.DataCase, async: false
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.VTEX.ProductSyncBatcher

  import Rms.Factory

  setup_all do
    ProductSyncBatcher.change_batch_size(0)
    :ok
  end

  setup do
    {:ok, org: insert(:organization)}
  end

  describe "add_notification/2" do
    test "successfully adds a notification and returns :ok", %{org: org} do
      notification = %{
        "IdSku" => "123",
        "sales_channel_id" => "1",
        "affiliate_id" => "GLL"
      }

      ProductSyncBatcher.change_flush_interval(1)
      assert :ok = ProductSyncBatcher.add_notification(org.id, notification)
    end

    test "batches notifications and creates syncs when batch size is reached", %{org: org} do
      ProductSyncBatcher.change_batch_size(5)

      notifications =
        for i <- 1..5 do
          %{
            "IdSku" => "#{i}",
            "sales_channel_id" => "1",
            "affiliate_id" => "GLL"
          }
        end

      # Add all notifications
      notification_tasks =
        notifications
        |> Enum.map(fn notification ->
          Task.async(fn ->
            ProductSyncBatcher.add_notification(org.id, notification)
          end)
        end)

      assert Enum.all?(Task.await_many(notification_tasks), &(&1 == :ok))

      # Check that one product sync was created
      assert [product_sync] =
               Rms.Repo.all(
                 from p in Rms.Integrations.ProductSync,
                   where: p.organization_id == ^org.id
               )

      assert product_sync.source == "vtex"
      product_sync_id = product_sync.id

      # Verify all jobs were created with the correct product_sync_id
      jobs = all_enqueued(worker: Rms.Workers.VTEXImportProductVariant)
      assert length(jobs) == 5

      Enum.each(jobs, fn job ->
        org_id = org.id

        assert %{
                 args: %{
                   "organization_id" => ^org_id,
                   "product_sync_id" => ^product_sync_id,
                   "sales_channel_id" => "1",
                   "affiliate_id" => "GLL"
                 }
               } = job
      end)
    end

    test "flushes notifications after timeout even if batch size not reached", %{org: org} do
      notification = %{
        "IdSku" => "123",
        "sales_channel_id" => "1",
        "affiliate_id" => "GLL"
      }

      ProductSyncBatcher.change_batch_size(1000)
      ProductSyncBatcher.change_flush_interval(10)

      assert :ok = ProductSyncBatcher.add_notification(org.id, notification)

      assert [product_sync] =
               Rms.Repo.all(
                 from p in Rms.Integrations.ProductSync,
                   where: p.organization_id == ^org.id
               )

      assert product_sync.source == "vtex"

      product_sync_id = product_sync.id
      org_id = org.id

      assert [
               %{
                 args: %{
                   "organization_id" => ^org_id,
                   "product_sync_id" => ^product_sync_id,
                   "sales_channel_id" => "1",
                   "affiliate_id" => "GLL",
                   "sku_id" => "123"
                 }
               }
             ] = all_enqueued(worker: Rms.Workers.VTEXImportProductVariant)
    end

    test "handles multiple organizations separately", %{org: org} do
      other_org = insert(:organization)

      notification1 = %{
        "IdSku" => "123",
        "sales_channel_id" => "1",
        "affiliate_id" => "GLL"
      }

      notification2 = %{
        "IdSku" => "456",
        "sales_channel_id" => "2",
        "affiliate_id" => "GLL"
      }

      ProductSyncBatcher.change_batch_size(100)
      ProductSyncBatcher.change_flush_interval(:timer.seconds(10))

      notification_tasks = [
        Task.async(fn -> ProductSyncBatcher.add_notification(org.id, notification1) end),
        Task.async(fn -> ProductSyncBatcher.add_notification(other_org.id, notification2) end)
      ]

      ProductSyncBatcher.force_flush()
      assert Enum.all?(Task.await_many(notification_tasks), &(&1 == :ok))

      assert [org1_sync] =
               Rms.Repo.all(
                 from p in Rms.Integrations.ProductSync, where: p.organization_id == ^org.id
               )

      assert [org2_sync] =
               Rms.Repo.all(
                 from p in Rms.Integrations.ProductSync, where: p.organization_id == ^other_org.id
               )

      jobs = all_enqueued(worker: Rms.Workers.VTEXImportProductVariant)
      assert length(jobs) == 2

      assert Enum.any?(jobs, fn job ->
               job.args["organization_id"] == org.id &&
                 job.args["product_sync_id"] == org1_sync.id
             end)

      assert Enum.any?(jobs, fn job ->
               job.args["organization_id"] == other_org.id &&
                 job.args["product_sync_id"] == org2_sync.id
             end)
    end
  end
end
