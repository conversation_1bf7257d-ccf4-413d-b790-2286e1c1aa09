defmodule Rms.Integrations.VTEX.UpdateOrderStatusTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Repo
  alias Rms.Integrations.VTEX.UpdateOrderStatus
  alias Rms.Integrations.VTEX

  import Mox

  setup :verify_on_exit!

  setup do
    org = insert(:organization)
    insert(:vtex_credential, organization: org)
    stub(VTEX.Mock, :client, fn _, _ -> :mock_client end)

    {:ok, org: org}
  end

  describe "process/1" do
    test "discards job if marketingData utmSource is not iglu or orderFormId is nil", %{org: org} do
      expect(VTEX.Mock, :get_ecommerce_order, fn _, _ ->
        %{
          "marketingData" => %{"utmSource" => "not_iglu"},
          "orderFormId" => nil
        }
      end)

      assert {:discard, "skip unrelated order"} =
               perform_job(UpdateOrderStatus, %{
                 "vtex_order_id" => "123",
                 "organization_id" => org.id
               })
    end

    test "updates order to paid, payment to settled, and transaction to done", %{org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          organization: org,
          transaction: transaction
        )

      epr =
        insert(:external_payment_reference, payment: payment, organization: org, partner: "vtex")

      expect(VTEX.Mock, :get_ecommerce_order, fn _, _ ->
        %{
          "marketingData" => %{"utmSource" => "iglu"},
          "orderFormId" => epr.external_id,
          "status" => "ready-for-handling"
        }
      end)

      assert :ok =
               perform_job(UpdateOrderStatus, %{
                 "vtex_order_id" => order.external_id,
                 "organization_id" => org.id
               })

      assert %{status: "paid"} = Repo.reload(order)
      assert %{status: "settled"} = Repo.reload(payment)
      assert %{status: "done"} = Repo.reload(payment.transaction)

      refute_enqueued(
        worker: Rms.Events.Handler,
        args: %{"event_name" => "order.paid"},
        prefix: "events"
      )
    end
  end
end
