defmodule Rms.IntegrationsTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Integrations
  alias Rms.Integrations.VTEX.Mock, as: VTEXMock

  setup :verify_on_exit!

  describe "create_product_sync_configuration/1" do
    setup do
      valid_attrs = %{
        field_priorities: %{
          "name" => ["shopify", "linx_pos", "vtex"],
          "price" => ["linx_pos", "shopify", "vtex"]
        },
        default_priority: ["shopify", "linx_pos", "vtex"],
        organization_id: insert(:organization).id
      }

      {:ok, valid_attrs: valid_attrs}
    end

    test "with valid data creates configuration", %{valid_attrs: valid_attrs} do
      assert {:ok, config} = Rms.Integrations.create_product_sync_configuration(valid_attrs)
      assert config.field_priorities == valid_attrs.field_priorities
      assert config.default_priority == valid_attrs.default_priority
    end

    test "with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} =
               Rms.Integrations.create_product_sync_configuration(%{field_priorities: nil})
    end
  end

  describe "get_product_sync_configuration!/1" do
    setup do
      valid_attrs = %{
        field_priorities: %{
          "name" => ["shopify", "linx_pos", "vtex"],
          "price" => ["linx_pos", "shopify", "vtex"]
        },
        default_priority: ["shopify", "linx_pos", "vtex"],
        organization_id: insert(:organization).id
      }

      {:ok, valid_attrs: valid_attrs}
    end

    test "returns configuration with valid id", %{valid_attrs: valid_attrs} do
      {:ok, config} = Rms.Integrations.create_product_sync_configuration(valid_attrs)
      assert Rms.Integrations.get_product_sync_configuration!(config.organization_id) == config
    end

    test "raises for non-existing configuration" do
      assert_raise Ecto.NoResultsError, fn ->
        Rms.Integrations.get_product_sync_configuration!(-1)
      end
    end
  end

  describe "update_product_sync_configuration/2" do
    setup do
      organization = insert(:organization)

      config =
        insert(:product_sync_configuration,
          field_priorities: %{
            "name" => ["shopify", "linx_pos", "vtex"],
            "price" => ["linx_pos", "shopify", "vtex"]
          },
          default_priority: ["shopify", "linx_pos", "vtex"],
          organization: organization
        )

      {:ok, config: config}
    end

    test "updates configuration with valid attributes", %{config: config} do
      attrs = %{
        field_priorities: %{
          "name" => ["vtex", "shopify"],
          "price" => ["shopify", "vtex"]
        },
        default_priority: ["vtex", "shopify"]
      }

      assert {:ok, updated_config} =
               Rms.Integrations.update_product_sync_configuration(config, attrs)

      assert updated_config.field_priorities == attrs.field_priorities
      assert updated_config.default_priority == attrs.default_priority
    end

    test "returns error with invalid attributes", %{config: config} do
      attrs = %{field_priorities: nil}

      assert {:error, %Ecto.Changeset{}} =
               Rms.Integrations.update_product_sync_configuration(config, attrs)
    end
  end

  describe "get_shopify_credential!/1" do
    test "returns a shopify credential" do
      user = insert(:user)
      shopify_credential = insert(:shopify_credential, organization: user.organization)
      id = shopify_credential.id
      assert %{id: ^id} = Integrations.get_shopify_credential!(user.organization_id)
    end
  end

  describe "get_vtex_credential!/1" do
    test "returns a vtex credential" do
      vtex_credential = insert(:vtex_credential)
      id = vtex_credential.id
      assert %{id: ^id} = Integrations.get_vtex_credential!(vtex_credential.organization_id)
    end
  end

  describe "get_vtex_credential_by_name!/1" do
    test "returns a vtex credential" do
      org = insert(:organization)

      vtex_credential =
        insert(:vtex_credential, account_name: "asd", main_account_name: "asd", organization: org)

      id = vtex_credential.id
      org_id = org.id

      assert %{id: ^id, organization_id: ^org_id} =
               Integrations.get_vtex_credential_by_name!("asd")
    end
  end

  describe "create_vtex_credential/2" do
    test "creates a vtex credential" do
      organization = insert(:organization)

      attrs = %{
        app_key: "some-api-key",
        app_token: "some-api-token",
        account_name: "some-seller",
        affiliate_id: "GLL",
        sales_channel_id: "1"
      }

      assert {:ok, vtex_credential} = Integrations.create_vtex_credential(organization.id, attrs)
      assert vtex_credential.organization_id == organization.id
      assert vtex_credential.app_key == "some-api-key"
      assert vtex_credential.app_token == "some-api-token"
      assert vtex_credential.account_name == "some-seller"
    end

    test "replaces current vtex credential if conflict on organization_id" do
      organization = insert(:organization)

      old_vtex_credential = insert(:vtex_credential, organization: organization)

      new_attrs = %{
        app_key: "new-api-key",
        app_token: "new-api-token",
        account_name: "new-seller",
        affiliate_id: "GLL",
        sales_channel_id: "1"
      }

      assert {:ok, new_vtex_credential} =
               Integrations.create_vtex_credential(organization.id, new_attrs)

      assert new_vtex_credential.organization_id == organization.id
      refute new_vtex_credential.app_key == old_vtex_credential.app_key
      refute new_vtex_credential.app_token == old_vtex_credential.app_token
      refute new_vtex_credential.account_name == old_vtex_credential.account_name

      assert new_vtex_credential.app_key == "new-api-key"
      assert new_vtex_credential.app_token == "new-api-token"
      assert new_vtex_credential.account_name == "new-seller"
    end
  end

  describe "create_shopify_app/2" do
    test "successfully creates a shopify app" do
      organization = insert(:organization)

      attrs = %{
        shop_domain: "iglu-demo.myshopify.com",
        client_id: "some-client-id",
        client_secret: "some-client-secret"
      }

      assert {:ok, shopify_app} = Integrations.create_shopify_app(organization.id, attrs)
      assert shopify_app.organization_id == organization.id
      assert shopify_app.shop_domain == "iglu-demo.myshopify.com"
      assert shopify_app.client_id == "some-client-id"
      assert shopify_app.client_secret == "some-client-secret"
    end

    test "fails to create a shopify app with invalid attributes" do
      organization = insert(:organization)

      attrs = %{
        shop_domain: nil,
        client_id: "some-client-id",
        client_secret: "some-client-secret"
      }

      assert {:error, changeset} = Integrations.create_shopify_app(organization.id, attrs)
      assert changeset.valid? == false
    end

    test "replaces current shopify app if conflict on organization_id" do
      organization = insert(:organization)

      old_shopify_app = insert(:shopify_app, organization: organization)

      new_attrs = %{
        shop_domain: "new-shop.myshopify.com",
        client_id: "new-client-id",
        client_secret: "new-client-secret"
      }

      assert {:ok, new_shopify_app} = Integrations.create_shopify_app(organization.id, new_attrs)

      assert new_shopify_app.organization_id == organization.id
      refute new_shopify_app.shop_domain == old_shopify_app.shop_domain
      refute new_shopify_app.client_id == old_shopify_app.client_id
      refute new_shopify_app.client_secret == old_shopify_app.client_secret

      assert new_shopify_app.shop_domain == "new-shop.myshopify.com"
      assert new_shopify_app.client_id == "new-client-id"
      assert new_shopify_app.client_secret == "new-client-secret"
    end
  end

  describe "connect_shopify/3" do
    test "successfully creates shopify credential and app" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-dmeo.myshopify.com",
        client_id: "somet client id",
        client_secret: "some client secret"
      }

      assert {:ok,
              %{shopify_credential: shopify_credential_result, shopify_app: shopify_app_result}} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)

      assert shopify_credential_result.shop == "iglu-demo.myshopify.com"
      assert shopify_credential_result.credential == "some credential"
      assert shopify_app_result.shop_domain == "iglu-dmeo.myshopify.com"
      assert shopify_app_result.client_id == "somet client id"
      assert shopify_app_result.client_secret == "some client secret"
    end

    test "creates shopify credential but not app if app creation fails" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}
      # Invalid app attributes
      shopify_app = %{
        shop_domain: nil,
        client_id: "some client id",
        client_secret: "some client secret"
      }

      assert {:ok, %{shopify_credential: shopify_credential_result, shopify_app: nil}} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)

      assert shopify_credential_result.shop == "iglu-demo.myshopify.com"
      assert shopify_credential_result.credential == "some credential"
    end

    test "fails to create shopify credential and app if credential creation fails" do
      resource = insert(:user)
      # Invalid credential attributes
      shopify_credential = %{shop: nil, credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-demo.myshopify.com",
        client_id: "some client id",
        client_secret: "some client secret"
      }

      assert {:error, :shopify_credential, _changeset, _changes} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)
    end

    test "enqueues create product feed worker" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-dmeo.myshopify.com",
        client_id: "somet client id",
        client_secret: "some client secret"
      }

      assert {:ok, _} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)

      assert_enqueued(
        worker: Rms.Workers.ShopifyCreateProductFeed,
        args: %{organization_id: resource.organization_id}
      )
    end

    test "enqueues create storefront credential worker" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-dmeo.myshopify.com",
        client_id: "somet client id",
        client_secret: "some client secret"
      }

      assert {:ok, _} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, "teste")

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorker,
        args: %{organization_id: resource.organization_id, storefront_credential: "teste"}
      )
    end

    test "enqueues register webhook for product feed full sync" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-dmeo.myshopify.com",
        client_id: "somet client id",
        client_secret: "some client secret"
      }

      assert {:ok, _} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)

      assert_enqueued(
        worker: Rms.Workers.ShopifyRegisterWebhooks,
        args: %{topic: "PRODUCT_FEEDS_FULL_SYNC", organization_id: resource.organization_id}
      )
    end

    test "enqueues register webhook for product feed incremental sync" do
      resource = insert(:user)
      shopify_credential = %{shop: "iglu-demo.myshopify.com", credential: "some credential"}

      shopify_app = %{
        shop_domain: "iglu-dmeo.myshopify.com",
        client_id: "somet client id",
        client_secret: "some client secret"
      }

      assert {:ok, _} =
               Integrations.connect_shopify(resource, shopify_credential, shopify_app, nil)

      assert_enqueued(
        worker: Rms.Workers.ShopifyRegisterWebhooks,
        args: %{
          topic: "PRODUCT_FEEDS_INCREMENTAL_SYNC",
          organization_id: resource.organization_id
        }
      )
    end
  end

  describe "connect_vtex/2" do
    test "successfully creates vtex credential" do
      organization = insert(:organization)

      vtex_credential = %{
        app_key: "some-app-key",
        app_token: "some-app-token",
        account_name: "some-account-name",
        affiliate_id: "some-affiliate-id",
        sales_channel_id: 1
      }

      assert {:ok, %{vtex_credential: vtex_credential_result}} =
               Integrations.connect_vtex(organization.id, vtex_credential)

      assert vtex_credential_result.app_key == "some-app-key"
      assert vtex_credential_result.app_token == "some-app-token"
      assert vtex_credential_result.account_name == "some-account-name"
      assert vtex_credential_result.affiliate_id == "some-affiliate-id"
      assert vtex_credential_result.sales_channel_id == 1
    end

    test "fails to create vtex credential if credential creation fails" do
      organization = insert(:organization)
      # Invalid credential attributes
      vtex_credential = %{
        app_key: nil,
        app_token: "some-app-token",
        account_name: "some-account-name",
        affiliate_id: "some-affiliate-id",
        sales_channel_id: 1
      }

      assert {:error, :vtex_credential, _changeset, _changes} =
               Integrations.connect_vtex(organization.id, vtex_credential)
    end

    test "enqueues start scroll worker" do
      organization = insert(:organization)

      vtex_credential = %{
        app_key: "some-app-key",
        app_token: "some-app-token",
        account_name: "some-account-name",
        affiliate_id: "some-affiliate-id",
        sales_channel_id: 1
      }

      assert {:ok, _} = Integrations.connect_vtex(organization.id, vtex_credential)

      assert_enqueued(
        worker: Rms.Integrations.VTEX.Customers.ScrollWorker,
        args: %{organization_id: organization.id}
      )
    end
  end

  describe "get_product_variant_mapping/3" do
    test "return a product variant mapping" do
      org = insert(:organization)

      product_variant =
        insert(:product_variant, organization: org, product: build(:product, organization: org))

      mapping =
        insert(:product_variant_mapping, product_variant: product_variant, organization: org)

      assert mapping =
               Rms.Integrations.get_product_variant_mapping(
                 org.id,
                 mapping.source,
                 mapping.external_id
               )

      assert mapping.product_variant_id == product_variant.id
    end
  end

  describe "get_inventory_item_mapping/3" do
    test "return a inventory item mapping" do
      org = insert(:organization)

      product_variant =
        insert(:product_variant, organization: org, product: build(:product, organization: org))

      mapping =
        insert(:inventory_item_mapping, product_variant: product_variant, organization: org)

      assert mapping =
               Rms.Integrations.get_inventory_item_mapping(
                 org.id,
                 mapping.source,
                 mapping.external_id
               )

      assert mapping.product_variant_id == product_variant.id
    end
  end

  describe "get_addon_mapping/3" do
    test "return a addon mapping" do
      org = insert(:organization)

      addon = insert(:addon, organization: org)

      mapping =
        insert(:addon_mapping, addon: addon, organization: org)

      assert mapping =
               Rms.Integrations.get_addon_mapping(
                 org.id,
                 mapping.source,
                 mapping.external_id
               )

      assert mapping.addon_id == addon.id
    end
  end

  describe "get_location_mapping/3" do
    test "return a location mapping" do
      org = insert(:organization)

      location = insert(:location, organization: org)

      mapping =
        insert(:location_mapping, location: location, organization: org)

      assert mapping =
               Rms.Integrations.get_location_mapping(
                 org.id,
                 mapping.source,
                 mapping.external_id
               )

      assert mapping.location_id == location.id
    end
  end

  describe "get_product_variant_external_ids/3" do
    test "return products variant mappings" do
      org = insert(:organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      mappings =
        insert_list(3, :product_variant_mapping,
          source: "vtex",
          product_variant: fn _ ->
            ExMachina.sequence(:product_variant, product_variants)
          end,
          organization: org
        )

      insert(:product_variant_mapping,
        source: "shopify",
        external_id: "dd",
        product_variant: hd(product_variants),
        organization: org
      )

      assert returned_mappings =
               Rms.Integrations.get_product_variant_external_ids(
                 org.id,
                 Enum.map(product_variants, & &1.id),
                 "vtex"
               )

      expected_external_ids = Enum.sort(Enum.map(mappings, & &1.external_id))
      returned_external_ids = Enum.sort(Map.values(returned_mappings))
      assert returned_external_ids == expected_external_ids
    end
  end

  describe "list_product_variant_mappings/3" do
    test "returns all product variant mappings for an organization with specific source and product variant ids" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      product_variant1 = insert(:product_variant, product: product, organization: org)
      product_variant2 = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: product_variant1,
        organization: org,
        source: "vtex"
      )

      insert(:product_variant_mapping,
        product_variant: product_variant2,
        organization: org,
        source: "vtex"
      )

      insert(:product_variant_mapping,
        product_variant: product_variant1,
        organization: org,
        source: "shopify"
      )

      other_org = insert(:organization)
      other_product = insert(:product, organization: other_org)
      other_variant = insert(:product_variant, product: other_product, organization: other_org)

      insert(:product_variant_mapping,
        product_variant: other_variant,
        organization: other_org,
        source: "vtex"
      )

      result =
        org.id
        |> Rms.Integrations.list_product_variant_mappings("vtex", [
          product_variant1.id,
          product_variant2.id
        ])
        |> Enum.sort_by(& &1.product_variant_id)

      assert [mapping1, mapping2] = result
      assert mapping1.product_variant_id == product_variant1.id
      assert mapping2.product_variant_id == product_variant2.id
    end

    test "returns empty list when no mappings match the criteria" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      product_variant = insert(:product_variant, product: product, organization: org)

      insert(:product_variant_mapping,
        product_variant: product_variant,
        organization: org,
        source: "shopify"
      )

      assert [] =
               Rms.Integrations.list_product_variant_mappings(org.id, "vtex", [product_variant.id])
    end
  end

  describe "get_inventory_item_external_ids/3" do
    test "return inventory item mappings" do
      org = insert(:organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      mappings =
        insert_list(3, :inventory_item_mapping,
          source: "vtex",
          product_variant: fn _ ->
            ExMachina.sequence(:product_variant, product_variants)
          end,
          organization: org
        )

      insert(:inventory_item_mapping,
        source: "shopify",
        external_id: "dd",
        product_variant: hd(product_variants),
        organization: org
      )

      assert returned_mappings =
               Rms.Integrations.get_inventory_item_external_ids(
                 org.id,
                 Enum.map(product_variants, & &1.id),
                 "vtex"
               )

      expected_external_ids = Enum.sort(Enum.map(mappings, & &1.external_id))
      returned_external_ids = Enum.sort(Map.values(returned_mappings))
      assert returned_external_ids == expected_external_ids
    end
  end

  describe "get_addon_external_ids/3" do
    test "return addons mappings" do
      org = insert(:organization)

      addons =
        insert_list(3, :addon, organization: org)

      mappings =
        insert_list(3, :addon_mapping,
          source: "vtex",
          addon: fn _ ->
            ExMachina.sequence(:addon, addons)
          end,
          organization: org
        )

      insert(:addon_mapping,
        source: "shopify",
        external_id: "dd",
        addon: hd(addons),
        organization: org
      )

      assert returned_mappings =
               Rms.Integrations.get_addon_external_ids(
                 org.id,
                 Enum.map(addons, & &1.id),
                 "vtex"
               )

      expected_external_ids = Enum.sort(Enum.map(mappings, & &1.external_id))
      returned_external_ids = Enum.sort(Map.values(returned_mappings))
      assert returned_external_ids == expected_external_ids
    end
  end

  describe "get_location_external_ids/3" do
    test "return locations mappings" do
      org = insert(:organization)

      locations =
        insert_list(3, :location, organization: org)

      mappings =
        insert_list(3, :location_mapping,
          source: "vtex",
          location: fn _ ->
            ExMachina.sequence(:addon, locations)
          end,
          organization: org
        )

      insert(:location_mapping,
        source: "shopify",
        external_id: "dd",
        location: hd(locations),
        organization: org
      )

      assert returned_mappings =
               Rms.Integrations.get_location_external_ids(
                 org.id,
                 Enum.map(locations, & &1.id),
                 "vtex"
               )

      expected_external_ids = Enum.sort(Enum.map(mappings, & &1.external_id))
      returned_external_ids = Enum.sort(Map.values(returned_mappings))
      assert returned_external_ids == expected_external_ids
    end
  end

  describe "create_payment_link/3" do
    test "calculates correct installments based on organization settings and order total" do
      org = insert(:organization)
      customer = insert(:customer, organization: org)
      location = insert(:location, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "pagarme"}
      )

      insert(:organization_setting,
        organization: org,
        key: "min_installment_amounts",
        value: %{"data" => [200, 300, 400, 500, 600]}
      )

      insert(:pagarme_credential, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: customer,
          fulfillments: [
            %{
              organization: org,
              shipping_method: "in-store",
              line_items: [
                build(:line_item,
                  organization: org,
                  location: location,
                  product_variant:
                    build(:product_variant,
                      organization: org,
                      product: build(:product, organization: org)
                    ),
                  quantity: 1,
                  price: Decimal.new("600.00")
                )
              ]
            }
          ]
        )

      expect(PagarMeClientMock, :client, fn _ -> :mock_client end)

      expect(PagarMeClientMock, :create_order, fn _client, params ->
        credit_card_installments =
          get_in(params, [
            :payments,
            Access.at(0),
            :checkout,
            :credit_card,
            :installments
          ])

        assert is_list(credit_card_installments)

        [installment1, installment2, installment3, installment4, installment5, installment6] =
          credit_card_installments

        assert %{number: 1, total: 60_000} = installment1
        assert %{number: 2, total: 60_000} = installment2
        assert %{number: 3, total: 60_000} = installment3
        assert %{number: 4, total: 60_000} = installment4
        assert %{number: 5, total: 60_000} = installment5
        assert %{number: 6, total: 60_000} = installment6

        {:ok,
         %{
           "checkouts" => [%{"payment_url" => "https://pay.mock.com/123"}],
           "id" => "plink_123"
         }}
      end)

      assert {:ok, "https://pay.mock.com/123", {"pagarme", "plink_123"}} =
               Rms.Integrations.create_payment_link(org.id, order, customer)
    end

    test "return a link to VTEX checkout" do
      org = insert(:organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert_list(3, :product_variant_mapping,
        source: "vtex",
        external_id: fn _ ->
          ExMachina.sequence(:product_variant_external_id, &to_string/1, start_at: 1)
        end,
        product_variant: fn _ ->
          ExMachina.sequence(:product_variant, product_variants)
        end,
        organization: org
      )

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "vtex"}
      )

      loc = insert(:location, organization: org)

      order =
        insert(:order,
          organization: org,
          customer: build(:customer),
          staff: build(:staff, organization: org),
          discounts: build_list(1, :discount, type: "coupon", value: "iglu95", organization: org),
          shipping_address:
            params_for(:address,
              zip: "04088004",
              city_name: "São Paulo",
              state: "SP",
              country_name: "BRA"
            ),
          fulfillments: [
            %{
              organization: org,
              shipping_method: "in-store",
              line_items:
                build_list(3, :line_item,
                  organization: org,
                  location: loc,
                  product_variant: fn _ ->
                    ExMachina.sequence(:line_item_product_variant, product_variants)
                  end
                )
            }
          ]
        )

      insert(:vtex_credential, organization: org)

      Mox.expect(VTEXMock, :client, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, fn _, _, shipping_data ->
        assert %{"selectedAddresses" => [selected_address]} = shipping_data
        assert selected_address["addressType"] == "residential"
        assert selected_address["receiverName"] == order.shipping_address.receiver_name
        assert selected_address["isDisposable"] == false
        assert selected_address["postalCode"] == order.shipping_address.zip
        assert selected_address["city"] == order.shipping_address.city_name
        assert selected_address["state"] == order.shipping_address.state
        assert selected_address["country"] == order.shipping_address.country_name
        assert selected_address["street"] == order.shipping_address.street
        assert selected_address["number"] == order.shipping_address.number
        assert selected_address["neighborhood"] == order.shipping_address.neighborhood
        assert selected_address["complement"] == order.shipping_address.complement
        assert selected_address["reference"] == nil

        assert Enum.empty?(shipping_data["logisticsInfo"])

        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_marketing_data_to_cart, fn _, received_cart_id, marketing_data ->
        assert received_cart_id == cart_id
        assert marketing_data["utmSource"] == "iglu"
        assert marketing_data["utmMedium"] == "point-of-sales"
        assert marketing_data["utmCampaign"] == order.staff.external_id
        assert marketing_data["coupon"] == "iglu95"
        {:ok, %{}}
      end)

      assert {:ok, _, {"vtex", ^cart_id}} =
               Rms.Integrations.create_payment_link(org.id, order, nil)
    end

    test "return a link to Shopify checkout" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      customer = insert(:customer, organization: org)
      order = insert(:order, organization: org, customer: customer)
      product = insert(:product, organization: org)

      pv =
        insert(:product_variant,
          product: product,
          product_variant_mappings:
            build_list(1, :product_variant_mapping, source: "shopify", organization: org),
          organization: org
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      fulfillment =
        insert(:fulfillment,
          ecommerce: "shopify",
          shipping_method: "in-store",
          order: order,
          external_reference: "123",
          organization: org
        )

      _line_item =
        insert(:line_item,
          organization: org,
          product_variant: pv,
          location: loc,
          shipping_method: "in-store",
          fulfillment: fulfillment
        )

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "shopify"}
      )

      external_id = fulfillment.external_reference

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_draft_order, fn _, ^external_id ->
        {:ok,
         %{
           "id" => external_id,
           "ready" => true,
           "status" => "OPEN",
           "invoiceUrl" => "url"
         }}
      end)

      assert {:ok, _, {"shopify", ^external_id}} =
               Rms.Integrations.create_payment_link(org.id, order, nil)
    end
  end

  describe "get_shopify_storefront_credential!/1" do
    test "returns a credential by organization_id" do
      org = insert(:organization)
      insert(:shopify_storefront_credential, organization: org)

      assert Rms.Integrations.get_shopify_storefront_credential!(org.id)
    end

    test "raise a error when ther is no credential" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Integrations.get_shopify_storefront_credential!(org.id)
      end
    end
  end

  describe "get_shopify_storefront_credential/1" do
    test "returns a credential by organization_id" do
      org = insert(:organization)
      insert(:shopify_storefront_credential, organization: org)

      assert Rms.Integrations.get_shopify_storefront_credential(org.id)
    end

    test "raise a error when ther is no credential" do
      org = insert(:organization)

      refute Rms.Integrations.get_shopify_storefront_credential(org.id)
    end
  end

  describe "create_shopify_storefront_credential/2" do
    test "create a new credential" do
      org = insert(:organization)

      attrs = %{credential_id: "IGLU_ID", credential: "IGLU"}

      assert {:ok, credential} =
               Rms.Integrations.create_shopify_storefront_credential(
                 %{organization_id: org.id},
                 attrs
               )

      assert credential.credential_id == attrs.credential_id
      assert credential.credential == attrs.credential
    end

    test "update credential when there is alredy one" do
      org = insert(:organization)
      insert(:shopify_storefront_credential, organization: org)

      attrs = %{credential_id: "IGLU_ID", credential: "IGLU"}

      assert {:ok, updated_credential} =
               Rms.Integrations.create_shopify_storefront_credential(
                 %{organization_id: org.id},
                 attrs
               )

      assert updated_credential.credential_id == attrs.credential_id
      assert updated_credential.credential == attrs.credential
    end
  end

  describe "create_location_sync/2" do
    test "correct create a location sync" do
      org = insert(:organization)

      attrs =
        %{
          external_id: "1234",
          expected_count: 1,
          message: "test",
          source: "shopify"
        }

      assert %Rms.Integrations.LocationSync{} =
               Rms.Integrations.create_location_sync!(org.id, attrs)
    end

    test "does not create a duplicated location sync" do
      org = insert(:organization)

      location_sync =
        insert(:location_sync, organization: org, source: "shopify", external_id: "123")

      attrs =
        %{
          external_id: "123",
          expected_count: 1,
          message: "test",
          source: "shopify"
        }

      assert updated_location_sync = Rms.Integrations.create_location_sync!(org.id, attrs)
      assert location_sync.id == updated_location_sync.id
    end
  end

  describe "get_location_sync/1" do
    test "correct get a location sync" do
      org = insert(:organization)

      location_sync =
        insert(:location_sync, organization: org, source: "shopify", external_id: "123")

      assert %Rms.Integrations.LocationSync{} =
               Rms.Integrations.get_location_sync!(location_sync.id)
    end
  end

  describe "create_location_sync_entry/2" do
    test "correct_create a location sync entry" do
      org = insert(:organization)

      location_sync =
        insert(:location_sync, organization: org, source: "shopify", external_id: "123")

      attrs = %{
        external_id: "123",
        status: "pending"
      }

      assert {:ok, _} = Rms.Integrations.create_location_sync_entry(org.id, location_sync, attrs)
    end
  end

  describe "create_inventory_item_mapping/1" do
    test "correct_create a inventory item mapping" do
      org = insert(:organization)

      product_variant =
        insert(:product_variant, organization: org, product: build(:product, organization: org))

      attrs = %{
        product_variant_id: product_variant.id,
        organization_id: org.id,
        external_id: "123",
        source: "shopify"
      }

      assert {:ok, _} = Rms.Integrations.create_inventory_item_mapping(attrs)
    end
  end

  describe "get_erp_credential!/1" do
    test "returns a credential by organization_id" do
      org = insert(:organization)
      insert(:erp_credential, organization: org)

      assert Rms.Integrations.get_erp_credential!(org.id)
    end

    test "raise a error when ther is no credential" do
      org = insert(:organization)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Integrations.get_erp_credential!(org.id)
      end
    end
  end

  describe "create_erp_credential/2" do
    test "create a new credential" do
      org = insert(:organization)

      attrs = %{url: "url", credential: "IGLU"}

      assert {:ok, credential} =
               Rms.Integrations.create_erp_credential(
                 %{organization_id: org.id},
                 attrs
               )

      assert credential.url == attrs.url
      assert credential.credential == attrs.credential
    end

    test "update credential when there is alredy one" do
      org = insert(:organization)
      insert(:erp_credential, organization: org)

      attrs = %{url: "IGLU_ID", credential: "IGLU"}

      assert {:ok, updated_credential} =
               Rms.Integrations.create_erp_credential(
                 %{organization_id: org.id},
                 attrs
               )

      assert updated_credential.url == attrs.url
      assert updated_credential.credential == attrs.credential
    end
  end

  describe "create_vinco_credential/2" do
    test "create a new credential" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      attrs = %{location_id: loc.id, key: "IGLU"}

      assert {:ok, credential} =
               Rms.Integrations.create_vinco_credential(
                 org.id,
                 attrs
               )

      assert credential.location_id == attrs.location_id
      assert credential.key == attrs.key
    end

    test "update credential when there is alredy one" do
      org = insert(:organization)
      loc = insert(:location, organization: org)

      credential =
        insert(:vinco_credential,
          location: loc,
          key: "IGLU_TEST",
          organization: org
        )

      attrs = %{location_id: loc.id, key: "IGLU"}

      assert {:ok, updated_credential} =
               Rms.Integrations.create_vinco_credential(
                 org.id,
                 attrs
               )

      assert updated_credential.id == credential.id
      assert updated_credential.key == attrs.key
    end
  end

  describe "import_ecommerce_product/2" do
    test "create a new product" do
      org = insert(:organization)
      insert(:shopify_credential, organization: org)

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :get_product, fn _, _ ->
        {:ok,
         %{
           "createdAt" => "2022-12-20T15:00:56Z",
           "description" =>
             "Sandália Verde | Amazônia Esta sandália verde é a linha Amazônia - uma cor que desenvolvemos para uma ação bem especial! Nós acreditamos que estamos diretamente conectados à vida na floresta. Mais do que mantê-la intocada, queremos travar uma relação de respeito com a Amazônia - e isso só é possível a partir da valorização dos direitos dos povos indígenas, que são os principais responsáveis pela proteção e preservação da Amazônia Brasileira. Por isso, 100% dos lucros da linha Amazônia serão doados à Operação Amazônia Nativa. Há 52 anos, a OPAN atua pelo fortalecimento do protagonismo indígena e pela conservação do meio ambiente. Além disso, você ainda leva pra casa: A primeira sandália de plástico vegana do Brasil A sandália mais confortável que já experimentou Uma palmilha anatômica única, desenvolvida por especialistas em palmilhas A práticidade de caminhar confortável sem abrir mão do seu estilo",
           "handle" => "sandalias-amazonia",
           "id" => "gid://shopify/Product/8056763908406",
           "images" => %{
             "edges" => [
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528254262",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1FB.jpg?v=1671548458",
                   "width" => 811
                 }
               },
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528287030",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM2FB.jpg?v=1671548459",
                   "width" => 811
                 }
               },
               %{
                 "node" => %{
                   "height" => 1024,
                   "id" => "gid://shopify/ProductImage/40038528319798",
                   "url" =>
                     "https://cdn.shopify.com/s/files/1/0695/2858/4502/products/AM1.jpg?v=1671548459",
                   "width" => 811
                 }
               }
             ]
           },
           "onlineStoreUrl" => nil,
           "options" => [
             %{
               "name" => "Size",
               "values" => ["34/35", "36/37", "38/39", "40/41", "42/43", "44/45"]
             }
           ],
           "productType" => "Sandália",
           "publishedAt" => "2024-10-01T21:33:42Z",
           "seo" => %{"description" => nil, "title" => nil},
           "tags" => [],
           "title" => "Sandálias Amazônia",
           "updatedAt" => "2024-10-02T13:50:10Z",
           "variants" => %{
             "edges" => [
               %{
                 "node" => %{
                   "barcode" => "7898535802052",
                   "compareAtPrice" => nil,
                   "createdAt" => "2022-12-20T15:00:56Z",
                   "id" => "gid://shopify/ProductVariant/44162076115254",
                   "image" => nil,
                   "inventoryPolicy" => "DENY",
                   "price" => "100.00",
                   "selectedOptions" => [%{"name" => "Size", "value" => "34/35"}],
                   "sku" => "108",
                   "title" => "34/35",
                   "updatedAt" => "2024-09-28T19:37:46Z",
                   "weight" => 0.0,
                   "weightUnit" => "KILOGRAMS"
                 }
               }
             ]
           },
           "vendor" => "iglu-demo"
         }}
      end)

      assert {:ok, _} =
               Rms.Integrations.import_ecommerce_product(org.id, "external_id", "shopify")

      assert [product] =
               Ecto.Query.preload(Rms.Commerce.Products.Product, [:product_variants])
               |> Repo.all()

      assert product.name == "Sandálias Amazônia"
      assert [variant] = product.product_variants
      assert variant.name == "Sandálias Amazônia 34/35"
      assert variant.sku == "108"

      assert product_variant_mapping =
               Repo.get_by(Rms.Integrations.ProductVariantMapping,
                 product_variant_id: variant.id,
                 source: "shopify"
               )

      assert product_variant_mapping.organization_id == variant.organization_id
      assert product_variant_mapping.product_variant_id == variant.id
    end
  end

  describe "get_shopify_additional_information/3" do
    test "return a shopify additional information" do
      org = insert(:organization)

      soi = insert(:shopify_order_information, organization: org)

      assert soi2 =
               Rms.Integrations.get_shopify_additional_information!(
                 org.id,
                 "in-store"
               )

      assert soi2.id == soi.id
    end
  end

  describe "create_shopify_additional_information/2" do
    test "create a new additional inormation" do
      org = insert(:organization)

      attrs = %{fields: ["iglu"], order_type: "delivery"}

      assert {:ok, soi} =
               Rms.Integrations.create_shopify_additional_information(
                 org.id,
                 attrs
               )

      assert soi.fields == attrs.fields
    end
  end

  describe "import_shopify_product/3" do
    setup do
      organization = insert(:organization)
      product_sync = insert(:product_sync, organization: organization)

      {:ok, organization: organization, product_sync: product_sync}
    end

    test "successfully imports a new product with variants", %{
      organization: organization,
      product_sync: product_sync
    } do
      external_product = %{
        "id" => "shopify_123",
        "title" => "Test Product",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_123",
                "sku" => "SKU123",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => nil
              }
            }
          ]
        }
      }

      assert {:ok, result} = Integrations.import_shopify_product(product_sync, external_product)

      assert %{
               product: %Rms.Commerce.Products.Product{} = product,
               archived_unused_variants: {0, nil},
               create_product_mapping: %Rms.Integrations.ProductSyncMapping{},
               product_mapping: nil
             } = result

      assert {:product_variant, "variant_123"} in Map.keys(result)
      assert {:product_variant_mapping, "variant_123"} in Map.keys(result)

      assert product.name == "Test Product"
      assert product.organization_id == organization.id

      variant = result[{:product_variant, "variant_123"}]
      assert variant.sku == "SKU123"
      assert variant.organization_id == organization.id
      assert variant.product_id == product.id

      assert product.name == "Test Product"
      assert product.organization_id == organization.id

      # Verify product mapping was created
      product_mapping =
        Integrations.get_product_mapping(organization.id, "shopify", "shopify_123")

      assert product_mapping.product_id == product.id
      assert product_mapping.external_id == "shopify_123"

      # Verify variant was created correctly
      assert variant.sku == "SKU123"
      assert variant.organization_id == organization.id
      assert variant.product_id == product.id
    end

    test "updates existing product when mapping exists", %{
      organization: organization,
      product_sync: product_sync
    } do
      existing_product = insert(:product, organization: organization, name: "Old Name")

      insert(:product_mapping,
        organization: organization,
        product: existing_product,
        source: "shopify",
        external_id: "shopify_123"
      )

      external_product = %{
        "id" => "shopify_123",
        "title" => "Updated Name",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_123",
                "sku" => "SKU123",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => nil
              }
            }
          ]
        }
      }

      assert {:ok, %{product: updated_product}} =
               Integrations.import_shopify_product(product_sync, external_product)

      assert updated_product.id == existing_product.id
      assert updated_product.name == "Updated Name"
    end

    test "archives unused variants during full sync", %{
      organization: organization,
      product_sync: product_sync
    } do
      existing_product = insert(:product, organization: organization)

      old_variant =
        insert(:product_variant, product: existing_product, organization: organization)

      insert(:product_mapping,
        organization: organization,
        product: existing_product,
        source: "shopify",
        external_id: "shopify_123"
      )

      external_product = %{
        "id" => "shopify_123",
        "title" => "Test Product",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "new_variant_123",
                "sku" => "NEW_SKU",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => nil
              }
            }
          ]
        }
      }

      assert {:ok, %{product: _product}} =
               Integrations.import_shopify_product(product_sync, external_product, :full)

      # Old variant should be archived
      updated_old_variant = Repo.reload(old_variant) |> Repo.preload(:product)
      assert updated_old_variant.archived_at != nil
    end

    test "does not archive variants during incremental sync", %{
      organization: organization,
      product_sync: product_sync
    } do
      existing_product = insert(:product, organization: organization)

      old_variant =
        insert(:product_variant, product: existing_product, organization: organization)

      insert(:product_mapping,
        organization: organization,
        product: existing_product,
        source: "shopify",
        external_id: "shopify_123"
      )

      external_product = %{
        "id" => "shopify_123",
        "title" => "Test Product",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "new_variant_123",
                "sku" => "NEW_SKU",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => nil
              }
            }
          ]
        }
      }

      assert {:ok, %{product: _product}} =
               Integrations.import_shopify_product(product_sync, external_product, :incremental)

      # Old variant should not be archived
      updated_old_variant = Repo.reload(old_variant) |> Repo.preload(:product)
      assert updated_old_variant.archived_at == nil
    end

    test "creates sync entries for successful imports", %{product_sync: product_sync} do
      external_product = %{
        "id" => "shopify_123",
        "title" => "Test Product",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_123",
                "sku" => "SKU123",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => nil
              }
            }
          ]
        }
      }

      assert {:ok, _result} = Integrations.import_shopify_product(product_sync, external_product)

      sync_entries = Repo.all(Rms.Integrations.ProductSyncEntry)
      assert length(sync_entries) == 2

      product_sync_entry = Enum.find(sync_entries, &(&1.external_id == "shopify_123"))
      assert product_sync_entry.product_sync_id == product_sync.id
      assert product_sync_entry.status == "success"

      variant_sync_entry = Enum.find(sync_entries, &(&1.external_id == "variant_123"))
      assert variant_sync_entry.product_sync_id == product_sync.id
      assert variant_sync_entry.sku == "SKU123"
      assert variant_sync_entry.status == "success"
    end

    test "respects sync configuration for existing iGlu products without Shopify mapping", %{
      organization: organization,
      product_sync: product_sync
    } do
      # Create existing iGlu product with variant
      existing_product =
        insert(:product, organization: organization, name: "Existing iGlu Product")

      existing_variant =
        insert(:product_variant,
          product: existing_product,
          organization: organization,
          sku: "IGLU_SKU",
          price: Decimal.new("19.99"),
          bar_code: "IGLU_BARCODE",
          sync_metadata: %{
            "field_sources" => %{
              "name" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"},
              "price" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"},
              "bar_code" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      # Create sync configuration that only syncs name and price
      insert(:product_sync_configuration,
        organization: organization,
        field_priorities: %{
          "name" => ["shopify"],
          "price" => ["shopify"],
          "bar_code" => ["internal", "shopify"]
        },
        default_priority: ["shopify"]
      )

      external_product = %{
        "id" => "shopify_123",
        "title" => "Shopify Product",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_123",
                "sku" => "IGLU_SKU",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "title" => "Small / Blue",
                "selectedOptions" => [],
                "barcode" => "SHOPIFY_BARCODE"
              }
            }
          ]
        }
      }

      assert {:ok, _result} = Integrations.import_shopify_product(product_sync, external_product)

      updated_variant = Repo.reload(existing_variant) |> Repo.preload(:product)

      # Fields in sync config should be updated
      assert updated_variant.price == Decimal.new("29.99")
      assert updated_variant.product.name == "Shopify Product"

      # Fields not in sync config should not be updated
      assert updated_variant.bar_code == "IGLU_BARCODE"
    end

    test "respects sync configuration for product with mapping", %{
      organization: organization,
      product_sync: product_sync
    } do
      # Create existing product with variant and mapping
      existing_product = insert(:product, organization: organization, name: "Old Name")

      existing_variant =
        insert(:product_variant,
          product: existing_product,
          organization: organization,
          sku: "SAME_SKU",
          price: Decimal.new("19.99"),
          bar_code: "OLD_BARCODE",
          sync_metadata: %{
            "field_sources" => %{
              "name" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"},
              "price" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"},
              "bar_code" => %{"source" => "internal", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      insert(:product_mapping,
        organization: organization,
        product: existing_product,
        source: "shopify",
        external_id: "shopify_123"
      )

      # Create sync configuration that only syncs price and name
      insert(:product_sync_configuration,
        organization: organization,
        field_priorities: %{
          "price" => ["shopify"],
          "name" => ["shopify"],
          "bar_code" => ["internal", "shopify"]
        },
        default_priority: ["internal"]
      )

      external_product = %{
        "id" => "shopify_123",
        "title" => "New Name",
        "images" => %{
          "edges" => [
            %{
              "node" => %{
                "url" => "https://example.com/image1.jpg"
              }
            }
          ]
        },
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "id" => "variant_123",
                "sku" => "SAME_SKU",
                "price" => %{
                  "amount" => "29.99"
                },
                "inventory_quantity" => 10,
                "barcode" => "NEW_BARCODE",
                "selectedOptions" => []
              }
            }
          ]
        }
      }

      assert {:ok, _result} = Integrations.import_shopify_product(product_sync, external_product)

      updated_variant = Repo.reload(existing_variant) |> Repo.preload(:product)

      # Price should be updated as per sync config
      assert updated_variant.price == Decimal.new("29.99")

      # Other fields should remain unchanged
      assert updated_variant.sku == "SAME_SKU"
      assert updated_variant.bar_code == "OLD_BARCODE"

      updated_product = Repo.reload(existing_product)
      # Name should be updated as per sync config
      assert updated_product.name == "New Name"
    end
  end

  describe "get_pagarme_credential!/2" do
    test "returns location-specific credential when it exists" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      location_credential = insert(:pagarme_credential, organization: org, location: location)
      _credential = insert(:pagarme_credential, organization: org, location: nil)

      result = Integrations.get_pagarme_credential!(org.id, location.id)
      assert result.id == location_credential.id
    end

    test "returns the rigth location-specific credential when it exists" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      location2 = insert(:location, organization: org)
      _location_credential = insert(:pagarme_credential, organization: org, location: location)
      location_credential2 = insert(:pagarme_credential, organization: org, location: location2)
      _credential = insert(:pagarme_credential, organization: org, location: nil)

      result = Integrations.get_pagarme_credential!(org.id, location2.id)
      assert result.id == location_credential2.id
    end

    test "returns general credential when no location-specific exists" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      general_credential = insert(:pagarme_credential, organization: org, location: nil)

      result = Integrations.get_pagarme_credential!(org.id, location.id)
      assert result.id == general_credential.id
    end

    test "raises when no credential exists" do
      org = insert(:organization)
      location = insert(:location, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        Integrations.get_pagarme_credential!(org.id, location.id)
      end
    end
  end

  describe "get_product_variant_ids_from_external_ids/3" do
    test "returns product variant ids mapped to their external ids" do
      org = insert(:organization)

      product_variants =
        insert_list(3, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      mappings =
        insert_list(3, :product_variant_mapping,
          source: "shopify",
          external_id: fn _ ->
            ExMachina.sequence(
              :product_variant_external_id,
              &"gid://shopify/ProductVariant/#{&1}",
              start_at: 1
            )
          end,
          product_variant: fn _ ->
            ExMachina.sequence(:product_variant, product_variants)
          end,
          organization: org
        )

      # Insert a mapping with a different source
      insert(:product_variant_mapping,
        source: "vtex",
        external_id: "some-vtex-id",
        product_variant: hd(product_variants),
        organization: org
      )

      # Get external IDs to query
      external_ids = Enum.map(mappings, & &1.external_id)

      result =
        Rms.Integrations.get_product_variant_ids_from_external_ids(
          org.id,
          external_ids,
          "shopify"
        )

      # The result should be a map of external_id => product_variant_id
      assert map_size(result) == 3

      # Verify each mapping is correct
      Enum.each(mappings, fn mapping ->
        assert result[mapping.external_id] == mapping.product_variant_id
      end)
    end

    test "returns empty map when no mappings match" do
      org = insert(:organization)

      product_variant =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      # Insert a mapping with a different source
      insert(:product_variant_mapping,
        source: "vtex",
        external_id: "some-vtex-id",
        product_variant: product_variant,
        organization: org
      )

      result =
        Rms.Integrations.get_product_variant_ids_from_external_ids(
          org.id,
          ["gid://shopify/ProductVariant/1"],
          "shopify"
        )

      assert result == %{}
    end

    test "only returns mappings for the specified organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)

      pv1 =
        insert(:product_variant, organization: org1, product: build(:product, organization: org1))

      pv2 =
        insert(:product_variant, organization: org2, product: build(:product, organization: org2))

      external_id = "gid://shopify/ProductVariant/1"

      insert(:product_variant_mapping,
        source: "shopify",
        external_id: external_id,
        product_variant: pv1,
        organization: org1
      )

      insert(:product_variant_mapping,
        source: "shopify",
        external_id: external_id,
        product_variant: pv2,
        organization: org2
      )

      result1 =
        Rms.Integrations.get_product_variant_ids_from_external_ids(
          org1.id,
          [external_id],
          "shopify"
        )

      result2 =
        Rms.Integrations.get_product_variant_ids_from_external_ids(
          org2.id,
          [external_id],
          "shopify"
        )

      assert result1[external_id] == pv1.id
      assert result2[external_id] == pv2.id
    end
  end

  describe "get_product_variant_details_from_external_ids/3" do
    test "returns product variant details (id and sku) mapped to their external ids" do
      org = insert(:organization)

      pv1_sku = "SKU1"
      pv2_sku = "SKU2"
      # Test with a nil SKU
      pv3_sku = nil

      pv1 =
        insert(:product_variant,
          sku: pv1_sku,
          organization: org,
          product: build(:product, organization: org)
        )

      pv2 =
        insert(:product_variant,
          sku: pv2_sku,
          organization: org,
          product: build(:product, organization: org)
        )

      pv3 =
        insert(:product_variant,
          sku: pv3_sku,
          organization: org,
          product: build(:product, organization: org)
        )

      mapping1 =
        insert(:product_variant_mapping,
          source: "shopify",
          external_id: "gid://shopify/ProductVariant/1",
          product_variant: pv1,
          organization: org
        )

      mapping2 =
        insert(:product_variant_mapping,
          source: "shopify",
          external_id: "gid://shopify/ProductVariant/2",
          product_variant: pv2,
          organization: org
        )

      mapping3 =
        insert(:product_variant_mapping,
          source: "shopify",
          external_id: "gid://shopify/ProductVariant/3",
          product_variant: pv3,
          organization: org
        )

      # Insert a mapping with a different source to ensure it's filtered out
      insert(:product_variant_mapping,
        source: "vtex",
        external_id: "some-vtex-id",
        product_variant: pv1,
        organization: org
      )

      external_ids = [
        mapping1.external_id,
        mapping2.external_id,
        mapping3.external_id,
        "gid://shopify/ProductVariant/non_existent"
      ]

      result =
        Rms.Integrations.get_product_variant_details_from_external_ids(
          org.id,
          external_ids,
          "shopify"
        )

      assert map_size(result) == 3
      assert result[mapping1.external_id] == %{id: pv1.id, sku: pv1.sku}
      assert result[mapping2.external_id] == %{id: pv2.id, sku: pv2.sku}
      assert result[mapping3.external_id] == %{id: pv3.id, sku: pv3.sku}
      assert result[mapping1.external_id].sku == pv1_sku
      assert result[mapping2.external_id].sku == pv2_sku
      assert result[mapping3.external_id].sku == pv3_sku
    end

    test "returns empty map when no mappings match the source" do
      org = insert(:organization)

      pv =
        insert(:product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert(:product_variant_mapping,
        # Different source
        source: "vtex",
        external_id: "gid://shopify/ProductVariant/1",
        product_variant: pv,
        organization: org
      )

      result =
        Rms.Integrations.get_product_variant_details_from_external_ids(
          org.id,
          ["gid://shopify/ProductVariant/1"],
          # Querying for shopify
          "shopify"
        )

      assert result == %{}
    end

    test "returns empty map when no external_ids match" do
      org = insert(:organization)

      result =
        Rms.Integrations.get_product_variant_details_from_external_ids(
          org.id,
          ["gid://shopify/ProductVariant/non_existent_id"],
          "shopify"
        )

      assert result == %{}
    end

    test "only returns mappings for the specified organization" do
      org1 = insert(:organization)
      org2 = insert(:organization)

      pv1 =
        insert(:product_variant,
          sku: "SKU_ORG1",
          organization: org1,
          product: build(:product, organization: org1)
        )

      pv2 =
        insert(:product_variant,
          sku: "SKU_ORG2",
          organization: org2,
          product: build(:product, organization: org2)
        )

      external_id = "gid://shopify/ProductVariant/shared"

      insert(:product_variant_mapping,
        source: "shopify",
        external_id: external_id,
        product_variant: pv1,
        organization: org1
      )

      insert(:product_variant_mapping,
        source: "shopify",
        external_id: external_id,
        product_variant: pv2,
        organization: org2
      )

      result1 =
        Rms.Integrations.get_product_variant_details_from_external_ids(
          org1.id,
          [external_id],
          "shopify"
        )

      result2 =
        Rms.Integrations.get_product_variant_details_from_external_ids(
          org2.id,
          [external_id],
          "shopify"
        )

      assert result1[external_id] == %{id: pv1.id, sku: "SKU_ORG1"}
      assert result2[external_id] == %{id: pv2.id, sku: "SKU_ORG2"}
    end
  end
end
