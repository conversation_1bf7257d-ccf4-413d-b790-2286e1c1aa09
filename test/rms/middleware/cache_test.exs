defmodule Rms.Middleware.CacheTest do
  use ExUnit.Case, async: true
  alias <PERSON><PERSON>.Env

  import Mox

  describe "call/3" do
    setup do
      env = %Env{body: "test_body"}
      next = []
      {:ok, env: env, next: next}
    end

    test "caches request when should_cache_request? is true", %{env: env, next: next} do
      opts = [organization_id: 1]
      expect(Rms.Clients.FeatureFlag.Mock, :should_cache_request?, fn _ -> true end)
      assert {:ok, %Env{body: "test_body"}} = Rms.Middleware.Cache.call(env, next, opts)
    end
  end
end
