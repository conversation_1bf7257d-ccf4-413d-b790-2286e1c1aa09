defmodule Rms.RepoTest do
  use Rms.DataCase, async: true
  alias Rms.Repo

  describe "transaction_with/2" do
    test "successfully executes a transaction with {:ok, result}" do
      result = Repo.transaction_with(fn -> {:ok, "success"} end)
      assert result == {:ok, "success"}
    end

    test "successfully executes a transaction with :ok" do
      result = Repo.transaction_with(fn -> :ok end)
      assert result == {:ok, :ok}
    end

    test "rolls back transaction on {:error, reason}" do
      result = Repo.transaction_with(fn -> {:error, "failure"} end)
      assert {:error, "failure"} = result
    end

    test "raises ArgumentError for invalid return values" do
      assert_raise ArgumentError,
                   ~r/expected to return {:ok, _} or {:error, _}, got: "invalid"/,
                   fn ->
                     Repo.transaction_with(fn -> "invalid" end)
                   end
    end

    test "supports nested transactions" do
      result =
        Repo.transaction_with(fn ->
          {:ok, nested_result} = Repo.transaction_with(fn -> {:ok, "nested"} end)
          {:ok, {"parent", nested_result}}
        end)

      assert result == {:ok, {"parent", "nested"}}
    end

    test "retries once on serialization failure" do
      # We'll simulate a serialization failure on first attempt
      attempt = :atomics.new(1, [])

      result =
        Repo.transaction_with(fn ->
          case :atomics.add_get(attempt, 1, 1) do
            1 ->
              raise %Postgrex.Error{
                postgres: %{code: :serialization_failure, severity: "error"},
                message: "serialization_failure"
              }

            2 ->
              {:ok, "success after retry"}

            _ ->
              raise "Should not retry more than once"
          end
        end)

      assert result == {:ok, "success after retry"}
    end

    test "retries once on stale entry error" do
      # We'll simulate a stale entry error on first attempt
      attempt = :atomics.new(1, [])

      result =
        Repo.transaction_with(fn ->
          case :atomics.add_get(attempt, 1, 1) do
            1 ->
              raise Ecto.StaleEntryError,
                message: "stale entry",
                changeset: %Ecto.Changeset{},
                action: :update

            2 ->
              {:ok, "success after retry"}

            _ ->
              raise "Should not retry more than once"
          end
        end)

      assert result == {:ok, "success after retry"}
    end

    test "does not retry on other errors" do
      # We'll track the number of attempts
      attempt = :atomics.new(1, [])

      assert_raise RuntimeError, "always fails", fn ->
        Repo.transaction_with(fn ->
          :atomics.add(attempt, 1, 1)
          raise "always fails"
        end)
      end

      # Verify it only tried once
      assert :atomics.get(attempt, 1) == 1
    end
  end
end
