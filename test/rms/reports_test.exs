defmodule Rms.ReportsTest do
  use Rms.DataCase

  alias Rms.Reports

  describe "retail_transactions_report/3" do
    test "returns transactions within the specified date range" do
      # Create test data
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      staff = insert(:staff, organization: organization)

      # Create an order with payment for today
      order = insert(:order, organization: organization, location: location, staff: staff)
      transaction = insert(:transaction, organization: organization, order: order)

      today_payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          amount: Decimal.new("100.00"),
          reference_at: DateTime.new!(Date.utc_today(), ~T[04:00:00], "Etc/UTC"),
          status: "settled"
        )

      # Create an order with payment for yesterday (at 15:00 UTC / 12:00 Brazil time)
      yesterday = Date.utc_today() |> Date.add(-1)
      yesterday_datetime = DateTime.new!(yesterday, ~T[15:00:00], "Etc/UTC")

      old_order = insert(:order, organization: organization, location: location, staff: staff)
      old_transaction = insert(:transaction, organization: organization, order: old_order)

      _yesterday_payment =
        insert(:payment,
          transaction: old_transaction,
          organization: organization,
          amount: Decimal.new("50.00"),
          status: "settled",
          reference_at: yesterday_datetime
        )

      # Test with today's date (at 15:00 UTC / 12:00 Brazil time)
      today = Date.utc_today()

      today_payment = %{
        today_payment
        | # 12:00 Brazil time
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
      }

      result = Reports.retail_transactions_report(organization.id, today, today)

      # Should only include today's transaction
      assert length(result) == 1
      transaction_data = List.first(result)
      assert transaction_data.order_id == transaction.order_id
      assert Decimal.equal?(transaction_data.amount, today_payment.amount)

      # Test with date range including yesterday and today
      yesterday_date = Date.add(today, -1)

      result = Reports.retail_transactions_report(organization.id, yesterday_date, today)

      # Should include both transactions
      assert length(result) == 2

      # Test with location filter
      result =
        Reports.retail_transactions_report(organization.id, yesterday_date, today, location.id)

      # Should still include both transactions as they're for the same location
      assert length(result) == 2

      # Create another location and order
      other_location = insert(:location, organization: organization)

      other_order =
        insert(:order, organization: organization, location: other_location, staff: staff)

      other_transaction = insert(:transaction, organization: organization, order: other_order)

      _other_payment =
        insert(:payment,
          transaction: other_transaction,
          organization: organization,
          amount: Decimal.new("75.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[04:00:00], "Etc/UTC")
        )

      # Test with original location filter
      result =
        Reports.retail_transactions_report(organization.id, yesterday_date, today, location.id)

      # Should only include transactions for the specified location
      assert length(result) == 2
      assert Enum.all?(result, fn tx -> tx.location_name == location.name end)

      # Create another organization with transactions
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)
      other_org_order = insert(:order, organization: other_org, location: other_org_location)

      other_org_transaction =
        insert(:transaction, organization: other_org, order: other_org_order)

      _other_org_payment =
        insert(:payment,
          transaction: other_org_transaction,
          organization: other_org,
          amount: Decimal.new("300.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[04:00:00], "Etc/UTC")
        )

      # Test that organization filtering works
      result = Reports.retail_transactions_report(organization.id, yesterday_date, today)

      # Should only include transactions for the specified organization
      assert length(result) == 3

      assert Enum.all?(result, fn tx ->
               tx.order_id == transaction.order_id ||
                 tx.order_id == old_transaction.order_id ||
                 tx.order_id == other_transaction.order_id
             end)

      # Test with other organization
      result = Reports.retail_transactions_report(other_org.id, yesterday_date, today)

      # Should only include transactions for the other organization
      assert length(result) == 1
      transaction_data = List.first(result)
      assert transaction_data.order_id == other_org_transaction.order_id
    end
  end

  describe "retail_staff_transactions_report/4" do
    test "returns staff transactions for the specified organization and date range" do
      # Create test data for two organizations
      org1 = insert(:organization)
      org2 = insert(:organization)

      # Create locations and staff for each organization
      location1 = insert(:location, organization: org1)
      staff1 = insert(:staff, name: "Staff Org1", organization: org1)

      location2 = insert(:location, organization: org2)
      staff2 = insert(:staff, name: "Staff Org2", organization: org2)

      # Create orders and payments for org1
      order1 =
        insert(:order, organization: org1, location: location1, staff: staff1, status: "paid")

      transaction1 = insert(:transaction, organization: org1, order: order1, status: "done")

      _payment1 =
        insert(:payment,
          transaction: transaction1,
          organization: org1,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[03:00:00], "Etc/UTC")
        )

      # Create orders and payments for org2
      order2 =
        insert(:order, organization: org2, location: location2, staff: staff2, status: "paid")

      transaction2 = insert(:transaction, organization: org2, order: order2, status: "done")

      _payment2 =
        insert(:payment,
          transaction: transaction2,
          organization: org2,
          amount: Decimal.new("200.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[03:00:00], "Etc/UTC")
        )

      today = Date.utc_today()

      # Test org1 report
      result = Reports.retail_staff_transactions_report(org1.id, today, today)

      # Should only include org1 data
      assert length(result) == 1
      staff_data = List.first(result)
      assert staff_data.staff_name == "Staff Org1"
      assert staff_data.location_name == location1.name
      assert staff_data.orders == 1
      assert Decimal.equal?(Decimal.new(staff_data.received), Decimal.new("100.00"))

      # Test org2 report
      result = Reports.retail_staff_transactions_report(org2.id, today, today)

      # Should only include org2 data
      assert length(result) == 1
      staff_data = List.first(result)
      assert staff_data.staff_name == "Staff Org2"
      assert staff_data.location_name == location2.name
      assert staff_data.orders == 1
      assert Decimal.equal?(Decimal.new(staff_data.received), Decimal.new("200.00"))

      # Test with location filter
      result = Reports.retail_staff_transactions_report(org1.id, today, today, location1.id)

      # Should include org1 data filtered by location
      assert length(result) == 1
      staff_data = List.first(result)
      assert staff_data.location_name == location1.name
      assert Decimal.equal?(staff_data.effective_amount, staff_data.amount)
      assert Decimal.equal?(staff_data.average, staff_data.amount)
      staff3 = insert(:staff, name: "Another Staff Org1", organization: org1)

      order3 =
        insert(:order, organization: org1, location: location1, staff: staff3, status: "paid")

      transaction3 = insert(:transaction, organization: org1, order: order3, status: "done")

      _payment3 =
        insert(:payment,
          transaction: transaction3,
          organization: org1,
          amount: Decimal.new("150.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[03:00:00], "Etc/UTC")
        )

      canceled_order =
        insert(:order, organization: org1, location: location1, staff: staff1, status: "canceled")

      canceled_transaction =
        insert(:transaction, organization: org1, order: canceled_order, status: "canceled")

      _canceled_payment =
        insert(:payment,
          transaction: canceled_transaction,
          organization: org1,
          amount: Decimal.new("75.00"),
          status: "canceled"
        )

      result = Reports.retail_staff_transactions_report(org1.id, today, today, location1.id)
      staff1_data = Enum.find(result, fn data -> data.staff_name == staff1.name end)

      assert Decimal.equal?(staff1_data.effective_amount, Decimal.new("100.00"))
      assert staff1_data.orders == 1
      assert Decimal.equal?(staff1_data.average, Decimal.new("100.00"))

      # Test org1 report again
      result = Reports.retail_staff_transactions_report(org1.id, today, today)

      # Should include both staff from org1
      assert length(result) == 2

      # Verify staff names
      staff_names = Enum.map(result, fn r -> r.staff_name end)
      assert "Staff Org1" in staff_names
      assert "Another Staff Org1" in staff_names
    end
  end

  describe "retail_stores_report/4" do
    test "returns store reports for the specified organization and date range" do
      # Create test data for two organizations
      org1 = insert(:organization)
      org2 = insert(:organization)

      # Create locations for each organization
      location1 = insert(:location, name: "Store Org1", organization: org1)
      location2 = insert(:location, name: "Store Org2", organization: org2)

      # Create regular orders and payments for org1
      order1 = insert(:order, organization: org1, location: location1, status: "paid")
      transaction1 = insert(:transaction, organization: org1, order: order1, status: "done")

      _payment1 =
        insert(:payment,
          transaction: transaction1,
          status: "settled",
          organization: org1,
          amount: Decimal.new("100.00"),
          reference_at: DateTime.new!(Date.utc_today(), ~T[04:00:00], "Etc/UTC")
        )

      # Create another regular order for org1
      order2 = insert(:order, organization: org1, location: location1, status: "paid")
      transaction2 = insert(:transaction, organization: org1, order: order2, status: "done")

      _payment2 =
        insert(:payment,
          transaction: transaction2,
          organization: org1,
          status: "settled",
          amount: Decimal.new("200.00"),
          # 12:00 Brazil time
          reference_at: DateTime.new!(Date.utc_today(), ~T[03:00:00], "Etc/UTC")
        )

      # Create a canceled order for org1
      canceled_order = insert(:order, organization: org1, location: location1, status: "canceled")
      canceled_transaction = insert(:transaction, organization: org1, order: canceled_order)

      _canceled_payment =
        insert(:payment,
          transaction: canceled_transaction,
          organization: org1,
          amount: Decimal.new("150.00"),
          status: "canceled"
        )

      # Create orders and payments for org2
      order3 = insert(:order, organization: org2, location: location2, status: "paid")
      transaction3 = insert(:transaction, organization: org2, order: order3, status: "done")

      _payment3 =
        insert(:payment,
          transaction: transaction3,
          status: "settled",
          organization: org2,
          amount: Decimal.new("300.00"),
          reference_at: DateTime.new!(Date.utc_today(), ~T[04:00:00], "Etc/UTC")
        )

      today = Date.utc_today()

      # Test org1 report
      result = Reports.retail_stores_report(org1.id, today, today)

      # Should only include org1 data
      assert length(result) == 1
      store_data = List.first(result)
      assert store_data.location_name == "Store Org1"
      # Count all non-canceled orders with payments in the date range
      assert store_data.orders == 2
      # Total amount includes payments in the date range
      assert Decimal.equal?(Decimal.new(store_data.amount), Decimal.new("300.00"))
      # Effective amount excludes canceled payments in the date range
      assert Decimal.equal?(Decimal.new(store_data.effective_amount), Decimal.new("300.00"))
      # Average only considers non-canceled orders in the date range
      assert Decimal.equal?(Decimal.new(store_data.average), Decimal.new("150.00"))

      # Test org2 report
      result = Reports.retail_stores_report(org2.id, today, today)

      # Should only include org2 data
      assert length(result) == 1
      store_data = List.first(result)
      assert store_data.location_name == "Store Org2"
      assert store_data.orders == 1
      assert Decimal.equal?(Decimal.new(store_data.amount), Decimal.new("300.00"))
      assert Decimal.equal?(Decimal.new(store_data.effective_amount), Decimal.new("300.00"))
      assert Decimal.equal?(Decimal.new(store_data.average), Decimal.new("300.00"))

      # Test with location filter
      result = Reports.retail_stores_report(org1.id, today, today, location1.id)

      # Should include org1 data filtered by location
      assert length(result) == 1
      store_data = List.first(result)
      assert store_data.location_name == "Store Org1"

      # Create another location for org1
      location3 = insert(:location, name: "Another Store Org1", organization: org1)
      order4 = insert(:order, organization: org1, location: location3, status: "paid")
      transaction4 = insert(:transaction, organization: org1, order: order4, status: "done")

      _payment4 =
        insert(:payment,
          transaction: transaction4,
          organization: org1,
          status: "settled",
          amount: Decimal.new("400.00"),
          # 12:00 Brazil time
          reference_at: DateTime.new!(Date.utc_today(), ~T[03:00:00], "Etc/UTC")
        )

      # Create a canceling order for the new location
      canceling_order =
        insert(:order, organization: org1, location: location3, status: "canceling")

      canceling_transaction = insert(:transaction, organization: org1, order: canceling_order)

      _canceling_payment =
        insert(:payment,
          transaction: canceling_transaction,
          organization: org1,
          amount: Decimal.new("250.00")
        )

      # Test org1 report again
      result = Reports.retail_stores_report(org1.id, today, today)

      # Should include both locations from org1
      assert length(result) == 2

      # Verify location names
      location_names = Enum.map(result, fn r -> r.location_name end)
      assert "Store Org1" in location_names
      assert "Another Store Org1" in location_names

      # Test with specific location filter
      result = Reports.retail_stores_report(org1.id, today, today, location3.id)

      # Should only include the specified location
      assert length(result) == 1
      store_data = List.first(result)
      assert store_data.location_name == "Another Store Org1"
      # Only count non-canceled/canceling orders
      assert store_data.orders == 1
      # Effective amount excludes canceled/canceling payments
      assert Decimal.equal?(Decimal.new(store_data.effective_amount), Decimal.new("400.00"))
      # Average only considers non-canceled/canceling orders
      assert Decimal.equal?(Decimal.new(store_data.average), Decimal.new("400.00"))
    end
  end

  describe "calculate_total_amount_sold/1" do
    test "correctly calculates total amount sold for different order statuses" do
      # Create test data
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      staff = insert(:staff, organization: organization)
      today = Date.utc_today()

      # Create a paid order
      paid_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "paid"
        )

      paid_transaction =
        insert(:transaction, organization: organization, order: paid_order, status: "done")

      _paid_payment =
        insert(:payment,
          transaction: paid_transaction,
          organization: organization,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Create a partially returned order
      partially_returned_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "partially_returned"
        )

      partially_returned_transaction =
        insert(:transaction,
          organization: organization,
          order: partially_returned_order,
          status: "done"
        )

      _partially_returned_payment =
        insert(:payment,
          transaction: partially_returned_transaction,
          organization: organization,
          amount: Decimal.new("200.00"),
          status: "settled",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Add a return credit payment for the partially returned order
      _return_credit_payment =
        insert(:payment,
          transaction: partially_returned_transaction,
          organization: organization,
          amount: Decimal.new("-50.00"),
          method: "return_credit",
          status: "settled",
          reference_at: DateTime.new!(today, ~T[16:00:00], "Etc/UTC")
        )

      # Create a fully returned order
      returned_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "returned"
        )

      returned_transaction =
        insert(:transaction, organization: organization, order: returned_order, status: "done")

      _returned_payment =
        insert(:payment,
          transaction: returned_transaction,
          organization: organization,
          amount: Decimal.new("150.00"),
          status: "settled",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Add a return credit payment for the fully returned order
      _full_return_credit_payment =
        insert(:payment,
          transaction: returned_transaction,
          organization: organization,
          amount: Decimal.new("-150.00"),
          method: "return_credit",
          status: "settled",
          reference_at: DateTime.new!(today, ~T[16:00:00], "Etc/UTC")
        )

      # Create an open order (should be excluded)
      open_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "open"
        )

      open_transaction =
        insert(:transaction, organization: organization, order: open_order, status: "open")

      _open_payment =
        insert(:payment,
          transaction: open_transaction,
          organization: organization,
          amount: Decimal.new("75.00"),
          status: "pending",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Create a canceled order (should be excluded)
      canceled_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "canceled"
        )

      canceled_transaction =
        insert(:transaction,
          organization: organization,
          order: canceled_order,
          status: "canceled"
        )

      _canceled_payment =
        insert(:payment,
          transaction: canceled_transaction,
          organization: organization,
          amount: Decimal.new("50.00"),
          status: "canceled",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Test with a date range that includes all orders
      start_datetime = DateTime.new!(today, ~T[03:00:00], "Etc/UTC") |> DateTime.truncate(:second)

      end_datetime =
        today
        |> Date.add(1)
        |> DateTime.new!(~T[02:59:59], "Etc/UTC")
        |> DateTime.truncate(:second)

      # Test the function by using the selling_summary function which calls calculate_total_amount_sold
      result = Reports.selling_summary(start_datetime, end_datetime)

      # The total amount sold should include paid, partially_returned, and returned orders
      # But exclude open and canceled orders
      # Paid: 100.00
      # Partially returned: 200.00
      # Returned: 150.00
      # Total: 450.00
      assert result.total_amount_sold == 450.0
    end

    test "excludes orders with status 'open' from total amount sold" do
      # Create test data
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      staff = insert(:staff, organization: organization)
      today = Date.utc_today()

      # Create a paid order
      paid_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "paid"
        )

      paid_transaction =
        insert(:transaction, organization: organization, order: paid_order, status: "done")

      _paid_payment =
        insert(:payment,
          transaction: paid_transaction,
          organization: organization,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Create an open order (should be excluded)
      open_order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "open"
        )

      open_transaction =
        insert(:transaction, organization: organization, order: open_order, status: "open")

      _open_payment =
        insert(:payment,
          transaction: open_transaction,
          organization: organization,
          amount: Decimal.new("75.00"),
          status: "pending",
          reference_at: DateTime.new!(today, ~T[15:00:00], "Etc/UTC")
        )

      # Test with a date range that includes all orders
      start_datetime = DateTime.new!(today, ~T[03:00:00], "Etc/UTC") |> DateTime.truncate(:second)

      end_datetime =
        today
        |> Date.add(1)
        |> DateTime.new!(~T[02:59:59], "Etc/UTC")
        |> DateTime.truncate(:second)

      # Test the function by using the selling_summary function which calls calculate_total_amount_sold
      result = Reports.selling_summary(start_datetime, end_datetime)

      # The total amount sold should only include the paid order (100.00)
      # and exclude the open order (75.00)
      assert result.total_amount_sold == 100.0
    end
  end
end
