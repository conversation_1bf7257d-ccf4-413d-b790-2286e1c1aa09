defmodule Rms.SettingsTest do
  use Rms.DataCase

  alias Rms.Settings
  alias Rms.Settings.OrganizationSetting

  describe "get_location_setting/2" do
    test "retrieves a specific location setting" do
      loc = insert(:location)

      insert(:location_setting,
        key: "operating_hours",
        value: %{data: "9 AM - 5 PM"},
        location: loc
      )

      assert setting =
               Settings.get_location_setting(loc.organization_id, loc.id, "operating_hours")

      assert setting.value == %{"data" => "9 AM - 5 PM"}
    end
  end

  describe "get_organization_setting/2" do
    test "retrieves a specific organization setting" do
      org = insert(:organization)
      insert(:organization_setting, key: "timezone", value: %{data: "UTC"}, organization: org)

      assert setting = Settings.get_organization_setting(org.id, "timezone")
      assert setting.value == %{"data" => "UTC"}
    end
  end

  describe "update_location_setting/2" do
    test "updates a specific location setting" do
      loc = insert(:location)

      setting =
        insert(:location_setting,
          key: "operating_hours",
          value: %{data: "9 AM - 5 PM"},
          location: loc
        )

      assert {:ok, %Rms.Settings.LocationSetting{value: %{data: "10 AM - 6 PM"}}} =
               Settings.update_location_setting(
                 loc.organization_id,
                 loc.id,
                 setting.key,
                 "10 AM - 6 PM"
               )

      assert updated_setting =
               Settings.get_location_setting(loc.organization_id, loc.id, "operating_hours")

      assert updated_setting.value == %{"data" => "10 AM - 6 PM"}
    end
  end

  describe "update_organization_setting/2" do
    test "updates a specific organization setting" do
      org = insert(:organization)

      setting =
        insert(:organization_setting, key: "timezone", value: %{data: "UTC"}, organization: org)

      assert {:ok, %Rms.Settings.OrganizationSetting{value: %{data: "EST"}}} =
               Settings.update_organization_setting(org.id, setting.key, "EST")

      assert updated_setting = Settings.get_organization_setting(org.id, "timezone")
      assert updated_setting.value == %{"data" => "EST"}
    end
  end

  describe "create_location_setting/3" do
    test "creates string setting for a location" do
      loc = insert(:location)

      key = "operating_hours"
      value = "9 AM - 5 PM"

      assert {:ok, %Rms.Settings.LocationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_location_setting(
                 loc.id,
                 loc.organization_id,
                 key,
                 value
               )
    end

    test "creates boolean setting for a location" do
      loc = insert(:location)

      key = "open_on_weekends"
      value = false

      assert {:ok, %Rms.Settings.LocationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_location_setting(
                 loc.id,
                 loc.organization_id,
                 key,
                 value
               )
    end

    test "creates array setting for a location" do
      loc = insert(:location)

      key = "services_offered"
      value = ["Consultation", "Surgery", "Rehabilitation"]

      assert {:ok, %Rms.Settings.LocationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_location_setting(
                 loc.id,
                 loc.organization_id,
                 key,
                 value
               )
    end

    test "creates numerical setting for a location" do
      loc = insert(:location)

      key = "max_capacity"
      value = 50

      assert {:ok, %Rms.Settings.LocationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_location_setting(
                 loc.id,
                 loc.organization_id,
                 key,
                 value
               )
    end
  end

  describe "create_organization_setting/3" do
    test "creates string setting" do
      org = insert(:organization)

      key = "timezone"
      value = "UTC"

      assert {:ok, %OrganizationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_organization_setting(
                 org.id,
                 key,
                 value
               )
    end

    test "creates boolean setting" do
      org = insert(:organization)

      key = "notifications_enabled"
      value = true

      assert {:ok, %OrganizationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_organization_setting(
                 org.id,
                 key,
                 value
               )
    end

    test "creates array setting" do
      org = insert(:organization)

      key = "working_days"
      value = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

      assert {:ok, %OrganizationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_organization_setting(
                 org.id,
                 key,
                 value
               )
    end

    test "creates numerical setting" do
      org = insert(:organization)

      key = "max_users"
      value = 100

      assert {:ok, %OrganizationSetting{key: ^key, value: %{data: ^value}}} =
               Settings.create_organization_setting(
                 org.id,
                 key,
                 value
               )
    end
  end

  describe "list_settings/2" do
    test "returns both location and organization settings" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: loc)

      assert settings = Settings.list_settings(org.id, loc.id)
      assert length(settings) == 2
    end

    test "returns only organization settings when no location is specified" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: loc)

      assert settings = Settings.list_settings(org.id, nil)
      assert length(settings) == 1
    end

    test "does not return other org/locs settings" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: loc)

      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)

      insert(:organization_setting,
        key: "example_another_org",
        value: %{data: 3},
        organization: another_org
      )

      insert(:location_setting,
        key: "example_another_loc",
        value: %{data: 4},
        location: another_loc
      )

      settings = Settings.list_settings(another_org.id, another_loc.id)

      assert length(settings) == 2

      assert %{type: "location_setting", value: 4, key: "example_another_loc"} =
               Enum.find(settings, &(&1.key == "example_another_loc"))

      assert %{type: "organization_setting", value: 3, key: "example_another_org"} =
               Enum.find(settings, &(&1.key == "example_another_org"))
    end

    test "does not return locs settings when the locs dont belongs to the organization" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example_org", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "example_loc", value: %{data: 1}, location: loc)

      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)

      insert(:organization_setting,
        key: "example_another_org",
        value: %{data: 3},
        organization: another_org
      )

      insert(:location_setting,
        key: "example_another_loc",
        value: %{data: 4},
        location: another_loc
      )

      assert [
               %{type: "organization_setting", value: 2, key: "example_org"}
             ] = Settings.list_settings(org.id, another_loc.id)
    end

    test "allows requesting location fields" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:location_setting, key: "example", value: %{data: 1}, location: loc)
      insert(:location_setting, key: "another_example", value: %{data: 2}, location: loc)

      assert [%{value: 1}] = Settings.list_settings(org.id, loc.id, ~w(location_example))
    end

    test "allows requesting organization fields" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 1}, organization: org)
      insert(:organization_setting, key: "another_example", value: %{data: 2}, organization: org)

      assert settings =
               Settings.list_settings(org.id, loc.id, ~w(organization_example another_example))

      assert length(settings) == 2
      assert %{value: 1} = Enum.find(settings, &(&1.key == "example"))
      assert %{value: 2} = Enum.find(settings, &(&1.key == "another_example"))
    end

    test "does not return other location/org setting when requesting fields" do
      org = insert(:organization)
      loc = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)
      insert(:organization_setting, key: "example2", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: loc)

      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: another_org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: another_loc)

      assert settings =
               Settings.list_settings(
                 org.id,
                 loc.id,
                 ~w(example example2 location_another_example)
               )

      assert length(settings) == 3
    end

    test "does not return other locations setting when a location_id is specified" do
      org = insert(:organization)
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)
      insert(:organization_setting, key: "example2", value: %{data: 2}, organization: org)
      insert(:location_setting, key: "another_example", value: %{data: 1.1}, location: loc_1)
      insert(:location_setting, key: "another_example", value: %{data: 1.2}, location: loc_2)

      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: another_org)
      insert(:location_setting, key: "another_example", value: %{data: 1}, location: another_loc)

      assert settings =
               Settings.list_settings(
                 org.id,
                 loc_1.id,
                 ~w(example example2 location_another_example)
               )

      assert length(settings) == 3
    end
  end

  describe "bulk_upsert_location_settings/3" do
    test "creates multiple new settings" do
      loc = insert(:location)

      settings = %{
        "operating_hours" => "9 AM - 5 PM",
        "printer_ip" => "***********",
        "open_on_weekends" => true
      }

      assert {:ok, updated_settings} =
               Settings.bulk_upsert_location_settings(
                 loc.organization_id,
                 loc.id,
                 settings
               )

      assert length(updated_settings) == 3

      # Verify each setting was created correctly
      Enum.each(settings, fn {key, value} ->
        setting = Settings.get_location_setting(loc.organization_id, loc.id, key)
        assert setting.value == %{"data" => value}
      end)
    end

    test "updates existing settings and creates new ones" do
      loc = insert(:location)

      # Create an existing setting
      _existing_setting =
        insert(:location_setting,
          key: "operating_hours",
          value: %{data: "9 AM - 5 PM"},
          location: loc
        )

      settings = %{
        # Update existing
        "operating_hours" => "10 AM - 6 PM",
        # Create new
        "printer_ip" => "***********"
      }

      assert {:ok, updated_settings} =
               Settings.bulk_upsert_location_settings(
                 loc.organization_id,
                 loc.id,
                 settings
               )

      assert length(updated_settings) == 2

      # Verify existing setting was updated
      updated_setting =
        Settings.get_location_setting(loc.organization_id, loc.id, "operating_hours")

      assert updated_setting.value == %{"data" => "10 AM - 6 PM"}

      # Verify new setting was created
      new_setting = Settings.get_location_setting(loc.organization_id, loc.id, "printer_ip")
      assert new_setting.value == %{"data" => "***********"}
    end

    test "returns ok with empty list when no settings are provided" do
      loc = insert(:location)

      assert {:ok, []} =
               Settings.bulk_upsert_location_settings(
                 loc.organization_id,
                 loc.id,
                 %{}
               )
    end

    test "rolls back all changes if any setting fails to update" do
      loc = insert(:location)

      # Create an existing setting
      _existing_setting =
        insert(:location_setting,
          key: "operating_hours",
          value: %{data: "9 AM - 5 PM"},
          location: loc
        )

      # Mock an error for one of the settings
      settings = %{
        "operating_hours" => "10 AM - 6 PM",
        # This should cause an error
        "invalid_setting" => nil
      }

      assert {:error, _} =
               Settings.bulk_upsert_location_settings(
                 loc.organization_id,
                 loc.id,
                 settings
               )

      # Verify the existing setting was not updated
      unchanged_setting =
        Settings.get_location_setting(loc.organization_id, loc.id, "operating_hours")

      assert unchanged_setting.value == %{"data" => "9 AM - 5 PM"}

      # Verify no new setting was created
      assert nil == Settings.get_location_setting(loc.organization_id, loc.id, "invalid_setting")
    end
  end
end
