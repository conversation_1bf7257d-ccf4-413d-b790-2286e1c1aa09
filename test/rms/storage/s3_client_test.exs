defmodule Rms.Storage.S3ClientTest do
  use Rms.DataCase

  @moduletag :localstack_integration

  alias Rms.Storage.S3Client
  alias Rms.Test.S3Helper

  @upload_bucket "test-bucket"

  setup do
    Application.put_env(:rms, :s3_bucket, @upload_bucket)
    S3Helper.ensure_test_bucket(@upload_bucket)
    S3Helper.clear_bucket(@upload_bucket)

    :ok
  end

  describe "generate_presigned_post/3" do
    test "generates valid presigned post data with defaults" do
      assert {:ok, %{url: url, fields: fields}} =
               S3Client.generate_presigned_post(@upload_bucket, "test/key.csv", [])

      assert is_binary(url)
      assert URI.parse(url).scheme == "http"
      assert is_map(fields)
      assert fields["key"] == "test/key.csv"
    end

    # this test is kinda useless, because the expiration time is encoded in the token
    test "respects custom expiration time" do
      assert {:ok, %{url: url, fields: fields}} =
               S3Client.generate_presigned_post(@upload_bucket, "test/key.csv", expires_in: 7200)

      assert is_binary(url)
      assert is_map(fields)
      assert fields["key"] == "test/key.csv"
    end
  end

  describe "exists?/1" do
    test "returns true when file exists" do
      key = "test/exists.csv"
      S3Helper.put_test_file(@upload_bucket, key, "test content")

      assert {:ok, true} = S3Client.exists?(key)
    end

    test "returns false when file doesn't exist" do
      assert {:ok, false} = S3Client.exists?("test/not_found.csv")
    end
  end
end
