defmodule Rms.Storage.UploadEntryTest do
  use Rms.DataCase

  alias Rms.Storage.UploadEntry

  @valid_attrs %{
    s3_key: "some/key.csv",
    status: "pending",
    expires_at: ~U[2025-02-13 14:00:00Z],
    organization_id: 1
  }

  describe "changeset/2" do
    test "changeset with valid attributes" do
      changeset = UploadEntry.changeset(build(:upload_entry, organization_id: 1), @valid_attrs)
      assert changeset.valid?
    end

    test "changeset with invalid attributes" do
      changeset = UploadEntry.changeset(%UploadEntry{}, %{})
      refute changeset.valid?
    end

    test "changeset enforces required fields" do
      changeset = UploadEntry.changeset(%UploadEntry{}, %{})

      assert "can't be blank" in errors_on(changeset).s3_key
      assert "can't be blank" in errors_on(changeset).expires_at
      assert "can't be blank" in errors_on(changeset).organization_id
    end

    test "changeset enforces valid status" do
      attrs = Map.put(@valid_attrs, :status, "invalid")
      changeset = UploadEntry.changeset(build(:upload_entry, organization_id: 1), attrs)

      assert "is invalid" in errors_on(changeset).status
    end

    test "changeset enforces s3_key uniqueness" do
      entry = insert(:upload_entry)

      changeset =
        UploadEntry.changeset(
          build(:upload_entry,
            organization: entry.organization,
            organization_id: entry.organization_id
          ),
          Map.merge(@valid_attrs, %{s3_key: entry.s3_key})
        )

      assert {:error, changeset} = Repo.insert(changeset)
      assert "has already been taken" in errors_on(changeset).s3_key
    end

    test "changeset allows valid status transitions" do
      valid_transitions = [
        {"pending", "processing"},
        {"pending", "cancelled"},
        {"pending", "expired"},
        {"processing", "cancelled"},
        {"processing", "error"}
      ]

      for {from, to} <- valid_transitions do
        entry = build(:upload_entry, organization_id: 1, status: from)
        changeset = UploadEntry.changeset(entry, %{status: to})
        assert changeset.valid?, "expected transition from #{from} to #{to} to be valid"
      end
    end

    test "changeset prevents invalid status transitions" do
      invalid_transitions = [
        {"cancelled", "processing"},
        {"cancelled", "error"},
        {"cancelled", "expired"},
        {"error", "processing"},
        {"error", "cancelled"},
        {"error", "expired"},
        {"expired", "processing"},
        {"expired", "cancelled"},
        {"expired", "error"}
      ]

      for {from, to} <- invalid_transitions do
        entry = build(:upload_entry, status: from)
        changeset = UploadEntry.changeset(entry, %{status: to})
        refute changeset.valid?, "Expected transition from #{from} to #{to} to be invalid"
        assert "is invalid" in errors_on(changeset).status
      end
    end
  end

  describe "status_changeset/2" do
    test "handles valid status changes" do
      entry = build(:upload_entry, status: "pending", organization_id: 1)
      changeset = UploadEntry.status_changeset(entry, "processing")
      assert changeset.valid?
    end
  end

  describe "error_changeset/2" do
    test "sets status to error and adds error messages" do
      entry = build(:upload_entry, status: "processing", organization_id: 1)
      error_messages = ["Error 1", "Error 2"]
      changeset = UploadEntry.error_changeset(entry, error_messages)

      assert changeset.valid?
      assert get_change(changeset, :status) == "error"
      assert get_change(changeset, :error_messages) == error_messages
    end
  end
end
