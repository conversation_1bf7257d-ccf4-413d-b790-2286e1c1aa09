defmodule Rms.StorageTest do
  use Rms.DataCase
  import Mox

  import Rms.Factory

  alias Rms.Storage

  @upload_bucket "test-bucket"

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    {:ok, %{organization: organization}}
  end

  describe "generate_presigned_post/1" do
    test "generates presigned URL with default options", %{organization: organization} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn bucket, key, opts ->
        assert bucket == @upload_bucket
        assert String.starts_with?(key, "uploads/")
        assert Keyword.get(opts, :content_length) == 10 * 1024 * 1024
        assert Keyword.get(opts, :expires_in) == 3600
        assert Keyword.get(opts, :content_type) == nil
        {:ok, %{url: "https://example.com/test-url", fields: %{}}}
      end)

      assert {:ok, %{url: url, upload: upload}} =
               Storage.generate_presigned_post(organization.id)

      assert is_binary(url)
      assert String.starts_with?(url, "http")
      assert upload.organization_id == organization.id
      assert upload.status == "pending"
      assert String.starts_with?(upload.s3_key, "uploads/")
      assert DateTime.compare(upload.expires_at, DateTime.utc_now()) == :gt
    end

    test "generates URL with custom prefix", %{organization: organization} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn bucket, key, _opts ->
        assert bucket == "test-bucket"
        assert String.starts_with?(key, "custom/prefix/")
        {:ok, %{url: "https://example.com/test-url", fields: %{}}}
      end)

      assert {:ok, %{url: _url, upload: upload}} =
               Storage.generate_presigned_post(
                 organization.id,
                 prefix: "custom/prefix"
               )

      assert String.starts_with?(upload.s3_key, "custom/prefix/")
    end

    test "fails with missing organization_id" do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn _bucket, _key, _opts ->
        {:ok, %{url: "https://example.com/test-url", fields: %{}}}
      end)

      assert {:error, changeset} = Storage.generate_presigned_post(nil)
      assert "can't be blank" in errors_on(changeset).organization_id
    end
  end

  describe "create_upload_entry/1" do
    test "creates upload entry", %{organization: organization} do
      attrs = %{
        s3_key: "test/key.csv",
        expires_at: DateTime.add(DateTime.utc_now(), 3600),
        organization_id: organization.id
      }

      assert {:ok, upload} = Storage.create_upload_entry(organization.id, attrs)
      assert upload.s3_key == attrs.s3_key
      assert upload.status == "pending"
    end

    test "enforces unique s3_key constraint", %{organization: organization} do
      existing = insert(:upload_entry, organization: organization)

      attrs = %{
        s3_key: existing.s3_key,
        expires_at: DateTime.add(DateTime.utc_now(), 3600),
        organization_id: organization.id
      }

      assert {:error, changeset} = Storage.create_upload_entry(organization.id, attrs)
      assert "has already been taken" in errors_on(changeset).s3_key
    end
  end

  describe "get_upload_entry!/1" do
    test "gets upload entry by id", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization)
      assert found = Storage.get_upload_entry!(organization.id, upload.id)
      assert found.id == upload.id
    end

    test "raises error when upload entry belongs to different organization", %{
      organization: organization
    } do
      other_org = insert(:organization)
      upload = insert(:upload_entry, organization: other_org)

      assert_raise Ecto.NoResultsError, fn ->
        Storage.get_upload_entry!(organization.id, upload.id)
      end
    end
  end

  describe "update_upload_entry/2" do
    test "updates upload entry", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization)

      assert {:ok, updated} =
               Storage.update_upload_entry(upload, %{
                 status: "processing"
               })

      assert updated.status == "processing"
    end

    test "enforces status transitions", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization)

      # Cannot transition directly from pending to completed
      assert {:error, changeset} =
               Storage.update_upload_entry(upload, %{
                 status: "completed"
               })

      assert "is invalid" in errors_on(changeset).status

      # Valid transition from pending to processing
      assert {:ok, updated} =
               Storage.update_upload_entry(upload, %{
                 status: "processing"
               })

      assert updated.status == "processing"
    end
  end

  describe "mark_as_uploaded/1" do
    test "marks upload as uploaded", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization)

      assert {:ok, updated} = Storage.mark_as_uploaded(upload)
      assert updated.uploaded_at != nil
      assert DateTime.compare(updated.uploaded_at, DateTime.utc_now()) in [:eq, :lt]
    end
  end

  describe "generate_s3_key/1" do
    test "generates unique keys with prefix" do
      key1 = Storage.generate_s3_key("test")
      key2 = Storage.generate_s3_key("test")

      assert String.starts_with?(key1, "test/")
      assert String.starts_with?(key2, "test/")
      assert key1 != key2
    end

    test "handles nested prefixes" do
      key = Storage.generate_s3_key("test/nested/path")
      assert String.starts_with?(key, "test/nested/path/")
    end
  end
end
