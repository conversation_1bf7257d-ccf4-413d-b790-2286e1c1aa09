defmodule Rms.Workers.CieloPaymentNotificationTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  import Mox

  alias Rms.Workers.CieloPaymentNotification
  alias Rms.Finance

  setup :verify_on_exit!

  setup do
    # Create organization and payment
    organization = insert(:organization)

    transaction =
      insert(:transaction,
        organization: organization,
        order: build(:order, organization: organization)
      )

    payment =
      insert(:payment,
        organization: organization,
        transaction: transaction,
        method: "payment_link",
        status: "pending",
        metadata: %{
          "link" => "https://cielolink.com.br/3YKhhR1"
        }
      )

    # Create external payment reference
    insert(:external_payment_reference,
      partner: "cielo-link",
      external_id: "123456",
      payment: payment,
      organization: organization
    )

    %{
      payment: payment,
      organization: organization
    }
  end

  describe "perform/1" do
    test "updates payment when Cielo returns Paid status", %{
      payment: payment,
      organization: organization
    } do
      # Create credential
      credential = insert(:cielo_credential, organization: organization)

      # Mock Cielo API response
      cielo_response = %{
        "payment" => %{
          "status" => "Paid",
          "nsu" => "123456",
          "authorizationCode" => "789012",
          "brand" => "Visa",
          "cardMaskedNumber" => "411111****1111",
          "numberOfPayments" => 1,
          "type" => "CreditCard"
        }
      }

      expect(Rms.Integrations.Cielo.Mock, :get_payment_details, fn token, order_number ->
        assert token == credential.token
        assert order_number == "123456"
        {:ok, cielo_response}
      end)

      # Perform the job
      assert {:ok, _updated_payment} =
               perform_job(CieloPaymentNotification, %{
                 "checkout_cielo_order_number" => "123456",
                 "product_id" => "123456"
               })

      # Assert payment was updated
      updated_payment = Finance.get_payment!(payment.organization_id, payment.id)
      assert updated_payment.status == "settled"
      assert updated_payment.metadata["payment_details"]["nsu"] == "123456"
      assert updated_payment.metadata["payment_details"]["aut"] == "789012"
      assert updated_payment.metadata["payment_details"]["card_brand"] == "Visa"
      assert updated_payment.metadata["payment_details"]["card_number"] == "411111****1111"
      assert updated_payment.metadata["payment_details"]["installments"] == 1
      assert updated_payment.metadata["payment_details"]["method"] == "credit_card"
    end

    test "updates payment when Cielo returns Denied status", %{
      payment: payment,
      organization: organization
    } do
      insert(:cielo_credential, organization: organization)

      cielo_response = %{
        "payment" => %{
          "status" => "Denied",
          "nsu" => "123456",
          "authorizationCode" => "789012",
          "brand" => "Visa",
          "cardMaskedNumber" => "411111****1111",
          "numberOfPayments" => 1,
          "type" => "CreditCard"
        }
      }

      expect(Rms.Integrations.Cielo.Mock, :get_payment_details, fn _, _ ->
        {:ok, cielo_response}
      end)

      assert {:ok, _updated_payment} =
               perform_job(CieloPaymentNotification, %{
                 "checkout_cielo_order_number" => "123456",
                 "product_id" => "123456"
               })

      updated_payment = Finance.get_payment!(payment.organization_id, payment.id)
      assert updated_payment.status == "canceled"
    end

    test "keeps payment as pending when Cielo returns unknown status", %{
      payment: payment,
      organization: organization
    } do
      insert(:cielo_credential, organization: organization)

      cielo_response = %{
        "payment" => %{
          "status" => "Processing",
          "nsu" => "123456",
          "authorizationCode" => "789012",
          "brand" => "Visa",
          "cardMaskedNumber" => "411111****1111",
          "numberOfPayments" => 1,
          "type" => "CreditCard"
        }
      }

      expect(Rms.Integrations.Cielo.Mock, :get_payment_details, fn _, _ ->
        {:ok, cielo_response}
      end)

      assert {:ok, _updated_payment} =
               perform_job(CieloPaymentNotification, %{
                 "checkout_cielo_order_number" => "123456",
                 "product_id" => "123456"
               })

      updated_payment = Finance.get_payment!(payment.organization_id, payment.id)
      assert updated_payment.status == "pending"
    end

    test "returns error when payment is not found" do
      assert {:error, :payment_not_found} =
               perform_job(CieloPaymentNotification, %{
                 "checkout_cielo_order_number" => "invalid",
                 "product_id" => "invalid"
               })
    end

    test "returns error when Cielo payment details fetch fails", %{organization: organization} do
      insert(:cielo_credential, organization: organization)

      expect(Rms.Integrations.Cielo.Mock, :get_payment_details, fn _, _ ->
        {:error, "failed to get payment details"}
      end)

      assert {:error, "failed to get payment details"} =
               perform_job(CieloPaymentNotification, %{
                 "checkout_cielo_order_number" => "123456",
                 "product_id" => "123456"
               })
    end
  end
end
