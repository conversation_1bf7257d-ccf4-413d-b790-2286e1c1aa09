defmodule Rms.Workers.ImportLocationWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Workers.ImportLocationWorker
  alias Rms.Integrations.LocationMapping

  alias Rms.Integrations.Shopify

  describe "process/1" do
    test "successfully creates a new location_mapping" do
      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location, fn _, _ ->
        {
          :ok,
          %{
            "nodes" => [
              %{
                "address" => %{
                  "address1" => "Avenida Rebouças 3970",
                  "address2" => "",
                  "city" => "São Paulo",
                  "country" => "Brazil",
                  "countryCode" => "BR",
                  "formatted" => [
                    "Avenida Rebouças 3970",
                    "São Paulo SP",
                    "05402-600",
                    "Brazil"
                  ],
                  "latitude" => -23.571601,
                  "longitude" => -46.69621069999999,
                  "phone" => "+5511123456789",
                  "province" => "São Paulo",
                  "provinceCode" => "SP",
                  "zip" => "05402-600"
                },
                "id" => "gid://shopify/Location/1",
                "isActive" => true,
                "name" => "Shopping Eldorado"
              }
            ]
          }
        }
      end)

      org = insert(:organization)
      location = insert(:location, organization: org)
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      assert {:ok, "mapping created"} =
               perform_job(ImportLocationWorker, %{
                 organization_id: org.id,
                 location_id: location.id
               })

      assert [location_mapping] = Rms.Repo.all(LocationMapping)

      assert location_mapping.source == "shopify"
      assert location_mapping.location_id == location.id
      assert location_mapping.external_id == "gid://shopify/Location/1"
    end

    test "only updates mapping when alredy exist one" do
      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location, fn _, _ ->
        {
          :ok,
          %{
            "nodes" => [
              %{
                "address" => %{
                  "address1" => "Avenida Rebouças 3970",
                  "address2" => "",
                  "city" => "São Paulo",
                  "country" => "Brazil",
                  "countryCode" => "BR",
                  "formatted" => [
                    "Avenida Rebouças 3970",
                    "São Paulo SP",
                    "05402-600",
                    "Brazil"
                  ],
                  "latitude" => -23.571601,
                  "longitude" => -46.69621069999999,
                  "phone" => "+5511123456789",
                  "province" => "São Paulo",
                  "provinceCode" => "SP",
                  "zip" => "05402-600"
                },
                "id" => "gid://shopify/Location/1",
                "isActive" => true,
                "name" => "Shopping Eldorado"
              }
            ]
          }
        }
      end)

      org = insert(:organization)
      location = insert(:location, organization: org)

      location_mapping =
        insert(:location_mapping,
          organization: org,
          location: location,
          external_id: "gid://shopify/Location/1",
          source: "shopify"
        )

      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      assert {:ok, "mapping created"} =
               perform_job(ImportLocationWorker, %{
                 organization_id: org.id,
                 location_id: location.id
               })

      assert [updated_location_mapping] = Rms.Repo.all(LocationMapping)

      assert updated_location_mapping.source == "shopify"
      assert updated_location_mapping.location_id == location.id
      assert updated_location_mapping.external_id == "gid://shopify/Location/1"
      refute updated_location_mapping.updated_at != location_mapping.updated_at
    end

    test "returns a error when ther is no shopify location" do
      Mox.expect(Shopify.Mock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Shopify.Mock, :fetch_location, fn _, _ ->
        {
          :ok,
          %{
            "nodes" => []
          }
        }
      end)

      org = insert(:organization)
      location = insert(:location, organization: org)
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "shopify"}
      )

      assert {:error, "shopify_location not found"} =
               perform_job(ImportLocationWorker, %{
                 organization_id: org.id,
                 location_id: location.id
               })

      assert [] = Rms.Repo.all(LocationMapping)
    end

    test "return discard for invalid ecommerce" do
      org = insert(:organization)
      location = insert(:location, organization: org)
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      assert {:discard, "discard"} =
               perform_job(ImportLocationWorker, %{
                 organization_id: org.id,
                 location_id: location.id
               })

      assert [] = Rms.Repo.all(LocationMapping)
    end
  end
end
