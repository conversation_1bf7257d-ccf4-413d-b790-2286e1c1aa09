defmodule Rms.Workers.PagarMeWorkerTest do
  use Rms.DataCase, async: true
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Workers.PagarMeWorker
  alias Rms.Finance
  alias Rms.Finance.Payment
  alias Rms.Finance.Transaction
  alias Rms.Commerce
  alias Rms.Commerce.Orders.Order

  defp build_job_args(org_id, external_id, pagarme_api_status, event_type_suffix \\ nil) do
    # If event_type_suffix is nil, derive it from pagarme_api_status (e.g. "paid" -> "order.paid")
    # This allows testing scenarios where the event type might differ from the data status,
    # though for PagarMe webhooks, they usually align.
    type_trigger = event_type_suffix || pagarme_api_status

    %{
      "payload" => %{
        "type" => "order.#{type_trigger}",
        "data" => %{
          "id" => external_id,
          "status" => pagarme_api_status
          # Minimal data needed by UpdatePayment.execute/2
        }
      },
      "org_id" => org_id
    }
  end

  describe "process/1" do
    test "when event is 'order.paid' and status is 'paid', settles the payment and updates related entities" do
      organization = insert(:organization)

      order =
        insert(:order,
          organization: organization,
          status: "open",
          total_price: Decimal.new("100.00")
        )

      transaction = insert(:transaction, order: order, organization: organization, status: "open")

      payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          method: "payment_link",
          status: "pending",
          amount: order.total_price
        )

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme"
        )

      args = build_job_args(organization.id, epr.external_id, "paid")

      assert {:ok, :updated_payment} = perform_job(PagarMeWorker, args)

      assert %Payment{status: "settled"} = Finance.get_payment!(organization.id, payment.id)

      assert %Transaction{status: "done"} =
               Finance.get_transaction!(organization.id, transaction.id)

      assert %Order{status: "paid"} = Commerce.Orders.get_order!(organization.id, order.id)
    end

    test "when event is 'order.canceled' and status is 'canceled', cancels the payment" do
      organization = insert(:organization)
      order = insert(:order, organization: organization, status: "open")
      transaction = insert(:transaction, order: order, organization: organization, status: "open")

      payment =
        insert(:payment, transaction: transaction, organization: organization, status: "pending")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme"
        )

      args = build_job_args(organization.id, epr.external_id, "canceled")
      assert {:ok, :updated_payment} = perform_job(PagarMeWorker, args)

      assert %Payment{status: "canceled"} = Finance.get_payment!(organization.id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(organization.id, transaction.id)

      assert %Order{status: "open"} = Commerce.Orders.get_order!(organization.id, order.id)
    end

    test "when payment is not found, discards the job with :not_found" do
      organization = insert(:organization)
      args = build_job_args(organization.id, "non_existent_id", "paid")

      assert {:discard, :not_found} = perform_job(PagarMeWorker, args)
    end

    test "when PagarMe status maps to internal 'pending', discards with {:invalid_status, \"pending\"}" do
      organization = insert(:organization)
      payment = insert(:payment, organization: organization, status: "authorized")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme"
        )

      # Use "order.paid" as the event type, but "pending" as the PagarMe status in data
      # to ensure it goes through the UpdatePayment.execute path.
      args = build_job_args(organization.id, epr.external_id, "pending", "paid")

      assert {:discard, {:invalid_status, "pending"}} = perform_job(PagarMeWorker, args)
      assert %Payment{status: "authorized"} = Finance.get_payment!(organization.id, payment.id)
    end

    test "when PagarMe status maps to internal 'failed', discards with {:invalid_status, \"failed\"}" do
      organization = insert(:organization)
      payment = insert(:payment, organization: organization, status: "authorized")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme"
        )

      args = build_job_args(organization.id, epr.external_id, "failed", "paid")

      assert {:discard, {:invalid_status, "failed"}} = perform_job(PagarMeWorker, args)
      assert %Payment{status: "authorized"} = Finance.get_payment!(organization.id, payment.id)
    end

    test "when Finance.sync_payment fails, returns error changeset" do
      organization = insert(:organization)
      # Already canceled
      payment = insert(:payment, organization: organization, status: "canceled")

      epr =
        insert(:external_payment_reference,
          payment: payment,
          organization: organization,
          partner: "pagarme"
        )

      # Attempt to settle
      args = build_job_args(organization.id, epr.external_id, "paid")
      result = perform_job(PagarMeWorker, args)

      assert {:error, changeset} = result
      assert "status transition not allowed canceled -> settled" in errors_on(changeset).status

      assert %Payment{status: "canceled"} = Finance.get_payment!(organization.id, payment.id)
    end

    test "when job payload 'type' is not in @update_payment_events, returns :ok (NOP)" do
      organization = insert(:organization)

      args = %{
        "payload" => %{
          # Not in @update_payment_events
          "type" => "order.some_other_event",
          "data" => %{"id" => "any_id", "status" => "paid"}
        },
        "org_id" => organization.id
      }

      assert {:discard, :invalid_args} = perform_job(PagarMeWorker, args)
    end
  end
end
