defmodule Rms.Workers.PendingPaymentWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Workers.PendingPaymentWorker

  import Rms.Factory

  describe "perform/1" do
    test "ignore payments older than 2 days" do
      current_time = DateTime.utc_now()
      org = insert(:organization)
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      old_pending_payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          updated_at: DateTime.add(current_time, -1, :day),
          organization: org
        )

      new_pending_payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          updated_at: DateTime.add(current_time, -6, :day),
          organization: org
        )

      settled_payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "settled",
          transaction: transaction,
          updated_at: DateTime.add(current_time, -7, :day),
          organization: org
        )

      canceled_payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "canceled",
          transaction: transaction,
          updated_at: DateTime.add(current_time, -10, :day),
          organization: org
        )

      %{external_id: external_id} =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: old_pending_payment,
          organization: org
        )

      insert(:external_payment_reference,
        partner: "pagarme",
        payment: new_pending_payment,
        organization: org
      )

      insert(:external_payment_reference,
        partner: "pagarme",
        payment: settled_payment,
        organization: org
      )

      insert(:external_payment_reference,
        partner: "pagarme",
        payment: canceled_payment,
        organization: org
      )

      assert :ok =
               perform_job(PendingPaymentWorker, %{})

      assert [%{args: %{"external_id" => ^external_id}}] =
               all_enqueued(worker: Rms.Workers.PollingWorker)
    end
  end
end
