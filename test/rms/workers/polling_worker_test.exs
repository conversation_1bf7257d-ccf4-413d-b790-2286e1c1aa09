defmodule Rms.Workers.PollingWorkerTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Workers.PollingWorker

  alias Rms.Finance
  alias Rms.Finance.Payment
  alias Rms.Finance.Transaction

  alias Rms.Commerce
  alias Rms.Commerce.Orders.Order

  import Rms.Factory

  setup :verify_on_exit!

  describe "process/1 for pagarme" do
    test "settle a payment and update its transaction and order" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      insert(:pagarme_credential, organization: order.organization, organization: organization)

      epr_id = epr.external_id

      expect(PagarMeClientMock, :get_order, fn _client, ^epr_id ->
        {:ok, order_builder(epr.external_id, "paid", 100)}
      end)

      assert {:ok, :updated_payment} =
               perform_job(PollingWorker, %{
                 external_id: epr.external_id,
                 organization_id: epr.organization_id,
                 partner: "pagarme",
                 entitie: "payment"
               })

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "done"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "paid"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "settle a payment and do not update its transaction and order" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: 70,
          status: "authorized",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      another_payment =
        insert(:payment,
          method: "payment_link",
          amount: 30,
          status: "authorized",
          transaction: transaction,
          organization: organization
        )

      _another_epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: another_payment,
          organization: organization
        )

      insert(:pagarme_credential, organization: order.organization)

      epr_id = epr.external_id

      expect(PagarMeClientMock, :get_order, fn _client, ^epr_id ->
        {:ok, order_builder(epr.external_id, "paid", 100)}
      end)

      assert {:ok, :updated_payment} =
               perform_job(PollingWorker, %{
                 external_id: epr.external_id,
                 organization_id: epr.organization_id,
                 partner: "pagarme",
                 entitie: "payment"
               })

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)
               |> Rms.Repo.preload([:payments, :order])

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)

      assert %Payment{status: "authorized"} =
               Finance.get_payment!(payment.organization_id, another_payment.id)
    end

    test "cancel a payment" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "authorized",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      insert(:pagarme_credential, organization: order.organization, organization: organization)

      epr_id = epr.external_id

      expect(PagarMeClientMock, :get_order, fn _client, ^epr_id ->
        {:ok, order_builder(epr.external_id, "canceled", 100)}
      end)

      assert {:ok, :updated_payment} =
               perform_job(PollingWorker, %{
                 external_id: epr.external_id,
                 organization_id: epr.organization_id,
                 partner: "pagarme",
                 entitie: "payment"
               })

      assert %Payment{status: "canceled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "does not find a payment" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "pending",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      insert(:pagarme_credential, organization: order.organization)

      epr_id = epr.external_id

      expect(PagarMeClientMock, :get_order, 0, fn _client, ^epr_id ->
        {:error, :not_found}
      end)

      assert {:discard, :payment_not_found} =
               perform_job(PollingWorker, %{
                 external_id: "does not exist",
                 organization_id: organization.id,
                 partner: "pagarme",
                 entitie: "payment"
               })

      assert %Payment{status: "pending"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end

    test "does not uppdate a payment when status is invalid" do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "settled",
          transaction: transaction,
          organization: organization
        )

      # epr =
      #   insert(:external_payment_reference,
      #     partner: "pagarme",
      #     payment: payment,
      #     organization: organization
      #   )

      insert(:pagarme_credential, organization: order.organization)

      # epr_id = epr.external_id

      # expect(PagarMeClientMock, :get_order, fn _client, ^epr_id ->
      #   {:ok, order_builder(epr.external_id, "canceled", 100)}
      # end)

      # assert {:error,
      #         %Ecto.Changeset{
      #           errors: [status: {"status transition not allowed", _}]
      #         }} =
      #          perform_job(PollingWorker, %{
      #            external_id: epr.external_id,
      #            partner: "pagarme",
      #            entitie: "payment"
      #          })

      assert %Payment{status: "settled"} =
               Finance.get_payment!(payment.organization_id, payment.id)

      assert %Transaction{status: "open"} =
               Finance.get_transaction!(transaction.organization_id, transaction.id)

      assert %Order{status: "open"} =
               Commerce.Orders.get_order!(order.organization_id, transaction.order_id)
    end
  end

  describe "process/1 for vtex" do
    test "returns :ok" do
      assert :ok =
               perform_job(PollingWorker, %{
                 external_id: "some_id",
                 partner: "vtex",
                 entitie: "payment"
               })
    end
  end

  def order_builder(order_id, status, amount) do
    %{
      "amount" => amount,
      "charges" => [],
      "closed" => true,
      "closed_at" => "2017-04-19T16:01:11Z",
      "code" => "62LVFN7I4R",
      "created_at" => "2017-04-19T16:01:09Z",
      "currency" => "BRL",
      "customer" => %{},
      "id" => "#{order_id}",
      "items" => [],
      "status" => "#{status}",
      "updated_at" => "2017-04-19T16:01:11Z"
    }
  end
end
