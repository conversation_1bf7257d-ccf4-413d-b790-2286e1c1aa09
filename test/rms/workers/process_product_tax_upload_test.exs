defmodule Rms.Workers.ProcessProductTaxUploadTest do
  use Rms.DataCase
  use Oban.Testing, repo: Rms.Repo

  @moduletag :localstack_integration

  alias Rms.Workers.ProcessProductTaxUpload
  alias Rms.Storage.UploadEntry
  import Rms.Factory

  alias Rms.Test.S3Helper

  @upload_bucket "test-bucket"

  setup do
    Application.put_env(:rms, :upload_bucket, @upload_bucket)
    S3Helper.ensure_test_bucket(@upload_bucket)
    S3Helper.clear_bucket(@upload_bucket)

    organization = insert(:organization)
    {:ok, %{organization: organization}}
  end

  describe "perform/1" do
    test "handles empty CSV file", %{organization: organization} do
      csv_path = csv_file_fixture([])
      upload = insert(:upload_entry, organization: organization)
      S3Helper.upload_file(@upload_bucket, upload.s3_key, csv_path)

      assert {:ok, 0} = perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})

      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "error"
    end

    test "handles missing required headers", %{organization: organization} do
      content = "ncm,uf\n12345678,SP\n"
      path = Path.join(System.tmp_dir!(), "invalid_headers.csv")
      File.write!(path, content)

      upload = insert(:upload_entry, organization: organization)
      S3Helper.upload_file(@upload_bucket, upload.s3_key, path)

      assert {:error,
              "missing required headers: origin, cfop, cst_icms, cst_pis, cst_cofins, icms_percentage, fcp_percentage, pis_percentage, cofins_percentage"} =
               perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})

      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "error"
      assert List.first(upload.error_messages) =~ "missing required headers"
    end

    test "handles invalid decimal values", %{organization: organization} do
      rows = [
        "12345678,SP,5102,00,01,01,invalid,2,1.65,7.6,SKU123,0,1234567,1"
      ]

      csv_path = csv_file_fixture(rows)
      upload = insert(:upload_entry, organization: organization)
      S3Helper.upload_file(@upload_bucket, upload.s3_key, csv_path)

      assert {:ok, 0} = perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})

      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "error"
      assert List.first(upload.error_messages) =~ "icms_percentage is invalid"
    end

    test "handles duplicate entries by updating them", %{organization: organization} do
      # Create existing product tax
      existing =
        insert(:product_taxes,
          organization: organization,
          ncm: "12345678",
          sku: nil,
          uf: "SP",
          icms_percentage: Decimal.new("18")
        )

      # Create CSV with same NCM/UF but different values
      rows = [
        "12345678,SP,5102,00,01,01,20,2,1.65,7.6,,0,1234567,1"
      ]

      csv_path = csv_file_fixture(rows)
      upload = insert(:upload_entry, organization: organization)
      S3Helper.upload_file(@upload_bucket, upload.s3_key, csv_path)

      assert {:ok, 1} = perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})

      # Verify record was updated
      updated = Rms.Repo.get!(Rms.Fiscal.ProductTaxes, existing.id)
      assert Decimal.equal?(updated.icms_percentage, Decimal.new("20"))
    end

    test "handles missing S3 file", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization)

      assert_raise ExAws.Error, fn ->
        perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})
      end

      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "processing"
    end

    test "handles already processed uploads", %{organization: organization} do
      upload = insert(:upload_entry, organization: organization, status: "completed")

      assert {:discard, message} =
               perform_job(ProcessProductTaxUpload, %{"upload_id" => upload.id})

      assert message =~ "cannot process upload in completed status"
    end
  end
end
