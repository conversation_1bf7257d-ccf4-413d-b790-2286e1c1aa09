defmodule Rms.Workers.ShopifyCreateProductFeedTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Workers.ShopifyCreateProductFeed
  alias Rms.Integrations.Shopify

  import Mox
  import ExUnit.CaptureLog

  @unsupported_context %{
    "data" => %{
      "productFeedCreate" => %{
        "productFeed" => nil,
        "userErrors" => [
          %{
            "field" => nil,
            "message" => "The shop doesn't support this country and language context."
          }
        ]
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 10,
        "requestedQueryCost" => 10,
        "throttleStatus" => %{
          "currentlyAvailable" => 1990,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @product_feed_already_exists %{
    "data" => %{
      "productFeedCreate" => %{
        "productFeed" => nil,
        "userErrors" => [
          %{
            "field" => ["input"],
            "message" =>
              "Country and language codes already exist as a product feed for this shop."
          }
        ]
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 10,
        "requestedQueryCost" => 10,
        "throttleStatus" => %{
          "currentlyAvailable" => 1990,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @product_feed_created %{
    "data" => %{
      "productFeedCreate" => %{
        "productFeed" => %{"id" => "gid://shopify/ProductFeed/1825701939"},
        "userErrors" => []
      }
    },
    "extensions" => %{
      "cost" => %{
        "actualQueryCost" => 10,
        "requestedQueryCost" => 10,
        "throttleStatus" => %{
          "currentlyAvailable" => 1990,
          "maximumAvailable" => 2000.0,
          "restoreRate" => 100.0
        }
      }
    }
  }

  @full_sync_success %{
    "data" => %{
      "productFullSync" => %{
        "userErrors" => []
      }
    }
  }

  setup :verify_on_exit!

  setup do
    expect(Shopify.Mock, :client, fn _shop, _credential -> :mock_client end)
    org = insert(:organization)
    insert(:shopify_credential, organization: org)

    {:ok, organization: org}
  end

  test "successfully creates a product feed", %{organization: organization} do
    expect(Shopify.Mock, :create_product_feed, fn :mock_client, _input ->
      {:ok, @product_feed_created}
    end)

    expect(Shopify.Mock, :run_product_full_sync, fn :mock_client,
                                                    "gid://shopify/ProductFeed/1825701939" ->
      {:ok, @full_sync_success}
    end)

    assert {:ok, @full_sync_success} =
             perform_job(ShopifyCreateProductFeed, %{organization_id: organization.id})
  end

  test "handles already existing product feed", %{organization: organization} do
    expect(Shopify.Mock, :create_product_feed, fn :mock_client, _input ->
      {:ok, @product_feed_already_exists}
    end)

    input = %{language: "PT_BR", country: "BR"}
    product_feed_id = "gid://shopify/ProductFeed/1825701939"

    feeds_response = %{
      "data" => %{
        "productFeeds" => %{
          "nodes" => [
            %{"id" => product_feed_id, "language" => input.language, "country" => input.country}
          ]
        }
      }
    }

    expect(Shopify.Mock, :get_product_feeds, fn _client ->
      {:ok, feeds_response}
    end)

    expect(Shopify.Mock, :run_product_full_sync, fn :mock_client, ^product_feed_id ->
      {:ok, @full_sync_success}
    end)

    assert {:ok, @full_sync_success} =
             perform_job(ShopifyCreateProductFeed, %{organization_id: organization.id})
  end

  test "handles unsupported country and language context", %{organization: organization} do
    Shopify.Mock
    |> expect(:create_product_feed, fn :mock_client, _input -> {:ok, @unsupported_context} end)
    |> expect(:create_product_feed, fn :mock_client, _input -> {:ok, @product_feed_created} end)

    expect(Shopify.Mock, :run_product_full_sync, fn :mock_client,
                                                    "gid://shopify/ProductFeed/1825701939" ->
      {:ok, @full_sync_success}
    end)

    assert {:ok, @full_sync_success} =
             perform_job(ShopifyCreateProductFeed, %{organization_id: organization.id})
  end

  test "logs unknown errors", %{organization: organization} do
    unknown_error_response = %{
      "data" => %{
        "productFeedCreate" => %{
          "productFeed" => nil,
          "userErrors" => [
            %{"field" => nil, "message" => "Some unknown error"}
          ]
        }
      }
    }

    Shopify.Mock
    |> expect(:create_product_feed, fn :mock_client, _input -> {:ok, unknown_error_response} end)

    job = %Oban.Job{args: %{"organization_id" => organization.id}}

    assert capture_log(fn ->
             assert {:error, ["Some unknown error"]} = ShopifyCreateProductFeed.perform(job)
           end) =~ "unkown errors: [\"Some unknown error\"]"
  end
end
