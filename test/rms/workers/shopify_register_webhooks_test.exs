defmodule Rms.Workers.ShopifyRegisterWebhooksTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Workers.ShopifyRegisterWebhooks
  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  setup :verify_on_exit!

  describe "perform/1" do
    setup do
      organization = insert(:organization)

      insert(:shopify_credential,
        shop: "test-shop",
        credential: "test-credential",
        organization: organization
      )

      expect(ShopifyMock, :client, fn "test-shop", "test-credential" ->
        :mock_client
      end)

      {:ok, organization: organization}
    end

    test "successfully registers a webhook", %{organization: organization} do
      topic = "PRODUCT_FEEDS_FULL_SYNC"
      args = %{"organization_id" => organization.id, "topic" => topic}

      expect(ShopifyMock, :create_webhook, fn :mock_client, ^topic, %{callbackUrl: _} ->
        {:ok, %{"data" => %{"webhookSubscriptionCreate" => %{"userErrors" => []}}}}
      end)

      assert :ok == ShopifyRegisterWebhooks.perform(%Oban.Job{args: args})
    end

    test "handles already taken address error", %{organization: organization} do
      topic = "PRODUCT_FEEDS_FULL_SYNC"
      args = %{"organization_id" => organization.id, "topic" => topic}

      expect(ShopifyMock, :create_webhook, fn :mock_client, ^topic, %{callbackUrl: _} ->
        {:ok,
         %{
           "data" => %{
             "webhookSubscriptionCreate" => %{
               "userErrors" => [
                 %{
                   "field" => ["webhookSubscription", "callbackUrl"],
                   "message" => "Address for this topic has already been taken"
                 }
               ]
             }
           }
         }}
      end)

      assert :ok == ShopifyRegisterWebhooks.perform(%Oban.Job{args: args})
    end

    test "handles other errors", %{organization: organization} do
      topic = "PRODUCT_FEEDS_FULL_SYNC"
      args = %{"organization_id" => organization.id, "topic" => topic}

      expect(ShopifyMock, :create_webhook, fn :mock_client, ^topic, %{callbackUrl: _} ->
        {:ok,
         %{
           "data" => %{
             "webhookSubscriptionCreate" => %{
               "userErrors" => [%{"field" => nil, "message" => "Some other error"}]
             }
           }
         }}
      end)

      assert {:error, ["Some other error"]} ==
               ShopifyRegisterWebhooks.perform(%Oban.Job{args: args})
    end
  end
end
