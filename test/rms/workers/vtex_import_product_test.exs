defmodule Rms.Workers.VTEXImportProductTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Workers.VTEXImportProduct
  alias Rms.Workers.VTEXImportProductVariant
  alias Rms.Integrations.VTEX.Mock

  setup :verify_on_exit!

  @mock_data %{
    "data" => %{
      "1" => [1, 2, 3],
      "2" => [4, 5, 6, 7, 8],
      "3" => [9, 10, 11, 12, 14],
      "4" => [13, 15, 16, 17],
      "5" => [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],
      "6" => [],
      "7" => [29, 30, 31, 32],
      "8" => [33],
      "9" => [34, 35, 36, 37, 38],
      "10" => [39, 40, 41, 42, 43, 44],
      "11" => [45, 46, 47, 48, 49],
      "12" => [50, 51, 52, 53],
      "13" => [54],
      "14" => [55, 56, 57, 58],
      "15" => [59, 60, 61, 62, 63],
      "16" => [64, 65, 66, 67, 68],
      "17" => [70, 71, 72, 73, 74],
      "18" => [69],
      "19" => [75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86],
      "20" => [87, 89, 91, 93, 97]
    },
    "range" => %{
      "total" => 2439,
      "from" => 1,
      "to" => 20
    }
  }
  test "perform/1 enqueues the correct jobs" do
    organization_id = insert(:vtex_credential).organization_id
    from = 1
    to = 20

    expect(Mock, :client, fn _, _ ->
      :mock_client
    end)

    expect(Mock, :list_product_and_sku, fn :mock_client, ^from, ^to ->
      {:ok, @mock_data}
    end)

    assert :ok =
             perform_job(VTEXImportProduct, %{
               "organization_id" => organization_id,
               "from" => from,
               "to" => to,
               "affiliate_id" => "GLL",
               "sales_channel_id" => 1,
               "product_sync_id" => 1
             })

    assert_enqueued(
      worker: VTEXImportProductVariant,
      args: %{
        "sku_id" => 1,
        "organization_id" => organization_id,
        "affiliate_id" => "GLL",
        "sales_channel_id" => 1
      }
    )

    assert_enqueued(
      worker: VTEXImportProductVariant,
      args: %{
        "sku_id" => 2,
        "organization_id" => organization_id,
        "affiliate_id" => "GLL",
        "sales_channel_id" => 1
      }
    )

    assert_enqueued(
      worker: VTEXImportProductVariant,
      args: %{
        "sku_id" => 3,
        "organization_id" => organization_id,
        "affiliate_id" => "GLL",
        "sales_channel_id" => 1
      }
    )

    assert_enqueued(
      worker: VTEXImportProduct,
      args: %{
        "organization_id" => organization_id,
        "from" => 21,
        "to" => 40,
        "affiliate_id" => "GLL",
        "sales_channel_id" => 1,
        "product_sync_id" => 1
      }
    )

    assert_enqueued(
      worker: VTEXImportProduct,
      args: %{
        "organization_id" => organization_id,
        "from" => 2421,
        "to" => 2439,
        "affiliate_id" => "GLL",
        "sales_channel_id" => 1,
        "product_sync_id" => 1
      }
    )
  end
end
