defmodule Rms.Workers.VTEXImportProductVariantTest do
  use Rms.DataCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Mox

  alias Rms.Commerce.Products.Product
  alias Rms.Commerce.Products.ProductVariant
  alias Rms.Commerce.Products.Addon
  alias Rms.Integrations.ProductSyncMapping
  alias Rms.Integrations.ProductVariantMapping
  alias Rms.Integrations.AddonMapping
  alias Rms.Integrations.ProductSyncEntry
  alias Rms.Integrations.VTEX.Mock
  alias Rms.Workers.VTEXImportProductVariant

  setup :verify_on_exit!

  @fulfillmenet_simulation %{
    "allowMultipleDeliveries" => true,
    "country" => nil,
    "geoCoordinates" => [],
    "itemMetadata" => nil,
    "items" => [
      %{
        "attachmentOfferings" => [],
        "availability" => "available",
        "catalogProvider" =>
          "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
        "id" => "1",
        "listPrice" => 30_500,
        "measurementUnit" => "un",
        "merchantName" => nil,
        "offerings" => [],
        "parentAssemblyBinding" => nil,
        "parentItemIndex" => nil,
        "price" => 30_500,
        "sellingPrice" => 30_500,
        "priceDefinition" => %{
          "calculatedSellingPrice" => 30_500,
          "reason" => nil,
          "sellingPrices" => [%{"quantity" => 1, "value" => 30_500}],
          "total" => 30_500
        },
        "priceTable" => nil,
        "priceTags" => [],
        "priceValidUntil" => "2025-05-27T12:44:38Z",
        "quantity" => 1,
        "requestIndex" => 0,
        "seller" => "1",
        "unitMultiplier" => 1.0
      }
    ],
    "logisticsInfo" => [
      %{
        "addressId" => nil,
        "deliveryChannels" => [
          %{"id" => "delivery", "stockBalance" => 79},
          %{"id" => "pickup-in-point", "stockBalance" => 79}
        ],
        "itemIndex" => 0,
        "quantity" => 1,
        "selectedDeliveryChannel" => nil,
        "selectedSla" => nil,
        "shipsTo" => ["BRA"],
        "slas" => [],
        "stockBalance" => 79
      }
    ],
    "messages" => [],
    "pickupPoints" => [],
    "postalCode" => nil,
    "totals" => [
      %{"id" => "Items", "name" => "Total dos Itens", "value" => 30_500}
    ]
  }

  @valid_sku_data %{
    "Id" => 3,
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "SalesChannels" => [1],
    "IsActive" => true,
    "AlternateIds" => %{"RefId" => "SKU123", "Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ]
  }

  @inactive_sku_data %{
    "Id" => 3,
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "SalesChannels" => [1],
    "IsActive" => false,
    "AlternateIds" => %{"RefId" => "SKU123", "Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ]
  }

  @invalid_sku_data %{
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "SalesChannels" => [1],
    "IsActive" => true,
    "AlternateIds" => %{"Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ]
  }

  setup do
    ps = insert(:product_sync)
    {:ok, product_sync: ps}
  end

  describe "import items" do
    test "perform/1 imports valid SKU data", %{product_sync: ps} do
      organization_id = insert(:vtex_credential, organization: ps.organization).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      # Assert creates ProductVariant
      assert product_variant = Repo.get_by(ProductVariant, sku: "SKU123")
      assert product_variant.bar_code == "1234567890123"
      assert product_variant.image_urls == ["http://example.com/image.jpg"]
      assert Decimal.eq?(product_variant.list_price, "305")
      assert [%{key: "Color", value: "Red"}] = product_variant.variation_types

      # Assert ProductSyncMapping exists
      assert product_sync_mapping = Repo.get_by(ProductSyncMapping, external_id: inspect(sku_id))
      assert product_sync_mapping.organization_id == organization_id
      assert product_sync_mapping.product_id == product_variant.product_id
      assert product_sync_mapping.external_id == inspect(sku_id)

      # Assert ProductVariantMapping exists
      assert product_variant_mapping =
               Repo.get_by(ProductVariantMapping,
                 external_id: to_string(@valid_sku_data["Id"]),
                 source: "vtex"
               )

      assert product_variant_mapping.organization_id == organization_id
      assert product_variant_mapping.product_variant_id == product_variant.id

      # Assert ProductSyncEntry exists
      assert [product_sync_entry] = Repo.all(ProductSyncEntry)
      assert product_sync_entry.status == "success"
      assert product_sync_entry.sku == "SKU123"
    end

    test "perform/1 discards inactive SKU data", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @inactive_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, :inactive_product} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(ProductVariant, sku: "SKU123")
    end

    test "perform/1 handles errors from VTEX API", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:error, :not_found}
      end)

      assert {:error, :not_found} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(ProductVariant, sku: "SKU123")
    end

    test "perform/1 creates product sync entry when importing invalid SKU data", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @invalid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(ProductVariant, sku: "SKU123")

      assert [
               %{
                 sku: nil,
                 status: "error",
                 errors: [
                   %{metadata: %{"errors" => changeset_errors}, code: "changeset_error"}
                 ]
               }
             ] = Repo.all(ProductSyncEntry)

      assert "can't be blank" in changeset_errors["sku"]
    end

    test "perform/1 importing the same product twice does not create two product variants", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, 2, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert [product_variant] = Repo.all(ProductVariant)
      assert product_variant.sku == "SKU123"
    end

    test "perform/1 importing inactive SKU data for an existing product variant archives it", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      # First import with valid SKU data
      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert product_variant = Repo.get_by(ProductVariant, sku: "SKU123")
      refute product_variant.archived_at

      # Second import with inactive SKU data
      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @inactive_sku_data}
      end)

      Repo.all(ProductVariantMapping)

      assert {:ok, _product} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert product_variant = Repo.get_by(ProductVariant, sku: "SKU123")
      assert product_variant.archived_at
    end

    test "perform/1 importing active SKU data for an archived product variant unarchives and updates it",
         %{
           product_sync: ps
         } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      # First import with valid SKU data
      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      product_variant = Repo.get_by(ProductVariant, sku: "SKU123")

      product_variant_mapping =
        Repo.get_by(ProductVariantMapping, product_variant_id: product_variant.id)

      # Archive the product variant
      product_variant
      |> Ecto.Changeset.change(archived_at: DateTime.utc_now() |> DateTime.truncate(:second))
      |> Repo.update!()

      # Second import with the same SKU data
      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      updated_product_variant = Repo.get_by(ProductVariant, sku: "SKU123")

      updated_product_variant_mapping =
        Repo.get_by(ProductVariantMapping, external_id: to_string(@valid_sku_data["Id"]))

      assert updated_product_variant.id == product_variant.id
      refute updated_product_variant.archived_at
      assert updated_product_variant_mapping.product_variant_id == updated_product_variant.id
      assert updated_product_variant_mapping.id == product_variant_mapping.id
    end

    test "perform/1 discards product that does not belong to trade policy", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 2

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:discard, :product_does_not_belong_to_trade_policy} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(ProductVariant, sku: "SKU123")
    end

    test "perform/1 importing two different product variants with the same ProductId does not create two products",
         %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id1 = 123
      sku_id2 = 124
      sales_channel_id = 1

      valid_sku_data_2 =
        Map.put(@valid_sku_data, "Id", sku_id2)
        |> Map.update!("AlternateIds", fn ids -> Map.put(ids, "RefId", "SKU124") end)

      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, 2, fn
        :mock_client, ^sku_id1 -> {:ok, @valid_sku_data}
        :mock_client, ^sku_id2 -> {:ok, valid_sku_data_2}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id1,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id2,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert [_] = Repo.all(Product)

      assert [product_variant1, product_variant2] =
               Repo.all(ProductVariant) |> Enum.sort_by(& &1.sku)

      assert product_variant1.sku == "SKU123"
      assert product_variant2.sku == "SKU124"
    end

    test "perform/1 does not unarchive duplicate variant", %{product_sync: product_sync} do
      organization = product_sync.organization
      product = insert(:product, organization: organization)

      archived_variant =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: DateTime.utc_now(),
          organization: organization
        )

      active_variant =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: nil,
          organization: organization
        )

      insert(:product_variant_mapping,
        organization: organization,
        product_variant: active_variant,
        external_id: "123",
        source: "vtex"
      )

      insert(:vtex_credential, organization: organization)
      mock_vtex_responses(123, product.id)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "organization_id" => organization.id,
                 "sku_id" => 123,
                 "sales_channel_id" => "1",
                 "affiliate_id" => "1",
                 "product_sync_id" => product_sync.id
               })

      assert Repo.reload(archived_variant).archived_at
      refute Repo.reload(active_variant).archived_at
      assert length(Repo.all(Product)) == 1
      assert length(Repo.all(ProductVariant)) == 2
    end

    test "updates the product variant mapping to the unarchived variant", %{
      product_sync: product_sync
    } do
      organization = product_sync.organization
      product = insert(:product, product_variants: [], organization: organization)

      variant1 =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: DateTime.utc_now(),
          organization: organization
        )

      variant2 =
        insert(:product_variant,
          product: product,
          sku: "123",
          archived_at: DateTime.utc_now(),
          organization: organization
        )

      pvm =
        insert(:product_variant_mapping,
          organization: organization,
          product_variant: variant2,
          external_id: "123",
          source: "vtex"
        )

      insert(:vtex_credential, organization: organization)
      mock_vtex_responses(123, product.id)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "organization_id" => organization.id,
                 "sku_id" => 123,
                 "sales_channel_id" => "1",
                 "affiliate_id" => "1",
                 "product_sync_id" => product_sync.id
               })

      updated_mapping = Repo.reload!(pvm)
      assert pvm.product_variant_id != variant1.id
      assert updated_mapping.product_variant_id == variant1.id
    end

    test "sets sold_out to true when stock balance is below safety net", %{product_sync: ps} do
      organization = insert(:organization, safety_net_quantity: 10)
      insert(:vtex_credential, organization: organization)
      sku_id = 123
      sales_channel_id = 1

      low_stock_simulation =
        put_in(
          @fulfillmenet_simulation,
          ["logisticsInfo", Access.at(0), "stockBalance"],
          5
        )

      expect(Mock, :client, fn _, _ -> :mock_client end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _, _ ->
        {:ok, low_stock_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization.id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert product_variant_mapping = Repo.get_by(ProductVariantMapping, external_id: "3")
      assert product_variant_mapping.sold_out == true
    end
  end

  @valid_sku_data_subscription %{
    "Id" => 3,
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "ProductDescription" => "Prime",
    "SalesChannels" => [1],
    "IsActive" => true,
    "AlternateIds" => %{"RefId" => "SKU123", "Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ],
    "Attachments" => [
      %{
        "Id" => 3,
        "Name" => "vtex.subscription.plano",
        "Keys" => [],
        "Fields" => [
          %{
            "FieldName" => "vtex.subscription.key.frequency",
            "MaxCaracters" => nil,
            "DomainValues" => "1 month"
          }
        ],
        "IsActive" => true,
        "IsRequired" => false
      }
    ]
  }

  @inactive_sku_data_subscription %{
    "Id" => 3,
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "ProductDescription" => "Prime",
    "SalesChannels" => [1],
    "IsActive" => false,
    "AlternateIds" => %{"RefId" => "SKU123", "Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ],
    "Attachments" => [
      %{
        "Id" => 3,
        "Name" => "vtex.subscription.plano",
        "Keys" => [],
        "Fields" => [
          %{
            "FieldName" => "vtex.subscription.key.frequency",
            "MaxCaracters" => nil,
            "DomainValues" => "1 month"
          }
        ],
        "IsActive" => true,
        "IsRequired" => false
      }
    ]
  }

  @invalid_sku_data_subscription %{
    "ProductId" => 123,
    "ProductName" => "Test Product",
    "ProductDescription" => "Prime",
    "SalesChannels" => [1],
    "IsActive" => true,
    "AlternateIds" => %{"Ean" => "1234567890123"},
    "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
    "SkuSpecifications" => [
      %{"IsFilter" => true, "FieldName" => "Color", "FieldValues" => ["Red"]}
    ],
    "Attachments" => [
      %{
        "Id" => 3,
        "Name" => "vtex.subscription.plano",
        "Keys" => [],
        "Fields" => [
          %{
            "FieldName" => "vtex.subscription.key.frequency",
            "MaxCaracters" => nil,
            "DomainValues" => "1 month"
          }
        ],
        "IsActive" => true,
        "IsRequired" => false
      }
    ]
  }

  describe "import subscription" do
    test "perform/1 imports valid SKU data", %{product_sync: ps} do
      organization_id = insert(:vtex_credential, organization: ps.organization).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      # Assert ProductSyncMapping exists
      assert product_sync_mapping = Repo.get_by(ProductSyncMapping, external_id: inspect(sku_id))
      assert product_sync_mapping.organization_id == organization_id
      assert product_sync_mapping.external_id == inspect(sku_id)

      # Assert ProductVariantMapping exists
      assert addon_mapping =
               Repo.get_by(AddonMapping,
                 external_id: to_string(@valid_sku_data["Id"]),
                 source: "vtex"
               )

      assert addon_mapping.organization_id == organization_id
      # assert product_variant_mapping.product_variant_id == product_variant.id

      # Assert ProductSyncEntry exists
      assert [product_sync_entry] = Repo.all(ProductSyncEntry)
      assert product_sync_entry.status == "success"
      assert product_sync_entry.sku == "SKU123"
    end

    test "perform/1 discards inactive SKU data", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @inactive_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, :inactive_product} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(AddonMapping,
               external_id: to_string(@valid_sku_data["Id"]),
               source: "vtex"
             )
    end

    test "perform/1 handles errors from VTEX API", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:error, :not_found}
      end)

      assert {:error, :not_found} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(AddonMapping,
               external_id: to_string(@valid_sku_data["Id"]),
               source: "vtex"
             )
    end

    test "perform/1 creates product sync entry when importing invalid SKU data", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @invalid_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:error, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })
    end

    test "perform/1 importing the same product twice does not create two product variants", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, 2, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert [_addon] = Repo.all(Addon)
      assert [addon_mapping] = Repo.all(AddonMapping)
      assert addon_mapping.external_id == "3"
    end

    test "perform/1 importing inactive SKU data for an existing addon archives it", %{
      product_sync: ps
    } do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 1

      # First import with valid SKU data
      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert addon_mapping =
               Repo.get_by(AddonMapping, external_id: to_string(@valid_sku_data["Id"]))

      assert addon = Repo.get_by(Addon, id: addon_mapping.addon_id)
      refute addon.archived_at

      # Second import with inactive SKU data
      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @inactive_sku_data_subscription}
      end)

      Repo.all(ProductVariantMapping)

      assert {:ok, _product} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert addon_mapping =
               Repo.get_by(AddonMapping, external_id: to_string(@valid_sku_data["Id"]))

      assert addon = Repo.get_by(Addon, id: addon_mapping.addon_id)
      assert addon.archived_at
    end

    test "perform/1 discards product that does not belong to trade policy", %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id = 123
      sales_channel_id = 2

      expect(Mock, :client, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok, @valid_sku_data_subscription}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:discard, :product_does_not_belong_to_trade_policy} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      refute Repo.get_by(AddonMapping, external_id: to_string(@valid_sku_data["Id"]))
    end

    test "perform/1 importing two different product variants with the same ProductId does not create two products",
         %{product_sync: ps} do
      organization_id = insert(:vtex_credential).organization_id
      sku_id1 = 123
      sku_id2 = 124
      sales_channel_id = 1

      valid_sku_data_2 =
        Map.put(@valid_sku_data_subscription, "Id", sku_id2)
        |> Map.update!("AlternateIds", fn ids -> Map.put(ids, "RefId", "SKU124") end)

      expect(Mock, :client, 2, fn _, _ ->
        :mock_client
      end)

      expect(Mock, :get_sku_by_id, 2, fn
        :mock_client, ^sku_id1 -> {:ok, @valid_sku_data_subscription}
        :mock_client, ^sku_id2 -> {:ok, valid_sku_data_2}
      end)

      expect(Mock, :private_simulate_fulfillment, 2, fn :mock_client, _affiliate_id, _payload ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id1,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization_id,
                 "sku_id" => sku_id2,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      assert [_] = Repo.all(Product)

      assert [_am_1, _am_2] =
               Repo.all(AddonMapping)
    end
  end

  describe "product name updates" do
    test "updates product name when it changes in VTEX", %{product_sync: ps} do
      organization = ps.organization
      insert(:vtex_credential, organization: organization)

      # Create initial product with different name
      product = insert(:product, name: "Old Name", organization: organization)

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "123",
        source: "vtex"
      )

      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ -> :mock_client end)

      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id ->
        {:ok,
         Map.merge(@valid_sku_data, %{
           "ProductId" => 123,
           "ProductName" => "New Name"
         })}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _, _ ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization.id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      updated_product = Repo.reload!(product)
      assert updated_product.name == "New Name"
    end

    test "doesn't update product name when it hasn't changed", %{product_sync: ps} do
      organization = ps.organization
      insert(:vtex_credential, organization: organization)

      product = insert(:product, name: "Test Product", organization: organization)

      insert(:product_sync_mapping,
        organization: organization,
        product: product,
        external_id: "123",
        source: "vtex"
      )

      sku_id = 123
      sales_channel_id = 1

      expect(Mock, :client, fn _, _ -> :mock_client end)
      expect(Mock, :get_sku_by_id, fn :mock_client, ^sku_id -> {:ok, @valid_sku_data} end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _, _ ->
        {:ok, @fulfillmenet_simulation}
      end)

      assert {:ok, _} =
               perform_job(VTEXImportProductVariant, %{
                 "affiliate_id" => "GLL",
                 "organization_id" => organization.id,
                 "sku_id" => sku_id,
                 "sales_channel_id" => sales_channel_id,
                 "product_sync_id" => ps.id
               })

      updated_product = Repo.reload!(product)
      assert updated_product.updated_at == product.updated_at
    end
  end

  describe "build_product_variant_changeset/4" do
    test "selects non-archived variant over archived one with the same SKU" do
      organization = insert(:organization)
      product = insert(:product, organization: organization)

      archived_variant =
        insert(:product_variant,
          product: product,
          sku: "SKU123",
          archived_at: DateTime.utc_now(),
          organization: organization
        )

      active_variant =
        insert(:product_variant,
          product: product,
          sku: "SKU123",
          archived_at: nil,
          organization: organization
        )

      sku_data = %{
        "AlternateIds" => %{"RefId" => "SKU123", "Ean" => "1234567890"},
        "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
        "SkuSpecifications" => []
      }

      changeset =
        VTEXImportProductVariant.build_product_variant_changeset(
          product,
          sku_data,
          1000,
          1200
        )

      assert changeset.data.id == active_variant.id
      refute changeset.data.id == archived_variant.id
    end
  end

  defp mock_vtex_responses(sku_id, product_id) do
    expect(Mock, :client, fn _, _ ->
      :mock_client
    end)

    expect(Mock, :get_sku_by_id, fn _, ^sku_id ->
      {:ok,
       %{
         "Id" => sku_id,
         "ProductId" => product_id,
         "IsActive" => true,
         "Name" => "Test Product",
         "RefId" => to_string(sku_id),
         "PackagedHeight" => 10,
         "PackagedLength" => 10,
         "PackagedWidth" => 10,
         "PackagedWeightKg" => 1,
         "Height" => nil,
         "Length" => nil,
         "Width" => nil,
         "WeightKg" => nil,
         "CubicWeight" => 1,
         "IsKit" => false,
         "CreationDate" => "2021-01-01T00:00:00",
         "RewardValue" => nil,
         "EstimatedDateArrival" => nil,
         "ManufacturerCode" => nil,
         "CommercialConditionId" => 1,
         "MeasurementUnit" => "un",
         "UnitMultiplier" => 1,
         "ModalType" => nil,
         "KitItensSellApart" => false,
         "Videos" => [],
         "ActivateIfPossible" => false,
         "SalesChannels" => ["1"],
         "ProductName" => "Test Product",
         "ProductDescription" => "Test Product Description",
         "ProductRefId" => "PROD123",
         "TaxCode" => "",
         "BrandId" => "1",
         "BrandName" => "Test Brand",
         "Images" => [%{"ImageUrl" => "http://example.com/image.jpg"}],
         "SkuSpecifications" => [],
         "ProductSpecifications" => [],
         "ProductCategoryIds" => "/1/2/3/",
         "ProductGlobalCategoryId" => nil,
         "ProductCategories" => %{
           "1" => "Category 1",
           "2" => "Category 2",
           "3" => "Category 3"
         },
         "CommercialConditionDescription" => "New",
         "AlternateIds" => %{
           "RefId" => to_string(sku_id),
           "Ean" => "1234567890"
         },
         "AlternateIdTypes" => ["RefId", "Ean"],
         "Attachments" => [],
         "Collections" => []
       }}
    end)

    expect(Mock, :private_simulate_fulfillment, fn _, _, _ ->
      {:ok,
       %{
         "items" => [
           %{
             "id" => sku_id,
             "requestIndex" => 0,
             "quantity" => 1,
             "seller" => "1",
             "sellerChain" => ["1"],
             "tax" => 0,
             "priceValidUntil" => "2023-12-31T23:59:59Z",
             "price" => 1000,
             "listPrice" => 1200,
             "rewardValue" => 0,
             "sellingPrice" => 1000,
             "offerings" => [],
             "priceTags" => [],
             "measurementUnit" => "un",
             "unitMultiplier" => 1,
             "parentItemIndex" => nil,
             "parentAssemblyBinding" => nil
           }
         ],
         "ratesAndBenefitsData" => %{
           "rateAndBenefitsIdentifiers" => [],
           "teaser" => []
         },
         "paymentData" => %{"installmentOptions" => []},
         "pickupPoints" => [],
         "subscriptionData" => nil,
         "totals" => [
           %{"id" => "Items", "name" => "Total dos Itens", "value" => 1000},
           %{"id" => "Discounts", "name" => "Total dos Descontos", "value" => 0},
           %{"id" => "Shipping", "name" => "Total do Frete", "value" => 0},
           %{"id" => "Tax", "name" => "Total dos Impostos", "value" => 0}
         ],
         "itemMetadata" => %{"items" => []}
       }}
    end)
  end

  describe "process/1 with existing products and sync configuration" do
    setup do
      organization = insert(:organization)

      # Create sync configuration where VTEX has lowest priority
      insert(:product_sync_configuration,
        organization: organization,
        field_priorities: %{
          "sku" => ["shopify", "linx_pos", "vtex"],
          "price" => ["linx_pos", "shopify", "vtex"],
          "bar_code" => ["vtex", "shopify", "linx_pos"]
        },
        default_priority: ["shopify", "linx_pos", "vtex"]
      )

      # Create a product sync record
      product_sync = insert(:product_sync, organization: organization)

      # Create VTEX credential
      insert(:vtex_credential, organization: organization)

      # Mock VTEX response data
      sku_data = %{
        "Id" => 123,
        "AlternateIds" => %{
          "RefId" => "SKU123",
          "Ean" => "7891234567890"
        },
        "ProductName" => "VTEX Product Name",
        "SkuName" => "VTEX SKU Name",
        "Attachments" => [],
        "SalesChannels" => ["1"],
        "IsActive" => true
      }

      simulation_data = %{
        "items" => [
          %{
            "price" => "100.00",
            "listPrice" => "150.00",
            "sellingPrice" => "100.00"
          }
        ]
      }

      {:ok,
       organization: organization,
       product_sync: product_sync,
       sku_data: sku_data,
       simulation_data: simulation_data}
    end

    test "respects field priorities when importing product with existing data", %{
      organization: organization,
      product_sync: product_sync,
      sku_data: sku_data,
      simulation_data: simulation_data
    } do
      # Create existing product with data from a higher priority source
      existing_product = insert(:product, organization: organization)

      existing_variant =
        insert(:product_variant,
          product: existing_product,
          organization: organization,
          sku: "SKU123",
          name: "Shopify Product Name",
          price: Decimal.new("200.00"),
          bar_code: "1234567890123",
          sync_metadata: %{
            "field_sources" => %{
              "name" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"},
              "price" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"},
              "bar_code" => %{"source" => "shopify", "updated_at" => "2024-01-01T00:00:00Z"}
            }
          }
        )

      # Create product variant mapping
      insert(:product_variant_mapping,
        organization: organization,
        product_variant: existing_variant,
        source: "shopify",
        external_id: "123"
      )

      # Mock VTEX API calls
      expect(Mock, :client, fn _, _ -> :mock_client end)

      expect(Mock, :get_sku_by_id, fn :mock_client, _sku_id ->
        {:ok, sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, simulation_data}
      end)

      # Perform the import
      args = %{
        "organization_id" => organization.id,
        "sku_id" => "123",
        "sales_channel_id" => "1",
        "affiliate_id" => "1",
        "product_sync_id" => product_sync.id
      }

      assert {:ok, _result} = perform_job(VTEXImportProductVariant, args)

      # Reload the variant
      updated_variant = Repo.reload!(existing_variant)

      # Name should not change (shopify has higher priority)
      assert updated_variant.name == "Shopify Product Name"

      # Price should not change (shopify has higher priority than vtex)
      assert Decimal.equal?(updated_variant.price, Decimal.new("200.00"))

      # Bar code should change (vtex has higher priority for bar_code)
      assert updated_variant.bar_code == "7891234567890"

      # Verify sync metadata was updated
      assert updated_variant.sync_metadata["field_sources"]["bar_code"]["source"] == "vtex"
      assert updated_variant.sync_metadata["field_sources"]["name"]["source"] == "shopify"
      assert updated_variant.sync_metadata["field_sources"]["price"]["source"] == "shopify"

      # Verify VTEX mappings were created
      product_mapping =
        Rms.Repo.get_by(Rms.Integrations.ProductSyncMapping,
          organization_id: organization.id,
          source: "vtex",
          external_id: inspect(sku_data["ProductId"])
        )

      assert product_mapping
      assert product_mapping.product_id == existing_product.id

      variant_mapping =
        Rms.Repo.get_by(Rms.Integrations.ProductVariantMapping,
          organization_id: organization.id,
          source: "vtex",
          external_id: inspect(sku_data["Id"])
        )

      assert variant_mapping
      assert variant_mapping.product_variant_id == existing_variant.id
    end

    test "allows updates when no source is recorded", %{
      organization: organization,
      product_sync: product_sync,
      sku_data: sku_data,
      simulation_data: simulation_data
    } do
      # Create existing product with no source metadata
      existing_product = insert(:product, organization: organization)

      existing_variant =
        insert(:product_variant,
          product: existing_product,
          organization: organization,
          sku: "SKU123",
          name: "Original Name",
          price: Decimal.new("200.00"),
          bar_code: "1234567890123"
        )

      # Mock VTEX API calls
      expect(Mock, :client, fn _, _ -> :mock_client end)

      expect(Mock, :get_sku_by_id, fn :mock_client, _sku_id ->
        {:ok, sku_data}
      end)

      expect(Mock, :private_simulate_fulfillment, fn :mock_client, _affiliate_id, _payload ->
        {:ok, simulation_data}
      end)

      # Perform the import
      args = %{
        "organization_id" => organization.id,
        "sku_id" => "123",
        "sales_channel_id" => "1",
        "affiliate_id" => "1",
        "product_sync_id" => product_sync.id
      }

      assert {:ok, _result} = perform_job(VTEXImportProductVariant, args)

      # Reload the variant
      updated_variant = Repo.reload!(existing_variant)

      # All fields should be updated since there was no source metadata
      assert Decimal.equal?(updated_variant.price, Decimal.new("1.00"))
      assert updated_variant.bar_code == "7891234567890"
      assert updated_variant.sku == "SKU123"

      # Verify sync metadata was added
      assert updated_variant.sync_metadata["field_sources"]["name"]["source"] == "vtex"
      assert updated_variant.sync_metadata["field_sources"]["price"]["source"] == "vtex"
      assert updated_variant.sync_metadata["field_sources"]["bar_code"]["source"] == "vtex"

      # Verify VTEX mappings were created
      product_mapping =
        Rms.Repo.get_by(Rms.Integrations.ProductSyncMapping,
          organization_id: organization.id,
          source: "vtex",
          external_id: inspect(sku_data["ProductId"])
        )

      assert product_mapping
      assert product_mapping.product_id == existing_product.id

      variant_mapping =
        Rms.Repo.get_by(Rms.Integrations.ProductVariantMapping,
          organization_id: organization.id,
          source: "vtex",
          external_id: inspect(sku_data["Id"])
        )

      assert variant_mapping
      assert variant_mapping.product_variant_id == existing_variant.id
    end
  end
end
