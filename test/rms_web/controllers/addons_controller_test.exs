defmodule RmsWeb.AddonsControllerTest do
  use RmsWeb.ConnCase
  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    {:ok, conn: authenticate_conn(conn, user), user: user, organization: user.organization}
  end

  describe "index/2" do
    test "lists all addons", %{conn: conn, organization: organization} do
      insert(:addon,
        organization: organization,
        metadata: %{
          "name" => "vtex.subscription.prime",
          "content" => %{
            "vtex.subscription.key.frequency" => "6 month"
          }
        }
      )

      insert(:addon,
        organization: organization,
        metadata: %{
          "name" => "vtex.subscription.prime",
          "content" => %{
            "vtex.subscription.key.frequency" => "6 month"
          }
        }
      )

      conn = get(conn, ~p"/api/addons")

      assert [
               %{
                 "description" => _,
                 "id" => _,
                 "image_url" => _,
                 "metadata" => %{"subscription_cycle" => 180},
                 "name" => _,
                 "type" => "subscription"
               },
               %{
                 "description" => _,
                 "id" => _,
                 "image_url" => _,
                 "metadata" => %{"subscription_cycle" => 180},
                 "name" => _,
                 "type" => "subscription"
               }
             ] = json_response(conn, 200)["addons"]

      assert json_response(conn, 200)["addons"] |> length() == 2
    end

    test "does not list other organization's addons", %{conn: conn} do
      other_organization = insert(:organization)
      insert(:addon, organization: other_organization)
      insert(:addon, organization: other_organization)

      conn = get(conn, ~p"/api/addons")
      assert json_response(conn, 200)["addons"] |> length() == 0
    end

    test "returns a `page` entry with pagination metadata", %{
      conn: conn,
      organization: organization
    } do
      insert_list(10, :addon, organization: organization)

      first_page = get(conn, ~p"/api/addons?limit=5")

      response_data = json_response(first_page, 200)
      assert length(response_data["addons"]) == 5

      assert %{
               "before" => nil,
               "after" => cursor_after,
               "limit" => 5
             } = response_data["page"]

      assert cursor_after

      second_page = get(conn, ~p"/api/addons?limit=5&after=#{cursor_after}")
      second_page_data = json_response(second_page, 200)
      assert response_data["addons"] != second_page_data["addons"]
    end

    test "list addons using some params", %{conn: conn, organization: organization} do
      insert(:addon, name: "addon1", type: "1", organization: organization)
      insert(:addon, name: "addon1", type: "2", organization: organization)
      insert(:addon, name: "addon2", type: "2", organization: organization)
      insert(:addon, name: "addon3", type: "3", organization: organization)
      insert(:addon, name: "addon4", type: "4", organization: organization)

      conn = get(conn, ~p"/api/addons?name=addon1&type=1")
      assert json_response(conn, 200)["addons"] |> length() == 1
      assert [%{"name" => "addon1", "type" => "1"}] = json_response(conn, 200)["addons"]
    end
  end
end
