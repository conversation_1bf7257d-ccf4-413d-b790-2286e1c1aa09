defmodule RmsWeb.AddressControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  alias Rms.Repo

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)
    {:ok, conn: put_req_header(conn, "accept", "application/json"), user: user}
  end

  describe "address" do
    test "update address", %{conn: conn, user: user} do
      address = insert(:address, organization: user.organization)

      attrs = %{
        "id" => address.id,
        "receiver_name" => "Chuck Norris",
        "city_name" => "Nova Iorque",
        "city_code" => "3550308",
        "state" => "NV",
        "country_name" => "Estados Unidos",
        "neighborhood" => "Fundao",
        "street" => "Rua dos Bobos",
        "street_type" => "Rua",
        "number" => "111",
        "zip" => "09812123",
        "complement" => "Escritorio"
      }

      conn = patch(conn, ~p"/api/addresses/#{address.id}", attrs)
      address = json_response(conn, 200)
      assert address == attrs
    end

    test "delete address", %{conn: conn, user: user} do
      address = insert(:address, organization: user.organization)
      conn = delete(conn, ~p"/api/addresses/#{address.id}")
      assert response(conn, 204)

      assert_error_sent 404, fn ->
        get(conn, ~p"/api/addresses/#{address.id}")
      end
    end
  end

  describe "customer" do
    test "delete address", %{conn: conn, user: user} do
      customer = insert(:customer, organization: user.organization)
      insert(:address, customer: customer, organization: user.organization)
      customer = Repo.preload(customer, :addresses)

      conn =
        delete(conn, ~p"/api/addresses/#{hd(customer.addresses).id}")

      get(conn, ~p"/api/customers/#{customer.id}")
      |> json_response(200)
      |> Map.get("data")
      |> Map.get("addresses")
      |> Enum.empty?()
      |> assert
    end

    test "update address", %{conn: conn, user: user} do
      customer = insert(:customer, organization: user.organization)
      address = insert(:address, customer: customer, organization: user.organization)
      customer = Repo.preload(customer, :addresses)

      attrs = %{
        "addresses" => [
          %{
            "id" => address.id,
            "receiver_name" => "Chuck Norris",
            "city_name" => "Nova Iorque",
            "city_code" => "3550308",
            "state" => "NV",
            "country_name" => "Estados Unidos",
            "neighborhood" => "Fundao",
            "street" => "Rua dos Bobos",
            "street_type" => "Rua",
            "number" => "111",
            "zip" => "09812123",
            "complement" => "Escritorio"
          }
        ]
      }

      conn = patch(conn, ~p"/api/customers/#{customer.id}/addresses", attrs)
      customer = json_response(conn, 200)["data"]["customer"]
      assert customer["addresses"] == attrs["addresses"]
    end

    test "update multiple addresses", %{conn: conn, user: user} do
      customer = insert(:customer, organization: user.organization)
      insert(:address, customer: customer, organization: user.organization)
      customer = Repo.preload(customer, :addresses)

      attrs = %{
        "addresses" => [
          %{
            "receiver_name" => "Chuck Norris",
            "city_name" => "Nova Iorque",
            "city_code" => "3333",
            "state" => "NV",
            "country_name" => "Estados Unidos",
            "neighborhood" => "Fundao",
            "street" => "Rua dos Bobos",
            "street_type" => "Rua",
            "number" => "111",
            "zip" => "09812123",
            "complement" => "Escritorio"
          },
          %{
            "receiver_name" => "Chuck Norris Filho",
            "city_name" => "Nova Iorque",
            "city_code" => "3333",
            "state" => "NV",
            "country_name" => "Estados Unidos",
            "neighborhood" => "Fundao",
            "street" => "Rua dos Bobos",
            "street_type" => "Rua",
            "number" => "111",
            "zip" => "09812123",
            "complement" => "Escritorio"
          }
        ]
      }

      conn = patch(conn, ~p"/api/customers/#{customer.id}/addresses", attrs)
      customer = json_response(conn, 200)["data"]["customer"]

      assert Enum.map(customer["addresses"], fn address -> Map.drop(address, ["id"]) end) ==
               attrs["addresses"]
    end

    test "update to empty address", %{conn: conn, user: user} do
      customer = insert(:customer, organization: user.organization)
      insert(:address, customer: customer, organization: user.organization)
      customer = Repo.preload(customer, :addresses)

      attrs = %{
        "addresses" => []
      }

      conn = patch(conn, ~p"/api/customers/#{customer.id}/addresses", attrs)
      customer = json_response(conn, 200)["data"]["customer"]
      assert customer["addresses"] == attrs["addresses"]
    end
  end
end
