defmodule RmsWeb.ApiTokensControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  describe "create/2" do
    test "can't create new api token authenticating as machine", %{conn: conn} do
      api_token = insert(:api_token)

      assert conn
             |> put_req_header("x-iglu-api-token", api_token.token)
             |> post(~p"/api/api_tokens")
             |> json_response(:forbidden)
    end

    test "creates new api token", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert %{"api_token" => api_token} =
               conn
               |> post(~p"/api/api_tokens")
               |> json_response(:ok)

      assert Rms.Accounts.get_api_token!(api_token)
    end

    test "can't create two api tokens", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)
      insert(:api_token, organization: user.organization)

      assert %{"errors" => _error_message} =
               conn
               |> post(~p"/api/api_tokens")
               |> json_response(:unprocessable_entity)
    end
  end

  describe "delete/2" do
    test "can't delete api token authenticating as machine", %{conn: conn} do
      api_token = insert(:api_token)

      assert conn
             |> put_req_header("x-iglu-api-token", api_token.token)
             |> delete(~p"/api/api_tokens")
             |> response(:forbidden)
    end

    test "deletes api token", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)
      insert(:api_token, organization: user.organization)

      assert conn
             |> delete(~p"/api/api_tokens")
             |> response(:no_content)

      assert Rms.Repo.aggregate(Rms.Accounts.ApiToken, :count) == 0
    end

    test "returns not found when no api token exists", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert_error_sent :not_found, fn ->
        conn
        |> delete(~p"/api/api_tokens")
        |> json_response(:not_found)
      end
    end
  end
end
