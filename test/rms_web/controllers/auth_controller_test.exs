defmodule RmsWeb.AuthControllerTest do
  use RmsWeb.ConnCase

  import Rms.Guardian
  import Rms.Factory

  describe "anonymous user" do
    test "GET /api/me", %{conn: conn} do
      conn = get(conn, ~p"/api/me")
      assert json_response(conn, 401)
    end
  end

  describe "user authenticated" do
    test "GET /api/me", %{conn: conn} do
      user = insert(:user)
      {:ok, token, _} = encode_and_sign(%{id: user.id}, %{}, token_type: :access)

      conn =
        conn
        |> put_req_header("authorization", "Bearer " <> token)
        |> get(~p"/api/me")

      user_id = user.id
      assert %{"resource" => %{"id" => ^user_id}} = json_response(conn, 200)
    end
  end

  describe "user with information" do
    test "GET /api/me", %{conn: conn} do
      org = insert(:organization)

      location =
        insert(:location,
          name: "Location 1",
          cnpj: "123456789",
          organization: org,
          address: build(:address, organization: org)
        )

      user =
        insert(:user,
          name: "<PERSON>",
          email: "<EMAIL>",
          external_id: "1",
          provider: "aaaaa",
          organization: org
        )

      insert(:location_users, user: user, location: location, organization: org)

      {:ok, token, _} = encode_and_sign(%{id: user.id}, %{}, token_type: :access)

      conn =
        conn
        |> put_req_header("authorization", "Bearer " <> token)
        |> get(~p"/api/me")

      user_id = user.id
      assert %{"resource" => %{"id" => ^user_id}} = json_response(conn, 200)
    end
  end

  describe "machine authenticated" do
    test "GET /api/me", %{conn: conn} do
      api_token = insert(:api_token)

      organization_id = api_token.organization_id

      assert %{"resource" => %{"id" => ^organization_id, "type" => "machine"}} =
               conn
               |> put_req_header("x-iglu-api-token", api_token.token)
               |> get(~p"/api/me")
               |> json_response(200)
    end
  end
end
