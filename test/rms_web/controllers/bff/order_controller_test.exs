defmodule RmsWeb.BFF.OrderControllerTest do
  use RmsWeb.ConnCase, async: true

  import Rms.Factory
  import Mox

  alias Rms.Settings

  @ecommerce_setting_key "ecommerce"

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)
    customer = insert(:customer, organization: organization)
    location = insert(:location, organization: organization)
    staff = insert(:staff, organization: organization)
    insert(:assign_location, user: user, location: location, organization: organization)
    conn = authenticate_conn(conn, user)

    {:ok,
     conn: conn,
     user: user,
     location: location,
     organization: organization,
     staff: staff,
     customer: customer}
  end

  describe "create_order/2" do
    test "creates order without line items", %{
      conn: conn,
      location: loc,
      customer: customer,
      staff: staff
    } do
      cart =
        insert(:cart,
          organization: loc.organization,
          customer: customer,
          staff: staff,
          location: loc
        )

      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert response["order"]
      assert response["order"]["customer"]["id"] == customer.id
      assert response["order"]["staff"]["id"] == staff.id
    end

    test "creates order with line items", %{
      conn: conn,
      user: user,
      location: loc,
      customer: customer
    } do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          location: loc,
          metadata: %{notes: "some random notes"}
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      order_attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/bff/create_order", order_attrs)
      assert response = json_response(conn, :created)
      assert response["order"]
      assert response["order"]["notes"] == "some random notes"
    end

    test "creates order with address", %{
      conn: conn,
      user: user,
      location: loc,
      customer: customer
    } do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      address =
        insert(:address,
          organization: user.organization,
          receiver_name: "Chuck Norris",
          city_name: "São Paulo",
          state: "SP",
          country_name: "Brazil",
          neighborhood: "Jardim Paulista",
          street: "Av. Paulista",
          street_type: "Avenue",
          number: "1000",
          zip: "01310100",
          complement: "Apt 1001"
        )

      cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          location: loc,
          shipping_address: address
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      order_attrs =
        %{
          "cart_id" => cart.id,
          "location_id" => loc.id,
          "customer_id" => customer.id
        }

      conn = post(conn, ~p"/bff/create_order", order_attrs)
      assert response = json_response(conn, :created)
      assert response["order"]

      assert response["order"]["shipping_address"] == %{
               "receiver_name" => "Chuck Norris",
               "city_name" => "São Paulo",
               "state" => "SP",
               "country_name" => "Brazil",
               "neighborhood" => "Jardim Paulista",
               "street" => "Av. Paulista",
               "street_type" => "Avenue",
               "number" => "1000",
               "zip" => "01310100",
               "complement" => "Apt 1001"
             }
    end

    test "creates order with discounts", %{
      conn: conn,
      user: user,
      location: loc,
      customer: customer
    } do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          location: loc
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      insert(:discount,
        organization: user.organization,
        cart: cart,
        type: "fixed",
        value: "10.00",
        description: "aaaa"
      )

      insert(:discount,
        organization: user.organization,
        cart: cart,
        type: "percentage",
        value: "10.00",
        description: "bbbb"
      )

      order_attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/bff/create_order", order_attrs)
      assert response = json_response(conn, :created)

      assert response["order"]["discounts"] == [
               %{
                 "type" => "fixed",
                 "value" => "10.00",
                 "description" => "aaaa"
               },
               %{
                 "type" => "percentage",
                 "value" => "10.00",
                 "description" => "bbbb"
               }
             ]
    end

    test "creates order with transaction customer data", %{
      conn: conn,
      user: user,
      location: loc,
      customer: customer
    } do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          location: loc
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      transaction_customer = %{
        "name" => "Transaction Customer",
        "email" => "<EMAIL>",
        "document" => "05774088529",
        "document_type" => "cpf"
      }

      order_attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id,
        "transaction_customer" => transaction_customer,
        "create_transaction" => true
      }

      conn = post(conn, ~p"/bff/create_order", order_attrs)
      assert response = json_response(conn, :created)
      assert order = response["order"]

      # Verify transaction was created with customer
      transaction = Rms.Repo.get!(Rms.Finance.Transaction, order["transaction"]["id"])
      transaction = Rms.Repo.preload(transaction, :customer)

      assert transaction.customer != nil
      assert transaction.customer.name == "Transaction Customer"
      assert transaction.customer.email == "<EMAIL>"
      assert transaction.customer.document_type == "cpf"
    end
  end

  describe "create_order with returns" do
    setup %{conn: conn, user: user, location: loc} do
      customer = insert(:customer, organization: user.organization)
      staff = insert(:staff, organization: user.organization)

      product = insert(:product, organization: user.organization)

      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: product
        )

      # Create original order with items that will be returned
      original_cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          staff: staff,
          location: loc,
          total_price: Decimal.new("100"),
          total_items_list_price: Decimal.new("100"),
          total_items_selling_price: Decimal.new("100")
        )

      delivery_group =
        insert(:delivery_group,
          cart: original_cart,
          organization: user.organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: user.organization,
        product_variant: product_variant,
        quantity: 2,
        list_price: "50",
        selling_price: "50",
        total_price: "100"
      )

      original_cart =
        Rms.Repo.preload(original_cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: [:product]]]
        ])

      {:ok, original_order} =
        Rms.Commerce.Orders.create_order(
          user.organization_id,
          Rms.Commerce.Orders.format_cart(original_cart, %{
            "cart_id" => original_cart.id,
            "location_id" => loc.id
          }),
          allowed_location_ids: :all
        )

      original_order = Rms.Repo.preload(original_order, fulfillments: [:line_items, :order])
      original_line_item = List.first(List.first(original_order.fulfillments).line_items)
      original_line_item = Rms.Repo.preload(original_line_item, fulfillment: [:order])

      # Create new cart for the order with return
      new_cart =
        insert(:cart,
          organization: user.organization,
          customer: customer,
          staff: staff,
          location: loc,
          total_price: Decimal.new("75"),
          total_items_list_price: Decimal.new("75"),
          total_items_selling_price: Decimal.new("75")
        )

      new_delivery_group =
        insert(:delivery_group,
          cart: new_cart,
          organization: user.organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: new_delivery_group,
        organization: user.organization,
        product_variant: product_variant,
        quantity: 1,
        list_price: "75",
        selling_price: "75",
        total_price: "75"
      )

      new_cart =
        Rms.Repo.preload(new_cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [product_variant: :product]]
        ])

      {:ok,
       conn: conn,
       customer: customer,
       original_line_item: original_line_item,
       product_variant: product_variant,
       new_cart: new_cart,
       location: loc}
    end

    test "successfully creates order with return when credit <= order total", %{
      conn: conn,
      original_line_item: original_line_item,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      staff = insert(:staff, organization: loc.organization)

      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "customer_id" => customer.id,
        "returned_line_items" => [
          %{
            "line_item_id" => original_line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert order = response["order"]

      # Verify order total and payment
      assert order["total_price"] == "75.00"
      assert payment = List.first(order["transaction"]["payments"])
      assert payment["amount"] == "50.00"
      assert payment["method"] == "return_credit"
      assert payment["status"] == "settled"

      # Verify credit was created and used
      assert credit =
               Rms.Repo.get_by(Rms.Finance.IgluCredit,
                 amount: Decimal.new("50.00"),
                 status: "used"
               )

      assert credit.metadata["reference"]["type"] == "reverse_fulfillment"
    end

    test "create reverse fulfilment without cart", %{
      conn: conn,
      original_line_item: original_line_item,
      customer: customer,
      location: loc
    } do
      staff = insert(:staff, organization: loc.organization)

      attrs = %{
        "cart_id" => nil,
        "customer_id" => customer.id,
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "returned_line_items" => [
          %{
            "line_item_id" => original_line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert true = response["success"]
    end

    test "fails when returned item not found", %{
      conn: conn,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id,
        "returned_line_items" => [
          %{
            "line_item_id" => 0,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert json_response(conn, :unprocessable_entity)
    end

    test "fails when trying to return more than original quantity", %{
      conn: conn,
      original_line_item: original_line_item,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id,
        "returned_line_items" => [
          %{
            "line_item_id" => original_line_item.id,
            "returned_quantity" => 3,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert json_response(conn, :unprocessable_entity)
    end

    test "includes return_credit_origin in order when using return credits", %{
      conn: conn,
      original_line_item: original_line_item,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      staff = insert(:staff, organization: loc.organization)

      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "customer_id" => customer.id,
        "returned_line_items" => [
          %{
            "line_item_id" => original_line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert order = response["order"]

      # Verify return_credit_origin contains the original order ID
      assert order["return_credit_origin"] == [original_line_item.fulfillment.order_id]
    end

    test "return_credit_origin is empty for orders without return credits", %{
      conn: conn,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert order = response["order"]

      # Verify return_credit_origin is empty
      assert order["return_credit_origin"] == []
    end

    test "successfully creates order with return and transaction customer data", %{
      conn: conn,
      original_line_item: original_line_item,
      new_cart: cart,
      location: loc,
      customer: customer
    } do
      staff = insert(:staff, organization: loc.organization)

      transaction_customer = %{
        "name" => "Transaction Customer",
        "email" => "<EMAIL>",
        "document" => "05774088529",
        "document_type" => "cpf"
      }

      attrs = %{
        "cart_id" => cart.id,
        "location_id" => loc.id,
        "staff_id" => staff.id,
        "customer_id" => customer.id,
        "transaction_customer" => transaction_customer,
        "returned_line_items" => [
          %{
            "line_item_id" => original_line_item.id,
            "returned_quantity" => 1,
            "reason" => "defective"
          }
        ]
      }

      conn = post(conn, ~p"/bff/create_order", attrs)
      assert response = json_response(conn, :created)
      assert order = response["order"]

      # Verify order total and payment
      assert order["total_price"] == "75.00"
      assert payment = List.first(order["transaction"]["payments"])
      assert payment["amount"] == "50.00"
      assert payment["method"] == "return_credit"
      assert payment["status"] == "settled"

      # Verify credit was created and used
      assert credit =
               Rms.Repo.get_by(Rms.Finance.IgluCredit,
                 amount: Decimal.new("50.00"),
                 status: "used"
               )

      assert credit.metadata["reference"]["type"] == "reverse_fulfillment"

      # Verify transaction customer was created and associated with the transaction
      transaction = Rms.Repo.get!(Rms.Finance.Transaction, order["transaction"]["id"])
      transaction = Rms.Repo.preload(transaction, :customer)

      assert transaction.customer != nil
      assert transaction.customer.name == "Transaction Customer"
      assert transaction.customer.email == "<EMAIL>"
      assert transaction.customer.document_type == "cpf"
      assert transaction.customer.document == "05774088529"
    end
  end

  describe "get_order/2" do
    test "returns order when exists", %{conn: conn, organization: organization, staff: staff} do
      customer = params_for(:customer, organization: organization)
      reference_time = DateTime.utc_now() |> DateTime.truncate(:second)

      order =
        insert(:order,
          organization: organization,
          staff: staff,
          reference_at: reference_time
        )

      insert(:order_customer, customer: customer, organization: organization, order: order)
      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)
      assert response["order"]
      assert response["order"]["id"] == order.id
      assert response["order"]["customer"]
      assert response["order"]["staff"]
      assert response["order"]["source"] == order.source
      assert response["order"]["reference_at"] == DateTime.to_iso8601(reference_time)
    end

    test "returns 404 when order not found", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/bff/get_order/999999")
      end
    end

    test "returns order with line items", %{conn: conn, organization: organization, location: loc} do
      pv =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      order =
        insert(:order,
          organization: organization,
          location: loc
        )

      fulfillment =
        insert(:fulfillment,
          organization: organization,
          order: order
        )

      line_item =
        insert(:line_item,
          location: loc,
          product_variant: pv,
          quantity: 1,
          organization: organization,
          fulfillment: fulfillment
        )

      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)
      assert response["order"]
      assert [fulfillment_resp] = response["order"]["fulfillments"]
      assert [line_item_resp] = fulfillment_resp["line_items"]
      assert line_item_resp["id"] == line_item.id
      assert line_item_resp["quantity"] == 1
    end

    test "returns order with address", %{conn: conn, organization: organization, location: loc} do
      address =
        params_for(:address,
          organization: organization,
          receiver_name: "Chuck Norris",
          city_name: "São Paulo",
          state: "SP",
          country_name: "Brazil",
          neighborhood: "Jardim Paulista",
          street: "Av. Paulista",
          street_type: "Avenue",
          number: "1000",
          zip: "01310100",
          complement: "Apt 1001"
        )

      order =
        insert(:order,
          organization: organization,
          location: loc,
          shipping_address: address
        )

      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)
      assert response["order"]

      assert response["order"]["shipping_address"] == %{
               "receiver_name" => "Chuck Norris",
               "city_name" => "São Paulo",
               "state" => "SP",
               "country_name" => "Brazil",
               "neighborhood" => "Jardim Paulista",
               "street" => "Av. Paulista",
               "street_type" => "Avenue",
               "number" => "1000",
               "zip" => "01310100",
               "complement" => "Apt 1001"
             }
    end

    test "returns order with discounts", %{conn: conn, organization: organization, location: loc} do
      order =
        insert(:order,
          organization: organization,
          location: loc
        )

      insert(:discount,
        organization: organization,
        order: order,
        type: "fixed",
        value: "10.00",
        description: "Fixed discount"
      )

      insert(:discount,
        organization: organization,
        order: order,
        type: "percentage",
        value: "10.00",
        description: "Percentage discount"
      )

      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)

      assert response["order"]["discounts"] == [
               %{
                 "type" => "fixed",
                 "value" => "10.00",
                 "description" => "Fixed discount"
               },
               %{
                 "type" => "percentage",
                 "value" => "10.00",
                 "description" => "Percentage discount"
               }
             ]
    end

    test "returns order with return line items", %{
      conn: conn,
      organization: organization,
      location: loc
    } do
      order = insert(:order, organization: organization, location: loc)
      fulfillment = insert(:fulfillment, organization: organization, order: order)

      pv =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      line_item =
        insert(:line_item,
          organization: organization,
          location: loc,
          fulfillment: fulfillment,
          product_variant: pv,
          quantity: 3
        )

      insert(:reverse_fulfillment_line_item,
        reverse_fulfillment: insert(:reverse_fulfillment, organization: organization),
        organization: organization,
        line_item: line_item,
        returned_quantity: 2,
        reason: "defective"
      )

      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)
      assert [return_item] = response["order"]["returned_line_items"]
      assert return_item["returned_quantity"] == 2
      assert return_item["line_item"]["id"] == line_item.id
    end

    test "returns current_quantity with returned items", %{
      conn: conn,
      organization: organization,
      location: loc
    } do
      order = insert(:order, organization: organization, location: loc)
      fulfillment = insert(:fulfillment, organization: organization, order: order)

      pv =
        insert(:product_variant,
          organization: organization,
          product: build(:product, organization: organization)
        )

      line_item =
        insert(:line_item,
          organization: organization,
          location: loc,
          fulfillment: fulfillment,
          product_variant: pv,
          quantity: 5
        )

      insert(:reverse_fulfillment_line_item,
        reverse_fulfillment: insert(:reverse_fulfillment, organization: organization),
        organization: organization,
        line_item: line_item,
        returned_quantity: 2,
        reason: "defective"
      )

      conn = get(conn, ~p"/bff/get_order/#{order.id}")

      assert response = json_response(conn, 200)
      assert [fulfillment_resp] = response["order"]["fulfillments"]
      assert [line_item_resp] = fulfillment_resp["line_items"]
      assert line_item_resp["quantity"] == 5
      assert line_item_resp["current_quantity"] == 3
    end
  end

  describe "external_orders/2" do
    test "returns 422 when e-commerce setting is not found", %{conn: conn, organization: org} do
      refute Settings.get_organization_setting(org.id, @ecommerce_setting_key)

      conn = get(conn, ~p"/bff/external-orders")
      assert response = json_response(conn, 422)

      assert %{
               "error" => %{
                 "detail" => "e-commerce integration settings not found or not configured."
               }
             } == response
    end

    test "returns 422 when e-commerce setting is malformed (missing 'data' key)", %{
      conn: conn,
      organization: org
    } do
      insert(:organization_setting,
        organization: org,
        key: @ecommerce_setting_key,
        value: %{"some_other_structure" => true}
      )

      conn = get(conn, ~p"/bff/external-orders")
      assert response = json_response(conn, 422)

      assert %{
               "error" => %{
                 "detail" => "e-commerce integration settings are improperly configured."
               }
             } == response
    end

    test "returns 422 when e-commerce setting is malformed ('data' key exists but is nil)", %{
      conn: conn,
      organization: org
    } do
      insert(:organization_setting,
        organization: org,
        key: @ecommerce_setting_key,
        value: %{"data" => nil}
      )

      conn = get(conn, ~p"/bff/external-orders")
      assert response = json_response(conn, 422)

      assert %{
               "error" => %{
                 "detail" => "e-commerce integration settings are improperly configured."
               }
             } == response
    end

    test "returns external orders", %{conn: conn, user: user, organization: org} do
      insert(:shopify_credential, organization: user.organization)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :find_orders, 1, fn _, params ->
        assert params[:query] == "fulfillment_status:fulfilled AND my search term"

        {:ok,
         %{
           orders: [
             %{
               "id" => "gid://shopify/Order/1",
               "cancelledAt" => nil,
               "createdAt" => "2025-03-28T17:27:48Z",
               "returnStatus" => "RETURNED",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "150.75"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322111",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444441",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/2",
               "cancelledAt" => nil,
               "createdAt" => "2025-03-28T17:30:09Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "200.00"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322112",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444442",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/3",
               "cancelledAt" => nil,
               "createdAt" => "2025-03-28T17:37:09Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "250.50"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322113",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444443",
                 "name" => "Ecommerce"
               }
             }
           ],
           last_cursor: "cursor"
         }}
      end)

      conn = get(conn, ~p"/bff/external-orders?search=my%20search%20term")
      json = json_response(conn, 200)

      assert json["orders"]
      assert json["metadata"]["count"] == length(json["orders"])
      assert Enum.at(json["orders"], 0)["name"] == "#322111"
      assert json["metadata"]["last_cursor"] == "cursor"

      first_order = Enum.at(json["orders"], 0)
      assert first_order["id"] == nil
      assert first_order["external_id"] == "gid://shopify/Order/1"
      assert first_order["name"] == "#322111"
      assert first_order["status"] == "paid"
      assert first_order["paid"] == true
      assert first_order["total_price"] == "150.75"
      assert first_order["total_price_with_addons"] == "150.75"
      assert first_order["discounts"] == []
      assert first_order["fulfillments"] == []
      assert first_order["addons"] == []
      assert first_order["staff"] == nil
      assert first_order["cashier"] == nil
      assert first_order["transaction"] == nil
      assert first_order["shipping_address"] == nil
      assert first_order["returned_line_items"] == []
      assert first_order["location_id"] == nil
      assert DateTime.from_iso8601(first_order["inserted_at"])

      customer = first_order["customer"]
      assert customer["id"] == nil
      assert customer["document_type"] == nil
      assert customer["document"] == nil
      assert customer["email"] == "<EMAIL>"
      assert customer["name"] == "Nome de Alguem"
      assert customer["primary_phone_number"] == nil
    end

    test "returns external orders with internal_order_id when matched", %{
      conn: conn,
      organization: org
    } do
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ -> :mock_client end)

      mock_shopify_orders = [
        %{"id" => "gid://shopify/Order/1", "name" => "Shopify Order 1"},
        %{"id" => "gid://shopify/Order/2", "name" => "Shopify Order 2"},
        %{"id" => "gid://shopify/Order/3", "name" => "Shopify Order 3"}
      ]

      mock_shopify_response = {:ok, %{orders: mock_shopify_orders, last_cursor: "cursor123"}}

      expect(Rms.Integrations.Shopify.Mock, :find_orders, fn :mock_client, query_params ->
        assert query_params[:query] == "fulfillment_status:fulfilled"
        assert query_params[:first] == 10
        mock_shopify_response
      end)

      internal_order_1 =
        insert(:order, organization: org, external_id: "gid://shopify/Order/1", source: "shopify")

      internal_order_3 =
        insert(:order, organization: org, external_id: nil, source: "shopify")

      insert(:fulfillment,
        order: internal_order_3,
        organization: org,
        external_reference: "gid://shopify/Order/3"
      )

      conn = get(conn, ~p"/bff/external-orders?limit=10")

      assert response = json_response(conn, 200)

      assert response["metadata"] == %{
               "after" => nil,
               "before" => nil,
               "first" => 10,
               "count" => 3,
               "last_cursor" => "cursor123"
             }

      assert length(response["orders"]) == 3

      order1 = Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/1"))
      order2 = Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/2"))
      order3 = Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/3"))

      assert order1["id"] == internal_order_1.id
      assert order2["id"] == nil
      assert order3["id"] == internal_order_3.id

      assert order1["name"] == "Shopify Order 1"
      assert order2["name"] == "Shopify Order 2"
      assert order3["name"] == "Shopify Order 3"
    end

    test "filters out external orders that exist internally with source 'iglu'", %{
      conn: conn,
      organization: org
    } do
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      mock_shopify_orders = [
        # No match
        %{"id" => "gid://shopify/Order/NEW", "name" => "Order NEW"},
        # Matches iglu internal
        %{"id" => "gid://shopify/Order/IGLU", "name" => "Order IGLU"},
        # Matches shopify internal
        %{"id" => "gid://shopify/Order/SHOPIFY", "name" => "Order SHOPIFY"}
      ]

      mock_shopify_response = {:ok, %{orders: mock_shopify_orders, last_cursor: "cursor789"}}

      # Setup internal data
      insert(:order, organization: org, external_id: "gid://shopify/Order/IGLU", source: "iglu")

      shopify_internal =
        insert(:order,
          organization: org,
          external_id: "gid://shopify/Order/SHOPIFY",
          source: "shopify"
        )

      expect(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)
      expect(Rms.Integrations.Shopify.Mock, :find_orders, fn _, _ -> mock_shopify_response end)

      conn = get(conn, ~p"/bff/external-orders")
      assert response = json_response(conn, 200)

      # Should only contain NEW and SHOPIFY orders
      assert length(response["orders"]) == 2

      order_new = Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/NEW"))

      order_iglu =
        Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/IGLU"))

      order_shopify =
        Enum.find(response["orders"], &(&1["external_id"] == "gid://shopify/Order/SHOPIFY"))

      assert order_new["id"] == nil
      # Filtered out
      assert order_iglu == nil
      assert order_shopify["id"] == shopify_internal.id

      # Count reflects filtered list
      assert response["metadata"]["count"] == 2
    end

    test "handles validation errors", %{conn: conn, organization: org} do
      insert(:shopify_credential, organization: org)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      conn = get(conn, ~p"/bff/external-orders?limit=invalid")
      assert response = json_response(conn, 422)
      assert response["errors"]["limit"] == ["is invalid"]
    end

    test "returns external orders in newest to oldest order", %{
      conn: conn,
      user: user,
      organization: org
    } do
      insert(:shopify_credential, organization: user.organization)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      # Mock responses with orders having different creation timestamps
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :find_orders, 1, fn _, params ->
        # Ensure reverse parameter is being set to true
        assert params[:reverse] == true

        {:ok,
         %{
           orders: [
             %{
               "id" => "gid://shopify/Order/3",
               "cancelledAt" => nil,
               # newest order
               "createdAt" => "2025-03-30T10:00:00Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "300.00"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322113",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444443",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/2",
               "cancelledAt" => nil,
               # middle order
               "createdAt" => "2025-03-29T15:30:00Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "200.00"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322112",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444442",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/1",
               "cancelledAt" => nil,
               # oldest order
               "createdAt" => "2025-03-28T09:15:00Z",
               "returnStatus" => "RETURNED",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "150.75"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322111",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444441",
                 "name" => "Ecommerce"
               }
             }
           ],
           last_cursor: "cursor"
         }}
      end)

      conn = get(conn, ~p"/bff/external-orders")
      json = json_response(conn, 200)

      assert json["orders"]
      assert length(json["orders"]) == 3

      # Verify orders are returned in newest to oldest order based on their Shopify createdAt
      # newest
      assert Enum.at(json["orders"], 0)["external_id"] == "gid://shopify/Order/3"
      # middle
      assert Enum.at(json["orders"], 1)["external_id"] == "gid://shopify/Order/2"
      # oldest
      assert Enum.at(json["orders"], 2)["external_id"] == "gid://shopify/Order/1"
    end

    test "returns external orders sorted by inserted_at from newest to oldest", %{
      conn: conn,
      user: user,
      organization: org
    } do
      insert(:shopify_credential, organization: user.organization)

      insert(:organization_setting,
        key: "ecommerce",
        value: %{data: "shopify"},
        organization: org
      )

      # Mock responses with orders having different creation timestamps
      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :find_orders, 1, fn _, params ->
        # Ensure reverse parameter is being set to true
        assert params[:reverse] == true

        {:ok,
         %{
           orders: [
             %{
               "id" => "gid://shopify/Order/3",
               "cancelledAt" => nil,
               # newest order
               "createdAt" => "2025-03-30T10:00:00Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "300.00"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322113",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444443",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/2",
               "cancelledAt" => nil,
               # middle order
               "createdAt" => "2025-03-29T15:30:00Z",
               "returnStatus" => "NO_RETURN",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "200.00"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322112",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444442",
                 "name" => "Ecommerce"
               }
             },
             %{
               "id" => "gid://shopify/Order/1",
               "cancelledAt" => nil,
               # oldest order
               "createdAt" => "2025-03-28T09:15:00Z",
               "returnStatus" => "RETURNED",
               "displayFinancialStatus" => "PAID",
               "displayFulfillmentStatus" => "FULFILLED",
               "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "150.75"}},
               "customer" => %{
                 "email" => "<EMAIL>",
                 "displayName" => "Nome de Alguem",
                 "phone" => nil
               },
               "name" => "#322111",
               "retailLocation" => %{
                 "id" => "gid://shopify/Location/47354444441",
                 "name" => "Ecommerce"
               }
             }
           ],
           last_cursor: "cursor"
         }}
      end)

      conn = get(conn, ~p"/bff/external-orders")
      json = json_response(conn, 200)

      assert json["orders"]
      assert length(json["orders"]) == 3

      # Get the inserted_at time for each order
      first_order_time = Enum.at(json["orders"], 0)["inserted_at"]
      second_order_time = Enum.at(json["orders"], 1)["inserted_at"]
      third_order_time = Enum.at(json["orders"], 2)["inserted_at"]

      # Verify orders are returned in newest to oldest order by inserted_at
      {:ok, first_datetime, _} = DateTime.from_iso8601(first_order_time)
      {:ok, second_datetime, _} = DateTime.from_iso8601(second_order_time)
      {:ok, third_datetime, _} = DateTime.from_iso8601(third_order_time)

      assert DateTime.compare(first_datetime, second_datetime) == :gt
      assert DateTime.compare(second_datetime, third_datetime) == :gt
    end
  end

  describe "import_shopify_order/2" do
    alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

    @shopify_order_id "gid://shopify/Order/1234567890"

    @sample_shopify_response %{
      "data" => %{
        "order" => %{
          "id" => @shopify_order_id,
          "name" => "#TEST999",
          "app" => %{"name" => "Online Store"},
          "createdAt" => "2024-01-01T10:00:00Z",
          "displayFinancialStatus" => "PAID",
          "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "50.0"}},
          "subtotalPriceSet" => %{"shopMoney" => %{"amount" => "50.0"}},
          "totalDiscountsSet" => %{"shopMoney" => %{"amount" => "0.0"}},
          "totalShippingPriceSet" => %{"shopMoney" => %{"amount" => "0.0"}},
          "customer" => nil,
          "shippingAddress" => nil,
          "retailLocation" => nil,
          "fulfillmentOrders" => %{
            "nodes" => [
              %{
                "assignedLocation" => %{
                  "location" => %{"id" => "gid://shopify/Location/FAKE-LOC-ID"}
                },
                "deliveryMethod" => %{"methodType" => "SHIPPING"},
                "status" => "CLOSED",
                "lineItems" => %{
                  "nodes" => [
                    %{
                      "lineItem" => %{
                        "currentQuantity" => 1,
                        "discountedUnitPriceAfterAllDiscountsSet" => %{
                          "shopMoney" => %{"amount" => "50.0"}
                        }
                      },
                      "sku" => "TESTSKU",
                      "productTitle" => "Test Product",
                      "variantTitle" => "Test Variant",
                      "variant" => %{"id" => "gid://shopify/ProductVariant/FAKE-VAR-ID"}
                    }
                  ]
                }
              }
            ]
          }
        }
      }
    }

    test "successfully imports a Shopify order", %{
      conn: conn,
      organization: org,
      location: loc
    } do
      insert(:product_variant_mapping,
        organization: org,
        source: "shopify",
        external_id: "gid://shopify/ProductVariant/FAKE-VAR-ID",
        product_variant:
          insert(:product_variant, organization: org, product: build(:product, organization: org))
      )

      insert(:location_mapping,
        organization: org,
        source: "shopify",
        external_id: "gid://shopify/Location/FAKE-LOC-ID",
        location: loc
      )

      insert(:shopify_credential, organization: org)

      expected_shopify_id = @shopify_order_id
      expect(ShopifyMock, :client, fn _shop, _credential -> :mock_client end)

      expect(ShopifyMock, :get_order!, fn :mock_client, ^expected_shopify_id, _selection, _opts ->
        {:ok, @sample_shopify_response}
      end)

      params = %{"shopify_order_id" => @shopify_order_id}

      conn = post(conn, ~p"/bff/import_shopify_order", params)

      assert response = json_response(conn, 200)
      assert response["order"]["external_id"] == @shopify_order_id
    end

    test "returns error when Shopify order import fails", %{conn: conn, organization: org} do
      insert(:shopify_credential, organization: org)

      expected_shopify_id = @shopify_order_id

      expect(ShopifyMock, :client, fn _shop, _credential -> :mock_client end)

      expect(ShopifyMock, :get_order!, fn :mock_client, ^expected_shopify_id, _selection, _opts ->
        {:error, :shopify_api_error}
      end)

      params = %{"shopify_order_id" => @shopify_order_id}

      conn = post(conn, ~p"/bff/import_shopify_order", params)

      assert json_response(conn, 400) == %{
               "errors" => %{"detail" => "Bad Request"}
             }
    end

    test "returns error when Shopify order ID is missing", %{conn: conn} do
      params = %{}

      conn = post(conn, ~p"/bff/import_shopify_order", params)

      assert json_response(conn, 400) == %{
               "error" => %{"detail" => "missing required parameter: shopify_order_id"}
             }
    end

    test "returns existing internal order if already imported", %{
      conn: conn,
      organization: org
    } do
      existing_order = insert(:order, organization: org, external_id: @shopify_order_id)

      expect(ShopifyMock, :client, 0, fn _, _ ->
        raise "ShopifyMock.client should not be called"
      end)

      expect(ShopifyMock, :get_order!, 0, fn _, _, _, _ ->
        raise "ShopifyMock.get_order! should not be called"
      end)

      params = %{"shopify_order_id" => @shopify_order_id}

      conn = post(conn, ~p"/bff/import_shopify_order", params)

      assert response = json_response(conn, 200)
      assert response["order"]["id"] == existing_order.id
      assert response["order"]["external_id"] == @shopify_order_id
    end

    test "returns 422 error when location mapping is not found", %{
      conn: conn,
      organization: org
    } do
      insert(:shopify_credential, organization: org)

      expected_shopify_id = @shopify_order_id

      missing_location_id =
        get_in(@sample_shopify_response, [
          "data",
          "order",
          "fulfillmentOrders",
          "nodes",
          Access.at(0),
          "assignedLocation",
          "location",
          "id"
        ])

      expect(ShopifyMock, :client, fn _shop, _credential -> :mock_client end)

      expect(ShopifyMock, :get_order!, fn :mock_client, ^expected_shopify_id, _selection, _opts ->
        {:ok, @sample_shopify_response}
      end)

      params = %{"shopify_order_id" => @shopify_order_id}

      conn = post(conn, ~p"/bff/import_shopify_order", params)

      assert response = json_response(conn, 422)

      assert response == %{
               "error" => %{
                 "detail" =>
                   "location mapping not found for shopify external id #{missing_location_id}"
               }
             }
    end
  end
end
