defmodule RmsWeb.CancelationIntegrationsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  alias Rms.Integrations.Orders.CancelationEndpoint
  alias Rms.Repo

  setup do
    user = insert(:user)
    conn = authenticate_conn(build_conn(), user)
    {:ok, conn: conn, user: user}
  end

  describe "list/2" do
    test "lists all cancelation endpoints for the organization", %{conn: conn, user: user} do
      insert(:cancelation_endpoint, organization: user.organization)

      response =
        conn
        |> get(~p"/api/integrations/cancelation")
        |> json_response(200)

      assert length(response["data"]) == 1
    end
  end

  describe "create/2" do
    test "creates a cancelation endpoint", %{conn: conn, user: user} do
      attrs = %{
        endpoint: "https://example.com",
        headers: [["Content-Type", "application/json"]],
        active: true
      }

      response =
        conn
        |> post(~p"/api/integrations/cancelation", %{cancelation_endpoint: attrs})
        |> json_response(201)

      assert response["data"]["endpoint"] == "https://example.com"
      assert Repo.get_by(CancelationEndpoint, organization_id: user.organization_id)
    end
  end

  describe "show/2" do
    test "shows a specific cancelation endpoint", %{conn: conn, user: user} do
      cancelation_endpoint = insert(:cancelation_endpoint, organization: user.organization)

      response =
        conn
        |> get(~p"/api/integrations/cancelation/#{cancelation_endpoint.id}")
        |> json_response(200)

      assert response["data"]["id"] == cancelation_endpoint.id
    end

    test "returns 404 if cancelation endpoint not found", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/integrations/cancelation/999")
        |> json_response(404)
      end
    end
  end

  describe "update/2" do
    test "updates a specific cancelation endpoint", %{conn: conn, user: user} do
      cancelation_endpoint = insert(:cancelation_endpoint, organization: user.organization)

      update_attrs = %{endpoint: "https://updated.com"}

      response =
        conn
        |> put(~p"/api/integrations/cancelation/#{cancelation_endpoint.id}", %{
          cancelation_endpoint: update_attrs
        })
        |> json_response(200)

      assert response["data"]["endpoint"] == "https://updated.com"

      cancelation_endpoint = Repo.reload!(cancelation_endpoint)
      assert cancelation_endpoint.endpoint == "https://updated.com"
    end
  end

  describe "delete/2" do
    test "deletes a specific cancelation endpoint", %{conn: conn, user: user} do
      cancelation_endpoint = insert(:cancelation_endpoint, organization: user.organization)

      response =
        conn
        |> delete(~p"/api/integrations/cancelation/#{cancelation_endpoint.id}")
        |> response(204)

      assert response == ""

      refute Repo.get_by(CancelationEndpoint,
               organization_id: user.organization_id,
               id: cancelation_endpoint.id
             )
    end
  end
end
