defmodule RmsWeb.CartControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  alias Rms.Integrations.VTEX

  setup :verify_on_exit!

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)

    conn = authenticate_conn(conn, user)

    {:ok,
     conn: put_req_header(conn, "accept", "application/json"),
     user: user,
     organization: organization}
  end

  describe "update/2" do
    test "updates a carts saved", %{conn: conn, organization: org} do
      cart = insert(:cart, saved: false, organization: org)

      conn = put(conn, ~p"/api/carts/#{cart.id}", %{saved: true})
      assert json_response(conn, 200)["saved"] == true
    end

    test "updates a carts state_of_sale", %{conn: conn, organization: org} do
      cart = insert(:cart, state_of_sale: "seller", organization: org)

      conn = put(conn, ~p"/api/carts/#{cart.id}", %{state_of_sale: "cashier"})
      assert json_response(conn, 200)["state_of_sale"] == "cashier"
    end

    test "updates a carts location", %{conn: conn, organization: org} do
      loc = insert(:location, organization: org)
      other_loc = insert(:location, organization: org)
      cart = insert(:cart, location: loc, organization: org)

      put(conn, ~p"/api/carts/#{cart.id}", %{location_id: other_loc.id})
      assert Rms.Commerce.Carts.get_cart!(org.id, cart.id).location_id == other_loc.id
    end
  end

  describe "index/2" do
    test "lists all carts", %{conn: conn, organization: org} do
      _p1 = insert(:cart, organization: org)
      _p2 = insert(:cart, organization: org)

      conn = get(conn, ~p"/api/carts")
      assert json_response(conn, 200)["carts"] |> length() == 2
    end

    test "renders cart_addons when there are cart addons associated with the cart", %{
      conn: conn,
      organization: org
    } do
      cart = insert(:cart, organization: org)
      addon = insert(:addon, organization: org)
      insert(:cart_addon, cart: cart, addon: addon, organization: org)

      conn = get(conn, ~p"/api/carts")
      response = json_response(conn, 200)

      assert [rendered_cart] = response["carts"]
      assert [rendered_cart_addon] = rendered_cart["addons"]
      assert rendered_cart_addon["id"] == addon.id
      assert rendered_cart_addon["name"] == addon.name
      assert rendered_cart_addon["type"] == addon.type
      assert rendered_cart_addon["price"] == to_string(addon.price)
      assert rendered_cart_addon["description"] == addon.description
    end

    test "does not list other organization's carts", %{conn: conn} do
      other_org = insert(:organization)
      _cart = insert(:cart, organization: other_org)

      conn = get(conn, ~p"/api/carts")
      assert json_response(conn, 200)["carts"] |> length() == 0
    end

    test "returns a `page` entry with pagination metadata", %{
      conn: conn,
      organization: org
    } do
      insert_list(10, :cart, organization: org)

      first_page = get(conn, ~p"/api/carts?limit=5")

      response_data = json_response(first_page, 200)
      assert length(response_data["carts"]) == 5

      assert %{
               "before" => nil,
               "after" => cursor_after,
               "limit" => 5
             } = response_data["page"]

      assert cursor_after

      second_page = get(conn, ~p"/api/carts?limit=5&after=#{cursor_after}")
      second_page_data = json_response(second_page, 200)
      assert response_data["carts"] != second_page_data["carts"]
    end

    test "allows searching by saved", %{conn: conn, organization: organization} do
      cart = insert(:cart, saved: true, organization: organization)
      insert(:cart, organization: organization)

      conn = get(conn, ~p"/api/carts?saved=true")

      assert [found_cart] = json_response(conn, 200)["carts"]
      assert found_cart["id"] == cart.id
    end

    test "allows searching by state_of_sale", %{conn: conn, organization: organization} do
      insert(:cart, state_of_sale: "seller", organization: organization)
      cart2 = insert(:cart, state_of_sale: "cashier", organization: organization)
      cart3 = insert(:cart, state_of_sale: "cashier", organization: organization)
      insert(:cart, organization: organization)

      conn = get(conn, ~p"/api/carts?state_of_sale=cashier")

      assert found_carts = json_response(conn, 200)["carts"]
      assert Enum.all?(found_carts, fn found_cart -> found_cart["id"] in [cart2.id, cart3.id] end)
    end

    test "allows searching by staff", %{conn: conn, organization: organization} do
      staff = insert(:staff, organization: organization)
      cart = insert(:cart, staff: staff, organization: organization)
      insert(:cart, organization: organization)

      conn = get(conn, ~p"/api/carts?staff_id=#{staff.id}")

      assert [found_cart] = json_response(conn, 200)["carts"]
      assert found_cart["id"] == cart.id
    end

    test "allows searching by customer", %{conn: conn, organization: organization} do
      customer = insert(:customer, organization: organization)
      cart = insert(:cart, customer: customer, organization: organization)
      insert(:cart, organization: organization)

      conn = get(conn, ~p"/api/carts?customer_id=#{customer.id}")

      assert [found_cart] = json_response(conn, 200)["carts"]
      assert found_cart["id"] == cart.id
    end

    test "allows searching by location", %{conn: conn, organization: organization} do
      location = insert(:location, organization: organization)
      cart = insert(:cart, location: location, organization: organization)
      insert(:cart, organization: organization)

      conn = get(conn, ~p"/api/carts?location_id=#{location.id}")

      assert [found_cart] = json_response(conn, 200)["carts"]
      assert found_cart["id"] == cart.id
    end
  end

  describe "simulate/2" do
    @vtex_cart_simulation %{
      "items" => [
        %{
          "id" => "2",
          "requestIndex" => 0,
          "quantity" => 40,
          "seller" => "1",
          "sellerChain" => [
            "1"
          ],
          "tax" => 0,
          "priceValidUntil" => "2025-05-31T19:15:40Z",
          "price" => 1500,
          "listPrice" => 1500,
          "rewardValue" => 0,
          "sellingPrice" => 1000,
          "offerings" => [],
          "priceTags" => [],
          "measurementUnit" => "un",
          "unitMultiplier" => 1.0000,
          "parentItemIndex" => nil,
          "parentAssemblyBinding" => nil,
          "availability" => "available",
          "priceDefinition" => %{
            "calculatedSellingPrice" => 1000,
            "total" => 40_000,
            "sellingPrices" => [
              %{
                "value" => 1000,
                "quantity" => 1
              }
            ],
            "reason" => nil
          },
          "priceTable" => nil
        },
        %{
          "id" => "3",
          "requestIndex" => 1,
          "quantity" => 10,
          "seller" => "1",
          "sellerChain" => [
            "1"
          ],
          "tax" => 0,
          "priceValidUntil" => "2025-05-31T19:15:40Z",
          "price" => 2500,
          "listPrice" => 2500,
          "rewardValue" => 0,
          "sellingPrice" => 2500,
          "offerings" => [],
          "priceTags" => [],
          "measurementUnit" => "un",
          "unitMultiplier" => 1.0000,
          "parentItemIndex" => nil,
          "parentAssemblyBinding" => nil,
          "availability" => "available",
          "priceDefinition" => %{
            "calculatedSellingPrice" => 2500,
            "total" => 25_000,
            "sellingPrices" => [
              %{
                "value" => 2500,
                "quantity" => 10
              }
            ],
            "reason" => nil
          },
          "priceTable" => nil
        }
      ],
      "marketingData" => %{
        "utmSource" => nil,
        "utmMedium" => nil,
        "utmCampaign" => nil,
        "utmipage" => nil,
        "utmiPart" => nil,
        "utmiCampaign" => nil,
        "coupon" => "iglucoupon",
        "marketingTags" => []
      },
      "postalCode" => "01402-000",
      "country" => "BRA",
      "logisticsInfo" => [
        %{
          "itemIndex" => 0,
          "addressId" => nil,
          "selectedSla" => nil,
          "selectedDeliveryChannel" => nil,
          "quantity" => 1,
          "shipsTo" => [
            "BRA"
          ],
          "slas" => [
            %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 890,
              "listPrice" => 890,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            },
            %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "pickup-in-point",
              "name" => "Retirada em Loja (1)",
              "deliveryIds" => [
                %{
                  "courierId" => "2",
                  "warehouseId" => "1e10a41",
                  "dockId" => "1dd5911",
                  "courierName" => "Retirada em Loja",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "2",
                  "warehouseId" => "1c3cbfe",
                  "dockId" => "18c1bdc",
                  "courierName" => "Retirada em Loja",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 0,
              "listPrice" => 0,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => true,
                "friendlyName" => "Baw Birth Store",
                "address" => %{
                  "addressType" => "pickup",
                  "receiverName" => nil,
                  "addressId" => "1",
                  "isDisposable" => true,
                  "postalCode" => "01131-000",
                  "city" => "São Paulo",
                  "state" => "SP",
                  "country" => "BRA",
                  "street" => "Rua dos Italianos",
                  "number" => "998",
                  "neighborhood" => "Bom Retiro",
                  "complement" => "",
                  "reference" => nil,
                  "geoCoordinates" => [
                    -46.64538,
                    -23.52124
                  ]
                },
                "additionalInfo" => "",
                "dockId" => "1dd5911"
              },
              "pickupPointId" => "1_1",
              "pickupDistance" => 6.259303569793701,
              "polygonName" => "",
              "transitTime" => "0bd"
            }
          ],
          "deliveryChannels" => [
            %{
              "id" => "pickup-in-point"
            },
            %{
              "id" => "delivery"
            }
          ]
        },
        %{
          "itemIndex" => 1,
          "addressId" => nil,
          "selectedSla" => nil,
          "selectedDeliveryChannel" => nil,
          "quantity" => 10,
          "shipsTo" => [
            "BRA"
          ],
          "slas" => [
            %{
              "id" => "Normal",
              "deliveryChannel" => "delivery",
              "name" => "Normal",
              "deliveryIds" => [
                %{
                  "courierId" => "1",
                  "warehouseId" => "16f4675",
                  "dockId" => "16c7f6f",
                  "courierName" => "Transportadora",
                  "quantity" => 5,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "1",
                  "warehouseId" => "1_1",
                  "dockId" => "1",
                  "courierName" => "Transportadora",
                  "quantity" => 5,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 890,
              "listPrice" => 890,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => 0.0,
              "polygonName" => "",
              "transitTime" => "3bd"
            },
            %{
              "id" => "Retirada em Loja (1)",
              "deliveryChannel" => "pickup-in-point",
              "name" => "Retirada em Loja (1)",
              "deliveryIds" => [
                %{
                  "courierId" => "2",
                  "warehouseId" => "1e10a41",
                  "dockId" => "1dd5911",
                  "courierName" => "Retirada em Loja",
                  "quantity" => 30,
                  "kitItemDetails" => []
                },
                %{
                  "courierId" => "2",
                  "warehouseId" => "1c3cbfe",
                  "dockId" => "18c1bdc",
                  "courierName" => "Retirada em Loja",
                  "quantity" => 10,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 0,
              "listPrice" => 0,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => true,
                "friendlyName" => "Baw Birth Store",
                "address" => %{
                  "addressType" => "pickup",
                  "receiverName" => nil,
                  "addressId" => "1",
                  "isDisposable" => true,
                  "postalCode" => "01131-000",
                  "city" => "São Paulo",
                  "state" => "SP",
                  "country" => "BRA",
                  "street" => "Rua dos Italianos",
                  "number" => "998",
                  "neighborhood" => "Bom Retiro",
                  "complement" => "",
                  "reference" => nil,
                  "geoCoordinates" => [
                    -46.64538,
                    -23.52124
                  ]
                },
                "additionalInfo" => "",
                "dockId" => "1dd5911"
              },
              "pickupPointId" => "1_1",
              "pickupDistance" => 6.259303569793701,
              "polygonName" => "",
              "transitTime" => "0bd"
            }
          ],
          "deliveryChannels" => [
            %{
              "id" => "pickup-in-point"
            },
            %{
              "id" => "delivery"
            }
          ]
        }
      ],
      "messages" => [
        %{
          "code" => "itemQuantityNotAvailable",
          "text" =>
            "A quantidade desejada para o item .SHORTS KNIT BAW X RICK AND MORTY SHORTS KNIT BAW X RICK AND MORTY PRETO P não está disponível",
          "status" => "warning",
          "fields" => %{
            "ean" => "7909944338336"
          }
        }
      ],
      "pickupPoints" => [
        %{
          "friendlyName" => "Baw Birth Store",
          "address" => %{
            "addressType" => "pickup",
            "receiverName" => nil,
            "addressId" => "1",
            "isDisposable" => true,
            "postalCode" => "01131-000",
            "city" => "São Paulo",
            "state" => "SP",
            "country" => "BRA",
            "street" => "Rua dos Italianos",
            "number" => "998",
            "neighborhood" => "Bom Retiro",
            "complement" => "",
            "reference" => nil,
            "geoCoordinates" => [
              -46.64538,
              -23.52124
            ]
          },
          "additionalInfo" => "",
          "id" => "1_1",
          "businessHours" => []
        }
      ]
    }
    @vtex_cart_simulation2 %{
      "allowMultipleDeliveries" => true,
      "country" => "BRA",
      "itemMetadata" => nil,
      "items" => [
        %{
          "availability" => "available",
          "catalogProvider" =>
            "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/3",
          "id" => "3",
          "listPrice" => 22_500,
          "measurementUnit" => "un",
          "offerings" => [],
          "parentAssemblyBinding" => nil,
          "parentItemIndex" => nil,
          "price" => 22_500,
          "priceDefinition" => %{
            "calculatedSellingPrice" => 22_500,
            "reason" => nil,
            "sellingPrices" => [%{"quantity" => 6, "value" => 22_500}],
            "total" => 135_000
          },
          "priceTable" => nil,
          "priceTags" => [],
          "priceValidUntil" => "2025-06-04T23:57:50Z",
          "quantity" => 6,
          "requestIndex" => 0,
          "rewardValue" => 0,
          "seller" => "1",
          "sellerChain" => ["1"],
          "sellingPrice" => 22_500,
          "tax" => 0,
          "unitMultiplier" => 1.0
        },
        %{
          "availability" => "available",
          "catalogProvider" =>
            "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
          "id" => "1",
          "listPrice" => 30_500,
          "measurementUnit" => "un",
          "offerings" => [],
          "parentAssemblyBinding" => nil,
          "parentItemIndex" => nil,
          "price" => 30_500,
          "priceDefinition" => %{
            "calculatedSellingPrice" => 30_500,
            "reason" => nil,
            "sellingPrices" => [%{"quantity" => 2, "value" => 30_500}],
            "total" => 61_000
          },
          "priceTable" => nil,
          "priceTags" => [],
          "priceValidUntil" => "2025-06-04T23:57:50Z",
          "quantity" => 2,
          "requestIndex" => 1,
          "rewardValue" => 0,
          "seller" => "1",
          "sellerChain" => ["1"],
          "sellingPrice" => 30_500,
          "tax" => 0,
          "unitMultiplier" => 1.0
        }
      ],
      "logisticsInfo" => [
        %{
          "addressId" => nil,
          "deliveryChannels" => [
            %{"id" => "pickup-in-point", "stockBalance" => 11}
          ],
          "itemIndex" => 0,
          "quantity" => 6,
          "selectedDeliveryChannel" => nil,
          "selectedSla" => nil,
          "shipsTo" => ["BRA"],
          "slas" => [
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "pickup-in-point",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "2",
                  "courierName" => "Retirada em Loja",
                  "dockId" => "18c1bdc",
                  "kitItemDetails" => [],
                  "quantity" => 6,
                  "warehouseId" => "1c3cbfe"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Retirada em Loja (1)",
              "listPrice" => 0,
              "lockTTL" => nil,
              "name" => "Retirada em Loja (1)",
              "pickupDistance" => 3.**************,
              "pickupPointId" => "1_1",
              "pickupStoreInfo" => %{
                "additionalInfo" => "",
                "address" => %{
                  "addressId" => "1",
                  "addressType" => "pickup",
                  "city" => "São Paulo",
                  "complement" => "",
                  "country" => "BRA",
                  "geoCoordinates" => [-46.64538, -23.52124],
                  "isDisposable" => true,
                  "neighborhood" => "Bom Retiro",
                  "number" => "998",
                  "postalCode" => "01131-000",
                  "receiverName" => nil,
                  "reference" => nil,
                  "state" => "SP",
                  "street" => "Rua dos Italianos"
                },
                "dockId" => "18c1bdc",
                "friendlyName" => "Baw Birth Store",
                "isPickupStore" => true
              },
              "polygonName" => "",
              "price" => 0,
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "0bd"
            },
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "pickup-in-point",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "2",
                  "courierName" => "Retirada em Loja",
                  "dockId" => "18c1bdc",
                  "kitItemDetails" => [],
                  "quantity" => 6,
                  "warehouseId" => "1c3cbfe"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Retirada em Loja (2)",
              "listPrice" => 0,
              "lockTTL" => nil,
              "name" => "Retirada em Loja (2)",
              "pickupDistance" => 9.*************,
              "pickupPointId" => "1_2",
              "pickupStoreInfo" => %{
                "additionalInfo" => "",
                "address" => %{
                  "addressId" => "2",
                  "addressType" => "pickup",
                  "city" => "São Paulo",
                  "complement" => "",
                  "country" => "BRA",
                  "geoCoordinates" => [-46.69802, -23.62317],
                  "isDisposable" => true,
                  "neighborhood" => "Jardim das Acacias",
                  "number" => "1089",
                  "postalCode" => "04707-000",
                  "receiverName" => nil,
                  "reference" => nil,
                  "state" => "SP",
                  "street" => "Avenida Roque Petroni Júnior"
                },
                "dockId" => "18c1bdc",
                "friendlyName" => "Baw® Morumbi",
                "isPickupStore" => true
              },
              "polygonName" => "",
              "price" => 0,
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "0bd"
            }
          ],
          "stockBalance" => 11
        },
        %{
          "addressId" => nil,
          "deliveryChannels" => [
            %{"id" => "delivery", "stockBalance" => 55},
            %{"id" => "pickup-in-point", "stockBalance" => 13}
          ],
          "itemIndex" => 1,
          "quantity" => 2,
          "selectedDeliveryChannel" => nil,
          "selectedSla" => nil,
          "shipsTo" => ["BRA"],
          "slas" => [
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "delivery",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "1ac4ae1",
                  "courierName" => "Entrega mais rápida",
                  "dockId" => "16c7f6f",
                  "kitItemDetails" => [],
                  "quantity" => 2,
                  "warehouseId" => "1_1"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Express",
              "listPrice" => 2_390,
              "lockTTL" => nil,
              "name" => "Express",
              "pickupDistance" => 0.0,
              "pickupPointId" => nil,
              "pickupStoreInfo" => %{
                "additionalInfo" => nil,
                "address" => nil,
                "dockId" => nil,
                "friendlyName" => nil,
                "isPickupStore" => false
              },
              "polygonName" => "",
              "price" => 2_390,
              "shippingEstimate" => "3bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "3bd"
            },
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "delivery",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "1",
                  "courierName" => "Entrega normal",
                  "dockId" => "1",
                  "kitItemDetails" => [],
                  "quantity" => 2,
                  "warehouseId" => "1_1"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Normal",
              "listPrice" => 2_390,
              "lockTTL" => nil,
              "name" => "Normal",
              "pickupDistance" => 0.0,
              "pickupPointId" => nil,
              "pickupStoreInfo" => %{
                "additionalInfo" => nil,
                "address" => nil,
                "dockId" => nil,
                "friendlyName" => nil,
                "isPickupStore" => false
              },
              "polygonName" => "",
              "price" => 2_390,
              "shippingEstimate" => "6bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "3bd"
            },
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "pickup-in-point",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "2",
                  "courierName" => "Retirada em Loja",
                  "dockId" => "18c1bdc",
                  "kitItemDetails" => [],
                  "quantity" => 2,
                  "warehouseId" => "1c3cbfe"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Retirada em Loja (1)",
              "listPrice" => 0,
              "lockTTL" => nil,
              "name" => "Retirada em Loja (1)",
              "pickupDistance" => 3.**************,
              "pickupPointId" => "1_1",
              "pickupStoreInfo" => %{
                "additionalInfo" => "",
                "address" => %{
                  "addressId" => "1",
                  "addressType" => "pickup",
                  "city" => "São Paulo",
                  "complement" => "",
                  "country" => "BRA",
                  "geoCoordinates" => [-46.64538, -23.52124],
                  "isDisposable" => true,
                  "neighborhood" => "Bom Retiro",
                  "number" => "998",
                  "postalCode" => "01131-000",
                  "receiverName" => nil,
                  "reference" => nil,
                  "state" => "SP",
                  "street" => "Rua dos Italianos"
                },
                "dockId" => "18c1bdc",
                "friendlyName" => "Baw Birth Store",
                "isPickupStore" => true
              },
              "polygonName" => "",
              "price" => 0,
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "0bd"
            },
            %{
              "availableDeliveryWindows" => [],
              "deliveryChannel" => "pickup-in-point",
              "deliveryIds" => [
                %{
                  "accountCarrierName" => "bawclothinghomolog",
                  "courierId" => "2",
                  "courierName" => "Retirada em Loja",
                  "dockId" => "18c1bdc",
                  "kitItemDetails" => [],
                  "quantity" => 2,
                  "warehouseId" => "1c3cbfe"
                }
              ],
              "deliveryWindow" => nil,
              "id" => "Retirada em Loja (2)",
              "listPrice" => 0,
              "lockTTL" => nil,
              "name" => "Retirada em Loja (2)",
              "pickupDistance" => 9.*************,
              "pickupPointId" => "1_2",
              "pickupStoreInfo" => %{
                "additionalInfo" => "",
                "address" => %{
                  "addressId" => "2",
                  "addressType" => "pickup",
                  "city" => "São Paulo",
                  "complement" => "",
                  "country" => "BRA",
                  "geoCoordinates" => [-46.69802, -23.62317],
                  "isDisposable" => true,
                  "neighborhood" => "Jardim das Acacias",
                  "number" => "1089",
                  "postalCode" => "04707-000",
                  "receiverName" => nil,
                  "reference" => nil,
                  "state" => "SP",
                  "street" => "Avenida Roque Petroni Júnior"
                },
                "dockId" => "18c1bdc",
                "friendlyName" => "Baw® Morumbi",
                "isPickupStore" => true
              },
              "polygonName" => "",
              "price" => 0,
              "shippingEstimate" => "0bd",
              "shippingEstimateDate" => nil,
              "tax" => 0,
              "transitTime" => "0bd"
            }
          ],
          "stockBalance" => 68
        }
      ],
      "marketingData" => nil,
      "messages" => [],
      "paymentData" => %{
        "availableAccounts" => [],
        "availableAssociations" => %{},
        "availableTokens" => [],
        "giftCardMessages" => [],
        "giftCards" => [],
        "installmentOptions" => [
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "creditCardPaymentGroup",
            "paymentName" => "Visa",
            "paymentSystem" => "2",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "creditCardPaymentGroup",
            "paymentName" => "Mastercard",
            "paymentSystem" => "4",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "bankInvoicePaymentGroup",
            "paymentName" => "Boleto Bancário",
            "paymentSystem" => "6",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "creditCardPaymentGroup",
            "paymentName" => "Elo",
            "paymentSystem" => "9",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "payPalPaymentGroup",
            "paymentName" => "PayPal",
            "paymentSystem" => "12",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "promissoryPaymentGroup",
            "paymentName" => "Promissory",
            "paymentSystem" => "17",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "cashPaymentGroup",
            "paymentName" => "Cash",
            "paymentSystem" => "47",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "NubankPaymentGroup",
            "paymentName" => "Nubank",
            "paymentSystem" => "178",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "custom202PaymentGroupPaymentGroup",
            "paymentName" => "Dinheiro",
            "paymentSystem" => "202",
            "value" => 196_000
          },
          %{
            "bin" => nil,
            "installments" => [
              %{
                "count" => 1,
                "hasInterestRate" => false,
                "interestRate" => 0,
                "sellerMerchantInstallments" => [
                  %{
                    "count" => 1,
                    "hasInterestRate" => false,
                    "id" => "BAWCLOTHINGHOMOLOG",
                    "interestRate" => 0,
                    "total" => 196_000,
                    "value" => 196_000
                  }
                ],
                "total" => 196_000,
                "value" => 196_000
              }
            ],
            "paymentGroupName" => "WH Google PayPaymentGroup",
            "paymentName" => "WH Google Pay",
            "paymentSystem" => "900",
            "value" => 196_000
          }
        ],
        "paymentSystems" => [
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "bankInvoicePaymentGroup",
            "id" => 6,
            "isCustom" => false,
            "name" => "Boleto Bancário",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "6",
            "template" => "bankInvoicePaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:51:37.7928949Z",
            "groupName" => "promissoryPaymentGroup",
            "id" => 17,
            "isCustom" => false,
            "name" => "Promissory",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "17",
            "template" => "promissoryPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "payPalPaymentGroup",
            "id" => 12,
            "isCustom" => false,
            "name" => "PayPal",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "12",
            "template" => "payPalPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "NubankPaymentGroup",
            "id" => 178,
            "isCustom" => false,
            "name" => "Nubank",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "178",
            "template" => "NubankPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => true,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "creditCardPaymentGroup",
            "id" => 2,
            "isCustom" => false,
            "name" => "Visa",
            "requiresAuthentication" => false,
            "requiresDocument" => true,
            "stringId" => "2",
            "template" => "creditCardPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => true,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "creditCardPaymentGroup",
            "id" => 9,
            "isCustom" => false,
            "name" => "Elo",
            "requiresAuthentication" => false,
            "requiresDocument" => true,
            "stringId" => "9",
            "template" => "creditCardPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => true,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "creditCardPaymentGroup",
            "id" => 4,
            "isCustom" => false,
            "name" => "Mastercard",
            "requiresAuthentication" => false,
            "requiresDocument" => true,
            "stringId" => "4",
            "template" => "creditCardPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:51:37.7928949Z",
            "groupName" => "cashPaymentGroup",
            "id" => 47,
            "isCustom" => false,
            "name" => "Cash",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "47",
            "template" => "cashPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => "",
            "displayDocument" => false,
            "dueDate" => "2024-06-14T23:50:37.7928949Z",
            "groupName" => "custom202PaymentGroupPaymentGroup",
            "id" => 202,
            "isCustom" => true,
            "name" => "Dinheiro",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "202",
            "template" => "custom202PaymentGroupPaymentGroup-template",
            "validator" => nil
          },
          %{
            "availablePayments" => nil,
            "description" => nil,
            "displayDocument" => false,
            "dueDate" => "2024-06-11T23:50:37.7928949Z",
            "groupName" => "WH Google PayPaymentGroup",
            "id" => 900,
            "isCustom" => false,
            "name" => "WH Google Pay",
            "requiresAuthentication" => false,
            "requiresDocument" => false,
            "stringId" => "900",
            "template" => "WH Google PayPaymentGroup-template",
            "validator" => nil
          }
        ],
        "payments" => []
      },
      "pickupPoints" => [
        %{
          "additionalInfo" => "",
          "address" => %{
            "addressId" => "1",
            "addressType" => "pickup",
            "city" => "São Paulo",
            "complement" => "",
            "country" => "BRA",
            "geoCoordinates" => [-46.64538, -23.52124],
            "isDisposable" => true,
            "neighborhood" => "Bom Retiro",
            "number" => "998",
            "postalCode" => "01131-000",
            "receiverName" => nil,
            "reference" => nil,
            "state" => "SP",
            "street" => "Rua dos Italianos"
          },
          "businessHours" => [],
          "friendlyName" => "Baw Birth Store",
          "id" => "1_1"
        },
        %{
          "additionalInfo" => "",
          "address" => %{
            "addressId" => "2",
            "addressType" => "pickup",
            "city" => "São Paulo",
            "complement" => "",
            "country" => "BRA",
            "geoCoordinates" => [-46.69802, -23.62317],
            "isDisposable" => true,
            "neighborhood" => "Jardim das Acacias",
            "number" => "1089",
            "postalCode" => "04707-000",
            "receiverName" => nil,
            "reference" => nil,
            "state" => "SP",
            "street" => "Avenida Roque Petroni Júnior"
          },
          "businessHours" => [
            %{
              "ClosingTime" => "20:00:00",
              "DayOfWeek" => 0,
              "OpeningTime" => "13:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 1,
              "OpeningTime" => "10:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 2,
              "OpeningTime" => "10:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 3,
              "OpeningTime" => "10:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 4,
              "OpeningTime" => "10:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 5,
              "OpeningTime" => "10:00:00"
            },
            %{
              "ClosingTime" => "21:00:00",
              "DayOfWeek" => 6,
              "OpeningTime" => "10:00:00"
            }
          ],
          "friendlyName" => "Baw® Morumbi",
          "id" => "1_2"
        }
      ],
      "postalCode" => "********",
      "purchaseConditions" => %{
        "itemPurchaseConditions" => [
          %{
            "id" => "3",
            "listPrice" => 22_500,
            "price" => 22_500,
            "seller" => "1",
            "sellerChain" => ["1"],
            "slas" => [
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "pickup-in-point",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "2",
                    "courierName" => "Retirada em Loja",
                    "dockId" => "18c1bdc",
                    "kitItemDetails" => [],
                    "quantity" => 6,
                    "warehouseId" => "1c3cbfe"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Retirada em Loja (1)",
                "listPrice" => 0,
                "lockTTL" => nil,
                "name" => "Retirada em Loja (1)",
                "pickupDistance" => 3.**************,
                "pickupPointId" => "1_1",
                "pickupStoreInfo" => %{
                  "additionalInfo" => "",
                  "address" => %{
                    "addressId" => "1",
                    "addressType" => "pickup",
                    "city" => "São Paulo",
                    "complement" => "",
                    "country" => "BRA",
                    "geoCoordinates" => [-46.64538, -23.52124],
                    "isDisposable" => true,
                    "neighborhood" => "Bom Retiro",
                    "number" => "998",
                    "postalCode" => "01131-000",
                    "receiverName" => nil,
                    "reference" => nil,
                    "state" => "SP",
                    "street" => "Rua dos Italianos"
                  },
                  "dockId" => "18c1bdc",
                  "friendlyName" => "Baw Birth Store",
                  "isPickupStore" => true
                },
                "polygonName" => "",
                "price" => 0,
                "shippingEstimate" => "0bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "0bd"
              },
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "pickup-in-point",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "2",
                    "courierName" => "Retirada em Loja",
                    "dockId" => "18c1bdc",
                    "kitItemDetails" => [],
                    "quantity" => 6,
                    "warehouseId" => "1c3cbfe"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Retirada em Loja (2)",
                "listPrice" => 0,
                "lockTTL" => nil,
                "name" => "Retirada em Loja (2)",
                "pickupDistance" => 9.*************,
                "pickupPointId" => "1_2",
                "pickupStoreInfo" => %{
                  "additionalInfo" => "",
                  "address" => %{
                    "addressId" => "2",
                    "addressType" => "pickup",
                    "city" => "São Paulo",
                    "complement" => "",
                    "country" => "BRA",
                    "geoCoordinates" => [-46.69802, -23.62317],
                    "isDisposable" => true,
                    "neighborhood" => "Jardim das Acacias",
                    "number" => "1089",
                    "postalCode" => "04707-000",
                    "receiverName" => nil,
                    "reference" => nil,
                    "state" => "SP",
                    "street" => "Avenida Roque Petroni Júnior"
                  },
                  "dockId" => "18c1bdc",
                  "friendlyName" => "Baw® Morumbi",
                  "isPickupStore" => true
                },
                "polygonName" => "",
                "price" => 0,
                "shippingEstimate" => "0bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "0bd"
              }
            ],
            "stockBalance" => 11
          },
          %{
            "id" => "1",
            "listPrice" => 30_500,
            "price" => 30_500,
            "seller" => "1",
            "sellerChain" => ["1"],
            "slas" => [
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "delivery",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "1ac4ae1",
                    "courierName" => "Entrega mais rápida",
                    "dockId" => "16c7f6f",
                    "kitItemDetails" => [],
                    "quantity" => 2,
                    "warehouseId" => "1_1"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Express",
                "listPrice" => 2_390,
                "lockTTL" => nil,
                "name" => "Express",
                "pickupDistance" => 0.0,
                "pickupPointId" => nil,
                "pickupStoreInfo" => %{
                  "additionalInfo" => nil,
                  "address" => nil,
                  "dockId" => nil,
                  "friendlyName" => nil,
                  "isPickupStore" => false
                },
                "polygonName" => "",
                "price" => 2_390,
                "shippingEstimate" => "3bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "3bd"
              },
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "delivery",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "1",
                    "courierName" => "Entrega normal",
                    "dockId" => "1",
                    "kitItemDetails" => [],
                    "quantity" => 2,
                    "warehouseId" => "1_1"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Normal",
                "listPrice" => 2_390,
                "lockTTL" => nil,
                "name" => "Normal",
                "pickupDistance" => 0.0,
                "pickupPointId" => nil,
                "pickupStoreInfo" => %{
                  "additionalInfo" => nil,
                  "address" => nil,
                  "dockId" => nil,
                  "friendlyName" => nil,
                  "isPickupStore" => false
                },
                "polygonName" => "",
                "price" => 2_390,
                "shippingEstimate" => "6bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "3bd"
              },
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "pickup-in-point",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "2",
                    "courierName" => "Retirada em Loja",
                    "dockId" => "18c1bdc",
                    "kitItemDetails" => [],
                    "quantity" => 2,
                    "warehouseId" => "1c3cbfe"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Retirada em Loja (1)",
                "listPrice" => 0,
                "lockTTL" => nil,
                "name" => "Retirada em Loja (1)",
                "pickupDistance" => 3.**************,
                "pickupPointId" => "1_1",
                "pickupStoreInfo" => %{
                  "additionalInfo" => "",
                  "address" => %{
                    "addressId" => "1",
                    "addressType" => "pickup",
                    "city" => "São Paulo",
                    "complement" => "",
                    "country" => "BRA",
                    "geoCoordinates" => [-46.64538, -23.52124],
                    "isDisposable" => true,
                    "neighborhood" => "Bom Retiro",
                    "number" => "998",
                    "postalCode" => "01131-000",
                    "receiverName" => nil,
                    "reference" => nil
                  },
                  "dockId" => "18c1bdc",
                  "friendlyName" => "Baw Birth Store",
                  "isPickupStore" => true
                },
                "polygonName" => "",
                "price" => 0,
                "shippingEstimate" => "0bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "0bd"
              },
              %{
                "availableDeliveryWindows" => [],
                "deliveryChannel" => "pickup-in-point",
                "deliveryIds" => [
                  %{
                    "accountCarrierName" => "bawclothinghomolog",
                    "courierId" => "2",
                    "courierName" => "Retirada em Loja",
                    "dockId" => "18c1bdc",
                    "kitItemDetails" => [],
                    "quantity" => 2,
                    "warehouseId" => "1c3cbfe"
                  }
                ],
                "deliveryWindow" => nil,
                "id" => "Retirada em Loja (2)",
                "listPrice" => 0,
                "lockTTL" => nil,
                "name" => "Retirada em Loja (2)",
                "pickupDistance" => 9.*************,
                "pickupPointId" => "1_2",
                "pickupStoreInfo" => %{
                  "additionalInfo" => "",
                  "address" => %{
                    "addressId" => "2",
                    "addressType" => "pickup",
                    "city" => "São Paulo",
                    "complement" => "",
                    "country" => "BRA",
                    "geoCoordinates" => [-46.69802, -23.62317],
                    "isDisposable" => true,
                    "neighborhood" => "Jardim das Acacias",
                    "number" => "1089",
                    "postalCode" => "04707-000",
                    "receiverName" => nil
                  },
                  "dockId" => "18c1bdc",
                  "friendlyName" => "Baw® Morumbi",
                  "isPickupStore" => true
                },
                "polygonName" => "",
                "price" => 0,
                "shippingEstimate" => "0bd",
                "shippingEstimateDate" => nil,
                "tax" => 0,
                "transitTime" => "0bd"
              }
            ],
            "stockBalance" => 68
          }
        ]
      },
      "ratesAndBenefitsData" => %{
        "rateAndBenefitsIdentifiers" => [],
        "teaser" => []
      },
      "selectableGifts" => [],
      "subscriptionData" => nil,
      "totals" => [
        %{"id" => "Items", "name" => "Total dos Itens", "value" => 196_000}
      ]
    }
    @vtex_cart_simulation_prime %{
      "salesChannel" => "1",
      "value" => 52_765,
      "messages" => [],
      "items" => [
        %{
          "id" => "434288",
          "requestIndex" => 0,
          "name" => "Camiseta Mc Piquet Flame Off White - G",
          "skuName" => "Off White - G",
          "modalType" => nil,
          "parentItemIndex" => nil,
          "parentAssemblyBinding" => nil,
          "assemblies" => [],
          "priceValidUntil" => "2025-10-21T21:34:33Z",
          "tax" => 0,
          "price" => 26_900,
          "listPrice" => 26_900,
          "manualPrice" => nil,
          "manualPriceAppliedBy" => nil,
          "sellingPrice" => 22_865,
          "rewardValue" => 0,
          "isGift" => false,
          "additionalInfo" => %{
            "dimension" => nil,
            "brandName" => "RESERVA",
            "brandId" => "1",
            "offeringInfo" => nil,
            "offeringType" => nil,
            "offeringTypeId" => nil
          },
          "preSaleDate" => nil,
          "productCategoryIds" => "/1/259/260/262/",
          "productCategories" => %{
            "1" => "Reserva",
            "259" => "Bazar",
            "260" => "Masculino",
            "262" => "Camisetas"
          },
          "quantity" => 1,
          "seller" => "1",
          "sellerChain" => [
            "1",
            "lojausereservac79516"
          ],
          "imageUrl" =>
            "https://lojausereserva.vteximg.com.br/arquivos/ids/8456997-55-55/0070605037_01.jpg?v=638399837045330000",
          "detailUrl" => "/camiseta-mc-piquet-flame0070605/p",
          "components" => [],
          "bundleItems" => [],
          "attachments" => [],
          "attachmentOfferings" => [],
          "offerings" => [
            %{
              "type" => "Embalagem pra Presente",
              "id" => "424804",
              "name" => "Embalagem pra Presente",
              "allowGiftMessage" => true,
              "attachmentOfferings" => [
                %{
                  "name" => "message",
                  "required" => false,
                  "schema" => %{
                    "text" => %{
                      "maximumNumberOfCharacters" => 300,
                      "domain" => []
                    }
                  }
                }
              ],
              "price" => 0
            }
          ],
          "priceTags" => [
            %{
              "name" =>
                "discount@price-0d5dd9f8-30a5-4946-837c-6ab5d59e64f0#cc595563-856d-4ecf-9d77-d249b5bb2445",
              "value" => -4035,
              "rawValue" => -40.35,
              "isPercentual" => false,
              "identifier" => "0d5dd9f8-30a5-4946-837c-6ab5d59e64f0",
              "owner" => "lojausereserva",
              "ratesAndBenefitsIdentifier" => %{
                "id" => "0d5dd9f8-30a5-4946-837c-6ab5d59e64f0",
                "name" => "Reserva I Assinatura Prime",
                "featured" => false,
                "description" => "Desconto para clientes com Prime no carrinho",
                "matchedParameters" => %{
                  "Seller@CatalogSystem" => "1,lojausereservaondemand,lojausereservago|inclusive",
                  "category@CatalogSystem" => "176,11,198|exclusive",
                  "productCluster@CatalogSystem" => "698,353,166,684,239|exclusive"
                },
                "additionalInfo" => nil
              }
            },
            %{
              "name" =>
                "discount@shipping-75d28b57-2afa-4962-ba81-2b08c4a67c0e#f2fc99f3-4d22-49b6-87c5-82139d8c2569",
              "value" => -2390,
              "rawValue" => -23.9,
              "isPercentual" => false,
              "identifier" => "75d28b57-2afa-4962-ba81-2b08c4a67c0e",
              "owner" => "lojausereserva",
              "ratesAndBenefitsIdentifier" => %{
                "id" => "75d28b57-2afa-4962-ba81-2b08c4a67c0e",
                "name" => "Reserva I Prime Frete Grátis",
                "featured" => false,
                "description" => "Frete grátis para clientes com Prime no carrinho",
                "matchedParameters" => %{
                  "Seller@CatalogSystem" => "1,lojausereservaondemand,lojausereservago|inclusive",
                  "slaIds" => "padrão"
                },
                "additionalInfo" => nil
              }
            }
          ],
          "availability" => "available",
          "measurementUnit" => "un",
          "unitMultiplier" => 1,
          "manufacturerCode" => nil,
          "priceDefinition" => %{
            "calculatedSellingPrice" => 22_865,
            "total" => 22_865,
            "sellingPrices" => [
              %{
                "value" => 22_865,
                "quantity" => 1
              }
            ],
            "reason" => nil
          },
          "taxCode" => "6109.10.00"
        },
        %{
          "id" => "348009",
          "requestIndex" => 1,
          "productId" => "35126",
          "productRefId" => "prime00",
          "refId" => "prime0001",
          "ean" => "1234567899999",
          "name" => "Assinatura Prime",
          "skuName" => "Prime",
          "modalType" => nil,
          "parentItemIndex" => nil,
          "parentAssemblyBinding" => nil,
          "assemblies" => [],
          "priceValidUntil" => "2025-10-21T21:34:13Z",
          "tax" => 0,
          "price" => 29_900,
          "listPrice" => 29_900,
          "manualPrice" => nil,
          "manualPriceAppliedBy" => nil,
          "sellingPrice" => 29_900,
          "rewardValue" => 0,
          "isGift" => false,
          "additionalInfo" => %{
            "dimension" => nil,
            "brandName" => "RESERVA",
            "brandId" => "1",
            "offeringInfo" => nil,
            "offeringType" => nil,
            "offeringTypeId" => nil
          },
          "preSaleDate" => nil,
          "productCategoryIds" => "/255/",
          "productCategories" => %{
            "255" => "Prime"
          },
          "quantity" => 1,
          "seller" => "1",
          "sellerChain" => [
            "1",
            "lojausereservaassinatura"
          ],
          "imageUrl" =>
            "https://lojausereserva.vteximg.com.br/arquivos/ids/6668063-55-55/card-assinatura.jpg?v=637795134631330000",
          "detailUrl" => "/assinatura-prime-35126/p",
          "components" => [],
          "bundleItems" => [],
          "attachments" => [
            %{
              "name" => "vtex.subscription.prime",
              "content" => %{
                "vtex.subscription.key.frequency" => "1 year"
              }
            }
          ],
          "attachmentOfferings" => [
            %{
              "name" => "vtex.subscription.prime",
              "required" => true,
              "schema" => %{
                "vtex.subscription.key.frequency" => %{
                  "maximumNumberOfCharacters" => 6,
                  "domain" => [
                    "1 year"
                  ]
                }
              }
            }
          ],
          "offerings" => [],
          "priceTags" => [],
          "availability" => "available",
          "measurementUnit" => "un",
          "unitMultiplier" => 1,
          "manufacturerCode" => nil,
          "priceDefinition" => %{
            "calculatedSellingPrice" => 29_900,
            "total" => 29_900,
            "sellingPrices" => [
              %{
                "value" => 29_900,
                "quantity" => 1
              }
            ],
            "reason" => nil
          },
          "taxCode" => ""
        }
      ],
      "selectableGifts" => [],
      "totalizers" => [
        %{
          "id" => "Items",
          "name" => "Total dos Itens",
          "value" => 56_800
        },
        %{
          "id" => "Discounts",
          "name" => "Total dos Descontos",
          "value" => -4035
        },
        %{
          "id" => "Shipping",
          "name" => "Total do Frete",
          "value" => 0,
          "alternativeTotals" => [
            %{
              "id" => "AlternativeShippingTotal",
              "name" => "Alternative Shipping Total",
              "value" => 2390
            },
            %{
              "id" => "AlternativeShippingDiscount",
              "name" => "Alternative Shipping Discount",
              "value" => -2390
            }
          ]
        }
      ],
      "logisticsInfo" => [
        %{
          "itemIndex" => 0,
          "selectedSla" => "Padrão",
          "selectedDeliveryChannel" => "delivery",
          "addressId" => "5187051525714",
          "slas" => [
            %{
              "id" => "Padrão",
              "deliveryChannel" => "delivery",
              "name" => "Padrão",
              "deliveryIds" => [
                %{
                  "courierId" => "15635",
                  "warehouseId" => "1_1",
                  "dockId" => "120792b",
                  "courierName" => "VAI FACIL SAMEDAY",
                  "quantity" => 1,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "2bd",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 0,
              "listPrice" => 2390,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => nil,
              "polygonName" => "",
              "transitTime" => "0bd"
            }
          ],
          "shipsTo" => [
            "BRA"
          ],
          "itemId" => "434288",
          "deliveryChannels" => [
            %{
              "id" => "delivery"
            }
          ]
        },
        %{
          "itemIndex" => 1,
          "selectedSla" => "Assinatura Prime",
          "selectedDeliveryChannel" => "delivery",
          "slas" => [
            %{
              "id" => "Assinatura Prime",
              "deliveryChannel" => "delivery",
              "name" => "Assinatura Prime",
              "deliveryIds" => [
                %{
                  "courierId" => "prime",
                  "warehouseId" => "1785239",
                  "dockId" => "1a63ba9",
                  "courierName" => "Assinatura Prime",
                  "quantity" => 1,
                  "kitItemDetails" => []
                }
              ],
              "shippingEstimate" => "2d",
              "shippingEstimateDate" => nil,
              "lockTTL" => nil,
              "availableDeliveryWindows" => [],
              "deliveryWindow" => nil,
              "price" => 0,
              "listPrice" => 0,
              "tax" => 0,
              "pickupStoreInfo" => %{
                "isPickupStore" => false,
                "friendlyName" => nil,
                "address" => nil,
                "additionalInfo" => nil,
                "dockId" => nil
              },
              "pickupPointId" => nil,
              "pickupDistance" => nil,
              "polygonName" => "",
              "transitTime" => "0d"
            }
          ],
          "shipsTo" => [
            "BRA"
          ],
          "itemId" => "348009",
          "deliveryChannels" => [
            %{
              "id" => "delivery"
            },
            %{
              "id" => "pickup-in-point"
            }
          ]
        }
      ]
    }

    test "returns expected response for valid payload with subscription addon", %{
      conn: conn,
      user: user
    } do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization, name: "Vinicius")
      location = insert(:location, organization: user.organization)

      customer =
        insert(:customer,
          organization: user.organization,
          name: "Gustavo",
          email: "<EMAIL>"
        )

      pv_delivery =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("269.00"),
          price: Decimal.new("228.65"),
          sku: "SKU123"
        )

      addon =
        insert(:addon,
          organization: user.organization,
          name: "Assinatura Prime",
          list_price: Decimal.new("299.00"),
          price: Decimal.new("299.00"),
          type: "subscription"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_delivery,
        source: "vtex",
        external_id: "434288"
      )

      insert(:addon_mapping,
        organization: user.organization,
        addon: addon,
        source: "vtex",
        external_id: "348009"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential,
        organization: user.organization
      )

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, payload ->
        assert [%{"id" => "434288"}, %{"id" => "348009"}] = payload["items"]
        assert "BRA" = payload["country"]
        assert "04088004" = payload["postalCode"]
        assert "<EMAIL>" = payload["clientProfileData"]["email"]
        {:ok, @vtex_cart_simulation_prime}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv_delivery.id,
            "quantity" => 1,
            "fulfillment_type" => "delivery"
          }
        ],
        "addons" => [
          %{
            "addon_id" => addon.id,
            "quantity" => 1,
            "name" => "Assinatura Prime",
            "type" => "subscription",
            "price" => "299.00",
            "list_price" => "299.00",
            "image_url" => "url"
          }
        ],
        "country" => "BRA",
        "postal_code" => "04088004",
        "customer_id" => customer.id,
        "staff_id" => staff.id,
        "location_id" => location.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)

      assert response = json_response(conn, :ok)

      assert [delivery_group] = response["delivery_groups"]

      assert delivery_group["fulfillment_type"] == "delivery"
      assert delivery_group["delivery_time"] == 2

      assert [product_item] = delivery_group["cart_items"]
      assert product_item["product_variant"]["id"] == pv_delivery.id
      assert product_item["list_price"] == "269"
      assert product_item["selling_price"] == "228.65"
      assert product_item["total_price"] == "228.65"
      assert product_item["quantity"] == 1

      assert [addon_item] = response["addons"]
      assert addon_item["id"] == addon.id
      assert addon_item["list_price"] == "299.00"
      assert addon_item["price"] == "299.00"
      assert addon_item["name"] == "Assinatura Prime"
      assert addon_item["type"] == "subscription"
      assert addon_item["description"] == addon.description

      assert response["max_delivery_time"] == 2
      assert response["total_price"] == "228.65"
      assert response["total_items_list_price"] == "269.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "0.00"
      assert response["total_items_selling_price"] == "228.65"
      assert response["staff"]["id"] == staff.id
      assert response["customer"]["id"] == customer.id
    end

    test "returns expected response for valid payload", %{conn: conn, user: user} do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization, name: "Vinicius")
      location = insert(:location, organization: user.organization)

      customer =
        insert(:customer,
          organization: user.organization,
          name: "Gustavo",
          email: "<EMAIL>"
        )

      pv_delivery =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("15.0"),
          price: Decimal.new("15.0"),
          sku: "SKU123"
        )

      pv_local =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("20.0"),
          price: Decimal.new("20.0"),
          sku: "SKU123"
        )

      pv_pickup =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("25.0"),
          price: Decimal.new("25.0"),
          sku: "SKU123"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_delivery,
        source: "vtex",
        external_id: "2"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_pickup,
        source: "vtex",
        external_id: "3"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential,
        organization: user.organization
      )

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, payload ->
        assert [%{"id" => "2"}, %{"id" => "3"}] =
                 payload["items"]

        assert "BRA" = payload["country"]
        assert "01402-000" = payload["postal_code"]

        assert "<EMAIL>" = payload["clientProfileData"]["email"]
        assert "iglucoupon" = payload["marketingData"]["coupon"]
        {:ok, @vtex_cart_simulation}
      end)

      payload =
        %{
          "items" => [
            %{
              "product_variant_id" => pv_delivery.id,
              "quantity" => 40,
              "location_id" => location.id,
              "fulfillment_type" => "delivery",
              "metadata" => nil
            },
            %{
              "product_variant_id" => pv_local.id,
              "quantity" => 5,
              "location_id" => location.id,
              "fulfillment_type" => "in-store",
              "metadata" => nil
            },
            %{
              "product_variant_id" => pv_pickup.id,
              "quantity" => 10,
              "location_id" => location.id,
              "fulfillment_type" => "local-pickup",
              "metadata" => %{
                "id" => "Retirada em Loja (1)",
                "deliveryChannel" => "pickup-in-point",
                "name" => "Retirada em Loja (1)",
                "deliveryIds" => [
                  %{
                    "courierId" => "2",
                    "warehouseId" => "1e10a41",
                    "dockId" => "1dd5911",
                    "courierName" => "Retirada em Loja",
                    "quantity" => 30,
                    "kitItemDetails" => []
                  },
                  %{
                    "courierId" => "2",
                    "warehouseId" => "1c3cbfe",
                    "dockId" => "18c1bdc",
                    "courierName" => "Retirada em Loja",
                    "quantity" => 10,
                    "kitItemDetails" => []
                  }
                ],
                "shippingEstimate" => "0bd",
                "shippingEstimateDate" => nil,
                "lockTTL" => nil,
                "availableDeliveryWindows" => [],
                "deliveryWindow" => nil,
                "price" => 0,
                "listPrice" => 0,
                "tax" => 0,
                "pickupStoreInfo" => %{
                  "isPickupStore" => true,
                  "friendlyName" => "Baw Birth Store",
                  "address" => %{
                    "addressType" => "pickup",
                    "receiverName" => nil,
                    "addressId" => "1",
                    "isDisposable" => true,
                    "postalCode" => "01131-000",
                    "city" => "São Paulo",
                    "state" => "SP",
                    "country" => "BRA",
                    "street" => "Rua dos Italianos",
                    "number" => "998",
                    "neighborhood" => "Bom Retiro",
                    "complement" => "",
                    "reference" => nil,
                    "geoCoordinates" => [
                      -46.64538,
                      -23.52124
                    ]
                  },
                  "additionalInfo" => "",
                  "dockId" => "1dd5911"
                },
                "pickupPointId" => "1_1",
                "pickupDistance" => 6.259303569793701,
                "polygonName" => "",
                "transitTime" => "0bd"
              }
            }
          ],
          "discounts" => [
            %{
              "type" => "coupon",
              "value" => "iglucoupon"
            }
          ],
          "coupon_codes" => ["iglucoupon"],
          "country" => "BRA",
          "postal_code" => "01402-000",
          "customer_id" => customer.id,
          "staff_id" => staff.id
        }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      pv_delivery_id = pv_delivery.id
      pv_pickup_id = pv_pickup.id
      pv_local_id = pv_local.id

      assert [first_delivery_group, second_delivery_group, third_delivery_group] =
               response["delivery_groups"]

      # in-store delivery group
      assert first_delivery_group["fulfillment_type"] == "in-store"
      assert first_delivery_group["pickup_point"] == nil
      assert first_delivery_group["delivery_time"] == 0

      assert %{
               "list_price" => "20.0",
               "selling_price" => "20.0",
               "total_price" => "100.0",
               "quantity" => 5
             } = hd(first_delivery_group["cart_items"])

      assert pv_local_id == hd(first_delivery_group["cart_items"])["product_variant"]["id"]

      # delivery delivery group
      assert second_delivery_group["fulfillment_type"] == "delivery"
      assert second_delivery_group["pickup_point"] == nil
      assert second_delivery_group["delivery_time"] == 3

      assert %{
               "list_price" => "15",
               "selling_price" => "10",
               "total_price" => "400",
               "quantity" => 40
             } = hd(second_delivery_group["cart_items"])

      assert pv_delivery_id == hd(second_delivery_group["cart_items"])["product_variant"]["id"]

      ## local-pickup delivery group
      assert third_delivery_group["fulfillment_type"] == "local-pickup"

      assert %{
               "address" => %{
                 "address_id" => "1",
                 "city" => "São Paulo",
                 "complement" => "",
                 "country" => "BRA",
                 "geo_coordinates" => [-46.64538, -23.52124],
                 "neighborhood" => "Bom Retiro",
                 "number" => "998",
                 "postal_code" => "01131-000",
                 "receiver_name" => nil,
                 "reference" => nil,
                 "state" => "SP",
                 "street" => "Rua dos Italianos"
               },
               "name" => "Baw Birth Store",
               "pickup_distance" => 6.259303569793701
             } = third_delivery_group["pickup_point"]

      assert third_delivery_group["delivery_time"] == 0

      assert %{
               "list_price" => "25",
               "selling_price" => "25",
               "total_price" => "250",
               "quantity" => 10
             } = hd(third_delivery_group["cart_items"])

      assert pv_pickup_id == hd(third_delivery_group["cart_items"])["product_variant"]["id"]

      # top-level assertions
      assert response["total_price"] == "767.80"
      assert response["total_items_list_price"] == "950.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "17.80"
      assert response["total_items_selling_price"] == "750.00"
      assert response["max_delivery_time"] == 3

      assert response["customer"]["id"] == customer.id
      assert response["customer"]["name"] == "Gustavo"
      assert response["staff"]["id"] == staff.id
      assert response["staff"]["name"] == "Vinicius"

      assert response["discounts"] == [
               %{
                 "type" => "coupon",
                 "value" => "iglucoupon",
                 "description" => nil
               }
             ]
    end

    test "returns expected response for payload with only in-store items", %{
      conn: conn,
      user: user
    } do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      pv_instore =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("20.0"),
          price: Decimal.new("20.0"),
          sku: "SKU123"
        )

      pv_instore_no_ecommerce =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("20.0"),
          price: Decimal.new("20.0"),
          sku: "SKU1234"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_instore,
        source: "vtex",
        external_id: "1"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, payload ->
        assert [%{"id" => "1", "quantity" => 2, "seller" => "1"}] = payload["items"]
        assert "BRA" = payload["country"]
        assert "01402-000" = payload["postal_code"]

        cart_simulation =
          @vtex_cart_simulation
          |> Map.put("items", [
            %{
              "availability" => "available",
              "catalogProvider" =>
                "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
              "id" => "1",
              "listPrice" => 30_500,
              "measurementUnit" => "un",
              "offerings" => [],
              "parentAssemblyBinding" => nil,
              "parentItemIndex" => nil,
              "price" => 30_500,
              "priceDefinition" => %{
                "calculatedSellingPrice" => 15_250,
                "reason" => nil,
                "sellingPrices" => [%{"quantity" => 2, "value" => 15_250}],
                "total" => 30_500
              },
              "priceTable" => nil,
              "priceTags" => [],
              "priceValidUntil" => "2025-06-04T23:57:50Z",
              "quantity" => 2,
              "requestIndex" => 0,
              "rewardValue" => 0,
              "seller" => "1",
              "sellerChain" => ["1"],
              "sellingPrice" => 15_250,
              "tax" => 0,
              "unitMultiplier" => 1.0
            }
          ])
          |> Map.put("logisticsInfo", [
            %{
              "addressId" => nil,
              "deliveryChannels" => [
                %{"id" => "delivery", "stockBalance" => 55},
                %{"id" => "pickup-in-point", "stockBalance" => 13}
              ],
              "itemIndex" => 0,
              "quantity" => 2,
              "selectedDeliveryChannel" => nil,
              "selectedSla" => nil,
              "shipsTo" => ["BRA"],
              "slas" => [
                %{
                  "availableDeliveryWindows" => [],
                  "deliveryChannel" => "delivery",
                  "deliveryIds" => [
                    %{
                      "accountCarrierName" => "bawclothinghomolog",
                      "courierId" => "1ac4ae1",
                      "courierName" => "Entrega mais rápida",
                      "dockId" => "16c7f6f",
                      "kitItemDetails" => [],
                      "quantity" => 2,
                      "warehouseId" => "1_1"
                    }
                  ],
                  "deliveryWindow" => nil,
                  "id" => "Express",
                  "listPrice" => 2_390,
                  "lockTTL" => nil,
                  "name" => "Express",
                  "pickupDistance" => 0.0,
                  "pickupPointId" => nil,
                  "pickupStoreInfo" => %{
                    "additionalInfo" => nil,
                    "address" => nil,
                    "dockId" => nil,
                    "friendlyName" => nil,
                    "isPickupStore" => false
                  },
                  "polygonName" => "",
                  "price" => 2_390,
                  "shippingEstimate" => "3bd",
                  "shippingEstimateDate" => nil,
                  "tax" => 0,
                  "transitTime" => "3bd"
                },
                %{
                  "availableDeliveryWindows" => [],
                  "deliveryChannel" => "delivery",
                  "deliveryIds" => [
                    %{
                      "accountCarrierName" => "bawclothinghomolog",
                      "courierId" => "1",
                      "courierName" => "Entrega normal",
                      "dockId" => "1",
                      "kitItemDetails" => [],
                      "quantity" => 2,
                      "warehouseId" => "1_1"
                    }
                  ],
                  "deliveryWindow" => nil,
                  "id" => "Normal",
                  "listPrice" => 2_390,
                  "lockTTL" => nil,
                  "name" => "Normal",
                  "pickupDistance" => 0.0,
                  "pickupPointId" => nil,
                  "pickupStoreInfo" => %{
                    "additionalInfo" => nil,
                    "address" => nil,
                    "dockId" => nil,
                    "friendlyName" => nil,
                    "isPickupStore" => false
                  },
                  "polygonName" => "",
                  "price" => 2_390,
                  "shippingEstimate" => "6bd",
                  "shippingEstimateDate" => nil,
                  "tax" => 0,
                  "transitTime" => "3bd"
                },
                %{
                  "availableDeliveryWindows" => [],
                  "deliveryChannel" => "pickup-in-point",
                  "deliveryIds" => [
                    %{
                      "accountCarrierName" => "bawclothinghomolog",
                      "courierId" => "2",
                      "courierName" => "Retirada em Loja",
                      "dockId" => "18c1bdc",
                      "kitItemDetails" => [],
                      "quantity" => 2,
                      "warehouseId" => "1c3cbfe"
                    }
                  ],
                  "deliveryWindow" => nil,
                  "id" => "Retirada em Loja (1)",
                  "listPrice" => 0,
                  "lockTTL" => nil,
                  "name" => "Retirada em Loja (1)",
                  "pickupDistance" => 3.**************,
                  "pickupPointId" => "1_1",
                  "pickupStoreInfo" => %{
                    "additionalInfo" => "",
                    "address" => %{
                      "addressId" => "1",
                      "addressType" => "pickup",
                      "city" => "São Paulo",
                      "complement" => "",
                      "country" => "BRA",
                      "geoCoordinates" => [-46.64538, -23.52124],
                      "isDisposable" => true,
                      "neighborhood" => "Bom Retiro",
                      "number" => "998",
                      "postalCode" => "01131-000",
                      "receiverName" => nil,
                      "reference" => nil,
                      "state" => "SP",
                      "street" => "Rua dos Italianos"
                    },
                    "dockId" => "18c1bdc",
                    "friendlyName" => "Baw Birth Store",
                    "isPickupStore" => true
                  },
                  "polygonName" => "",
                  "price" => 0,
                  "shippingEstimate" => "0bd",
                  "shippingEstimateDate" => nil,
                  "tax" => 0,
                  "transitTime" => "0bd"
                },
                %{
                  "availableDeliveryWindows" => [],
                  "deliveryChannel" => "pickup-in-point",
                  "deliveryIds" => [
                    %{
                      "accountCarrierName" => "bawclothinghomolog",
                      "courierId" => "2",
                      "courierName" => "Retirada em Loja",
                      "dockId" => "18c1bdc",
                      "kitItemDetails" => [],
                      "quantity" => 2,
                      "warehouseId" => "1c3cbfe"
                    }
                  ],
                  "deliveryWindow" => nil,
                  "id" => "Retirada em Loja (2)",
                  "listPrice" => 0,
                  "lockTTL" => nil,
                  "name" => "Retirada em Loja (2)",
                  "pickupDistance" => 9.*************,
                  "pickupPointId" => "1_2",
                  "pickupStoreInfo" => %{
                    "additionalInfo" => "",
                    "address" => %{
                      "addressId" => "2",
                      "addressType" => "pickup",
                      "city" => "São Paulo",
                      "complement" => "",
                      "country" => "BRA",
                      "geoCoordinates" => [-46.69802, -23.62317],
                      "isDisposable" => true,
                      "neighborhood" => "Jardim das Acacias",
                      "number" => "1089",
                      "postalCode" => "04707-000",
                      "receiverName" => nil,
                      "reference" => nil,
                      "state" => "SP",
                      "street" => "Avenida Roque Petroni Júnior"
                    },
                    "dockId" => "18c1bdc",
                    "friendlyName" => "Baw® Morumbi",
                    "isPickupStore" => true
                  },
                  "polygonName" => "",
                  "price" => 0,
                  "shippingEstimate" => "0bd",
                  "shippingEstimateDate" => nil,
                  "tax" => 0,
                  "transitTime" => "0bd"
                }
              ],
              "stockBalance" => 68
            }
          ])

        {:ok, cart_simulation}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv_instore.id,
            "quantity" => 2,
            "fulfillment_type" => "in-store"
          },
          %{
            "product_variant_id" => pv_instore_no_ecommerce.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ],
        "discounts" => [
          %{
            "type" => "fixed",
            "value" => "10",
            "description" => "teste"
          }
        ],
        "staff_id" => staff.id,
        "location_id" => location.id,
        "country" => "BRA",
        "postal_code" => "01402-000"
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      assert [delivery_group] = response["delivery_groups"]
      assert delivery_group["fulfillment_type"] == "in-store"
      assert delivery_group["pickup_point"] == nil
      assert delivery_group["delivery_time"] == 0

      assert [first_item, second_item] = delivery_group["cart_items"]
      assert first_item["product_variant"]["id"] == pv_instore.id
      assert first_item["list_price"] == "305"
      assert first_item["selling_price"] == "152.5"
      assert first_item["total_price"] == "305"
      assert first_item["quantity"] == 2

      assert second_item["product_variant"]["id"] == pv_instore_no_ecommerce.id
      assert second_item["list_price"] == "20.0"
      assert second_item["selling_price"] == "20.0"
      assert second_item["total_price"] == "20.0"
      assert second_item["quantity"] == 1

      assert response["max_delivery_time"] == 0
      assert response["total_price"] == "315.00"
      assert response["total_items_list_price"] == "630.00"
      assert response["total_manual_discount"] == "10.00"
      assert response["total_delivery_price"] == "0.00"
      assert response["total_items_selling_price"] == "315.00"
      assert response["staff"]["id"] == staff.id

      assert response["discounts"] == [
               %{
                 "type" => "fixed",
                 "value" => "10",
                 "description" => "teste"
               }
             ]
    end

    test "return error/warning messages", %{
      conn: conn,
      user: user
    } do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          sku: "SKU564"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv1,
        source: "vtex",
        external_id: "3"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv2,
        source: "vtex",
        external_id: "1"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      # Mox.stub_with(VTEX.Mock, VTEX.Client)
      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        {:ok, @vtex_cart_simulation2}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 5,
            "fulfillment_type" => "delivery",
            "metadata" => %{
              "deliveryChannel" => "delivery",
              "id" => "Express"
            }
          },
          %{
            "product_variant_id" => pv2.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery",
            "metadata" => %{
              "deliveryChannel" => "delivery",
              "id" => "Express"
            }
          },
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 1,
            "fulfillment_type" => "local-pickup",
            "metadata" => %{
              "deliveryChannel" => "pickup-in-point",
              "id" => "Retirada em Loja (2)"
            }
          }
        ],
        "staff_id" => staff.id,
        "location_id" => location.id,
        "country" => "BRA",
        "postal_code" => "********",
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      pv1_id = pv1.id
      pv2_id = pv2.id

      assert [unfulfillable_delivery_group, delivery_delivery_group] = response["delivery_groups"]

      assert delivery_delivery_group["delivery_time"] == 3
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      refute delivery_delivery_group["pickup_point"]

      assert unfulfillable_delivery_group["fulfillment_type"] == "unfulfillable"
      refute unfulfillable_delivery_group["pickup_point"]

      assert response["total_price"] == "633.90"
      assert response["total_items_list_price"] == "610.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "23.90"
      assert response["total_items_selling_price"] == "610.00"
      assert response["max_delivery_time"] == 3

      assert [second_item] = delivery_delivery_group["cart_items"]
      assert [first_item] = unfulfillable_delivery_group["cart_items"]

      assert first_item["list_price"] == "225"
      assert first_item["product_variant"]["id"] == pv1_id
      assert first_item["quantity"] == 6
      assert first_item["selling_price"] == "225"
      assert first_item["total_price"] == "1350"

      assert second_item["list_price"] == "305"
      assert second_item["product_variant"]["id"] == pv2_id
      assert second_item["quantity"] == 2
      assert second_item["selling_price"] == "305"
      assert second_item["total_price"] == "610"

      assert [first_message, second_message] = response["messages"]

      assert first_message["code"] == "unfulfillable_items"
      assert hd(first_message["info"]["items"])["product_variant_id"] == pv1_id

      assert first_message["message"] ==
               "Some items are not available for the selected fulfillment_type"

      assert first_message["type"] == "error"

      assert second_message["code"] == "items_removed"
      assert hd(second_message["info"]["items"])["product_variant_id"] == pv1_id
      assert second_message["message"] == "Some items were removed from the response."
      assert second_message["type"] == "warning"
    end

    test "respects user asked fulfillment_type", %{
      conn: conn,
      user: user
    } do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          sku: "SKU564"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv1,
        source: "vtex",
        external_id: "3"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv2,
        source: "vtex",
        external_id: "1"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      # Mox.stub_with(VTEX.Mock, VTEX.Client)
      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        {:ok, @vtex_cart_simulation2}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv2.id,
            "quantity" => 1,
            "staff_id" => staff.id,
            "location_id" => location.id,
            "fulfillment_type" => "local-pickup",
            "metadata" => %{
              "deliveryChannel" => "pickup-in-point",
              "id" => "Retirada em Loja (3)"
            }
          },
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 5,
            "fulfillment_type" => "delivery",
            "metadata" => %{
              "deliveryChannel" => "delivery",
              "id" => "Express"
            }
          }
        ],
        "country" => "BRA",
        "postal_code" => "********",
        "customer_id" => customer.id,
        "staff_id" => staff.id,
        "location_id" => location.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      pv1_id = pv1.id
      pv2_id = pv2.id

      assert [unfulfillable_pickup_delivery_group, delivery_delivery_group] =
               response["delivery_groups"]

      assert delivery_delivery_group["delivery_time"] == 3
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      assert delivery_delivery_group["pickup_point"] == nil

      assert unfulfillable_pickup_delivery_group["fulfillment_type"] == "unfulfillable"

      assert [first_item] = unfulfillable_pickup_delivery_group["cart_items"]
      assert [second_item] = delivery_delivery_group["cart_items"]

      assert first_item["list_price"] == "225"
      assert first_item["product_variant"]["id"] == pv2_id
      assert first_item["quantity"] == 6
      assert first_item["selling_price"] == "225"
      assert first_item["total_price"] == "1350"

      assert second_item["list_price"] == "305"
      assert second_item["product_variant"]["id"] == pv1_id
      assert second_item["quantity"] == 2
      assert second_item["selling_price"] == "305"
      assert second_item["total_price"] == "610"

      assert response["max_delivery_time"] == 3

      assert response["total_price"] == "633.90"
      assert response["total_items_list_price"] == "610.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "23.90"
      assert response["total_items_selling_price"] == "610.00"

      assert [first_message] = response["messages"]
      assert first_message["code"] == "unfulfillable_items"
      assert hd(first_message["info"]["items"])["product_variant_id"] == pv2_id

      assert first_message["message"] ==
               "Some items are not available for the selected fulfillment_type"
    end

    test "passing empty string as postal_code does not return local-pickup/delivery items as unfufillable",
         %{conn: conn, user: user} do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU564"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      pv3 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv1,
        source: "vtex",
        external_id: "3"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv2,
        source: "vtex",
        external_id: "1"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, _ ->
        vtex_cart_simulation =
          update_in(@vtex_cart_simulation2, ["logisticsInfo", Access.all(), "slas"], fn _ ->
            []
          end)

        {:ok, vtex_cart_simulation}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv2.id,
            "quantity" => 1,
            "fulfillment_type" => "local-pickup",
            "metadata" => %{
              "deliveryChannel" => "pickup-in-point",
              "id" => "Retirada em Loja (3)"
            }
          },
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 5,
            "fulfillment_type" => "delivery"
          },
          %{
            "product_variant_id" => pv3.id,
            "quantity" => 1,
            "fulfillment_type" => "in-store"
          }
        ],
        "staff_id" => staff.id,
        "country" => "BRA",
        "postal_code" => "",
        "location_id" => location.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      assert response["total_price"] == "1983.90"
      assert response["total_items_list_price"] == "1983.90"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "0.00"
      assert response["total_items_selling_price"] == "1983.90"

      assert [instore_delivery_group, delivery_delivery_group, pickup_delivery_group] =
               response["delivery_groups"]

      assert instore_delivery_group["fulfillment_type"] == "in-store"
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      assert pickup_delivery_group["fulfillment_type"] == "local-pickup"

      assert [first_item] = pickup_delivery_group["cart_items"]
      assert first_item["list_price"] == "225"
      assert first_item["product_variant"]["id"] == pv2.id
      assert first_item["quantity"] == 6
      assert first_item["selling_price"] == "225"
      assert first_item["total_price"] == "1350"

      assert [second_item] = delivery_delivery_group["cart_items"]
      assert second_item["list_price"] == "305"
      assert second_item["product_variant"]["id"] == pv1.id
      assert second_item["quantity"] == 2
      assert second_item["selling_price"] == "305"
      assert second_item["total_price"] == "610"

      assert [third_item] = instore_delivery_group["cart_items"]
      assert third_item["list_price"] == "23.90"
      assert third_item["product_variant"]["id"] == pv3.id
      assert third_item["quantity"] == 1
      assert third_item["selling_price"] == "23.90"
      assert third_item["total_price"] == "23.90"
    end

    test "passing manual discounts to the cart simulation",
         %{conn: conn, user: user} do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv_delivery =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("15.0"),
          price: Decimal.new("15.0"),
          sku: "SKU123"
        )

      pv_local =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("20.0"),
          price: Decimal.new("20.0"),
          sku: "SKU123"
        )

      pv_pickup =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("25.0"),
          price: Decimal.new("25.0"),
          sku: "SKU123"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_delivery,
        source: "vtex",
        external_id: "2"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv_pickup,
        source: "vtex",
        external_id: "3"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential,
        organization: user.organization
      )

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      Mox.expect(VTEX.Mock, :simulate_fulfillment, fn _, _, _, payload ->
        assert [%{"id" => "2"}, %{"id" => "3"}] =
                 payload["items"]

        assert "BRA" = payload["country"]
        assert "01402-000" = payload["postal_code"]

        assert "<EMAIL>" = payload["clientProfileData"]["email"]
        assert "iglucoupon" = payload["marketingData"]["coupon"]
        {:ok, @vtex_cart_simulation}
      end)

      payload =
        %{
          "items" => [
            %{
              "product_variant_id" => pv_delivery.id,
              "quantity" => 40,
              "staff_id" => staff.id,
              "location_id" => location.id,
              "fulfillment_type" => "delivery"
            },
            %{
              "product_variant_id" => pv_local.id,
              "quantity" => 5,
              "fulfillment_type" => "in-store"
            },
            %{
              "product_variant_id" => pv_pickup.id,
              "quantity" => 10,
              "fulfillment_type" => "local-pickup",
              "metadata" => %{
                "id" => "Retirada em Loja (1)",
                "deliveryChannel" => "pickup-in-point",
                "name" => "Retirada em Loja (1)"
              }
            }
          ],
          "discounts" => [
            %{
              "type" => "coupon",
              "value" => "iglucoupon"
            },
            %{
              "type" => "percentage",
              "value" => "20"
            },
            %{
              "type" => "fixed",
              "value" => "17.8"
            }
          ],
          "coupon_codes" => ["iglucoupon"],
          "country" => "BRA",
          "postal_code" => "01402-000",
          "customer_id" => customer.id,
          "staff_id" => staff.id
        }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      pv_delivery_id = pv_delivery.id
      pv_pickup_id = pv_pickup.id
      pv_local_id = pv_local.id

      assert [local_delivery_group, delivery_delivery_group, local_pickup_delivery_group] =
               response["delivery_groups"]

      # First delivery group assertions
      assert local_delivery_group["fulfillment_type"] == "in-store"
      assert local_delivery_group["pickup_point"] == nil
      assert local_delivery_group["delivery_time"] == 0

      # Second delivery group assertions
      assert local_pickup_delivery_group["fulfillment_type"] == "local-pickup"
      assert local_pickup_delivery_group["pickup_point"]
      assert local_pickup_delivery_group["delivery_time"] == 0

      # Third delivery group assertions
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      assert delivery_delivery_group["pickup_point"] == nil
      assert delivery_time = delivery_delivery_group["delivery_time"]

      assert [first_item] = delivery_delivery_group["cart_items"]
      assert [second_item] = local_delivery_group["cart_items"]
      assert [third_item] = local_pickup_delivery_group["cart_items"]

      # First item assertions
      assert first_item["product_variant"]["id"] == pv_delivery_id
      assert first_item["selling_price"] == "10"
      assert first_item["total_price"] == "400"
      assert first_item["quantity"] == 40

      # Second item assertions
      assert second_item["product_variant"]["id"] == pv_local_id
      assert second_item["selling_price"] == "20.0"
      assert second_item["total_price"] == "100.0"
      assert second_item["quantity"] == 5

      # Third item assertions
      assert third_item["product_variant"]["id"] == pv_pickup_id
      assert third_item["selling_price"] == "25"
      assert third_item["total_price"] == "250"
      assert third_item["quantity"] == 10

      # Top-level assertions
      assert response["total_price"] == "600.00"
      assert response["total_items_list_price"] == "950.00"
      assert response["total_manual_discount"] == "167.80"
      assert response["total_delivery_price"] == "17.80"
      assert response["total_items_selling_price"] == "582.20"
      assert response["max_delivery_time"] == delivery_time
      assert response["staff"]["id"] == staff.id
      assert response["customer"]["id"] == customer.id

      assert response["discounts"] == [
               %{
                 "type" => "coupon",
                 "value" => "iglucoupon",
                 "description" => nil
               },
               %{
                 "type" => "percentage",
                 "value" => "20",
                 "description" => nil
               },
               %{
                 "type" => "fixed",
                 "value" => "17.8",
                 "description" => nil
               }
             ]
    end

    test "when in-store items are unavailable, retry fetching price",
         %{conn: conn, user: user} do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU564"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      pv3 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv1,
        source: "vtex",
        external_id: "1"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv2,
        source: "vtex",
        external_id: "2"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv3,
        source: "vtex",
        external_id: "3"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      VTEX.Mock
      |> Mox.expect(:simulate_fulfillment, fn _, _, _, _ ->
        vtex_cart_simulation =
          @vtex_cart_simulation2
          |> Map.update!("items", fn items ->
            items ++
              [
                %{
                  "id" => "2",
                  "requestIndex" => 2,
                  "quantity" => 4,
                  "seller" => "1",
                  "sellerChain" => [
                    "1"
                  ],
                  "tax" => 0,
                  "priceValidUntil" => "2025-08-26T19:21:16Z",
                  "price" => 100,
                  "listPrice" => 300,
                  "rewardValue" => 0,
                  "sellingPrice" => 100,
                  "offerings" => [],
                  "priceTags" => [],
                  "measurementUnit" => "un",
                  "unitMultiplier" => 1.0000,
                  "parentItemIndex" => nil,
                  "parentAssemblyBinding" => nil,
                  "availability" => "cannotBeDelivered",
                  "catalogProvider" =>
                    "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
                  "priceDefinition" => %{
                    "calculatedSellingPrice" => 100,
                    "total" => 400,
                    "sellingPrices" => [
                      %{
                        "value" => 100,
                        "quantity" => 4
                      }
                    ],
                    "reason" => nil
                  },
                  "priceTable" => nil
                }
              ]
          end)

        {:ok, vtex_cart_simulation}
      end)
      |> Mox.expect(:simulate_fulfillment, fn _, _, _, payload ->
        assert payload["postalCode"] == nil

        vtex_cart_simulation =
          @vtex_cart_simulation2
          |> Map.update!("items", fn items ->
            items ++
              [
                %{
                  "id" => "2",
                  "requestIndex" => 2,
                  "quantity" => 4,
                  "seller" => "1",
                  "sellerChain" => [
                    "1"
                  ],
                  "tax" => 0,
                  "priceValidUntil" => "2025-08-26T22:57:18Z",
                  "price" => 100,
                  "listPrice" => 300,
                  "rewardValue" => 0,
                  "sellingPrice" => 75,
                  "offerings" => [],
                  "priceTags" => [
                    %{
                      "name" =>
                        "discount@price-f5555b56-afd3-410a-9d2e-daa9c9cabca5#404ce7a2-29b9-42c6-9a1b-924b962d0585",
                      "value" => -100,
                      "rawValue" => -1.00,
                      "isPercentual" => false,
                      "identifier" => nil,
                      "owner" => "bawclothinghomolog"
                    }
                  ],
                  "measurementUnit" => "un",
                  "unitMultiplier" => 1.0,
                  "parentItemIndex" => nil,
                  "parentAssemblyBinding" => nil,
                  "availability" => "available",
                  "catalogProvider" =>
                    "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
                  "priceDefinition" => %{
                    "calculatedSellingPrice" => 75,
                    "total" => 300,
                    "sellingPrices" => [
                      %{
                        "value" => 75,
                        "quantity" => 4
                      }
                    ],
                    "reason" => nil
                  },
                  "priceTable" => nil
                }
              ]
          end)

        {:ok, vtex_cart_simulation}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv3.id,
            "quantity" => 6,
            "fulfillment_type" => "local-pickup",
            "metadata" => %{
              "deliveryChannel" => "pickup-in-point",
              "id" => "Retirada em Loja (1)"
            }
          },
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery"
          },
          %{
            "product_variant_id" => pv2.id,
            "quantity" => 4,
            "fulfillment_type" => "in-store"
          }
        ],
        "staff_id" => staff.id,
        "country" => "BRA",
        "postal_code" => "12345678",
        "location_id" => location.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      assert response["total_price"] == "1986.90"
      assert response["total_items_list_price"] == "1972.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "23.90"
      assert response["total_items_selling_price"] == "1963.00"

      assert [instore_delivery_group, delivery_delivery_group, pickup_delivery_group] =
               response["delivery_groups"]

      assert instore_delivery_group["fulfillment_type"] == "in-store"
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      assert pickup_delivery_group["fulfillment_type"] == "local-pickup"

      assert [first_item] = pickup_delivery_group["cart_items"]
      assert first_item["list_price"] == "225"
      assert first_item["product_variant"]["id"] == pv3.id
      assert first_item["quantity"] == 6
      assert first_item["selling_price"] == "225"
      assert first_item["total_price"] == "1350"

      assert [second_item] = delivery_delivery_group["cart_items"]
      assert second_item["list_price"] == "305"
      assert second_item["product_variant"]["id"] == pv1.id
      assert second_item["quantity"] == 2
      assert second_item["selling_price"] == "305"
      assert second_item["total_price"] == "610"

      assert [third_item] = instore_delivery_group["cart_items"]
      assert third_item["list_price"] == "3"
      assert third_item["product_variant"]["id"] == pv2.id
      assert third_item["quantity"] == 4
      assert third_item["selling_price"] == "0.75"
      assert third_item["total_price"] == "3"
    end

    test "when in-store items are unavailable, doesnt update local-pickup and delivery prices ",
         %{conn: conn, user: user} do
      product = insert(:product, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      location = insert(:location, organization: user.organization)

      customer = insert(:customer, organization: user.organization, email: "<EMAIL>")

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU564"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      pv3 =
        insert(:product_variant,
          organization: user.organization,
          product: product,
          list_price: Decimal.new("23.90"),
          price: Decimal.new("23.90"),
          sku: "SKU562"
        )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv1,
        source: "vtex",
        external_id: "1"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv2,
        source: "vtex",
        external_id: "2"
      )

      insert(:product_variant_mapping,
        organization: user.organization,
        product_variant: pv3,
        source: "vtex",
        external_id: "3"
      )

      insert(:organization_setting,
        organization: user.organization,
        key: "ecommerce",
        value: %{data: "vtex"}
      )

      insert(:vtex_credential, organization: user.organization)

      Mox.stub_with(VTEX.Mock, VTEXClientStub)

      VTEX.Mock
      |> Mox.expect(:simulate_fulfillment, fn _, _, _, _ ->
        vtex_cart_simulation =
          @vtex_cart_simulation2
          |> Map.update!("items", fn items ->
            items ++
              [
                %{
                  "id" => "2",
                  "requestIndex" => 2,
                  "quantity" => 4,
                  "seller" => "1",
                  "sellerChain" => [
                    "1"
                  ],
                  "tax" => 0,
                  "priceValidUntil" => "2025-08-26T19:21:16Z",
                  "price" => 100,
                  "listPrice" => 300,
                  "rewardValue" => 0,
                  "sellingPrice" => 100,
                  "offerings" => [],
                  "priceTags" => [],
                  "measurementUnit" => "un",
                  "unitMultiplier" => 1.0000,
                  "parentItemIndex" => nil,
                  "parentAssemblyBinding" => nil,
                  "availability" => "cannotBeDelivered",
                  "catalogProvider" =>
                    "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
                  "priceDefinition" => %{
                    "calculatedSellingPrice" => 100,
                    "total" => 400,
                    "sellingPrices" => [
                      %{
                        "value" => 100,
                        "quantity" => 4
                      }
                    ],
                    "reason" => nil
                  },
                  "priceTable" => nil
                }
              ]
          end)

        {:ok, vtex_cart_simulation}
      end)
      |> Mox.expect(:simulate_fulfillment, fn _, _, _, payload ->
        assert payload["postalCode"] == nil

        vtex_cart_simulation =
          @vtex_cart_simulation2
          |> Map.update!("items", fn items ->
            Enum.map(items, fn item ->
              item
              |> Map.update!("quantity", &(&1 * 2))
              |> Map.update!("priceDefinition", fn price_definition ->
                price_definition
                |> Map.update!("total", &(&1 * 2))
              end)
            end) ++
              [
                %{
                  "id" => "2",
                  "requestIndex" => 2,
                  "quantity" => 4,
                  "seller" => "1",
                  "sellerChain" => [
                    "1"
                  ],
                  "tax" => 0,
                  "priceValidUntil" => "2025-08-26T22:57:18Z",
                  "price" => 100,
                  "listPrice" => 300,
                  "rewardValue" => 0,
                  "sellingPrice" => 75,
                  "offerings" => [],
                  "priceTags" => [
                    %{
                      "name" =>
                        "discount@price-f5555b56-afd3-410a-9d2e-daa9c9cabca5#404ce7a2-29b9-42c6-9a1b-924b962d0585",
                      "value" => -100,
                      "rawValue" => -1.00,
                      "isPercentual" => false,
                      "identifier" => nil,
                      "owner" => "bawclothinghomolog"
                    }
                  ],
                  "measurementUnit" => "un",
                  "unitMultiplier" => 1.0,
                  "parentItemIndex" => nil,
                  "parentAssemblyBinding" => nil,
                  "availability" => "available",
                  "catalogProvider" =>
                    "vrn:vtex.catalog-api-proxy:-:bawclothinghomolog:master:/proxy/authenticated/catalog/pvt/sku/stockkeepingunitbyid/1",
                  "priceDefinition" => %{
                    "calculatedSellingPrice" => 75,
                    "total" => 300,
                    "sellingPrices" => [
                      %{
                        "value" => 75,
                        "quantity" => 4
                      }
                    ],
                    "reason" => nil
                  },
                  "priceTable" => nil
                }
              ]
          end)

        {:ok, vtex_cart_simulation}
      end)

      payload = %{
        "items" => [
          %{
            "product_variant_id" => pv3.id,
            "quantity" => 6,
            "fulfillment_type" => "local-pickup",
            "metadata" => %{
              "deliveryChannel" => "pickup-in-point",
              "id" => "Retirada em Loja (1)"
            }
          },
          %{
            "product_variant_id" => pv1.id,
            "quantity" => 2,
            "fulfillment_type" => "delivery"
          },
          %{
            "product_variant_id" => pv2.id,
            "quantity" => 4,
            "fulfillment_type" => "in-store"
          }
        ],
        "staff_id" => staff.id,
        "country" => "BRA",
        "postal_code" => "12345678",
        "location_id" => location.id,
        "customer_id" => customer.id
      }

      conn = post(conn, ~p"/api/carts/simulate", payload)
      assert response = json_response(conn, :ok)

      assert response["total_price"] == "1986.90"
      assert response["total_items_list_price"] == "1972.00"
      assert response["total_manual_discount"] == "0.00"
      assert response["total_delivery_price"] == "23.90"
      assert response["total_items_selling_price"] == "1963.00"

      assert [instore_delivery_group, delivery_delivery_group, pickup_delivery_group] =
               response["delivery_groups"]

      assert instore_delivery_group["fulfillment_type"] == "in-store"
      assert delivery_delivery_group["fulfillment_type"] == "delivery"
      assert pickup_delivery_group["fulfillment_type"] == "local-pickup"

      assert [first_item] = pickup_delivery_group["cart_items"]
      assert first_item["list_price"] == "225"
      assert first_item["product_variant"]["id"] == pv3.id
      assert first_item["quantity"] == 6
      assert first_item["selling_price"] == "225"
      assert first_item["total_price"] == "1350"

      assert [second_item] = delivery_delivery_group["cart_items"]
      assert second_item["list_price"] == "305"
      assert second_item["product_variant"]["id"] == pv1.id
      assert second_item["quantity"] == 2
      assert second_item["selling_price"] == "305"
      assert second_item["total_price"] == "610"

      assert [third_item] = instore_delivery_group["cart_items"]
      assert third_item["list_price"] == "3"
      assert third_item["product_variant"]["id"] == pv2.id
      assert third_item["quantity"] == 4
      assert third_item["selling_price"] == "0.75"
      assert third_item["total_price"] == "3"
    end
  end

  describe "group/2" do
    test "create a new grouped cart", %{
      conn: conn,
      user: user
    } do
      loc = insert(:location, organization: user.organization)

      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          location: loc,
          total_price: Decimal.new("100.00"),
          total_items_selling_price: Decimal.new("90.00"),
          total_items_list_price: Decimal.new("110.00"),
          total_manual_discount: Decimal.new("10.00"),
          total_delivery_price: Decimal.new("5.00")
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      cart_item1 =
        insert(:cart_item,
          product_variant: pv1,
          quantity: 2,
          organization: user.organization,
          delivery_group: delivery_group
        )

      cart_item2 =
        insert(:cart_item,
          product_variant: pv2,
          quantity: 2,
          organization: user.organization,
          delivery_group: delivery_group
        )

      payload =
        %{
          "items" => [
            %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 1},
            %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 1},
            %{"id" => cart_item1.id, "quantity" => 1, "group_index" => 2},
            %{"id" => cart_item2.id, "quantity" => 1, "group_index" => 2}
          ]
        }

      conn = post(conn, ~p"/api/carts/#{cart.id}/group", payload)
      assert json_response(conn, :ok)
    end
  end
end
