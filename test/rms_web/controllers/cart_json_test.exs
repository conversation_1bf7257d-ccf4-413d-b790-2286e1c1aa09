defmodule RmsWeb.CartJSONTest do
  use RmsWeb.ConnCase, async: true
  import Rms.Factory

  describe "render_cart_item/1" do
    test "adds default variation type when none exists" do
      org = insert(:organization)
      product = insert(:product, organization: org)

      product_variant =
        insert(:product_variant, product: product, organization: org, variation_types: [])

      cart = insert(:cart, organization: org)
      delivery_group = insert(:delivery_group, cart: cart, organization: org)

      cart_item =
        insert(:cart_item,
          product_variant: product_variant,
          organization: org,
          delivery_group: delivery_group
        )

      rendered = RmsWeb.CartJSON.render_cart_item(cart_item)

      assert rendered.product_variant.variant_types == [
               %{"default" => "default", :metadata => %{}}
             ]
    end

    test "preserves existing variation types" do
      org = insert(:organization)
      product = insert(:product, organization: org)
      variation_types = [%{key: "color", value: "blue", metadata: %{hex: "#0000FF"}}]

      product_variant =
        insert(:product_variant,
          product: product,
          organization: org,
          variation_types: variation_types
        )

      cart = insert(:cart, organization: org)
      delivery_group = insert(:delivery_group, cart: cart, organization: org)

      cart_item =
        insert(:cart_item,
          product_variant: product_variant,
          organization: org,
          delivery_group: delivery_group
        )

      rendered = RmsWeb.CartJSON.render_cart_item(cart_item)

      assert rendered.product_variant.variant_types == [
               %{"color" => "blue", :metadata => %{hex: "#0000FF"}}
             ]
    end
  end
end
