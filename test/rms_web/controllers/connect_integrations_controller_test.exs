defmodule RmsWeb.ConnectIntegrationsControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  describe "/connect/:integration" do
    test "creates shopify credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/shopify", %{shop: "salva-01", credential: "my credential"})
               |> json_response(201)

      assert response["success"]
      assert Rms.Integrations.get_shopify_credential!(user.organization_id)
    end

    test "creates shopify app", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/shopify", %{
                 shop: "salva-01.myshopify.com",
                 credential: "my credential",
                 client_id: "test client id",
                 client_secret: "test client secret"
               })
               |> json_response(201)

      assert response["success"]
      assert Rms.Integrations.unsafe_get_shopify_app("salva-01.myshopify.com")

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorker,
        args: %{
          "storefront_credential" => nil,
          "organization_id" => user.organization_id
        }
      )
    end

    test "creates shopify app and a storefront credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/shopify", %{
                 shop: "salva-01.myshopify.com",
                 credential: "my credential",
                 client_id: "test client id",
                 client_secret: "test client secret",
                 storefront_credential: "teste"
               })
               |> json_response(201)

      assert response["success"]
      assert Rms.Integrations.unsafe_get_shopify_app("salva-01.myshopify.com")

      assert_enqueued(
        worker: Rms.Integrations.Shopify.Workers.CreateStorefrontCredentialWorker,
        args: %{
          "storefront_credential" => "teste",
          "organization_id" => user.organization_id
        }
      )
    end

    test "creates pagarme credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/pagarme", %{credential: "my credential"})
               |> json_response(201)

      assert response["success"]
      assert Rms.Integrations.get_pagarme_credential!(user.organization_id)
    end

    test "creates pagarme hook credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/pagarme_hook", %{
                 credential: "my:credential",
                 shop_id: "acc_1"
               })
               |> json_response(201)

      assert response["success"]
      assert Rms.Integrations.get_pagarme_hook_credential("acc_1", "my:credential")
    end

    test "creates vtex credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/vtex", %{
                 account_name: "my account",
                 app_key: "my app key",
                 app_token: "my app token",
                 affiliate_id: "GLL",
                 sales_channel_id: "1"
               })
               |> json_response(201)

      assert response["success"]
      vtex_credential = Rms.Integrations.get_vtex_credential!(user.organization_id)
      assert vtex_credential.account_name == "my account"
      assert vtex_credential.main_account_name == "my account"
      assert vtex_credential.app_key == "my app key"
      assert vtex_credential.app_token == "my app token"
      assert vtex_credential.affiliate_id == "GLL"
      assert vtex_credential.sales_channel_id == 1

      assert_enqueued(
        worker: Rms.Workers.VTEXImportProduct,
        args: %{
          "organization_id" => user.organization_id,
          "affiliate_id" => "GLL",
          "sales_channel_id" => 1
        }
      )
    end

    test "creates vinco credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      loc = insert(:location, organization: user.organization)

      assert response =
               conn
               |> post(~p"/api/connect/vinco", %{
                 key: "iglu",
                 location_id: loc.id
               })
               |> json_response(201)

      assert response["success"]
    end

    test "creates erp credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/erp", %{
                 url: "iglu",
                 credential: "iglu"
               })
               |> json_response(201)

      assert response["success"]
    end

    test "creates cielo credential", %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      assert response =
               conn
               |> post(~p"/api/connect/cielo", %{
                 client_id: "iglu",
                 client_secret: "iglu",
                 token: "iglu",
                 expires_in: 230
               })
               |> json_response(201)

      assert response["success"]
    end
  end
end
