defmodule RmsWeb.CustomerControllerTest do
  use RmsWeb.ConnCase, async: false

  import Rms.Factory

  alias Rms.Customers.Customer

  @ibge_mock Application.compile_env(:rms, :ibge_client, Rms.Integrations.Ibge.Mock)

  @create_attrs %{
    name: "some name",
    document_type: "SOME DOCUMENT_TYPE",
    document: "some document",
    document_hash: "some document_hash",
    email: "some email",
    email_hash: "some email_hash",
    primary_phone_number: "some primary_phone_number",
    primary_phone_number_hash: "some primary_phone_number_hash",
    addresses: []
  }
  @create_attrs_with_address %{
    name: "some name",
    document_type: "SOME DOCUMENT_TYPE",
    document: "some document",
    document_hash: "some document_hash",
    email: "some email",
    email_hash: "some email_hash",
    primary_phone_number: "some primary_phone_number",
    primary_phone_number_hash: "some primary_phone_number_hash",
    addresses: [
      %{
        receiver_name: "<PERSON>",
        city_name: "São Paulo",
        state: "SP",
        country_name: "Brazil",
        neighborhood: "Jardim Paulista",
        street: "Av. Paulista",
        street_type: "Avenue",
        number: "1000",
        zip: "01310100",
        complement: "Apt 1001"
      }
    ]
  }
  @update_attrs %{
    name: "some updated name",
    document_type: "SOME UPDATED DOCUMENT_TYPE",
    document: "some updated document",
    document_hash: "some updated document_hash",
    email: "some updated email",
    email_hash: "some updated email_hash",
    primary_phone_number: "some updated primary_phone_number",
    primary_phone_number_hash: "some updated primary_phone_number_hash"
  }
  @invalid_attrs %{
    name: nil,
    document_type: nil,
    document: nil,
    document_hash: nil,
    email: nil,
    email_hash: nil,
    primary_phone_number: nil,
    primary_phone_number_hash: nil
  }

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)
    {:ok, conn: put_req_header(conn, "accept", "application/json"), user: user}
  end

  describe "index" do
    test "lists all customers", %{conn: conn} do
      conn = get(conn, ~p"/api/customers")
      assert json_response(conn, 200)["data"] == []
    end

    test "searches customers including from the external API by name", %{conn: conn, user: user} do
      # Insert a customer in the database
      insert(:customer, name: "DB Customer John", organization: user.organization)
      insert(:customer, name: "Another DB Customer John", organization: user.organization)
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************"
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=Customer John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" in customer_names
      assert "Another DB Customer John" in customer_names
      assert "API Customer John" in customer_names
    end

    test "searches customers and address including from the external API by name", %{
      conn: conn,
      user: user
    } do
      # Insert a customer in the database
      insert(:customer, name: "DB Customer John", organization: user.organization)
      insert(:customer, name: "Another DB Customer John", organization: user.organization)
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************",
                "addresses" => [
                  %{
                    "street" => "AVENIDA LAURO SODRÉ",
                    "number" => "150",
                    "complement" => "Bl 1 ap 804",
                    "neighborhood" => "BOTAFOGO",
                    "city" => "RIO DE JANEIRO",
                    "state" => "RJ",
                    "country_name" => "BRASIL",
                    "zip" => "22290070"
                  }
                ]
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=Customer John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" in customer_names
      assert "Another DB Customer John" in customer_names
      assert "API Customer John" in customer_names

      api_customer_addresses =
        Enum.find(response_data, fn customer -> customer["name"] == "API Customer John" end)[
          "addresses"
        ]

      assert [
               %{
                 "city_name" => "Rio De Janeiro",
                 "complement" => "Bl 1 ap 804",
                 "country_name" => "BRA",
                 "id" => _,
                 "neighborhood" => "Botafogo",
                 "number" => "150",
                 "receiver_name" => "API Customer John",
                 "state" => "RJ",
                 "street" => "Avenida Lauro Sodré",
                 "street_type" => nil,
                 "zip" => "22290070"
               }
             ] = api_customer_addresses
    end

    test "searches customers and address including from the external API by name and ignore invalid addresses",
         %{
           conn: conn,
           user: user
         } do
      # Insert a customer in the database
      insert(:customer, name: "DB Customer John", organization: user.organization)
      insert(:customer, name: "Another DB Customer John", organization: user.organization)
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************",
                "addresses" => [
                  %{
                    "street" => "AVENIDA LAURO SODRÉ",
                    "number" => "150",
                    "complement" => "Bl 1 ap 804",
                    "neighborhood" => "BOTAFOGO",
                    "city" => "RIO DE JANEIRO",
                    "state" => "RJ",
                    "country_name" => "BRASIL",
                    "zip" => "22290070"
                  },
                  %{
                    "street" => "AVENIDA LAURO SODRÉ",
                    "number" => "152",
                    "complement" => "Bl 1 ap 804",
                    "neighborhood" => "BOTAFOGO",
                    "city" => "RIO DE JANEIRO",
                    "state" => "RJ",
                    "country_name" => "BRASIL",
                    "zip" => "222"
                  }
                ]
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=Customer John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" in customer_names
      assert "Another DB Customer John" in customer_names
      assert "API Customer John" in customer_names

      api_customer_addresses =
        Enum.find(response_data, fn customer -> customer["name"] == "API Customer John" end)[
          "addresses"
        ]

      assert [
               %{
                 "city_name" => "Rio De Janeiro",
                 "complement" => "Bl 1 ap 804",
                 "country_name" => "BRA",
                 "id" => _,
                 "neighborhood" => "Botafogo",
                 "number" => "150",
                 "receiver_name" => "API Customer John",
                 "state" => "RJ",
                 "street" => "Avenida Lauro Sodré",
                 "street_type" => nil,
                 "zip" => "22290070"
               }
             ] = api_customer_addresses
    end

    test "searches customers including from the external API by name with ad", %{
      conn: conn,
      user: user
    } do
      # Insert a customer in the database
      insert(:customer, name: "DB Customer John", organization: user.organization)
      insert(:customer, name: "Another DB Customer John", organization: user.organization)
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************"
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=Customer John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" in customer_names
      assert "Another DB Customer John" in customer_names
      assert "API Customer John" in customer_names
    end

    test "searches customers including from the external API by name with existing user in DB", %{
      conn: conn,
      user: user
    } do
      # Insert a customer in the database with the same data as the API response
      insert_pair(:customer,
        name: "API Customer John",
        document_type: "ssn",
        document: "987654321",
        email: "<EMAIL>",
        primary_phone_number: "************",
        organization: user.organization
      )

      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email_hash"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************"
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=Customer John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes the customer from the DB only
      assert "API Customer John" in customer_names
      assert Enum.count(customer_names, fn name -> name == "API Customer John" end) == 2
    end

    test "calling the endpoint twice does not import the same customer twice", %{
      conn: conn,
      user: user
    } do
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email", "document", "document_type"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: query} ->
          assert [{:name, "Customer John"}] == query

          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************"
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name the first time
      fisrt_call = get(conn, ~p"/api/customers?name=Customer John")
      response_data_first_call = json_response(fisrt_call, 200)["data"]

      # Search for customers by name the second time
      second_call = get(conn, ~p"/api/customers?name=Customer John")
      response_data_second_call = json_response(second_call, 200)["data"]

      # Ensure the response data from both calls are the same
      assert response_data_first_call == response_data_second_call

      # Ensure the response includes the API customer only once
      customer_names = Enum.map(response_data_second_call, fn customer -> customer["name"] end)
      assert Enum.count(customer_names, fn name -> name == "API Customer John" end) == 1
    end

    test "calling the endpoint twice does not import the same customer twice and keep existing adresses",
         %{
           conn: conn,
           user: user
         } do
      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        unique_on: ["email", "document", "document_type"],
        active: true
      )

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: [{:name, "Customer John"}]} ->
          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************",
                "addresses" => [
                  %{
                    "street" => "AVENIDA LAURO SODRÉ",
                    "number" => "150",
                    "complement" => "Bl 1 ap 804",
                    "neighborhood" => "BOTAFOGO",
                    "city" => "RIO DE JANEIRO",
                    "state" => "RJ",
                    "country_name" => "BRASIL",
                    "zip" => "22290070"
                  }
                ]
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name the first time
      fisrt_call = get(conn, ~p"/api/customers?name=Customer John")
      [%{"addresses" => [first_address]}] = json_response(fisrt_call, 200)["data"]

      # Mock the Tesla to return a sample response
      Tesla.Mock.mock(fn
        %{query: [{:name, "API Customer John"}]} ->
          customers_response = [
            %{
              "customer" => %{
                "name" => "API Customer John",
                "document_type" => "ssn",
                "document" => "987654321",
                "email" => "<EMAIL>",
                "primary_phone_number" => "************",
                "addresses" => [
                  %{
                    "street" => "AVENIDA LAURO SODRÉ",
                    "number" => "151",
                    "complement" => "Bl 1 ap 804",
                    "neighborhood" => "BOTAFOGO",
                    "city" => "RIO DE JANEIRO",
                    "state" => "RJ",
                    "country_name" => "BRASIL",
                    "zip" => "22290070"
                  }
                ]
              }
            }
          ]

          %Tesla.Env{status: 200, body: Jason.encode!(customers_response)}
      end)

      # Search for customers by name the second time
      second_call = get(conn, ~p"/api/customers?name=API Customer John")

      [response_data_second_call] =
        json_response(second_call, 200)["data"]

      assert length(response_data_second_call["addresses"]) == 2
      assert first_address in response_data_second_call["addresses"]

      assert %{
               "addresses" => [
                 ^first_address,
                 %{
                   "city_name" => "Rio De Janeiro",
                   "complement" => "Bl 1 ap 804",
                   "country_name" => "BRA",
                   "neighborhood" => "Botafogo",
                   "number" => "151",
                   "receiver_name" => "API Customer John",
                   "state" => "RJ",
                   "street" => "Avenida Lauro Sodré",
                   "street_type" => nil,
                   "zip" => "22290070"
                 }
               ],
               "document" => "987654321",
               "document_type" => "SSN",
               "email" => "<EMAIL>",
               "name" => "API Customer John",
               "primary_phone_number" => "************"
             } = response_data_second_call
    end

    test "searches customers by name from the external shopify API", %{conn: conn, user: user} do
      # Insert a customer in the database
      insert(:customer,
        name: "DB Customer John",
        email: "<EMAIL>",
        organization: user.organization
      )

      insert(:customer,
        name: "Another DB Customer John",
        primary_phone_number: "+5511999981082",
        organization: user.organization
      )

      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        endpoint: "https://shopify.com",
        unique_on: ["email", "primary_phone_number"],
        headers: [],
        active: true
      )

      insert(:shopify_credential, organization: user.organization)

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customers, 1, fn _, opts ->
        assert opts[:query] =~ "John"

        {:ok,
         %{
           items: [
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "2"
               },
               "email" => "<EMAIL>",
               "firstName" => "John",
               "id" => "gid://shopify/Customer/1",
               "lastName" => "Doe",
               "phone" => "+5511999981081"
             },
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "24507138"
               },
               "email" => "<EMAIL>",
               "firstName" => "Jane",
               "id" => "gid://shopify/Customer/2",
               "lastName" => "John",
               "phone" => "+5511999981082"
             }
           ],
           cursor: 1
         }}
      end)

      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 389_098,
             name: "Víctor dos Dourados",
             uf_name: "Amazonas",
             uf_initials: "AM"
           }
         ]}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?name=John")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" in customer_names
      assert "Another DB Customer John" in customer_names
      assert "John Doe" in customer_names
      assert "Jane John" in customer_names

      Enum.each(response_data, fn
        %{"name" => "John Doe", "addresses" => addresses} ->
          assert addresses == []

        %{"name" => "Jane John", "addresses" => addresses} ->
          assert [
                   %{
                     "city_code" => city_code,
                     "city_name" => city_name,
                     "complement" => complement,
                     "country_name" => country_name,
                     "id" => _id,
                     "neighborhood" => neighborhood,
                     "number" => number,
                     "receiver_name" => receiver_name,
                     "state" => state,
                     "street" => street,
                     "street_type" => street_type,
                     "zip" => zip
                   }
                 ] = addresses

          assert city_code == "389098"
          assert city_name == "Víctor dos Dourados"
          assert complement == "AP 878"
          assert country_name == "Brazil"
          assert neighborhood == nil
          assert number == nil
          assert receiver_name == "Jake Peralta"
          assert state == "AM"
          assert street == "Trecho Sais, 54"
          assert street_type == nil
          assert zip == "24507138"

        _ ->
          :ok
      end)
    end

    test "searches customers from the external shopify API", %{conn: conn, user: user} do
      # Insert a customer in the database
      insert(:customer,
        name: "DB Customer John",
        email: "<EMAIL>",
        organization: user.organization
      )

      insert(:customer,
        name: "Another DB Customer John",
        primary_phone_number: "+5511999981082",
        organization: user.organization
      )

      # Insert a customer endpoint for the organization
      insert(:customer_endpoint,
        organization: user.organization,
        endpoint: "https://shopify.com",
        unique_on: ["email", "primary_phone_number"],
        headers: [],
        active: true
      )

      insert(:shopify_credential, organization: user.organization)

      Mox.expect(Rms.Integrations.Shopify.Mock, :client, 1, fn _, _ ->
        :mock_client
      end)

      Mox.expect(Rms.Integrations.Shopify.Mock, :fetch_customers, 1, fn _, _ ->
        {:ok,
         %{
           items: [
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "24507138"
               },
               "email" => "<EMAIL>",
               "firstName" => "John",
               "id" => "gid://shopify/Customer/1",
               "lastName" => "3",
               "phone" => "+5511999981081"
             },
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "24507138"
               },
               "email" => "<EMAIL>",
               "firstName" => "John",
               "id" => "gid://shopify/Customer/2",
               "lastName" => "2",
               "phone" => "+5511999981082"
             },
             %{
               "defaultAddress" => %{
                 "address1" => "Trecho Sais, 54",
                 "address2" => "AP 878",
                 "city" => "Víctor dos Dourados",
                 "company" => "85655142503",
                 "country" => "Brazil",
                 "name" => "Jake Peralta",
                 "provinceCode" => "AM",
                 "zip" => "24507138"
               },
               "email" => "<EMAIL>",
               "firstName" => "John",
               "id" => "gid://shopify/Customer/3",
               "lastName" => "3",
               "phone" => "+5511999981083"
             }
           ],
           cursor: 1
         }}
      end)

      Mox.expect(@ibge_mock, :get_cities_data, fn ->
        {:ok,
         [
           %{
             ibge_id: 3_550_308,
             name: "Víctor dos Dourados",
             uf_name: "Amazonas",
             uf_initials: "AM"
           }
         ]}
      end)

      # Search for customers by name
      conn = get(conn, ~p"/api/customers?email=<EMAIL>")

      # Extract the response data
      response_data = json_response(conn, 200)["data"]

      # Extract the names from the response data
      customer_names = Enum.map(response_data, fn customer -> customer["name"] end)

      # Ensure the response includes both the DB and API customers
      assert "DB Customer John" not in customer_names
      assert "Another DB Customer John" not in customer_names
      assert "John 3" in customer_names
    end

    test "searches customers by name", %{conn: conn, user: user} do
      existing_customer =
        insert(:customer,
          name: "John Doe",
          organization: user.organization
        )

      insert(:customer, name: "Jane Smith", organization: user.organization)

      conn = get(conn, ~p"/api/customers?name=John Doe")

      assert [
               %{
                 "id" => existing_customer_id,
                 "name" => "John Doe"
               }
             ] = json_response(conn, 200)["data"]

      assert existing_customer.id == existing_customer_id
    end

    test "allows partial search on name", %{conn: conn, user: user} do
      insert(:customer,
        name: "Partial Name Test",
        organization: user.organization
      )

      insert(:customer,
        name: "Another Name",
        organization: user.organization
      )

      conn = get(conn, ~p"/api/customers?name=Partial")

      assert [%{"name" => "Partial Name Test"}] = json_response(conn, 200)["data"]
    end

    test "searches customers by document_type and document without punctuation", %{
      conn: conn,
      user: user
    } do
      existing_customer =
        insert(:customer,
          document_type: "ID",
          document: "12345678",
          organization: user.organization
        )

      insert(:customer,
        document_type: "Passport",
        document: "87654321",
        organization: user.organization
      )

      conn = get(conn, ~p"/api/customers?document_type=ID&document=12345678")

      assert [
               %{
                 "id" => existing_customer_id,
                 "document_type" => "ID",
                 "document" => "12345678"
               }
             ] = json_response(conn, 200)["data"]

      assert existing_customer.id == existing_customer_id
    end

    test "searches customers by document_type and document with punctuation", %{
      conn: conn,
      user: user
    } do
      existing_customer =
        insert(:customer,
          document_type: "ID",
          document: "12345678",
          organization: user.organization
        )

      insert(:customer,
        document_type: "Passport",
        document: "87654321",
        organization: user.organization
      )

      conn = get(conn, ~p"/api/customers?document_type=ID&document=123.456-78")

      assert [
               %{
                 "id" => existing_customer_id_2,
                 "document_type" => "ID",
                 "document" => "12345678"
               }
             ] = json_response(conn, 200)["data"]

      assert existing_customer.id == existing_customer_id_2
    end

    test "searches customers by email", %{conn: conn, user: user} do
      existing_customer =
        insert(:customer,
          email: "<EMAIL>",
          organization: user.organization
        )

      insert(:customer, email: "<EMAIL>", organization: user.organization)

      conn = get(conn, ~p"/api/customers?email=<EMAIL>")

      assert [
               %{
                 "id" => existing_customer_id,
                 "email" => "<EMAIL>"
               }
             ] = json_response(conn, 200)["data"]

      assert existing_customer.id == existing_customer_id
    end

    test "searches customers by primary_phone_number without punctuation", %{
      conn: conn,
      user: user
    } do
      existing_customer =
        insert(:customer,
          primary_phone_number: "1234567890",
          organization: user.organization
        )

      insert(:customer, primary_phone_number: "0987654321", organization: user.organization)

      conn = get(conn, ~p"/api/customers?primary_phone_number=1234567890")

      assert [
               %{
                 "id" => existing_customer_id,
                 "primary_phone_number" => "1234567890"
               }
             ] = json_response(conn, 200)["data"]

      assert existing_customer.id == existing_customer_id
    end
  end

  test "searches customers by primary_phone_number with punctuation", %{conn: conn, user: user} do
    existing_customer =
      insert(:customer,
        primary_phone_number: "12234567890",
        organization: user.organization
      )

    insert(:customer, primary_phone_number: "0987654321", organization: user.organization)

    conn = get(conn, ~p"/api/customers?primary_phone_number=(12)23456-7890")

    assert [
             %{
               "id" => existing_customer_id,
               "primary_phone_number" => "12234567890"
             }
           ] = json_response(conn, 200)["data"]

    assert existing_customer.id == existing_customer_id
  end

  describe "paginated_customers/2" do
    test "lists all customers with pagination", %{conn: conn, user: user} do
      insert_list(25, :customer,
        organization: user.organization,
        inserted_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC")
      )

      # First page
      conn = get(conn, ~p"/api/customers/paginated", limit: 10, count: true)
      response = json_response(conn, 200)

      assert length(response["data"]) == 10
      assert response["metadata"]["after"] != nil
      assert response["metadata"]["before"] == nil
      assert response["metadata"]["limit"] == 10
      assert response["metadata"]["total_count"] == 25

      # Second page
      conn =
        get(conn, ~p"/api/customers/paginated",
          limit: 10,
          after: response["metadata"]["after"],
          count: true
        )

      response = json_response(conn, 200)

      assert length(response["data"]) == 10
      assert response["metadata"]["after"] != nil
      assert response["metadata"]["before"] != nil
      assert response["metadata"]["limit"] == 10
      assert response["metadata"]["total_count"] == 25

      # Last page
      conn =
        get(conn, ~p"/api/customers/paginated",
          limit: 10,
          after: response["metadata"]["after"],
          count: true
        )

      response = json_response(conn, 200)

      assert length(response["data"]) == 5
      assert response["metadata"]["after"] == nil
      assert response["metadata"]["before"] != nil
      assert response["metadata"]["limit"] == 10
      assert response["metadata"]["total_count"] == 25
    end

    test "lists empty customers with metadata", %{conn: conn} do
      conn = get(conn, ~p"/api/customers/paginated", count: true)
      response = json_response(conn, 200)

      assert response["data"] == []
      assert response["metadata"]["after"] == nil
      assert response["metadata"]["before"] == nil
      assert response["metadata"]["total_count"] == 0
    end

    test "does not return other organization's custoemrs", %{conn: conn} do
      other_org = insert(:organization)

      insert_list(25, :customer,
        organization: other_org,
        inserted_at: DateTime.from_naive!(~N[2023-01-01T00:00:00], "Etc/UTC")
      )

      # First page
      conn = get(conn, ~p"/api/customers/paginated", limit: 10, count: true, search: "email")
      response = json_response(conn, 200)

      assert Enum.empty?(response["data"])
      assert response["metadata"]["total_count"] == 0
    end
  end

  describe "create customer" do
    test "create customer without address", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: Map.delete(@create_attrs, :addresses))
      assert %{"id" => id} = json_response(conn, 201)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some document",
               "document_type" => "SOME DOCUMENT_TYPE",
               "email" => "some email",
               "name" => "some name",
               "primary_phone_number" => "some primary_phone_number",
               "addresses" => []
             } = json_response(conn, 200)["data"]
    end

    test "create customer with null address", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: Map.merge(%{addresses: nil}, @create_attrs))
      assert %{"id" => id} = json_response(conn, 201)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some document",
               "document_type" => "SOME DOCUMENT_TYPE",
               "email" => "some email",
               "name" => "some name",
               "primary_phone_number" => "some primary_phone_number",
               "addresses" => []
             } = json_response(conn, 200)["data"]
    end

    test "create customer with empty address", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: @create_attrs)
      assert %{"id" => id} = json_response(conn, 201)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some document",
               "document_type" => "SOME DOCUMENT_TYPE",
               "email" => "some email",
               "name" => "some name",
               "primary_phone_number" => "some primary_phone_number",
               "addresses" => []
             } = json_response(conn, 200)["data"]
    end

    test "create customer with address", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: @create_attrs_with_address)
      assert %{"id" => id} = json_response(conn, 201)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some document",
               "document_type" => "SOME DOCUMENT_TYPE",
               "email" => "some email",
               "name" => "some name",
               "primary_phone_number" => "some primary_phone_number",
               "addresses" => [
                 %{
                   "receiver_name" => "John Doe",
                   "city_name" => "São Paulo",
                   "state" => "SP",
                   "country_name" => "Brazil",
                   "neighborhood" => "Jardim Paulista",
                   "street" => "Av. Paulista",
                   "street_type" => "Avenue",
                   "number" => "1000",
                   "zip" => "01310100",
                   "complement" => "Apt 1001"
                 }
               ]
             } = json_response(conn, 200)["data"]
    end

    test "renders customer when data is valid", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: @create_attrs)
      assert %{"id" => id} = json_response(conn, 201)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some document",
               "document_type" => "SOME DOCUMENT_TYPE",
               "email" => "some email",
               "name" => "some name",
               "primary_phone_number" => "some primary_phone_number",
               "addresses" => []
             } = json_response(conn, 200)["data"]
    end

    test "renders errors when data is invalid", %{conn: conn} do
      conn = post(conn, ~p"/api/customers", customer: @invalid_attrs)
      assert json_response(conn, 422)["errors"] != %{}
    end
  end

  describe "update customer" do
    setup [:create_customer]

    test "renders customer when data is valid", %{
      conn: conn,
      customer: %Customer{id: id} = customer
    } do
      conn = put(conn, ~p"/api/customers/#{customer}", customer: @update_attrs)
      assert %{"id" => ^id} = json_response(conn, 200)["data"]

      conn = get(conn, ~p"/api/customers/#{id}")

      assert %{
               "id" => ^id,
               "document" => "some updated document",
               "document_type" => "SOME UPDATED DOCUMENT_TYPE",
               "email" => "some updated email",
               "name" => "some updated name",
               "primary_phone_number" => "some updated primary_phone_number"
             } = json_response(conn, 200)["data"]
    end

    test "renders errors when data is invalid", %{conn: conn, customer: customer} do
      conn = put(conn, ~p"/api/customers/#{customer}", customer: @invalid_attrs)
      assert json_response(conn, 422)["errors"] != %{}
    end

    test "renders the same customer when trying to update the addresses prop", %{
      conn: conn,
      customer: customer
    } do
      receiver_name = Ecto.UUID.autogenerate()

      conn =
        put(conn, ~p"/api/customers/#{customer}",
          customer: %{addresses: [string_params_for(:address, receiver_name: receiver_name)]}
        )

      id = customer.id
      assert %{"id" => ^id} = json_response(conn, 200)["data"]

      conn = get(conn, ~p"/api/customers/#{customer}")

      address = Rms.Repo.preload(customer, :addresses).addresses

      assert %{
               "id" => ^id,
               "addresses" => ^address
             } = json_response(conn, 200)["data"]
    end
  end

  describe "delete customer" do
    setup [:create_customer]

    test "deletes chosen customer", %{conn: conn, customer: customer} do
      conn = delete(conn, ~p"/api/customers/#{customer}")
      assert response(conn, 204)

      assert_error_sent 404, fn ->
        get(conn, ~p"/api/customers/#{customer}")
      end
    end
  end

  defp create_customer(%{user: user}) do
    customer = insert(:customer, organization: user.organization)
    %{customer: customer}
  end
end
