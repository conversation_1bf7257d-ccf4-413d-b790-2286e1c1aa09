defmodule RmsWeb.CustomerIntegrationsControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Integrations.Customers.CustomerEndpoint
  alias Rms.Repo

  setup do
    user = insert(:user)
    conn = authenticate_conn(build_conn(), user)
    {:ok, conn: conn, user: user}
  end

  describe "GET /api/integrations/customer" do
    test "lists all customer endpoints for the organization", %{conn: conn, user: user} do
      insert(:customer_endpoint, organization: user.organization)

      response =
        conn
        |> get(~p"/api/integrations/customer")
        |> json_response(200)

      assert length(response["data"]) == 1
    end
  end

  describe "POST /api/integrations/customer" do
    test "creates a customer endpoint", %{conn: conn, user: user} do
      attrs = %{
        endpoint: "https://example.com",
        unique_on: ["email"],
        headers: [["Authorization", "Bearer token"]],
        active: true
      }

      response =
        conn
        |> post(~p"/api/integrations/customer", %{customer_endpoint: attrs})
        |> json_response(201)

      assert response["data"]["endpoint"] == "https://example.com"
      assert Repo.get_by(CustomerEndpoint, organization_id: user.organization_id)
    end
  end

  describe "GET /api/integrations/customer/:id" do
    test "shows a specific customer endpoint", %{conn: conn, user: user} do
      customer_endpoint = insert(:customer_endpoint, organization: user.organization)

      response =
        conn
        |> get(~p"/api/integrations/customer/#{customer_endpoint.id}")
        |> json_response(200)

      assert response["data"]["id"] == customer_endpoint.id
    end

    test "returns 404 if customer endpoint not found", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/integrations/customer/999")
        |> json_response(404)
      end
    end
  end

  describe "PUT /api/integrations/customer/:id" do
    test "updates a specific customer endpoint", %{conn: conn, user: user} do
      customer_endpoint = insert(:customer_endpoint, organization: user.organization)

      update_attrs = %{endpoint: "https://updated.com"}

      response =
        conn
        |> put(~p"/api/integrations/customer/#{customer_endpoint.id}", %{
          customer_endpoint: update_attrs
        })
        |> json_response(200)

      assert response["data"]["endpoint"] == "https://updated.com"

      assert Repo.get_by(CustomerEndpoint, organization_id: user.organization_id).endpoint ==
               "https://updated.com"
    end
  end

  describe "DELETE /api/integrations/customer/:id" do
    test "deletes a specific customer endpoint", %{conn: conn, user: user} do
      customer_endpoint = insert(:customer_endpoint, organization: user.organization)

      response =
        conn
        |> delete(~p"/api/integrations/customer/#{customer_endpoint.id}")
        |> response(204)

      assert response == ""

      refute Repo.get_by(CustomerEndpoint,
               organization_id: user.organization_id,
               id: customer_endpoint.id
             )
    end
  end
end
