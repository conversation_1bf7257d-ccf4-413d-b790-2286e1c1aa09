defmodule RmsWeb.DiscountsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  alias Rms.Commerce.Discounts.AuthorizationRequest

  setup %{conn: conn} do
    user = insert(:user)
    organization = user.organization

    location = insert(:location, organization: organization)
    staff = insert(:staff, organization: organization)

    authorization_request =
      %AuthorizationRequest{
        organization_id: organization.id,
        location_id: location.id,
        staff_id: staff.id,
        status: "pending",
        code: "123456"
      }
      |> Rms.Repo.insert!()

    conn = authenticate_conn(conn, user)
    {:ok, conn: conn, user: user, authorization_request: authorization_request}
  end

  describe "validate_authorization/2" do
    test "should approve with valid code", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "123456"
        )

      assert conn.status == 200
      authorization_request = Rms.Repo.get!(AuthorizationRequest, authorization_request.id)
      assert authorization_request.status == "accepted"
    end

    test "should return error for invalid code format", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "abc"
        )

      assert conn.status == 401
      assert conn.resp_body == "invalid code"
    end

    test "should return error for wrong code", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "654321"
        )

      assert conn.status == 401
      assert conn.resp_body == "wrong code"
    end

    test "should return error for already approved request", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "accepted"})
        |> Rms.Repo.update!()

      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "123456"
        )

      assert conn.status == 400
      assert conn.resp_body == "request already approved"
    end

    test "should return error for canceled request", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "canceled"})
        |> Rms.Repo.update!()

      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "123456"
        )

      assert conn.status == 400
      assert conn.resp_body == "request already canceled"
    end

    test "should return error for rejected request", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      authorization_request =
        authorization_request
        |> AuthorizationRequest.changeset(%{status: "rejected"})
        |> Rms.Repo.update!()

      conn =
        post(conn, ~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "123456"
        )

      assert conn.status == 400
      assert conn.resp_body == "request already denied"
    end

    test "should return unauthorized for unauthorized access", %{
      conn: conn,
      authorization_request: authorization_request
    } do
      unauthorized_user = insert(:user)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> authenticate_conn(unauthorized_user)
        |> post(~p"/api/discounts/authorization/validate",
          id: authorization_request.id,
          code: "123456"
        )
      end
    end
  end
end
