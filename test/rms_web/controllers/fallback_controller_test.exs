defmodule RmsWeb.FallbackControllerTest do
  use ExUnit.Case, async: true
  import Plug.Test

  import ExUnit.CaptureLog

  alias Rms.Finance.Payment

  describe "call/2 without schema" do
    setup do
      changeset =
        {%{}, %{name: :string}}
        |> Ecto.Changeset.cast(%{}, [:name])
        |> Ecto.Changeset.validate_required([:name])

      conn =
        :get
        |> conn("/")
        |> Map.put(:params, %{"_format" => "json"})

      {:ok, conn: conn, changeset: changeset}
    end

    test "should log the error when the action does not return a valid response", %{
      conn: conn,
      changeset: changeset
    } do
      log =
        capture_log(fn ->
          RmsWeb.FallbackController.call(conn, {:error, changeset})
        end)

      assert log =~ "errors:"
      assert log =~ "name: [\"can't be blank\"]"
    end
  end

  describe "call/2 with schema" do
    setup do
      conn =
        :get
        |> conn("/")
        |> Map.put(:params, %{"_format" => "json"})

      payment = %Payment{
        id: "payment_123",
        status: "pending",
        method: "credit_card"
      }

      changeset =
        payment
        |> Ecto.Changeset.change(%{status: "invalid_status"})
        |> Ecto.Changeset.validate_inclusion(:status, ["pending", "settled"])
        |> Ecto.Changeset.validate_required([:amount])

      {:ok, conn: conn, changeset: changeset}
    end

    test "should log the error with schema information", %{
      conn: conn,
      changeset: changeset
    } do
      log =
        capture_log(fn ->
          RmsWeb.FallbackController.call(conn, {:error, changeset})
        end)

      assert log =~ "Elixir.Rms.Finance.Payment"
      assert log =~ "payment_123"
      assert log =~ "errors:"
      assert log =~ "status: [\"is invalid\"]"
      assert log =~ "amount: [\"can't be blank\"]"
    end
  end
end
