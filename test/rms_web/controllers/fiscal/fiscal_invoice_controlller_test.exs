defmodule RmsWeb.Fiscal.FiscalInvoiceControlllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Swoosh.TestAssertions

  use Oban.Pro.Testing, repo: Rms.Repo

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all invoice with the same organization", %{conn: conn, user: user} do
      create_invoice(user.organization)
      create_invoice(user.organization)

      other_org = insert(:organization)

      create_invoice(other_org)
      create_invoice(other_org)

      assert %{"invoices" => [_invoice1, _invoice2]} =
               conn
               |> get(~p"/api/fiscal/invoices")
               |> json_response(200)
    end

    test "lists all invoices with the same params", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      serie = insert(:invoice_serie, invoice_type: "nf", location: loc, organization: org)

      create_invoice(org, loc, serie)
      create_invoice(org, loc, serie)

      assert %{"invoices" => [_invoice1, _invoice2]} =
               conn
               |> get(~p"/api/fiscal/invoices", %{invoice_type: "nfc"})
               |> json_response(200)
    end

    test "lists all invoices with the same params and a format", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)
      serie1 = insert(:invoice_serie, invoice_type: "nfc", location: loc, organization: org)

      create_invoice(org, loc, serie1)
      create_invoice(org, loc, serie1)

      serie = insert(:invoice_serie, invoice_type: "nf", location: loc, organization: org)

      create_invoice(org, loc, serie)
      create_invoice(org, loc, serie)

      assert %{"invoices" => [_invoice1, _invoice2]} =
               conn
               |> get(~p"/api/fiscal/invoices", %{invoice_type: "nfc", format_type: "none"})
               |> json_response(200)
    end
  end

  describe "update/2" do
    test "correct update a fiscal invoice", %{conn: conn, user: user} do
      org = user.organization
      {:ok, invoice} = create_invoice_with_status(org, "authorized")

      update_params = %{status: "canceled", canceled_at: DateTime.utc_now()}

      assert conn
             |> put(~p"/api/fiscal/invoices/#{invoice.id}", update_params)
             |> json_response(200)

      updated_invoice = Rms.Fiscal.get_fiscal_invoice!(user.organization_id, invoice.id)
      assert updated_invoice.status == "canceled"
      assert not is_nil(updated_invoice.canceled_at)
    end
  end

  describe "show/2" do
    test "shows chosen invoice", %{conn: conn, user: user} do
      create_invoice(user.organization)
      create_invoice(user.organization)
      {:ok, invoice} = create_invoice(user.organization)

      conn = get(conn, ~p"/api/fiscal/invoices/#{invoice.id}")
      assert invoice = json_response(conn, 200)
      assert get_in(invoice, ["nfce", "nfc", "accessUrl"])
    end

    test "does not show invoices from another organization", %{conn: conn, user: user} do
      other_org = insert(:organization)

      create_invoice(user.organization)
      create_invoice(user.organization)
      {:ok, invoice} = create_invoice(other_org)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/fiscal/invoices/#{invoice.id}")
        |> json_response(404)
      end
    end
  end

  describe "import/2" do
    test "imports a fiscal invoice successfully", %{conn: conn, user: user} do
      org = user.organization
      order = insert(:order, organization: org, customer: nil, status: "paid")
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      payload = %{
        "order_id" => order.id,
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => "43210987654321098765432109876543210987654321",
        "status" => "pending",
        "authorized_at" => "2023-06-15T14:30:00Z",
        "canceled_at" => nil,
        "qr_code" =>
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==",
        "xml" =>
          "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240852380726000109650020000000541937210225\"><ide><cUF>35</cUF><cNF>93721022</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>2</serie><nNF>54</nNF><dhEmi>2024-08-09T16:49:42-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>5</cDV><tpAmb>1</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>52380726000109</CNPJ><xNome>SLYCE ARTIGOS ESPORTIVOS LTDA</xNome><enderEmit><xLgr>Av. Nove de Julho</xLgr><nro>5966</nro><xBairro>Jardim Paulista</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>01406902</CEP></enderEmit><IE>125815764111</IE><CRT>1</CRT></emit><dest><CPF>92486614753</CPF><xNome>Gulherme Horn</xNome><indIEDest>9</indIEDest></dest><det nItem=\"1\"><prod><cProd>SLBVNV0001</cProd><cEAN>SEM GTIN</cEAN><xProd>Boné Vintage Navy / Vintage Beige</xProd><NCM>65050012</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>170.10</vUnCom><vProd>170.10</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>170.10</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>102</CSOSN></ICMSSN102></ICMS><PIS><PISOutr><CST>49</CST><vBC>0</vBC><pPIS>0.00</pPIS><vPIS>0.00</vPIS></PISOutr></PIS><COFINS><COFINSOutr><CST>49</CST><vBC>0</vBC><pCOFINS>0.00</pCOFINS><vCOFINS>0.00</vCOFINS></COFINSOutr></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>170.10</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>0.00</vPIS><vCOFINS>0.00</vCOFINS><vOutro>0</vOutro><vNF>170.10</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>170.10</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.nfce.fazenda.sp.gov.br/qrcode?p=35240852380726000109650020000000541937210225|2|1|1|8c4f220c8c7092ea20613618647f1ba93fd8133b]]></qrCode><urlChave>https://www.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240852380726000109650020000000541937210225\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>tY7DsP2yx9kaGlzNrfsk35UhPoE=</DigestValue></Reference></SignedInfo><SignatureValue>aDRjLcCXr31N0sWluEdMv22ZH1/Qi5TfnR+bN5jk9KWgLYd/Ua8bKIYe/FFfnyrYjf5BXFvtA/fEBFSJLGJl1yZOz7HyfV3dWoi+uamn7V05SQoYZeT8RxAKlhhBxGpBLlX2Nyam0Cbi8aLBs9CDA9KJOp5Zkyf8/7VR9zdJ62WlvFhcJ+A01ap4bANKf7lkq+i1w7MlhPdD4WY/3apY9BGH6f+nunthay/2Sgmen0/Lz5fAab3QWgrCimyhT+/TbVamWrEEUMC/kuN/mvofsW6g2VtDU1jmG1Pgaj7pRO7xDKRSduByMth1xJpi6tDjQiFIV6xalP+OfZi9jek4NQ==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>1</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240852380726000109650020000000541937210225</chNFe><dhRecbto>2024-08-09T16:49:42-03:00</dhRecbto><nProt>135240753687657</nProt><digVal>tY7DsP2yx9kaGlzNrfsk35UhPoE=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>",
        "danfe" => nil
      }

      conn = post(conn, ~p"/api/fiscal/invoices/import", payload)

      assert %{"id" => id} = json_response(conn, 201)
      fiscal_invoice = Rms.Fiscal.get_fiscal_invoice!(user.organization_id, id)
      assert fiscal_invoice.external_id == "EXT-98765"
    end

    test "sends email when importing fiscal invoice successfully", %{conn: conn, user: user} do
      org = user.organization

      order =
        insert(:order,
          organization: org,
          customer: build(:customer, organization: org),
          status: "paid"
        )

      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      payload = %{
        "order_id" => order.id,
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => "43210987654321098765432109876543210987654321",
        "status" => "pending",
        "authorized_at" => "2023-06-15T14:30:00Z",
        "canceled_at" => nil,
        "qr_code" =>
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==",
        "xml" =>
          "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240852380726000109650020000000541937210225\"><ide><cUF>35</cUF><cNF>93721022</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>2</serie><nNF>54</nNF><dhEmi>2024-08-09T16:49:42-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>5</cDV><tpAmb>1</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>52380726000109</CNPJ><xNome>SLYCE ARTIGOS ESPORTIVOS LTDA</xNome><enderEmit><xLgr>Av. Nove de Julho</xLgr><nro>5966</nro><xBairro>Jardim Paulista</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>01406902</CEP></enderEmit><IE>125815764111</IE><CRT>1</CRT></emit><dest><CPF>92486614753</CPF><xNome>Gulherme Horn</xNome><indIEDest>9</indIEDest></dest><det nItem=\"1\"><prod><cProd>SLBVNV0001</cProd><cEAN>SEM GTIN</cEAN><xProd>Boné Vintage Navy / Vintage Beige</xProd><NCM>65050012</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>170.10</vUnCom><vProd>170.10</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>170.10</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>102</CSOSN></ICMSSN102></ICMS><PIS><PISOutr><CST>49</CST><vBC>0</vBC><pPIS>0.00</pPIS><vPIS>0.00</vPIS></PISOutr></PIS><COFINS><COFINSOutr><CST>49</CST><vBC>0</vBC><pCOFINS>0.00</pCOFINS><vCOFINS>0.00</vCOFINS></COFINSOutr></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>170.10</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>0.00</vPIS><vCOFINS>0.00</vCOFINS><vOutro>0</vOutro><vNF>170.10</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>170.10</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.nfce.fazenda.sp.gov.br/qrcode?p=35240852380726000109650020000000541937210225|2|1|1|8c4f220c8c7092ea20613618647f1ba93fd8133b]]></qrCode><urlChave>https://www.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240852380726000109650020000000541937210225\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>tY7DsP2yx9kaGlzNrfsk35UhPoE=</DigestValue></Reference></SignedInfo><SignatureValue>aDRjLcCXr31N0sWluEdMv22ZH1/Qi5TfnR+bN5jk9KWgLYd/Ua8bKIYe/FFfnyrYjf5BXFvtA/fEBFSJLGJl1yZOz7HyfV3dWoi+uamn7V05SQoYZeT8RxAKlhhBxGpBLlX2Nyam0Cbi8aLBs9CDA9KJOp5Zkyf8/7VR9zdJ62WlvFhcJ+A01ap4bANKf7lkq+i1w7MlhPdD4WY/3apY9BGH6f+nunthay/2Sgmen0/Lz5fAab3QWgrCimyhT+/TbVamWrEEUMC/kuN/mvofsW6g2VtDU1jmG1Pgaj7pRO7xDKRSduByMth1xJpi6tDjQiFIV6xalP+OfZi9jek4NQ==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>1</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240852380726000109650020000000541937210225</chNFe><dhRecbto>2024-08-09T16:49:42-03:00</dhRecbto><nProt>135240753687657</nProt><digVal>tY7DsP2yx9kaGlzNrfsk35UhPoE=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>",
        "danfe" => nil
      }

      conn = post(conn, ~p"/api/fiscal/invoices/import", payload)

      assert %{"id" => id} = json_response(conn, 201)
      fiscal_invoice = Rms.Fiscal.get_fiscal_invoice!(user.organization_id, id)
      assert fiscal_invoice.external_id == "EXT-98765"

      assert_email_sent(fn email ->
        assert email.subject =~ "NFC-e"
        assert [{_, customer_email}] = email.to
        assert customer_email == order.customer.email
      end)
    end

    test "imported fiscal invoice appear in list fiscal invoice endpoint", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      order = insert(:order, organization: org, status: "paid")
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      payload = %{
        "order_id" => order.id,
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => "43210987654321098765432109876543210987654321",
        "status" => "pending",
        "authorized_at" => "2023-06-15T14:30:00Z",
        "canceled_at" => nil,
        "qr_code" =>
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==",
        "xml" =>
          "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240852380726000109650020000000541937210225\"><ide><cUF>35</cUF><cNF>93721022</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>2</serie><nNF>54</nNF><dhEmi>2024-08-09T16:49:42-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>5</cDV><tpAmb>1</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>52380726000109</CNPJ><xNome>SLYCE ARTIGOS ESPORTIVOS LTDA</xNome><enderEmit><xLgr>Av. Nove de Julho</xLgr><nro>5966</nro><xBairro>Jardim Paulista</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>01406902</CEP></enderEmit><IE>125815764111</IE><CRT>1</CRT></emit><dest><CPF>92486614753</CPF><xNome>Gulherme Horn</xNome><indIEDest>9</indIEDest></dest><det nItem=\"1\"><prod><cProd>SLBVNV0001</cProd><cEAN>SEM GTIN</cEAN><xProd>Boné Vintage Navy / Vintage Beige</xProd><NCM>65050012</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>170.10</vUnCom><vProd>170.10</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>170.10</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>102</CSOSN></ICMSSN102></ICMS><PIS><PISOutr><CST>49</CST><vBC>0</vBC><pPIS>0.00</pPIS><vPIS>0.00</vPIS></PISOutr></PIS><COFINS><COFINSOutr><CST>49</CST><vBC>0</vBC><pCOFINS>0.00</pCOFINS><vCOFINS>0.00</vCOFINS></COFINSOutr></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>170.10</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>0.00</vPIS><vCOFINS>0.00</vCOFINS><vOutro>0</vOutro><vNF>170.10</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>170.10</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.nfce.fazenda.sp.gov.br/qrcode?p=35240852380726000109650020000000541937210225|2|1|1|8c4f220c8c7092ea20613618647f1ba93fd8133b]]></qrCode><urlChave>https://www.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240852380726000109650020000000541937210225\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>tY7DsP2yx9kaGlzNrfsk35UhPoE=</DigestValue></Reference></SignedInfo><SignatureValue>aDRjLcCXr31N0sWluEdMv22ZH1/Qi5TfnR+bN5jk9KWgLYd/Ua8bKIYe/FFfnyrYjf5BXFvtA/fEBFSJLGJl1yZOz7HyfV3dWoi+uamn7V05SQoYZeT8RxAKlhhBxGpBLlX2Nyam0Cbi8aLBs9CDA9KJOp5Zkyf8/7VR9zdJ62WlvFhcJ+A01ap4bANKf7lkq+i1w7MlhPdD4WY/3apY9BGH6f+nunthay/2Sgmen0/Lz5fAab3QWgrCimyhT+/TbVamWrEEUMC/kuN/mvofsW6g2VtDU1jmG1Pgaj7pRO7xDKRSduByMth1xJpi6tDjQiFIV6xalP+OfZi9jek4NQ==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240543253315000234655000000000121995347039</chNFe><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto><nProt>135240000955186</nProt><digVal>ysevFCxzIA4bN3b8elOg/nu65T8=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>",
        "danfe" => "https://example.com/danfe/12345.pdf"
      }

      conn = post(conn, ~p"/api/fiscal/invoices/import", payload)
      assert %{"id" => id} = json_response(conn, 201)

      conn = get(conn, ~p"/api/fiscal/invoices?transaction_id=#{transaction.id}&invoice_type=nfc")
      assert %{"invoices" => [invoice]} = json_response(conn, 200)
      assert invoice["id"] == id

      assert invoice["nfce"]["qrcode"] ==
               "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg=="
    end

    test "returns error when importing with invalid data", %{conn: conn, user: user} do
      org = user.organization
      order = insert(:order, organization: org, status: "paid")
      transaction = insert(:transaction, order: order, organization: org)
      insert(:payment, status: "settled", transaction: transaction, organization: org)

      payload = %{
        "order_id" => order.id,
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => nil,
        "status" => "invalid_status",
        "authorized_at" => "invalid_date",
        "canceled_at" => nil,
        "qr_code" => nil,
        "xml" => nil,
        "danfe" => nil
      }

      conn = post(conn, ~p"/api/fiscal/invoices/import", payload)

      assert %{"errors" => errors} = json_response(conn, 422)
      assert errors["df_key"]
      assert errors["status"]
      assert errors["authorized_at"]
      assert errors["xml"]
    end

    test "returns error when trying to import for an order from another org", %{conn: conn} do
      other_org = insert(:organization)
      order = insert(:order, organization: other_org)

      payload = %{
        "order_id" => order.id,
        "operation_type" => "sale",
        "service" => "external",
        "invoice_number" => 12_345,
        "external_id" => "EXT-98765",
        "df_key" => "43210987654321098765432109876543210987654321",
        "status" => "pending",
        "authorized_at" => "2023-06-15T14:30:00Z",
        "canceled_at" => nil,
        "qr_code" =>
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==",
        "xml" =>
          "<nfe><infNFe Id=\"NFe43210987654321098765432109876543210987654321\"></infNFe></nfe>",
        "danfe" => "https://example.com/danfe/12345.pdf"
      }

      assert_raise Ecto.NoResultsError, fn ->
        post(conn, ~p"/api/fiscal/invoices/import", payload)
      end
    end
  end

  describe "reprocess/2" do
    test "queue the reprocessing worker", %{conn: conn, user: user} do
      {:ok, invoice} = create_invoice_with_status(user.organization, "pending")

      assert %{"success" => true} =
               conn
               |> post(~p"/api/fiscal/invoices/reprocess/#{invoice.id}")
               |> json_response(201)

      assert_enqueued(
        worker: Rms.Workers.InvoiceIssuerWorker,
        args: %{"fiscal_invoice_id" => invoice.id}
      )
    end

    test " does not queue the reprocessing worker", %{conn: conn, user: user} do
      {:ok, invoice} = create_invoice_with_status(user.organization, "authorized")

      conn
      |> post(~p"/api/fiscal/invoices/reprocess/#{invoice.id}")
      |> json_response(422)

      refute_enqueued(
        worker: Rms.Workers.InvoiceIssuerWorker,
        args: %{"fiscal_invoice_id" => invoice.id}
      )
    end
  end

  describe "send_email/2" do
    test "sends an email successfully", %{conn: conn, user: user} do
      {:ok, invoice} = create_invoice(user.organization)
      email = "<EMAIL>"

      conn = post(conn, ~p"/api/fiscal/invoices/#{invoice.id}/email", %{email: email})

      assert json_response(conn, 200) == %{"message" => "email sent successfully"}

      assert_email_sent(fn sent_email ->
        assert [{_, customer_email}] = sent_email.to
        assert customer_email == email
        assert [%{data: xml}] = sent_email.attachments
        assert xml == invoice.xml
      end)
    end

    test "returns error when invoice not found", %{conn: conn} do
      email = "<EMAIL>"

      assert_error_sent 404, fn ->
        post(conn, ~p"/api/fiscal/invoices/-1/email", %{email: email})
      end
    end
  end

  describe "import_vinco_fiscal_invoice/2" do
    setup do
      Mox.stub_with(VincoClientMock, VincoClientStub)
      :ok
    end

    test "successfully imports a fiscal invoice from Vinco", %{conn: conn, user: user} do
      Mox.expect(VincoClientMock, :get_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:ok,
         %{
           "ChaveDFe" => "35240443253315000234653000000003071110814728",
           "CodStatus" => 100,
           "CodStatusLote" => 104,
           "DFeProtocolo" => "135240000777778",
           "IdAssincrono" => 1_258_790_202,
           "Motivo" => "Autorizado o uso da NF-e",
           "MotivoLote" => "Lote processado",
           "QrCode" => "https://example.com/qrcode",
           "UrlDanfe" => "https://example.com/danfe",
           "XmlDFe" =>
             "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240543253315000234655000000000121995347039\"><ide><cUF>35</cUF><cNF>99534703</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>500</serie><nNF>12</nNF><dhEmi>2024-05-02T17:49:09-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>9</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>43253315000234</CNPJ><xNome>name</xNome><enderEmit><xLgr>Rua Rua</xLgr><nro>1537</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05404014</CEP></enderEmit><IE>138557519112</IE><CRT>1</CRT></emit><dest><CPF>85414167478</CPF><xNome>NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xNome><indIEDest>9</indIEDest><email><EMAIL></email></dest><det nItem=\"1\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>176.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>1.14</vPIS><vCOFINS>5.28</vCOFINS><vOutro>0</vOutro><vNF>176.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><Signature></Signature></NFe></nfeProc>",
           "XmlRecebimento" =>
             "<?xml version=\"1.0\" encoding=\"utf-8\"?><retEnviNFe xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><tpAmb>2</tpAmb><verAplic>SP_NFE_PL009_V4</verAplic><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo><cUF>35</cUF><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto></retEnviNFe>",
           "XmlRecibo" =>
             "<?xml version=\"1.0\" encoding=\"utf-8\"?><retConsReciNFe xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><tpAmb>2</tpAmb><verAplic>SP_NFE_PL009_V4</verAplic><nRec>123456789012345</nRec><cStat>104</cStat><xMotivo>Lote processado</xMotivo><cUF>35</cUF><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto></retConsReciNFe>"
         }}
      end)

      org = user.organization
      loc = insert(:location, organization: org)
      insert(:vinco_credential, location: loc, organization: org)
      {:ok, invoice} = create_invoice_with_status(org, "pending", loc: loc)
      df_key = "35240443253315000234653000000003071110814728"

      response =
        conn
        |> post(~p"/api/fiscal/invoices/vinco_import/#{invoice.id}", %{df_key: df_key})
        |> json_response(200)

      assert response["id"] == invoice.id
      assert response["status"] == "authorized"
      assert response["df_key"] == df_key
    end

    test "returns error when invoice is not found", %{conn: conn} do
      df_key = "35240443253315000234653000000003071110814728"

      assert_error_sent 404, fn ->
        post(conn, ~p"/api/fiscal/invoices/vinco_import/-1", %{df_key: df_key})
      end
    end

    test "returns error when invoice belongs to another organization", %{conn: conn} do
      other_org = insert(:organization)
      {:ok, invoice} = create_invoice_with_status(other_org, "pending")
      df_key = "35240443253315000234653000000003071110814728"

      assert_error_sent 404, fn ->
        post(conn, ~p"/api/fiscal/invoices/vinco_import/#{invoice.id}", %{df_key: df_key})
      end
    end

    test "returns error when Vinco import fails", %{conn: conn, user: user} do
      Mox.expect(VincoClientMock, :get_fiscal_invoice, 1, fn _, _, _, _, _ ->
        {:error, %{reason: "Some error", status: 500}}
      end)

      org = user.organization
      loc = insert(:location, organization: org)
      insert(:vinco_credential, location: loc, organization: org)
      {:ok, invoice} = create_invoice_with_status(org, "pending", loc: loc)
      df_key = "35240443253315000234653000000003071110814728"

      response =
        conn
        |> post(~p"/api/fiscal/invoices/vinco_import/#{invoice.id}", %{df_key: df_key})
        |> json_response(400)

      assert response["errors"]["detail"] == "Bad Request"
    end
  end

  describe "import_fulfillment_invoice/2" do
    setup %{user: user} do
      org = user.organization

      order =
        insert(:order, organization: org, customer: build(:customer, organization: org))

      fulfillment = insert(:fulfillment, order: order, organization: org)

      valid_xml = """
        <?xml version="1.0" encoding="UTF-8"?>
        <nfeProc versao="4.00" xmlns="http://www.portalfiscal.inf.br/nfe">
          <NFe>
      <infNFe Id="NFe35210112345678000191550010000000011234567890" versao="4.00">
        <ide>
          <cUF>35</cUF>
          <cNF>12345678</cNF>
          <natOp>VENDA</natOp>
          <mod>55</mod>
          <serie>1</serie>
          <nNF>1</nNF>
          <dhEmi>2023-10-26T10:00:00-03:00</dhEmi>
          <tpNF>1</tpNF>
          <idDest>1</idDest>
          <cMunFG>3550308</cMunFG>
          <tpImp>1</tpImp>
          <tpEmis>1</tpEmis>
          <cDV>0</cDV>
          <tpAmb>1</tpAmb>
          <finNFe>1</finNFe>
          <indFinal>1</indFinal>
          <indPres>1</indPres>
          <procEmi>0</procEmi>
          <verProc>AppExemplo 1.0</verProc>
        </ide>
        <emit>
          <CNPJ>12345678000191</CNPJ>
          <xNome>Empresa Exemplo</xNome>
          <enderEmit>
            <xLgr>Rua Exemplo</xLgr>
            <nro>123</nro>
            <xBairro>Centro</xBairro>
            <cMun>3550308</cMun>
            <xMun>Sao Paulo</xMun>
            <UF>SP</UF>
            <CEP>01000000</CEP>
            <cPais>1058</cPais>
            <xPais>Brasil</xPais>
          </enderEmit>
          <IE>111222333444</IE>
          <CRT>1</CRT>
        </emit>
        <dest>
          <CPF>11122233344</CPF>
          <xNome>Cliente Exemplo</xNome>
          <enderDest>
            <xLgr>Av Exemplo</xLgr>
            <nro>456</nro>
            <xBairro>Bairro Exemplo</xBairro>
            <cMun>3550308</cMun>
            <xMun>Sao Paulo</xMun>
            <UF>SP</UF>
            <CEP>02000000</CEP>
            <cPais>1058</cPais>
            <xPais>Brasil</xPais>
          </enderDest>
          <indIEDest>9</indIEDest>
        </dest>
        <det nItem="1">
          <prod>
            <cProd>PROD001</cProd>
            <cEAN></cEAN>
            <xProd>Produto Exemplo</xProd>
            <NCM>99999999</NCM>
            <CFOP>5102</CFOP>
            <uCom>UN</uCom>
            <qCom>1.0000</qCom>
            <vUnCom>100.00</vUnCom>
            <vProd>100.00</vProd>
            <cEANTrib></cEANTrib>
            <uTrib>UN</uTrib>
            <qTrib>1.0000</qTrib>
            <vUnTrib>100.00</vUnTrib>
            <indTot>1</indTot>
          </prod>
          <imposto>
            <ICMS>
              <ICMS00>
                <orig>0</orig>
                <CST>00</CST>
                <modBC>3</modBC>
                <vBC>100.00</vBC>
                <pICMS>18.00</pICMS>
                <vICMS>18.00</vICMS>
              </ICMS00>
            </ICMS>
            <PIS>
              <PISAliq>
                <CST>01</CST>
                <vBC>100.00</vBC>
                <pPIS>1.65</pPIS>
                <vPIS>1.65</vPIS>
              </PISAliq>
            </PIS>
            <COFINS>
              <COFINSAliq>
                <CST>01</CST>
                <vBC>100.00</vBC>
                <pCOFINS>7.60</pCOFINS>
                <vCOFINS>7.60</vCOFINSAliq>
            </COFINS>
          </imposto>
        </det>
        <total>
          <ICMSTot>
            <vBC>100.00</vBC>
            <vICMS>18.00</vICMS>
            <vICMSDeson>0.00</vICMSDeson>
            <vFCP>0.00</vFCP>
            <vBCST>0.00</vBCST>
            <vST>0.00</vST>
            <vFCPST>0.00</vFCPST>
            <vFCPSTRet>0.00</vFCPSTRet>
            <vProd>100.00</vProd>
            <vFrete>0.00</vFrete>
            <vSeg>0.00</vSeg>
            <vDesc>0.00</vDesc>
            <vII>0.00</vII>
            <vIPI>0.00</vIPI>
            <vIPIDevol>0.00</vIPIDevol>
            <vPIS>1.65</vPIS>
            <vCOFINS>7.60</vCOFINS>
            <vOutro>0.00</vOutro>
            <vNF>100.00</vNF>
          </ICMSTot>
        </total>
        <transp>
          <modFrete>9</modFrete>
        </transp>
        <pag>
          <detPag>
            <tPag>01</tPag>
            <vPag>100.00</vPag>
          </detPag>
        </pag>
      </infNFe>
      <protNFe versao="4.00">
        <infProt>
          <tpAmb>1</tpAmb>
          <verAplic>SP_NFE_PL_009_V4</verAplic>
          <chNFe>35210112345678000191550010000000011234567890</chNFe>
          <dhRecbto>2023-10-26T10:01:00-03:00</dhRecbto>
          <nProt>135210000000001</nProt>
          <digVal>XYZ==</digVal>
          <cStat>100</cStat>
          <xMotivo>Autorizado o uso da NF-e</xMotivo>
        </infProt>
          </protNFe>
        </NFe>
      </nfeProc>
      """

      xml_missing_key =
        "<?xml version=\"1.0\"?><NFe><infNFe><ide><nNF>1</nNF></ide><infProt><dhRecbto>2023-10-26T10:01:00-03:00</dhRecbto></infProt></infNFe></NFe>"

      xml_invalid_date =
        "<?xml version=\"1.0\"?><NFe><infNFe><ide><nNF>1</nNF></ide><infProt><chNFe>KEY</chNFe><dhRecbto>INVALID-DATE</dhRecbto></infProt></infNFe></NFe>"

      {:ok,
       fulfillment: fulfillment,
       valid_xml: valid_xml,
       xml_missing_key: xml_missing_key,
       xml_invalid_date: xml_invalid_date}
    end

    test "imports a fiscal invoice from XML successfully", %{
      conn: conn,
      user: user,
      fulfillment: fulfillment,
      valid_xml: valid_xml
    } do
      payload = %{"fiscal_invoice_xml" => valid_xml}

      response =
        conn
        |> post(~p"/api/fiscal/invoices/fulfillments/#{fulfillment.id}/import", payload)
        |> json_response(201)

      assert %{"id" => invoice_id} = response
      created_invoice = Rms.Fiscal.get_fiscal_invoice!(user.organization_id, invoice_id)

      assert created_invoice.fulfillment_id == fulfillment.id
      assert created_invoice.xml == valid_xml
      assert created_invoice.df_key == "35210112345678000191550010000000011234567890"
      assert created_invoice.invoice_number == 1
      assert created_invoice.status == "authorized"
      assert created_invoice.service == "external"
      assert created_invoice.operation_type == "sale"
      assert created_invoice.metadata["import_source"] == "manual_xml"
    end
  end

  defp create_invoice(org) do
    loc = insert(:location, organization: org)
    serie = insert(:invoice_serie, location: loc, organization: org)

    order = insert(:order, organization: org)
    transaction = insert(:transaction, order: order, organization: org)
    payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

    transaction_customer =
      insert(:transaction_customer, transaction: transaction, organization: org)

    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    fulfillment = insert(:fulfillment, order: order, organization: org)

    line_item =
      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

    attrs = %{
      operation_type: "sale",
      serie_id: serie.id,
      customer_id: transaction_customer.id,
      invoice_payments: [%{payment_id: payment.id}],
      invoice_items: [%{line_item_id: line_item.id}],
      xml:
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240543253315000234655000000000121995347039\"><ide><cUF>35</cUF><cNF>99534703</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>500</serie><nNF>12</nNF><dhEmi>2024-05-02T17:49:09-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>9</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>43253315000234</CNPJ><xNome>name</xNome><enderEmit><xLgr>Rua Rua</xLgr><nro>1537</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05404014</CEP></enderEmit><IE>138557519112</IE><CRT>1</CRT></emit><dest><CPF>85414167478</CPF><xNome>NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xNome><indIEDest>9</indIEDest><email><EMAIL></email></dest><det nItem=\"1\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><det nItem=\"2\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>352.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>2.28</vPIS><vCOFINS>10.56</vCOFINS><vOutro>0</vOutro><vNF>352.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag><detPag><tPag>04</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240543253315000234655000000000121995347039|2|2|1|fd17972250604b4ba220706ee3f731bed7315bff]]></qrCode><urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240543253315000234655000000000121995347039\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>ysevFCxzIA4bN3b8elOg/nu65T8=</DigestValue></Reference></SignedInfo><SignatureValue>Grb8o9gadpRolu6Lmh7pFLfwUuR12dzCVK6w0xDr9AhVP5MQHHVq5nr0azZ7uQiCoy2lXDQ/q/DsuesDzQTErqMdLTtN+pupjXtMSuyfxOLrtqHo4oIT8li+6FJgxYR2viohnE3f59zTHfpRFLEjqqFMZ4fYZK3RwL1e9Gv947eXJ+xO4Fe/2EEVc8aloRhEBdx9TjYtNjgwaw0apNMkLmVUdwxWxNG1T+KDYtL5zj/QB1vDCXpvkIAu4Tf8Q0tCdW0oIjpKQcGgap6Y+qvM9/NwpukxxGdX4KGujzf6OsAbdjSXO5/QqKZWWFjAbTGY/1YJKL+IPdF/B1m6AEV2Qw==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240543253315000234655000000000121995347039</chNFe><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto><nProt>135240000955186</nProt><digVal>ysevFCxzIA4bN3b8elOg/nu65T8=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>"
    }

    Rms.Fiscal.create_fiscal_invoice(org.id, attrs)
  end

  defp create_invoice_with_status(org, status, opts \\ []) do
    loc = Keyword.get_lazy(opts, :loc, fn -> insert(:location, organization: org) end)
    serie = insert(:invoice_serie, location: loc, organization: org)

    order = insert(:order, organization: org)
    transaction = insert(:transaction, order: order, organization: org)
    payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

    transaction_customer =
      insert(:transaction_customer, transaction: transaction, organization: org)

    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    fulfillment = insert(:fulfillment, order: order, organization: org)

    line_item =
      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

    attrs = %{
      operation_type: "sale",
      service: "vinco",
      serie_id: serie.id,
      status: status,
      customer_id: transaction_customer.id,
      invoice_payments: [%{payment_id: payment.id}],
      invoice_items: [%{line_item_id: line_item.id}],
      xml:
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240543253315000234655000000000121995347039\"><ide><cUF>35</cUF><cNF>99534703</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>500</serie><nNF>12</nNF><dhEmi>2024-05-02T17:49:09-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>9</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>43253315000234</CNPJ><xNome>name</xNome><enderEmit><xLgr>Rua Rua</xLgr><nro>1537</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05404014</CEP></enderEmit><IE>138557519112</IE><CRT>1</CRT></emit><dest><CPF>85414167478</CPF><xNome>NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xNome><indIEDest>9</indIEDest><email><EMAIL></email></dest><det nItem=\"1\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><det nItem=\"2\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>352.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>2.28</vPIS><vCOFINS>10.56</vCOFINS><vOutro>0</vOutro><vNF>352.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag><detPag><tPag>04</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240543253315000234655000000000121995347039|2|2|1|fd17972250604b4ba220706ee3f731bed7315bff]]></qrCode><urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240543253315000234655000000000121995347039\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>ysevFCxzIA4bN3b8elOg/nu65T8=</DigestValue></Reference></SignedInfo><SignatureValue>Grb8o9gadpRolu6Lmh7pFLfwUuR12dzCVK6w0xDr9AhVP5MQHHVq5nr0azZ7uQiCoy2lXDQ/q/DsuesDzQTErqMdLTtN+pupjXtMSuyfxOLrtqHo4oIT8li+6FJgxYR2viohnE3f59zTHfpRFLEjqqFMZ4fYZK3RwL1e9Gv947eXJ+xO4Fe/2EEVc8aloRhEBdx9TjYtNjgwaw0apNMkLmVUdwxWxNG1T+KDYtL5zj/QB1vDCXpvkIAu4Tf8Q0tCdW0oIjpKQcGgap6Y+qvM9/NwpukxxGdX4KGujzf6OsAbdjSXO5/QqKZWWFjAbTGY/1YJKL+IPdF/B1m6AEV2Qw==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240543253315000234655000000000121995347039</chNFe><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto><nProt>135240000955186</nProt><digVal>ysevFCxzIA4bN3b8elOg/nu65T8=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>"
    }

    Rms.Fiscal.create_fiscal_invoice(org.id, attrs)
  end

  defp create_invoice(org, loc, serie) do
    order = insert(:order, organization: org)
    transaction = insert(:transaction, order: order, organization: org)
    payment = insert(:payment, status: "settled", transaction: transaction, organization: org)

    transaction_customer =
      insert(:transaction_customer, transaction: transaction, organization: org)

    product = insert(:product, organization: org)
    pv = insert(:product_variant, product: product, organization: org)

    fulfillment = insert(:fulfillment, order: order, organization: org)

    line_item =
      insert(:line_item,
        fulfillment: fulfillment,
        organization: org,
        product_variant: pv,
        location: loc
      )

    attrs = %{
      operation_type: "sale",
      serie_id: serie.id,
      customer_id: transaction_customer.id,
      invoice_payments: [%{payment_id: payment.id}],
      invoice_items: [%{line_item_id: line_item.id}],
      xml:
        "<?xml version=\"1.0\" encoding=\"utf-8\"?><nfeProc xmlns=\"http://www.portalfiscal.inf.br/nfe\" versao=\"4.00\"><NFe xmlns=\"http://www.portalfiscal.inf.br/nfe\"><infNFe versao=\"4.00\" Id=\"NFe35240543253315000234655000000000121995347039\"><ide><cUF>35</cUF><cNF>99534703</cNF><natOp>Operacao interna</natOp><mod>65</mod><serie>500</serie><nNF>12</nNF><dhEmi>2024-05-02T17:49:09-03:00</dhEmi><tpNF>1</tpNF><idDest>1</idDest><cMunFG>3550308</cMunFG><tpImp>4</tpImp><tpEmis>1</tpEmis><cDV>9</cDV><tpAmb>2</tpAmb><finNFe>1</finNFe><indFinal>1</indFinal><indPres>1</indPres><procEmi>0</procEmi><verProc>iContNFe 1.22</verProc></ide><emit><CNPJ>43253315000234</CNPJ><xNome>name</xNome><enderEmit><xLgr>Rua Rua</xLgr><nro>1537</nro><xBairro>Pinheiros</xBairro><cMun>3550308</cMun><xMun>Sao Paulo</xMun><UF>SP</UF><CEP>05404014</CEP></enderEmit><IE>138557519112</IE><CRT>1</CRT></emit><dest><CPF>85414167478</CPF><xNome>NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xNome><indIEDest>9</indIEDest><email><EMAIL></email></dest><det nItem=\"1\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><det nItem=\"2\"><prod><cProd>008</cProd><cEAN>SEM GTIN</cEAN><xProd>NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL</xProd><NCM>64029990</NCM><CFOP>5102</CFOP><uCom>UN</uCom><qCom>1</qCom><vUnCom>176.00</vUnCom><vProd>176.00</vProd><cEANTrib>SEM GTIN</cEANTrib><uTrib>UN</uTrib><qTrib>1</qTrib><vUnTrib>176.00</vUnTrib><indTot>1</indTot></prod><imposto><ICMS><ICMSSN102><orig>0</orig><CSOSN>400</CSOSN></ICMSSN102></ICMS><PIS><PISAliq><CST>01</CST><vBC>176.00</vBC><pPIS>0.65</pPIS><vPIS>1.14</vPIS></PISAliq></PIS><COFINS><COFINSAliq><CST>01</CST><vBC>176.00</vBC><pCOFINS>3.00</pCOFINS><vCOFINS>5.28</vCOFINS></COFINSAliq></COFINS></imposto></det><total><ICMSTot><vBC>0.00</vBC><vICMS>0.00</vICMS><vICMSDeson>0</vICMSDeson><vFCP>0.00</vFCP><vBCST>0</vBCST><vST>0</vST><vFCPST>0</vFCPST><vFCPSTRet>0</vFCPSTRet><vProd>352.00</vProd><vFrete>0</vFrete><vSeg>0</vSeg><vDesc>0</vDesc><vII>0</vII><vIPI>0</vIPI><vIPIDevol>0</vIPIDevol><vPIS>2.28</vPIS><vCOFINS>10.56</vCOFINS><vOutro>0</vOutro><vNF>352.00</vNF></ICMSTot></total><transp><modFrete>9</modFrete><vol><qVol>1</qVol></vol></transp><pag><detPag><tPag>03</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag><detPag><tPag>04</tPag><vPag>176.00</vPag><card><tpIntegra>2</tpIntegra></card></detPag></pag></infNFe><infNFeSupl><qrCode><![CDATA[https://www.homologacao.nfce.fazenda.sp.gov.br/qrcode?p=35240543253315000234655000000000121995347039|2|2|1|fd17972250604b4ba220706ee3f731bed7315bff]]></qrCode><urlChave>https://www.homologacao.nfce.fazenda.sp.gov.br/consulta</urlChave></infNFeSupl><Signature xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#NFe35240543253315000234655000000000121995347039\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-********\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>ysevFCxzIA4bN3b8elOg/nu65T8=</DigestValue></Reference></SignedInfo><SignatureValue>Grb8o9gadpRolu6Lmh7pFLfwUuR12dzCVK6w0xDr9AhVP5MQHHVq5nr0azZ7uQiCoy2lXDQ/q/DsuesDzQTErqMdLTtN+pupjXtMSuyfxOLrtqHo4oIT8li+6FJgxYR2viohnE3f59zTHfpRFLEjqqFMZ4fYZK3RwL1e9Gv947eXJ+xO4Fe/2EEVc8aloRhEBdx9TjYtNjgwaw0apNMkLmVUdwxWxNG1T+KDYtL5zj/QB1vDCXpvkIAu4Tf8Q0tCdW0oIjpKQcGgap6Y+qvM9/NwpukxxGdX4KGujzf6OsAbdjSXO5/QqKZWWFjAbTGY/1YJKL+IPdF/B1m6AEV2Qw==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></NFe><protNFe versao=\"4.00\"><infProt><tpAmb>2</tpAmb><verAplic>SP_NFCE_PL_009_V400</verAplic><chNFe>35240543253315000234655000000000121995347039</chNFe><dhRecbto>2024-05-02T17:49:10-03:00</dhRecbto><nProt>135240000955186</nProt><digVal>ysevFCxzIA4bN3b8elOg/nu65T8=</digVal><cStat>100</cStat><xMotivo>Autorizado o uso da NF-e</xMotivo></infProt></protNFe></nfeProc>"
    }

    Rms.Fiscal.create_fiscal_invoice(org.id, attrs)
  end
end
