defmodule RmsWeb.Fiscal.FiscalInvoiceJSONTest do
  use RmsWeb.ConnCase, async: true

  import Rms.Factory
  alias Rms.Fiscal

  describe "format_fiscal_invoice_errors/1" do
    setup do
      # Create organization and location
      organization = insert(:organization)
      location = insert(:location, organization: organization)
      serie = insert(:invoice_serie, location: location, organization: organization)

      # Create order and related records
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment_1 =
        insert(:payment, status: "settled", transaction: transaction, organization: organization)

      payment_2 =
        insert(:payment, status: "settled", transaction: transaction, organization: organization)

      payment_3 =
        insert(:payment, status: "settled", transaction: transaction, organization: organization)

      transaction_customer =
        insert(:transaction_customer, transaction: transaction, organization: organization)

      product = insert(:product, organization: organization)
      pv = insert(:product_variant, product: product, organization: organization)

      # Set up product taxes
      product_taxes_params = %{
        ncm: product.ncm,
        uf: "SP",
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      {:ok, _product_taxes} = Fiscal.create_product_taxes(organization.id, product_taxes_params)

      # Default product taxes
      default_product_taxes_params = %{
        ncm: nil,
        uf: nil,
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      {:ok, _default_product_taxes} =
        Fiscal.create_product_taxes(organization.id, default_product_taxes_params)

      fulfillment = insert(:fulfillment, order: order, organization: organization)

      line_item =
        insert(:line_item,
          fulfillment: fulfillment,
          organization: organization,
          product_variant: pv,
          location: location
        )

      # Create fiscal invoice
      attrs = %{
        operation_type: "sale",
        serie_id: serie.id,
        customer_id: transaction_customer.id,
        invoice_payments: [
          %{payment_id: payment_1.id},
          %{payment_id: payment_2.id},
          %{payment_id: payment_3.id}
        ],
        invoice_items: [%{line_item_id: line_item.id}]
      }

      {:ok, fiscal_invoice} = Fiscal.create_fiscal_invoice(organization.id, attrs)

      # Create test XML for the fiscal invoice with vinco_taxes
      xml = """
      <?xml version="1.0" encoding="utf-8"?>
      <nfeProc xmlns="http://www.portalfiscal.inf.br/nfe" versao="4.00">
        <NFe xmlns="http://www.portalfiscal.inf.br/nfe">
          <infNFe versao="4.00" Id="NFe12345">
            <ide>
              <cUF>35</cUF>
              <nNF>123</nNF>
              <serie>1</serie>
              <dhEmi>2024-01-01T10:00:00-03:00</dhEmi>
            </ide>
            <emit>
              <CNPJ>12345678901234</CNPJ>
              <xNome>Test Company</xNome>
              <enderEmit>
                <xLgr>Test Street</xLgr>
                <nro>123</nro>
                <xBairro>Test District</xBairro>
                <xMun>Test City</xMun>
                <UF>SP</UF>
                <CEP>12345678</CEP>
              </enderEmit>
            </emit>
            <total>
              <ICMSTot>
                <vNF>100.00</vNF>
                <vDesc>0.00</vDesc>
                <vOutro>0.00</vOutro>
                <vPis>10.00</vPis>
                <vCofins>15.00</vCofins>
                <vICMS>20.00</vICMS>
              </ICMSTot>
            </total>
          </infNFe>
          <infNFeSupl>
            <urlChave>https://test.url</urlChave>
          </infNFeSupl>
        </NFe>
      </nfeProc>
      """

      # Mock the format_vinco_taxes function by adding metadata with tax values
      metadata = %{
        "DFeProtocolo" => "test_protocol",
        "taxes" => %{
          "vpis" => "10.00",
          "vcofins" => "15.00",
          "vicms" => "20.00"
        }
      }

      # Update fiscal invoice with XML and metadata
      fiscal_invoice_with_xml =
        fiscal_invoice
        |> Ecto.Changeset.change(%{xml: xml, df_key: "test_key", metadata: metadata})
        |> Rms.Repo.update!()
        |> Rms.Repo.preload([
          :customer,
          :organization,
          invoice_items: [line_item: [product_variant: [:product]]],
          invoice_payments: [:payment],
          serie: [:location],
          fiscal_invoice_errors: []
        ])

      {:ok, %{organization: organization, fiscal_invoice: fiscal_invoice_with_xml}}
    end

    test "correctly formats fiscal invoice errors", %{
      organization: organization,
      fiscal_invoice: fiscal_invoice
    } do
      # Create fiscal invoice errors
      error1 =
        insert(:fiscal_invoice_error,
          fiscal_invoice: fiscal_invoice,
          organization: organization,
          reason: "Test error message 1",
          status: "error_status_1",
          stacktrace: "Test stacktrace 1"
        )

      error2 =
        insert(:fiscal_invoice_error,
          fiscal_invoice: fiscal_invoice,
          organization: organization,
          reason: "Test error message 2",
          status: "error_status_2",
          stacktrace: "Test stacktrace 2"
        )

      # Reload the fiscal invoice with errors
      fiscal_invoice_with_errors =
        fiscal_invoice
        |> Rms.Repo.preload([:fiscal_invoice_errors], force: true)

      # Call the render function
      result =
        RmsWeb.Fiscal.FiscalInvoiceJSON.render("show.json", %{invoice: fiscal_invoice_with_errors})

      # Assert that the errors are correctly formatted
      assert Map.has_key?(result, "errors")
      errors = result["errors"]
      assert length(errors) == 2

      # Check the first error
      error1_result = Enum.find(errors, fn e -> e["id"] == error1.id end)
      assert error1_result["reason"] == "Test error message 1"
      assert error1_result["status"] == "error_status_1"
      assert error1_result["stacktrace"] == "Test stacktrace 1"

      # Check the second error
      error2_result = Enum.find(errors, fn e -> e["id"] == error2.id end)
      assert error2_result["reason"] == "Test error message 2"
      assert error2_result["status"] == "error_status_2"
      assert error2_result["stacktrace"] == "Test stacktrace 2"
    end

    test "returns empty array when there are no errors", %{fiscal_invoice: fiscal_invoice} do
      # Call the render function
      result = RmsWeb.Fiscal.FiscalInvoiceJSON.render("show.json", %{invoice: fiscal_invoice})

      # Assert that the errors key exists and is an empty list
      assert Map.has_key?(result, "errors")
      assert result["errors"] == []
    end
  end
end
