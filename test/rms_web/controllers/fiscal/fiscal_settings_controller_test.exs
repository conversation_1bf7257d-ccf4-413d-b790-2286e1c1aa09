defmodule RmsWeb.Fiscal.FiscalSettingsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    organization = insert(:organization)
    location = insert(:location, organization: organization)
    user = insert(:user, organization: organization)

    {:ok, conn: authenticate_conn(conn, user), organization: organization, location: location}
  end

  describe "show" do
    test "returns settings when they exist", %{
      conn: conn,
      organization: organization,
      location: location
    } do
      settings =
        insert(:fiscal_settings,
          organization: organization,
          location_id: location.id
        )

      conn = get(conn, ~p"/api/fiscal/settings/#{location.id}")

      %{
        id: id,
        handle_sale: handle_sale,
        handle_return: handle_return,
        handle_transfer: handle_transfer,
        environment: environment,
        location_id: location_id
      } = settings

      assert %{
               "data" => %{
                 "id" => ^id,
                 "handle_sale" => ^handle_sale,
                 "handle_return" => ^handle_return,
                 "handle_transfer" => ^handle_transfer,
                 "environment" => ^environment,
                 "location_id" => ^location_id
               }
             } = json_response(conn, 200)
    end

    test "returns 404 when settings don't exist", %{conn: conn, location: location} do
      conn = get(conn, ~p"/api/fiscal/settings/#{location.id}")

      assert json_response(conn, 404)
    end
  end

  describe "update" do
    setup %{organization: organization, location: location, conn: conn} do
      fs = insert(:fiscal_settings, organization: organization, location: location)

      {:ok, conn: conn, organization: organization, location: location, settings: fs}
    end

    test "updates settings", %{
      conn: conn,
      location: location,
      settings: settings
    } do
      params = %{
        "handle_sale" => false,
        "handle_return" => false,
        "handle_transfer" => false,
        "environment" => "prod"
      }

      conn = put(conn, ~p"/api/fiscal/settings/#{location.id}", params)

      settings_id = settings.id
      location_id = location.id

      assert %{
               "data" => %{
                 "id" => ^settings_id,
                 "handle_sale" => false,
                 "handle_return" => false,
                 "handle_transfer" => false,
                 "environment" => "prod",
                 "location_id" => ^location_id
               }
             } = json_response(conn, 200)
    end

    test "returns error for invalid params", %{conn: conn, location: location} do
      params = %{
        "handle_sale" => true,
        "handle_return" => true,
        "handle_transfer" => true,
        "environment" => "invalid"
      }

      conn = put(conn, ~p"/api/fiscal/settings/#{location.id}", params)

      assert %{"errors" => %{"environment" => ["is invalid"]}} = json_response(conn, 422)
    end
  end

  describe "create" do
    test "returns settings when given valid params", %{conn: conn, location: location} do
      params = %{
        "handle_sale" => true,
        "handle_return" => true,
        "handle_transfer" => true,
        "environment" => "prod"
      }

      conn = post(conn, ~p"/api/fiscal/settings/#{location.id}", params)

      assert %{
               "data" => %{
                 "id" => _id,
                 "handle_sale" => true,
                 "handle_return" => true,
                 "handle_transfer" => true,
                 "environment" => "prod",
                 "location_id" => location_id
               }
             } = json_response(conn, 201)

      assert location_id == location.id
    end

    test "returns error for invalid params", %{conn: conn, location: location} do
      params = %{
        "handle_sale" => true,
        "handle_return" => true,
        "handle_transfer" => true,
        "environment" => "invalid"
      }

      conn = post(conn, ~p"/api/fiscal/settings/#{location.id}", params)

      assert %{"errors" => %{"environment" => ["is invalid"]}} = json_response(conn, 422)
    end

    test "returns error when settings already exist", %{
      conn: conn,
      organization: organization,
      location: location
    } do
      insert(:fiscal_settings, organization: organization, location: location)

      params = %{
        "handle_sale" => true,
        "handle_return" => true,
        "handle_transfer" => true,
        "environment" => "prod"
      }

      conn = post(conn, ~p"/api/fiscal/settings/#{location.id}", params)

      assert json_response(conn, 422)
    end
  end
end
