defmodule RmsWeb.Fiscal.InvoiceSerieControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all series with the same organization", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      loc_2 = insert(:location, organization: user.organization)
      another_org = insert(:organization)
      another_loc = insert(:location, organization: another_org)

      serie_1 = insert(:invoice_serie, location: loc, organization: user.organization)
      serie_2 = insert(:invoice_serie, location: loc_2, organization: user.organization)
      insert(:invoice_serie, location: another_loc, organization: another_org)

      %{"series" => [serie1, serie2]} =
        conn
        |> get(~p"/api/fiscal/series")
        |> json_response(200)

      assert serie = Rms.Fiscal.get_invoice_serie!(user.organization_id, serie_1.id)
      assert serie.invoice_type == serie1["type"]

      assert serie = Rms.Fiscal.get_invoice_serie!(user.organization_id, serie_2.id)
      assert serie.invoice_type == serie2["type"]
    end

    test "lists all series with the same params", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      insert(:invoice_serie,
        invoice_env: "prod",
        invoice_type: "nf",
        location: loc,
        organization: user.organization
      )

      insert(:invoice_serie,
        invoice_env: "prod",
        invoice_type: "nfc",
        location: loc,
        organization: user.organization
      )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nf",
        location: loc,
        organization: user.organization
      )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nfc",
        location: loc,
        organization: user.organization
      )

      %{"series" => [serie1]} =
        conn
        |> get(~p"/api/fiscal/series", %{invoice_env: "dev", invoice_type: "nfc"})
        |> json_response(200)

      assert serie = Rms.Fiscal.get_invoice_serie!(user.organization_id, serie1["id"])
      assert serie.invoice_type == "nfc"
      assert serie.invoice_env == "dev"
    end
  end

  describe "show/2" do
    test "shows chosen serie", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      insert(:invoice_serie,
        invoice_env: "prod",
        invoice_type: "nf",
        location: loc,
        organization: user.organization
      )

      serie =
        insert(:invoice_serie,
          invoice_env: "prod",
          invoice_type: "nfc",
          location: loc,
          organization: user.organization
        )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nf",
        location: loc,
        organization: user.organization
      )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nfc",
        location: loc,
        organization: user.organization
      )

      conn = get(conn, ~p"/api/fiscal/series/#{serie.id}")
      assert json_response(conn, 200)["id"] == serie.id
    end

    test "does not show serie from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)

      insert(:invoice_serie,
        invoice_env: "prod",
        invoice_type: "nf",
        location: other_org_location,
        organization: other_org
      )

      serie =
        insert(:invoice_serie,
          invoice_env: "prod",
          invoice_type: "nfc",
          location: other_org_location,
          organization: other_org
        )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nf",
        location: other_org_location,
        organization: other_org
      )

      insert(:invoice_serie,
        invoice_env: "dev",
        invoice_type: "nfc",
        location: other_org_location,
        organization: other_org
      )

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/fiscal/series/#{serie.id}")
        |> json_response(404)
      end
    end
  end

  describe "create/2" do
    test "creates serie and returns its info", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      serie_params = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "dev",
        status: "active",
        location_id: loc.id
      }

      assert %{"id" => id} =
               conn
               |> post(~p"/api/fiscal/series", serie_params)
               |> json_response(201)

      assert serie = Rms.Fiscal.get_invoice_serie!(user.organization_id, id)
      assert serie.invoice_type == "nf"
      assert serie.invoice_serie == 1
      assert serie.available_number == 1
      assert serie.invoice_env == "dev"
    end

    test "does not create a serie without required fields", %{
      conn: conn
    } do
      serie_params = %{}

      assert %{
               "errors" => %{
                 "available_number" => ["can't be blank"],
                 "invoice_env" => ["can't be blank"],
                 "invoice_serie" => ["can't be blank"],
                 "invoice_type" => ["can't be blank"],
                 "location_id" => ["can't be blank"],
                 "status" => ["can't be blank"]
               }
             } =
               conn
               |> post(~p"/api/fiscal/series", serie_params)
               |> json_response(422)
    end

    test "does not create a serie for a location of another organization", %{
      conn: conn
    } do
      other_org = insert(:organization)

      loc = insert(:location, organization: other_org)

      serie_params = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "dev",
        status: "active",
        location_id: loc.id
      }

      assert %{"errors" => %{"location" => ["does not exist"]}} =
               conn
               |> post(~p"/api/fiscal/series", serie_params)
               |> json_response(422)
    end

    test "does not allow creating duplicate invoice serie", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      serie_params = %{
        invoice_type: "nf",
        invoice_serie: "1",
        available_number: 1,
        invoice_env: "dev",
        status: "active",
        location_id: loc.id
      }

      # Create first serie
      conn
      |> post(~p"/api/fiscal/series", serie_params)
      |> json_response(201)

      # Try creating duplicate serie
      assert %{"errors" => %{"invoice_type" => ["has already been taken"]}} =
               conn
               |> post(~p"/api/fiscal/series", serie_params)
               |> json_response(422)
    end
  end

  describe "update/2" do
    test "updates chosen serie", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      serie =
        insert(:invoice_serie,
          invoice_env: "prod",
          status: "active",
          invoice_type: "nf",
          location: loc,
          organization: user.organization
        )

      update_params = %{status: "inactive"}

      assert conn
             |> put(~p"/api/fiscal/series/#{serie.id}", update_params)
             |> json_response(200)

      assert Rms.Fiscal.get_invoice_serie!(user.organization_id, serie.id).status == "inactive"
    end

    test "does not update a serie from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)

      serie =
        insert(:invoice_serie,
          invoice_env: "prod",
          status: "active",
          invoice_type: "nf",
          location: other_org_location,
          organization: other_org
        )

      update_params = %{invoice_env: "unauthorized update"}

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> put(~p"/api/fiscal/series/#{serie.id}", update_params)
        |> json_response(404)
      end

      assert Rms.Fiscal.get_invoice_serie!(other_org_location.organization_id, serie.id).status !=
               "unauthorized update"
    end

    test "does not allow two active status for the same invoice type, env and location", %{
      conn: conn,
      user: user
    } do
      loc = insert(:location, organization: user.organization)

      serie =
        insert(:invoice_serie,
          invoice_env: "prod",
          invoice_type: "nf",
          status: "active",
          location: loc,
          organization: user.organization
        )

      serie_2 =
        insert(:invoice_serie,
          invoice_env: "prod",
          invoice_type: "nf",
          status: "inactive",
          location: loc,
          organization: user.organization
        )

      update_params = %{status: "active"}

      assert %{"errors" => %{"status" => ["has already been taken"]}} =
               conn
               |> put(~p"/api/fiscal/series/#{serie_2.id}", update_params)
               |> json_response(422)

      assert Rms.Fiscal.get_invoice_serie!(user.organization_id, serie_2.id).status == "inactive"
      assert Rms.Fiscal.get_invoice_serie!(user.organization_id, serie.id).status == "active"
    end
  end
end
