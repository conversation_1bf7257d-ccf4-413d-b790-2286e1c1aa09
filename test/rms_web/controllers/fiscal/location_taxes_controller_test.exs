defmodule RmsWeb.Fiscal.LocationTaxesControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all location taxes with the same organization", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      insert(:location_tax, location: location, organization: user.organization)
      location = insert(:location, organization: user.organization)
      insert(:location_tax, location: location, organization: user.organization)

      %{"location_taxes" => [lt1, lt2]} =
        conn
        |> get(~p"/api/fiscal/location_taxes")
        |> json_response(200)

      assert lt = Rms.Fiscal.get_location_tax!(user.organization_id, lt1["id"])
      assert lt.crt == lt1["crt"]

      assert lt = Rms.Fiscal.get_location_tax!(user.organization_id, lt2["id"])
      assert lt.crt == lt2["crt"]
    end
  end

  describe "show/2" do
    test "shows chosen location taxes", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      insert(:location_tax, location: location, organization: user.organization)
      location = insert(:location, organization: user.organization)

      lt =
        insert(:location_tax,
          location: location,
          organization: user.organization
        )

      conn = get(conn, ~p"/api/fiscal/location_taxes/#{lt.id}")
      assert json_response(conn, 200)["id"] == lt.id
    end

    test "does not show location taxes from another organization", %{conn: conn} do
      org = insert(:organization)

      location = insert(:location, organization: org)
      insert(:location_tax, location: location, organization: org)
      location = insert(:location, organization: org)
      lt = insert(:location_tax, location: location, organization: org)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/fiscal/location_taxes/#{lt.id}")
        |> json_response(404)
      end
    end
  end

  describe "create/2" do
    test "creates location taxes and returns its info", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)

      params = %{
        ie: "IE",
        crt: "3",
        name: "Name",
        location_id: location.id
      }

      assert %{"id" => id} =
               conn
               |> post(~p"/api/fiscal/location_taxes", params)
               |> json_response(201)

      assert lt = Rms.Fiscal.get_location_tax!(user.organization_id, id)
      assert params.ie == lt.ie
      assert params.crt == lt.crt
      assert params.name == lt.name
      assert params.location_id == lt.location_id
    end

    test "does not create a location taxes without required fields", %{
      conn: conn
    } do
      params = %{}

      assert %{
               "errors" => %{
                 "crt" => ["can't be blank"],
                 "ie" => ["can't be blank"],
                 "location_id" => ["can't be blank"],
                 "name" => ["can't be blank"]
               }
             } =
               conn
               |> post(~p"/api/fiscal/location_taxes", params)
               |> json_response(422)
    end
  end

  describe "update/2" do
    test "updates chosen location taxes", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)

      lt =
        insert(:location_tax,
          location: location,
          organization: user.organization
        )

      another_location = insert(:location, organization: user.organization)

      update_params = %{
        ie: "MEU IE",
        crt: "MEU CRT",
        name: "MEU NAME",
        location_id: another_location.id
      }

      assert conn
             |> put(~p"/api/fiscal/location_taxes/#{lt.id}", update_params)
             |> json_response(200)

      lt = Rms.Fiscal.get_location_tax!(user.organization_id, lt.id)

      assert update_params.ie == lt.ie
      assert update_params.crt == lt.crt
      assert update_params.name == lt.name
      refute update_params.location_id == lt.location_id
    end
  end
end
