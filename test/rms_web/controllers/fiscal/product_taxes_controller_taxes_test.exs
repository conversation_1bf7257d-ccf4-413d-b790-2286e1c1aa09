defmodule RmsWeb.Fiscal.ProductTaxesControllerTaxesTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all product taxes with the same organization", %{conn: conn, user: user} do
      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: user.organization
      )

      insert(:product_taxes,
        uf: "RJ",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: user.organization
      )

      %{"product_taxes" => [pt1, pt2]} =
        conn
        |> get(~p"/api/fiscal/product_taxes")
        |> json_response(200)

      assert pt = Rms.Fiscal.get_product_taxes!(user.organization_id, pt1["id"])
      assert pt.uf == pt1["uf"]

      assert pt = Rms.Fiscal.get_product_taxes!(user.organization_id, pt2["id"])
      assert pt.uf == pt2["uf"]
    end

    test "lists all series with the same params", %{conn: conn, user: user} do
      org = user.organization

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      insert(:product_taxes,
        uf: "RJ",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      %{"product_taxes" => [pt]} =
        conn
        |> get(~p"/api/fiscal/product_taxes", %{uf: "RJ", origin: "0"})
        |> json_response(200)

      assert pt = Rms.Fiscal.get_product_taxes!(user.organization_id, pt["id"])
      assert pt.uf == "RJ"
      assert pt.origin == "0"
    end
  end

  describe "show/2" do
    test "shows chosen product_taxes", %{conn: conn, user: user} do
      org = user.organization

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      pt =
        insert(:product_taxes,
          uf: "RJ",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      conn = get(conn, ~p"/api/fiscal/product_taxes/#{pt.id}")
      assert json_response(conn, 200)["id"] == pt.id
    end

    test "does not show product_taxes from another organization", %{conn: conn} do
      org = insert(:organization)

      insert(:product_taxes,
        uf: "SP",
        origin: "0",
        cst_icms: "30",
        fcp_percentage: Decimal.new("0.02"),
        icms_percentage: Decimal.new("0.18"),
        organization: org
      )

      pt =
        insert(:product_taxes,
          uf: "RJ",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/fiscal/product_taxes/#{pt.id}")
        |> json_response(404)
      end
    end
  end

  describe "create/2" do
    test "creates product_taxes and returns its info", %{conn: conn, user: user} do
      params = %{
        ncm: "p1.ncm",
        uf: "SP",
        origin: "0",
        cfop: "5102",
        cest: "400",
        cst_icms: "400",
        cst_pis: "08",
        cst_cofins: "08",
        icms_percentage: Decimal.new("0.1"),
        fcp_percentage: Decimal.new("0.1"),
        pis_percentage: Decimal.new("0.1"),
        cofins_percentage: Decimal.new("0.1")
      }

      assert %{"id" => id} =
               conn
               |> post(~p"/api/fiscal/product_taxes", params)
               |> json_response(201)

      assert pt = Rms.Fiscal.get_product_taxes!(user.organization_id, id)
      assert params.ncm == pt.ncm
      assert params.uf == pt.uf
      assert params.origin == pt.origin
      assert params.cfop == pt.cfop
      assert params.cest == pt.cest
      assert params.cst_icms == pt.cst_icms
      assert params.cst_pis == pt.cst_pis
      assert params.cst_cofins == pt.cst_cofins
      assert params.icms_percentage == pt.icms_percentage
      assert params.fcp_percentage == pt.fcp_percentage
      assert params.pis_percentage == pt.pis_percentage
      assert params.cofins_percentage == pt.cofins_percentage
    end

    test "does not create a product_taxes without required fields", %{
      conn: conn
    } do
      params = %{}

      assert %{
               "errors" => %{
                 "cfop" => ["can't be blank"],
                 "cofins_percentage" => ["can't be blank"],
                 "cst_cofins" => ["can't be blank"],
                 "cst_icms" => ["can't be blank"],
                 "cst_pis" => ["can't be blank"],
                 "fcp_percentage" => ["can't be blank"],
                 "icms_percentage" => ["can't be blank"],
                 "pis_percentage" => ["can't be blank"]
               }
             } =
               conn
               |> post(~p"/api/fiscal/product_taxes", params)
               |> json_response(422)
    end
  end

  describe "update/2" do
    test "updates chosen product_taxes", %{conn: conn, user: user} do
      org = user.organization

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      update_params = %{
        ncm: "MEU NCM",
        uf: "BR",
        origin: "6",
        cfop: "5123",
        cest: "500",
        cst_icms: "200",
        cst_pis: "80",
        cst_cofins: "80",
        icms_percentage: Decimal.new("10"),
        fcp_percentage: Decimal.new("20"),
        pis_percentage: Decimal.new("30"),
        cofins_percentage: Decimal.new("40")
      }

      assert conn
             |> put(~p"/api/fiscal/product_taxes/#{pt.id}", update_params)
             |> json_response(200)

      pt = Rms.Fiscal.get_product_taxes!(user.organization_id, pt.id)

      assert update_params.ncm == pt.ncm
      assert update_params.uf == pt.uf
      assert update_params.origin == pt.origin
      assert update_params.cfop == pt.cfop
      assert update_params.cst_icms == pt.cst_icms
      assert update_params.cst_pis == pt.cst_pis
      assert update_params.cst_cofins == pt.cst_cofins
      assert update_params.icms_percentage == pt.icms_percentage
      assert update_params.fcp_percentage == pt.fcp_percentage
      assert update_params.pis_percentage == pt.pis_percentage
      assert update_params.cofins_percentage == pt.cofins_percentage
    end

    test "does not update a product_taxes from another organization", %{conn: conn} do
      org = insert(:organization)

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      update_params = %{
        ncm: "MEU NCM",
        uf: "BR",
        origin: "6",
        cfop: "5123",
        cest: "500",
        cst_icms: "200",
        cst_pis: "80",
        cst_cofins: "80",
        icms_percentage: Decimal.new("10"),
        fcp_percentage: Decimal.new("20"),
        pis_percentage: Decimal.new("30"),
        cofins_percentage: Decimal.new("40")
      }

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> put(~p"/api/fiscal/product_taxes/#{pt.id}", update_params)
        |> json_response(404)
      end

      pt = Rms.Fiscal.get_product_taxes!(org.id, pt.id)

      refute update_params.ncm == pt.ncm
      refute update_params.uf == pt.uf
      refute update_params.origin == pt.origin
      refute update_params.cfop == pt.cfop
      refute update_params.cst_icms == pt.cst_icms
      refute update_params.cst_pis == pt.cst_pis
      refute update_params.cst_cofins == pt.cst_cofins
      refute update_params.icms_percentage == pt.icms_percentage
      refute update_params.fcp_percentage == pt.fcp_percentage
      refute update_params.pis_percentage == pt.pis_percentage
      refute update_params.cofins_percentage == pt.cofins_percentage
    end
  end

  describe "delete/2" do
    test "deletes a product_taxes", %{conn: conn, user: user} do
      org = user.organization

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      conn = delete(conn, ~p"/api/fiscal/product_taxes/#{pt.id}")
      assert response(conn, 204)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Fiscal.get_product_taxes!(user.organization_id, pt.id)
      end
    end

    test "does not deletes a product_taxes from another org", %{conn: conn} do
      org = insert(:organization)

      pt =
        insert(:product_taxes,
          uf: "SP",
          origin: "0",
          cst_icms: "30",
          fcp_percentage: Decimal.new("0.02"),
          icms_percentage: Decimal.new("0.18"),
          organization: org
        )

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> delete(~p"/api/fiscal/product_taxes/#{pt.id}")
        |> json_response(404)
      end

      assert %Rms.Fiscal.ProductTaxes{} = Rms.Fiscal.get_product_taxes!(org.id, pt.id)
    end
  end
end
