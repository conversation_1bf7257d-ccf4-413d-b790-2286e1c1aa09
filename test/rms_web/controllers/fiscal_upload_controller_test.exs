defmodule RmsWeb.FiscalUploadControllerTest do
  use RmsWeb.ConnCase
  import Mox
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  alias Rms.Workers.ConfirmProductTaxUploadWorker
  alias Rms.Storage.UploadEntry

  @upload_bucket "test-bucket"
  setup :verify_on_exit!

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)

    conn = authenticate_conn(conn, user)

    {:ok, %{conn: conn, organization: organization}}
  end

  describe "create_upload_request/2" do
    test "creates upload request successfully", %{conn: conn, organization: organization} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn bucket, key, opts ->
        assert bucket == @upload_bucket
        assert String.starts_with?(key, "product_taxes/")
        assert Keyword.get(opts, :content_type) == "text/csv"
        assert Keyword.get(opts, :content_length) == 10 * 1024 * 1024
        assert Keyword.get(opts, :expires_in) == 3600

        {:ok,
         %{
           url: "https://example.com/test-url",
           fields: %{
             "key" => key,
             "Content-Type" => "text/csv"
           }
         }}
      end)

      conn = post(conn, ~p"/api/fiscal/product_taxes/upload_requests")

      assert %{
               "url" => url,
               "upload_id" => upload_id,
               "expires_at" => expires_at,
               "fields" => fields
             } = json_response(conn, 201)

      assert is_binary(url)
      assert is_integer(upload_id)
      assert {:ok, _datetime, _offset} = DateTime.from_iso8601(expires_at)
      assert is_map(fields)
      assert fields["Content-Type"] == "text/csv"

      # Verify confirmation job was scheduled with organization_id
      assert_enqueued(
        worker: ConfirmProductTaxUploadWorker,
        args: %{
          "upload_id" => upload_id,
          "organization_id" => organization.id
        }
      )
    end

    test "returns error when URL generation fails", %{conn: conn} do
      expect(Rms.Storage.Mock, :generate_presigned_post, fn _bucket, _key, _opts ->
        {:error, "some error"}
      end)

      conn = post(conn, ~p"/api/fiscal/product_taxes/upload_requests")

      assert %{"error" => "failed to generate upload url"} = json_response(conn, 422)

      # Verify no confirmation job was scheduled
      refute_enqueued(worker: ConfirmProductTaxUploadWorker)
    end

    test "requires authentication", %{conn: _conn} do
      conn = build_conn()
      conn = post(conn, ~p"/api/fiscal/product_taxes/upload_requests")

      assert response(conn, 401)
    end
  end

  describe "confirm_upload/2" do
    test "confirms upload when file exists", %{conn: conn, organization: organization} do
      upload = insert(:upload_entry, organization: organization)

      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == upload.s3_key
        {:ok, true}
      end)

      conn = post(conn, ~p"/api/fiscal/product_taxes/uploads/#{upload.id}/confirm")

      assert %{"status" => "processing"} = json_response(conn, 200)

      # Check upload status was updated
      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "processing"
    end

    test "returns pending when file doesn't exist and not expired", %{
      conn: conn,
      organization: organization
    } do
      upload = insert(:upload_entry, organization: organization)

      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == upload.s3_key
        {:ok, false}
      end)

      conn = post(conn, ~p"/api/fiscal/product_taxes/uploads/#{upload.id}/confirm")

      assert %{"status" => "pending"} = json_response(conn, 200)

      # Check upload status remains pending
      upload = Rms.Repo.get!(UploadEntry, upload.id)
      assert upload.status == "pending"
    end

    test "returns expired when file doesn't exist and expired", %{
      conn: conn,
      organization: organization
    } do
      expired_upload =
        insert(:upload_entry,
          organization: organization,
          expires_at: DateTime.utc_now() |> DateTime.add(-1, :hour) |> DateTime.truncate(:second)
        )

      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == expired_upload.s3_key
        {:ok, false}
      end)

      conn = post(conn, ~p"/api/fiscal/product_taxes/uploads/#{expired_upload.id}/confirm")

      assert %{"status" => "expired"} = json_response(conn, 200)

      # Check upload was marked as expired
      upload = Rms.Repo.get!(UploadEntry, expired_upload.id)
      assert upload.status == "expired"
    end

    test "returns error when upload not found", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        post(conn, ~p"/api/fiscal/product_taxes/uploads/999999/confirm")
      end
    end

    test "returns error when upload not in pending status", %{
      conn: conn,
      organization: organization
    } do
      upload =
        insert(:upload_entry,
          organization: organization,
          status: "processing"
        )

      conn = post(conn, ~p"/api/fiscal/product_taxes/uploads/#{upload.id}/confirm")

      assert %{"error" => "cannot confirm upload in processing status"} = json_response(conn, 422)
    end

    test "requires authentication", %{conn: _conn} do
      conn = build_conn()
      conn = post(conn, ~p"/api/fiscal/product_taxes/uploads/1/confirm")

      assert response(conn, 401)
    end
  end
end
