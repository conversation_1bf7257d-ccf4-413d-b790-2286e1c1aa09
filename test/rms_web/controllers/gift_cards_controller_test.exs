defmodule RmsWeb.GiftCardsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  setup :verify_on_exit!

  setup %{conn: conn} do
    user = insert(:user)

    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user, org: user.organization}
  end

  describe "check_balance/2" do
    test "validates required parameters", %{conn: conn} do
      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "not_supported"
          # missing card_number
        })

      assert %{"errors" => errors} = json_response(conn, 422)
      assert "is invalid" in errors["provider"]
      assert "can't be blank" in errors["card_number"]
    end
  end

  describe "check_balance/2 with gift_promo provider" do
    test "successfully returns the balance", %{conn: conn, org: org} do
      credential =
        insert(:gift_promo_credential,
          organization: org,
          location: build(:location, organization: org)
        )

      expect(Rms.Integrations.GiftPromo.Mock, :client, fn _ -> :mock_client end)

      expect(Rms.Integrations.GiftPromo.Mock, :check_gift_card, fn :mock_client, _ ->
        {:ok,
         %{
           "sucesso" => "1",
           "cartaoStatus" => "Ativo",
           "cartaoSaldo" => "29,10"
         }}
      end)

      expect(Rms.Integrations.GiftPromo.Mock, :validate_gift_card_password, fn :mock_client, _ ->
        {:ok, %{}}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "gift_promo",
          "card_number" => "6367030104262382",
          "card_pin" => "4357",
          "location_id" => credential.location_id
        })

      assert %{"balance" => "29.10"} = json_response(conn, 200)
    end
  end

  describe "check_balance/2 with shopify provider" do
    setup %{org: org} do
      insert(:shopify_credential, organization: org)

      stub(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

      :ok
    end

    test "successfully returns balance", %{conn: conn} do
      card_number = "r8dkphvjymkdhdgc"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, code ->
        assert code == card_number

        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/636559687990",
                     "balance" => %{
                       "amount" => "10.0"
                     },
                     "enabled" => true,
                     "expiresOn" => nil,
                     "deactivatedAt" => nil
                   }
                 }
               ]
             }
           },
           "extensions" => %{
             "cost" => %{
               "requestedQueryCost" => 5,
               "actualQueryCost" => 3,
               "throttleStatus" => %{
                 "maximumAvailable" => 2000,
                 "currentlyAvailable" => 1997,
                 "restoreRate" => 100
               }
             }
           }
         }}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => card_number
        })

      assert %{"balance" => "10.0"} = json_response(conn, 200)
    end

    test "returns card expired error code", %{conn: conn} do
      card_number = "39bqyggq8krpymhp"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, code ->
        assert code == card_number

        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/636559622454",
                     "balance" => %{
                       "amount" => "10.0"
                     },
                     "enabled" => true,
                     "expiresOn" => "2023-01-01",
                     "deactivatedAt" => nil
                   }
                 }
               ]
             }
           },
           "extensions" => %{
             "cost" => %{
               "requestedQueryCost" => 5,
               "actualQueryCost" => 3,
               "throttleStatus" => %{
                 "maximumAvailable" => 2000,
                 "currentlyAvailable" => 1997,
                 "restoreRate" => 100
               }
             }
           }
         }}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => card_number
        })

      assert %{"error" => %{"code" => "CARD_EXPIRED"}} = json_response(conn, 400)
    end

    test "returns card not active error code", %{conn: conn} do
      card_number = "39bqyggq8krpymhp"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, code ->
        assert code == card_number

        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/636559622454",
                     "balance" => %{
                       "amount" => "10.0"
                     },
                     "enabled" => false,
                     "expiresOn" => nil,
                     "deactivatedAt" => nil
                   }
                 }
               ]
             }
           },
           "extensions" => %{
             "cost" => %{
               "requestedQueryCost" => 5,
               "actualQueryCost" => 3,
               "throttleStatus" => %{
                 "maximumAvailable" => 2000,
                 "currentlyAvailable" => 1997,
                 "restoreRate" => 100
               }
             }
           }
         }}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => card_number
        })

      assert %{"error" => %{"code" => "CARD_NOT_ACTIVE"}} = json_response(conn, 400)
    end

    test "returns card not found error code", %{conn: conn} do
      card_number = "39bqyggq8krpymhp"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, code ->
        assert code == card_number

        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => []
             }
           },
           "extensions" => %{
             "cost" => %{
               "requestedQueryCost" => 5,
               "actualQueryCost" => 3,
               "throttleStatus" => %{
                 "maximumAvailable" => 2000,
                 "currentlyAvailable" => 1997,
                 "restoreRate" => 100
               }
             }
           }
         }}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => card_number
        })

      assert %{"error" => %{"code" => "CARD_NOT_FOUND"}} = json_response(conn, 404)
    end

    test "returns error on multiple gift cards return", %{conn: conn} do
      card_number = "39bqyggq8krpymhp"

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, code ->
        assert code == card_number

        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/636559622454",
                     "balance" => %{
                       "amount" => "10.0"
                     },
                     "enabled" => false,
                     "expiresOn" => nil,
                     "deactivatedAt" => nil
                   }
                 },
                 %{
                   "node" => %{
                     "id" => "gid://shopify/GiftCard/different_gift_card",
                     "balance" => %{
                       "amount" => "10.0"
                     },
                     "enabled" => false,
                     "expiresOn" => nil,
                     "deactivatedAt" => nil
                   }
                 }
               ]
             }
           },
           "extensions" => %{
             "cost" => %{
               "requestedQueryCost" => 5,
               "actualQueryCost" => 3,
               "throttleStatus" => %{
                 "maximumAvailable" => 2000,
                 "currentlyAvailable" => 1997,
                 "restoreRate" => 100
               }
             }
           }
         }}
      end)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => card_number
        })

      assert %{"error" => %{"code" => "INTERNAL_SERVER_ERROR"}} = json_response(conn, 500)
    end

    test "returns card not found if card number is less than 4 characters", %{conn: conn} do
      deny(Rms.Integrations.Shopify.Mock, :client, 2)
      deny(Rms.Integrations.Shopify.Mock, :fetch_gift_card, 2)

      conn =
        post(conn, ~p"/api/gift_cards/check_balance", %{
          "provider" => "shopify",
          "card_number" => "4444"
        })

      assert %{"error" => %{"code" => "CARD_NOT_FOUND"}} = json_response(conn, 404)
    end
  end
end
