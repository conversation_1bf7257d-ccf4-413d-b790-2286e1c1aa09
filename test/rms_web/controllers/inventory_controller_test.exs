defmodule RmsWeb.InventoryControllerTest do
  use RmsWeb.ConnCase
  import Rms.Factory

  import Mox
  setup :verify_on_exit!

  setup %{conn: conn} do
    user = insert(:user)
    loc = insert(:location, organization: user.organization)
    {:ok, conn: authenticate_conn(conn, user), user: user, loc: loc}
  end

  describe "index/2" do
    setup %{user: user, loc: loc} = opts do
      insert_list(10, :inventory_item,
        organization: user.organization,
        location: loc,
        product_variant:
          build(:product_variant,
            organization: user.organization,
            product: build(:product, organization: user.organization)
          )
      )

      {:ok, opts}
    end

    test "lists all inventory items", %{conn: conn} do
      conn = get(conn, ~p"/api/inventory")
      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 10
    end

    test "can filter by location_id", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      insert_list(5, :inventory_item,
        organization: user.organization,
        location: loc,
        product_variant:
          build(:product_variant,
            organization: user.organization,
            product: build(:product, organization: user.organization)
          )
      )

      conn = get(conn, ~p"/api/inventory?location_id=#{loc.id}")
      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 5
    end

    test "can filter by product_variant_id", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      insert(:inventory_item,
        organization: user.organization,
        location: loc,
        product_variant: product_variant
      )

      conn = get(conn, ~p"/api/inventory?product_variant_id=#{product_variant.id}")
      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 1
    end

    test "can filter by multiple product_variant_ids", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      product_variant1 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      product_variant2 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      insert(:inventory_item,
        organization: user.organization,
        location: loc,
        product_variant: product_variant1
      )

      insert(:inventory_item,
        organization: user.organization,
        location: loc,
        product_variant: product_variant2
      )

      conn =
        get(conn, ~p"/api/inventory", %{
          product_variant_ids: [product_variant1.id, product_variant2.id]
        })

      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 2
    end

    test "fetches inventory from integration when listing a single inventory from a single location",
         %{conn: conn, loc: loc} do
      {:ok, flag} = :ldclient_testdata.flag("custom-inventory-integration")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations(["smartstore"], flag))

      product_variant1 =
        insert(:product_variant,
          organization: loc.organization,
          product: build(:product, organization: loc.organization)
        )

      expect(Rms.Integrations.SmartStore.Mock, :client, fn _ -> :mock_client end)
      loc_cnpj = loc.cnpj
      sku = product_variant1.sku

      expect(Rms.Integrations.SmartStore.Mock, :get_inventory, fn :mock_client, ^loc_cnpj, ^sku ->
        {:ok, %{"quantity" => 10, "cnpj" => loc_cnpj, "sku" => sku}}
      end)

      conn =
        get(conn, ~p"/api/inventory", %{
          product_variant_ids: [product_variant1.id],
          location_id: loc.id
        })

      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 1
      assert [%{"quantity" => 10}] = data
    end

    test "update inventory based on integration when listing a single inventory from a single location and inventory exists",
         %{conn: conn, loc: loc} do
      {:ok, flag} = :ldclient_testdata.flag("custom-inventory-integration")
      :ldclient_testdata.update(:ldclient_flagbuilder.variations(["smartstore"], flag))

      product_variant1 =
        insert(:product_variant,
          organization: loc.organization,
          product: build(:product, organization: loc.organization)
        )

      inserted_at = DateTime.utc_now() |> DateTime.add(-5 * 24 * 60 * 60, :second)

      insert(:inventory_item,
        product_variant: product_variant1,
        location: loc,
        organization: loc.organization,
        quantity: 3,
        inserted_at: inserted_at,
        updated_at: inserted_at
      )

      expect(Rms.Integrations.SmartStore.Mock, :client, fn _ -> :mock_client end)
      loc_cnpj = loc.cnpj
      sku = product_variant1.sku

      expect(Rms.Integrations.SmartStore.Mock, :get_inventory, fn :mock_client, ^loc_cnpj, ^sku ->
        {:ok, %{"quantity" => 10, "cnpj" => loc_cnpj, "sku" => sku}}
      end)

      conn =
        get(conn, ~p"/api/inventory", %{
          product_variant_ids: [product_variant1.id],
          location_id: loc.id
        })

      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 1
      assert [%{"quantity" => 10}] = data
    end
  end

  describe "show/2" do
    test "shows chosen inventory item", %{conn: conn, user: user, loc: loc} do
      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant:
            build(:product_variant,
              organization: user.organization,
              product: build(:product, organization: user.organization)
            )
        )

      conn = get(conn, ~p"/api/inventory/#{inventory_item}")
      assert %{"data" => inventory_item_response} = json_response(conn, 200)
      assert inventory_item_response["id"] == inventory_item.id
    end
  end

  describe "show_by_sku/2" do
    test "shows chosen inventory item by SKU", %{conn: conn, user: user, loc: loc} do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant: product_variant
        )

      conn = get(conn, ~p"/api/inventory/by_sku/#{product_variant.sku}")
      assert %{"data" => inventory_item_response} = json_response(conn, 200)
      assert inventory_item_response["id"] == inventory_item.id
    end
  end

  describe "create/2" do
    test "creates a new inventory item", %{conn: conn, user: user, loc: loc} do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      conn =
        post(conn, ~p"/api/inventory",
          inventory_item: %{
            location_id: loc.id,
            product_variant_id: product_variant.id,
            quantity: 30
          }
        )

      assert %{"data" => inventory_item_response} = json_response(conn, 201)
      assert inventory_item_response["location_id"] == loc.id
      assert inventory_item_response["product_variant_id"] == product_variant.id
      assert inventory_item_response["quantity"] == 30
    end

    test "does not create inventory item with invalid data", %{conn: conn, loc: loc} do
      conn =
        post(conn, ~p"/api/inventory",
          inventory_item: %{location_id: loc.id, product_variant_id: nil, quantity: 30}
        )

      assert %{"errors" => errors} = json_response(conn, 422)
      assert "can't be blank" in errors["product_variant_id"]
    end

    test "upserts inventory items", %{conn: conn, user: user, loc: loc} do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant: product_variant,
          quantity: 25
        )

      conn =
        post(conn, ~p"/api/inventory",
          inventory_item: %{
            location_id: loc.id,
            product_variant_id: product_variant.id,
            quantity: 30
          }
        )

      assert %{"quantity" => 30} = json_response(conn, 201)["data"]
      assert inventory_item.quantity == 25
      assert Rms.Repo.reload!(inventory_item).quantity == 30
    end
  end

  describe "update/2" do
    test "updates chosen inventory item", %{conn: conn, user: user, loc: loc} do
      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          quantity: 50,
          product_variant:
            build(:product_variant,
              organization: user.organization,
              product: build(:product, organization: user.organization)
            )
        )

      conn =
        put(conn, ~p"/api/inventory/#{inventory_item}",
          inventory_item: %{location_id: loc.id, quantity: 91}
        )

      assert %{"data" => inventory_item_response} = json_response(conn, 200)

      reloaded_inventory_item = Rms.Repo.reload!(inventory_item)
      assert reloaded_inventory_item.quantity == 91
      assert reloaded_inventory_item.quantity != inventory_item
      assert inventory_item_response["id"] == inventory_item.id
    end

    test "does not update inventory item with invalid data", %{conn: conn, user: user, loc: loc} do
      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          quantity: 50,
          product_variant:
            build(:product_variant,
              organization: user.organization,
              product: build(:product, organization: user.organization)
            )
        )

      conn =
        put(conn, ~p"/api/inventory/#{inventory_item}",
          inventory_item: %{location_id: loc.id, quantity: nil}
        )

      assert %{"errors" => errors} = json_response(conn, 422)
      assert "can't be blank" in errors["quantity"]
      assert Rms.Repo.reload!(inventory_item).quantity == inventory_item.quantity
    end
  end

  describe "update_by_sku/2" do
    test "updates chosen inventory item by SKU", %{conn: conn, user: user, loc: loc} do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant: product_variant,
          quantity: 50
        )

      conn =
        put(conn, ~p"/api/inventory/by_sku/#{product_variant.sku}",
          inventory_item: %{location_id: loc.id, quantity: 91}
        )

      assert %{"data" => inventory_item_response} = json_response(conn, 200)

      reloaded_inventory_item = Rms.Repo.reload!(inventory_item)
      assert reloaded_inventory_item.quantity == 91
      assert reloaded_inventory_item.quantity != inventory_item
      assert inventory_item_response["id"] == inventory_item.id
    end

    test "does not update inventory item by SKU with invalid data", %{
      conn: conn,
      user: user,
      loc: loc
    } do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      inventory_item =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant: product_variant,
          quantity: 50
        )

      conn =
        put(conn, ~p"/api/inventory/by_sku/#{product_variant.sku}",
          inventory_item: %{location_id: loc.id, quantity: nil}
        )

      assert %{"errors" => errors} = json_response(conn, 422)
      assert "can't be blank" in errors["quantity"]
      assert Rms.Repo.reload!(inventory_item).quantity == inventory_item.quantity
    end
  end

  describe "create_by_sku/2" do
    test "creates inventory item when valid data and SKU", %{conn: conn, user: user, loc: loc} do
      product_variant =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization),
          sku: "TEST123"
        )

      conn =
        post(conn, ~p"/api/inventory/by_sku/TEST123",
          inventory_item: %{
            location_id: loc.id,
            quantity: 30
          }
        )

      assert %{"data" => inventory_item} = json_response(conn, 201)
      assert inventory_item["quantity"] == 30
      assert inventory_item["location_id"] == loc.id
      assert inventory_item["product_variant_id"] == product_variant.id
    end

    test "returns error when SKU doesn't exist", %{conn: conn, loc: loc} do
      assert_error_sent :not_found, fn ->
        post(conn, ~p"/api/inventory/by_sku/NONEXISTENT",
          inventory_item: %{
            location_id: loc.id,
            quantity: 30
          }
        )
      end
    end
  end

  describe "create_bulk_by_sku/2" do
    test "creates multiple inventory items successfully", %{conn: conn, user: user, loc: loc} do
      insert(:product_variant,
        organization: user.organization,
        product: build(:product, organization: user.organization),
        sku: "SKU1"
      )

      insert(:product_variant,
        organization: user.organization,
        product: build(:product, organization: user.organization),
        sku: "SKU2"
      )

      conn =
        post(conn, ~p"/api/inventory/by_sku/bulk",
          items: %{
            "SKU1" => %{
              "location_id" => loc.id,
              "quantity" => 10
            },
            "SKU2" => %{
              "location_id" => loc.id,
              "quantity" => 20
            }
          }
        )

      assert %{"data" => data} = json_response(conn, 201)
      assert length(data) == 2

      [item1, item2] = Enum.sort_by(data, & &1["quantity"])
      assert item1["quantity"] == 10
      assert item2["quantity"] == 20
    end

    test "partially succeeds for bulk inventory update", %{conn: conn, user: user, loc: loc} do
      insert(:product_variant,
        organization: user.organization,
        product: build(:product, organization: user.organization),
        sku: "SKU1"
      )

      assert response =
               conn
               |> post(~p"/api/inventory/by_sku/bulk",
                 items: %{
                   "SKU1" => %{
                     "location_id" => loc.id,
                     "quantity" => 10
                   },
                   "INVALID" => %{
                     "location_id" => loc.id,
                     "quantity" => 20
                   }
                 }
               )
               |> json_response(:created)

      assert length(response["data"]) == 1
    end
  end

  describe "update_bulk_by_sku/2" do
    test "updates multiple inventory items successfully", %{conn: conn, user: user, loc: loc} do
      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization),
          sku: "SKU1"
        )

      pv2 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization),
          sku: "SKU2"
        )

      insert(:inventory_item,
        organization: user.organization,
        location: loc,
        product_variant: pv1,
        quantity: 10
      )

      insert(:inventory_item,
        organization: user.organization,
        location: loc,
        product_variant: pv2,
        quantity: 20
      )

      conn =
        put(conn, ~p"/api/inventory/by_sku/bulk",
          items: %{
            "SKU1" => %{"quantity" => 15},
            "SKU2" => %{"quantity" => 25}
          }
        )

      assert %{"data" => data} = json_response(conn, 200)
      assert length(data) == 2

      [item1, item2] = Enum.sort_by(data, & &1["quantity"])
      assert item1["quantity"] == 15
      assert item2["quantity"] == 25
    end

    test "returns error when any update fails", %{conn: conn, user: user, loc: loc} do
      pv1 =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization),
          sku: "SKU1"
        )

      ii1 =
        insert(:inventory_item,
          organization: user.organization,
          location: loc,
          product_variant: pv1,
          quantity: 10
        )

      conn =
        put(conn, ~p"/api/inventory/by_sku/bulk",
          items: %{
            "SKU1" => %{"quantity" => nil}
          }
        )

      assert %{"errors" => errors} = json_response(conn, 422)
      assert length(errors) == 1

      # Verify no updates were made
      reloaded_ii1 = Rms.Repo.reload!(ii1)
      assert reloaded_ii1.quantity == 10
    end
  end
end
