defmodule RmsWeb.LocationsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all non-archived locations", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      assert %{"locations" => [location]} =
               conn
               |> get(~p"/api/locations")
               |> json_response(200)

      assert location["id"] == loc.id
    end

    test "list locations by cnpj", %{conn: conn, user: user} do
      insert(:location, organization: user.organization, cnpj: "12345678901234")
      insert(:location, organization: user.organization, cnpj: "98765432109876")

      insert(:location,
        organization: user.organization,
        cnpj: "11111111111111",
        archived_at: DateTime.utc_now()
      )

      conn = get(conn, ~p"/api/locations", %{cnpj: "12345678901234"})
      assert response = json_response(conn, 200)
      assert ["12345678901234"] = get_in(response, ["locations", Access.all(), "cnpj"])
    end

    test "list locations by name", %{conn: conn, user: user} do
      insert(:location, organization: user.organization, name: "Main Office")
      insert(:location, organization: user.organization, name: "Subsidiary Office")

      insert(:location,
        organization: user.organization,
        name: "Warehouse",
        archived_at: DateTime.utc_now()
      )

      conn = get(conn, ~p"/api/locations", %{name: "Office"})
      assert response = json_response(conn, 200)

      names = Enum.sort(Enum.map(response["locations"], & &1["name"]))
      assert names == Enum.sort(["Main Office", "Subsidiary Office"])
    end

    test "lists all locations including archived when 'allow_archived' is true", %{
      conn: conn,
      user: user
    } do
      active_location = insert(:location, organization: user.organization)
      now = DateTime.truncate(DateTime.utc_now(), :second)
      archived_location = insert(:location, organization: user.organization, archived_at: now)

      conn = get(conn, ~p"/api/locations", %{allow_archived: true})
      assert response = json_response(conn, 200)

      locations_id = Enum.map(response["locations"], & &1["id"])
      assert active_location.id in locations_id
      assert archived_location.id in locations_id
    end

    test "lists only non-archived locations when 'allow_archived' is false", %{
      conn: conn,
      user: user
    } do
      active_location = insert(:location, organization: user.organization)

      now = DateTime.truncate(DateTime.utc_now(), :second)
      archived_location = insert(:location, organization: user.organization, archived_at: now)

      conn = get(conn, ~p"/api/locations", %{allow_archived: false})
      assert response = json_response(conn, 200)

      locations_id = Enum.map(response["locations"], & &1["id"])
      assert active_location.id in locations_id
      refute archived_location.id in locations_id
    end

    test "returns page information", %{conn: conn, user: user} do
      insert_list(15, :location, organization: user.organization)

      conn = get(conn, ~p"/api/locations", %{limit: 5})
      assert page = json_response(conn, 200)["page"]
      assert page["after"]
      assert page["limit"] == 5
    end
  end

  describe "show/2" do
    test "shows chosen location", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      conn = get(conn, ~p"/api/locations/#{location.id}")
      assert json_response(conn, 200)["id"] == location.id
    end

    test "does not show location from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/locations/#{other_org_location.id}")
        |> json_response(404)
      end
    end
  end

  describe "create/2" do
    test "creates location and returns its info", %{conn: conn, user: user} do
      location_params = string_params_for(:location, name: "some company", cnpj: "some cnpj")

      assert %{"id" => id} =
               conn
               |> post(~p"/api/locations", location_params)
               |> json_response(201)

      assert location = Rms.Accounts.get_location!(user.organization_id, id)
      assert location.name == "some company"
      assert location.cnpj == "some cnpj"
    end

    test "creates location with address and returns its info", %{conn: conn} do
      location_params = %{
        name: "some company",
        cnpj: "some cnpj",
        address: %{
          "receiver_name" => "John Doe",
          "city_name" => "São Paulo",
          "state" => "SP",
          "country_name" => "Brazil",
          "neighborhood" => "Jardim Paulista",
          "street" => "Av. Paulista",
          "street_type" => "Avenue",
          "number" => "1000",
          "zip" => "********",
          "complement" => "Apt 1001"
        }
      }

      assert %{"id" => id} =
               conn
               |> post(~p"/api/locations", location_params)
               |> json_response(201)

      conn = get(conn, ~p"/api/locations/#{id}")

      assert %{
               "id" => ^id,
               "name" => "some company",
               "cnpj" => "some cnpj",
               "archived_at" => nil,
               "address" => %{
                 "receiver_name" => "John Doe",
                 "city_name" => "São Paulo",
                 "state" => "SP",
                 "country_name" => "Brazil",
                 "neighborhood" => "Jardim Paulista",
                 "street" => "Av. Paulista",
                 "street_type" => "Avenue",
                 "number" => "1000",
                 "zip" => "********",
                 "complement" => "Apt 1001"
               }
             } = json_response(conn, 200)
    end

    test "does not create a location for another organization", %{conn: conn, user: user} do
      other_org = insert(:organization)

      location_params = %{
        name: "unauthorized company",
        cnpj: "unauthorized cnpj",
        organization_id: other_org.id,
        address: string_params_for(:address)
      }

      assert %{"id" => location_id} =
               conn
               |> post(~p"/api/locations", location_params)
               |> json_response(201)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Accounts.get_location!(other_org.id, location_id)
      end

      assert Rms.Accounts.get_location!(user.organization_id, location_id)
    end
  end

  describe "update/2" do
    test "updates chosen location", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      update_params = %{name: "new name", external_id: "new_external_id"}

      assert conn
             |> put(~p"/api/locations/#{location.id}", update_params)
             |> json_response(200)

      updated_location = Rms.Accounts.get_location!(user.organization_id, location.id)
      assert updated_location.name == "new name"
      assert updated_location.external_id == "new_external_id"
    end

    test "does not update a location from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)
      update_params = %{name: "unauthorized update", external_id: "unauthorized_external_id"}

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> put(~p"/api/locations/#{other_org_location.id}", update_params)
        |> json_response(404)
      end

      unchanged_location = Rms.Accounts.get_location!(other_org.id, other_org_location.id)
      assert unchanged_location.name != "unauthorized update"
      assert unchanged_location.external_id != "unauthorized_external_id"
    end

    test "does not allow updating the 'organization_id' of a location", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      update_params = %{organization_id: Ecto.UUID.generate(), external_id: "new_external_id"}

      conn = put(conn, ~p"/api/locations/#{location.id}", update_params)
      assert response(conn, 200)

      updated_location = Rms.Accounts.get_location!(user.organization_id, location.id)
      assert location.organization_id == updated_location.organization_id
      assert updated_location.external_id == "new_external_id"
    end
  end

  describe "delete/2" do
    test "deletes a archived location", %{conn: conn, user: user} do
      now = DateTime.truncate(DateTime.utc_now(), :second)
      location = insert(:location, organization: user.organization, archived_at: now)
      conn = delete(conn, ~p"/api/locations/#{location.id}", %{hard: true})
      assert response(conn, 204)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Accounts.get_location!(user.organization_id, location.id)
      end
    end

    test "does not delete a non-archived location", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      conn = delete(conn, ~p"/api/locations/#{location.id}", %{hard: true})
      assert json_response(conn, 422)

      assert Rms.Accounts.get_location!(user.organization_id, location.id)
    end

    test "archives a non-archived location when 'hard' is not passed", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization)
      conn = delete(conn, ~p"/api/locations/#{location.id}")
      assert response(conn, 204)

      location =
        Rms.Accounts.get_location!(user.organization_id, location.id, allow_archived?: true)

      assert location.archived_at
    end
  end
end
