defmodule RmsWeb.NotificationControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  setup :verify_on_exit!

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "create_push_notification/2" do
    test "create a new push notification", %{conn: conn, user: user} do
      staff1 = insert(:staff, organization: user.organization, name: "<PERSON>")

      Mox.expect(Rms.Integrations.OneSignal.Mock, :send_push_notification, 1, fn _ ->
        {:ok, "test body"}
      end)

      conn =
        post(conn, ~p"/api/notification/push", %{
          title: "Test Push Notification Title",
          message: "Test Push Notification Message",
          staffs_ids: [staff1.id],
          url: "https://example.com",
          channel_id: "1"
        })

      assert %{"message" => message} = json_response(conn, 201)
      assert message == "Notification sent successfully"
    end

    test "do not create a new push notification if staff is not valid given the user location", %{
      conn: conn,
      user: _
    } do
      Mox.expect(Rms.Integrations.OneSignal.Mock, :send_push_notification, 0, fn _ ->
        {}
      end)

      conn =
        post(conn, ~p"/api/notification/push", %{
          title: "Test Push Notification Title",
          message: "Test Push Notification Message",
          staffs_ids: [1],
          url: "https://example.com",
          channel_id: "1"
        })

      assert %{"message" => message} = json_response(conn, 400)
      assert message == "Invalid staffs_ids"
    end

    test "return bad request when an unexpected error is returned by the client", %{
      conn: conn,
      user: user
    } do
      staff1 = insert(:staff, organization: user.organization, name: "Ana Paula")

      Mox.expect(Rms.Integrations.OneSignal.Mock, :send_push_notification, 1, fn _ ->
        {:error, "random error"}
      end)

      conn =
        post(conn, ~p"/api/notification/push", %{
          title: "Test Push Notification Title",
          message: "Test Push Notification Message",
          staffs_ids: [staff1.id],
          url: "https://example.com",
          channel_id: "1"
        })

      assert %{"errors" => errors} = json_response(conn, 400)
      assert errors["detail"] == "Bad Request"
    end
  end
end
