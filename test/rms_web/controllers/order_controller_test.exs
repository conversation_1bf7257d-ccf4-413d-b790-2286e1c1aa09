defmodule RmsWeb.OrderControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)
    location = insert(:location, organization: organization)
    insert(:assign_location, user: user, location: location, organization: organization)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user, location: location, organization: organization}
  end

  describe "index/2" do
    defp create_order_with_payment(attrs) do
      order =
        insert(:order, attrs)

      transaction = insert(:transaction, order: order, organization: order.organization)
      insert(:payment, transaction: transaction, organization: order.organization)

      Rms.Repo.preload(order, :transaction)
    end

    test "returns only iglu orders by default", %{conn: conn, user: user} do
      iglu_order = create_order_with_payment(organization: user.organization, source: "iglu")

      _shopify_order =
        create_order_with_payment(organization: user.organization, source: "shopify")

      conn = get(conn, ~p"/api/orders")
      json = json_response(conn, 200)

      assert length(json["orders"]) == 1
      assert hd(json["orders"])["id"] == iglu_order.id
      assert hd(json["orders"])["source"] == "iglu"
    end

    test "returns orders filtered by source=shopify", %{conn: conn, user: user} do
      _iglu_order = create_order_with_payment(organization: user.organization, source: "iglu")

      shopify_order =
        create_order_with_payment(organization: user.organization, source: "shopify")

      conn = get(conn, ~p"/api/orders?source=shopify")
      json = json_response(conn, 200)

      assert length(json["orders"]) == 1
      assert hd(json["orders"])["id"] == shopify_order.id
      assert hd(json["orders"])["source"] == "shopify"
    end

    test "returns only filtered orders by search (respecting default source)", %{
      conn: conn,
      user: user
    } do
      _orders = [
        create_order_with_payment(id: 1002, organization: user.organization, source: "iglu"),
        create_order_with_payment(id: 1003, organization: user.organization, source: "iglu"),
        create_order_with_payment(id: 1004, organization: user.organization, source: "iglu"),
        create_order_with_payment(id: 1044, organization: user.organization, source: "iglu"),
        create_order_with_payment(id: 1444, organization: user.organization, source: "iglu")
      ]

      # This one shouldn't be returned due to source even if it matches search
      create_order_with_payment(id: 2444, organization: user.organization, source: "shopify")

      conn = get(conn, ~p"/api/orders?search=4")
      json = json_response(conn, 200)

      assert length(json["orders"]) == 3
      matching_ids = [1004, 1044, 1444]
      returned_ids = Enum.map(json["orders"], & &1["id"])
      assert Enum.sort(returned_ids) == Enum.sort(matching_ids)
    end

    test "returns page information (respecting default source)", %{conn: conn, user: user} do
      # Create 23 iglu orders
      for _ <- 1..23 do
        create_order_with_payment(organization: user.organization, source: "iglu")
      end

      # Create some non-iglu orders that shouldn't affect pagination
      create_order_with_payment(organization: user.organization, source: "shopify")

      conn = get(conn, ~p"/api/orders")
      json = json_response(conn, 200)

      assert json["page"]["after"]
      assert json["page"]["limit"] == 20
      # Ensure only iglu orders are counted towards limit
      assert length(json["orders"]) == 20
    end

    test "respect page limit (respecting default source)", %{conn: conn, user: user} do
      # Create 10 iglu orders
      for _ <- 1..10 do
        create_order_with_payment(organization: user.organization, source: "iglu")
      end

      # Create some non-iglu orders
      create_order_with_payment(organization: user.organization, source: "shopify")

      conn = get(conn, ~p"/api/orders", %{limit: 5})
      json = json_response(conn, 200)

      assert json["page"]["after"]
      assert json["page"]["limit"] == 5
      assert length(json["orders"]) == 5
    end

    test "returns second page if passing after (respecting default source)", %{
      conn: conn,
      user: user
    } do
      # Create 10 iglu orders
      iglu_orders =
        for _ <- 1..10,
            do: create_order_with_payment(organization: user.organization, source: "iglu")

      # Create some non-iglu orders
      create_order_with_payment(organization: user.organization, source: "shopify")

      first_page_conn = get(conn, ~p"/api/orders", %{limit: 5})
      json = json_response(first_page_conn, 200)

      assert json["page"]["after"]
      order_ids_page1 = Enum.map(json["orders"], & &1["id"])
      cursor_after = json["page"]["after"]
      second_page_conn = get(conn, ~p"/api/orders", %{limit: 5, after: cursor_after})
      second_page_json = json_response(second_page_conn, 200)
      order_ids_page2 = Enum.map(second_page_json["orders"], & &1["id"])

      # Ensure all returned orders are from the original iglu list
      all_returned_ids = MapSet.new(order_ids_page1 ++ order_ids_page2)
      all_iglu_ids = MapSet.new(Enum.map(iglu_orders, & &1.id))
      assert MapSet.subset?(all_returned_ids, all_iglu_ids)
      assert length(MapSet.to_list(all_returned_ids)) == 10

      # Ensure pages are disjoint
      ids_set_page1 = MapSet.new(order_ids_page1)
      ids_set_page2 = MapSet.new(order_ids_page2)
      assert MapSet.disjoint?(ids_set_page1, ids_set_page2)
    end

    test "filtering by payment_method returns orders in the proper order (respecting default source)",
         %{
           conn: conn,
           user: user
         } do
      # Assuming create_order_with_payment implicitly uses source: "iglu" or we set it
      order1 = create_order_with_payment(organization: user.organization, source: "iglu")

      _p1 =
        insert(:payment,
          method: "credit_card",
          transaction: order1.transaction,
          organization: user.organization
        )

      order2 = create_order_with_payment(organization: user.organization, source: "iglu")

      _p2 =
        insert(:payment,
          method: "credit_card",
          transaction: order2.transaction,
          organization: user.organization
        )

      # Create a non-iglu order that shouldn't be returned
      other_order = create_order_with_payment(organization: user.organization, source: "shopify")

      insert(:payment,
        method: "credit_card",
        transaction: other_order.transaction,
        organization: user.organization
      )

      conn = get(conn, ~p"/api/orders", %{payment_method: "credit_card"})
      json = json_response(conn, 200)

      order1_id = order1.id
      order2_id = order2.id
      # Order matters based on insertion/ID, assuming order2 inserted later/higher ID
      returned_ids = Enum.map(json["orders"], & &1["id"])
      # Ensure only 2 orders are returned
      assert length(returned_ids) == 2
      # Adjust order based on actual insertion order if needed
      assert returned_ids == [order2_id, order1_id]
    end

    test "does not return orders that have no payments (respecting default source)", %{
      conn: conn,
      user: user
    } do
      # Order without payment (shouldn't be returned anyway)
      insert(:order, organization: user.organization, source: "iglu")
      # Order with payment but wrong source (shouldn't be returned)
      order_shopify = insert(:order, organization: user.organization, source: "shopify")

      transaction_shopify =
        insert(:transaction, order: order_shopify, organization: user.organization)

      insert(:payment, transaction: transaction_shopify, organization: user.organization)

      # Order with payment and correct source (should be returned)
      order_with_payment =
        create_order_with_payment(organization: user.organization, source: "iglu")

      conn = get(conn, ~p"/api/orders")
      json = json_response(conn, 200)

      assert length(json["orders"]) == 1
      assert hd(json["orders"])["id"] == order_with_payment.id
    end

    test "returns orders with no payments if the filter is explictly by status open", %{
      conn: conn,
      user: user
    } do
      insert(:order, organization: user.organization, source: "iglu")
      create_order_with_payment(organization: user.organization, source: "iglu")

      conn = get(conn, ~p"/api/orders", %{status: "open"})
      json = json_response(conn, 200)

      assert length(json["orders"]) == 2
    end

    test "returns orders filtered by inserted_at", %{conn: conn, user: user} do
      # Create orders with different insertion times
      yesterday = DateTime.utc_now() |> DateTime.add(-1, :day)
      today = DateTime.utc_now()
      tomorrow = DateTime.utc_now() |> DateTime.add(1, :day)

      # Order from yesterday (should be returned when filtering for yesterday)
      yesterday_order =
        create_order_with_payment(
          organization: user.organization,
          source: "iglu",
          inserted_at: yesterday
        )

      # Order from today (should not be returned when filtering for yesterday)
      _today_order =
        create_order_with_payment(
          organization: user.organization,
          source: "iglu",
          inserted_at: today
        )

      # Order from tomorrow (should not be returned when filtering for yesterday)
      _tomorrow_order =
        create_order_with_payment(
          organization: user.organization,
          source: "iglu",
          inserted_at: tomorrow
        )

      # Format the date as ISO8601 for the query parameter
      yesterday_iso = DateTime.to_iso8601(yesterday)

      conn = get(conn, ~p"/api/orders?inserted_at=#{yesterday_iso}")
      json = json_response(conn, 200)

      assert length(json["orders"]) == 1
      assert hd(json["orders"])["id"] == yesterday_order.id
    end

    test "returns 422 for invalid limit parameter", %{conn: conn} do
      conn = get(conn, ~p"/api/orders?limit=invalid")
      assert json_response(conn, 422)["errors"]
    end

    test "returns 422 for invalid inserted_at format", %{conn: conn} do
      conn = get(conn, ~p"/api/orders?inserted_at=2023-01-01")
      assert json_response(conn, 422)["errors"]
    end

    test "returns 422 for invalid location_id format", %{conn: conn} do
      conn = get(conn, ~p"/api/orders?location_id=abc")
      assert json_response(conn, 422)["errors"]
    end
  end

  describe "show/2" do
    test "returns 404 if shopify is not configured", %{conn: conn} do
      Rms.ShopifyMock.order_mock()

      id = "gid://shopify/Order/1"

      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/orders/#{id}")
      end
    end

    test "returns order data", %{conn: conn, user: user} do
      insert(:shopify_credential, organization: user.organization)
      id = "gid://shopify/Order/custom_id"
      replace_id = %{"data" => %{"order" => %{"id" => id}}}
      Rms.ShopifyMock.order_mock(replace_id)

      assert json =
               conn
               |> get(~p"/api/orders/#{id}")
               |> json_response(200)

      assert "gid://shopify/Order/custom_id" = json["order"]["external_id"]
      assert json["order"]["id"]
    end

    test "returns bad request on error when getting order", %{conn: conn, user: user} do
      insert(:shopify_credential, organization: user.organization)
      Rms.ShopifyMock.order_mock()

      id = "gid://shopify/Order/bad-request-id"

      assert conn
             |> get(~p"/api/orders/#{id}")
             |> json_response(400)
    end

    test "returns order from database when not using gid id", %{conn: conn, user: user} do
      order =
        insert(:order,
          organization: user.organization,
          customer: build(:customer, organization: user.organization),
          staff: build(:staff, organization: user.organization)
        )

      insert(:order_customer,
        order: order,
        customer: order.customer,
        organization: user.organization
      )

      assert json =
               conn
               |> get(~p"/api/orders/#{order.id}")
               |> json_response(200)

      assert order.id == json["order"]["id"]
      assert json["order"]["customer"]
      assert json["order"]["staff"]
    end

    test "returns reference_at field", %{conn: conn, user: user} do
      reference_time = DateTime.utc_now() |> DateTime.truncate(:second)
      order = insert(:order, organization: user.organization, reference_at: reference_time)

      conn = get(conn, ~p"/api/orders/#{order.id}")
      json = json_response(conn, 200)

      assert json["order"]["id"] == order.id
      assert json["order"]["reference_at"] == DateTime.to_iso8601(reference_time)
    end

    test "returns the open transaction id", %{conn: conn, user: user} do
      order = insert(:order, organization: user.organization)

      transaction =
        insert(:transaction, status: "open", order: order, organization: user.organization)

      insert(:transaction, status: "canceled", order: order, organization: user.organization)

      conn = get(conn, ~p"/api/orders/#{transaction.order_id}")
      json = json_response(conn, 200)

      assert transaction.id == json["order"]["transaction"]["id"]
    end

    test "returns the payments within a transaction", %{conn: conn, user: user} do
      order = insert(:order, organization: user.organization)
      transaction = insert(:transaction, order: order, organization: user.organization)
      payment = insert(:payment, transaction: transaction, organization: user.organization)

      conn = get(conn, ~p"/api/orders/#{order.id}")
      json = json_response(conn, 200)

      payment_id = payment.id
      assert [%{"id" => ^payment_id}] = get_in(json, ["order", "transaction", "payments"])
    end

    test "does not return order for another organization", %{conn: conn} do
      order = insert(:order, organization: insert(:organization))

      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/orders/#{order.id}")
      end
    end

    test "should return the line items fields", %{conn: conn, organization: organization} do
      staff = insert(:staff, organization: organization)
      location = insert(:location, organization: organization)
      customer = insert(:customer, organization: organization)

      order =
        insert(:order,
          organization: organization,
          location: location,
          customer: customer,
          staff: staff
        )

      product_variant =
        insert(:product_variant,
          organization: order.organization,
          product: build(:product, organization: order.organization)
        )

      image_url =
        case product_variant.image_urls do
          [] -> nil
          nil -> nil
          urls -> hd(urls)
        end

      fulfillment = insert(:fulfillment, order: order, organization: order.organization)

      _line_item =
        insert(:line_item,
          location: order.location,
          product_variant: product_variant,
          sku: product_variant.sku,
          image_url: image_url,
          variant_name: product_variant.name,
          product_name: product_variant.product.name,
          organization: order.organization,
          shipping_settings: %{
            "deliveryChannel" => "pickup-in-point",
            "pickupStoreInfo" => %{
              "friendlyName" => "Baw Birth Store"
            }
          },
          fulfillment: fulfillment
        )

      transaction = insert(:transaction, order: order, organization: order.organization)
      _payment = insert(:payment, transaction: transaction, organization: order.organization)

      _customer_transaction =
        insert(:transaction_customer,
          transaction: transaction,
          organization: order.organization
        )

      assert json =
               conn
               |> get(~p"/api/orders/#{order.id}")
               |> json_response(200)

      assert [
               %{
                 "list_price" => _,
                 "sku" => _,
                 "price" => _,
                 "quantity" => _,
                 "pickup_point_name" => _,
                 "shipping_method" => _,
                 "image_url" => "http://example.com",
                 "variant_name" => _,
                 "product_name" => _
               }
             ] = json["order"]["fulfillments"] |> Enum.flat_map(& &1["line_items"])
    end

    test "returns order with location_id", %{conn: conn, organization: organization} do
      location = insert(:location, organization: organization)
      order = insert(:order, organization: organization, location: location)

      json =
        conn
        |> get(~p"/api/orders/#{order.id}")
        |> json_response(200)

      assert json["order"]["location_id"] == order.location_id
    end
  end

  describe "get_by_df_key/2" do
    test "returns order when df_key exists", %{conn: conn, organization: organization} do
      order = insert(:order, organization: organization)
      fulfillment = insert(:fulfillment, order: order, organization: organization)

      prod_invoice_series =
        insert(:invoice_serie, invoice_env: "prod", organization: organization)

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          df_key: "123ABC",
          organization: organization,
          serie: prod_invoice_series
        )

      transaction = insert(:transaction, order: order, organization: organization)
      insert(:payment, transaction: transaction, organization: organization)

      conn = get(conn, ~p"/api/orders/by_df_key/123ABC")
      json = json_response(conn, 200)

      assert json["order"]["id"] == order.id
    end

    test "returns 404 when df_key does not exist", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/orders/by_df_key/nonexistent")
      end
    end

    test "returns 404 when df_key exists in another organization", %{conn: conn} do
      other_org = insert(:organization)
      order = insert(:order, organization: other_org)
      fulfillment = insert(:fulfillment, order: order, organization: other_org)

      _fiscal_invoice =
        insert(:fiscal_invoice,
          fulfillment: fulfillment,
          df_key: "123ABC",
          organization: other_org
        )

      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/orders/by_df_key/123ABC")
      end
    end
  end

  describe "create/2" do
    test "creates order without line items", %{conn: conn, location: loc} do
      customer = insert(:customer, organization: loc.organization)
      staff = insert(:staff, organization: loc.organization)

      cart =
        insert(:cart,
          organization: loc.organization,
          customer: customer,
          staff: staff,
          location: loc
        )

      attrs = %{"cart_id" => cart.id, "location_id" => loc.id}

      conn = post(conn, ~p"/api/orders", attrs)
      assert response = json_response(conn, :created)
      assert response["order"]
      assert response["order"]["customer"]
      assert response["order"]["staff"]
    end

    test "creates order with line items", %{conn: conn, user: user, location: loc} do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          location: loc
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      order_attrs = %{"cart_id" => cart.id, "location_id" => loc.id}

      conn = post(conn, ~p"/api/orders", order_attrs)
      assert response = json_response(conn, :created)
      assert response["order"]
    end

    test "creates order with address", %{conn: conn, user: user, location: loc} do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      address =
        insert(:address,
          organization: user.organization,
          receiver_name: "Chuck Norris",
          city_name: "São Paulo",
          state: "SP",
          country_name: "Brazil",
          neighborhood: "Jardim Paulista",
          street: "Av. Paulista",
          street_type: "Avenue",
          number: "1000",
          zip: "01310100",
          complement: "Apt 1001"
        )

      cart =
        insert(:cart,
          organization: user.organization,
          location: loc,
          shipping_address: address
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      insert(:cart_item,
        product_variant: pv,
        quantity: 1,
        organization: user.organization,
        delivery_group: delivery_group
      )

      order_attrs =
        %{"cart_id" => cart.id, "location_id" => loc.id}

      conn = post(conn, ~p"/api/orders", order_attrs)
      assert response = json_response(conn, :created)
      assert response["order"]

      assert response["order"]["shipping_address"] == %{
               "receiver_name" => "Chuck Norris",
               "city_name" => "São Paulo",
               "state" => "SP",
               "country_name" => "Brazil",
               "neighborhood" => "Jardim Paulista",
               "street" => "Av. Paulista",
               "street_type" => "Avenue",
               "number" => "1000",
               "zip" => "01310100",
               "complement" => "Apt 1001"
             }
    end

    test "creates order with discounts", %{conn: conn, user: user, location: loc} do
      pv =
        insert(:product_variant,
          organization: user.organization,
          product: build(:product, organization: user.organization)
        )

      cart =
        insert(:cart,
          organization: user.organization,
          location: loc
        )

      delivery_group =
        insert(:delivery_group,
          organization: user.organization,
          cart: cart
        )

      cart_item =
        insert(:cart_item,
          product_variant: pv,
          quantity: 1,
          organization: user.organization,
          delivery_group: delivery_group
        )

      insert(:discount,
        organization: user.organization,
        cart: cart,
        type: "fixed",
        value: "10.00",
        description: "aaaa"
      )

      insert(:discount,
        organization: user.organization,
        cart: cart,
        type: "percentage",
        value: "10.00",
        description: "bbbb"
      )

      insert(:cart_item_discount,
        type: "fixed",
        value: "10.00",
        cart_item: cart_item,
        organization: user.organization
      )

      order_attrs = %{"cart_id" => cart.id, "location_id" => loc.id}

      conn = post(conn, ~p"/api/orders", order_attrs)
      assert response = json_response(conn, :created)

      assert response["order"]["discounts"] == [
               %{
                 "type" => "fixed",
                 "value" => "10.00",
                 "description" => "aaaa"
               },
               %{"type" => "percentage", "value" => "10.00", "description" => "bbbb"}
             ]

      Enum.each(response["order"]["fulfillments"], fn fulfillment ->
        Enum.each(fulfillment["line_items"], fn line_item ->
          assert [
                   %{
                     "description" => nil,
                     "type" => "fixed",
                     "value" => "10.00"
                   }
                 ] == line_item["discounts"]
        end)
      end)
    end
  end

  describe "update/2" do
    setup %{organization: organization} do
      {:ok, order: insert(:order, organization: organization)}
    end

    test "successfully updates an order", %{conn: conn, order: order} do
      update_params = %{"name" => "Updated Order Name"}

      conn = put(conn, ~p"/api/orders/#{order.id}", update_params)
      assert response = json_response(conn, 200)

      assert response["order"]["id"] == order.id
      assert response["order"]["name"] == "Updated Order Name"
    end

    test "returns error when name is blank", %{conn: conn, order: order} do
      update_params = %{"name" => ""}

      conn = put(conn, ~p"/api/orders/#{order.id}", update_params)
      assert response = json_response(conn, 422)

      assert response["errors"]["name"] == ["can't be blank"]
    end

    test "returns error when name is nil", %{conn: conn, order: order} do
      update_params = %{"name" => nil}

      conn = put(conn, ~p"/api/orders/#{order.id}", update_params)
      assert response = json_response(conn, 422)

      assert response["errors"]["name"] == ["can't be blank"]
    end

    test "returns 404 when order not found", %{conn: conn} do
      non_existent_id = -1
      update_params = %{"name" => "doesnt matter"}

      assert_error_sent :not_found, fn ->
        put(conn, ~p"/api/orders/#{non_existent_id}", update_params)
      end
    end
  end

  describe "cancel/2" do
    setup %{organization: organization, location: location} do
      {:ok, order: insert(:order, organization: organization, location: location)}
    end

    test "successfully cancels an order", %{conn: conn, order: order} do
      Oban.Testing.with_testing_mode(:inline, fn ->
        Tesla.Mock.mock(fn _ ->
          Tesla.Mock.json(%{})
        end)

        assert response =
                 conn
                 |> post(~p"/api/orders/#{order.id}/cancel", %{
                   timeout: 500,
                   reason: "Test cancellation"
                 })
                 |> json_response(200)

        assert response["order"]["id"] == order.id
        assert response["order"]["status"] == "canceled"
        assert Enum.empty?(response["errors"])
      end)
    end

    test "returns error when cancellation fails", %{conn: conn, order: order} do
      response =
        conn
        |> post(~p"/api/orders/#{order.id}/cancel", %{timeout: 0, reason: "Test cancellation"})
        |> json_response(200)

      refute Enum.empty?(response["errors"])
    end
  end

  test "filter orders by customer document (respecting default source)", %{
    conn: conn,
    organization: org
  } do
    document = "12345678900"
    customer = insert(:customer, organization: org, document: document)
    # Matching order with correct source
    order = insert(:order, organization: org, source: "iglu")

    insert(:order_customer,
      order: order,
      customer: customer,
      organization: org,
      document: document
    )

    transaction = insert(:transaction, order: order, organization: org)
    insert(:payment, transaction: transaction, organization: org)

    # Non-matching order (different document, same source)
    other_customer = insert(:customer, organization: org, document: "09876543210")
    other_order = insert(:order, organization: org, source: "iglu")
    insert(:order_customer, order: other_order, customer: other_customer, organization: org)
    other_transaction = insert(:transaction, order: other_order, organization: org)
    insert(:payment, transaction: other_transaction, organization: org)

    # Matching document but wrong source
    shopify_customer = insert(:customer, organization: org, document: document)
    shopify_order = insert(:order, organization: org, source: "shopify")

    insert(:order_customer,
      order: shopify_order,
      customer: shopify_customer,
      organization: org,
      document: document
    )

    shopify_transaction = insert(:transaction, order: shopify_order, organization: org)
    insert(:payment, transaction: shopify_transaction, organization: org)

    conn = get(conn, ~p"/api/orders", %{search: document})
    json = json_response(conn, 200)

    # Only the iglu order should match
    assert length(json["orders"]) == 1
    assert hd(json["orders"])["id"] == order.id
  end

  test "filter orders by customer email (respecting default source)", %{
    conn: conn,
    organization: org
  } do
    email = "<EMAIL>"
    customer = insert(:customer, organization: org, email: email)
    # Matching order with correct source
    order = insert(:order, organization: org, source: "iglu")

    insert(:order_customer,
      order: order,
      customer: customer,
      organization: org,
      email: email
    )

    transaction = insert(:transaction, order: order, organization: org)
    insert(:payment, transaction: transaction, organization: org)

    # Non-matching order (different email, same source)
    other_customer = insert(:customer, organization: org, email: "<EMAIL>")
    other_order = insert(:order, organization: org, source: "iglu")
    insert(:order_customer, order: other_order, customer: other_customer, organization: org)
    other_transaction = insert(:transaction, order: other_order, organization: org)
    insert(:payment, transaction: other_transaction, organization: org)

    # Matching email but wrong source
    shopify_customer = insert(:customer, organization: org, email: email)
    shopify_order = insert(:order, organization: org, source: "shopify")

    insert(:order_customer,
      order: shopify_order,
      customer: shopify_customer,
      organization: org,
      email: email
    )

    shopify_transaction = insert(:transaction, order: shopify_order, organization: org)
    insert(:payment, transaction: shopify_transaction, organization: org)

    conn = get(conn, ~p"/api/orders", %{search: email})
    json = json_response(conn, 200)

    # Only the iglu order should match
    assert length(json["orders"]) == 1
    assert hd(json["orders"])["id"] == order.id
  end

  test "filter orders by customer phone number (respecting default source)", %{
    conn: conn,
    organization: org
  } do
    phone = "555-1234"
    customer = insert(:customer, organization: org, primary_phone_number: phone)
    # Matching order with correct source
    order = insert(:order, organization: org, source: "iglu")

    insert(:order_customer,
      order: order,
      customer: customer,
      organization: org,
      primary_phone_number: phone
    )

    transaction = insert(:transaction, order: order, organization: org)
    insert(:payment, transaction: transaction, organization: org)

    # Non-matching order (different phone, same source)
    other_customer = insert(:customer, organization: org, primary_phone_number: "555-5678")
    other_order = insert(:order, organization: org, source: "iglu")
    insert(:order_customer, order: other_order, customer: other_customer, organization: org)
    other_transaction = insert(:transaction, order: other_order, organization: org)
    insert(:payment, transaction: other_transaction, organization: org)

    # Matching phone but wrong source
    shopify_customer = insert(:customer, organization: org, primary_phone_number: phone)
    shopify_order = insert(:order, organization: org, source: "shopify")

    insert(:order_customer,
      order: shopify_order,
      customer: shopify_customer,
      organization: org,
      primary_phone_number: phone
    )

    shopify_transaction = insert(:transaction, order: shopify_order, organization: org)
    insert(:payment, transaction: shopify_transaction, organization: org)

    conn = get(conn, ~p"/api/orders", %{search: phone})
    json = json_response(conn, 200)

    # Only the iglu order should match
    assert length(json["orders"]) == 1
    assert hd(json["orders"])["id"] == order.id
  end

  test "search returns empty when no matches (respecting default source)", %{
    conn: conn,
    organization: org
  } do
    # Create test data that shouldn't match search or source filter
    customer =
      insert(:customer,
        organization: org,
        document: "11111111111",
        email: "<EMAIL>",
        primary_phone_number: "555-0000"
      )

    order = insert(:order, organization: org, source: "iglu")
    insert(:order_customer, order: order, customer: customer, organization: org)
    transaction = insert(:transaction, order: order, organization: org)
    insert(:payment, transaction: transaction, organization: org)

    conn = get(conn, ~p"/api/orders", %{search: "non-existent"})
    json = json_response(conn, 200)

    assert json["orders"] == []
  end
end
