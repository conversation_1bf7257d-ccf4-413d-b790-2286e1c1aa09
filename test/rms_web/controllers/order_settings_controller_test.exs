defmodule RmsWeb.OrderSettingsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)
    {:ok, conn: authenticate_conn(conn, user), organization: organization}
  end

  describe "update" do
    test "renders order_settings when data is valid", %{conn: conn} do
      attrs = %{prefix: "ORD-", suffix: "-2024"}

      conn =
        put(conn, ~p"/api/organizations/order_settings", order_settings: attrs)

      assert %{"id" => id} = json_response(conn, 200)["data"]

      conn = get(conn, ~p"/api/organizations/order_settings")

      assert %{
               "id" => ^id,
               "prefix" => "ORD-",
               "suffix" => "-2024"
             } = json_response(conn, 200)["data"]
    end

    test "renders errors when data is invalid", %{conn: conn} do
      attrs = %{prefix: String.duplicate("a", 6), suffix: String.duplicate("b", 6)}

      conn =
        put(conn, ~p"/api/organizations/order_settings", order_settings: attrs)

      assert json_response(conn, 422)["errors"] != %{}
    end
  end

  describe "show" do
    test "renders order_settings when it exists", %{conn: conn, organization: organization} do
      order_settings = insert(:order_settings, organization: organization)
      conn = get(conn, ~p"/api/organizations/order_settings")

      assert %{
               "id" => id,
               "prefix" => prefix,
               "suffix" => suffix
             } = json_response(conn, 200)["data"]

      assert id == order_settings.id
      assert prefix == order_settings.prefix
      assert suffix == order_settings.suffix
    end

    test "renders 404 when order_settings does not exist", %{conn: conn} do
      assert_error_sent :not_found, fn ->
        get(conn, ~p"/api/organizations/order_settings")
      end
    end
  end
end
