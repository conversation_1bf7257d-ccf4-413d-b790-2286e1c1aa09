defmodule RmsWeb.PackingControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  setup %{conn: conn} do
    org = insert(:organization)
    user = insert(:user, organization: org)
    location = insert(:location, organization: org)
    dock = insert(:dock, organization: org, location: location)
    order = insert(:order, organization: org)
    fulfillment = insert(:fulfillment, organization: org, order: order)
    product = insert(:product, organization: org)
    pv = insert(:product_variant, organization: org, product: product)

    line_item =
      insert(:line_item,
        organization: org,
        fulfillment: fulfillment,
        location: location,
        product_variant: pv
      )

    conn = authenticate_conn(conn, user)

    {:ok,
     conn: conn,
     user: user,
     organization: org,
     location: location,
     dock: dock,
     order: order,
     fulfillment: fulfillment,
     line_item: line_item,
     product_variant: pv}
  end

  describe "index/2" do
    test "returns all orders", %{
      conn: conn,
      user: user,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert_list(16, :packing,
        organization: user.organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      conn = get(conn, ~p"/api/packings")
      json = json_response(conn, 200)

      assert length(json["packings"]) == 16
    end

    test "returns page information", %{
      conn: conn,
      user: user,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert_list(27, :packing,
        organization: user.organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      conn = get(conn, ~p"/api/packings")
      json = json_response(conn, 200)

      assert json["page"]["after"]
      assert json["page"]["limit"] == 20
    end

    test "respect page limit", %{conn: conn, user: user, dock: dock, fulfillment: fulfillment} do
      insert_list(27, :packing,
        organization: user.organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      conn = get(conn, ~p"/api/packings", %{limit: 5})
      json = json_response(conn, 200)

      assert json["page"]["after"]
      assert json["page"]["limit"] == 5
    end

    test "returns second page if passing after", %{
      conn: conn,
      user: user,
      dock: dock,
      fulfillment: fulfillment
    } do
      insert_list(27, :packing,
        organization: user.organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      first_page_conn = get(conn, ~p"/api/packings", %{limit: 5})
      json = json_response(first_page_conn, 200)

      assert json["page"]["after"]
      packing_ids = Enum.map(json["packings"], & &1["id"])
      cursor_after = json["page"]["after"]
      second_page_conn = get(conn, ~p"/api/packings", %{limit: 5, after: cursor_after})
      second_page_json = json_response(second_page_conn, 200)

      ids_set = MapSet.new(packing_ids)
      second_page_ids_set = MapSet.new(Enum.map(second_page_json["packings"], & &1["id"]))
      assert MapSet.disjoint?(ids_set, second_page_ids_set)
    end

    test "filters by location_id", %{
      conn: conn,
      user: user,
      location: loc,
      dock: dock,
      fulfillment: fulfillment
    } do
      loc2 = insert(:location, organization: user.organization)
      dock2 = insert(:dock, organization: user.organization, location: loc2)

      insert_list(3, :packing,
        organization: user.organization,
        dock: dock,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      insert_list(2, :packing,
        organization: user.organization,
        dock: dock2,
        fulfillment: fulfillment,
        shipping_method: "delivery"
      )

      conn = get(conn, ~p"/api/packings", %{location_id: loc.id})
      json = json_response(conn, 200)

      assert length(json["packings"]) == 3
    end
  end

  describe "show/2" do
    test "preloads all necessary models", %{
      conn: conn,
      user: user,
      dock: dock,
      fulfillment: fulfillment,
      line_item: line_item
    } do
      packing =
        insert(:packing,
          organization: user.organization,
          dock: dock,
          fulfillment: fulfillment,
          shipping_method: "delivery",
          customer: build(:customer, organization: user.organization),
          staff: build(:staff, organization: user.organization)
        )

      _packing_item =
        insert(:packing_item,
          packing: packing,
          fulfillment: fulfillment,
          line_item: line_item,
          organization: user.organization
        )

      conn = get(conn, ~s"/api/packings/#{packing.id}")
      json = json_response(conn, 200)

      assert json["packing"]
      assert json["packing"]["dock"]
      assert json["packing"]["customer"]
      assert json["packing"]["staff"]

      Enum.each(json["packing"]["packing_items"], fn packing_item ->
        assert packing_item["line_item"]
      end)
    end
  end
end
