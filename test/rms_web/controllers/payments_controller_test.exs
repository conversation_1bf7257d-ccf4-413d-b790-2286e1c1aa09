defmodule RmsWeb.PaymentsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  alias Rms.Integrations.VTEX.Mock, as: VTEXMock

  setup :verify_on_exit!

  setup %{conn: conn} do
    org = insert(:organization)
    user = insert(:user, organization: org)
    conn = authenticate_conn(conn, user)

    {:ok, %{conn: conn, org: org, user: user}}
  end

  describe "create/2 gift card with shopify as provider" do
    setup %{org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      insert(:shopify_credential, organization: org)

      stub(Rms.Integrations.Shopify.Mock, :client, fn _, _ -> :mock_client end)

      {:ok, transaction: transaction, order: order}
    end

    test "successfully create and settle payment", %{
      conn: conn,
      transaction: transaction,
      order: order
    } do
      card_number = "123456"

      params =
        params_with_assocs(:payment,
          transaction: transaction,
          method: "gift_card",
          amount: order.total_price,
          metadata: %{
            card_number: card_number,
            provider: "shopify"
          }
        )

      gift_card_id = "gid://shopify/GiftCard/123"
      debit_transaction_id = "gid://shopify/GiftCardDebitTransaction/456"
      total_price = Decimal.to_string(order.total_price)

      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     "balance" => %{
                       "amount" => total_price,
                       "currencyCode" => "BRL"
                     },
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 debit_input ->
        assert %{
                 debitAmount: %{
                   amount: ^total_price,
                   currencyCode: "BRL"
                 }
               } = debit_input

        {:ok,
         %{
           "giftCardDebitTransaction" => %{
             "id" => debit_transaction_id,
             "amount" => %{
               "amount" => total_price,
               "currencyCode" => "BRL"
             }
           }
         }}
      end)

      Oban.Testing.with_testing_mode(:inline, fn ->
        assert response =
                 conn
                 |> post(~p"/api/payments", params)
                 |> json_response(:ok)

        assert response["status"] == "settled"
      end)
    end

    test "returns pending payment when gift card redemption fails temporarily (e.g. timeout)", %{
      conn: conn,
      transaction: transaction,
      order: order
    } do
      card_number = "987654"

      params =
        params_with_assocs(:payment,
          transaction: transaction,
          method: "gift_card",
          amount: order.total_price,
          metadata: %{
            card_number: card_number,
            provider: "shopify"
          }
        )

      # Mock fetch_gift_card to return a timeout error
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:error, :timeout}
      end)

      deny(Rms.Integrations.Shopify.Mock, :debit_gift_card, 3)

      Oban.Testing.with_testing_mode(:inline, fn ->
        conn = post(conn, ~p"/api/payments", params)

        assert response = json_response(conn, :ok)

        assert response["id"]
        assert response["status"] == "pending"
      end)
    end

    test "returns canceled payment when gift card redemption fails permanently (e.g. insufficient funds)",
         %{
           conn: conn,
           transaction: transaction,
           order: order
         } do
      card_number = "654321"

      params =
        params_with_assocs(:payment,
          transaction: transaction,
          method: "gift_card",
          amount: order.total_price,
          metadata: %{
            card_number: card_number,
            provider: "shopify"
          }
        )

      gift_card_id = "gid://shopify/GiftCard/789"
      total_price = Decimal.to_string(order.total_price)

      # Mock fetch_gift_card to find the card
      expect(Rms.Integrations.Shopify.Mock, :fetch_gift_card, fn :mock_client, ^card_number ->
        {:ok,
         %{
           "data" => %{
             "giftCards" => %{
               "edges" => [
                 %{
                   "node" => %{
                     "id" => gift_card_id,
                     # Balance is sufficient initially, but debit will fail
                     "balance" => %{"amount" => total_price, "currencyCode" => "BRL"},
                     "maskedCode" => "•••• •••• •••• #{String.slice(card_number, -4..-1)}"
                   }
                 }
               ]
             }
           }
         }}
      end)

      # Mock debit_gift_card to return insufficient funds error
      expect(Rms.Integrations.Shopify.Mock, :debit_gift_card, fn :mock_client,
                                                                 ^gift_card_id,
                                                                 _debit_input ->
        {:error,
         {:unknown_error_code,
          %{
            "code" => "INSUFFICIENT_FUNDS",
            "field" => ["debitInput", "debitAmount", "amount"],
            "message" => "Insufficient funds to perform this operation."
          }}}
      end)

      Oban.Testing.with_testing_mode(:inline, fn ->
        assert response =
                 conn
                 |> post(~p"/api/payments", params)
                 |> json_response(:ok)

        assert response["status"] == "canceled"
      end)
    end
  end

  describe "create/2" do
    test "creates a payment", %{conn: conn, org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      assert response =
               conn
               |> post(~p"/api/payments", params_with_assocs(:payment, transaction: transaction))
               |> json_response(:ok)

      assert response["id"]
    end

    test "creates a payment in an order with subscription", %{conn: conn, org: org} do
      loc = insert(:location, organization: org)

      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "prime",
            "content" => %{"vtex.subscription.key.frequency" => "12 month"}
          }
        )

      customer = insert(:customer, organization: org)

      attrs = %{
        organization_id: org.id,
        status: "open",
        total_price: addon.price,
        customer_id: customer.id,
        line_items: [],
        addons: [
          %{
            quantity: 1,
            name: "Prime",
            price: addon.price,
            list_price: addon.price,
            type: "subscription",
            image_url: "url",
            addon_id: addon.id,
            metadata: addon.metadata,
            description: addon.description
          }
        ],
        location_id: loc.id
      }

      assert {:ok, order} =
               Rms.Commerce.Orders.create_order(org.id, attrs, allowed_location_ids: :all)

      transaction = insert(:transaction, status: "open", order: order, organization: org)

      assert response =
               conn
               |> post(
                 ~p"/api/payments",
                 params_with_assocs(:payment,
                   transaction: transaction,
                   amount: order.total_price_with_addons
                 )
               )
               |> json_response(:ok)

      assert response["id"]
    end

    test "cannot create a payment that exceeds the order total amount", %{conn: conn, org: org} do
      order = insert(:order, total_price: 100, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      payment_params =
        params_with_assocs(:payment, amount: 150, transaction: transaction, organization: org)

      assert %{"errors" => %{"amount" => [error_msg]}} =
               conn
               |> post(~p"/api/payments", payment_params)
               |> json_response(:unprocessable_entity)

      assert "the total amount of payments exceeds the order total." == error_msg
    end

    test "creates a payment with gift card method", %{conn: conn, org: org} do
      order = insert(:order, organization: org, external_id: nil)

      transaction =
        insert(:transaction,
          order: order,
          organization: org,
          customer: build(:transaction_customer, organization: org)
        )

      params = %{
        "amount" => 0.12,
        "transaction_id" => transaction.id,
        "method" => "gift_card",
        "metadata" => %{
          "card_number" => "6367030104262382",
          "card_pin" => "4357",
          "provider" => "some provider"
        }
      }

      assert conn
             |> post(
               ~p"/api/payments",
               params
             )
             |> json_response(:ok)
    end

    test "creates a payment with payment_link method and pagarme partner", %{conn: conn, org: org} do
      order = insert(:order, organization: org, external_id: nil)

      transaction =
        insert(:transaction,
          order: order,
          organization: org,
          customer: build(:transaction_customer, organization: org)
        )

      insert(:pagarme_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "pagarme"}
      )

      params = params_with_assocs(:payment, transaction: transaction, method: "payment_link")

      expect(PagarMeClientMock, :create_order, fn _, _ ->
        {:ok, %{"checkouts" => [%{"payment_url" => "random link"}], "id" => "external id"}}
      end)

      assert response =
               conn
               |> post(
                 ~p"/api/payments",
                 params
               )
               |> json_response(:ok)

      assert response["id"]
      assert response["method"] == "payment_link"
      assert response["metadata"]["link"] == "random link"

      assert %{partner: "pagarme"} =
               Rms.Repo.get_by(Rms.Integrations.ExternalPaymentReference,
                 external_id: "external id"
               )
    end

    test "creates a payment with payment_link method and vtex partner", %{conn: conn, org: org} do
      loc = insert(:location, organization: org)

      product_variants =
        insert_list(3, :product_variant,
          organization: org,
          product: build(:product, organization: org)
        )

      insert_list(3, :product_variant_mapping,
        source: "vtex",
        external_id: fn _ ->
          ExMachina.sequence(:product_variant_external_id, &to_string/1, start_at: 1)
        end,
        product_variant: fn _ ->
          ExMachina.sequence(:product_variant, product_variants)
        end,
        organization: org
      )

      order =
        insert(:order,
          organization: org,
          customer: build(:customer),
          staff: build(:staff, organization: org),
          shipping_address:
            params_for(:address,
              zip: "04088004",
              city_name: "São Paulo",
              state: "SP",
              country_name: "BRA"
            ),
          fulfillments: [
            %{
              organization: org,
              shipping_method: "delivery",
              line_items:
                build_list(3, :line_item,
                  organization: org,
                  location: loc,
                  product_variant: fn _ ->
                    ExMachina.sequence(:line_item_product_variant, product_variants)
                  end
                )
            }
          ]
        )

      transaction =
        insert(:transaction,
          order: order,
          organization: org,
          customer: build(:transaction_customer, organization: org)
        )

      insert(:vtex_credential, organization: org)

      insert(:organization_setting,
        organization: org,
        key: "payment_link_integration",
        value: %{data: "vtex"}
      )

      params = params_with_assocs(:payment, transaction: transaction, method: "payment_link")

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :client, fn _, _ ->
        :mock_client
      end)

      Mox.expect(VTEXMock, :create_cart, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_marketing_data_to_cart, fn _, ^cart_id, _ ->
        {:ok, %{}}
      end)

      assert response =
               conn
               |> post(~p"/api/payments", params)
               |> json_response(:ok)

      assert response["id"]
      assert response["method"] == "payment_link"

      assert response["metadata"]["link"] =~
               "checkout?orderFormId=#{cart_id}#/cart"

      assert %{partner: "vtex"} =
               Rms.Repo.get_by(Rms.Integrations.ExternalPaymentReference,
                 external_id: cart_id
               )
    end

    test "creates a settled payment with payment_link method", %{conn: conn, org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      params =
        params_with_assocs(:payment,
          transaction: transaction,
          method: "payment_link",
          status: "settled"
        )

      assert response =
               conn
               |> post(~p"/api/payments", params)
               |> json_response(:ok)

      assert response["id"]
      assert response["method"] == "payment_link"
      assert response["status"] == "settled"
    end

    test "returns an error for a non-existent transaction", %{conn: conn, org: _org} do
      params = params_with_assocs(:payment, transaction: nil)

      assert %{"errors" => %{"transaction_id" => [error_msg]}} =
               conn
               |> post(~p"/api/payments", params)
               |> json_response(:unprocessable_entity)

      assert "can't be blank" == error_msg
    end
  end

  describe "create_subscriptions/2" do
    test "returns a payment link", %{conn: conn, org: org} do
      customer = insert(:customer, organization: org)

      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "vtex.subscription.prime",
            "content" => %{
              "vtex.subscription.key.frequency" => "6 month"
            }
          }
        )

      insert(:addon_mapping, addon: addon, organization: org, source: "vtex")

      insert(:vtex_credential, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      Mox.expect(VTEXMock, :client, 1, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, 1, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, 1, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_cart_subscriptions, 1, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, 1, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, 1, fn _, _, _ ->
        {:ok, %{}}
      end)

      assert %{"payment_link" => _payment_link} =
               conn
               |> post(~p"/api/payments/subscriptions", %{
                 addon_id: addon.id,
                 customer_id: customer.id,
                 staff_id: nil,
                 shipping_address: %{
                   city_name: "São Paulo",
                   state: "SP",
                   country_name: "Brazil",
                   neighborhood: "Jardim Paulista",
                   street: "Av. Paulista",
                   street_type: "Avenue",
                   number: "1000",
                   zip: "01310100",
                   complement: "Apt 1001"
                 }
               })
               |> json_response(:ok)
    end

    test "return error when there is no customer", %{conn: conn, org: org} do
      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "vtex.subscription.prime",
            "content" => %{
              "vtex.subscription.key.frequency" => "6 month"
            }
          }
        )

      insert(:addon_mapping, addon: addon, organization: org, source: "vtex")

      insert(:vtex_credential, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      Mox.expect(VTEXMock, :client, 1, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, 0, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_cart_subscriptions, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> post(~p"/api/payments/subscriptions", %{
          addon_id: addon.id,
          customer_id: 1,
          staff_id: nil,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        })
      end
    end

    test "return error when there is no addon", %{conn: conn, org: org} do
      customer = insert(:customer, organization: org)

      insert(:vtex_credential, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      Mox.expect(VTEXMock, :client, 1, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, 0, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_cart_subscriptions, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> post(~p"/api/payments/subscriptions", %{
          addon_id: 1,
          customer_id: customer.id,
          staff_id: nil,
          shipping_address: %{
            city_name: "São Paulo",
            state: "SP",
            country_name: "Brazil",
            neighborhood: "Jardim Paulista",
            street: "Av. Paulista",
            street_type: "Avenue",
            number: "1000",
            zip: "01310100",
            complement: "Apt 1001"
          }
        })
      end
    end

    test "return error when there is no mapping", %{conn: conn, org: org} do
      customer = insert(:customer, organization: org)

      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "vtex.subscription.prime",
            "content" => %{
              "vtex.subscription.key.frequency" => "6 month"
            }
          }
        )

      insert(:addon_mapping, addon: addon, organization: org, source: "shopify")

      insert(:vtex_credential, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      Mox.expect(VTEXMock, :client, 1, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, 0, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_cart_subscriptions, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      assert_raise RuntimeError, fn ->
        conn
        |> post(~p"/api/payments/subscriptions", %{
          addon_id: addon.id,
          customer_id: customer.id,
          staff_id: nil,
          shipping_address: nil
        })
      end
    end

    test "return error when there is no address", %{conn: conn, org: org} do
      customer = insert(:customer, organization: org)

      addon =
        insert(:addon,
          organization: org,
          metadata: %{
            "name" => "vtex.subscription.prime",
            "content" => %{
              "vtex.subscription.key.frequency" => "6 month"
            }
          }
        )

      insert(:addon_mapping, addon: addon, organization: org, source: "vtex")

      insert(:vtex_credential, organization: org)

      env_params = %{data: "vtex"}

      insert(:organization_setting,
        key: "ecommerce",
        value: env_params,
        organization: org
      )

      Mox.expect(VTEXMock, :client, 1, fn _, _ ->
        :mock_client
      end)

      cart_id = Ecto.UUID.autogenerate()

      Mox.expect(VTEXMock, :create_cart, 0, fn _ ->
        {:ok, %{"orderFormId" => cart_id}}
      end)

      Mox.expect(VTEXMock, :add_cart_items, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_cart_subscriptions, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_customer_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      Mox.expect(VTEXMock, :add_delivery_options_to_cart, 0, fn _, _, _ ->
        {:ok, %{}}
      end)

      assert_raise RuntimeError, fn ->
        conn
        |> post(~p"/api/payments/subscriptions", %{
          addon_id: addon.id,
          customer_id: customer.id,
          staff_id: nil,
          shipping_address: nil
        })
      end
    end
  end

  describe "show/2" do
    test "retrieves a payment", %{conn: conn, org: org} do
      order =
        build(:order,
          organization: org,
          location: build(:location, organization: org)
        )

      transaction =
        build(:transaction,
          organization: org,
          order: order
        )

      payment =
        insert(:payment,
          organization: org,
          transaction: transaction
        )

      assert response =
               conn
               |> get(~p"/api/payments/#{payment.id}")
               |> json_response(:ok)

      assert response["data"]["id"] == payment.id
    end

    test "returns an error for a non-existent payment", %{conn: conn} do
      assert conn
             |> get(~p"/api/payments/999999")
             |> json_response(:not_found)
             |> Map.get("errors")
             |> Map.get("detail") == "Not Found"
    end
  end

  describe "cancel/2" do
    test "cancels a payment", %{conn: conn, org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)

      assert %{"data" => response} =
               conn
               |> post(~p"/api/payments/#{payment.id}/cancel")
               |> json_response(:ok)

      assert response["status"] == "canceled"
    end

    test "cancels a payment with receipt metadata", %{conn: conn, org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)
      payment = insert(:payment, transaction: transaction, organization: org)

      receipt_data = %{
        "customer_refund_receipt" => "customer-receipt-123",
        "store_refund_receipt" => "store-receipt-456"
      }

      assert %{"data" => response} =
               conn
               |> post(~p"/api/payments/#{payment.id}/cancel", receipt_data)
               |> json_response(:ok)

      assert response["status"] == "canceled"
      assert response["metadata"]["customer_refund_receipt"] == "customer-receipt-123"
      assert response["metadata"]["store_refund_receipt"] == "store-receipt-456"
    end

    test "cancels a payment with existing metadata", %{conn: conn, org: org} do
      order = insert(:order, organization: org)
      transaction = insert(:transaction, order: order, organization: org)

      existing_metadata = %{"existing_key" => "existing_value"}

      payment =
        insert(:payment, transaction: transaction, organization: org, metadata: existing_metadata)

      receipt_data = %{
        "customer_refund_receipt" => "customer-receipt-123",
        "store_refund_receipt" => "store-receipt-456"
      }

      assert %{"data" => response} =
               conn
               |> post(~p"/api/payments/#{payment.id}/cancel", receipt_data)
               |> json_response(:ok)

      assert response["status"] == "canceled"
      assert response["metadata"]["existing_key"] == "existing_value"
      assert response["metadata"]["customer_refund_receipt"] == "customer-receipt-123"
      assert response["metadata"]["store_refund_receipt"] == "store-receipt-456"
    end

    test "returns error when payment is not found", %{conn: conn} do
      assert %{"errors" => %{"detail" => "Not Found"}} =
               conn
               |> post(~p"/api/payments/999999/cancel")
               |> json_response(:not_found)
    end
  end
end
