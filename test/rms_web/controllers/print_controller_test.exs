defmodule RmsWeb.PrintControllerTest do
  use RmsWeb.ConnCase
  import Tesla.Mock
  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    location = insert(:location, organization: user.organization)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, location: location}
  end

  describe "print" do
    test "returns 200 when print request succeeds", %{conn: conn, location: location} do
      Rms.Settings.create_location_setting(
        location.id,
        location.organization_id,
        "tailscale_printer_ip",
        "***************"
      )

      mock(fn
        %{method: :post, url: "http://***************:2345"} ->
          %Tesla.Env{status: 200, body: %{"status" => "ok"}}
      end)

      conn = post(conn, ~p"/api/remote_print/#{location.id}", %{"data" => [1, 2, 3]})
      assert response(conn, 200)
    end

    test "returns 404 when tailscale address is not configured", %{conn: conn, location: location} do
      conn = post(conn, ~p"/api/remote_print/#{location.id}", %{"data" => [1, 2, 3]})
      assert json_response(conn, 404)
    end

    test "returns 422 when print request fails", %{conn: conn, location: location} do
      Rms.Settings.create_location_setting(
        location.id,
        location.organization_id,
        "tailscale_printer_ip",
        "***************"
      )

      mock(fn
        %{method: :post, url: "http://***************:2345"} ->
          %Tesla.Env{status: 422, body: %{"error" => "Printer not ready"}}
      end)

      conn = post(conn, ~p"/api/remote_print/#{location.id}", %{"data" => [1, 2, 3]})
      assert json_response(conn, 400)
    end
  end
end
