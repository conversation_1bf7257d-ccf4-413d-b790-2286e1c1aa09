defmodule RmsWeb.ProductSyncConfigurationControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "show" do
    test "returns existing configuration", %{conn: conn, user: user} do
      config = insert(:product_sync_configuration, organization: user.organization)

      conn = get(conn, ~p"/api/product_sync/configurations")

      assert %{
               "data" => %{
                 "id" => id,
                 "field_priorities" => field_priorities,
                 "default_priority" => default_priority
               }
             } = json_response(conn, 200)

      assert id == config.id
      assert field_priorities == config.field_priorities
      assert default_priority == config.default_priority
    end

    test "returns 404 when configuration doesn't exist", %{conn: conn} do
      assert_error_sent 404, fn ->
        get(conn, ~p"/api/product_sync/configurations")
      end
    end
  end

  describe "create" do
    test "creates configuration with valid data", %{conn: conn} do
      valid_attrs = %{
        field_priorities: %{
          "name" => ["shopify", "linx_pos", "vtex"],
          "price" => ["linx_pos", "shopify", "vtex"]
        },
        default_priority: ["shopify", "linx_pos", "vtex"]
      }

      conn = post(conn, ~p"/api/product_sync/configurations", valid_attrs)
      assert %{"data" => data} = json_response(conn, 201)
      assert data["field_priorities"] == valid_attrs.field_priorities
      assert data["default_priority"] == valid_attrs.default_priority
    end

    test "returns error with invalid data", %{conn: conn} do
      invalid_attrs = %{
        field_priorities: %{"name" => "invalid"},
        default_priority: "invalid"
      }

      conn = post(conn, ~p"/api/product_sync/configurations", invalid_attrs)
      assert json_response(conn, 422)["errors"] != %{}
    end
  end

  describe "update" do
    test "updates configuration with valid data", %{conn: conn, user: user} do
      insert(:product_sync_configuration, organization: user.organization)

      update_attrs = %{
        field_priorities: %{
          "name" => ["vtex", "shopify"],
          "price" => ["shopify", "vtex"]
        },
        default_priority: ["vtex", "shopify"]
      }

      conn = put(conn, ~p"/api/product_sync/configurations", update_attrs)
      assert %{"data" => data} = json_response(conn, 200)
      assert data["field_priorities"] == update_attrs.field_priorities
      assert data["default_priority"] == update_attrs.default_priority
    end

    test "returns error with invalid data", %{conn: conn, user: user} do
      insert(:product_sync_configuration, organization: user.organization)

      invalid_attrs = %{
        field_priorities: %{"name" => "invalid"},
        default_priority: "invalid"
      }

      conn = put(conn, ~p"/api/product_sync/configurations", invalid_attrs)
      assert json_response(conn, 422)["errors"] != %{}
    end
  end
end
