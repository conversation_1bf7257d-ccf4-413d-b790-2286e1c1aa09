defmodule RmsWeb.ProductsControllerTest do
  use RmsWeb.ConnCase
  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    {:ok, conn: authenticate_conn(conn, user), user: user, organization: user.organization}
  end

  describe "index/2" do
    test "lists all products", %{conn: conn, organization: organization} do
      p1 = insert(:product, organization: organization, product_type: "item")
      p2 = insert(:product, organization: organization, product_type: "subscription")
      insert(:product_variant, product: p1, organization: organization)
      insert(:product_variant, product: p2, organization: organization)

      conn = get(conn, ~p"/api/products")
      assert json_response(conn, 200)["products"] |> length() == 2
    end

    test "lists all item products", %{conn: conn, organization: organization} do
      p1 = insert(:product, organization: organization, product_type: "item")
      p2 = insert(:product, organization: organization, product_type: "subscription")
      insert(:product_variant, product: p1, organization: organization)
      insert(:product_variant, product: p2, organization: organization)

      conn = get(conn, ~p"/api/products", %{product_types: ["item"]})
      assert json_response(conn, 200)["products"] |> length() == 1
    end

    test "does not list other organization's products", %{conn: conn} do
      other_organization = insert(:organization)
      product = insert(:product, organization: other_organization)
      insert(:product_variant, product: product, organization: other_organization)

      conn = get(conn, ~p"/api/products")
      assert json_response(conn, 200)["products"] |> length() == 0
    end

    test "returns a `page` entry with pagination metadata", %{
      conn: conn,
      organization: organization
    } do
      insert_list(10, :product, organization: organization)
      |> Enum.each(&insert(:product_variant, product: &1, organization: organization))

      first_page = get(conn, ~p"/api/products?limit=5")

      response_data = json_response(first_page, 200)
      assert length(response_data["products"]) == 5

      assert %{
               "before" => nil,
               "after" => cursor_after,
               "limit" => 5
             } = response_data["page"]

      assert cursor_after

      second_page = get(conn, ~p"/api/products?limit=5&after=#{cursor_after}")
      second_page_data = json_response(second_page, 200)
      assert response_data["products"] != second_page_data["products"]
    end

    test "allows searching for products by name", %{conn: conn, organization: organization} do
      insert(:product, name: "Searchable Product", organization: organization)
      |> then(&insert(:product_variant, product: &1, organization: organization))

      insert(:product, name: "Another Product", organization: organization)
      |> then(&insert(:product_variant, product: &1, organization: organization))

      conn = get(conn, ~p"/api/products?search=Searchable Product")

      assert [product] = json_response(conn, 200)["products"]
      assert product["name"] == "Searchable Product"
    end

    test "partial search returns multiple matches", %{conn: conn, organization: organization} do
      insert(:product, name: "Product A", organization: organization)
      |> then(&insert(:product_variant, product: &1, organization: organization))

      insert(:product, name: "Product B", organization: organization)
      |> then(&insert(:product_variant, product: &1, organization: organization))

      insert(:product, name: "Unrelated", organization: organization)
      |> then(
        &insert(:product_variant,
          name: "#{&1.name} Variant",
          product: &1,
          organization: organization
        )
      )

      conn = get(conn, ~p"/api/products?search=Product")

      products = json_response(conn, 200)["products"]
      assert length(products) == 2
      assert Enum.all?(products, fn product -> String.contains?(product["name"], "Product") end)
    end

    test "does not search other organization's products", %{conn: conn} do
      other_organization = insert(:organization)

      insert(:product, name: "Invisible Product", organization: other_organization)
      |> then(&insert(:product_variant, product: &1, organization: other_organization))

      conn = get(conn, ~p"/api/products?search=Invisible")

      assert [] = json_response(conn, 200)["products"]
    end

    test "allows searching for products by variant name", %{
      conn: conn,
      organization: organization
    } do
      product_with_variant = insert(:product, organization: organization)

      insert(:product_variant,
        product: product_with_variant,
        name: "Variant Name Search",
        organization: organization
      )

      insert(:product, organization: organization)

      conn = get(conn, ~p"/api/products?search=Variant Name Search")

      assert [product] = json_response(conn, 200)["products"]
      assert product["id"] == product_with_variant.id
    end

    test "allows searching for products by variant bar_code", %{
      conn: conn,
      organization: organization
    } do
      product_with_variant = insert(:product, organization: organization)

      insert(:product_variant,
        product: product_with_variant,
        bar_code: "123456789",
        organization: organization
      )

      insert(:product, organization: organization)

      conn = get(conn, ~p"/api/products?search=123456789")

      assert [product] = json_response(conn, 200)["products"]
      assert product["id"] == product_with_variant.id
    end

    test "prioritizes exact matches over similar products", %{
      conn: conn,
      organization: organization
    } do
      # Create a product with an exact match name
      exact_match = insert(:product, name: "Coffee M", organization: organization)
      insert(:product_variant, product: exact_match, organization: organization)

      # Create several similar products that would match by similarity
      similar_product1 = insert(:product, name: "Coffee Maker", organization: organization)
      insert(:product_variant, product: similar_product1, organization: organization)

      # Create several similar products that would match by similarity
      similar_product2 = insert(:product, name: "Coffee Mole", organization: organization)
      insert(:product_variant, product: similar_product2, organization: organization)

      similar_product3 = insert(:product, name: "Coffee Filter", organization: organization)
      insert(:product_variant, product: similar_product3, organization: organization)

      # Search for the exact term
      conn = get(conn, ~p"/api/products?search=Coffee M")
      products = json_response(conn, 200)["products"]

      # Should only return the exact match, not the similar ones
      assert length(products) == 1
      assert Enum.at(products, 0)["id"] == exact_match.id

      # Verify similar products aren't included
      product_ids = Enum.map(products, & &1["id"])
      refute similar_product1.id in product_ids
      refute similar_product2.id in product_ids
    end

    test "does not return products with no variants", %{conn: conn, organization: organization} do
      insert(:product_variant,
        organization: organization,
        product: build(:product, name: "Product with Variant", organization: organization)
      )

      insert(:product, name: "Product without Variant", organization: organization)

      conn = get(conn, ~p"/api/products?with_variants=true")

      assert [%{"name" => "Product with Variant"}] = json_response(conn, 200)["products"]
    end

    test "returns one product even if it has multiple variants", %{
      conn: conn,
      organization: organization
    } do
      product = insert(:product, organization: organization)

      insert_list(3, :product_variant, product: product, organization: organization)

      conn = get(conn, ~p"/api/products")

      product_id = product.id
      assert [%{"id" => ^product_id}] = json_response(conn, 200)["products"]
    end

    test "return default variation type even if product variant does not have any", %{
      conn: conn,
      organization: organization
    } do
      product = insert(:product, organization: organization)

      insert(:product_variant, product: product, organization: organization, variation_types: [])

      conn = get(conn, ~p"/api/products")

      product_id = product.id

      assert [
               %{
                 "id" => ^product_id,
                 "variants" => variants,
                 "variant_metadata" => variant_metadata
               }
             ] = json_response(conn, 200)["products"]

      assert %{"default" => [%{"value" => "default"}]} == variant_metadata
      assert [%{"variant_types" => [%{"default" => "default"}]}] = variants
    end
  end

  describe "show/2" do
    test "displays the product", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      conn = get(conn, ~p"/api/products/#{product.id}")

      product_id = product.id
      assert %{"id" => ^product_id} = json_response(conn, 200)
    end

    test "returns a variant_metadata", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      insert(:product_variant,
        product: product,
        organization: organization,
        variation_types: [
          %{
            key: "color",
            value: "red",
            metadata: %{"image_urls" => ["http://example.com/image1.jpg"]}
          },
          %{
            key: "size",
            value: "M"
          }
        ]
      )

      conn = get(conn, ~p"/api/products/#{product.id}")

      assert %{
               "variant_metadata" => %{
                 "color" => [
                   %{"value" => "red", "image_urls" => ["http://example.com/image1.jpg"]}
                 ],
                 "size" => [%{"value" => "M"}]
               }
             } = json_response(conn, 200)
    end

    test "raises Ecto.NoResultsError for a non-existent product", %{conn: conn} do
      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/products/123")
      end
    end

    test "raises Ecto.NoResultsError if product belongs to another organization", %{conn: conn} do
      other_organization = insert(:organization)
      product = insert(:product, organization: other_organization)

      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/products/#{product.id}")
      end
    end

    test "returns a variant_metadata order by name", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      insert(:product_variant,
        product: product,
        organization: organization,
        name: "Product Variant P",
        variation_types: [
          %{
            key: "color",
            value: "red",
            metadata: %{"image_urls" => ["http://example.com/image1.jpg"]}
          },
          %{
            key: "size",
            value: "P"
          }
        ]
      )

      insert(:product_variant,
        product: product,
        organization: organization,
        name: "Product Variant M",
        variation_types: [
          %{
            key: "color",
            value: "red",
            metadata: %{"image_urls" => ["http://example.com/image1.jpg"]}
          },
          %{
            key: "size",
            value: "M"
          }
        ]
      )

      insert(:product_variant,
        product: product,
        organization: organization,
        name: "Product Variant G",
        variation_types: [
          %{
            key: "color",
            value: "red",
            metadata: %{"image_urls" => ["http://example.com/image1.jpg"]}
          },
          %{
            key: "size",
            value: "G"
          }
        ]
      )

      conn = get(conn, ~p"/api/products/#{product.id}")

      assert %{
               "variant_metadata" => %{
                 "size" => [%{"value" => "P"}, %{"value" => "M"}, %{"value" => "G"}]
               }
             } = json_response(conn, 200)
    end
  end

  describe "get_by_barcode/2" do
    test "retrieves a product by its barcode", %{conn: conn, organization: organization} do
      product_with_barcode = insert(:product, organization: organization)

      insert(:product_variant,
        product: product_with_barcode,
        bar_code: "uniquebarcode123",
        organization: organization
      )

      conn = get(conn, ~p"/api/products/by_barcode/uniquebarcode123")

      assert json_response(conn, 200)["id"] == product_with_barcode.id

      assert [variant] = json_response(conn, 200)["variants"]
      assert "uniquebarcode123" = variant["bar_code"]
    end

    test "does not retrieve a product by barcode from another organization", %{conn: conn} do
      other_organization = insert(:organization)
      product_with_barcode = insert(:product, organization: other_organization)

      insert(:product_variant,
        product: product_with_barcode,
        bar_code: "uniquebarcode456",
        organization: other_organization
      )

      assert_raise Ecto.NoResultsError, fn ->
        get(conn, ~p"/api/products/by_barcode/uniquebarcode456")
      end
    end
  end

  describe "create/2" do
    test "creates a product", %{conn: conn} do
      params = string_params_for(:product, name: "New Product")
      conn = post(conn, ~p"/api/products", params)

      assert json_response(conn, 201)["name"] == "New Product"
    end

    test "does not allow creating a product in another organization", %{
      conn: conn,
      organization: organization
    } do
      other_organization = insert(:organization)
      params = string_params_for(:product, organization_id: other_organization.id)

      conn = post(conn, ~p"/api/products", params)

      assert id = json_response(conn, 201)["id"]
      assert product = Rms.Repo.get(Rms.Commerce.Products.Product, id)
      refute product.organization_id == other_organization.id
      assert product.organization_id == organization.id
    end
  end

  describe "update/2" do
    test "updates a product", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)
      params = %{name: "Updated Product"}

      conn = put(conn, ~p"/api/products/#{product.id}", params)

      assert json_response(conn, 200)["name"] == "Updated Product"
    end

    test "does not update organization_id", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)
      other_organization = insert(:organization)
      params = %{organization_id: other_organization.id}

      assert conn
             |> put(~p"/api/products/#{product.id}", params)
             |> response(:ok)

      assert product = Rms.Repo.get!(Rms.Commerce.Products.Product, product.id)
      refute product.organization_id == other_organization.id
      assert product.organization_id == organization.id
    end
  end

  describe "delete/2" do
    test "deletes a product", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      conn = delete(conn, ~p"/api/products/#{product.id}")

      assert response(conn, :no_content)
    end

    test "does not allow deleting the product of another organization", %{conn: conn} do
      other_organization = insert(:organization)
      product = insert(:product, organization: other_organization)

      assert_raise Ecto.NoResultsError, fn ->
        delete(conn, ~p"/api/products/#{product.id}")
      end
    end
  end

  describe "archive/2" do
    test "archive all variants of a product", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      pv1 =
        insert(:product_variant,
          product: product,
          organization: organization
        )

      pv2 =
        insert(:product_variant,
          product: product,
          organization: organization
        )

      post(conn, ~p"/api/products/archive/#{product.id}")

      Enum.each([pv1, pv2], fn pv ->
        updated_pv = Rms.Repo.get!(Rms.Commerce.Products.ProductVariant, pv.id)
        refute is_nil(updated_pv.archived_at)
      end)
    end

    test "archive only a variants of a product", %{conn: conn, organization: organization} do
      product = insert(:product, organization: organization)

      pv1 =
        insert(:product_variant,
          product: product,
          organization: organization
        )

      pv2 =
        insert(:product_variant,
          product: product,
          organization: organization
        )

      post(conn, ~p"/api/products/archive/#{product.id}?variant_ids[]=#{pv1.id}")

      updated_pv = Rms.Repo.get!(Rms.Commerce.Products.ProductVariant, pv2.id)
      assert is_nil(updated_pv.archived_at)

      updated_pv = Rms.Repo.get!(Rms.Commerce.Products.ProductVariant, pv1.id)
      refute is_nil(updated_pv.archived_at)
    end
  end

  describe "create_gift_handler_configuration/2" do
    test "create new gift_handler_configuration", %{conn: conn, organization: organization} do
      org = organization
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      pv_2 = insert(:product_variant, organization: org, product: product)
      gift_pv_2 = insert(:product_variant, organization: org, product: product)

      attrs = %{
        "Bundles" => [
          %{
            "Bundle" => [
              %{
                "Cost" => 0.1,
                "Discount" => 0.1,
                "Quantity" => 1,
                "Sku" => gift_pv.sku
              }
            ],
            "Sku" => pv.sku
          },
          %{
            "Bundle" => [
              %{
                "Cost" => 0.1,
                "Discount" => 0.1,
                "Quantity" => 1,
                "Sku" => gift_pv_2.sku
              }
            ],
            "Sku" => pv_2.sku
          }
        ]
      }

      assert %{"success" => true} ==
               conn
               |> post(~p"/api/products/gift_handler_configuration", attrs)
               |> json_response(201)
    end

    test "does not create new gift_handler_configuration", %{
      conn: conn,
      organization: organization
    } do
      org = organization
      product = insert(:product, organization: org)
      pv = insert(:product_variant, organization: org, product: product)
      gift_pv = insert(:product_variant, organization: org, product: product)

      pv_2 = insert(:product_variant, organization: org, product: product)

      attrs = %{
        "Bundles" => [
          %{
            "Bundle" => [
              %{
                "Cost" => 0.1,
                "Discount" => 0.1,
                "Quantity" => 1,
                "Sku" => gift_pv.sku
              }
            ],
            "Sku" => pv.sku
          },
          %{
            "Bundle" => [
              %{
                "Cost" => 0.1,
                "Discount" => 0.1,
                "Quantity" => 1,
                "Sku" => "gift_pv_2.sku"
              }
            ],
            "Sku" => pv_2.sku
          }
        ]
      }

      assert %{"success" => false} =
               conn
               |> post(~p"/api/products/gift_handler_configuration", attrs)
               |> json_response(422)
    end
  end
end
