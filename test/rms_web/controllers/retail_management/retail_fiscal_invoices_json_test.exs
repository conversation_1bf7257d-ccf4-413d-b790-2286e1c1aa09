defmodule RmsWeb.RetailManagement.RetailFiscalInvoicesJSONTest do
  use RmsWeb.ConnCase, async: true

  alias RmsWeb.RetailManagement.RetailFiscalInvoicesJSON
  alias Rms.Fiscal.FiscalInvoice
  alias Rms.Commerce.Orders.LineItem
  alias Rms.Commerce.Fulfillments.ReverseFulfillment
  alias Rms.Fiscal.InvoiceItem

  describe "render_reverse_fulfillment/1" do
    setup do
      fiscal_invoice = %FiscalInvoice{
        id: "fi_123",
        invoice_items: [],
        invoice_payments: [],
        reverse_fulfillment: %{
          "customer" => %{
            "id" => "customer_123"
          }
        },
        status: :active
      }

      %{fiscal_invoice: fiscal_invoice}
    end

    test "returns nil when reverse fulfillment is nil" do
      fiscal_invoice = %Rms.Fiscal.FiscalInvoice{
        id: "fi_123",
        invoice_items: [],
        invoice_payments: [],
        reverse_fulfillment: nil,
        status: :active
      }

      assert RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).reverse_fulfillment == nil
    end

    test "fiscal_invoice of type return must have reverse_fulfillment" do
      fiscal_invoice = %Rms.Fiscal.FiscalInvoice{
        id: "fi_123",
        invoice_items: [],
        invoice_payments: [],
        reverse_fulfillment: %Rms.Commerce.Fulfillments.ReverseFulfillment{
          customer: %Rms.Customers.Customer{
            name: "Customer 123",
            document_type: "CPF",
            document: "12345678900",
            email: "<EMAIL>",
            addresses: [
              %{
                city_name: "Customer City",
                state: "Customer State",
                country_name: "Customer Country",
                neighborhood: "Customer Neighborhood",
                street: "Customer Street",
                number: "123",
                zip: "12345678",
                complement: "Customer Complement"
              }
            ]
          }
        },
        operation_type: "return",
        status: :return
      }

      assert %{
               customer: %{
                 name: "Customer 123",
                 address: %{
                   state: "Customer State",
                   zip: "12345678",
                   number: "123",
                   city_name: "Customer City",
                   country_name: "Customer Country",
                   neighborhood: "Customer Neighborhood",
                   street: "Customer Street",
                   complement: "Customer Complement"
                 },
                 email: "<EMAIL>",
                 document_type: "CPF",
                 document: "12345678900"
               }
             } = RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).reverse_fulfillment
    end
  end

  describe "render_staff_id/1" do
    setup do
      fiscal_invoice = %FiscalInvoice{
        id: "fi_123",
        invoice_items: [],
        invoice_payments: [],
        reverse_fulfillment: nil,
        status: :active
      }

      %{fiscal_invoice: fiscal_invoice}
    end

    test "returns staff_id from reverse fulfillment when present", %{
      fiscal_invoice: fiscal_invoice
    } do
      fiscal_invoice = %{
        fiscal_invoice
        | reverse_fulfillment: %ReverseFulfillment{staff_id: "staff_123"}
      }

      assert RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).staff_id == "staff_123"
    end

    test "returns staff_id from first line item when present", %{fiscal_invoice: fiscal_invoice} do
      fiscal_invoice = %{
        fiscal_invoice
        | invoice_items: [%InvoiceItem{line_item: %LineItem{staff_id: "staff_456"}}]
      }

      assert RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).staff_id == "staff_456"
    end

    test "returns staff_id when there is only one line item", %{fiscal_invoice: fiscal_invoice} do
      fiscal_invoice = %{
        fiscal_invoice
        | invoice_items: [%InvoiceItem{line_item: %LineItem{staff_id: "staff_456"}}]
      }

      assert RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).staff_id == "staff_456"
    end

    test "returns nil when no line items or reverse fulfillment present", %{
      fiscal_invoice: fiscal_invoice
    } do
      assert RetailFiscalInvoicesJSON.detail_data(fiscal_invoice).staff_id == nil
    end
  end
end
