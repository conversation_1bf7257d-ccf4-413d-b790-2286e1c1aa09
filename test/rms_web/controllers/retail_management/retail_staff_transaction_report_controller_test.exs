defmodule RmsWeb.RetailManagement.RetailStaffTransactionReportControllerTest do
  use ExUnit.Case

  @moduledoc false
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)
    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "returns transactions when given valid dates and location_id", %{conn: conn, user: user} do
      organization = user.organization
      location = insert(:location, organization: organization)
      start_date = Date.utc_today()
      end_date = start_date

      staff = insert(:staff, name: "<PERSON>", organization: organization)

      order =
        insert(:order,
          organization: organization,
          location: location,
          staff: staff,
          status: "paid"
        )

      transaction = insert(:transaction, organization: organization, order: order, status: "done")

      _payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(start_date, ~T[12:00:00], "Etc/UTC")
        )

      response =
        conn
        |> get(
          "/rms/reports/staff-transactions?start_date=#{Date.to_iso8601(start_date)}&end_date=#{Date.to_iso8601(end_date)}&location_id=#{location.id}"
        )
        |> json_response(:ok)

      assert %{"data" => [transaction_data]} = response
      assert transaction_data["orders"] == 1
      assert transaction_data["staff_name"] == "John Doe"
      assert transaction_data["location_name"] == location.name
    end

    test "returns error with invalid date format", %{conn: conn} do
      response =
        conn
        |> get("/rms/reports/staff-transactions?start_date=invalid&end_date=invalid")
        |> json_response(:bad_request)

      assert %{"error" => "Invalid date format. Use ISO8601 (YYYY-MM-DD)"} = response
    end

    test "filters by location_id when provided", %{conn: conn, user: user} do
      organization = user.organization
      location1 = insert(:location, organization: organization)
      location2 = insert(:location, organization: organization)

      staff1 = insert(:staff, name: "John Doe", organization: organization)
      staff2 = insert(:staff, name: "Jane Smith", organization: organization)

      order1 =
        insert(:order,
          organization: organization,
          location: location1,
          staff: staff1,
          status: "paid"
        )

      transaction1 =
        insert(:transaction, organization: organization, order: order1, status: "done")

      _payment1 =
        insert(:payment,
          transaction: transaction1,
          organization: organization,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      order2 =
        insert(:order,
          organization: organization,
          location: location2,
          staff: staff2,
          status: "paid"
        )

      transaction2 =
        insert(:transaction, organization: organization, order: order2, status: "done")

      _payment2 =
        insert(:payment,
          transaction: transaction2,
          organization: organization,
          amount: Decimal.new("200.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      today = Date.utc_today() |> Date.to_iso8601()

      response =
        conn
        |> get(
          "/rms/reports/staff-transactions?start_date=#{today}&end_date=#{today}&location_id=#{location1.id}"
        )
        |> json_response(:ok)

      assert %{"data" => transactions} = response
      assert length(transactions) == 1
      assert Enum.all?(transactions, fn t -> t["orders"] == 1 end)
      assert Enum.all?(transactions, fn t -> t["location_name"] == location1.name end)
    end

    test "returns transactions from all locations when no location_id is provided", %{
      conn: conn,
      user: user
    } do
      organization = user.organization
      location1 = insert(:location, organization: organization)
      location2 = insert(:location, organization: organization)

      staff1 = insert(:staff, name: "John Doe", organization: organization)
      staff2 = insert(:staff, name: "Jane Smith", organization: organization)

      order1 =
        insert(:order,
          organization: organization,
          location: location1,
          staff: staff1,
          status: "paid"
        )

      transaction1 =
        insert(:transaction, organization: organization, order: order1, status: "done")

      _payment1 =
        insert(:payment,
          transaction: transaction1,
          organization: organization,
          amount: Decimal.new("100.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      order2 =
        insert(:order,
          organization: organization,
          location: location2,
          staff: staff2,
          status: "paid"
        )

      transaction2 =
        insert(:transaction, organization: organization, order: order2, status: "done")

      _payment2 =
        insert(:payment,
          transaction: transaction2,
          organization: organization,
          amount: Decimal.new("200.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      today = Date.utc_today() |> Date.to_iso8601()

      response =
        conn
        |> get("/rms/reports/staff-transactions?start_date=#{today}&end_date=#{today}")
        |> json_response(:ok)

      assert %{"data" => transactions} = response
      assert length(transactions) == 2

      staff1_transaction = Enum.find(transactions, fn t -> t["staff_name"] == "John Doe" end)
      assert staff1_transaction["location_name"] == location1.name
      assert staff1_transaction["orders"] == 1

      staff2_transaction = Enum.find(transactions, fn t -> t["staff_name"] == "Jane Smith" end)
      assert staff2_transaction["location_name"] == location2.name
      assert staff2_transaction["orders"] == 1
    end

    test "returns error for invalid date string", %{conn: conn, user: _user} do
      invalid_date = "invalid-date"
      location_id = "123"

      conn =
        get(
          conn,
          "/rms/reports/staff-transactions?start_date=#{invalid_date}&end_date=2025-01-31&location_id=#{location_id}"
        )

      assert json_response(conn, 400) == %{
               "error" => "Invalid date format. Use ISO8601 (YYYY-MM-DD)"
             }
    end

    test "cannot access data from locations that don't belong to user's organization", %{
      conn: conn
    } do
      # Create another organization
      other_organization = insert(:organization)

      # Create a location and staff for the other organization
      other_location = insert(:location, organization: other_organization)
      other_staff = insert(:staff, name: "Other Staff", organization: other_organization)

      # Create order, transaction and payment for the other organization's location
      other_order =
        insert(:order,
          organization: other_organization,
          location: other_location,
          staff: other_staff
        )

      other_transaction =
        insert(:transaction, organization: other_organization, order: other_order)

      _other_payment =
        insert(:payment,
          transaction: other_transaction,
          organization: other_organization,
          amount: Decimal.new("500.00"),
          status: "settled",
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      today = Date.utc_today() |> Date.to_iso8601()

      # Try to access the other organization's location data
      response =
        conn
        |> get(
          "/rms/reports/staff-transactions?start_date=#{today}&end_date=#{today}&location_id=#{other_location.id}"
        )
        |> json_response(:ok)

      # Should return empty data since the location doesn't belong to user's organization
      assert %{"data" => []} = response
    end
  end
end
