defmodule RmsWeb.RetailManagement.RetailTransactionsControllerTest do
  use ExUnit.Case

  @moduledoc false
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)
    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "returns transactions within date range", %{conn: conn, user: user} do
      organization = user.organization
      location = insert(:location, organization: organization)

      order = insert(:order, organization: organization, location: location)
      transaction = insert(:transaction, organization: organization, order: order)

      payment =
        insert(:payment,
          transaction: transaction,
          organization: organization,
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      old_order = insert(:order, organization: organization, location: location)
      old_transaction = insert(:transaction, organization: organization, order: old_order)

      _old_payment =
        insert(:payment,
          transaction: old_transaction,
          organization: organization,
          reference_at: ~U[2024-01-01 00:00:00Z]
        )

      today = Date.utc_today() |> Date.to_iso8601()

      response =
        conn
        |> get("/rms/reports/transactions?start_date=#{today}&end_date=#{today}")
        |> json_response(:ok)

      assert %{"data" => [transaction_data]} = response
      assert transaction_data["order_id"] == transaction.order_id
      assert Decimal.new(transaction_data["amount"]) == payment.amount
      assert transaction_data["method"] == payment.method
      assert transaction_data["status"] == payment.status
    end

    test "filters by location_id when provided", %{conn: conn, user: user} do
      organization = user.organization
      location1 = insert(:location, organization: organization)
      location2 = insert(:location, organization: organization)

      order1 = insert(:order, organization: organization, location: location1)
      transaction1 = insert(:transaction, organization: organization, order: order1)

      _payment1 =
        insert(:payment,
          transaction: transaction1,
          organization: organization,
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      order2 = insert(:order, organization: organization, location: location2)
      transaction2 = insert(:transaction, organization: organization, order: order2)

      _payment2 =
        insert(:payment,
          transaction: transaction2,
          organization: organization,
          reference_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      today = Date.utc_today() |> Date.to_iso8601()

      response =
        conn
        |> get(
          "/rms/reports/transactions?start_date=#{today}&end_date=#{today}&location_id=#{location1.id}"
        )
        |> json_response(:ok)

      assert %{"data" => transactions} = response
      assert length(transactions) == 1
      assert Enum.all?(transactions, fn t -> t["order_id"] == transaction1.order_id end)
    end

    test "returns error with invalid date format", %{conn: conn} do
      response =
        conn
        |> get("/rms/reports/transactions?start_date=invalid&end_date=invalid")
        |> json_response(:bad_request)

      assert %{"error" => "Invalid date format. Use ISO8601 (YYYY-MM-DD)"} = response
    end

    test "cannot access data from locations that don't belong to user's organization", %{
      conn: conn
    } do
      # Create another organization
      other_organization = insert(:organization)

      # Create a location for the other organization
      other_location = insert(:location, organization: other_organization)

      # Create order, transaction and payment for the other organization's location
      other_order = insert(:order, organization: other_organization, location: other_location)

      other_transaction =
        insert(:transaction, organization: other_organization, order: other_order)

      _other_payment =
        insert(:payment,
          transaction: other_transaction,
          organization: other_organization,
          amount: Decimal.new("500.00"),
          inserted_at: DateTime.new!(Date.utc_today(), ~T[12:00:00], "Etc/UTC")
        )

      today = Date.utc_today() |> Date.to_iso8601()

      # Try to access the other organization's location data
      response =
        conn
        |> get(
          "/rms/reports/transactions?start_date=#{today}&end_date=#{today}&location_id=#{other_location.id}"
        )
        |> json_response(:ok)

      # Should return empty data since the location doesn't belong to user's organization
      assert %{"data" => []} = response
    end
  end
end
