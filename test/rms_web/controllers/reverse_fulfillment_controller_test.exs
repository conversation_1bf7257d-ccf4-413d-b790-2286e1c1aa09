defmodule RmsWeb.ReverseFulfillmentControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    organization = insert(:organization)
    user = insert(:user, organization: organization)
    location = insert(:location, organization: organization)
    insert(:assign_location, user: user, location: location, organization: organization)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user, location: location, organization: organization}
  end

  describe "get_reverse_fulfillment_orders/2" do
    test "returns reverse fulfillment with associated orders", %{
      conn: conn,
      organization: organization,
      location: location
    } do
      # Create a staff member
      staff = insert(:staff, organization: organization)

      # Create a customer
      customer = insert(:customer, organization: organization)

      # Create a product and variant
      product = insert(:product, organization: organization)
      product_variant = insert(:product_variant, organization: organization, product: product)

      # Create a cart with items
      cart =
        insert(:cart,
          organization: organization,
          customer: customer,
          staff: staff,
          location: location,
          total_price: Decimal.new("50"),
          total_items_list_price: Decimal.new("50"),
          total_items_selling_price: Decimal.new("50"),
          total_manual_discount: Decimal.new("0"),
          total_delivery_price: Decimal.new("0")
        )

      delivery_group =
        insert(:delivery_group,
          cart: cart,
          organization: organization,
          fulfillment_type: "in-store"
        )

      insert(:cart_item,
        delivery_group: delivery_group,
        organization: organization,
        product_variant: product_variant,
        quantity: 5,
        list_price: "10",
        selling_price: "10",
        total_price: "50"
      )

      # Preload cart with necessary associations
      cart =
        Rms.Repo.preload(cart, [
          :discounts,
          :shipping_address,
          :cart_addons,
          delivery_groups: [cart_items: [:discounts, product_variant: :product]]
        ])

      # Create an order
      order_params = %{
        "cart_id" => cart.id,
        "location_id" => location.id
      }

      order_attrs = Rms.Commerce.Orders.format_cart(cart, order_params)

      {:ok, order} =
        Rms.Commerce.Orders.create_order(organization.id, order_attrs, allowed_location_ids: :all)

      order = Rms.Repo.preload(order, fulfillments: [line_items: [:product_variant]])
      fulfillment = List.first(order.fulfillments)
      line_item = List.first(fulfillment.line_items)

      returned_line_items = [
        %{
          "line_item_id" => line_item.id,
          "returned_quantity" => 2,
          "reason" => "defective",
          "return_to_inventory" => true
        }
      ]

      {:ok, reverse_fulfillment} =
        Rms.Commerce.Fulfillments.create_reverse_fulfillment(
          organization.id,
          returned_line_items,
          location.id,
          staff.id,
          customer.id
        )

      conn = get(conn, ~p"/api/reverse_fulfillments/#{reverse_fulfillment.id}")
      json = json_response(conn, 200)

      assert json["reverse_fulfillment"]["id"] == reverse_fulfillment.id
      assert json["reverse_fulfillment"]["location"]["id"] == location.id
      assert json["reverse_fulfillment"]["staff"]["id"] == staff.id

      assert [line_item_return] = json["reverse_fulfillment"]["line_items"]

      assert line_item_return["line_item"]["fulfillment"]["order"]["location"]["id"] ==
               location.id

      assert line_item_return["line_item"]["fulfillment"]["order"]["staff"]["id"] == staff.id
    end

    test "returns 404 when reverse fulfillment not found", %{conn: conn} do
      assert_error_sent :not_found, fn ->
        get(conn, ~p"/api/reverse_fulfillments/#{Ecto.UUID.generate()}")
      end
    end
  end
end
