defmodule RmsWeb.SettingsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "show/2 when no fields are requested" do
    test "returns no setting data when no settings exist", %{conn: conn, user: _user} do
      assert json =
               conn
               |> get(~p"/api/settings")
               |> json_response(200)

      assert %{} == json
    end

    test "returns only all organization data when no location are specified", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_2
      )

      assert json =
               conn
               |> get(~p"/api/settings")
               |> json_response(200)

      assert %{"example" => 2} == json
    end

    test "returns only the select location and organization settings when a location are specified",
         %{
           conn: conn,
           user: user
         } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example", value: %{data: 2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_2
      )

      assert json =
               conn
               |> get(~p"/api/settings?location_id=#{loc_1.id}")
               |> json_response(200)

      assert %{"another_example_loc_1" => 1.1, "example" => 2} == json
    end
  end

  describe "show/2 when fields are requested" do
    test "returns no setting data when no settings exist", %{conn: conn, user: _user} do
      assert json =
               conn
               |> get(~p"/api/settings?fields=example")
               |> json_response(200)

      assert %{} == json
    end

    test "returns only requested organization data when no location are specified", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example_1", value: %{data: 2}, organization: org)
      insert(:organization_setting, key: "example_2", value: %{data: 2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_2
      )

      assert json =
               conn
               |> get(
                 ~p"/api/settings?fields=location_another_example_loc_1,organization_example_1"
               )
               |> json_response(200)

      assert %{"example_1" => 2} == json
    end

    test "returns only the requested location and organization when a location are specified", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      _loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example_1", value: %{data: 2}, organization: org)
      insert(:organization_setting, key: "example_2", value: %{data: 2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_1
      )

      assert json =
               conn
               |> get(
                 ~p"/api/settings?location_id=#{loc_1.id}&fields=location_another_example_loc_1,organization_example_1"
               )
               |> json_response(200)

      assert %{"another_example_loc_1" => 1.1, "example_1" => 2} == json
    end
  end

  describe "show/2 when the requested location does not belongs to the organization" do
    setup %{conn: conn} do
      user = insert(:user)
      conn = authenticate_conn(conn, user)

      {:ok, conn: conn, user: user}
    end

    test "returns only the organization fields", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example_1", value: %{data: 2.1}, organization: org)
      insert(:organization_setting, key: "example_2", value: %{data: 2.2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_2
      )

      a_org = insert(:organization)
      a_loc_1 = insert(:location, organization: a_org)
      a_loc_2 = insert(:location, organization: a_org)
      insert(:organization_setting, key: "example_1", value: %{data: 4.1}, organization: a_org)
      insert(:organization_setting, key: "example_2", value: %{data: 4.2}, organization: a_org)

      insert(:location_setting,
        key: "another_example_a_loc_1",
        value: %{data: 1.1},
        location: a_loc_1
      )

      insert(:location_setting,
        key: "another_example_a_loc_2",
        value: %{data: 1.2},
        location: a_loc_2
      )

      assert json =
               conn
               |> get(~p"/api/settings?location_id=#{a_loc_1.id}")
               |> json_response(200)

      assert %{"example_1" => 2.1, "example_2" => 2.2} == json
    end

    test "returns only the requested organization fields", %{
      conn: conn,
      user: user
    } do
      org = user.organization
      loc_1 = insert(:location, organization: org)
      loc_2 = insert(:location, organization: org)
      insert(:organization_setting, key: "example_1", value: %{data: 2.1}, organization: org)
      insert(:organization_setting, key: "example_2", value: %{data: 2.2}, organization: org)

      insert(:location_setting,
        key: "another_example_loc_1",
        value: %{data: 1.1},
        location: loc_1
      )

      insert(:location_setting,
        key: "another_example_loc_2",
        value: %{data: 1.2},
        location: loc_2
      )

      a_org = insert(:organization)
      a_loc_1 = insert(:location, organization: a_org)
      a_loc_2 = insert(:location, organization: a_org)
      insert(:organization_setting, key: "example_1", value: %{data: 4.1}, organization: a_org)
      insert(:organization_setting, key: "example_2", value: %{data: 4.2}, organization: a_org)

      insert(:location_setting,
        key: "another_example_a_loc_1",
        value: %{data: 1.1},
        location: a_loc_1
      )

      insert(:location_setting,
        key: "another_example_a_loc_2",
        value: %{data: 1.2},
        location: a_loc_2
      )

      assert json =
               conn
               |> get(~p"/api/settings?location_id=#{a_loc_1.id}&fields=example_2")
               |> json_response(200)

      assert %{"example_2" => 2.2} == json
    end
  end

  describe "create/2" do
    test "creates an organization setting", %{conn: conn} do
      setting_params = %{key: "new_setting", value: "new_value", type: "organization_setting"}

      json =
        conn
        |> post("/api/settings", setting_params)
        |> json_response(201)

      assert %{"key" => "new_setting", "value" => "new_value", "type" => "organization_setting"} ==
               json

      assert [%{key: "new_setting"}] = Rms.Repo.all(Rms.Settings.OrganizationSetting)
    end

    test "creates a location setting", %{conn: conn, user: user} do
      org = user.organization
      location = insert(:location, organization: org)

      setting_params = %{
        key: "new_location_setting",
        value: "location_value",
        type: "location_setting",
        location_id: location.id
      }

      json =
        conn
        |> post("/api/settings", setting_params)
        |> json_response(201)

      assert %{
               "key" => "new_location_setting",
               "value" => "location_value",
               "type" => "location_setting",
               "location_id" => location.id
             } == json

      assert [%{key: "new_location_setting"}] = Rms.Repo.all(Rms.Settings.LocationSetting)
    end

    test "rejects location setting creation for non-owned location with 404", %{conn: conn} do
      other_org = insert(:organization)
      other_location = insert(:location, organization: other_org)

      setting_params = %{
        key: "unauthorized_location_setting",
        value: "should_fail",
        type: "location_setting",
        location_id: other_location.id
      }

      assert response =
               conn
               |> post("/api/settings", setting_params)
               |> json_response(422)

      assert [] == Rms.Repo.all(Rms.Settings.LocationSetting)
      assert %{"errors" => %{"location_id" => ["does not exist"]}} = response
    end

    test "return 422 when type is missing", %{conn: conn} do
      incomplete_params = %{key: "incomplete_setting"}

      assert response =
               conn
               |> post("/api/settings", incomplete_params)
               |> json_response(422)

      assert response["errors"]["type"] == ["is required"]
    end

    test "return 422 when type is not the expected value", %{conn: conn} do
      invalid_params = %{key: "invalid_type_setting", value: "some_value", type: "invalid_type"}

      assert response =
               conn
               |> post("/api/settings", invalid_params)
               |> json_response(422)

      assert response["errors"]["type"] == [
               "expected type to be either organization_setting or location_setting"
             ]
    end

    test "return 422 when creating a location setting without providing location_id", %{
      conn: conn
    } do
      setting_params = %{
        key: "location_setting_without_id",
        value: "some_value",
        type: "location_setting"
        # Notice that location_id is not provided here
      }

      assert response =
               conn
               |> post("/api/settings", setting_params)
               |> json_response(422)

      assert response["errors"]["location_id"] == ["is required"]
    end
  end

  describe "delete/2" do
    test "deletes an existing organization setting", %{conn: conn, user: user} do
      org = user.organization
      setting = insert(:organization_setting, organization: org)

      assert conn
             |> delete(~p"/api/settings", %{key: setting.key, type: "organization_setting"})
             |> response(:no_content)

      refute Rms.Repo.get(Rms.Settings.OrganizationSetting, setting.id)
    end

    test "returns 404 when deleting a non-existent organization setting", %{conn: conn} do
      assert conn
             |> delete(~p"/api/settings", %{key: "9999", type: "organization_setting"})
             |> json_response(404)
    end

    test "deletes an existing location setting", %{conn: conn, user: user} do
      org = user.organization
      location = insert(:location, organization: org)
      setting = insert(:location_setting, location: location)

      assert conn
             |> delete(~p"/api/settings", %{
               key: setting.key,
               type: "location_setting",
               location_id: location.id
             })
             |> response(:no_content)

      refute Rms.Repo.get(Rms.Settings.LocationSetting, setting.id)
    end

    test "returns 404 when deleting a non-existent location setting", %{conn: conn, user: user} do
      org = user.organization
      location = insert(:location, organization: org)

      assert conn
             |> delete(~p"/api/settings", %{
               key: "9999",
               location_id: location.id,
               type: "location_setting"
             })
             |> json_response(404)
    end

    test "returns 404 when deleting a location setting belonging to another org", %{conn: conn} do
      other_org = insert(:organization)
      other_location = insert(:location, organization: other_org)
      setting = insert(:location_setting, location: other_location)

      assert conn
             |> delete(~p"/api/settings", %{
               key: setting.key,
               type: "location_setting",
               location_id: other_location.id
             })
             |> json_response(404)
    end
  end

  describe "update/2" do
    test "updates an existing organization setting", %{conn: conn, user: user} do
      org = user.organization
      setting = insert(:organization_setting, organization: org)
      updated_params = %{key: setting.key, value: "updated_value", type: "organization_setting"}

      assert conn
             |> put(~p"/api/settings", updated_params)
             |> json_response(200)

      assert %{"data" => "updated_value"} ==
               Rms.Repo.get(Rms.Settings.OrganizationSetting, setting.id).value
    end

    test "returns 404 when updating a non-existent organization setting", %{conn: conn} do
      updated_params = %{key: "9999", value: "updated_value", type: "organization_setting"}

      assert conn
             |> put(~p"/api/settings", updated_params)
             |> json_response(404)
    end

    test "updates an existing location setting", %{conn: conn, user: user} do
      org = user.organization
      location = insert(:location, organization: org)
      setting = insert(:location_setting, location: location)

      updated_params = %{
        key: setting.key,
        value: "updated_location_value",
        type: "location_setting",
        location_id: location.id
      }

      assert conn
             |> put(~p"/api/settings", updated_params)
             |> json_response(200)

      assert %{"data" => "updated_location_value"} ==
               Rms.Repo.get(Rms.Settings.LocationSetting, setting.id).value
    end
  end

  describe "bulk_upsert/2" do
    test "creates multiple new settings", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)

      settings = %{
        "operating_hours" => "9 AM - 5 PM",
        "printer_ip" => "***********",
        "open_on_weekends" => true
      }

      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          location_id: loc.id,
          settings: settings
        })
        |> json_response(200)

      # Verify response contains all settings
      Enum.each(settings, fn {key, value} ->
        assert json[key] == value
      end)

      # Verify settings were created in the database
      Enum.each(settings, fn {key, value} ->
        setting = Rms.Settings.get_location_setting(org.id, loc.id, key)
        assert setting.value == %{"data" => value}
      end)
    end

    test "updates existing settings and creates new ones", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)

      # Create an existing setting
      _existing_setting =
        insert(:location_setting,
          key: "operating_hours",
          value: %{data: "9 AM - 5 PM"},
          location: loc
        )

      settings = %{
        # Update existing
        "operating_hours" => "10 AM - 6 PM",
        # Create new
        "printer_ip" => "***********"
      }

      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          location_id: loc.id,
          settings: settings
        })
        |> json_response(200)

      # Verify response
      assert json["operating_hours"] == "10 AM - 6 PM"
      assert json["printer_ip"] == "***********"

      # Verify database state
      updated_setting = Rms.Settings.get_location_setting(org.id, loc.id, "operating_hours")
      assert updated_setting.value == %{"data" => "10 AM - 6 PM"}

      new_setting = Rms.Settings.get_location_setting(org.id, loc.id, "printer_ip")
      assert new_setting.value == %{"data" => "***********"}
    end

    test "returns error when location_id is missing", %{conn: conn} do
      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          settings: %{"key" => "value"}
        })
        |> json_response(422)

      assert json["errors"]["location_id"] == ["is required"]
    end

    test "returns error when settings is missing", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)

      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          location_id: loc.id
        })
        |> json_response(422)

      assert json["errors"]["settings"] == ["is required and must be a map"]
    end

    test "returns error when settings is not a map", %{conn: conn, user: user} do
      org = user.organization
      loc = insert(:location, organization: org)

      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          location_id: loc.id,
          settings: "not a map"
        })
        |> json_response(422)

      assert json["errors"]["settings"] == ["is required and must be a map"]
    end

    test "returns error when location does not belong to user's organization", %{conn: conn} do
      other_org = insert(:organization)
      other_loc = insert(:location, organization: other_org)

      json =
        conn
        |> put(~p"/api/settings/bulk_upsert", %{
          location_id: other_loc.id,
          settings: %{"key" => "value"}
        })
        |> json_response(422)

      assert "does not exist" in json["errors"]["location_id"]
    end
  end
end
