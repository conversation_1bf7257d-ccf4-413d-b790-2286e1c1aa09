defmodule RmsWeb.SettingsJSONTest do
  use RmsWeb.ConnCase, async: true

  test "renders a empty list" do
    assert RmsWeb.SettingsJSON.render("settings.json", %{settings_list: []}) == %{}
  end

  test "renders a no empty list" do
    settings_list =
      [
        %{type: "location_setting", value: 4, key: "location_setting"},
        %{type: "organization_setting", value: 3, key: "organization_setting"}
      ]

    assert RmsWeb.SettingsJSON.render("settings.json", %{settings_list: settings_list}) == %{
             "location_setting" => 4,
             "organization_setting" => 3
           }
  end
end
