defmodule RmsWeb.StaffsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "index/2" do
    test "lists all non-archived staffs with the same organization", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      loc_2 = insert(:location, organization: user.organization)
      another_loc = insert(:location)

      staff1 = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff1, location: loc, organization: user.organization)

      staff2 = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff2, location: loc_2, organization: user.organization)

      staff3 = insert(:staff, organization: another_loc.organization)

      insert(:staff_location,
        staff: staff3,
        location: another_loc,
        organization: another_loc.organization
      )

      insert(:staff_role, staff: staff1, organization: user.organization)
      insert(:staff_role, staff: staff2, organization: user.organization)
      insert(:staff_role, staff: staff3, organization: another_loc.organization)

      %{"staffs" => [staff1, staff2]} =
        conn
        |> get(~p"/api/staffs")
        |> json_response(200)

      assert staff = Rms.Accounts.get_staff!(user.organization_id, staff1["id"])
      assert staff.external_id == staff1["external_id"]

      assert staff = Rms.Accounts.get_staff!(user.organization_id, staff2["id"])
      assert staff.external_id == staff2["external_id"]
    end

    test "lists all stafss including archived when 'allow_archived' is true", %{
      conn: conn,
      user: user
    } do
      loc = insert(:location, organization: user.organization)
      now = DateTime.truncate(DateTime.utc_now(), :second)
      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      archived_staff = insert(:staff, organization: user.organization, archived_at: now)

      insert(:staff_location,
        staff: archived_staff,
        location: loc,
        organization: user.organization
      )

      insert(:staff_role, staff: staff, organization: user.organization)
      insert(:staff_role, staff: archived_staff, organization: user.organization)

      conn = get(conn, ~p"/api/staffs", %{allow_archived: true})
      assert response = json_response(conn, 200)

      staffs_id = Enum.map(response["staffs"], & &1["id"])
      assert staff.id in staffs_id
      assert archived_staff.id in staffs_id
    end

    test "lists only non-archived staff when 'allow_archived' is false", %{
      conn: conn,
      user: user
    } do
      loc = insert(:location, organization: user.organization)
      now = DateTime.truncate(DateTime.utc_now(), :second)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      archived_staff = insert(:staff, organization: user.organization, archived_at: now)

      insert(:staff_location,
        staff: archived_staff,
        location: loc,
        organization: user.organization
      )

      insert(:staff_role, staff: staff, organization: user.organization)
      insert(:staff_role, staff: archived_staff, organization: user.organization)

      conn = get(conn, ~p"/api/staffs", %{allow_archived: false})
      assert response = json_response(conn, 200)

      staffs_id = Enum.map(response["staffs"], & &1["id"])
      assert staff.id in staffs_id
      refute archived_staff.id in staffs_id
    end

    test "lists all staffs with the same params", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      loc_2 = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization, external_id: "1")
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)
      insert(:staff_role, staff: staff, organization: user.organization)

      staff1 = insert(:staff, organization: user.organization, external_id: "2")
      insert(:staff_location, staff: staff1, location: loc, organization: user.organization)
      insert(:staff_role, staff: staff1, organization: user.organization)

      staff = insert(:staff, organization: user.organization, external_id: "3")
      insert(:staff_location, staff: staff, location: loc_2, organization: user.organization)
      insert(:staff_role, staff: staff, organization: user.organization)

      staff = insert(:staff, organization: user.organization, external_id: "4")
      insert(:staff_location, staff: staff, location: loc_2, organization: user.organization)
      insert(:staff_role, staff: staff, organization: user.organization)

      %{"staffs" => [staff1]} =
        conn
        |> get(~p"/api/staffs", %{location_id: loc.id, staff_external_id: 2})
        |> json_response(200)

      assert staff = Rms.Accounts.get_staff!(user.organization_id, staff1["id"])
      assert staff.external_id == staff1["external_id"]
    end

    test "when staff is in multiple locations, should not duplicate in listing", %{
      conn: conn,
      user: user
    } do
      loc = insert(:location, organization: user.organization)
      loc_2 = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization, name: "John Doe")
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc_2, organization: user.organization)

      insert(:staff_role, staff: staff, organization: user.organization)

      conn = get(conn, ~p"/api/staffs", %{staff_name: "John Doe"})
      assert response = json_response(conn, 200)

      assert length(response["staffs"]) == 1
      [staff_response] = response["staffs"]
      assert staff_response["id"] == staff.id
      assert staff_response["name"] == "John Doe"
      assert length(staff_response["location_ids"]) == 2
      assert Enum.sort(staff_response["location_ids"]) == Enum.sort([loc.id, loc_2.id])

      conn = get(conn, ~p"/api/staffs", %{staff_name: "Jane Doe"})
      assert response = json_response(conn, 200)

      assert Enum.empty?(response["staffs"])
    end

    test "returns page information", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      staffs = insert_list(10, :staff, organization: user.organization)

      Enum.each(staffs, fn staff ->
        insert(:staff_location, staff: staff, location: loc, organization: user.organization)
        insert(:staff_role, staff: staff, organization: user.organization)
      end)

      conn = get(conn, ~p"/api/staffs", %{limit: 5})
      assert page = json_response(conn, 200)["page"]

      assert page["after"]
      assert page["limit"] == 5
    end

    test "pagination returns different records for each page", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      staffs = insert_list(10, :staff, organization: user.organization)

      Enum.each(staffs, fn staff ->
        insert(:staff_location, staff: staff, location: loc, organization: user.organization)
        insert(:staff_role, staff: staff, organization: user.organization)
      end)

      # First page
      first_page_conn = get(conn, ~p"/api/staffs", %{limit: 5})
      first_page_response = json_response(first_page_conn, 200)

      assert length(first_page_response["staffs"]) == 5
      assert first_page_response["page"]["limit"] == 5
      assert after_cursor = first_page_response["page"]["after"]

      first_page_ids = Enum.map(first_page_response["staffs"], & &1["id"])

      # Second page
      second_page_conn = get(conn, ~p"/api/staffs", %{limit: 5, after: after_cursor})
      second_page_response = json_response(second_page_conn, 200)

      assert length(second_page_response["staffs"]) == 5
      second_page_ids = Enum.map(second_page_response["staffs"], & &1["id"])

      # Ensure no overlap between pages
      refute Enum.empty?(first_page_ids -- second_page_ids)
      assert length(Enum.uniq(first_page_ids ++ second_page_ids)) == 10
    end
  end

  describe "show/2" do
    test "shows chosen staff with staff_locations properly rendered", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      loc_2 = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)

      staff_location_1 =
        insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      staff_location_2 =
        insert(:staff_location, staff: staff, location: loc_2, organization: user.organization)

      conn = get(conn, ~p"/api/staffs/#{staff.id}")
      response = json_response(conn, 200)

      assert response["id"] == staff.id
      assert length(response["staff_locations"]) == 2

      staff_locations = Enum.sort_by(response["staff_locations"], & &1["id"])

      assert [
               %{
                 "id" => staff_location_1.id,
                 "location_id" => loc.id
               },
               %{
                 "id" => staff_location_2.id,
                 "location_id" => loc_2.id
               }
             ] == staff_locations

      assert response["location_ids"] == [loc.id, loc_2.id]
    end

    test "shows chosen staff", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      conn = get(conn, ~p"/api/staffs/#{staff.id}")
      assert json_response(conn, 200)["id"] == staff.id
    end

    test "does not show staff from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)

      staff = insert(:staff, organization: other_org)
      insert(:staff_location, staff: staff, location: other_org_location, organization: other_org)

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/staffs/#{staff.id}")
        |> json_response(404)
      end
    end
  end

  describe "create/2" do
    test "creates staff and returns its info", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff_params = %{
        "staff" => %{
          "name" => "some staff",
          "external_id" => "some external_id",
          "location_ids" => [loc.id],
          "staff_locations" => [
            %{
              "location_id" => loc.id
            }
          ],
          "staff_role" => %{
            "seller" => true,
            "stocker" => true,
            "cashier" => true
          }
        }
      }

      assert %{"id" => id} =
               conn
               |> post(~p"/api/staffs", staff_params)
               |> json_response(201)

      assert staff = Rms.Accounts.get_staff!(user.organization_id, id, preloads: [:staff_role])
      assert staff.name == "some staff"
      assert staff.external_id == "some external_id"
      assert %Rms.Accounts.StaffRole{seller: true, stocker: true} = staff.staff_role
    end

    test "does not create a staff when a location is not informed", %{
      conn: conn
    } do
      staff_params = %{
        "staff" => %{
          "name" => "some staff",
          "external_id" => "some external_id",
          "staff_role" => %{
            "seller" => true,
            "stocker" => true
          }
        }
      }

      assert %{"errors" => %{"staff_locations" => ["can't be blank"]}} =
               conn
               |> post(~p"/api/staffs", staff_params)
               |> json_response(422)
    end

    test "does not create a staff when a staff_role is not informed", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff_params = %{
        "staff" => %{
          "name" => "some staff",
          "external_id" => "some external_id",
          "location_ids" => [loc.id],
          "staff_locations" => [
            %{
              "location_id" => loc.id
            }
          ]
        }
      }

      assert %{"errors" => %{"staff_role" => ["can't be blank"]}} =
               conn
               |> post(~p"/api/staffs", staff_params)
               |> json_response(422)
    end

    test "does not create a staff for a location of another organization", %{
      conn: conn
    } do
      other_org = insert(:organization)

      loc = insert(:location, organization: other_org)

      staff_params = %{
        "staff" => %{
          "name" => "some staff",
          "external_id" => "some external_id",
          "staff_locations" => [
            %{
              "location_id" => loc.id
            }
          ],
          "staff_role" => %{
            "seller" => true,
            "stocker" => true,
            "cashier" => false
          }
        }
      }

      assert %{"errors" => %{"staff_locations" => [%{"location_id" => ["does not exist"]}]}} =
               conn
               |> post(~p"/api/staffs", staff_params)
               |> json_response(422)
    end

    test "creates staff with erp_id", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      params = %{
        "staff" => %{
          "name" => "John Doe",
          "external_id" => "STAFF123",
          "erp_id" => "ERP123",
          "staff_locations" => [
            %{
              "location_id" => loc.id
            }
          ],
          "staff_role" => %{
            "seller" => true,
            "cashier" => true,
            "stocker" => true
          }
        }
      }

      response =
        conn
        |> post(~p"/api/staffs", params)
        |> json_response(201)

      assert response["erp_id"] == "ERP123"
      assert staff = Rms.Accounts.get_staff!(user.organization_id, response["id"])
      assert staff.erp_id == "ERP123"
    end

    test "updates staff erp_id", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)
      insert(:staff_role, staff: staff, organization: user.organization)

      params = %{
        "staff" => %{
          "name" => staff.name,
          "external_id" => staff.external_id,
          "erp_id" => "NEW_ERP_ID",
          "staff_role" => %{
            "seller" => true,
            "cashier" => true,
            "stocker" => true
          }
        }
      }

      response =
        conn
        |> put(~p"/api/staffs/#{staff.id}", params)
        |> json_response(200)

      assert response["erp_id"] == "NEW_ERP_ID"
      assert updated_staff = Rms.Accounts.get_staff!(user.organization_id, staff.id)
      assert updated_staff.erp_id == "NEW_ERP_ID"
    end
  end

  describe "update/2" do
    test "updates chosen staff", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      update_params = %{
        "staff" => %{
          "name" => "new name"
        }
      }

      assert conn
             |> put(~p"/api/staffs/#{staff.id}", update_params)
             |> json_response(200)

      assert Rms.Accounts.get_staff!(user.organization_id, staff.id).name == "new name"
    end

    test "updates chosen staff with new role", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      insert(:staff_role,
        staff: staff,
        organization: user.organization,
        seller: false,
        stocker: false
      )

      update_params = %{
        "staff" => %{
          "staff_role" => %{
            "seller" => true,
            "stocker" => true
          }
        }
      }

      assert conn
             |> put(~p"/api/staffs/#{staff.id}", update_params)
             |> json_response(200)

      staff = Rms.Accounts.get_staff!(user.organization_id, staff.id, preloads: [:staff_role])

      assert %Rms.Accounts.StaffRole{seller: true, stocker: true} = staff.staff_role
    end

    test "does not update a staff from another organization", %{conn: conn} do
      other_org = insert(:organization)
      other_org_location = insert(:location, organization: other_org)

      other_staff = insert(:staff, organization: other_org)

      insert(:staff_location,
        staff: other_staff,
        location: other_org_location,
        organization: other_org
      )

      update_params = %{
        "staff" => %{
          "name" => "unauthorized update"
        }
      }

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> put(~p"/api/staffs/#{other_staff.id}", update_params)
        |> json_response(404)
      end

      assert Rms.Accounts.get_staff!(other_org.id, other_staff.id).name !=
               "unauthorized update"
    end
  end

  describe "delete/2" do
    test "deletes a archived staff", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)
      now = DateTime.truncate(DateTime.utc_now(), :second)

      archived_staff = insert(:staff, organization: user.organization, archived_at: now)

      insert(:staff_location,
        staff: archived_staff,
        location: loc,
        organization: user.organization
      )

      conn = delete(conn, ~p"/api/staffs/#{archived_staff.id}", %{hard: true})
      assert response(conn, 204)

      assert_raise Ecto.NoResultsError, fn ->
        Rms.Accounts.get_staff!(user.organization_id, archived_staff.id)
      end
    end

    test "does not delete a non-archived staff", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      conn = delete(conn, ~p"/api/staffs/#{staff.id}", %{hard: true})
      assert json_response(conn, 422)

      assert Rms.Accounts.get_staff!(user.organization_id, staff.id)
    end

    test "archives a non-archived location when 'hard' is not passed", %{conn: conn, user: user} do
      loc = insert(:location, organization: user.organization)

      staff = insert(:staff, organization: user.organization)
      insert(:staff_location, staff: staff, location: loc, organization: user.organization)

      conn = delete(conn, ~p"/api/staffs/#{staff.id}")
      assert response(conn, 204)

      staff =
        Rms.Accounts.get_staff!(user.organization_id, staff.id, allow_archived?: true)

      assert staff.archived_at
    end
  end
end
