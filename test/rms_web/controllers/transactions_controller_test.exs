defmodule RmsWeb.TransactionsControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory
  import Mox

  alias Rms.ShopifyMock

  setup :verify_on_exit!

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)

    {:ok, conn: conn, user: user}
  end

  describe "show/2" do
    test "returns the specified transaction", %{conn: conn, user: user} do
      organization = user.organization
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, organization: organization, order: order)
      payment = insert(:payment, transaction: transaction, organization: organization)

      transaction_id = transaction.id

      assert %{"id" => ^transaction_id, "payments" => payments} =
               conn
               |> get(~p"/api/transactions/#{transaction.id}")
               |> json_response(:ok)

      payment_id = payment.id
      assert [%{"id" => ^payment_id}] = payments
    end

    test "returns an error when the transaction does not exist", %{conn: conn} do
      nonexistent_transaction_id = 123_123

      assert_raise Ecto.NoResultsError, fn ->
        conn
        |> get(~p"/api/transactions/#{nonexistent_transaction_id}")
      end
    end
  end

  describe "create/2" do
    test "creates payment link from order with no external_id", %{conn: conn, user: user} do
      insert(:pagarme_credential, organization: user.organization)

      insert(:organization_setting,
        organization: user.organization,
        key: "payment_link_integration",
        value: %{data: "pagarme"}
      )

      expect(PagarMeClientMock, :create_order, fn _, _ ->
        {:ok, %{"checkouts" => [%{"payment_url" => "random link"}], "id" => "external id"}}
      end)

      order = insert(:order, organization: user.organization, external_id: nil)
      params = params_for(:transaction, order_id: order.id, payments: [%{method: "payment_link"}])

      assert %{"payment_link" => payment_link} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:created)

      assert payment_link
    end

    test "creates payment link from order with external_id", %{conn: conn, user: user} do
      ShopifyMock.order_mock(%{
        "data" => %{
          "order" => %{
            "currentTotalPriceSet" => %{
              "shopMoney" => %{
                "amount" => "10.53"
              }
            },
            "customer" => nil,
            "lineItems" => %{
              "edges" => [
                %{
                  "node" => %{
                    "name" => "Nadica de Nada",
                    "currentQuantity" => 1,
                    "sku" => "something",
                    "discountedUnitPriceAfterAllDiscountsSet" => %{
                      "shopMoney" => %{
                        "amount" => "9.0"
                      }
                    }
                  }
                }
              ]
            }
          }
        }
      })

      Mox.expect(PagarMeClientMock, :create_order, fn _, _ ->
        {:ok, %{"checkouts" => [%{"payment_url" => "random link"}], "id" => "external id"}}
      end)

      insert(:shopify_credential, organization: user.organization)
      insert(:pagarme_credential, organization: user.organization)

      insert(:organization_setting,
        organization: user.organization,
        key: "payment_link_integration",
        value: %{data: "pagarme"}
      )

      order =
        insert(:order,
          organization: user.organization,
          external_id: "gid://shopify/Order/random_id"
        )

      params = params_for(:transaction, order_id: order.id, payments: [%{method: "payment_link"}])

      assert %{"payment_link" => payment_link} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:created)

      assert payment_link
    end

    test "allows creating a transaction with no customer and payment", %{conn: conn, user: user} do
      organization = user.organization
      order = insert(:order, organization: organization)
      params = params_for(:transaction, order_id: order.id)

      assert %{"id" => transaction_id} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:ok)

      assert transaction_id
    end

    test "allows creating a transaction with no payment", %{conn: conn, user: user} do
      organization = user.organization
      order = insert(:order, organization: organization)

      params =
        params_with_assocs(:transaction, order: order, customer: build(:transaction_customer))

      assert %{"id" => transaction_id} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:ok)

      assert transaction_id
    end

    test "allows creating a transaction with a single payment", %{conn: conn, user: user} do
      payment = build(:payment)
      organization = user.organization
      order = insert(:order, organization: organization)
      params = params_with_assocs(:transaction, order: order, payments: [payment])

      assert %{"id" => transaction_id, "payments" => payments} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:ok)

      assert transaction_id
      assert length(payments) == 1
    end

    test "allows creating a transaction with multiple payments", %{conn: conn, user: user} do
      payments = build_pair(:payment)
      organization = user.organization
      order = insert(:order, organization: organization)
      params = params_with_assocs(:transaction, order: order, payments: payments)

      assert %{"id" => transaction_id, "payments" => payments} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:ok)

      assert transaction_id
      assert length(payments) == 2
    end

    test "requires nsu and aut for settled credit_card payments", %{conn: conn} do
      payment = build(:payment, method: "credit_card", status: "settled")
      organization = insert(:organization)
      params = params_with_assocs(:transaction, payments: [payment], organization: organization)

      assert %{"errors" => %{"payments" => [payment_errors]}} =
               conn
               |> post(~p"/api/transactions", params)
               |> json_response(:unprocessable_entity)

      assert {_, ["aut can't be nil for settled payments."]} =
               Enum.find(payment_errors, &(elem(&1, 0) == "metadata.aut"))

      assert {_, ["nsu can't be nil for settled payments."]} =
               Enum.find(payment_errors, &(elem(&1, 0) == "metadata.nsu"))
    end
  end

  describe "close/2" do
    test "can't close transaction that doesn't have enough settled payments", %{
      conn: conn,
      user: user
    } do
      organization = user.organization
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      assert %{
               "errors" => %{
                 "status" => ["sum of settled payments is not enough to close transaction"]
               }
             } =
               conn
               |> post(~p"/api/transactions/#{transaction.id}/close")
               |> json_response(:unprocessable_entity)
    end

    test "closes transaction that has enough settled payments", %{conn: conn, user: user} do
      organization = user.organization
      order = insert(:order, organization: organization)

      transaction =
        insert(:transaction, order: order, organization: organization)

      insert(:payment,
        method: "cash",
        amount: order.total_price,
        transaction: transaction,
        status: "settled",
        organization: organization
      )

      assert conn
             |> post(~p"/api/transactions/#{transaction.id}/close")
             |> json_response(:ok)

      assert %{status: "done"} = Rms.Repo.reload(transaction)
    end
  end

  describe "cancel/2" do
    test "cancels an open transaction", %{conn: conn, user: user} do
      organization = user.organization
      order = insert(:order, status: "open", organization: organization)
      transaction = insert(:transaction, status: "open", order: order, organization: organization)

      assert %{"status" => "canceled"} =
               conn
               |> post(~p"/api/transactions/#{transaction.id}/cancel")
               |> json_response(:ok)

      assert %{status: "canceled"} = Rms.Repo.reload(transaction)
    end

    test "returns an error when trying to cancel a transaction that is already closed", %{
      conn: conn,
      user: user
    } do
      organization = user.organization
      order = insert(:order, status: "open", organization: organization)
      transaction = insert(:transaction, status: "done", order: order, organization: organization)

      assert %{"errors" => %{"status" => status_errors}} =
               conn
               |> post(~p"/api/transactions/#{transaction.id}/cancel")
               |> json_response(:unprocessable_entity)

      assert "status transition not allowed" in status_errors
    end

    # test "returns an error when trying to cancel a transaction with settled payments", %{
    #   conn: conn,
    #   user: user
    # } do
    #   organization = user.organization
    #   order = insert(:order, status: "open", organization: organization)
    #   transaction = insert(:transaction, status: "open", order: order, organization: organization)
    #   insert(:payment, transaction: transaction, organization: organization, status: "settled")

    #   assert %{"errors" => %{"payments" => [payment_errors]}} =
    #            conn
    #            |> post(~p"/api/transactions/#{transaction.id}/cancel")
    #            |> json_response(:unprocessable_entity)

    #   assert "status transition not allowed" in payment_errors["status"]
    # end
  end
end
