defmodule RmsWeb.UsersControllerTest do
  use RmsWeb.ConnCase

  import Rms.Factory

  setup %{conn: conn} do
    user = insert(:user)
    conn = authenticate_conn(conn, user)
    {:ok, conn: conn, user: user}
  end

  describe "assign_location/2" do
    test "assign a location_id to a user from the same org", %{conn: conn, user: user} do
      location = insert(:location, organization: user.organization, address: build(:address))

      conn = post(conn, ~p"/api/users/locations", %{location_id: location.id, user_id: user.id})
      location_id = location.id
      assert %{"locations" => [%{"id" => ^location_id}]} = json_response(conn, :created)
    end

    test "cannot assign a location outside of their organization to a user", %{
      conn: conn,
      user: user
    } do
      different_organization = insert(:organization)
      outside_location = insert(:location, organization: different_organization)

      conn =
        post(conn, ~p"/api/users/locations", %{location_id: outside_location.id, user_id: user.id})

      assert json_response(conn, :unprocessable_entity) == %{
               "errors" => %{"location" => ["does not exist"]}
             }
    end

    test "cannot assign a location to a user that's outside the organization", %{
      conn: conn,
      user: user
    } do
      another_organization = insert(:organization)
      outside_org_location = insert(:location, organization: another_organization)

      conn =
        post(conn, ~p"/api/users/locations", %{
          location_id: outside_org_location.id,
          user_id: user.id
        })

      assert json_response(conn, :unprocessable_entity) == %{
               "errors" => %{"location" => ["does not exist"]}
             }
    end
  end
end
