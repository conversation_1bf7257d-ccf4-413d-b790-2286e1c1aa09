defmodule RmsWeb.Webhooks.CieloControllerTest do
  use RmsWeb.ConnCase

  describe "notify_payment/2" do
    test "returns 200 when receiving a valid form data payload", %{conn: conn} do
      params = %{
        "order_number" => "1d0099a3422f445da724",
        "amount" => "10000",
        "checkout_cielo_order_number" => "3e8728a3370a4ab3a199",
        "created_date" => "17/11/2024 16:05:28",
        "customer_name" => "<PERSON>",
        "customer_phone" => "71991764326",
        "customer_identity" => "05774088529",
        "customer_identity_type" => "0",
        "customer_email" => "<EMAIL>",
        "shipping_type" => "5",
        "shipping_price" => "0",
        "payment_method_type" => "1",
        "payment_method_brand" => "1",
        "payment_maskedcreditcard" => "400000******1091",
        "payment_installments" => "1",
        "payment_status" => "2",
        "tid" => "171120241605284246",
        "test_transaction" => "True",
        "product_id" => "c46a7b48-2ce3-4f1e-b5e3-30e0c85e8516",
        "product_type" => "4",
        "nsu" => "999999",
        "authorization_code" => "000000"
      }

      response =
        conn
        |> put_req_header("content-type", "application/x-www-form-urlencoded")
        |> post(~p"/webhooks/cielo/notify_payment", params)
        |> json_response(200)

      assert response == %{}
    end
  end
end
