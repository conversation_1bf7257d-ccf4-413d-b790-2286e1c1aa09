defmodule RmsWeb.Webhooks.PagarMeControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory

  setup %{conn: conn} do
    conn = using_basic_auth(conn, "iglu_test", "iglu_test")
    {:ok, conn: conn}
  end

  describe "create/2" do
    test "enqueue a worker to settle a payment", %{conn: conn} do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "authorized",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      phc =
        insert(:pagarme_hook_credential,
          credential: "iglu_test:iglu_test",
          credential_hash: "iglu_test:iglu_test",
          organization: order.organization
        )

      params = set_params(phc.shop_id, epr.external_id, "paid")

      assert %{"success" => true} =
               conn
               |> post(~p"/webhooks/pagarme", params)
               |> json_response(:ok)

      assert_enqueued(
        worker: Rms.Workers.PagarMeWorker,
        args: %{payload: params, org_id: order.organization_id}
      )
    end

    test "enqueue a worker to cancel a payment", %{conn: conn} do
      organization = insert(:organization)
      order = insert(:order, organization: organization)
      transaction = insert(:transaction, order: order, organization: organization)

      payment =
        insert(:payment,
          method: "payment_link",
          amount: order.total_price,
          status: "authorized",
          transaction: transaction,
          organization: organization
        )

      epr =
        insert(:external_payment_reference,
          partner: "pagarme",
          payment: payment,
          organization: organization
        )

      phc =
        insert(:pagarme_hook_credential,
          credential: "iglu_test:iglu_test",
          credential_hash: "iglu_test:iglu_test",
          organization: order.organization
        )

      params = set_params(phc.shop_id, epr.external_id, "canceled")

      assert %{"success" => true} =
               conn
               |> post(~p"/webhooks/pagarme", params)
               |> json_response(:ok)

      assert_enqueued(
        worker: Rms.Workers.PagarMeWorker,
        args: %{payload: params, org_id: order.organization_id}
      )
    end
  end

  defp set_params(shop_id, external_id, status) do
    %{
      "account" => %{"id" => "#{shop_id}", "name" => "kvit - test"},
      "created_at" => "2024-03-17T00:54:31.207Z",
      "data" => %{
        "amount" => 100,
        "charges" => [],
        "checkouts" => [],
        "closed" => false,
        "code" => "JQWWT9XUZE",
        "created_at" => "2024-03-15T00:53:59",
        "currency" => "BRL",
        "customer" => %{},
        "id" => "#{external_id}",
        "items" => [],
        "status" => "#{status}",
        "updated_at" => "2024-03-17T00:54:31"
      },
      "id" => "hook_Gy4xBAKS5rS6AVMg",
      "type" => "order.#{status}"
    }
  end

  defp using_basic_auth(conn, username, password) do
    header_content = "Basic " <> Base.encode64("#{username}:#{password}")
    conn |> put_req_header("authorization", header_content)
  end
end
