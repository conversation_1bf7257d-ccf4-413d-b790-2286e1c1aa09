defmodule RmsWeb.Webhooks.ShopifyControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo

  alias Rms.Integrations.ProductVariantMapping
  alias Rms.Commerce.Products.ProductVariant

  import Rms.Factory

  import Ecto.Query
  alias Rms.Repo

  @product_delete_payload %{
    "metadata" => %{
      "action" => "DELETE",
      "type" => "INCREMENTAL",
      "resource" => "PRODUCT",
      "truncatedFields" => [],
      "occurred_at" => "2025-04-09T10:00:04-03:00"
    },
    "productFeed" => %{
      "id" => "gid://shopify/ProductFeed/6595051815",
      "shop_id" => "gid://shopify/Shop/68076077351",
      "country" => "BR",
      "language" => "PT_BR"
    },
    "product" => %{
      "id" => "gid://shopify/Product/9685825683751",
      "updatedAt" => "2025-04-09T10:00:04-03:00"
    },
    "products" => nil
  }

  @variant_delete_payload %{
    "metadata" => %{
      "action" => "DELETE",
      "type" => "INCREMENTAL",
      "resource" => "VARIANT",
      "truncatedFields" => [],
      "occurred_at" => "2025-04-09T10:21:24-03:00"
    },
    "productFeed" => %{
      "id" => "gid://shopify/ProductFeed/6595051815",
      "shop_id" => "gid://shopify/Shop/68076077351",
      "country" => "BR",
      "language" => "PT_BR"
    },
    "product" => %{
      "id" => "gid://shopify/Product/9797255659815",
      "variants" => %{
        "edges" => [
          %{
            "node" => %{
              "id" => "gid://shopify/ProductVariant/49914794606887"
            }
          }
        ]
      },
      "updatedAt" => "2025-04-09T10:21:24-03:00"
    },
    "products" => nil
  }

  defp sign_payload(payload, secret \\ Application.get_env(:rms, :shopify_client_secret)) do
    :hmac
    |> :crypto.mac(:sha256, secret, payload)
    |> Base.encode64()
  end

  defp build_signed_conn(conn, payload, opts \\ []) do
    signature = Keyword.get(opts, :signature, sign_payload(payload))
    shop_domain = Keyword.get(opts, :shop_domain, "iglu-demo.myshopify.com")
    topic = Keyword.get(opts, :topic, "test_topic")

    conn
    |> put_req_header("x-shopify-shop-domain", shop_domain)
    |> put_req_header("x-shopify-hmac-sha256", signature)
    |> put_req_header("x-shopify-topic", topic)
    |> put_req_header("content-type", "application/json")
    |> put_req_header("accepts", "application/json")
  end

  setup %{conn: conn} do
    org = insert(:organization)
    insert(:shopify_credential, shop: "iglu-demo.myshopify.com", organization: org)
    {:ok, conn: conn, org: org}
  end

  describe "verifies webhook signature" do
    test "invalid webhook signature returns unauthorized", %{conn: conn} do
      conn =
        conn
        |> build_signed_conn("{}", signature: "invalid-signature")
        |> post(~p"/webhooks/shopify", "{}")

      assert response(conn, 401)
    end

    test "valid webhook signature does not return unauthorized", %{conn: conn} do
      conn =
        conn
        |> build_signed_conn("{}")
        |> post(~p"/webhooks/shopify", "{}")

      assert response(conn, 200)
    end

    test "uses custom shopify app to validate webhook", %{conn: conn} do
      app = insert(:shopify_app, shop_domain: "iglu-demo.myshopify.com")

      signature = sign_payload("{}", app.client_secret)

      assert conn
             |> build_signed_conn("{}", signature: signature)
             |> post(~p"/webhooks/shopify", "{}")
             |> response(200)
    end
  end

  describe "product feed sync" do
    @product_sync_payload %{
      "metadata" => %{
        "action" => "CREATE",
        "fullSyncId" => "gid://shopify/ProductFullSync/17753047551716004874359",
        "occurred_at" => "2024-05-18T00:01:14-04:00",
        "resource" => "PRODUCT",
        "truncatedFields" => [],
        "type" => "FULL"
      },
      "product" => %{
        "createdAt" => "2023-12-19T17:52:46Z",
        "description" => "Nadinha",
        "handle" => "nadica-de-nada",
        "id" => "gid://shopify/Product/7033254608947",
        "images" => %{"edges" => []},
        "isPublished" => true,
        "onlineStoreUrl" => "https://salva-01.myshopify.com/products/nadica-de-nada",
        "options" => [%{"name" => "Size", "values" => ["Medium"]}],
        "productType" => "óculos",
        "publishedAt" => "2023-12-19T17:52:46Z",
        "seo" => %{"description" => nil, "title" => nil},
        "tags" => ["tag1", "tag2"],
        "title" => "Nadica de Nada",
        "updatedAt" => "2024-05-16T18:36:11Z",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "availableForSale" => true,
                "barcode" => "",
                "compareAtPrice" => nil,
                "createdAt" => "2024-05-16T18:35:22Z",
                "id" => "gid://shopify/ProductVariant/41640556363827",
                "image" => nil,
                "inventoryPolicy" => "DENY",
                "price" => %{"amount" => "9.0", "currencyCode" => "BRL"},
                "quantityAvailable" => 3044,
                "requiresShipping" => true,
                "selectedOptions" => [%{"name" => "Size", "value" => "Medium"}],
                "sku" => "50702016_0892_03A06",
                "title" => "Medium",
                "updatedAt" => "2024-05-16T18:35:22Z",
                "weight" => 1.0,
                "weightUnit" => "KILOGRAMS"
              }
            }
          ]
        },
        "vendor" => "salva-01"
      },
      "productFeed" => %{
        "country" => "BR",
        "id" => "gid://shopify/ProductFeed/1775304755",
        "language" => "EN",
        "shop_id" => "gid://shopify/Shop/58093338675"
      }
    }

    @finish_full_sync_paylaod %{
      "fullSync" => %{
        "count" => 3,
        "createdAt" => "2024-05-18T17:39:48Z",
        "errorCode" => "",
        "status" => "completed"
      },
      "metadata" => %{
        "action" => "CREATE",
        "fullSyncId" => "gid://shopify/ProductFullSync/17753047551716053988829",
        "resource" => "FULL_SYNC",
        "truncatedFields" => [],
        "type" => "FULL"
      },
      "productFeed" => %{
        "country" => "BR",
        "id" => "gid://shopify/ProductFeed/1775304755",
        "language" => "EN",
        "shop_id" => "gid://shopify/Shop/58093338675"
      }
    }

    @incremental_sync_payload %{
      "metadata" => %{
        "action" => "UPDATE",
        "occurred_at" => "2024-05-21T13:41:00-04:00",
        "resource" => "PRODUCT",
        "truncatedFields" => [],
        "type" => "INCREMENTAL"
      },
      "product" => %{
        "createdAt" => "2024-01-17T14:27:39Z",
        "description" => "",
        "handle" => "produto-de-teste-iglu",
        "id" => "gid://shopify/Product/7033254608947",
        "images" => %{"edges" => []},
        "isPublished" => true,
        "onlineStoreUrl" => "https://salva-01.myshopify.com/products/produto-de-teste-iglu",
        "options" => [%{"name" => "Title", "values" => ["Default Title"]}],
        "productType" => "lente",
        "publishedAt" => "2024-01-17T14:27:39Z",
        "seo" => %{"description" => nil, "title" => nil},
        "tags" => ["tag3", "tag4"],
        "title" => "Produto de Teste (iglu)",
        "updatedAt" => "2024-05-21T17:40:58Z",
        "variants" => %{
          "edges" => [
            %{
              "node" => %{
                "availableForSale" => true,
                "barcode" => "",
                "compareAtPrice" => %{"amount" => "1.0", "currencyCode" => "BRL"},
                "createdAt" => "2024-01-17T14:27:39Z",
                "id" => "gid://shopify/ProductVariant/41640556363827",
                "image" => nil,
                "inventoryPolicy" => "DENY",
                "price" => %{"amount" => "5.0", "currencyCode" => "BRL"},
                "quantityAvailable" => 1800,
                "requiresShipping" => true,
                "selectedOptions" => [%{"name" => "Title", "value" => "Default Title"}],
                "sku" => "50702016_0892_03A06",
                "title" => "Default Title",
                "updatedAt" => "2024-05-21T17:40:57Z",
                "weight" => 0.0,
                "weightUnit" => "KILOGRAMS"
              }
            }
          ]
        },
        "vendor" => "salva-01"
      },
      "productFeed" => %{
        "country" => "BR",
        "id" => "gid://shopify/ProductFeed/1775304755",
        "language" => "EN",
        "shop_id" => "gid://shopify/Shop/58093338675"
      }
    }
    alias Rms.Commerce.Products.Product

    test "save product and variants", %{conn: conn} do
      payload = Jason.encode!(@product_sync_payload)

      conn =
        conn
        |> build_signed_conn(payload, topic: "product_feeds/full_sync")
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200)

      Oban.drain_queue(queue: :shopify)

      assert [product] = Ecto.Query.preload(Product, [:product_variants]) |> Repo.all()
      assert product.name == "Nadica de Nada"
      assert product.tags == ["óculos"]
      assert [variant] = product.product_variants
      assert variant.name == "Nadica de Nada Medium"
      assert variant.sku == "50702016_0892_03A06"
      assert variant.tags == ["óculos"]

      assert product_variant_mapping =
               Repo.get_by(ProductVariantMapping,
                 product_variant_id: variant.id,
                 source: "shopify"
               )

      assert product_variant_mapping.organization_id == variant.organization_id
      assert product_variant_mapping.product_variant_id == variant.id
    end

    test "the same event does not duplicate products", %{conn: conn} do
      payload = Jason.encode!(@product_sync_payload)

      signed_conn = build_signed_conn(conn, payload, topic: "product_feeds/full_sync")

      post(signed_conn, ~p"/webhooks/shopify", payload)
      post(signed_conn, ~p"/webhooks/shopify", payload)

      Oban.drain_queue(queue: :shopify)

      assert [product] = Ecto.Query.preload(Product, [:product_variants]) |> Repo.all()
      assert product.name == "Nadica de Nada"
      assert product.tags == ["óculos"]
      assert [variant] = product.product_variants
      assert variant.sku == "50702016_0892_03A06"
      assert variant.tags == ["óculos"]

      assert product_variant_mapping =
               Repo.get_by(ProductVariantMapping,
                 product_variant_id: variant.id,
                 source: "shopify"
               )

      assert product_variant_mapping.organization_id == variant.organization_id
      assert product_variant_mapping.product_variant_id == variant.id
    end

    test "mark full sync as completed", %{conn: conn, org: org} do
      payload = Jason.encode!(@finish_full_sync_paylaod)

      full_sync_id = get_in(@finish_full_sync_paylaod, ["metadata", "fullSyncId"])
      ps = insert(:product_sync, organization: org, source: "shopify", external_id: full_sync_id)

      conn =
        conn
        |> build_signed_conn(payload, topic: "product_feeds/full_sync")
        |> post(~p"/webhooks/shopify", payload)

      Oban.drain_queue(queue: :shopify)

      assert json_response(conn, 200)
      assert %{status: "completed", expected_count: 3} = Repo.reload!(ps)
    end

    test "count successful product syncs", %{conn: conn} do
      payload = Jason.encode!(@product_sync_payload)

      assert conn
             |> build_signed_conn(payload, topic: "product_feeds/full_sync")
             |> post(~p"/webhooks/shopify", payload)
             |> json_response(200)

      Oban.drain_queue(queue: :shopify)

      assert [main_entry, variant_entry] = Repo.all(Rms.Integrations.ProductSyncEntry)

      assert %{
               status: "success",
               sku: nil,
               external_id: "gid://shopify/Product/7033254608947"
             } = main_entry

      assert %{
               status: "success",
               sku: "50702016_0892_03A06",
               external_id: "gid://shopify/ProductVariant/41640556363827"
             } = variant_entry
    end

    test "count unsuccessfull product sycs and saves error", %{conn: conn} do
      payload =
        @product_sync_payload
        |> update_in(
          ["product", "variants", "edges", Access.all(), "node", "sku"],
          fn _ -> nil end
        )
        |> Jason.encode!()

      assert conn
             |> build_signed_conn(payload, topic: "product_feeds/full_sync")
             |> post(~p"/webhooks/shopify", payload)
             |> json_response(200)

      Oban.drain_queue(queue: :shopify)

      assert [_, product_sync_entry] = Repo.all(Rms.Integrations.ProductSyncEntry)

      assert %{
               status: "error",
               sku: nil,
               errors: [%{metadata: %{"errors" => %{"sku" => ["can't be blank"]}}}],
               external_id: "gid://shopify/ProductVariant/41640556363827"
             } = product_sync_entry
    end

    test "creates product on incremental sync, if not exists", %{conn: conn} do
      payload = Jason.encode!(@incremental_sync_payload)

      assert conn
             |> build_signed_conn(payload, topic: "product_feeds/incremental_sync")
             |> post(~p"/webhooks/shopify", payload)
             |> json_response(200)

      Oban.drain_queue(queue: :shopify)

      assert [
               %{status: "success", external_id: "gid://shopify/Product/7033254608947"},
               %{status: "success", external_id: "gid://shopify/ProductVariant/41640556363827"}
             ] =
               Repo.all(Rms.Integrations.ProductSyncEntry)
    end

    test "updates existing product and its variants on incremental sync", %{conn: conn} do
      existing_product_payload = Jason.encode!(@product_sync_payload)

      conn
      |> build_signed_conn(existing_product_payload, topic: "product_feeds/full_sync")
      |> post(~p"/webhooks/shopify", existing_product_payload)

      Oban.drain_queue(queue: :shopify)

      updated_payload = Jason.encode!(@incremental_sync_payload)

      conn =
        conn
        |> build_signed_conn(updated_payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", updated_payload)

      Oban.drain_queue(queue: :shopify)

      assert json_response(conn, 200)
      assert [updated_product] = Repo.all(Product)

      updated_product = Repo.preload(updated_product, [:product_variants])
      assert updated_product.name == "Produto de Teste (iglu)"
      assert updated_product.tags == ["lente"]
      assert [updated_variant] = updated_product.product_variants
      assert updated_variant.sku == "50702016_0892_03A06"
      assert updated_variant.list_price == Decimal.new("5.0")
      assert updated_variant.tags == ["lente"]

      assert product_variant_mapping =
               Repo.get_by(ProductVariantMapping,
                 product_variant_id: updated_variant.id,
                 source: "shopify"
               )

      assert product_variant_mapping.organization_id == updated_variant.organization_id
      assert product_variant_mapping.product_variant_id == updated_variant.id
    end
  end

  describe "product_feeds/incremental_sync product delete" do
    setup %{org: org} do
      product_to_delete = insert(:product, organization: org)

      variant_for_deleted_product =
        insert(:product_variant, product: product_to_delete, organization: org)

      insert(:product_variant_mapping,
        product_variant_id: variant_for_deleted_product.id,
        organization_id: org.id,
        source: "shopify",
        # Example ID
        external_id: "gid://shopify/ProductVariant/49914794606887_deleted_product_variant"
      )

      insert(:product_mapping,
        product_id: product_to_delete.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/Product/9685825683751"
      )

      {:ok, %{product_to_delete: product_to_delete}}
    end

    test "archives existing product and variants", %{
      conn: conn,
      product_to_delete: product_to_delete
    } do
      payload = Jason.encode!(@product_delete_payload)

      conn =
        conn
        |> build_signed_conn(payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200)

      variants = Repo.all(from v in ProductVariant, where: v.product_id == ^product_to_delete.id)

      for variant <- variants do
        assert variant.archived_at
      end
    end

    test "no-op for not found products", %{conn: conn, product_to_delete: product_to_delete} do
      not_found_payload =
        @product_delete_payload
        |> put_in(["product", "id"], "gid://shopify/Product/NOT_FOUND_ID")
        |> Jason.encode!()

      conn =
        conn
        |> build_signed_conn(not_found_payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", not_found_payload)

      assert json_response(conn, 200)

      variants = Repo.all(from v in ProductVariant, where: v.product_id == ^product_to_delete.id)

      for variant <- variants do
        refute variant.archived_at
      end
    end
  end

  describe "product_feeds/incremental_sync variant delete" do
    setup %{org: org} do
      product = insert(:product, organization: org)

      variant_to_delete =
        insert(:product_variant, product: product, organization: org, sku: "SKU_TO_DELETE")

      unrelated_variant =
        insert(:product_variant, product: product, organization: org, sku: "SKU_UNRELATED")

      insert(:product_variant_mapping,
        product_variant_id: variant_to_delete.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/ProductVariant/49914794606887"
      )

      insert(:product_variant_mapping,
        product_variant_id: unrelated_variant.id,
        organization_id: org.id,
        source: "shopify",
        # Example ID
        external_id: "gid://shopify/ProductVariant/UNRELATED_VARIANT_ID"
      )

      insert(:product_mapping,
        product_id: product.id,
        organization_id: org.id,
        source: "shopify",
        external_id: "gid://shopify/Product/9797255659815"
      )

      {:ok,
       %{
         product: product,
         variant_to_delete: variant_to_delete,
         unrelated_variant: unrelated_variant
       }}
    end

    test "archives the variant", %{conn: conn, variant_to_delete: variant_to_delete} do
      payload = Jason.encode!(@variant_delete_payload)

      conn =
        conn
        |> build_signed_conn(payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200)

      variant_to_delete = Repo.reload!(variant_to_delete)
      assert variant_to_delete.archived_at
    end

    test "does not archive unrelated variants", %{
      conn: conn,
      unrelated_variant: unrelated_variant
    } do
      payload = Jason.encode!(@variant_delete_payload)

      conn =
        conn
        |> build_signed_conn(payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200)

      unrelated_variant = Repo.reload!(unrelated_variant)
      refute unrelated_variant.archived_at
    end

    test "no-op for not found variants", %{
      conn: conn,
      variant_to_delete: variant_to_delete,
      unrelated_variant: unrelated_variant
    } do
      not_found_payload =
        @variant_delete_payload
        |> put_in(
          ["product", "variants", "edges", Access.at(0), "node", "id"],
          "gid://shopify/ProductVariant/NOT_FOUND_ID"
        )
        |> Jason.encode!()

      conn =
        conn
        |> build_signed_conn(not_found_payload, topic: "product_feeds/incremental_sync")
        |> post(~p"/webhooks/shopify", not_found_payload)

      assert json_response(conn, 200)

      variant_to_delete = Repo.reload!(variant_to_delete)
      refute variant_to_delete.archived_at

      unrelated_variant = Repo.reload!(unrelated_variant)
      refute unrelated_variant.archived_at
    end
  end

  describe "customers/create webhook" do
    @customer_create_payload %{
      "id" => 8_616_466_579_751,
      "email" => "<EMAIL>",
      "created_at" => "2025-01-06T15:43:27-03:00",
      "updated_at" => "2025-04-16T17:00:27-03:00",
      "first_name" => "Test",
      "last_name" => "User",
      "phone" => "+5585996532132",
      "addresses" => [
        %{
          "id" => 10_719_097_291_047,
          "address1" => "Test Street 123",
          "address2" => "Apt 1",
          "city" => "Test City",
          "province" => "Test Province",
          "country" => "Brazil",
          "zip" => "12345",
          "province_code" => "TP",
          "country_code" => "BR",
          "country_name" => "Brazil",
          "default" => true
        }
      ],
      "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
    }

    test "queues customer creation job", %{conn: conn, org: org} do
      payload = Jason.encode!(@customer_create_payload)

      conn =
        conn
        |> build_signed_conn(payload,
          shop_domain: "iglu-demo.myshopify.com",
          topic: "customers/create"
        )
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200) == %{"success" => true}

      # Verify that a job was created
      assert_enqueued(
        worker: Rms.Integrations.Shopify.UpsertCustomerWorker,
        args: %{
          "organization_id" => org.id,
          "shopify_event" => "customers/create",
          "customer_external_id" => "gid://shopify/Customer/8616466579751",
          "external_customer" => @customer_create_payload
        }
      )
    end
  end

  describe "customers/update webhook" do
    @customer_update_payload %{
      "id" => 8_616_466_579_751,
      "email" => "<EMAIL>",
      "created_at" => "2025-01-06T15:43:27-03:00",
      "updated_at" => "2025-04-16T18:00:27-03:00",
      "first_name" => "Updated",
      "last_name" => "User",
      "phone" => "+5585996532132",
      "addresses" => [
        %{
          "id" => 10_719_097_291_047,
          "address1" => "Updated Street 456",
          "address2" => "Apt 2",
          "city" => "Updated City",
          "province" => "Updated Province",
          "country" => "Brazil",
          "zip" => "54321",
          "province_code" => "UP",
          "country_code" => "BR",
          "country_name" => "Brazil",
          "default" => true
        }
      ],
      "admin_graphql_api_id" => "gid://shopify/Customer/8616466579751"
    }

    test "queues customer update job", %{conn: conn, org: org} do
      payload = Jason.encode!(@customer_update_payload)

      conn =
        conn
        |> build_signed_conn(payload,
          shop_domain: "iglu-demo.myshopify.com",
          topic: "customers/update"
        )
        |> post(~p"/webhooks/shopify", payload)

      assert json_response(conn, 200) == %{"success" => true}

      # Verify that a job was created
      assert_enqueued(
        worker: Rms.Integrations.Shopify.UpsertCustomerWorker,
        args: %{
          "organization_id" => org.id,
          "shopify_event" => "customers/update",
          "customer_external_id" => "gid://shopify/Customer/8616466579751",
          "external_customer" => @customer_update_payload
        }
      )
    end
  end
end
