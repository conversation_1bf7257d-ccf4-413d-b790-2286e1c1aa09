defmodule RmsWeb.Webhooks.VTEXControllerTest do
  use RmsWeb.ConnCase
  use Oban.Pro.Testing, repo: Rms.Repo
  import Rms.Factory

  @app_key_suffix "ZEOQN"
  @order_id "v52277740atmc-01"

  @valid_payload %{
    "An" => "igludemo",
    "Attempt" => 1,
    "DateModified" => "2024-05-27T20:06:45.8343502Z",
    "HasStockKeepingUnitModified" => false,
    "HasStockKeepingUnitRemovedFromAffiliate" => false,
    "IdAffiliate" => "GLL",
    "IdSku" => "3",
    "IsActive" => true,
    "PriceModified" => false,
    "ProductId" => 1,
    "StockModified" => true
  }

  setup do
    # define :vtex_id so String.to_existing_atom/1 doesn't raise
    :vtex_id
    org = insert(:organization)
    insert(:order, external_id: @order_id)

    credential =
      insert(:vtex_credential,
        account_name: "igludemo",
        main_account_name: "igludemo",
        app_key: "vtexappkey-igludemo-#{@app_key_suffix}",
        organization: org
      )

    {:ok, credential: credential}
  end

  describe "order_status_notification/2" do
    test "returns order response with valid marketplace_order_id and action", %{conn: conn} do
      order_id = @order_id
      path = ~p"/webhooks/vtex/pvt/orders/#{order_id}/cancel"

      assert response =
               conn
               |> post(path, %{"marketplace_order_id" => order_id, "action" => "cancel"})
               |> json_response(:ok)

      assert %{
               "orderId" => ^order_id,
               "receipt" => _receipt,
               "date" => _date
             } = response
    end

    test "returns bad request with missing parameters", %{conn: conn} do
      path = ~p"/webhooks/vtex/pvt/orders/#{@order_id}/status_update"

      assert response =
               conn
               |> post(path, %{})
               |> json_response(:bad_request)

      assert %{"error" => "Invalid request"} = response
    end
  end

  describe "order_seller_cancellation/2" do
    test "returns order response with valid marketplace_order_id and action", %{conn: conn} do
      order_id = @order_id
      path = ~p"/webhooks/vtex/orders/order-group/#{@order_id}/notifications/seller-cancellation"

      assert response =
               conn
               |> post(path, %{"marketplace_order_id" => order_id, "action" => "cancel"})
               |> json_response(:ok)

      assert %{
               "orderId" => ^order_id,
               "receipt" => _receipt,
               "date" => _date
             } = response
    end
  end

  describe "notify_product_change/2" do
    test "returns unauthorized if app key suffix does not match", %{
      conn: conn,
      credential: credential
    } do
      path =
        ~p"/webhooks/vtex/product_changes/ANYOTHERSUFFIX?organization_id=#{credential.organization_id}"

      assert conn
             |> post(path, @valid_payload)
             |> json_response(:unauthorized)

      refute_enqueued(queue: "vtex")
    end

    test "returns success if app key suffix matches", %{conn: conn, credential: credential} do
      path =
        ~p"/webhooks/vtex/product_changes/#{@app_key_suffix}?organization_id=#{credential.organization_id}"

      Rms.Integrations.VTEX.ProductSyncBatcher.change_batch_size(0)
      Rms.Integrations.VTEX.ProductSyncBatcher.change_flush_interval(10)

      assert response =
               conn
               |> post(path, @valid_payload)
               |> json_response(:ok)

      assert %{"success" => true} = response

      :ok = Rms.Integrations.VTEX.ProductSyncBatcher.force_flush()

      affiliate_id = credential.affiliate_id
      org_id = credential.organization_id
      sales_channel_id = credential.sales_channel_id

      jobs = all_enqueued(worker: Rms.Workers.VTEXImportProductVariant)
      assert length(jobs) == 1

      assert [
               %{
                 args: %{
                   "affiliate_id" => ^affiliate_id,
                   "organization_id" => ^org_id,
                   "product_sync_id" => _,
                   "sales_channel_id" => ^sales_channel_id,
                   "sku_id" => "3"
                 }
               }
             ] = jobs
    end

    test "returns success if An matches main_account_name but not account_name", %{conn: conn} do
      org = insert(:organization)
      main_account_name = "mainaccount"

      credential =
        insert(:vtex_credential,
          account_name: "subaccount",
          main_account_name: main_account_name,
          app_key: "vtexappkey-mainaccount-#{@app_key_suffix}",
          organization: org
        )

      path =
        ~p"/webhooks/vtex/product_changes/#{@app_key_suffix}?organization_id=#{credential.organization_id}"

      payload = Map.put(@valid_payload, "An", main_account_name)

      assert response =
               conn
               |> post(path, payload)
               |> json_response(:ok)

      assert %{"success" => true} = response

      :ok = Rms.Integrations.VTEX.ProductSyncBatcher.force_flush()

      affiliate_id = credential.affiliate_id
      org_id = credential.organization_id
      sales_channel_id = credential.sales_channel_id

      assert [
               %{
                 args: %{
                   "affiliate_id" => ^affiliate_id,
                   "organization_id" => ^org_id,
                   "product_sync_id" => _,
                   "sales_channel_id" => ^sales_channel_id,
                   "sku_id" => "3"
                 }
               }
             ] = all_enqueued(worker: Rms.Workers.VTEXImportProductVariant)
    end
  end

  describe "notify_customer_change/2" do
    test "returns ok with valid payload", %{conn: conn} do
      vtex_credential = insert(:vtex_credential, app_key: "valid-app-key")

      path = ~p"/webhooks/vtex/customers"

      payload = %{
        "email" => "<EMAIL>",
        "name" => "John Doe",
        "account_name" => vtex_credential.account_name,
        "vtex_id" => "123"
      }

      conn =
        conn
        |> put_req_header("x-vtex-api-appkey", "valid-app-key")
        |> post(path, payload)

      assert json_response(conn, :ok) == %{}

      assert [%{email: "<EMAIL>", name: "John Doe"}] =
               Rms.Customers.list_customers(vtex_credential.organization_id)
    end

    test "returns error with invalid app key", %{conn: conn} do
      vtex_credential = insert(:vtex_credential, app_key: "valid-app-key")

      path = ~p"/webhooks/vtex/customers"

      payload = %{
        "email" => "<EMAIL>",
        "name" => "John Doe",
        "account_name" => vtex_credential.account_name,
        "vtex_id" => "123"
      }

      conn =
        conn
        |> put_req_header("x-vtex-api-appkey", "invalid-app-key")
        |> post(path, payload)

      assert json_response(conn, :unauthorized)
    end

    test "returns ok with valid payload including null fields", %{conn: conn} do
      vtex_credential =
        insert(:vtex_credential,
          app_key: "valid-app-key",
          account_name: "boboqa",
          main_account_name: "boboqa"
        )

      path = ~p"/webhooks/vtex/customers"

      payload = %{
        "account_name" => "boboqa",
        "document" => "",
        "document_type" => "",
        "email" => "<EMAIL>",
        "name" => "Gian Lucca",
        "primary_phone_number" => "",
        "vtex_id" => "123"
      }

      conn =
        conn
        |> put_req_header("x-vtex-api-appkey", "valid-app-key")
        |> post(path, payload)

      assert json_response(conn, :ok) == %{}

      assert [%{email: "<EMAIL>", name: "Gian Lucca"}] =
               Rms.Customers.list_customers(vtex_credential.organization_id)
    end

    test "updates existing customer without overriding empty fields", %{conn: conn} do
      vtex_credential =
        insert(:vtex_credential,
          app_key: "valid-app-key",
          account_name: "boboqa",
          main_account_name: "boboqa"
        )

      existing_customer =
        insert(:customer,
          email: "<EMAIL>",
          name: "Gian Lucc4",
          document: "*********",
          document_type: "CPF",
          primary_phone_number: "**********",
          organization: vtex_credential.organization
        )

      path = ~p"/webhooks/vtex/customers"

      payload = %{
        "account_name" => "boboqa",
        "document" => "",
        "document_type" => "",
        "email" => "<EMAIL>",
        "name" => "Gian Lucca",
        "primary_phone_number" => "",
        "vtex_id" => "123"
      }

      conn =
        conn
        |> put_req_header("x-vtex-api-appkey", "valid-app-key")
        |> post(path, payload)

      assert json_response(conn, :ok) == %{}

      updated_customer = Rms.Repo.get!(Rms.Customers.Customer, existing_customer.id)
      assert updated_customer.email == "<EMAIL>"
      assert updated_customer.name == "Gian Lucca"
      assert updated_customer.document == "*********"
      assert updated_customer.document_type == "CPF"
      assert updated_customer.primary_phone_number == "**********"
    end

    test "handles encoding conversion gracefully", %{conn: conn} do
      insert(:vtex_credential,
        app_key: "valid-app-key",
        account_name: "boboqa",
        main_account_name: "boboqa"
      )

      path = ~p"/webhooks/vtex/customers"

      payload = %{
        "account_name" => "boboqa",
        "email" => "<EMAIL>",
        "name" => "JoÃ£o Silva",
        "vtex_id" => "123",
        "addresses" => [
          %{
            "receiver_name" => "JoÃ£o Silva",
            "city_name" => "SÃ£o Paulo",
            "state" => "SP",
            "country_name" => "BRA",
            "neighborhood" => "Vila Olímpia",
            "street" => "Rua Funchal",
            "number" => "100",
            "zip" => "04551-060",
            "complement" => ""
          }
        ]
      }

      conn =
        conn
        |> put_req_header("x-vtex-api-appkey", "valid-app-key")
        |> post(path, payload)

      assert json_response(conn, :ok) == %{}

      customer =
        Rms.Customers.Customer
        |> Rms.Repo.get_by!(email: "<EMAIL>")
        |> Rms.Repo.preload([:addresses])

      assert customer.name == "João Silva"

      assert [address] = customer.addresses
      assert address.receiver_name == "João Silva"
      assert address.city_name == "São Paulo"
      assert address.neighborhood == "Vila Olímpia"
      assert address.street == "Rua Funchal"
    end
  end

  describe "order_processing_result/2" do
    test "updates fulfillment external reference with valid params", %{conn: conn} do
      order = insert(:order)

      fulfillment =
        insert(:fulfillment,
          order: order,
          organization: order.organization,
          external_reference: nil
        )

      params = %{
        "fields" => %{"mainOrderId" => "GLL-123-456"},
        "marketplaceOrderId" => "#{order.id}-#{fulfillment.id}"
      }

      conn =
        post(conn, ~p"/webhooks/vtex/order-integration/notification/processing-result", params)

      assert json_response(conn, :ok) == %{"success" => true}

      updated_fulfillment = Rms.Repo.reload(fulfillment)
      assert updated_fulfillment.external_reference == "GLL-123-456"
    end

    test "returns error with invalid external reference", %{conn: conn} do
      params = %{
        "fields" => %{},
        "marketplaceOrderId" => "123-456"
      }

      log =
        ExUnit.CaptureLog.capture_log(fn ->
          conn =
            post(
              conn,
              ~p"/webhooks/vtex/order-integration/notification/processing-result",
              params
            )

          assert json_response(conn, :ok) == %{"success" => false}
        end)

      assert log =~
               "failed to process VTEX order result=missing_external_reference"
    end

    test "returns error with invalid marketplaceOrderId format", %{conn: conn} do
      params = %{
        "fields" => %{"mainOrderId" => "GLL-123-456"},
        "marketplaceOrderId" => "invalid-format"
      }

      assert_raise Ecto.Query.CastError, fn ->
        post(conn, ~p"/webhooks/vtex/order-integration/notification/processing-result", params)
      end
    end

    test "returns error when fulfillment is not found", %{conn: conn} do
      params = %{
        "fields" => %{"mainOrderId" => "GLL-123-456"},
        "marketplaceOrderId" => "9999-9999"
      }

      assert_raise Ecto.NoResultsError, fn ->
        post(conn, ~p"/webhooks/vtex/order-integration/notification/processing-result", params)
      end
    end

    test "returns error when fulfillment order_id mismatch", %{conn: conn} do
      order = insert(:order)
      fulfillment = insert(:fulfillment, order: order, organization: order.organization)

      params = %{
        "fields" => %{"mainOrderId" => "GLL-123-456"},
        "marketplaceOrderId" => "9999-#{fulfillment.id}"
      }

      log =
        ExUnit.CaptureLog.capture_log(fn ->
          conn =
            post(
              conn,
              ~p"/webhooks/vtex/order-integration/notification/processing-result",
              params
            )

          assert json_response(conn, :unprocessable_entity) == %{"success" => false}
        end)

      assert log =~ "fulfillment order_id mismatch"
    end
  end

  describe "notify_order_status" do
    test "valid state and app key", %{conn: conn} do
      vtex_credential = insert(:vtex_credential, app_key: "valid-app-key")
      path = ~p"/webhooks/vtex/orders/hook"

      params = %{
        "OrderId" => "123456",
        "State" => "ready-for-handling",
        "Origin" => %{
          "Account" => vtex_credential.account_name,
          "x-vtex-api-appkey" => vtex_credential.app_key
        }
      }

      conn =
        conn
        |> post(path, params)

      assert json_response(conn, 200)
    end

    test "invalid state", %{conn: conn} do
      vtex_credential = insert(:vtex_credential, app_key: "valid-app-key")
      path = ~p"/webhooks/vtex/orders/hook"

      params = %{
        "OrderId" => "123456",
        "State" => "invalid-state",
        "Origin" => %{
          "Account" => vtex_credential.account_name,
          "x-vtex-api-appkey" => vtex_credential.app_key
        }
      }

      assert %{"errors" => %{"detail" => "Unprocessable Entity"}} =
               conn
               |> post(path, params)
               |> json_response(422)
    end

    test "invalid app key", %{conn: conn} do
      vtex_credential = insert(:vtex_credential, app_key: "valid-app-key")
      path = ~p"/webhooks/vtex/orders/hook"

      params = %{
        "OrderId" => "123456",
        "State" => "ready-for-handling",
        "Origin" => %{
          "Account" => vtex_credential.account_name,
          "x-vtex-api-appkey" => "invalid-app-key"
        }
      }

      assert %{"errors" => %{"detail" => "Unauthorized"}} =
               conn
               |> post(path, params)
               |> json_response(401)
    end
  end
end
