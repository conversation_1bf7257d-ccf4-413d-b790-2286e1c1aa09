defmodule RmsWeb.SchemaValidatorTest do
  use ExUnit.Case, async: true
  alias RmsWeb.SchemaValidator

  defmodule MockConn do
    defstruct private: %{}
  end

  defp build_mock(controller) do
    %MockConn{
      private: %{phoenix_controller: controller}
    }
  end

  setup do
    conn = build_mock(RmsWeb.CustomerController)
    conn2 = build_mock(RmsWeb.TestController)
    {:ok, conn: conn, conn2: conn2}
  end

  test "valid schema returns ok tuple", %{conn: conn} do
    params = %{name: "<PERSON> Doe"}
    assert {:ok, _} = SchemaValidator.validate(conn, :paginated_customers, params)
  end

  test "invalid schema returns error tuple", %{conn: conn} do
    params = %{name: 123}

    assert {:error, %Ecto.Changeset{}} =
             SchemaValidator.validate(conn, :paginated_customers, params)
  end

  test "nonexistent schema raises UndefinedFunctionError", %{conn: conn} do
    assert_raise UndefinedFunctionError, fn ->
      SchemaValidator.validate(conn, :nonexistent_schema, %{})
    end
  end

  test "noexistent schema file", %{conn2: conn2} do
    assert_raise ArgumentError, "Schema file not found", fn ->
      SchemaValidator.validate(conn2, :validate_schema, %{})
    end
  end
end
