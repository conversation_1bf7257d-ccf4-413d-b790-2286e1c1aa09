defmodule Rms.EventsCase do
  @moduledoc """
  A test support module that defines common setup and helper functions for
  testing the event handler.
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Oban.Pro.Testing, repo: Rms.Repo, prefix: "events"

      alias Rms.Events

      import Rms.EventsCase
      require Rms.EventsCase

      def handle_event(event_name, resource) do
        perform_job(Events.Handler, %{event_name: event_name, resource: resource})
      end

      def assert_emit(event_name, resource) do
        assert_enqueued(
          worker: Events.Handler,
          args: %{event_name: event_name, resource: resource}
        )
      end

      def refute_emit(event_name, resource \\ nil) do
        args =
          if resource,
            do: %{event_name: event_name, resource: resource},
            else: %{event_name: event_name}

        refute_enqueued(
          worker: Events.Handler,
          args: args
        )
      end
    end
  end

  defmacro test_event_handler_enqueued do
    event_handler_map =
      :attributes
      |> Rms.Events.Handler.__info__()
      |> Keyword.get(:event_handler_map, [%{}])
      |> hd

    for {event_name, handler_modules} <- event_handler_map,
        handler_module <- handler_modules do
      quote do
        test "enqueue #{unquote(handler_module)} when #{unquote(event_name)} is emitted" do
          assert :ok == handle_event(unquote(event_name), %{})
          assert_enqueued(worker: unquote(handler_module))
        end
      end
    end
  end
end
