defmodule Rms.Factory do
  use ExMachina.Ecto, repo: Rms.Repo

  def user_factory do
    %Rms.Accounts.User{
      name: "<PERSON>",
      email: sequence(:email, &"email-#{&1}@example.com"),
      external_id: Ecto.UUID.generate(),
      provider: "ecto",
      organization: build(:organization)
    }
  end

  def location_users_factory do
    %Rms.Accounts.LocationUser{
      user: fn parent -> build(:user, organization: parent.organization) end,
      location: fn parent -> build(:location, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def dock_factory do
    %Rms.Accounts.Dock{
      name: "Main Dock",
      external_id: sequence(:external_id, &"EXT#{&1}"),
      location: fn parent -> build(:location, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def organization_factory do
    %Rms.Accounts.Organization{
      name: "Jane Street Ltda.",
      cnpj: sequence(:cnpj, &"00.000.000/0001-#{String.pad_leading(to_string(&1), 2, "0")}"),
      org_id: Ecto.UUID.generate()
    }
  end

  def staff_factory() do
    %Rms.Accounts.Staff{
      name: "<PERSON>",
      external_id: sequence(:external_id, &"STAFF#{&1}"),
      organization: build(:organization)
    }
  end

  def location_factory do
    %Rms.Accounts.Location{
      name: "Jane Street Ltda.",
      cnpj: sequence(:cnpj, &"00.000.000/0001-#{String.pad_leading(to_string(&1), 2, "0")}"),
      organization: build(:organization),
      address: build(:address)
    }
  end

  def location_mapping_factory do
    %Rms.Integrations.LocationMapping{
      external_id: sequence(:external_id, &"EXT#{&1}"),
      source: "factory"
    }
  end

  def organization_setting_factory do
    %Rms.Settings.OrganizationSetting{key: sequence(:location_setting, &"key-#{&1}")}
  end

  def location_setting_factory(attrs) do
    location = Map.get(attrs, :location)

    record = %Rms.Settings.LocationSetting{
      key: sequence(:location_setting, &"key-#{&1}"),
      organization_id: location.organization_id
    }

    merge_attributes(record, attrs)
  end

  def staff_location_factory() do
    %Rms.Accounts.StaffLocation{
      organization: build(:organization),
      location: fn parent -> build(:location, organization: parent.organization) end,
      staff: fn parent -> build(:staff, organization: parent.organization) end
    }
  end

  def staff_role_factory() do
    %Rms.Accounts.StaffRole{
      organization: build(:organization),
      seller: false,
      cashier: false,
      stocker: false
    }
  end

  def shopify_credential_factory do
    %Rms.Integrations.ShopifyCredential{
      shop: sequence(:shop, &"test-shop-#{&1}"),
      credential: Ecto.UUID.generate(),
      organization: build(:organization)
    }
  end

  def order_factory do
    %Rms.Commerce.Orders.Order{
      total_items_selling_price: "100.00",
      total_price: "100.00",
      total_items_list_price: "100.00",
      total_discount: "0.00",
      total_delivery_price: "0.00",
      status: "open",
      external_id: sequence(:external_id, &"ORD#{&1}"),
      organization: build(:organization)
    }
  end

  def line_item_factory do
    %Rms.Commerce.Orders.LineItem{
      list_price: 10.0,
      price: 10.0,
      quantity: 1,
      shipping_method: "shipping",
      sku: fn
        %{product_variant: %{sku: sku}} -> sku
        _ -> nil
      end
    }
  end

  def transaction_factory do
    %Rms.Finance.Transaction{
      status: "open",
      order: fn parent -> build(:order, organization: parent.organization) end
    }
  end

  def payment_factory(attrs) do
    metadata =
      if attrs[:method] == "payment_link" do
        %{"link" => "http://example.com"}
      else
        %{}
      end

    record = %Rms.Finance.Payment{
      status: "pending",
      metadata: metadata,
      amount: "10.00",
      method: "credit_card",
      transaction: fn parent -> build(:transaction, organization: parent.organization) end,
      organization: build(:organization)
    }

    record
    |> merge_attributes(attrs)
    |> evaluate_lazy_attributes()
  end

  def transaction_customer_factory do
    %Rms.Finance.Customer{
      name: "John Doe",
      document: Brcpfcnpj.cpf_generate(),
      document_type: "cpf",
      address: %{
        state: "state",
        zip: "zip",
        number: "123",
        city_code: "123",
        city_name: "city_name",
        country_name: "country_name",
        neighborhood: "neighborhood",
        street: "street"
      },
      organization: build(:organization)
    }
  end

  def api_token_factory do
    token = sequence(:token, &"any token #{&1}")

    %Rms.Accounts.ApiToken{
      token: token,
      token_hash: token,
      organization: build(:organization)
    }
  end

  def external_payment_reference_factory do
    %Rms.Integrations.ExternalPaymentReference{
      partner: "partner",
      external_id: sequence(:external_id, &"EXT#{&1}"),
      payment: fn parent -> build(:payment, organization: parent.organization) end
    }
  end

  def vinco_credential_factory() do
    %Rms.Integrations.Vinco.Credential{
      key: "33aCAKJREH",
      location: fn parent -> build(:location, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def cielo_credential_factory() do
    %Rms.Integrations.CieloCredential{
      token: Ecto.UUID.generate(),
      client_id: Ecto.UUID.generate(),
      client_secret: Ecto.UUID.generate(),
      expires_in: 1199,
      organization: build(:organization)
    }
  end

  def gift_promo_credential_factory() do
    %Rms.Integrations.GiftPromo.Credential{
      store_id: "188484",
      access_key: "gcod#188484*aKsu1",
      username: "isja",
      password: "nce3",
      organization: build(:organization)
    }
  end

  def pagarme_credential_factory() do
    %Rms.Integrations.PagarMeCredential{
      credential: "#{Ecto.UUID.generate()}",
      organization: build(:organization)
    }
  end

  def pagarme_hook_credential_factory do
    %Rms.Integrations.PagarMeHookCredential{
      shop_id: sequence(:external_id, &"SHOP#{&1}"),
      credential: "#{Ecto.UUID.generate()}:#{Ecto.UUID.generate()}",
      credential_hash: "#{Ecto.UUID.generate()}:#{Ecto.UUID.generate()}",
      organization: build(:organization)
    }
  end

  def product_factory do
    %Rms.Commerce.Products.Product{
      name: sequence(:product_name, &"Product #{&1}"),
      ncm: sequence(:ncm, &"NCM #{&1}"),
      product_type: "item",
      organization: build(:organization)
    }
  end

  def product_variant_factory do
    %Rms.Commerce.Products.ProductVariant{
      list_price: "10.0",
      price: "10.0",
      name: sequence(:product_variant_name, &"Product Variant #{&1}"),
      image_urls: ["http://example.com"],
      sku: sequence(:sku, &"SKU #{&1}"),
      bar_code: sequence(:bar_code, &"bar_code_#{&1}"),
      organization: build(:organization),
      product: fn parent -> build(:product, organization: parent.organization) end
    }
  end

  def address_factory() do
    %Rms.Addresses.Address{
      receiver_name: "John Doe",
      city_name: "City",
      city_code: "3550308",
      state: "SP",
      country_name: "Brasil",
      neighborhood: "Pinheiros",
      street: "Cunha Gago",
      street_type: "Rua",
      number: "100",
      zip: sequence(:zip, &"#{String.pad_leading(to_string(&1), 8, "0")}"),
      complement: "Casa"
    }
  end

  def customer_factory(attrs) do
    email = sequence(:email, &"email-#{&1}@example.com")
    primary_phone_number = sequence(:primary_phone_number, &"000-000-000#{&1}")

    email = Map.get(attrs, :email, email)
    email_hash = if(email, do: String.downcase(email))

    document = Map.get(attrs, :document, Brcpfcnpj.cpf_generate())
    primary_phone_number = Map.get(attrs, :primary_phone_number, primary_phone_number)

    customer = %Rms.Customers.Customer{
      name: "John Doe",
      document: document,
      document_hash: document,
      document_plain: document,
      document_type: "cpf",
      email: email,
      email_hash: email_hash,
      email_plain: email,
      primary_phone_number: primary_phone_number,
      primary_phone_number_hash: primary_phone_number,
      primary_phone_number_plain: primary_phone_number,
      organization: build(:organization),
      birthdate: DateTime.utc_now() |> Date.add(-365 * 18)
    }

    merge_attributes(customer, attrs)
  end

  def order_customer_factory(attrs) do
    email = sequence(:email, &"email-#{&1}@example.com")
    primary_phone_number = sequence(:primary_phone_number, &"000-000-000#{&1}")

    email = Map.get(attrs, :email, email)
    email_hash = String.downcase(email)

    document = Map.get(attrs, :document, Brcpfcnpj.cpf_generate())
    primary_phone_number = Map.get(attrs, :primary_phone_number, primary_phone_number)

    plain_name = Map.get(attrs, :plain_name, "John Doe")

    customer = %Rms.Commerce.Orders.OrderCustomer{
      name: "John Doe",
      plain_name: plain_name,
      document: document,
      document_hash: document,
      document_type: "cpf",
      email: email,
      email_hash: email_hash,
      primary_phone_number: primary_phone_number,
      primary_phone_number_hash: primary_phone_number,
      order: fn parent -> build(:order, organization: parent.organization) end,
      organization: build(:organization)
    }

    merge_attributes(customer, attrs)
  end

  def invoice_serie_factory do
    %Rms.Fiscal.InvoiceSerie{
      invoice_type: "nf",
      invoice_serie: sequence(:invoice_serie, & &1),
      invoice_env: "dev",
      status: "active",
      available_number: 1,
      organization: build(:organization),
      location: fn parent -> build(:location, organization: parent.organization) end
    }
  end

  def fiscal_invoice_factory do
    %Rms.Fiscal.FiscalInvoice{
      operation_type: "sale",
      service: "vinco",
      status: "pending",
      external_id: sequence(:inoice_external_id, &"EXT #{&1}"),
      df_key: sequence(:inoice_df, &"DF #{&1}"),
      qr_code: "qr_code",
      xml: "xml",
      danfe: "danfe",
      metadata: %{meta: "meta"},
      reverse_fulfillment: nil,
      organization: build(:organization),
      serie: fn parent -> build(:invoice_serie, organization: parent.organization) end
    }
  end

  def vtex_credential_factory do
    account_name = sequence(:shop, &"seller#{&1}")

    %Rms.Integrations.VTEXCredential{
      account_name: account_name,
      main_account_name: account_name,
      app_key: Ecto.UUID.generate(),
      app_token: Ecto.UUID.generate(),
      sales_channel_id: "1",
      affiliate_id: "GLL",
      organization: build(:organization)
    }
  end

  def product_taxes_factory do
    %Rms.Fiscal.ProductTaxes{
      ncm: sequence(:ncm, &String.pad_leading(to_string(&1), 8, "0")),
      sku: sequence(:sku, &"SKU#{String.pad_leading(to_string(&1), 8, "0")}"),
      origin: "0",
      cfop: "5102",
      cst_icms: "400",
      cst_pis: "08",
      cest: "0101000",
      cst_cofins: "08",
      icms_percentage: Decimal.new("0.1"),
      fcp_percentage: Decimal.new("0.1"),
      pis_percentage: Decimal.new("0.1"),
      cofins_percentage: Decimal.new("0.1"),
      organization: build(:organization),
      benefit_code: nil,
      additional_info_message: "Info Message"
    }
  end

  def interstate_taxes_factory do
    %Rms.Fiscal.InterStateTaxes{
      uf_destiny: "SP",
      uf_origin: "AC",
      icms_percentage: Decimal.new("0.1")
    }
  end

  def location_tax_factory do
    %Rms.Fiscal.LocationTax{
      ie: sequence(:ie, &"IE#{&1}"),
      name: sequence(:name, &"NM#{&1}"),
      crt: "1",
      organization: build(:organization),
      location: fn parent -> build(:location, organization: parent.organization) end
    }
  end

  def assign_location_factory do
    %Rms.Accounts.LocationUser{}
  end

  def product_sync_factory do
    %Rms.Integrations.ProductSync{
      external_id: Ecto.UUID.autogenerate(),
      status: "pending",
      organization: build(:organization)
    }
  end

  def location_sync_factory do
    %Rms.Integrations.LocationSync{
      external_id: Ecto.UUID.autogenerate(),
      status: "pending",
      organization: build(:organization)
    }
  end

  def location_sync_entry_factory do
    %Rms.Integrations.CustomerSyncEntry{}
  end

  def cart_factory do
    %Rms.Commerce.Carts.Cart{
      status: "active",
      organization: build(:organization),
      total_price: "0",
      total_items_list_price: "0",
      total_items_selling_price: "0",
      total_manual_discount: "0",
      total_delivery_price: "0"
    }
  end

  def delivery_group_factory do
    %Rms.Commerce.Carts.DeliveryGroup{
      fulfillment_type: "in-store"
    }
  end

  def cart_item_factory() do
    %Rms.Commerce.Carts.CartItem{
      quantity: 1,
      list_price: "10",
      total_price: "10",
      selling_price: "10",
      product_variant: fn parent -> build(:product_variant, organization: parent.organization) end
    }
  end

  def discount_factory do
    %Rms.Commerce.Discounts.Discount{
      type: "fixed",
      value: "10",
      organization: build(:organization)
    }
  end

  def line_item_discount_factory do
    %Rms.Commerce.Discounts.LineItemDiscount{
      type: "fixed",
      value: "10",
      organization: build(:organization)
    }
  end

  def cart_item_discount_factory do
    %Rms.Commerce.Discounts.CartItemDiscount{
      type: "fixed",
      value: "10",
      organization: build(:organization)
    }
  end

  def inventory_item_factory() do
    %Rms.Commerce.Products.InventoryItem{
      quantity: 1,
      location: fn parent -> build(:location, organization: parent.organization) end,
      organization: build(:organization),
      product_variant: fn parent -> build(:product_variant, organization: parent.organization) end
    }
  end

  def shopify_app_factory do
    %Rms.Integrations.ShopifyApp{
      shop_domain: sequence(:shop, &"shop#{&1}"),
      client_id: Ecto.UUID.generate(),
      client_secret: Ecto.UUID.generate(),
      organization: build(:organization)
    }
  end

  def shopify_storefront_credential_factory do
    %Rms.Integrations.ShopifyStorefrontCredential{
      credential_id: sequence(:shop, &"iglu_#{&1}"),
      credential: Ecto.UUID.generate(),
      organization: build(:organization)
    }
  end

  def erp_credential_factory do
    %Rms.Integrations.ErpCredential{
      url: sequence(:url, &"url_#{&1}"),
      credential: Ecto.UUID.generate(),
      organization: build(:organization)
    }
  end

  def product_mapping_factory do
    %Rms.Integrations.ProductSyncMapping{}
  end

  def product_variant_mapping_factory do
    %Rms.Integrations.ProductVariantMapping{
      external_id: sequence(:external_id, &"EXT#{&1}"),
      source: "factory"
    }
  end

  def inventory_item_mapping_factory do
    %Rms.Integrations.InventoryItemMapping{
      external_id: sequence(:external_id, &"EXT#{&1}"),
      source: "factory"
    }
  end

  def fiscal_invoice_error_factory() do
    %Rms.Errors.FiscalInvoiceError{
      reason: sequence(:invoice_error, &"ERROR#{&1}"),
      stacktrace: "#{__MODULE__}",
      organization: build(:organization),
      fiscal_invoice: fn parent -> build(:fiscal_invoice, organization: parent.organization) end
    }
  end

  def order_error_factory() do
    %Rms.Errors.OrderError{
      error: sequence(:invoice_error, &"ERROR#{&1}"),
      stacktrace: "#{__MODULE__}",
      organization: build(:organization),
      order: fn parent -> build(:order, organization: parent.organization) end
    }
  end

  def fulfillment_factory() do
    %Rms.Commerce.Fulfillments.Fulfillment{
      status: "pending",
      ecommerce: "shopify",
      shipping_method: "delivery",
      organization: build(:organization),
      order: fn parent -> build(:order, organization: parent.organization) end
    }
  end

  def shipping_setting_factory() do
    %Rms.Commerce.Fulfillments.ShippingSetting{
      fulfillment: fn parent -> build(:fulfillment, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def packing_factory() do
    %Rms.Commerce.Packings.Packing{
      status: "pending",
      shipping_method: "delivery",
      organization: build(:organization),
      fulfillment: fn parent -> build(:fulfillment, organization: parent.organization) end,
      dock: fn parent -> build(:dock, organization: parent.organization) end,
      total_price: Decimal.new("0.0")
    }
  end

  def packing_item_factory() do
    %Rms.Commerce.Packings.PackingItem{
      quantity: 1,
      organization: build(:organization),
      fulfillment: fn parent -> build(:fulfillment, organization: parent.organization) end,
      line_item: fn parent -> build(:line_item, organization: parent.organization) end
    }
  end

  def addon_factory() do
    %Rms.Commerce.Products.Addon{
      name: sequence(:addon_name, &"ADDON#{&1}"),
      list_price: Decimal.new("540.045"),
      price: Decimal.new("540.045"),
      type: "subscription",
      description: sequence(:addon_sku, &"ADDON#{&1}"),
      image_url: sequence(:addon_image_url, &"ADDON#{&1}"),
      organization: build(:organization)
    }
  end

  def addon_mapping_factory() do
    %Rms.Integrations.AddonMapping{
      external_id: sequence(:external_id, &"EXT#{&1}"),
      source: "factory"
    }
  end

  def authorization_request_factory do
    %Rms.Commerce.Discounts.AuthorizationRequest{
      status: "pending",
      organization: build(:organization),
      code: "123456"
    }
  end

  def customer_endpoint_factory do
    %Rms.Integrations.Customers.CustomerEndpoint{
      endpoint: "https://example.com",
      unique_on: ["email"],
      headers: [["Content-Type", "application/json"]],
      active: true,
      organization: build(:organization)
    }
  end

  def customer_sync_factory do
    %Rms.Integrations.CustomerSync{
      external_id: Ecto.UUID.autogenerate(),
      status: "pending",
      organization: build(:organization)
    }
  end

  def customer_sync_entry_factory do
    %Rms.Integrations.CustomerSyncEntry{}
  end

  def customer_sync_mapping_factory do
    %Rms.Integrations.CustomerSyncMapping{}
  end

  def cancelation_endpoint_factory do
    %Rms.Integrations.Orders.CancelationEndpoint{
      endpoint: "https://example.com/cancelation",
      headers: [["Content-Type", "application/json"]],
      active: true,
      organization: build(:organization)
    }
  end

  def order_settings_factory do
    %Rms.Commerce.Orders.OrderSettings{
      prefix: "ORD-",
      suffix: "-2024",
      current_order_number: 0,
      organization: build(:organization)
    }
  end

  def cart_addon_factory do
    %Rms.Commerce.Carts.CartAddon{
      quantity: 1,
      name: sequence(:cart_addon_name, &"CartAddon #{&1}"),
      type: "subscription",
      price: Decimal.new("10.0"),
      description: "A cart addon description",
      cart: fn parent -> build(:cart, organization: parent.organization) end,
      addon: fn parent -> build(:addon, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def product_sync_mapping_factory do
    %Rms.Integrations.ProductSyncMapping{
      external_id: sequence(:external_id, &"EXT#{&1}"),
      source: sequence(:source, &"SOURCE#{&1}"),
      organization: build(:organization)
    }
  end

  def shopify_order_information_factory do
    %Rms.Integrations.Shopify.Orders.AdditionalInformations{
      order_type: "in-store",
      fields: ["location"],
      organization: build(:organization)
    }
  end

  def product_sync_configuration_factory do
    %Rms.Integrations.ProductSyncConfiguration{
      field_priorities: %{
        "name" => ["shopify", "linx_pos", "vtex"],
        "price" => ["linx_pos", "shopify", "vtex"]
      },
      default_priority: ["shopify", "linx_pos", "vtex"],
      organization: build(:organization)
    }
  end

  def iglu_credit_factory do
    %Rms.Finance.IgluCredit{
      amount: Decimal.new("100.00"),
      status: "available",
      reason: "return",
      expires_at: DateTime.utc_now() |> DateTime.add(30, :day),
      metadata: %{},
      organization: build(:organization),
      customer: fn parent -> build(:customer, organization: parent.organization) end
    }
  end

  def reverse_fulfillment_factory do
    %Rms.Commerce.Fulfillments.ReverseFulfillment{
      status: "pending",
      location: build(:location),
      staff: fn parent -> build(:staff, organization: parent.organization) end,
      organization: build(:organization),
      line_items: []
    }
  end

  def reverse_fulfillment_line_item_factory do
    %Rms.Commerce.Fulfillments.ReverseFulfillment.LineItem{
      returned_quantity: 1,
      reason: "defective",
      organization: build(:organization),
      line_item: fn parent -> build(:line_item, organization: parent.organization) end
    }
  end

  def iglu_credit_payment_factory do
    %Rms.Finance.IgluCreditPayment{
      payment: fn parent ->
        build(:payment, organization: parent.organization, method: "iglu_credit")
      end,
      iglu_credit: fn parent -> build(:iglu_credit, organization: parent.organization) end,
      organization: build(:organization)
    }
  end

  def fiscal_settings_factory do
    %Rms.Fiscal.FiscalSettings{
      handle_sale: true,
      handle_return: true,
      handle_transfer: true,
      transfer_without_taxes: false,
      environment: "dev",
      organization: build(:organization)
    }
  end

  def csv_file_fixture(rows \\ []) do
    path =
      Path.join(
        System.tmp_dir!(),
        "product_taxes_test_" <> Integer.to_string(:rand.uniform(1_000_000)) <> ".csv"
      )

    header =
      "ncm,uf,cfop,cst_icms,cst_pis,cst_cofins,icms_percentage,fcp_percentage,pis_percentage,cofins_percentage,sku,origin,cest,icms_base_reduction,benefit_code,additional_info_message\n"

    default_row = "12345678,SP,5102,00,01,01,18,2,1.65,7.6,,,,,"

    rows_to_join = if Enum.empty?(rows), do: [default_row], else: rows
    content = header <> Enum.join(rows_to_join, "\n")
    File.write!(path, content)
    path
  end

  def upload_entry_factory do
    %Rms.Storage.UploadEntry{
      s3_key: sequence(:s3_key, &"uploads/test-#{&1}.csv"),
      status: "pending",
      expires_at: DateTime.utc_now() |> DateTime.add(1, :hour) |> DateTime.truncate(:second),
      uploaded_at: DateTime.utc_now() |> DateTime.truncate(:second),
      error_messages: [],
      organization: build(:organization)
    }
  end
end
