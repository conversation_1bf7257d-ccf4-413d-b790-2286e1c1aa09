defmodule Rms.ShopifyMock do
  import Mox

  alias Rms.Integrations.Shopify.Mock, as: ShopifyMock

  def mark_as_paid_mock(data \\ %{}) do
    mock_data = mark_as_paid_mock_data(data)

    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:mark_order_as_paid, fn :mock_client, id ->
      case id do
        "gid://shopify/Order/bad-request-id" ->
          {:error, :bad_request}

        _ ->
          {:ok, mock_data}
      end
    end)
  end

  defp mark_as_paid_mock_data(data) do
    mock_data = %{
      "data" => %{
        "orderMarkAsPaid" => %{
          "order" => %{
            "name" => "#1073",
            "displayFinancialStatus" => "PAID"
          },
          "userErrors" => []
        }
      },
      "extensions" => %{
        "cost" => %{
          "requestedQueryCost" => 10,
          "actualQueryCost" => 10,
          "throttleStatus" => %{
            "maximumAvailable" => 2000,
            "currentlyAvailable" => 1990,
            "restoreRate" => 100
          }
        }
      }
    }

    deep_merge(mock_data, data)
  end

  def order_mock(data \\ %{}) do
    mock_data = order_mock_data(data)
    user_provided_id = get_in(mock_data, ["data", "order", "id"])

    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:get_order!, fn :mock_client, id, _, _ ->
      case id do
        ^user_provided_id ->
          {:ok, mock_data}

        "gid://shopify/Order/bad-request-id" ->
          {:error, :bad_request}

        _ ->
          {:error, :not_found}
      end
    end)
  end

  def order_mock_data(data \\ %{}) do
    mock_data = %{
      "data" => %{
        "order" => %{
          "id" => "gid://shopify/Order/random_id",
          "name" => "#1001",
          "displayFinancialStatus" => "PAID",
          "currentTotalPriceSet" => %{"shopMoney" => %{"amount" => "1.17"}},
          "customer" => %{
            "cpf" => %{"value" => "05774088529"},
            "displayName" => "Gustavo",
            "email" => "<EMAIL>",
            "id" => "gid://shopify/Customer/6488827068467"
          }
        }
      }
    }

    deep_merge(mock_data, data)
  end

  def customer_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:create_customer, fn :mock_client, _ ->
      {:ok, customer_data()}
    end)
  end

  def customer_data do
    %{"id" => "gid://shopify/Customer/123"}
  end

  def draft_order_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:create_draft_order, fn :mock_client, _ ->
      {:ok, draft_order_data()}
    end)
  end

  def create_storefront_credential_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:create_storefront_credential, fn :mock_client, _ ->
      {:ok, create_storefront_credential_data()}
    end)
  end

  def draft_order_data do
    %{"id" => "gid://shopify/DraftOrder/123", "ready" => true}
  end

  def create_storefront_credential_data do
    %{"id" => "gid://shopify/StorefrontAccessToken/123", "accessToken" => "iglu"}
  end

  def fetch_draft_order_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:fetch_draft_order, fn :mock_client, _ ->
      {:ok, fetch_draft_order_data()}
    end)
  end

  def fetch_draft_order_data do
    %{
      "id" => "gid://shopify/DraftOrder/123",
      "ready" => true,
      "status" => "OPEN",
      "invoiceUrl" => "url"
    }
  end

  def fetch_location_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:fetch_location, fn :mock_client, _ ->
      {:ok, fetch_location_data()}
    end)
  end

  def fetch_location_data do
    %{
      "nodes" => [
        %{
          "address" => %{
            "address1" => "Avenida Rebouças 3970",
            "address2" => "",
            "city" => "São Paulo",
            "country" => "Brazil",
            "countryCode" => "BR",
            "formatted" => ["Avenida Rebouças 3970", "São Paulo SP", "05402-600", "Brazil"],
            "latitude" => -23.571601,
            "longitude" => -46.69621069999999,
            "phone" => "+5511123456789",
            "province" => "São Paulo",
            "provinceCode" => "SP",
            "zip" => "05402-600"
          },
          "id" => "gid://shopify/Location/1234567890",
          "isActive" => true,
          "name" => "Shopping Eldorado"
        }
      ]
    }
  end

  def mark_order_as_completed_mock do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:fetch_draft_order, fn :mock_client, _ ->
      {:ok, %{"id" => "gid://shopify/DraftOrder/123", "ready" => true, "status" => "OPEN"}}
    end)
    |> expect(:mark_order_as_completed, fn :mock_client, _ ->
      {:ok,
       %{
         "id" => "gid://shopify/DraftOrder/123",
         "ready" => true,
         "status" => "COMPLETED",
         "order" => %{"id" => "gid://shopify/Order/123"}
       }}
    end)
  end

  def fetch_customers_mock(data \\ fetch_customers_data("foobarbaz")) do
    ShopifyMock
    |> expect(:client, fn _, _ -> :mock_client end)
    |> expect(:fetch_customers, fn :mock_client, _ ->
      {:ok, data}
    end)
  end

  def fetch_customers_data(cursor \\ "foobarbaz") do
    %{
      items: [
        %{
          "defaultAddress" => %{
            "address1" => "Trecho Sais, 54",
            "address2" => "AP 878",
            "city" => "Víctor dos Dourados",
            "company" => "85655142503",
            "country" => "Brazil",
            "name" => "Jake Peralta",
            "provinceCode" => "AM",
            "zip" => "24507138"
          },
          "email" => "<EMAIL>",
          "firstName" => "Jake",
          "id" => "gid://shopify/Customer/7525413486902",
          "lastName" => "Peralta",
          "phone" => "+5511999981081"
        }
      ],
      cursor: cursor
    }
  end

  def fetch_product_variants_mock(data \\ fetch_product_variants_data("foobarbaz")) do
    ShopifyMock
    |> expect(:storefront_client, fn _, _, _ -> :mock_storefront_client end)
    |> expect(:fetch_product_variants, fn :mock_storefront_client, _ ->
      {:ok, data}
    end)
  end

  def fetch_product_variants_data(cursor \\ "foobarbaz") do
    %{
      items: [
        %{
          "availableForSale" => true,
          "displayName" => "Bo - Óculos de Grau - Tortoise Nero",
          "id" => "gid://shopify/ProductVariant/44161969357110",
          "inventoryQuantity" => 15,
          "product" => %{
            "id" => "gid://shopify/Product/8056750244150",
            "storefrontId" => "gid://shopify/Product/8056750244150",
            "title" => "Bo - Óculos de Grau"
          },
          "selectedOptions" => [
            %{"name" => "Color", "value" => "Tortoise Nero"}
          ],
          "sku" => "000600277001",
          "title" => "Tortoise Nero"
        }
      ],
      cursor: cursor
    }
  end

  def fetch_product_store_availability_mock(
        data \\ fetch_product_store_availability_data("foobarbaz")
      ) do
    ShopifyMock
    |> expect(:storefront_client, fn _, _, _ -> :mock_storefront_client end)
    |> expect(:fetch_product_store_availability, fn :mock_storefront_client, _, _, _ ->
      {:ok, data}
    end)
  end

  def fetch_product_store_availability_data(cursor \\ "foobarbaz") do
    %{
      items: [
        %{
          "available" => true,
          "location" => %{
            "address" => %{
              "address1" => "Avenida Rebouças 3970",
              "address2" => "",
              "city" => "São Paulo",
              "country" => "Brazil",
              "countryCode" => "BR",
              "latitude" => -23.571601,
              "longitude" => -46.6962107,
              "phone" => "+5511998111994",
              "province" => "São Paulo",
              "provinceCode" => "SP",
              "zip" => "05402-600"
            },
            "id" => "gid://shopify/Location/74913710390",
            "name" => "Shopping Eldorado"
          },
          "pickUpTime" => "Usually ready in 24 hours"
        }
      ],
      cursor: cursor
    }
  end

  defp deep_merge(left, right) do
    Map.merge(left, right, &deep_resolve/3)
  end

  defp deep_resolve(_key, %{} = left, %{} = right) do
    deep_merge(left, right)
  end

  defp deep_resolve(_key, _left, right), do: right
end
