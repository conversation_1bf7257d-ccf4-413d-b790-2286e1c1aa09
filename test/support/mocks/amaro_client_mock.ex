Mox.defmock(Rms.Integrations.Erp.Amaro.Mock, for: Rms.Integrations.Erp.Amaro.ClientBehaviour)

defmodule AmaroClientStub do
  @behaviour Rms.Integrations.Erp.Amaro.ClientBehaviour

  @impl Rms.Integrations.Erp.Amaro.ClientBehaviour
  def client(_shop_domain), do: raise("unimplemented")

  @impl Rms.Integrations.Erp.Amaro.ClientBehaviour
  def insert_new_invoice(_client, _params), do: raise("unimplemented")

  @impl Rms.Integrations.Erp.Amaro.ClientBehaviour
  def insert_cancellation_invoice(_client, _params), do: raise("unimplemented")

  @impl Rms.Integrations.Erp.Amaro.ClientBehaviour
  def get_product_taxes(_client, _sku), do: raise("unimplemented")
end
