Mox.defmock(Rms.Integrations.Cielo.Mock, for: Rms.Integrations.Cielo.ClientBehaviour)

defmodule CieloClientStub do
  @behaviour Rms.Integrations.Cielo.ClientBehaviour

  @impl Rms.Integrations.Cielo.ClientBehaviour
  def get_payment_details(_token, _checkout_cielo_order_number) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.Cielo.ClientBehaviour
  def create_payment_link(_token, _params) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.Cielo.ClientBehaviour
  def get_payment_link(_token, _link_id) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.Cielo.ClientBehaviour
  def create_credential(_client_id, _client_secret) do
    raise "unimplemented"
  end
end
