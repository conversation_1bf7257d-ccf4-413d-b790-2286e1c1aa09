Mox.defmock(PagarMeClientMock, for: Rms.Integrations.PagarMe.ClientBehaviour)

defmodule PagarMeClientStub do
  @behaviour Rms.Integrations.PagarMe.ClientBehaviour

  @impl Rms.Integrations.PagarMe.ClientBehaviour
  def client(_token) do
    Tesla.client([])
  end

  @impl Rms.Integrations.PagarMe.ClientBehaviour
  def get_order(_client, _order_id) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.PagarMe.ClientBehaviour
  def create_order(_client, _params) do
    raise "unimplemented"
  end
end
