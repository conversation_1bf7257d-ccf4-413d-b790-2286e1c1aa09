Mox.defmock(Rms.Integrations.Erp.Sallve.Mock, for: Rms.Integrations.Erp.Sallve.ClientBehaviour)

defmodule SallveClientStub do
  @behaviour Rms.Integrations.Erp.Sallve.ClientBehaviour

  @impl Rms.Integrations.Erp.Sallve.ClientBehaviour
  def client(_shop_domain, _token), do: raise("unimplemented")

  @impl Rms.Integrations.Erp.Sallve.ClientBehaviour
  def insert_new_invoice(_client, _params), do: raise("unimplemented")
end
