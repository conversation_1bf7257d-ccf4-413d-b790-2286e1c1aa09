Mox.defmock(Rms.Integrations.Shopify.Mock, for: Rms.Integrations.Shopify.ClientBehaviour)

defmodule ShopifyClientStub do
  @behaviour Rms.Integrations.Shopify.ClientBehaviour

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def find_orders(_client, _filters), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_order!(_client, _id, _selection, _opts), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_product(_client, _id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def mark_order_as_paid(_client, _id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_product_feed(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def return_create(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_return(_client, _return_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def return_close(_client, _return_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def reverse_fulfillment_order_dispose(_client, _dispose_input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def returnable_fulfillments(_client, _order_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_webhook(_client, _topic, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_product_feeds(_client), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def run_product_full_sync(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_draft_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def new_create_draft_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def calculate_draft_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def move_fulfillment(_client, _fulfillment_id, _location_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fulfill_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_storefront_credential(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_draft_order(_client, _id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_location_inventory(_client, _inventory_id, _location_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_location_inventory_by_sku(_client, _query, _id, _otps), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_variant_inventory(_client, _variant_id, _location_id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_location(_client, _query), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def mark_order_as_completed(_client, _id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_customer(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def update_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def update_draft_order(_client, _id, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def cancel_fulfillment(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def cancel_order(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_customers(_client, _filters), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_customer(_client, _id), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def update_customer(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def simulate_cart(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_product_variants(_client, _filters), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_product_store_availability(_client, _id, _selected_options, _filters),
    do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def client(_shop_domain, _token), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def storefront_client(_shop_domain, _token, _opts), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_discount_code(_client, _code), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def fetch_gift_card(_client, _code), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def create_order(_client, _order), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def debit_gift_card(_client, _id, _debit_input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def credit_gift_card(_client, _id, _credit_input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def order_update(_client, _input), do: raise("unimplemented")

  @impl Rms.Integrations.Shopify.ClientBehaviour
  def get_product_variant_stock(_client, _id), do: raise("unimplemented")
end
