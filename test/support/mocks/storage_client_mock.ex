Mox.defmock(Rms.Storage.Mock, for: Rms.Storage.ClientBehaviour)

defmodule Rms.Storage.Stub do
  @behaviour Rms.Storage.ClientBehaviour

  @impl true
  def generate_presigned_post(_bucket, key, _opts) do
    {:ok,
     %{
       url: "https://example.com/test-presigned-url",
       fields: %{
         "key" => key,
         "Content-Type" => "text/csv"
       }
     }}
  end

  @impl true
  def exists?(_key) do
    {:ok, true}
  end
end
