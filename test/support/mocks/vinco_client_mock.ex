Mox.defmock(VincoClientMock, for: Rms.Integrations.Vinco.ClientBehaviour)

defmodule VincoClientStub do
  @behaviour Rms.Integrations.Vinco.ClientBehaviour

  @impl Rms.Integrations.Vinco.ClientBehaviour
  def create_fiscal_invoice(_fiscal_invoice, _vinco_invoice, _vinco_key, _vinco_url) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.Vinco.ClientBehaviour
  def cancel_fiscal_invoice(_vinco_invoice, _vinco_key, _vinco_url) do
    raise "unimplemented"
  end

  @impl Rms.Integrations.Vinco.ClientBehaviour
  def get_fiscal_invoice(_document_type, _df_key, _env, _vinco_id_key, _vinco_url) do
    raise "unimplemented"
  end
end
