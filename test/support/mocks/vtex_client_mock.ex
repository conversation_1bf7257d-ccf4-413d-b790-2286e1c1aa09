Mox.defmock(Rms.Integrations.VTEX.Mock, for: Rms.Integrations.VTEX.ClientBehaviour)

defmodule VTEXClientStub do
  @behaviour Rms.Integrations.VTEX.ClientBehaviour

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def client(_vtex_credential, _env \\ nil), do: Tesla.client([])

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_ecommerce_order(
        _client,
        _account_name,
        _affiliate_id,
        _sales_channel_id,
        _order_params
      ),
      do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_external_ecommerce_order(_client, _account_name, _affiliate_id, _order_params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_document(_client, _document, _params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def update_ecommerce_order(_client, _account_name, _order_params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def send_payment_information(_client, _transaction_id, _order_id, _params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def authorize_new_transaction(_client, _transaction_id, _params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_ecommerce_order(_client, _order_id),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def scroll_masterdata(_client, _entity, _params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def cancel_ecommerce_order(_client, _order_id, _params),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_order_hook_configuration(_client, _attributes),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_cart(_client), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_cart_items(_client, _cart_id, _items), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_cart_subscriptions(_client, _cart_id, _items), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_customer_to_cart(_client, _cart_id, _customer_info), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_delivery_options_to_cart(_client, _cart_id, _delivery_options),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_payment_to_cart(_client, _cart_id, _payment_info), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def add_marketing_data_to_cart(_client, _cart_id, _marketing_data), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_customer(_client, _customer_id), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def search_customer_address(_client, _userId, _fields), do: {:ok, []}

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def search_customer(_client, _email), do: {:ok, []}

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def create_order(_client, _order_params), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def update_document(_client, _document, _id, _params), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def process_order(_client, _order_id), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def notify_payment(_client, _order_id, _payment_id, _payment_info), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def get_sku_by_id(_client, _sku_id), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def list_product_and_sku(_client, _page, _page_size), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def catalog_search(_client, _from, _to), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def simulate_fulfillment(_client, _affiliate_id, _sales_channel_id, _payload),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def private_simulate_fulfillment(_client, _affiliate_id, _payload),
    do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def place_order(_client, _params), do: raise("unimplemented")

  @impl Rms.Integrations.VTEX.ClientBehaviour
  def send_payment_notification(_client, _doc, _params), do: raise("unimplemented")
end
