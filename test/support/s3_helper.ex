defmodule Rms.Test.S3Helper do
  @moduledoc """
  Helper functions for working with S3 in tests using localstack
  """

  @doc """
  Creates a test bucket if it doesn't exist
  """
  def ensure_test_bucket(bucket) do
    ExAws.S3.head_bucket(bucket)
    |> ExAws.request()
    |> case do
      {:ok, _} ->
        :ok

      {:error, _} ->
        ExAws.S3.put_bucket(bucket, "us-east-1")
        |> ExAws.request!()
    end
  end

  @doc """
  Uploads a file to S3
  """
  def upload_file(bucket, key, file_path) do
    file_path
    |> ExAws.S3.Upload.stream_file()
    |> ExAws.S3.upload(bucket, key)
    |> ExAws.request!()
  end

  @doc """
  Puts content directly into S3 as a file
  """
  def put_test_file(bucket, key, content) do
    ExAws.S3.put_object(bucket, key, content)
    |> ExAws.request!()
  end

  @doc """
  Deletes all objects in a bucket
  """
  def clear_bucket(bucket) do
    bucket
    |> ExAws.S3.list_objects()
    |> ExAws.request!()
    |> get_in([:body, :contents])
    |> case do
      nil ->
        :ok

      objects ->
        objects
        |> Enum.map(& &1.key)
        |> then(&ExAws.S3.delete_all_objects(bucket, &1))
        |> ExAws.request!()
    end
  end
end
