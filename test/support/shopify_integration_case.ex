defmodule Rms.ShopifyIntegrationCase do
  @moduledoc """
  This module defines the setup for tests requiring
  access to the application's data layer that integrates
  with Shopify Admin GraphQL API.
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      use Rms.DataCase

      @moduletag :shopify_integration
    end
  end

  setup do
    Application.put_env(:tesla, :adapter, {Tesla.Adapter.Finch, name: R<PERSON>.<PERSON>})
    on_exit(fn -> Application.put_env(:tesla, :adapter, Tesla.Mock) end)

    token = System.fetch_env!("SHOPIFY_TOKEN")
    shop_domain = System.fetch_env!("SHOPIFY_SHOP_DOMAIN")
    storefront_token = System.fetch_env!("SHOPIFY_STOREFRONT_TOKEN")

    {:ok,
     client: Rms.Integrations.Shopify.Client.client(shop_domain, token),
     storefront_client:
       Rms.Integrations.Shopify.Client.storefront_client(shop_domain, storefront_token, 1)}
  end
end
