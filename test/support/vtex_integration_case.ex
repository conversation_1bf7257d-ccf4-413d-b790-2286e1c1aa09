defmodule Rms.VTEXIntegrationCase do
  @moduledoc """
  This module defines the setup for tests requiring
  access to the application's data layer that integrates
  with VTEX API.
  """

  use ExUnit.CaseTemplate

  def vtex_integration(%{vtex_integration: true}) do
    Application.put_env(:tesla, :adapter, {Tesla.Adapter.Finch, name: <PERSON><PERSON><PERSON>})
    on_exit(fn -> Application.put_env(:tesla, :adapter, Tesla.Mock) end)

    account_name = System.fetch_env!("VTEX_ACCOUNT_NAME")
    app_key = System.fetch_env!("VTEX_APP_KEY")
    app_token = System.fetch_env!("VTEX_APP_TOKEN")

    vtex_credential =
      struct!(Rms.Integrations.VTEXCredential,
        account_name: account_name,
        main_account_name: account_name,
        app_key: app_key,
        app_token: app_token,
        sales_channel_id: 1
      )

    {:ok,
     credential: vtex_credential,
     account_name: account_name,
     client: Rms.Integrations.VTEX.Client.client(vtex_credential)}
  end

  def vtex_integration(_context), do: :ok
end
