{:ok, _} = Application.ensure_all_started(:ex_machina)

shopify_token = System.get_env("SHOPIFY_TOKEN")
shopify_shop_domain = System.get_env("SHOPIFY_SHOP_DOMAIN")
shopify_storefront_token = System.get_env("SHOPIFY_STOREFRONT_TOKEN")

shopify_integration? =
  shopify_token == nil or shopify_shop_domain == nil or shopify_storefront_token == nil

vtex_account_name = System.get_env("VTEX_ACCOUNT_NAME")
vtex_app_key = System.get_env("VTEX_APP_KEY")
vtex_app_token = System.get_env("VTEX_APP_TOKEN")
vtex_integration? = vtex_account_name == nil or vtex_app_key == nil or vtex_app_token == nil

ExUnit.start(
  exclude: [
    shopify_integration: shopify_integration?,
    vtex_integration: vtex_integration?,
    localstack_integration: true
  ]
)

Faker.start()
Ecto.Adapters.SQL.Sandbox.mode(Rms.Repo, :manual)

{:ok, flag} = :ldclient_testdata.flag("custom-inventory-integration")
:ldclient_testdata.update(:ldclient_flagbuilder.variations([], flag))
