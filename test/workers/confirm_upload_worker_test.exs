defmodule Rms.Workers.ConfirmProductTaxUploadWorkerTest do
  use Rms.DataCase, async: true
  use Oban.Pro.Testing, repo: Rms.Repo

  import Rms.Factory
  import Mox

  alias Rms.Workers.ConfirmProductTaxUploadWorker
  alias Rms.Storage.UploadEntry
  alias Rms.Workers.ProcessProductTaxUpload

  setup :verify_on_exit!

  setup do
    organization = insert(:organization)
    upload = insert(:upload_entry, organization: organization)
    %{upload: upload, organization: organization}
  end

  describe "process/1" do
    test "schedules processing when file exists", %{upload: upload} do
      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == upload.s3_key
        {:ok, true}
      end)

      assert :ok =
               perform_job(ConfirmProductTaxUploadWorker, %{
                 "upload_id" => upload.id,
                 "organization_id" => upload.organization_id
               })

      # Check upload status was updated
      upload = Rms.Storage.get_upload_entry!(upload.organization_id, upload.id)
      assert upload.status == "processing"

      # Check processing job was scheduled
      assert_enqueued(
        worker: ProcessProductTaxUpload,
        args: %{"upload_id" => upload.id, "organization_id" => upload.organization_id}
      )
    end

    test "snoozes when file doesn't exist and not expired", %{upload: upload} do
      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == upload.s3_key
        {:ok, false}
      end)

      assert {:snooze, 5} =
               perform_job(ConfirmProductTaxUploadWorker, %{
                 "upload_id" => upload.id,
                 "organization_id" => upload.organization_id
               })

      # Check upload status remains pending
      upload = Rms.Storage.get_upload_entry!(upload.organization_id, upload.id)
      assert upload.status == "pending"
    end

    test "marks as expired when file doesn't exist and expired", %{upload: upload} do
      # Set expires_at to past date
      expired_upload =
        upload
        |> UploadEntry.status_changeset("pending")
        |> Ecto.Changeset.put_change(
          :expires_at,
          DateTime.utc_now() |> DateTime.add(-1, :hour) |> DateTime.truncate(:second)
        )
        |> Rms.Repo.update!()

      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == expired_upload.s3_key
        {:ok, false}
      end)

      assert {:discard, :expired} =
               perform_job(ConfirmProductTaxUploadWorker, %{
                 "upload_id" => expired_upload.id,
                 "organization_id" => expired_upload.organization_id
               })

      # Check upload was marked as expired
      upload = Rms.Storage.get_upload_entry!(expired_upload.organization_id, expired_upload.id)
      assert upload.status == "expired"
    end

    test "handles error checking file existence", %{upload: upload} do
      error_reason = "s3 connection error"

      expect(Rms.Storage.Mock, :exists?, fn key ->
        assert key == upload.s3_key
        {:error, error_reason}
      end)

      assert {:error, ^error_reason} =
               perform_job(ConfirmProductTaxUploadWorker, %{
                 "upload_id" => upload.id,
                 "organization_id" => upload.organization_id
               })
    end

    test "discards job when upload is not in pending status", %{upload: upload} do
      # Set upload to processing status
      processing_upload =
        upload
        |> UploadEntry.status_changeset("processing")
        |> Rms.Repo.update!()

      assert {:discard, "cannot confirm upload in processing status"} =
               perform_job(ConfirmProductTaxUploadWorker, %{
                 "upload_id" => processing_upload.id,
                 "organization_id" => processing_upload.organization_id
               })

      # Check status wasn't changed
      upload =
        Rms.Storage.get_upload_entry!(processing_upload.organization_id, processing_upload.id)

      assert upload.status == "processing"
    end
  end
end
